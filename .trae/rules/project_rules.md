# Auto Care SaaS 开发规范

## 1. 项目结构规范

### 1.1 目录结构
```plaintext
src/
├── main/
│   ├── java/
│   │   └── com/extracme/saas/autocare/
│   │       ├── config/                 # 配置类目录
│   │       │   ├── SwaggerConfig       # Swagger配置
│   │       │   └── WebMvcConfig        # Web MVC配置
│   │       ├── controller/             # 控制器目录
│   │       │   └── api/                # API接口
│   │       ├── service/                # 服务层目录
│   │       │   ├── base/               # 服务接口
│   │       │   └── impl/               # 服务实现
│   │       ├── repository/             # 数据访问层目录
│   │       │   └── impl/               # 数据访问实现
│   │       ├── model/                  # 数据模型目录
│   │       │   ├── entity/             # 数据库实体
│   │       │   ├── dto/                # 数据传输对象
│   │       │   └── vo/                 # 视图对象
│   │       ├── mapper/                 # MyBatis映射目录
│   │       │   ├── base/               # 基础Mapper
│   │       │   └── extend/             # 扩展Mapper
│   │       └── common/                 # 公共组件目录
│   │           ├── exception/          # 异常类
│   │           ├── util/               # 工具类
│   │           └── constant/           # 常量定义
│   └── resources/
│       ├── mapper/                     # MyBatis映射文件
│       ├── application.yml             # 应用配置
│       ├── application-dev.yml         # 开发环境配置
│       ├── application-test.yml        # 测试环境配置
│       └── application-prod.yml        # 生产环境配置
└── test/
    └── java/
        └── com/extracme/saas/autocare/
            ├── service/                # 服务测试
            └── controller/             # 控制器测试
```

### 1.2 包结构规范

#### 基础包结构
- **基础包名**：`com.extracme.saas.autocare`
- **模块划分**：按功能模块划分包结构
- **命名规范**：使用小写字母，多词用点号分隔

#### 各层规范

1. **配置层 (config)**
   - 存放所有配置类
   - 使用 `@Configuration` 注解
   - 配置类命名以 `Config` 结尾
   - 示例：`SwaggerConfig`、`WebMvcConfig`

2. **控制层 (controller)**
   - 统一使用 `@RestController` 注解
   - 路径使用 kebab-case 风格
   - 版本号在路径中体现
   - 示例：`/api/v1/users`

3. **服务层 (service)**
   - 接口命名：`{Domain}Service`
   - 实现类命名：`{Domain}ServiceImpl`
   - 示例：`UserService`、`UserServiceImpl`

4. **数据访问层 (repository)**
   - 遵循 Repository 模式
   - 统一使用 `@Repository` 注解
   - 示例：`UserRepository`、`UserRepositoryImpl`

5. **模型层 (model)**
   - **实体类 (entity)**
     - 必须实现 `Serializable` 接口
     - 使用 `@Table` 注解指定表名
     - 示例：`User`、`Role`
   - **DTO (dto)**
     - 用于数据传输
     - 命名以 `DTO` 结尾
     - 示例：`UserDTO`、`CreateUserDTO`
   - **VO (vo)**
     - 用于视图展示
     - 命名以 `VO` 结尾
     - 示例：`UserVO`、`UserDetailVO`

6. **Mapper层 (mapper)**
   - **基础Mapper (base)**
     - 由 MyBatis Generator 生成
     - 禁止手动修改
   - **扩展Mapper (extend)**
     - 必须继承基础Mapper
     - 命名以 `ExtendMapper` 结尾
     - 示例：`UserExtendMapper`

7. **公共组件 (common)**
   - **异常类 (exception)**
     - 命名以 `Exception` 结尾
     - 示例：`BusinessException`
   - **工具类 (util)**
     - 命名以 `Utils` 结尾
     - 示例：`StringUtils`
   - **常量定义 (constant)**
     - 命名以 `Constants` 结尾
     - 示例：`SystemConstants`

### 1.3 必需文件
1. **项目文档**
   - `README.md`：项目说明文档
   - `docs/`：详细文档目录

2. **配置文件**
   - `pom.xml`：Maven配置文件
   - `.gitignore`：Git忽略文件
   - `application.yml`：应用配置
   - `application-dev.yml`：开发环境配置
   - `application-test.yml`：测试环境配置
   - `application-prod.yml`：生产环境配置

## 2. Git提交规范

提交信息格式：
```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试相关
chore: 构建相关
```

## 3. 代码规范

### 3.1 Controller层规范
```java
@Api(tags = "用户管理")
@RestController
@RequestMapping("/api/user")
public class UserController {
    @ApiOperation("获取用户信息")
    @GetMapping("/{id}")
    public Result<UserVO> getUser(@PathVariable Long id) {
        return Result.success(userService.getUser(id));
    }
}

// 统一响应格式
public class Result<T> {
    private int code;      // 状态码
    private String msg;    // 消息
    private T data;        // 数据
}
```

### 3.2 Service层规范
```java
@Service
public class UserServiceImpl implements UserService {
    @Transactional(rollbackFor = Exception.class)
    public void createUser(UserDTO dto) {
        // 参数校验
        if (dto == null || StringUtils.isEmpty(dto.getUsername())) {
            throw new BusinessException("用户名不能为空");
        }
        // 业务处理
        User user = new User();
        BeanUtils.copyProperties(dto, user);
        userMapper.insert(user);
    }
}
```

### 3.3 Mybatis规范
```java
// 推荐查询写法
SelectStatementProvider selectStatement = select(user.allColumns())
    .from(user)
    .where(user.id, isEqualTo(id))
    .and(user.status, isNotEqualTo(0))
    .orderBy(user.createTime.descending())
    .build()
    .render(RenderingStrategies.MYBATIS3);

// 批量插入
BatchInsert<User> batchInsert = insert(userList)
    .into(user)
    .map(id).toProperty("id")
    .map(name).toProperty("name")
    .build()
    .render(RenderingStrategies.MYBATIS3);
```

## 4. 数据库规范

### 4.1 基础字段
```sql
`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`create_by` varchar(64) NOT NULL COMMENT '创建人',
`update_by` varchar(64) NOT NULL COMMENT '更新人',
`is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除'
```

### 4.2 SQL规范
```sql
-- 分页查询（必须带软删除条件）
SELECT * FROM table
WHERE is_deleted = 0
  AND condition
ORDER BY id DESC
LIMIT #{offset}, #{size}

-- 更新（必须带软删除条件）
UPDATE table 
SET status = #{status},
    update_time = NOW(),
    update_by = #{operator}
WHERE id = #{id}
  AND is_deleted = 0
```

## 5. 异常处理规范

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(BusinessException.class)
    public Result<?> handleBusinessException(BusinessException e) {
        log.error("业务异常", e);
        return Result.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public Result<?> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error(500, "系统异常");
    }
}

// 业务异常
public class BusinessException extends RuntimeException {
    private int code;
    private String message;
    
    public BusinessException(String message) {
        this(500, message);
    }
    
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
}
```