-- =====================================================
-- Auto Care SaaS 系统表结构脚本
-- 数据库：auto_care_saas
-- 说明：系统级别的公共表，为所有租户提供基础服务
-- 创建时间：2025-01-19
-- =====================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `auto_care_saas` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE `auto_care_saas`;

-- =====================================================
-- 租户管理模块
-- =====================================================

-- 1. 租户信息表
CREATE TABLE IF NOT EXISTS `sys_tenant` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_name` VARCHAR(100) NOT NULL COMMENT '租户名称',
    `tenant_code` VARCHAR(50) NOT NULL COMMENT '租户编码',
    `contact_name` VARCHAR(50) DEFAULT NULL COMMENT '联系人姓名',
    `contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系人电话',
    `contact_email` VARCHAR(100) DEFAULT NULL COMMENT '联系人邮箱',
    `expire_time` DATETIME DEFAULT NULL COMMENT '租约过期时间',
    `max_user_count` INT DEFAULT 0 COMMENT '最大用户数量',
    `status` INT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_code`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户信息表';

-- 2. 租户同步密钥表
CREATE TABLE IF NOT EXISTS `sys_tenant_sync_key` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `tenant_code` VARCHAR(50) NOT NULL COMMENT '租户编码',
    `sync_key` VARCHAR(128) NOT NULL COMMENT '同步密钥（用于租户身份识别）',
    `encryption_key` VARCHAR(512) DEFAULT NULL COMMENT '加密密钥（Base64编码，已废弃）',
    `key_expire_time` DATETIME DEFAULT NULL COMMENT '密钥过期时间',
    `is_active` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    `update_by` VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_sync_key` (`sync_key`),
    UNIQUE KEY `uk_tenant_id` (`tenant_id`),
    KEY `idx_tenant_code` (`tenant_code`),
    KEY `idx_key_expire` (`key_expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户同步密钥表';

-- =====================================================
-- 用户权限管理模块
-- =====================================================

-- 3. 用户信息表
CREATE TABLE IF NOT EXISTS `sys_user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `mobile` VARCHAR(11) NOT NULL COMMENT '手机号',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像',
    `email` VARCHAR(50) DEFAULT NULL COMMENT '邮箱',
    `status` INT NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-启用',
    `account_type` TINYINT NOT NULL DEFAULT '0' COMMENT '账号类型:0-普通,1-修理厂',
    `garage_id` BIGINT DEFAULT NULL COMMENT '关联修理厂ID',
    `org_id` BIGINT DEFAULT NULL COMMENT '组织ID',
    `approval_level` INT DEFAULT NULL COMMENT '审批层级（0：无，1：一级，2：二级，3：三级，4：四级）',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_mobile` (`mobile`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_approval_level` (`approval_level`),
    KEY `idx_account_type` (`account_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- 4. 角色信息表
CREATE TABLE IF NOT EXISTS `sys_role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
    `description` VARCHAR(200) DEFAULT NULL COMMENT '角色描述',
    `status` INT NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code_tenant` (`role_code`, `tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色信息表';

-- 5. 权限信息表
CREATE TABLE IF NOT EXISTS `sys_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `permission_name` VARCHAR(50) NOT NULL COMMENT '权限名称',
    `permission_code` VARCHAR(100) NOT NULL COMMENT '权限编码',
    `permission_type` VARCHAR(20) NOT NULL COMMENT '权限类型：menu-菜单，button-按钮',
    `parent_id` BIGINT DEFAULT '0' COMMENT '父权限ID',
    `path` VARCHAR(200) DEFAULT NULL COMMENT '路由路径',
    `component` VARCHAR(200) DEFAULT NULL COMMENT '组件路径',
    `icon` VARCHAR(50) DEFAULT NULL COMMENT '图标',
    `sort_order` INT DEFAULT '0' COMMENT '排序',
    `status` INT NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_permission_type` (`permission_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限信息表';

-- 6. 用户角色关联表
CREATE TABLE IF NOT EXISTS `sys_user_role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 7. 角色权限关联表
CREATE TABLE IF NOT EXISTS `sys_role_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `permission_id` BIGINT NOT NULL COMMENT '权限ID',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- =====================================================
-- 数据同步模块
-- =====================================================

-- 8. 数据同步日志表
CREATE TABLE IF NOT EXISTS `sys_data_sync_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `batch_no` VARCHAR(64) NOT NULL COMMENT '同步批次号',
    `target_table` VARCHAR(64) NOT NULL COMMENT '目标表名',
    `sync_status` VARCHAR(16) NOT NULL COMMENT '同步状态：SUCCESS、FAILED、PROCESSING',
    `error_message` VARCHAR(1000) DEFAULT NULL COMMENT '失败原因',
    `source_ip` VARCHAR(64) DEFAULT NULL COMMENT '请求来源IP',
    `sync_start_time` DATETIME NOT NULL COMMENT '同步开始时间',
    `sync_end_time` DATETIME DEFAULT NULL COMMENT '同步结束时间',
    `sync_duration` BIGINT DEFAULT NULL COMMENT '同步耗时（毫秒）',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `idx_table_status_time` (`target_table`, `sync_status`, `sync_start_time`),
    KEY `idx_batch_status` (`batch_no`, `sync_status`),
    KEY `idx_sync_start_time` (`sync_start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据同步日志表';

-- =====================================================
-- 验证码与短信服务模块
-- =====================================================

-- 9. 验证码表
CREATE TABLE IF NOT EXISTS `sys_verification_code` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `mobile` VARCHAR(11) NOT NULL COMMENT '手机号',
    `code` VARCHAR(6) NOT NULL COMMENT '验证码',
    `type` VARCHAR(20) NOT NULL COMMENT '验证码类型:LOGIN-登录,REGISTER-注册',
    `fail_count` INT NOT NULL DEFAULT 0 COMMENT '失败次数',
    `status` INT NOT NULL DEFAULT 0 COMMENT '状态：0-未使用，1-已使用',
    `expire_time` DATETIME NOT NULL COMMENT '过期时间',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_mobile_type` (`mobile`, `type`),
    KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证码表';

-- 10. 短信发送记录表
CREATE TABLE IF NOT EXISTS `sms_send_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `phone_number` VARCHAR(11) NOT NULL COMMENT '手机号',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    `type` VARCHAR(20) NOT NULL COMMENT '短信类型',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_phone_time` (`phone_number`, `create_time`),
    KEY `idx_ip_time` (`ip_address`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信发送记录表';

-- =====================================================
-- 创建外键约束
-- =====================================================

-- 租户同步密钥表外键
ALTER TABLE `sys_tenant_sync_key` ADD CONSTRAINT `fk_tenant_sync_key_tenant` 
    FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`) ON DELETE CASCADE;

-- 用户表外键
ALTER TABLE `sys_user` ADD CONSTRAINT `fk_user_tenant` 
    FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`) ON DELETE CASCADE;

-- 角色表外键
ALTER TABLE `sys_role` ADD CONSTRAINT `fk_role_tenant` 
    FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`) ON DELETE CASCADE;

-- 用户角色关联表外键
ALTER TABLE `sys_user_role` ADD CONSTRAINT `fk_user_role_user` 
    FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE;
ALTER TABLE `sys_user_role` ADD CONSTRAINT `fk_user_role_role` 
    FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`) ON DELETE CASCADE;

-- 角色权限关联表外键
ALTER TABLE `sys_role_permission` ADD CONSTRAINT `fk_role_permission_role` 
    FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`) ON DELETE CASCADE;
ALTER TABLE `sys_role_permission` ADD CONSTRAINT `fk_role_permission_permission` 
    FOREIGN KEY (`permission_id`) REFERENCES `sys_permission` (`id`) ON DELETE CASCADE;

-- =====================================================
-- 脚本执行完成
-- =====================================================
