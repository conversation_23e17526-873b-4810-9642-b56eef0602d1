# 数据库文档

## 📋 文档概述

本目录包含Auto Care SaaS项目的完整数据库文档，涵盖表结构设计、字段说明、索引约束、SQL脚本等内容。

## 🗂️ 文档结构

### 📖 核心文档
- [数据库字典](./数据库字典.md) - 完整的数据库表和字段说明
- [索引和约束说明](./索引和约束说明.md) - 数据库索引和约束设计

### 📊 表结构详细说明
- [系统表](./table-structures/系统表结构.md) - 系统级别的基础表结构
- [多租户表](./table-structures/多租户表结构.md) - 租户相关的业务表结构
- [工作流表](./table-structures/工作流表结构.md) - 工作流引擎相关表结构
- [业务表](./table-structures/业务表结构.md) - 车辆维修业务相关表结构

### 💾 SQL脚本
- [建表脚本](./sql/schema/) - 按模块分类的建表SQL脚本
- [初始化数据](./sql/data/) - 系统初始化数据脚本
- [数据库迁移](./sql/migration/) - 数据库版本迁移脚本

## 🏗️ 数据库架构

### 多租户架构设计
- **系统库**：`auto_care_saas` - 存储系统级别的公共数据
- **租户库**：`auto_care_tenant_{tenant_code}` - 存储租户专属的业务数据

### 表命名规范
- **系统表**：`sys_` 前缀 - 系统级别的公共表
- **多租户表**：`mtc_` 前缀 - 租户专属的业务表

### 字段命名规范
- 使用下划线分隔的小写字母
- 主键统一使用 `id` 字段
- 外键使用 `{table_name}_id` 格式
- 时间字段使用 `{action}_time` 格式

## 📋 表分类说明

### 系统表 (sys_*)
| 表名 | 说明 | 用途 |
|------|------|------|
| sys_tenant | 租户信息表 | 管理租户基本信息 |
| sys_user | 用户信息表 | 管理用户账号信息 |
| sys_role | 角色信息表 | 管理系统角色 |
| sys_permission | 权限信息表 | 管理系统权限 |
| sys_data_sync_log | 数据同步日志表 | 记录数据同步操作 |

### 多租户表 (mtc_*)
| 表名 | 说明 | 用途 |
|------|------|------|
| mtc_vehicle_info | 车辆信息表 | 管理车辆基本信息 |
| mtc_repair_depot_info | 维修厂信息表 | 管理维修厂信息 |
| mtc_vehicle_repair_task | 车辆维修任务表 | 管理维修任务 |
| mtc_org_info | 组织机构表 | 管理组织架构 |

### 工作流表 (workflow_*)
| 表名 | 说明 | 用途 |
|------|------|------|
| workflow_template | 工作流模板表 | 定义工作流模板 |
| workflow_instance | 工作流实例表 | 管理工作流实例 |
| activity_definition | 活动定义表 | 定义工作流节点 |
| activity_instance | 活动实例表 | 管理节点实例 |

## 🔧 数据库维护

### 版本管理
- 使用Flyway进行数据库版本管理
- 迁移脚本命名格式：`V{version}__{description}.sql`
- 所有结构变更必须通过迁移脚本执行

### 备份策略
- 每日自动备份生产数据库
- 重要操作前手动备份
- 保留30天的备份历史

### 性能优化
- 定期分析慢查询日志
- 优化高频查询的索引
- 监控表空间使用情况

## 📞 联系方式

数据库相关问题请联系：
- DBA团队：<EMAIL>
- 开发团队：<EMAIL>

---

> 📝 **文档版本**：v2.0  
> 🕒 **最后更新**：2025-01-19  
> 👥 **维护团队**：Auto Care DBA Team
