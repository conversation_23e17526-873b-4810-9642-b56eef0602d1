# 数据同步服务数据库表结构说明

## 概述

数据同步服务使用简化的表结构设计，主要包含一个系统级的同步日志表。本文档详细说明了表结构、字段含义、索引设计和使用说明。

## 表结构设计原则

### 1. 系统级表设计
- 使用 `sys_` 前缀表示系统级表，不按租户分表
- 所有租户的同步日志统一存储，便于系统级监控和管理
- 通过应用层逻辑实现租户数据隔离

### 2. 简化设计
- 移除冗余字段，保留核心业务字段
- 优化存储空间，提高查询性能
- 简化维护复杂度

### 3. 审计设计
- 包含完整的审计字段（创建时间、创建人、更新时间、更新人）
- 支持操作追踪和数据变更历史

## 核心表结构

### sys_data_sync_log (数据同步日志表)

#### 表定义
```sql
CREATE TABLE `sys_data_sync_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_no` varchar(64) NOT NULL COMMENT '同步批次号',
  `target_table` varchar(64) NOT NULL COMMENT '目标表名',
  `sync_status` varchar(16) NOT NULL COMMENT '同步状态：SUCCESS、FAILED、PROCESSING',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '失败原因',
  `source_ip` varchar(64) DEFAULT NULL COMMENT '请求来源IP',
  `sync_start_time` datetime NOT NULL COMMENT '同步开始时间',
  `sync_end_time` datetime DEFAULT NULL COMMENT '同步结束时间',
  `sync_duration` bigint(20) DEFAULT NULL COMMENT '同步耗时（毫秒）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` varchar(64) NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_by` varchar(64) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_target_table` (`target_table`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_sync_start_time` (`sync_start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据同步日志表（系统级表，不按租户分表）';
```

#### 字段说明

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | AUTO_INCREMENT | 主键ID，自增长 |
| batch_no | varchar | 64 | 是 | - | 同步批次号，格式：SYNC_{timestamp}_{random} |
| target_table | varchar | 64 | 是 | - | 目标表名，如：mtc_vehicle_info |
| sync_status | varchar | 16 | 是 | - | 同步状态：SUCCESS、FAILED、PROCESSING |
| error_message | varchar | 1000 | 否 | NULL | 失败原因，成功时为空 |
| source_ip | varchar | 64 | 否 | NULL | 请求来源IP地址 |
| sync_start_time | datetime | - | 是 | - | 同步开始时间 |
| sync_end_time | datetime | - | 否 | NULL | 同步结束时间 |
| sync_duration | bigint | 20 | 否 | NULL | 同步耗时（毫秒） |
| create_time | datetime | - | 是 | - | 记录创建时间 |
| create_by | varchar | 64 | 是 | - | 记录创建人 |
| update_time | datetime | - | 是 | - | 记录更新时间 |
| update_by | varchar | 64 | 是 | - | 记录更新人 |

#### 索引设计

| 索引名 | 索引类型 | 字段 | 说明 |
|--------|----------|------|------|
| PRIMARY | 主键索引 | id | 主键，唯一标识 |
| idx_batch_no | 普通索引 | batch_no | 批次号查询，高频使用 |
| idx_target_table | 普通索引 | target_table | 按表名查询 |
| idx_sync_status | 普通索引 | sync_status | 按状态查询 |
| idx_create_time | 普通索引 | create_time | 按时间范围查询，日志清理 |
| idx_sync_start_time | 普通索引 | sync_start_time | 按同步时间查询 |

#### 状态枚举

**sync_status 字段值说明**:
- `SUCCESS`: 同步成功
- `FAILED`: 同步失败
- `PROCESSING`: 同步处理中（预留状态，当前版本未使用）

## 已删除的字段

### 简化前的字段（已移除）
以下字段在表结构简化过程中已被移除：

| 字段名 | 原用途 | 移除原因 |
|--------|--------|----------|
| tenant_id | 租户ID | 系统级表不需要租户字段 |
| tenant_code | 租户编码 | 系统级表不需要租户字段 |
| source_data_id | 源数据标识 | 简化设计，减少冗余 |
| target_data_id | 目标数据ID | 简化设计，减少冗余 |
| before_data | 变更前数据 | 简化设计，减少存储 |
| after_data | 变更后数据 | 简化设计，减少存储 |
| operation_type | 操作类型 | 统一为AUTO自动判断 |

### 影响说明
1. **重试功能**: 由于移除了原始数据字段，重试功能暂时不可用
2. **数据追踪**: 无法追踪具体的数据变更内容
3. **租户隔离**: 通过应用层逻辑实现，不依赖数据库字段

## 数据生命周期

### 1. 数据写入
- 每次同步操作都会创建日志记录
- 批量同步会为每条数据创建独立的日志记录
- 自动填充审计字段（创建时间、创建人等）

### 2. 数据查询
- 支持按批次号查询同步结果
- 支持按表名查询历史同步记录
- 支持按状态查询失败或成功的记录
- 支持按时间范围查询

### 3. 数据清理
- 提供过期日志清理功能
- 默认保留策略可配置
- 支持手动和自动清理

## 查询示例

### 1. 按批次号查询
```sql
SELECT * FROM sys_data_sync_log 
WHERE batch_no = 'SYNC_1640995200000_1234'
ORDER BY create_time;
```

### 2. 查询失败的同步记录
```sql
SELECT * FROM sys_data_sync_log 
WHERE sync_status = 'FAILED'
  AND create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
ORDER BY create_time DESC;
```

### 3. 按表名统计同步情况
```sql
SELECT 
  target_table,
  COUNT(*) as total_count,
  SUM(CASE WHEN sync_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
  SUM(CASE WHEN sync_status = 'FAILED' THEN 1 ELSE 0 END) as failed_count
FROM sys_data_sync_log 
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY target_table;
```

### 4. 清理过期日志
```sql
DELETE FROM sys_data_sync_log 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

## 性能优化建议

### 1. 索引优化
- 根据查询模式调整索引策略
- 定期分析慢查询并优化
- 考虑复合索引的使用

### 2. 分区策略
- 对于大数据量场景，考虑按时间分区
- 分区可以提高查询性能和维护效率

### 3. 归档策略
- 定期归档历史数据
- 保留近期数据在线查询
- 历史数据可移至归档表或文件

## 监控指标

### 1. 数据量监控
- 每日新增记录数
- 表总记录数
- 数据增长趋势

### 2. 性能监控
- 查询响应时间
- 索引使用情况
- 锁等待情况

### 3. 业务监控
- 同步成功率
- 失败原因分布
- 同步耗时分析

## 维护操作

### 1. 日常维护
```sql
-- 检查表状态
SHOW TABLE STATUS LIKE 'sys_data_sync_log';

-- 分析表
ANALYZE TABLE sys_data_sync_log;

-- 优化表
OPTIMIZE TABLE sys_data_sync_log;
```

### 2. 索引维护
```sql
-- 检查索引使用情况
SHOW INDEX FROM sys_data_sync_log;

-- 重建索引（如需要）
ALTER TABLE sys_data_sync_log DROP INDEX idx_create_time;
ALTER TABLE sys_data_sync_log ADD INDEX idx_create_time (create_time);
```

### 3. 数据清理
```sql
-- 清理30天前的数据
DELETE FROM sys_data_sync_log 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
LIMIT 1000;
```

## 注意事项

1. **字符集**: 使用 utf8mb4 字符集，支持完整的Unicode字符
2. **存储引擎**: 使用 InnoDB 引擎，支持事务和外键
3. **时区**: 所有时间字段使用系统默认时区（GMT+8）
4. **备份**: 定期备份重要数据，特别是在大批量操作前
5. **权限**: 严格控制表的访问权限，只允许应用程序访问

## 版本历史

### v1.0 (当前版本)
- 简化表结构，移除冗余字段
- 优化索引设计
- 完善文档说明

### 未来版本规划
- 考虑添加分区支持
- 优化大数据量场景的性能
- 增加更多监控指标
