# 系统表结构说明

## 📋 概述

系统表（sys_*）是Auto Care SaaS项目的核心基础表，存储在主数据库`auto_care_saas`中，为所有租户提供公共的系统级服务。

## 🏗️ 表结构详细说明

### 1. sys_tenant - 租户信息表

**表说明**：管理系统中所有租户的基本信息，是多租户架构的核心表。

```sql
CREATE TABLE `sys_tenant` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_name` VARCHAR(100) NOT NULL COMMENT '租户名称',
    `tenant_code` VARCHAR(50) NOT NULL COMMENT '租户编码',
    `contact_name` VARCHAR(50) DEFAULT NULL COMMENT '联系人姓名',
    `contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系人电话',
    `contact_email` VARCHAR(100) DEFAULT NULL COMMENT '联系人邮箱',
    `expire_time` DATETIME DEFAULT NULL COMMENT '租约过期时间',
    `max_user_count` INT DEFAULT 0 COMMENT '最大用户数量',
    `status` INT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户信息表';
```

**字段说明**：
- `tenant_code`：租户唯一标识，用于数据库Schema命名和数据隔离
- `expire_time`：租约到期时间，系统会根据此时间控制租户访问权限
- `max_user_count`：租户最大用户数限制，0表示无限制
- `status`：租户状态，禁用后该租户下所有用户无法登录

### 2. sys_user - 用户信息表

**表说明**：管理系统中所有用户的账号信息，支持多租户用户隔离。

```sql
CREATE TABLE `sys_user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `mobile` VARCHAR(11) NOT NULL COMMENT '手机号',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像',
    `email` VARCHAR(50) DEFAULT NULL COMMENT '邮箱',
    `status` INT NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-启用',
    `account_type` TINYINT NOT NULL DEFAULT '0' COMMENT '账号类型:0-普通,1-修理厂',
    `garage_id` BIGINT DEFAULT NULL COMMENT '关联修理厂ID',
    `org_id` BIGINT DEFAULT NULL COMMENT '组织ID',
    `approval_level` INT DEFAULT NULL COMMENT '审批层级（0：无，1：一级，2：二级，3：三级，4：四级）',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_mobile` (`mobile`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_approval_level` (`approval_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';
```

**字段说明**：
- `tenant_id`：关联租户ID，实现用户的租户隔离
- `account_type`：账号类型，0-普通用户，1-修理厂用户
- `garage_id`：修理厂用户关联的修理厂ID
- `approval_level`：用户的审批权限级别，用于工作流审批

### 3. sys_role - 角色信息表

**表说明**：管理系统角色信息，支持租户级别的角色隔离。

```sql
CREATE TABLE `sys_role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
    `description` VARCHAR(200) DEFAULT NULL COMMENT '角色描述',
    `status` INT NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code_tenant` (`role_code`, `tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色信息表';
```

**字段说明**：
- `role_code`：角色编码，在同一租户内唯一
- `tenant_id`：租户ID，-1表示系统级角色，其他值表示租户专属角色

### 4. sys_permission - 权限信息表

**表说明**：管理系统权限信息，采用树形结构支持菜单和按钮权限。

```sql
CREATE TABLE `sys_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `permission_name` VARCHAR(50) NOT NULL COMMENT '权限名称',
    `permission_code` VARCHAR(100) NOT NULL COMMENT '权限编码',
    `permission_type` VARCHAR(20) NOT NULL COMMENT '权限类型：menu-菜单，button-按钮',
    `parent_id` BIGINT DEFAULT '0' COMMENT '父权限ID',
    `path` VARCHAR(200) DEFAULT NULL COMMENT '路由路径',
    `component` VARCHAR(200) DEFAULT NULL COMMENT '组件路径',
    `icon` VARCHAR(50) DEFAULT NULL COMMENT '图标',
    `sort_order` INT DEFAULT '0' COMMENT '排序',
    `status` INT NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限信息表';
```

**字段说明**：
- `permission_type`：权限类型，menu-菜单权限，button-按钮权限
- `parent_id`：父权限ID，0表示顶级权限，支持树形结构
- `path`：前端路由路径，仅菜单权限使用
- `component`：前端组件路径，仅菜单权限使用

### 5. sys_data_sync_log - 数据同步日志表

**表说明**：记录多租户数据同步操作的详细日志，用于监控和故障排查。

```sql
CREATE TABLE `sys_data_sync_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `batch_no` VARCHAR(64) NOT NULL COMMENT '同步批次号',
    `target_table` VARCHAR(64) NOT NULL COMMENT '目标表名',
    `sync_status` VARCHAR(16) NOT NULL COMMENT '同步状态：SUCCESS、FAILED、PROCESSING',
    `error_message` VARCHAR(1000) DEFAULT NULL COMMENT '失败原因',
    `source_ip` VARCHAR(64) DEFAULT NULL COMMENT '请求来源IP',
    `sync_start_time` DATETIME NOT NULL COMMENT '同步开始时间',
    `sync_end_time` DATETIME DEFAULT NULL COMMENT '同步结束时间',
    `sync_duration` BIGINT DEFAULT NULL COMMENT '同步耗时（毫秒）',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `idx_table_status_time` (`target_table`, `sync_status`, `sync_start_time`),
    KEY `idx_batch_status` (`batch_no`, `sync_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据同步日志表';
```

**字段说明**：
- `batch_no`：同步批次号，用于关联同一批次的多条同步记录
- `sync_status`：同步状态，SUCCESS-成功，FAILED-失败，PROCESSING-处理中
- `sync_duration`：同步耗时，单位毫秒，用于性能监控

## 🔗 表关系说明

### 主要外键关系
- `sys_user.tenant_id` → `sys_tenant.id`
- `sys_role.tenant_id` → `sys_tenant.id`
- `sys_user_role.user_id` → `sys_user.id`
- `sys_user_role.role_id` → `sys_role.id`
- `sys_role_permission.role_id` → `sys_role.id`
- `sys_role_permission.permission_id` → `sys_permission.id`

### 数据隔离策略
- 租户级隔离：用户、角色通过tenant_id实现租户隔离
- 系统级共享：权限信息为系统级共享，所有租户使用相同的权限定义
- 日志级记录：数据同步日志记录所有租户的操作，便于系统监控

---

> 📝 **说明**：系统表是多租户架构的基础，任何修改都需要谨慎评估影响范围。  
> 🕒 **最后更新**：2025-01-19  
> 👥 **维护团队**：Auto Care DBA Team
