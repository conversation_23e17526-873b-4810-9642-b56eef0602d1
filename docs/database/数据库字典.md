# 数据库字典

## 📋 概述

本文档提供Auto Care SaaS项目的完整数据库字典，包含所有表的结构定义、字段说明、数据类型、约束条件等详细信息。

## 🏗️ 数据库架构

### 数据库列表
- **auto_care_saas** - 系统主库，存储公共数据
- **auto_care_tenant_{tenant_code}** - 租户专属库，存储业务数据

### 表命名规范
- **sys_** - 系统级别公共表
- **mtc_** - 多租户业务表  
- **workflow_** - 工作流相关表
- **activity_** - 工作流活动表

## 📊 系统表 (sys_*)

### sys_tenant - 租户信息表
| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键 |
| tenant_name | VARCHAR | 100 | NOT NULL | - | 租户名称 |
| tenant_code | VARCHAR | 50 | NOT NULL | - | 租户编码 |
| contact_name | VARCHAR | 50 | NULL | - | 联系人姓名 |
| contact_phone | VARCHAR | 20 | NULL | - | 联系人电话 |
| contact_email | VARCHAR | 100 | NULL | - | 联系人邮箱 |
| expire_time | DATETIME | - | NULL | - | 租约过期时间 |
| max_user_count | INT | - | NULL | 0 | 最大用户数量 |
| status | INT | - | NULL | 1 | 状态：0-禁用，1-启用 |
| create_by | VARCHAR | 128 | NOT NULL | '' | 创建人 |
| created_time | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_by | VARCHAR | 128 | NOT NULL | '' | 更新人 |
| updated_time | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- UNIQUE KEY uk_tenant_code (tenant_code)

### sys_user - 用户信息表
| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键 |
| tenant_id | BIGINT | - | NOT NULL | - | 租户ID |
| username | VARCHAR | 50 | NOT NULL | - | 用户名 |
| mobile | VARCHAR | 11 | NOT NULL | - | 手机号 |
| nickname | VARCHAR | 50 | NULL | - | 真实姓名 |
| avatar | VARCHAR | 255 | NULL | - | 头像 |
| email | VARCHAR | 50 | NULL | - | 邮箱 |
| status | INT | - | NOT NULL | 1 | 状态:0-禁用,1-启用 |
| account_type | TINYINT | - | NOT NULL | 0 | 账号类型:0-普通,1-修理厂 |
| garage_id | BIGINT | - | NULL | - | 关联修理厂ID |
| org_id | BIGINT | - | NULL | - | 组织ID |
| approval_level | INT | - | NULL | - | 审批层级（0：无，1：一级，2：二级，3：三级，4：四级） |
| create_by | VARCHAR | 128 | NOT NULL | '' | 创建人 |
| created_time | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_by | VARCHAR | 128 | NOT NULL | '' | 更新人 |
| updated_time | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- UNIQUE KEY uk_username (username)
- UNIQUE KEY uk_mobile (mobile)
- KEY idx_tenant_id (tenant_id)
- KEY idx_approval_level (approval_level)

### sys_role - 角色信息表
| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键 |
| role_name | VARCHAR | 50 | NOT NULL | - | 角色名称 |
| role_code | VARCHAR | 50 | NOT NULL | - | 角色编码 |
| description | VARCHAR | 200 | NULL | - | 角色描述 |
| status | INT | - | NOT NULL | 1 | 状态：0-禁用，1-启用 |
| tenant_id | BIGINT | - | NOT NULL | - | 租户ID |
| create_by | VARCHAR | 128 | NOT NULL | '' | 创建人 |
| created_time | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_by | VARCHAR | 128 | NOT NULL | '' | 更新人 |
| updated_time | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- UNIQUE KEY uk_role_code_tenant (role_code, tenant_id)
- KEY idx_tenant_id (tenant_id)

### sys_permission - 权限信息表
| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键 |
| permission_name | VARCHAR | 50 | NOT NULL | - | 权限名称 |
| permission_code | VARCHAR | 100 | NOT NULL | - | 权限编码 |
| permission_type | VARCHAR | 20 | NOT NULL | - | 权限类型：menu-菜单，button-按钮 |
| parent_id | BIGINT | - | NULL | 0 | 父权限ID |
| path | VARCHAR | 200 | NULL | - | 路由路径 |
| component | VARCHAR | 200 | NULL | - | 组件路径 |
| icon | VARCHAR | 50 | NULL | - | 图标 |
| sort_order | INT | - | NULL | 0 | 排序 |
| status | INT | - | NOT NULL | 1 | 状态：0-禁用，1-启用 |
| create_by | VARCHAR | 128 | NOT NULL | '' | 创建人 |
| created_time | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_by | VARCHAR | 128 | NOT NULL | '' | 更新人 |
| updated_time | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- UNIQUE KEY uk_permission_code (permission_code)
- KEY idx_parent_id (parent_id)

### sys_data_sync_log - 数据同步日志表
| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键ID |
| batch_no | VARCHAR | 64 | NOT NULL | - | 同步批次号 |
| target_table | VARCHAR | 64 | NOT NULL | - | 目标表名 |
| sync_status | VARCHAR | 16 | NOT NULL | - | 同步状态：SUCCESS、FAILED、PROCESSING |
| error_message | VARCHAR | 1000 | NULL | - | 失败原因 |
| source_ip | VARCHAR | 64 | NULL | - | 请求来源IP |
| sync_start_time | DATETIME | - | NOT NULL | - | 同步开始时间 |
| sync_end_time | DATETIME | - | NULL | - | 同步结束时间 |
| sync_duration | BIGINT | - | NULL | - | 同步耗时（毫秒） |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| create_by | VARCHAR | 64 | NULL | - | 创建人 |

**索引：**
- PRIMARY KEY (id)
- KEY idx_table_status_time (target_table, sync_status, sync_start_time)
- KEY idx_batch_status (batch_no, sync_status)

## 📊 多租户表 (mtc_*)

### mtc_vehicle_info - 车辆信息表
| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键 |
| vin | VARCHAR | 17 | NOT NULL | - | 车架号 |
| vehicle_no | VARCHAR | 10 | NOT NULL | - | 车牌号 |
| vehicle_model_id | BIGINT | - | NULL | - | 车型ID |
| vehicle_org_id | VARCHAR | 20 | NULL | - | 维修时所属公司 |
| operation_org_id | VARCHAR | 20 | NULL | - | 维修时运营公司 |
| product_line | INT | - | NULL | - | 产品线 |
| sub_product_line | INT | - | NULL | - | 子产品线 |
| fact_operate_tag | INT | - | NULL | - | 实际运营标签 |
| engine_id | VARCHAR | 20 | NULL | - | 发动机号 |
| register_date | DATE | - | NULL | - | 注册日期 |
| tci_startdate | DATE | - | NULL | - | 交强险开始日期 |
| tci_enddate | DATE | - | NULL | - | 交强险结束日期 |
| insurance_belongs | INT | - | NULL | - | 保险归属 |
| insurance_company_name | VARCHAR | 100 | NULL | - | 保险公司名称 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| create_by | VARCHAR | 64 | NOT NULL | - | 创建人 |
| update_by | VARCHAR | 64 | NOT NULL | - | 更新人 |
| is_deleted | TINYINT | - | NOT NULL | 0 | 是否删除 |

**索引：**
- PRIMARY KEY (id)
- UNIQUE KEY uk_vin (vin)
- KEY idx_vehicle_no (vehicle_no)
- KEY idx_vehicle_model_id (vehicle_model_id)

## 📊 工作流表 (workflow_*)

### workflow_template - 工作流模板表
| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键 |
| workflow_name | VARCHAR | 100 | NOT NULL | - | 工作流名称 |
| description | VARCHAR | 500 | NULL | - | 描述 |
| task_type | INT | - | NOT NULL | - | 任务类型 |
| repair_factory_type | INT | - | NOT NULL | - | 维修厂类型 |
| sub_product_line | INT | - | NULL | - | 子产品线 |
| is_active | BOOLEAN | - | NOT NULL | true | 是否启用 |
| tenant_id | BIGINT | - | NULL | - | 租户ID |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| create_by | VARCHAR | 64 | NOT NULL | - | 创建人 |
| update_by | VARCHAR | 64 | NOT NULL | - | 更新人 |

**索引：**
- PRIMARY KEY (id)
- KEY idx_tenant_id (tenant_id)
- KEY idx_task_type (task_type)

---

> 📝 **说明**：本文档仅展示主要表结构，完整的表结构请参考各模块的详细文档。  
> 🕒 **最后更新**：2025-01-19  
> 👥 **维护团队**：Auto Care DBA Team
