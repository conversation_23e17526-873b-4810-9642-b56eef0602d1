# 租户有效期校验功能

## 功能概述

在用户登录过程中添加租户有效期校验逻辑，确保只有状态正常且未过期的租户用户才能成功登录系统。

## 实现内容

### 1. 错误码扩展

在 `ErrorCode` 枚举中新增了两个租户相关的错误码：

```java
TENANT_EXPIRED(240004, "商户已过期，请联系管理员"),
TENANT_DISABLED(240005, "商户状态异常，无法登录"),
```

### 2. 租户校验逻辑

在 `AuthServiceImpl` 中新增了 `validateTenantStatus` 私有方法，用于校验租户状态和有效期：

```java
private void validateTenantStatus(Long tenantId) {
    if (tenantId == null) {
        logger.warn("租户ID为空，跳过租户校验");
        return;
    }

    try {
        SysTenant tenant = tenantService.selectById(tenantId);
        if (tenant == null) {
            logger.warn("租户不存在，租户ID: {}", tenantId);
            throw new BusinessException(ErrorCode.TENANT_NOT_FOUND);
        }

        // 检查租户状态
        if (tenant.getStatus() == null || tenant.getStatus() != 1) {
            logger.warn("租户状态异常，租户ID: {}, 状态: {}", tenantId, tenant.getStatus());
            throw new BusinessException(ErrorCode.TENANT_DISABLED);
        }

        // 检查租户有效期
        if (tenant.getExpireTime() != null) {
            Date currentTime = new Date();
            if (currentTime.after(tenant.getExpireTime())) {
                logger.warn("租户已过期，租户ID: {}, 过期时间: {}, 当前时间: {}", 
                           tenantId, tenant.getExpireTime(), currentTime);
                throw new BusinessException(ErrorCode.TENANT_EXPIRED);
            }
        }

        logger.debug("租户校验通过，租户ID: {}, 租户名称: {}", tenantId, tenant.getTenantName());
    } catch (BusinessException e) {
        // 重新抛出业务异常
        throw e;
    } catch (Exception e) {
        logger.error("租户校验过程中发生异常，租户ID: {}", tenantId, e);
        throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR, "租户校验失败");
    }
}
```

### 3. 登录方法集成

#### 3.1 loginByMobileWithTenant 方法

在验证码校验成功、用户状态检查通过后，添加租户校验：

```java
// 5. 校验租户状态和有效期
validateTenantStatus(user.getTenantId());
```

如果租户已过期或状态异常，会抛出相应的 `BusinessException`。

#### 3.2 loginByMobile 方法

同样在验证码校验成功、用户状态检查通过后，添加租户校验：

```java
// 4. 校验租户状态和有效期
try {
    validateTenantStatus(user.getTenantId());
} catch (BusinessException e) {
    logger.warn("租户校验失败, 用户ID: {}, 手机号: {}, 错误: {}", user.getId(), mobile, e.getMessage());
    return null;
}
```

对于 `loginByMobile` 方法，租户校验失败时返回 `null`，保持与原有错误处理方式的一致性。

### 4. 租户账号列表过滤

在 `getUserTenantAccounts` 方法中增强了租户过滤逻辑，不仅检查租户状态，还检查有效期：

```java
// 2. 查询租户信息并构建下拉列表
List<ComboVO<String>> result = new ArrayList<>();
Date currentTime = new Date();
for (Long tenantId : tenantIds) {
    SysTenant tenant = tenantService.selectById(tenantId);
    if (tenant != null && tenant.getStatus() == 1) { // 只返回启用的租户
        // 检查租户有效期
        boolean isExpired = tenant.getExpireTime() != null && currentTime.after(tenant.getExpireTime());
        if (!isExpired) { // 只返回未过期的租户
            ComboVO<String> combo = new ComboVO<>();
            combo.setId(String.valueOf(tenant.getId()));
            combo.setValue(tenant.getTenantName());
            result.add(combo);
        } else {
            logger.debug("租户已过期，不返回给用户选择，租户ID: {}, 租户名称: {}, 过期时间: {}", 
                       tenantId, tenant.getTenantName(), tenant.getExpireTime());
        }
    }
}
```

## 校验规则

### 1. 租户状态校验
- 租户必须存在
- 租户状态必须为启用（status = 1）

### 2. 租户有效期校验
- 如果租户设置了过期时间（expireTime 不为 null），则检查当前时间是否超过过期时间
- 如果未设置过期时间，则视为永久有效

### 3. 校验时机
- 在验证码校验成功后
- 在用户状态检查通过后
- 在生成 JWT token 之前

## 错误处理

### 1. loginByMobileWithTenant 方法
- 租户过期：抛出 `BusinessException(ErrorCode.TENANT_EXPIRED)`
- 租户禁用：抛出 `BusinessException(ErrorCode.TENANT_DISABLED)`
- 租户不存在：抛出 `BusinessException(ErrorCode.TENANT_NOT_FOUND)`

### 2. loginByMobile 方法
- 任何租户校验失败都返回 `null`，并记录警告日志

### 3. getUserTenantAccounts 方法
- 过期或禁用的租户不会出现在返回的账号列表中
- 记录调试日志说明过滤原因

## 测试覆盖

新增了以下测试用例：

1. `testLoginByMobileWithTenant_TenantExpired_ThrowsException` - 测试租户过期时抛出异常
2. `testLoginByMobileWithTenant_TenantDisabled_ThrowsException` - 测试租户禁用时抛出异常
3. `testGetUserTenantAccounts_FilterExpiredTenants` - 测试租户账号列表过滤功能

所有测试用例均已通过，确保功能的正确性和稳定性。

## 兼容性

- 保持与现有多租户架构的完全兼容
- 不影响现有的登录流程和错误处理机制
- 对于没有设置过期时间的租户，功能保持不变

## 日志记录

- 租户校验过程中的关键步骤都有相应的日志记录
- 使用不同的日志级别：DEBUG（正常流程）、WARN（校验失败）、ERROR（异常情况）
- 日志信息包含租户ID、租户名称、过期时间等关键信息，便于问题排查
