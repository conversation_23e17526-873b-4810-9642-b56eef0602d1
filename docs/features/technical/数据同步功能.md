# 数据同步服务架构说明

## 概述

数据同步服务是一个多租户数据同步解决方案，支持第三方系统通过加密方式安全地同步数据到指定的租户表中。系统采用策略模式设计，支持多种数据表的同步，并提供完整的日志记录和状态查询功能。

## 系统架构

### 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   第三方系统     │    │   数据同步服务   │    │   目标数据库     │
│                │    │                │    │                │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │ 数据源    │  │───▶│  │ Controller│  │    │  │ 租户表    │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│                │    │        │        │    │                │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │ 加密模块  │  │    │  │ Service   │  │    │  │ 日志表    │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│                │    │        │        │    │                │
└─────────────────┘    │  ┌───────────┐  │    └─────────────────┘
                       │  │ Strategy  │  │
                       │  └───────────┘  │
                       │        │        │
                       │  ┌───────────┐  │
                       │  │Repository │  │
                       │  └───────────┘  │
                       └─────────────────┘
```

### 核心组件

#### 1. Controller 层 (DataSyncController)
- **职责**: 接收HTTP请求，参数验证，响应格式化
- **主要功能**:
  - 单条数据同步接口
  - 批量数据同步接口
  - 同步状态查询接口
  - 支持的表和操作类型查询
- **特点**: 
  - 完整的API文档注解
  - 统一的错误处理
  - 客户端IP获取

#### 2. Service 层 (DataSyncService)
- **职责**: 业务逻辑处理，事务管理
- **主要功能**:
  - 数据解密和租户识别
  - 同步策略选择和执行
  - 日志记录和状态管理
  - 批量处理和错误收集
- **特点**:
  - 事务性保证
  - 异步日志记录
  - 策略模式实现

#### 3. Strategy 层 (SyncStrategy)
- **职责**: 具体的数据同步逻辑
- **设计模式**: 策略模式
- **主要功能**:
  - 数据格式验证
  - INSERT/UPDATE自动判断
  - 业务主键匹配
- **扩展性**: 支持新表的同步策略

#### 4. Repository 层 (TableDataSyncLogService)
- **职责**: 数据访问和持久化
- **主要功能**:
  - 同步日志的CRUD操作
  - 批次查询和状态统计
  - 过期日志清理
- **特点**:
  - MyBatis Dynamic SQL
  - 审计字段自动填充

## 核心流程

### 单条数据同步流程

```mermaid
sequenceDiagram
    participant Client as 第三方系统
    participant Controller as DataSyncController
    participant Service as DataSyncService
    participant Encryption as EncryptionService
    participant Strategy as SyncStrategy
    participant Repository as Repository
    participant DB as 数据库

    Client->>Controller: POST /single
    Controller->>Service: syncSingleData()
    Service->>Encryption: decryptAndIdentifyTenant()
    Encryption-->>Service: 解密结果+租户信息
    Service->>Strategy: validateData()
    Strategy-->>Service: 验证结果
    Service->>Strategy: syncData()
    Strategy->>DB: INSERT/UPDATE
    DB-->>Strategy: 执行结果
    Strategy-->>Service: 同步结果
    Service->>Repository: insert(syncLog)
    Repository->>DB: 记录日志
    Service-->>Controller: 同步结果VO
    Controller-->>Client: HTTP响应
```

### 批量数据同步流程

```mermaid
sequenceDiagram
    participant Client as 第三方系统
    participant Service as DataSyncService
    participant Strategy as SyncStrategy
    participant DB as 数据库

    Client->>Service: 批量数据请求
    loop 遍历每条数据
        Service->>Service: 解密数据
        Service->>Strategy: 验证数据
        Service->>Strategy: 同步数据
        Strategy->>DB: INSERT/UPDATE
        Service->>Service: 记录结果
    end
    Service->>Service: 汇总统计
    Service-->>Client: 批量同步结果
```

## 数据模型

### 核心实体

#### SysDataSyncLog (同步日志表)
```sql
CREATE TABLE `sys_data_sync_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_no` varchar(64) NOT NULL COMMENT '同步批次号',
  `target_table` varchar(64) NOT NULL COMMENT '目标表名',
  `sync_status` varchar(16) NOT NULL COMMENT '同步状态',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '失败原因',
  `source_ip` varchar(64) DEFAULT NULL COMMENT '请求来源IP',
  `sync_start_time` datetime NOT NULL COMMENT '同步开始时间',
  `sync_end_time` datetime DEFAULT NULL COMMENT '同步结束时间',
  `sync_duration` bigint(20) DEFAULT NULL COMMENT '同步耗时（毫秒）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` varchar(64) NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_by` varchar(64) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_target_table` (`target_table`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_create_time` (`create_time`)
) COMMENT='数据同步日志表（系统级表，不按租户分表）';
```

#### 关键DTO/VO类

1. **DataSyncRequestDTO**: 同步请求数据传输对象
2. **DataSyncResultVO**: 同步结果视图对象
3. **TenantDecryptionResultDTO**: 租户解密结果对象
4. **SyncDataResultDTO**: 策略执行结果对象

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **应用场景**: 不同表的同步逻辑
- **优势**: 易于扩展新的同步策略
- **实现**: SyncStrategy接口及其实现类

### 2. 工厂模式 (Factory Pattern)
- **应用场景**: 策略实例的创建和管理
- **实现**: 通过Spring的依赖注入自动装配策略列表

### 3. 模板方法模式 (Template Method Pattern)
- **应用场景**: 同步流程的标准化
- **实现**: Service层的通用同步流程

## 安全设计

### 1. 数据加密
- **传输加密**: 所有敏感数据采用加密传输
- **租户隔离**: 基于加密数据的租户识别
- **密钥管理**: 租户专用加密密钥

### 2. 访问控制
- **IP白名单**: 支持来源IP记录和控制
- **接口限流**: 防止恶意请求
- **参数验证**: 严格的输入参数验证

### 3. 审计日志
- **操作记录**: 完整的同步操作日志
- **错误追踪**: 详细的错误信息记录
- **性能监控**: 同步耗时统计

## 性能优化

### 1. 批量处理
- **批量同步**: 支持批量数据处理
- **事务优化**: 合理的事务边界设计
- **内存管理**: 大批量数据的流式处理

### 2. 数据库优化
- **索引设计**: 关键字段的索引优化
- **查询优化**: Dynamic SQL的性能优化
- **连接池**: 数据库连接池配置

### 3. 缓存策略
- **策略缓存**: 同步策略的内存缓存
- **配置缓存**: 系统配置的缓存机制

## 监控和运维

### 1. 日志管理
- **分级日志**: 不同级别的日志记录
- **日志轮转**: 自动的日志文件管理
- **日志清理**: 过期日志的自动清理

### 2. 健康检查
- **接口监控**: API接口的可用性监控
- **性能监控**: 响应时间和吞吐量监控
- **错误监控**: 错误率和异常监控

### 3. 运维工具
- **状态查询**: 同步状态的查询接口
- **数据统计**: 同步数据的统计分析
- **故障排查**: 完整的错误信息和堆栈

## 扩展性设计

### 1. 新表支持
- **策略扩展**: 实现新的SyncStrategy
- **配置更新**: 更新支持的表列表
- **测试验证**: 完整的测试用例

### 2. 新功能扩展
- **接口扩展**: 新的API接口开发
- **业务扩展**: 新的业务逻辑实现
- **集成扩展**: 与其他系统的集成

### 3. 性能扩展
- **水平扩展**: 支持多实例部署
- **垂直扩展**: 支持更大的数据量处理
- **异步处理**: 支持异步的数据同步

## 版本演进

### 当前版本特性
- 表结构简化，移除冗余字段
- 重试功能暂时禁用
- 完整的API文档和测试用例

### 未来版本规划
- 重试功能的重新设计
- 更多数据表的支持
- 实时同步功能
- 数据同步的可视化监控
