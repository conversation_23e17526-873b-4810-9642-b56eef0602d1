# Redis 集成使用指南

## 概述

本项目已完成 Redis 集成配置，提供了完整的 Redis 操作工具类和配置。支持字符串、哈希、列表、集合、有序集合等数据结构操作，以及分布式锁和缓存模式等高级功能。

## 配置说明

### 1. Redis 连接配置

在 `application.yml` 中已配置 Redis 连接参数：

```yaml
spring:
  redis:
    database: 0
    host: evcard-st-lan.redis.rds.aliyuncs.com
    port: 6379
    password: Wp4uJK*Vc3v2
    timeout: 10s
    connect-timeout: 10s
    lettuce:
      pool:
        min-idle: 2
        max-idle: 10
        max-active: 20
        max-wait: 5s
        time-between-eviction-runs: 30s
        min-evictable-idle-time: 60s
      shutdown-timeout: 100ms
```

### 2. Redis 配置类

`RedisConfig.java` 提供了以下配置：
- `RedisTemplate<String, Object>` - 支持对象序列化
- `StringRedisTemplate` - 专门处理字符串操作
- Jackson2JsonRedisSerializer - JSON 序列化方式
- 连接池配置和事务支持

## 使用方法

### 1. 注入 RedisUtils

```java
@Service
public class YourService {
    
    @Autowired
    private RedisUtils redisUtils;
    
    // 使用示例...
}
```

### 2. 基础操作

```java
// 设置值
redisUtils.set("key", "value");
redisUtils.set("key", "value", 3600); // 设置过期时间(秒)

// 获取值
Object value = redisUtils.get("key");

// 检查键是否存在
boolean exists = redisUtils.hasKey("key");

// 设置过期时间
redisUtils.expire("key", 3600);

// 删除键
redisUtils.del("key1", "key2");
```

### 3. 字符串操作

```java
// 递增/递减
long result = redisUtils.incr("counter", 1);
long result = redisUtils.decr("counter", 1);

// 字符串追加
redisUtils.append("key", "additional_text");
```

### 4. 哈希操作

```java
// 设置哈希字段
redisUtils.hset("hash_key", "field", "value");

// 获取哈希字段
Object value = redisUtils.hget("hash_key", "field");

// 设置多个字段
Map<String, Object> map = new HashMap<>();
map.put("field1", "value1");
map.put("field2", "value2");
redisUtils.hmset("hash_key", map);

// 获取所有字段
Map<Object, Object> allFields = redisUtils.hmget("hash_key");

// 删除字段
redisUtils.hdel("hash_key", "field1", "field2");
```

### 5. 列表操作

```java
// 右侧推入
redisUtils.lRightPush("list_key", "value");

// 左侧推入
redisUtils.lLeftPush("list_key", "value");

// 获取范围元素
List<Object> range = redisUtils.lGet("list_key", 0, -1); // 获取所有

// 弹出元素
Object leftValue = redisUtils.lLeftPop("list_key");
Object rightValue = redisUtils.lRightPop("list_key");

// 获取列表长度
long size = redisUtils.lGetListSize("list_key");
```

### 6. 集合操作

```java
// 添加元素
redisUtils.sSet("set_key", "value1", "value2", "value3");

// 检查元素是否存在
boolean exists = redisUtils.sHasKey("set_key", "value1");

// 获取所有元素
Set<Object> members = redisUtils.sGet("set_key");

// 移除元素
redisUtils.setRemove("set_key", "value1");
```

### 7. 有序集合操作

```java
// 添加成员
redisUtils.zAdd("zset_key", "member1", 1.0);
redisUtils.zAdd("zset_key", "member2", 2.0);

// 获取分数
Double score = redisUtils.zScore("zset_key", "member1");

// 获取排名
Long rank = redisUtils.zRank("zset_key", "member1");

// 范围查询
Set<Object> range = redisUtils.zRange("zset_key", 0, 10);

// 移除成员
redisUtils.zRemove("zset_key", "member1");
```

### 8. 分布式锁

```java
String lockKey = "business_lock";
String requestId = UUID.randomUUID().toString();
int expireTime = 30; // 30秒

// 尝试获取锁
if (redisUtils.tryLock(lockKey, requestId, expireTime)) {
    try {
        // 执行业务逻辑
        doBusinessLogic();
    } finally {
        // 释放锁
        redisUtils.releaseLock(lockKey, requestId);
    }
} else {
    // 获取锁失败的处理
    throw new BusinessException("系统繁忙，请稍后重试");
}
```

### 9. 缓存模式（防止缓存穿透）

```java
// 缓存穿透保护的数据获取
User user = redisUtils.getOrSet(
    "user:" + userId,
    3600, // 缓存1小时
    () -> {
        // 数据加载器 - 从数据库获取数据
        return userService.getUserById(userId);
    }
);
```

### 10. 批量操作

```java
// 批量删除（支持通配符）
long deletedCount = redisUtils.deleteByPattern("user:*");

// 获取匹配的键
Set<String> keys = redisUtils.keys("session:*");
```

## 最佳实践

### 1. 键命名规范

```java
// 推荐的键命名格式
String userCacheKey = "user:" + userId;
String sessionKey = "session:" + sessionId;
String lockKey = "lock:order:" + orderId;
```

### 2. 过期时间设置

```java
// 根据业务场景设置合适的过期时间
redisUtils.set("user_token:" + userId, token, 7200);    // 用户令牌 2小时
redisUtils.set("sms_code:" + phone, code, 300);         // 短信验证码 5分钟
redisUtils.set("user_info:" + userId, userInfo, 3600);  // 用户信息 1小时
```

### 3. 异常处理

```java
try {
    Object result = redisUtils.get("key");
    if (result != null) {
        // 处理缓存命中
        return (String) result;
    } else {
        // 缓存未命中，从数据库获取
        return getFromDatabase();
    }
} catch (Exception e) {
    log.error("Redis操作异常", e);
    // 降级处理，直接从数据库获取
    return getFromDatabase();
}
```

### 4. 连接状态检查

```java
// 检查 Redis 连接状态
boolean isConnected = redisUtils.ping();
if (!isConnected) {
    log.warn("Redis连接异常，启用降级模式");
    // 执行降级逻辑
}
```

## 注意事项

1. **线程安全**: RedisUtils 是线程安全的，可以在多线程环境中使用
2. **异常处理**: 所有方法都包含异常处理，Redis 异常不会影响主业务流程
3. **连接池**: 已配置连接池，支持高并发访问
4. **序列化**: 使用 Jackson 进行对象序列化，支持复杂对象存储
5. **分布式锁**: 使用 Lua 脚本保证原子性操作
6. **缓存穿透**: getOrSet 方法提供缓存穿透保护

## 测试

项目包含完整的测试用例：
- `RedisConfigTest` - 配置测试
- `RedisUtilsTest` - 工具类功能测试

运行测试需要 Redis 服务器运行在 localhost:6379，或者修改测试配置。

## 监控和调试

可以通过以下方式获取 Redis 信息：

```java
// 获取 Redis 信息
String info = redisUtils.info();
System.out.println(info);

// 检查连接状态
boolean connected = redisUtils.ping();
```

## 性能优化建议

1. 合理设置过期时间，避免内存泄漏
2. 使用批量操作减少网络往返
3. 避免存储过大的对象
4. 定期清理无用的键
5. 监控 Redis 内存使用情况
