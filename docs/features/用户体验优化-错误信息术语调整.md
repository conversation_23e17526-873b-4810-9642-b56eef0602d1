# 用户体验优化 - 错误信息术语调整

## 概述

为了提供更友好的用户体验，将面向外部用户的错误信息中的"租户"术语统一替换为"商户"，使错误提示更加直观易懂。

## 调整原则

### 1. 面向用户的错误信息
- **调整前**：使用技术术语"租户"
- **调整后**：使用业务术语"商户"
- **适用范围**：所有最终用户可能看到的错误提示信息

### 2. 内部技术实现
- **保持不变**：数据库字段名、变量名、类名等技术层面的命名
- **保持不变**：面向开发人员的日志信息
- **保持不变**：系统管理员使用的配置相关错误信息

## 具体调整内容

### 1. ErrorCode 枚举调整

#### 用户登录相关错误码 (220005-220007)
```java
// 调整前 → 调整后
MULTIPLE_TENANT_ACCOUNTS(220005, "用户存在多个租户账号，请选择具体的租户进行登录")
→ MULTIPLE_TENANT_ACCOUNTS(220005, "用户存在多个商户账号，请选择具体的商户进行登录")

NO_TENANT_ACCOUNTS(220006, "用户未关联任何租户账号")
→ NO_TENANT_ACCOUNTS(220006, "用户未关联任何商户账号")

TENANT_ACCESS_DENIED(220007, "用户无权限访问指定的租户")
→ TENANT_ACCESS_DENIED(220007, "用户无权限访问指定商户")
```

#### 租户状态相关错误码 (240001, 240004, 240005)
```java
// 调整前 → 调整后
TENANT_NOT_FOUND(240001, "租户不存在")
→ TENANT_NOT_FOUND(240001, "商户不存在")

TENANT_EXPIRED(240004, "租户已过期，请联系管理员")
→ TENANT_EXPIRED(240004, "商户已过期，请联系管理员")

TENANT_DISABLED(240005, "租户状态异常，无法登录")
→ TENANT_DISABLED(240005, "商户状态异常，无法登录")
```

#### 保持不变的错误码
```java
// 系统管理相关，保持技术术语
TENANT_CODE_EXISTS(240002, "租户编码已存在")     // 不变
TENANT_NAME_EXISTS(240003, "租户名称已存在")     // 不变
```

### 2. 测试用例更新

更新了相关测试用例以匹配新的错误信息：

```java
// AuthServiceImplMultiTenantTest.java
assertEquals("商户已过期，请联系管理员", exception.getMessage());
assertEquals("商户状态异常，无法登录", exception.getMessage());
```

### 3. 文档更新

更新了相关技术文档中的错误信息示例，确保文档与代码实现保持一致。

## 技术实现细节

### 1. 影响范围
- ✅ **ErrorCode 枚举类**：调整面向用户的错误信息
- ✅ **测试用例**：更新期望的错误信息
- ✅ **技术文档**：更新错误信息示例
- ❌ **日志信息**：保持技术术语不变
- ❌ **数据库设计**：保持现有命名不变
- ❌ **API 接口**：保持现有命名不变

### 2. 兼容性保证
- **向后兼容**：错误码数值保持不变
- **API 兼容**：接口参数和返回结构保持不变
- **功能兼容**：业务逻辑和处理流程保持不变

### 3. 多租户架构
- **技术实现**：多租户架构的技术实现保持不变
- **数据隔离**：租户数据隔离机制保持不变
- **权限控制**：基于租户的权限控制逻辑保持不变

## 验证测试

### 1. 单元测试
创建了专门的测试类 `ErrorCodeUserFriendlyTest` 来验证：
- 面向用户的错误信息使用"商户"术语
- 技术相关的错误信息可以保留"租户"术语
- 错误码的一致性和正确性

### 2. 集成测试
运行现有的登录相关测试，确保：
- 租户过期场景的错误信息正确
- 租户禁用场景的错误信息正确
- 多租户登录流程正常工作

## 用户体验改进

### 1. 错误信息更直观
- **改进前**："租户已过期，请联系管理员"
- **改进后**："商户已过期，请联系管理员"
- **效果**：用户更容易理解错误原因

### 2. 术语统一性
- 前端界面使用"商户"术语
- 错误提示使用"商户"术语
- 用户手册使用"商户"术语
- **效果**：提供一致的用户体验

### 3. 降低学习成本
- 避免用户接触技术术语
- 使用业务领域的通用词汇
- **效果**：降低用户理解和使用成本

## 最佳实践

### 1. 错误信息设计原则
- **用户友好**：使用用户熟悉的业务术语
- **信息明确**：提供清晰的错误原因和解决建议
- **一致性**：在整个系统中保持术语统一

### 2. 技术实现原则
- **分离关注点**：区分面向用户和面向开发者的信息
- **保持兼容**：确保技术架构和 API 的稳定性
- **渐进改进**：逐步优化用户体验而不影响系统稳定性

### 3. 维护建议
- 定期审查面向用户的错误信息
- 收集用户反馈并持续优化
- 保持文档与代码实现的同步

## 总结

本次调整成功实现了面向用户错误信息的术语优化，在保持技术架构稳定性的同时，显著提升了用户体验。通过将"租户"术语替换为"商户"，使错误提示更加直观易懂，降低了用户的理解成本。

### 主要成果
- ✅ 6个错误码的用户友好性优化
- ✅ 完整的测试覆盖和验证
- ✅ 技术文档同步更新
- ✅ 保持系统架构稳定性
- ✅ 提升用户体验一致性

### 后续建议
- 考虑在前端界面中进行类似的术语统一
- 定期收集用户反馈，持续优化错误信息
- 建立错误信息的设计规范和审查机制
