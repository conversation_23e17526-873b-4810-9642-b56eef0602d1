# 并行处理租户上下文传递问题修复

## 问题描述

在 DataSyncServiceImpl 的 `processDataInParallel` 方法中，存在租户上下文（TenantContextHolder）在并行处理时的线程安全问题：

1. **ThreadLocal 传递问题**：在主线程中设置的 tenantId 上下文无法正确传递到并行执行的子线程中
2. **SQL 拦截器失效**：TenantSchemaInterceptor 无法获取到正确的租户信息，导致无法在 SQL 语句中拼接正确的数据库 schema 前缀
3. **数据隔离失效**：可能导致不同租户的数据混乱或访问错误的数据库 schema

## 根本原因分析

### ThreadLocal 在多线程环境下的特性

ThreadLocal 变量是线程本地存储，每个线程都有自己独立的变量副本：

```java
// 主线程设置租户上下文
TenantContextHolder.setTenant(tenantId); // 只在主线程有效

// 子线程中无法获取到主线程的 ThreadLocal 值
CompletableFuture.runAsync(() -> {
    Long tenantId = TenantContextHolder.getTenantId(); // 返回 null
    String schema = TenantContextHolder.getTenantSchema(); // 返回默认值
}, taskExecutor);
```

### 问题代码示例

**修复前的代码**：
```java
// 创建并行任务
List<CompletableFuture<Void>> futures = chunks.stream()
    .map(chunk -> CompletableFuture.runAsync(() -> {
        // 子线程中无法获取到租户上下文
        processChunk(chunk, strategy, tenantResult, batchNo, targetTable,
                   syncStartTime, sourceIp, totalSuccessCount, allFailureDetails);
    }, taskExecutor))
    .collect(Collectors.toList());
```

## 修复方案

### 1. 显式传递租户上下文

在每个并行任务中手动设置租户上下文：

```java
// 获取当前线程的租户上下文，用于传递到子线程
Long currentTenantId = tenantResult.getTenantId();

// 创建并行任务，确保每个任务都正确设置租户上下文
List<CompletableFuture<Void>> futures = chunks.stream()
    .map(chunk -> CompletableFuture.runAsync(() -> {
        try {
            // 在子线程中设置租户上下文
            TenantContextHolder.setTenant(currentTenantId);
            log.debug("子线程设置租户上下文 - 租户ID: {}, 线程: {}", 
                     currentTenantId, Thread.currentThread().getName());
            
            // 处理数据分片
            processChunk(chunk, strategy, tenantResult, batchNo, targetTable,
                       syncStartTime, sourceIp, totalSuccessCount, allFailureDetails);
        } finally {
            // 清理子线程的租户上下文
            TenantContextHolder.clear();
            log.debug("子线程清理租户上下文 - 线程: {}", Thread.currentThread().getName());
        }
    }, taskExecutor))
    .collect(Collectors.toList());
```

### 2. 移除同步策略中的重复上下文管理

由于现在在批量同步开始时统一设置租户上下文，移除各个同步策略类中的重复设置：

**修复前**：
```java
public SyncDataResultDTO syncData(...) {
    try {
        TenantContextHolder.setTenant(tenantId); // 重复设置
        // 处理逻辑
    } finally {
        TenantContextHolder.clear(); // 重复清理
    }
}
```

**修复后**：
```java
public SyncDataResultDTO syncData(...) {
    // 租户上下文已在批量同步开始时设置，这里不再重复设置
    // 处理逻辑
    // 移除 finally 块中的租户上下文清理
}
```

## 修复效果验证

### 1. SQL 语句正确生成

修复后，TenantSchemaInterceptor 能够正确获取租户上下文并生成带有 schema 前缀的 SQL：

```sql
-- 租户ID=1 (auto_care_dzjt)
SELECT * FROM auto_care_dzjt.mtc_vehicle_info WHERE id = ?

-- 租户ID=2 (auto_care_extracme)  
SELECT * FROM auto_care_extracme.mtc_vehicle_info WHERE id = ?
```

### 2. 并发测试验证

通过测试验证了以下场景：

1. **ThreadLocal 传递机制**：
   - 主线程设置的 ThreadLocal 不会自动传递到子线程
   - 手动传递后子线程能正确获取租户上下文

2. **多线程租户上下文隔离**：
   - 不同线程的租户上下文相互独立
   - 每个线程都能维护正确的租户信息

3. **TenantSchemaInterceptor 在并发环境下的正确性**：
   - 拦截器能正确识别不同线程的租户信息
   - 生成的 SQL 语句包含正确的 schema 前缀

### 3. 测试结果

```
[INFO] Tests run: 3, Failures: 0, Errors: 0, Skipped: 0
```

所有测试通过，验证了修复的正确性。

## 性能影响

### 1. 性能优化保持

- 保持了原有的并行处理性能优势
- 没有回退到单线程处理
- 租户上下文设置开销极小

### 2. 上下文管理优化

- 减少了重复的租户上下文设置和清理
- 在批量同步级别统一管理租户上下文
- 提高了代码的可维护性

## 代码变更总结

### 1. DataSyncServiceImpl.java

- **processDataInParallel()**: 添加显式的租户上下文传递
- **syncBatchDataWithTenant()**: 在批量同步开始时设置租户上下文，结束时清理

### 2. 同步策略类

- **VehicleInfoSyncStrategy.java**: 移除租户上下文管理
- **VehicleModelSyncStrategy.java**: 移除租户上下文管理  
- **OrgInfoSyncStrategy.java**: 移除租户上下文管理

### 3. 测试验证

- **ParallelTenantContextTest.java**: 验证 ThreadLocal 传递机制
- **TenantSchemaInterceptorTest.java**: 验证拦截器在多线程环境下的正确性

## 最佳实践总结

### 1. ThreadLocal 在多线程中的使用

- ThreadLocal 变量不会自动传递到子线程
- 需要显式传递和设置 ThreadLocal 值
- 必须在子线程结束时清理 ThreadLocal 以避免内存泄漏

### 2. 租户上下文管理

- 在合适的层级统一管理租户上下文
- 避免在多个地方重复设置和清理
- 确保并行处理时每个线程都有正确的上下文

### 3. 多租户架构中的并发处理

- 必须考虑 ThreadLocal 在并发环境下的传递问题
- 测试时要验证不同租户的数据隔离
- 确保 SQL 拦截器在并发环境下正常工作

## 后续建议

1. **监控告警**：添加租户上下文缺失的监控告警
2. **性能测试**：在生产环境中验证并行处理的性能表现
3. **代码审查**：确保其他使用 ThreadLocal 的地方也考虑了多线程传递问题
4. **文档更新**：更新多租户架构文档，说明并发处理的注意事项

这次修复确保了数据同步服务在并行处理时能够正确维护租户隔离，避免了潜在的数据安全问题。
