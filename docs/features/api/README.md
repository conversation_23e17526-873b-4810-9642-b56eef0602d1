# API接口文档

## 📋 文档概述

本目录包含Auto Care SaaS项目的完整API接口文档，提供详细的接口说明、参数定义、响应格式和使用示例。

## 🗂️ API文档结构

### 📡 核心API文档
- [数据同步API](./数据同步接口.md) - 多租户数据同步接口
- [工作流API](./工作流接口.md) - 工作流管理接口
- [用户管理API](./用户管理接口.md) - 用户和权限管理接口

## 🎯 API设计原则

### 1. RESTful设计
- 使用标准HTTP方法（GET、POST、PUT、DELETE）
- 资源导向的URL设计
- 统一的响应状态码
- 合理的HTTP头部使用

### 2. 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  }
}
```

### 3. 错误处理
- 统一的错误码定义
- 详细的错误信息描述
- 国际化的错误消息
- 调试信息的合理暴露

### 4. 版本管理
- URL路径中包含版本号：`/api/v1/`
- 向后兼容的版本升级策略
- 废弃API的优雅下线

## 🔧 API使用指南

### 认证方式
```bash
# JWT Token认证
Authorization: Bearer <your-jwt-token>

# 租户标识（多租户场景）
X-Tenant-Code: <tenant-code>
```

### 请求格式
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`
- **时间格式**: `yyyy-MM-dd HH:mm:ss`

### 分页参数
```json
{
  "pageNum": 1,
  "pageSize": 20
}
```

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 5
  }
}
```

## 📊 API分类

### 1. 系统管理API
- 用户管理
- 角色管理
- 权限管理
- 租户管理

### 2. 业务功能API
- 车辆信息管理
- 维修任务管理
- 工作流管理
- 报表统计

### 3. 数据同步API
- 车辆信息同步
- 组织架构同步
- 用户信息同步
- 业务数据同步

### 4. 文件管理API
- 文件上传
- 文件下载
- 文件删除
- 文件预览

## 🚀 快速开始

### 1. 获取访问令牌
```bash
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "123456"
  }'
```

### 2. 调用业务接口
```bash
curl -X GET "http://localhost:8080/api/v1/users" \
  -H "Authorization: Bearer <your-token>" \
  -H "X-Tenant-Code: <tenant-code>"
```

### 3. 处理响应数据
```javascript
// 成功响应
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [...],
    "total": 100
  }
}

// 错误响应
{
  "code": 400,
  "message": "参数验证失败",
  "data": null
}
```

## 📋 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 200 | 请求成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式和内容 |
| 401 | 未授权访问 | 检查认证令牌是否有效 |
| 403 | 权限不足 | 检查用户权限配置 |
| 404 | 资源不存在 | 检查请求URL和资源ID |
| 500 | 服务器内部错误 | 联系技术支持 |

## 🔍 调试工具

### 1. Swagger UI
- 访问地址：`http://localhost:8080/swagger-ui.html`
- 提供交互式API文档
- 支持在线测试接口

### 2. Postman集合
- 导入Postman集合文件
- 预配置的请求示例
- 环境变量配置

### 3. 日志查看
- 应用日志：`/logs/application.log`
- 访问日志：`/logs/access.log`
- 错误日志：`/logs/error.log`

## 📞 技术支持

API相关问题请联系：
- 技术支持：<EMAIL>
- 开发团队：<EMAIL>

---

> 📝 **文档版本**：v2.0  
> 🕒 **最后更新**：2025-01-19  
> 👥 **维护团队**：Auto Care API Team
