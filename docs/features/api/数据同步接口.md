# 数据同步服务 API 接口文档

## 概述

数据同步服务提供多租户数据同步功能，支持第三方系统通过加密方式安全地同步数据到指定的租户表中。系统自动判断INSERT或UPDATE操作，无需手动指定操作类型。

## 基础信息

- **Base URL**: `/api/v1/data-sync`
- **Content-Type**: `application/json`
- **认证方式**: 基于加密数据的租户识别
- **支持的表**: `mtc_vehicle_info`, `mtc_vehicle_model`, `mtc_org_info`

## API 端点

### 1. 同步单条数据

**接口地址**: `POST /api/v1/data-sync/single`

**功能描述**: 第三方系统通过此接口同步单条数据到指定的租户表中，系统自动判断INSERT或UPDATE操作。

**请求参数**:
```json
{
  "targetTable": "mtc_vehicle_info",
  "encryptedData": "encrypted_data_string"
}
```

**参数说明**:
- `targetTable` (string, 必填): 目标表名，支持的表见上方列表
- `encryptedData` (string, 必填): 加密的同步数据

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "batchNo": "SYNC_1640995200000_1234",
    "tenantCode": "tenant_001",
    "targetTable": "mtc_vehicle_info",
    "operationType": "AUTO",
    "syncStatus": "SUCCESS",
    "totalCount": 1,
    "successCount": 1,
    "failedCount": 0,
    "syncStartTime": "2024-01-01 10:00:00",
    "syncEndTime": "2024-01-01 10:00:01",
    "syncDuration": 1000,
    "failureDetails": []
  }
}
```

### 2. 批量同步数据

**接口地址**: `POST /api/v1/data-sync/batch`

**功能描述**: 第三方系统通过此接口批量同步数据到指定的租户表中，支持部分成功的处理模式。

**请求参数**:
```json
{
  "targetTable": "mtc_vehicle_info",
  "batchData": [
    {
      "sourceDataId": "data_001",
      "encryptedData": "encrypted_data_001"
    },
    {
      "sourceDataId": "data_002", 
      "encryptedData": "encrypted_data_002"
    }
  ]
}
```

**参数说明**:
- `targetTable` (string, 必填): 目标表名
- `batchData` (array, 必填): 批量数据列表
  - `sourceDataId` (string, 必填): 源数据标识
  - `encryptedData` (string, 必填): 加密的数据内容

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "batchNo": "SYNC_1640995200000_5678",
    "tenantCode": "tenant_001",
    "targetTable": "mtc_vehicle_info",
    "operationType": "AUTO",
    "syncStatus": "PARTIAL_SUCCESS",
    "totalCount": 2,
    "successCount": 1,
    "failedCount": 1,
    "syncStartTime": "2024-01-01 10:00:00",
    "syncEndTime": "2024-01-01 10:00:02",
    "syncDuration": 2000,
    "failureDetails": [
      {
        "sourceDataId": "data_002",
        "errorMessage": "数据格式验证失败",
        "failureTime": "2024-01-01 10:00:01"
      }
    ]
  }
}
```

### 3. 查询同步状态

**接口地址**: `GET /api/v1/data-sync/status/{batchNo}`

**功能描述**: 根据批次号查询数据同步的状态和结果详情。

**路径参数**:
- `batchNo` (string, 必填): 同步批次号，例如 `SYNC_1640995200000_1234`

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "batchNo": "SYNC_1640995200000_1234",
    "tenantCode": "tenant_001",
    "targetTable": "mtc_vehicle_info",
    "operationType": "AUTO",
    "syncStatus": "SUCCESS",
    "totalCount": 1,
    "successCount": 1,
    "failedCount": 0,
    "syncStartTime": "2024-01-01 10:00:00",
    "syncEndTime": "2024-01-01 10:00:01",
    "syncDuration": 1000,
    "failureDetails": []
  }
}
```



### 4. 获取支持的同步表列表

**接口地址**: `GET /api/v1/data-sync/supported-tables`

**功能描述**: 获取系统当前支持的所有可同步表名列表。

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    "mtc_vehicle_info",
    "mtc_vehicle_model",
    "mtc_org_info"
  ]
}
```

### 5. 获取支持的操作类型

**接口地址**: `GET /api/v1/data-sync/supported-operations`

**功能描述**: 获取系统支持的所有操作类型。

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    "AUTO"
  ]
}
```

## 状态码说明

### 同步状态 (syncStatus)
- `SUCCESS`: 所有数据同步成功
- `FAILED`: 所有数据同步失败
- `PARTIAL_SUCCESS`: 部分数据同步成功
- `PROCESSING`: 同步处理中（暂未使用）

### HTTP 响应码
- `200`: 请求成功
- `400`: 请求参数错误
- `500`: 服务器内部错误

## 错误处理

### 常见错误类型
1. **解密失败**: 加密数据格式错误或密钥不匹配
2. **数据格式验证失败**: 数据结构不符合目标表要求
3. **不支持的目标表**: 指定的表名不在支持列表中
4. **租户识别失败**: 无法从加密数据中识别租户信息

### 错误响应格式
```json
{
  "code": 500,
  "message": "错误描述",
  "data": null
}
```

## 使用示例

### 单条数据同步示例
```bash
curl -X POST "http://localhost:8080/api/v1/data-sync/single" \
  -H "Content-Type: application/json" \
  -d '{
    "targetTable": "mtc_vehicle_info",
    "encryptedData": "your_encrypted_data_here"
  }'
```

### 批量数据同步示例
```bash
curl -X POST "http://localhost:8080/api/v1/data-sync/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "targetTable": "mtc_vehicle_info",
    "batchData": [
      {
        "sourceDataId": "vehicle_001",
        "encryptedData": "encrypted_vehicle_data_001"
      },
      {
        "sourceDataId": "vehicle_002",
        "encryptedData": "encrypted_vehicle_data_002"
      }
    ]
  }'
```

### 查询同步状态示例
```bash
curl -X GET "http://localhost:8080/api/v1/data-sync/status/SYNC_1640995200000_1234"
```

## 最佳实践

1. **数据完整性**: 确保传入的数据包含必要的ID字段
2. **批量大小**: 建议单次批量同步不超过200条记录以获得最佳性能
3. **错误处理**: 对于批量同步，建议检查失败详情并重新提交失败的数据
4. **状态查询**: 对于大批量数据，建议定期查询同步状态
5. **重试机制**: 对于网络异常等临时性错误，建议实现客户端重试机制

## 注意事项

1. 所有时间字段均使用 GMT+8 时区
2. 批次号格式为 `SYNC_{timestamp}_{random}`
3. 同步密钥中必须包含租户识别信息
4. 系统会自动记录所有同步操作的日志
5. 车辆和车型数据必须提供有效的ID字段
