# 用户多机构管理

## 概述

本文档描述了用户多机构关联功能的实现，支持一个用户关联多个机构，通过 sys_user_org 关联表实现多对多关系，同时保持 API 向后兼容性。

## 功能特性

### 1. 多机构关联
- 支持一个用户关联多个机构
- 通过 sys_user_org 关联表实现多对多关系
- 保持与原有单机构模式的向后兼容

### 2. API 兼容性
- 保留原有的 `orgId` 字段，确保现有 API 调用不受影响
- 支持单个 `orgId` 和多个 `orgIds` 的混合使用
- 主要机构 ID 取 `orgIds` 列表的第一个

### 3. 性能优化
- 批量查询用户机构关联，避免 N+1 查询
- 使用 Map 缓存机构信息，减少数据库访问
- 登录时一次性加载用户机构列表

## 技术实现

### 1. 数据层实现

#### 1.1 新增 Repository 服务
- **TableUserOrgService**: 用户机构关联表数据访问服务接口
- **TableUserOrgServiceImpl**: 实现类，提供 CRUD 和批量操作
- **SysUserOrgExtendMapper**: 扩展 Mapper 接口

#### 1.2 查询逻辑增强
- **TableUserService.findByConditionWithOrgs()**: 新增支持多机构查询的方法
- 支持机构 ID 列表的 IN 查询
- 支持角色和机构的交集查询

### 2. 业务层实现

#### 2.1 LoginUser 缓存优化
```java
/**
 * 用户关联的所有机构ID列表
 */
private List<String> orgIds;

/**
 * 获取主要机构ID（向后兼容）
 */
public String getPrimaryOrgId() {
    return orgIds != null && !orgIds.isEmpty() ? orgIds.get(0) : null;
}
```

#### 2.2 SessionUtils 增强
- 登录时自动加载用户机构列表
- 新增便利方法：`getUserOrgIds()`, `getPrimaryOrgId()`, `hasOrgAccess()`

#### 2.3 UserService 功能扩展
- **updateUserOrgs()**: 更新用户机构关联
- **getUsersByOrgIds()**: 根据机构 ID 查询用户
- 创建/更新用户时处理机构关联

### 3. DTO/VO 对象调整

#### 3.1 支持多机构字段
```java
// 新增字段
private List<String> orgIds;        // 机构ID列表
private List<String> orgNames;      // 机构名称列表（UserVO）

// 保留向后兼容字段
private String orgId;               // 主要机构ID
private String orgName;             // 主要机构名称（UserVO）
```

#### 3.2 涉及的 DTO/VO
- UserCreateDTO
- UserUpdateDTO
- UserQueryDTO
- UserVO

### 4. 控制器层实现

#### 4.1 新增接口
```java
@PostMapping("/updateOrgs/{userId}")
public Result<Void> updateUserOrgs(@PathVariable Long userId, @RequestBody List<String> orgIds)
```

#### 4.2 现有接口增强
- 用户查询支持机构 ID 列表过滤
- 用户详情返回机构信息

## 使用示例

### 1. 创建用户（多机构）
```java
UserCreateDTO createDTO = new UserCreateDTO();
createDTO.setMobile("13800138000");
createDTO.setNickname("张三");
createDTO.setOrgIds(Arrays.asList("ORG001", "ORG002", "ORG003"));
Long userId = userService.createUser(createDTO);
```

### 2. 查询指定机构的用户
```java
UserQueryDTO queryDTO = new UserQueryDTO();
queryDTO.setOrgIds(Arrays.asList("ORG001", "ORG002"));
BasePageVO<UserVO> result = userService.getUserList(queryDTO);
```

### 3. 更新用户机构关联
```java
List<String> newOrgIds = Arrays.asList("ORG003", "ORG004");
boolean success = userService.updateUserOrgs(userId, newOrgIds);
```

### 4. 检查用户机构权限
```java
// 在SessionUtils中
List<String> userOrgIds = SessionUtils.getUserOrgIds();
boolean hasAccess = SessionUtils.hasOrgAccess("ORG001");
```

## 数据库设计

### sys_user_org 关联表
```sql
CREATE TABLE IF NOT EXISTS `sys_user_org` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `org_id` VARCHAR(50) NOT NULL COMMENT '机构ID',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `created_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_org` (`user_id`, `org_id`),
    KEY `idx_org_id` (`org_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户机构关联表';
```

## 最佳实践

### 1. 数据一致性
- 创建/更新用户时，机构关联操作在同一事务中
- 删除用户时需要同时清理机构关联
- 机构删除时需要考虑用户关联的处理

### 2. 性能考虑
- 大量用户的机构查询可能影响性能
- 建议对 sys_user_org 表的 user_id 和 org_id 字段建立索引
- 考虑缓存热点机构的用户列表

### 3. 业务规则
- 用户至少需要关联一个机构
- 机构 ID 的有效性验证
- 跨租户机构关联的权限控制

## 注意事项

### 1. 向后兼容性
- 保留原有的 `orgId` 字段，确保现有 API 调用不受影响
- 支持单个 `orgId` 和多个 `orgIds` 的混合使用
- 主要机构 ID 取 `orgIds` 列表的第一个

### 2. 多租户支持
- 保持现有的多租户隔离机制
- 超级管理员可访问所有租户数据
- 普通用户只能访问自己租户的数据

### 3. 权限控制
- 用户只能访问自己关联的机构数据
- 支持基于机构的数据权限过滤
- 维护机构层级关系的权限继承

## 后续优化建议

### 1. 功能增强
- 支持机构层级关系
- 用户在不同机构的角色权限
- 机构切换功能

### 2. 性能优化
- 机构用户关系的缓存策略
- 分页查询的性能优化
- 批量操作的事务优化

### 3. 监控告警
- 机构关联操作的审计日志
- 异常机构关联的监控
- 性能指标的监控

## 总结

用户多机构管理功能成功实现了用户与机构的多对多关联，在保持 API 兼容性的同时，提供了强大的多机构支持功能。通过合理的数据结构设计和性能优化，确保了系统的稳定性和可扩展性。

主要成果：
- ✅ 支持用户多机构关联
- ✅ 保持 API 向后兼容
- ✅ 优化查询性能
- ✅ 完善测试覆盖
- ✅ 维护多租户隔离
- ✅ 提供便利的工具方法
