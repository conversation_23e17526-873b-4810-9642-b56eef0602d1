# Auto Care SaaS 项目文档

> **项目概述**：Auto Care SaaS 是一个基于多租户架构的车辆维修管理系统，提供从车辆交接到验收的完整维修流程管理。

## 📚 文档导航

### 🏗️ 项目架构文档
- [架构文档导航](./architecture/README.md)
- [系统架构设计](./architecture/系统架构设计.md) - 系统整体架构设计
- [技术栈说明](./architecture/技术栈说明.md) - 项目使用的技术栈详细说明
- [模块划分设计](./architecture/模块划分设计.md) - 系统模块划分和职责说明
- [多租户架构设计](./architecture/多租户架构设计.md) - 多租户架构实现方案
- [部署架构说明](./architecture/部署架构说明.md) - 系统部署架构和环境配置

### 💻 开发规范文档
- [开发规范导航](./development/README.md)
- [代码开发规范](./development/代码开发规范.md) - Java代码编写规范和最佳实践
- [API设计规范](./development/API设计规范.md) - RESTful API设计规范
- [数据库设计规范](./development/数据库设计规范.md) - 数据库表设计和SQL编写规范
- [测试规范](./development/测试规范.md) - 单元测试和集成测试规范
- [Git工作流规范](./development/Git工作流规范.md) - Git分支管理和提交规范

### 🚀 功能文档
- [功能文档导航](./features/README.md)

#### 业务功能
- [车辆维修业务](./features/business/车辆维修业务.md) - 车辆维修业务流程和功能说明
- [工作流系统](./features/business/工作流系统.md) - 工作流引擎设计和使用说明
- [权限系统](./features/business/权限系统设计.md) - 基于RBAC的权限管理系统
- [多租户管理](./features/business/多租户管理.md) - 多租户数据隔离和管理

#### 技术功能
- [数据同步](./features/technical/数据同步功能.md) - 多租户数据同步功能
- [文件上传](./features/technical/文件上传功能.md) - 断点续传文件上传功能
- [Excel导入导出](./features/technical/Excel导入导出.md) - Excel数据处理功能
- [Redis集成](./features/technical/Redis集成使用.md) - Redis缓存和分布式锁

#### API文档
- [API文档导航](./features/api/README.md)
- [数据同步API](./features/api/数据同步接口.md) - 数据同步接口文档
- [工作流API](./features/api/工作流接口.md) - 工作流管理接口文档
- [用户管理API](./features/api/用户管理接口.md) - 用户和权限管理接口文档

### 🗄️ 数据库文档
- [数据库文档导航](./database/README.md)
- [数据库字典](./database/数据库字典.md) - 完整的数据库表和字段说明
- [表结构详细说明](./database/table-structures/) - 按模块分类的表结构文档
- [SQL脚本](./database/sql/) - 建表脚本、初始化数据和迁移脚本
- [索引和约束说明](./database/索引和约束说明.md) - 数据库索引和约束设计

## 🔧 快速开始

### 环境要求
- JDK 8+
- MySQL 8.0+
- Redis 6.2+
- Maven 3.8+

### 本地开发环境搭建
1. 克隆项目代码
2. 配置数据库连接
3. 执行数据库初始化脚本
4. 启动Redis服务
5. 运行项目

详细的环境搭建步骤请参考：[部署架构说明](./architecture/deployment-architecture.md)

## 📖 文档维护

### 文档更新原则
- 代码变更时同步更新相关文档
- 新功能开发完成后及时补充文档
- 定期检查和更新过时的文档内容
- 保持文档结构清晰，便于查找

### 文档贡献指南
1. 遵循Markdown格式规范
2. 使用清晰的标题层级
3. 提供必要的代码示例
4. 包含适当的图表说明
5. 确保链接有效性

## 📞 联系方式

如有文档相关问题或建议，请联系：
- 项目负责人：Auto Care Team
- 邮箱：<EMAIL>

---

> 📝 **文档版本**：v2.0  
> 🕒 **最后更新**：2025-01-19  
> 👥 **维护团队**：Auto Care Development Team
