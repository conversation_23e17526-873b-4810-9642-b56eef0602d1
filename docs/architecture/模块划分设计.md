# 模块划分设计

## 📋 概述

本文档详细说明Auto Care SaaS项目的模块划分设计，包括各模块的职责边界、依赖关系和接口定义。

## 🏗️ 整体架构

### 分层架构设计
```
┌─────────────────────────────────────────────────────────┐
│                    前端展示层                              │
│              Vue.js + Element UI                        │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    控制器层                               │
│              Spring MVC Controllers                     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    业务服务层                              │
│    用户管理  │  工作流引擎  │  车辆管理  │  数据同步        │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    数据访问层                              │
│              MyBatis + Repository                       │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    数据存储层                              │
│        MySQL  │  Redis  │  MinIO  │  Elasticsearch      │
└─────────────────────────────────────────────────────────┘
```

## 📦 核心模块

### 1. 系统管理模块 (system)

**职责范围**：
- 租户管理
- 用户管理
- 角色权限管理
- 系统配置管理

**主要组件**：
```
system/
├── controller/
│   ├── TenantController          # 租户管理控制器
│   ├── UserController            # 用户管理控制器
│   ├── RoleController            # 角色管理控制器
│   └── PermissionController      # 权限管理控制器
├── service/
│   ├── TenantService            # 租户业务服务
│   ├── UserService              # 用户业务服务
│   ├── RoleService              # 角色业务服务
│   └── PermissionService        # 权限业务服务
└── repository/
    ├── TableTenantService       # 租户数据访问
    ├── TableUserService         # 用户数据访问
    ├── TableRoleService         # 角色数据访问
    └── TablePermissionService   # 权限数据访问
```

**对外接口**：
- 租户CRUD操作
- 用户认证和授权
- 权限验证服务
- 多租户上下文管理

### 2. 工作流引擎模块 (workflow)

**职责范围**：
- 工作流模板管理
- 工作流实例执行
- 活动节点管理
- 状态转换控制

**主要组件**：
```
workflow/
├── controller/
│   ├── WorkflowTemplateController    # 工作流模板控制器
│   ├── WorkflowInstanceController    # 工作流实例控制器
│   └── ActivityController            # 活动节点控制器
├── service/
│   ├── WorkflowTemplateService      # 模板管理服务
│   ├── WorkflowInstanceService      # 实例执行服务
│   ├── ActivityService              # 活动管理服务
│   └── WorkflowEngineService        # 工作流引擎服务
├── handler/
│   ├── VehicleHandoverHandler       # 车辆交接处理器
│   ├── PreInspectionHandler         # 进保预审处理器
│   └── RepairQuotationHandler       # 维修报价处理器
└── repository/
    ├── TableWorkflowTemplateService # 模板数据访问
    ├── TableWorkflowInstanceService # 实例数据访问
    └── TableActivityService         # 活动数据访问
```

**对外接口**：
- 工作流模板CRUD
- 工作流实例启动和控制
- 活动节点状态查询
- 工作流事件通知

### 3. 车辆管理模块 (vehicle)

**职责范围**：
- 车辆信息管理
- 维修任务管理
- 维修厂管理
- 组织架构管理

**主要组件**：
```
vehicle/
├── controller/
│   ├── VehicleInfoController        # 车辆信息控制器
│   ├── RepairTaskController         # 维修任务控制器
│   ├── RepairDepotController        # 维修厂控制器
│   └── OrgInfoController            # 组织架构控制器
├── service/
│   ├── VehicleInfoService          # 车辆信息服务
│   ├── RepairTaskService           # 维修任务服务
│   ├── RepairDepotService          # 维修厂服务
│   └── OrgInfoService              # 组织架构服务
└── repository/
    ├── TableVehicleInfoService     # 车辆信息数据访问
    ├── TableRepairTaskService      # 维修任务数据访问
    ├── TableRepairDepotService     # 维修厂数据访问
    └── TableOrgInfoService         # 组织架构数据访问
```

**对外接口**：
- 车辆信息CRUD
- 维修任务管理
- 维修厂查询服务
- 组织架构查询服务

### 4. 数据同步模块 (datasync)

**职责范围**：
- 多租户数据同步
- 数据转换和映射
- 同步状态监控
- 冲突处理机制

**主要组件**：
```
datasync/
├── controller/
│   └── DataSyncController           # 数据同步控制器
├── service/
│   ├── DataSyncService             # 数据同步服务
│   ├── TenantIdentificationService # 租户识别服务
│   └── SyncLogService              # 同步日志服务
├── strategy/
│   ├── VehicleInfoSyncStrategy     # 车辆信息同步策略
│   ├── VehicleModelSyncStrategy    # 车型信息同步策略
│   └── OrgInfoSyncStrategy         # 组织信息同步策略
└── repository/
    └── TableDataSyncLogService     # 同步日志数据访问
```

**对外接口**：
- 批量数据同步API
- 同步状态查询API
- 租户身份验证API
- 同步日志查询API

### 5. 文件管理模块 (file)

**职责范围**：
- 文件上传下载
- 断点续传支持
- 文件存储管理
- 访问权限控制

**主要组件**：
```
file/
├── controller/
│   └── FileController              # 文件管理控制器
├── service/
│   ├── FileUploadService          # 文件上传服务
│   ├── FileDownloadService        # 文件下载服务
│   └── FileStorageService         # 文件存储服务
└── config/
    └── FileStorageConfig          # 文件存储配置
```

**对外接口**：
- 文件上传API
- 文件下载API
- 文件删除API
- 文件预览API

## 🔗 模块依赖关系

### 依赖层次
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   工作流引擎     │    │   车辆管理       │    │   数据同步       │
│   (workflow)    │    │   (vehicle)     │    │   (datasync)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   系统管理       │
                    │   (system)      │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   公共组件       │
                    │   (common)      │
                    └─────────────────┘
```

### 依赖规则
1. **上层依赖下层**：业务模块依赖系统管理模块
2. **同层互不依赖**：同层模块之间通过接口通信
3. **公共组件共享**：所有模块都可以使用公共组件

## 🎯 模块设计原则

### 1. 单一职责原则
- 每个模块只负责一个业务领域
- 模块内部高内聚，模块之间低耦合
- 清晰的模块边界和职责定义

### 2. 开闭原则
- 对扩展开放，对修改关闭
- 通过接口和抽象类定义模块契约
- 支持插件式扩展

### 3. 依赖倒置原则
- 高层模块不依赖低层模块
- 都依赖于抽象接口
- 抽象不依赖于具体实现

### 4. 接口隔离原则
- 客户端不应该依赖它不需要的接口
- 使用多个专门的接口，而不是单一的总接口
- 接口应该小而专一

## 📊 模块交互

### 典型业务流程
```
用户请求 → 控制器层 → 业务服务层 → 数据访问层 → 数据库
    ↓
权限验证 ← 系统管理模块
    ↓
工作流执行 ← 工作流引擎模块
    ↓
数据同步 ← 数据同步模块
```

### 模块间通信
- **同步调用**：直接方法调用
- **异步消息**：通过消息队列
- **事件驱动**：发布订阅模式
- **API调用**：RESTful接口

## 📞 联系方式

模块设计相关问题请联系：
- 架构师：<EMAIL>
- 技术负责人：<EMAIL>

---

> 📝 **文档版本**：v2.0  
> 🕒 **最后更新**：2025-01-19  
> 👥 **维护团队**：Auto Care Architecture Team
