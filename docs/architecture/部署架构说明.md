# 部署架构说明

## 📋 概述

本文档详细说明Auto Care SaaS项目的部署架构设计，包括环境配置、容器化部署、负载均衡、监控运维等内容。

## 🏗️ 部署架构图

### 整体部署架构
```
                    ┌─────────────────┐
                    │   负载均衡器     │
                    │   (Nginx/ALB)   │
                    └─────────────────┘
                             │
                    ┌─────────────────┐
                    │   API网关       │
                    │ (Spring Gateway)│
                    └─────────────────┘
                             │
        ┌────────────────────┼────────────────────┐
        │                    │                    │
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│   应用服务集群   │ │   应用服务集群   │ │   应用服务集群   │
│   (Pod 1-3)     │ │   (Pod 4-6)     │ │   (Pod 7-9)     │
└─────────────────┘ └─────────────────┘ └─────────────────┘
        │                    │                    │
        └────────────────────┼────────────────────┘
                             │
        ┌────────────────────┼────────────────────┐
        │                    │                    │
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│   MySQL主库     │ │   MySQL从库     │ │   Redis集群     │
│   (Master)      │ │   (Slave)       │ │   (Cluster)     │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

## 🌍 环境规划

### 1. 环境分类

| 环境 | 用途 | 配置规模 | 数据保留 |
|------|------|----------|----------|
| 开发环境 (dev) | 日常开发测试 | 单节点 | 不保留 |
| 测试环境 (test) | 功能测试验证 | 小规模集群 | 定期清理 |
| 预生产环境 (staging) | 上线前验证 | 生产同等配置 | 定期同步生产数据 |
| 生产环境 (prod) | 正式服务 | 高可用集群 | 永久保留 |

### 2. 环境配置

#### 开发环境配置
```yaml
# application-dev.yml
server:
  port: 8080

spring:
  datasource:
    url: ******************************************
    username: dev_user
    password: dev_password
  
  redis:
    host: localhost
    port: 6379
    database: 0

logging:
  level:
    com.extracme.saas.autocare: DEBUG
```

#### 生产环境配置
```yaml
# application-prod.yml
server:
  port: 8080

spring:
  datasource:
    url: *********************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
  
  redis:
    cluster:
      nodes:
        - redis-node1:6379
        - redis-node2:6379
        - redis-node3:6379

logging:
  level:
    com.extracme.saas.autocare: INFO
```

## 🐳 容器化部署

### 1. Docker镜像构建

#### Dockerfile
```dockerfile
FROM openjdk:8-jre-alpine

# 设置工作目录
WORKDIR /app

# 复制应用JAR文件
COPY target/auto-care-saas.jar app.jar

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC"

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### 镜像构建脚本
```bash
#!/bin/bash
# build-image.sh

# 构建应用
mvn clean package -DskipTests

# 构建Docker镜像
docker build -t autocare:${VERSION} .

# 推送到镜像仓库
docker tag autocare:${VERSION} registry.example.com/autocare:${VERSION}
docker push registry.example.com/autocare:${VERSION}
```

### 2. Kubernetes部署

#### 应用部署配置
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: autocare-app
  namespace: autocare
spec:
  replicas: 3
  selector:
    matchLabels:
      app: autocare
  template:
    metadata:
      labels:
        app: autocare
    spec:
      containers:
      - name: autocare
        image: registry.example.com/autocare:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
```

#### 服务配置
```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: autocare-service
  namespace: autocare
spec:
  selector:
    app: autocare
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
```

#### Ingress配置
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: autocare-ingress
  namespace: autocare
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.autocare.com
    secretName: autocare-tls
  rules:
  - host: api.autocare.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: autocare-service
            port:
              number: 80
```

## 🗄️ 数据库部署

### 1. MySQL集群部署

#### 主从复制配置
```yaml
# mysql-master.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mysql-master
spec:
  serviceName: mysql-master
  replicas: 1
  selector:
    matchLabels:
      app: mysql-master
  template:
    metadata:
      labels:
        app: mysql-master
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: root-password
        - name: MYSQL_REPLICATION_USER
          value: "replicator"
        - name: MYSQL_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: replication-password
        ports:
        - containerPort: 3306
        volumeMounts:
        - name: mysql-data
          mountPath: /var/lib/mysql
        - name: mysql-config
          mountPath: /etc/mysql/conf.d
  volumeClaimTemplates:
  - metadata:
      name: mysql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 100Gi
```

#### MySQL配置文件
```ini
# my.cnf
[mysqld]
server-id = 1
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON

# 性能优化
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
max_connections = 1000
query_cache_size = 128M
```

### 2. Redis集群部署

#### Redis集群配置
```yaml
# redis-cluster.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
spec:
  serviceName: redis-cluster
  replicas: 6
  selector:
    matchLabels:
      app: redis-cluster
  template:
    metadata:
      labels:
        app: redis-cluster
    spec:
      containers:
      - name: redis
        image: redis:6.2-alpine
        command:
        - redis-server
        - /etc/redis/redis.conf
        - --cluster-enabled
        - "yes"
        - --cluster-config-file
        - nodes.conf
        - --cluster-node-timeout
        - "5000"
        ports:
        - containerPort: 6379
        - containerPort: 16379
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
```

## 📊 监控运维

### 1. 应用监控

#### Prometheus配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
- job_name: 'autocare-app'
  kubernetes_sd_configs:
  - role: pod
  relabel_configs:
  - source_labels: [__meta_kubernetes_pod_label_app]
    action: keep
    regex: autocare
  - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
    action: keep
    regex: true
  - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
    action: replace
    target_label: __metrics_path__
    regex: (.+)
```

#### Grafana仪表盘
```json
{
  "dashboard": {
    "title": "Auto Care SaaS监控",
    "panels": [
      {
        "title": "应用QPS",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{uri}}"
          }
        ]
      },
      {
        "title": "响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

### 2. 日志管理

#### ELK Stack配置
```yaml
# elasticsearch.yaml
apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: autocare-es
spec:
  version: 7.15.0
  nodeSets:
  - name: default
    count: 3
    config:
      node.store.allow_mmap: false
    volumeClaimTemplates:
    - metadata:
        name: elasticsearch-data
      spec:
        accessModes:
        - ReadWriteOnce
        resources:
          requests:
            storage: 50Gi
```

#### Logstash配置
```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][app] == "autocare" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{DATA:thread} %{DATA:logger} - %{GREEDYDATA:msg}" }
    }
    
    date {
      match => [ "timestamp", "yyyy-MM-dd HH:mm:ss.SSS" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "autocare-logs-%{+YYYY.MM.dd}"
  }
}
```

## 🚀 CI/CD流水线

### 1. Jenkins Pipeline
```groovy
pipeline {
    agent any
    
    environment {
        DOCKER_REGISTRY = 'registry.example.com'
        K8S_NAMESPACE = 'autocare'
    }
    
    stages {
        stage('代码检出') {
            steps {
                git branch: 'main', url: 'https://gitlab.example.com/autocare/auto-care-saas.git'
            }
        }
        
        stage('单元测试') {
            steps {
                sh 'mvn test'
            }
            post {
                always {
                    junit 'target/surefire-reports/*.xml'
                }
            }
        }
        
        stage('代码质量检查') {
            steps {
                sh 'mvn sonar:sonar'
            }
        }
        
        stage('构建应用') {
            steps {
                sh 'mvn clean package -DskipTests'
            }
        }
        
        stage('构建镜像') {
            steps {
                script {
                    def image = docker.build("${DOCKER_REGISTRY}/autocare:${BUILD_NUMBER}")
                    image.push()
                    image.push("latest")
                }
            }
        }
        
        stage('部署到测试环境') {
            steps {
                sh """
                    kubectl set image deployment/autocare-app autocare=${DOCKER_REGISTRY}/autocare:${BUILD_NUMBER} -n ${K8S_NAMESPACE}-test
                    kubectl rollout status deployment/autocare-app -n ${K8S_NAMESPACE}-test
                """
            }
        }
        
        stage('集成测试') {
            steps {
                sh 'mvn verify -Dtest.environment=test'
            }
        }
        
        stage('部署到生产环境') {
            when {
                branch 'main'
            }
            steps {
                input message: '确认部署到生产环境?', ok: '部署'
                sh """
                    kubectl set image deployment/autocare-app autocare=${DOCKER_REGISTRY}/autocare:${BUILD_NUMBER} -n ${K8S_NAMESPACE}
                    kubectl rollout status deployment/autocare-app -n ${K8S_NAMESPACE}
                """
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        failure {
            emailext (
                subject: "构建失败: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "构建失败，请检查Jenkins控制台输出。",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}
```

## 📞 联系方式

部署架构相关问题请联系：
- 运维团队：<EMAIL>
- 架构师：<EMAIL>

---

> 📝 **文档版本**：v2.0  
> 🕒 **最后更新**：2025-01-19  
> 👥 **维护团队**：Auto Care DevOps Team
