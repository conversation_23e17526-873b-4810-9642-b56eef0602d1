# Auto Care SaaS 系统架构设计

## 一、系统概述

### 1.1 系统定位
- 全流程车辆维修管理系统
- 支持多租户部署的 SaaS 服务
- 覆盖从车辆交接到验收的完整维修流程
- 通过多个环节和外部系统交互，实现车辆维修全过程的精细化管理

### 1.2 系统架构
- **内部模块**：车辆交接、进保预审、维修报价、核损核价、车辆维修、车辆验收
- **外部系统集成**：
  - 调度系统：车辆调度和任务分配
  - 车管系统：车辆信息管理
  - 精友系统：配件报价和核价
  - 长租系统：长租车辆管理
  - 梧桐系统：维修任务管理

## 二、多租户架构设计

### 2.1 数据隔离
- **数据库设计**：
  - 采用独立 Schema 模式，每个租户拥有独立的数据库 Schema
  - 所有业务表都需要包含租户 ID 字段
  - 系统表采用共享 Schema 模式
- **数据访问控制**：
  - 所有数据访问必须经过租户 ID 过滤
  - 使用 MyBatis 拦截器自动注入租户 ID 条件
  - 提供租户数据访问审计日志

### 2.2 业务配置隔离
- **流程配置**：
  - 每个租户可以配置自己的业务流程
  - 支持租户级别的流程模板管理
  - 租户可以基于模板创建自定义流程
- **业务规则配置**：
  - 支持租户级别的业务规则配置
  - 包括审核规则、金额阈值、超时规则等
  - 规则变更需要记录审计日志

### 2.3 外部系统集成
- **系统对接配置**：
  - 每个租户可以配置自己的外部系统对接信息
  - 支持不同租户对接不同的外部系统
  - 外部系统接口调用需要租户认证
- **数据同步规则**：
  - 租户可以配置自己的数据同步规则
  - 支持自定义数据转换和映射规则
  - 同步失败需要支持重试机制

### 2.4 资源隔离
- **存储资源**：
  - 每个租户的文件存储空间独立
  - 支持租户级别的存储配额管理
  - 文件访问需要租户认证

## 三、业务流程设计

### 3.1 工作流核心概念
- **流程模板(WorkflowTemplate)**：
  - 定义完整的业务流程
  - 包含所有环节和转换规则
  - 支持租户级别的自定义配置
- **活动节点(ActivityDefinition)**：
  - 定义流程中的具体环节
  - 包括起始节点、结束节点和中间节点
  - 支持节点顺序和条件配置
- **转换规则(ActivityTransition)**：
  - 定义节点之间的流转条件
  - 支持事件触发和条件判断
  - 可配置节点跳过和并行处理
- **流程实例(WorkflowInstance)**：
  - 基于模板创建的具体业务实例
  - 记录当前节点和流程状态
  - 支持实例级别的状态管理
- **活动实例(ActivityInstance)**：
  - 记录每个环节的执行情况
  - 包含开始和结束时间
  - 记录操作人和处理状态

### 3.2 任务类型
- **事故维修**：车辆因事故需要维修的任务
- **自费维修**：客户自费的维修任务
- **车辆保养**：常规的车辆保养任务
- **常规保养**：按照车辆里程或时间进行的定期保养
- **终端维修**：车辆终端设备的维修任务
- **自定义任务**：系统支持新增自定义任务类型

### 3.3 标准流程示例

#### 事故维修流程
1. **车辆交接**（起始节点）
   - 车辆信息登记
   - 状态更新
   - 外部系统通知
2. **进保预审**
   - 预审报价
   - 状态流转
   - 外部系统交互
3. **维修报价**
   - 定损报价
   - 配件管理
   - 外部系统对接
4. **核损核价**
   - 核价审核
   - 配件确认
   - 外部系统同步
5. **车辆维修**
   - 维修进度管理
   - 超时处理
   - 预算管理
6. **车辆验收**（结束节点）
   - 验收确认
   - 费用结算
   - 外部系统通知

#### 自费维修流程
1. **车辆交接**（起始节点）
2. **维修报价**
3. **核损核价**
4. **车辆维修**
5. **车辆验收**（结束节点）

## 四、核心业务模块

### 4.1 车辆交接
- **状态**：起始节点
- **主要操作**：
  - 确认接车（触发事件：完成交接）
  - 驳回
- **关键功能**：
  - 车辆信息登记
  - 状态更新
  - 外部系统通知（梧桐系统、出厂登记）
- **业务逻辑**：
  - 状态校验
  - 流程实例状态更新
  - 活动实例记录
  - 操作日志记录
  - 外部系统通知

### 4.2 进保预审
- **状态**：
  - 未处理
  - 已保存
  - 待审核
  - 已驳回
  - 已转自费
  - 已关闭
- **主要操作**：
  - 保存
  - 提交审核（触发事件：提交预审）
  - 审核通过（触发事件：审核通过）
  - 驳回
  - 转自费
- **关键功能**：
  - 预审报价
  - 状态流转
  - 外部系统交互
- **业务逻辑**：
  - 任务状态校验
  - 预审报价校验
  - 审核级别校验
  - 外部系统通知
  - 客户直付金额调整

### 4.3 维修报价
- **状态**：
  - 未处理
  - 已保存
  - 已提交
- **主要操作**：
  - 保存
  - 提交审核（触发事件：提交报价）
  - 维修报价（精友/自有）
- **关键功能**：
  - 定损报价
  - 配件管理
  - 外部系统对接
- **业务逻辑**：
  - 业务数据校验
  - 配件信息处理
  - 外部系统交互
  - 保险报案号同步
  - 客户直付金额调整

### 4.4 核损核价
- **状态**：
  - 未处理
  - 处理中
  - 审核通过
  - 退回
- **主要操作**：
  - 保存
  - 审核通过（触发事件：审核通过）
  - 退回
  - 平级移交
- **关键功能**：
  - 核价审核
  - 配件确认
  - 外部系统同步
- **业务逻辑**：
  - 核损核价预检查
  - 配件信息保存
  - 业财对接
  - 追偿任务生成
  - 精友系统同步

### 4.5 车辆维修
- **状态**：
  - 维修中
  - 已申请验收
- **主要操作**：
  - 申请验收（触发事件：申请验收）
- **关键功能**：
  - 维修进度管理
  - 超时处理
  - 预算管理
- **业务逻辑**：
  - 任务状态校验
  - 超时原因记录
  - 自费金额计算
  - 预算更新
  - 外部系统通知

### 4.6 车辆验收
- **状态**：
  - 待验收
  - 验收通过
  - 验收不通过
  - 已关闭
- **主要操作**：
  - 验收通过
  - 验收不通过
  - 税率调整
  - 金额调整
- **关键功能**：
  - 验收确认
  - 费用结算
  - 外部系统通知
- **业务逻辑**：
  - 任务状态校验
  - 费用重新计算
  - 预算处理
  - 多系统通知
  - 短信通知

## 五、系统管理功能

### 5.1 流程模板管理
- **模板设计**：
  - 可视化流程设计器
  - 节点和转换规则配置
  - 模板版本管理
  - 模板导入导出
- **模板部署**：
  - 模板测试和验证
  - 发布和生效控制
  - 租户隔离管理
  - 权限控制

### 5.2 流程实例管理
- **实例监控**：
  - 实时状态查看
  - 历史记录查询
  - 条件筛选
  - 图形化展示
- **实例干预**：
  - 挂起和恢复
  - 手动终止
  - 节点跳转
  - 异常处理
  - 数据修正

### 5.3 活动实例管理
- **活动监控**：
  - 节点状态查看
  - 历史记录查询
  - 耗时统计
  - 效率分析
  - 异常预警
- **活动干预**：
  - 手动完成
  - 转派和委托
  - 撤回和重做
  - 超时处理
  - 批量处理

## 六、监控与分析

### 6.1 环节耗时监控
- **耗时记录**：
  - 环节状态耗时记录
  - 关键节点时间记录
  - 状态转换耗时
- **统计分析**：
  - 实时耗时统计
  - 历史趋势分析
  - 效率评估
  - 异常预警

### 6.2 业务数据分析
- **效率分析**：
  - 环节效率评估
  - 处理人效率分析
  - 租户效率对比
- **优化建议**：
  - 效率优化建议
  - 最佳实践推荐
  - 异常处理建议

