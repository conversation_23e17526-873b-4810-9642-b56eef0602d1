# 多租户架构设计

## 📋 概述

本文档详细说明Auto Care SaaS项目的多租户架构设计，包括数据隔离策略、租户管理机制、资源分配方案等核心内容。

> **重要说明**：本项目的多租户架构采用**MyBatis拦截器**方式实现，而非动态数据源切换。通过`TenantSchemaInterceptor`拦截SQL执行过程，动态修改SQL语句为多租户表（mtc_开头）添加Schema前缀，实现租户数据隔离。

## 🏗️ 多租户架构模式

### 架构选择：独立Schema + MyBatis拦截器模式

我们采用**独立Schema模式**结合**MyBatis拦截器**作为多租户架构的核心设计。每个租户拥有独立的数据库Schema，通过MyBatis拦截器在SQL执行时动态添加Schema前缀，实现完全的数据隔离。

```
┌─────────────────────────────────────────────────────────┐
│                    应用层                                │
│              Auto Care SaaS Application                 │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                 MyBatis拦截器层                          │
│           TenantSchemaInterceptor                       │
│         (动态SQL Schema前缀添加)                          │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    数据库层                               │
│ auto_care_saas │ auto_care_dzjt │ auto_care_extracme     │
│   (系统库)      │   (租户1)       │     (租户2)           │
│  sys_* 表      │   mtc_* 表      │     mtc_* 表          │
└─────────────────────────────────────────────────────────┘
```

### 架构优势
- **完全数据隔离**：租户间数据完全独立，安全性最高
- **性能隔离**：租户间不会相互影响性能
- **实现简单**：通过MyBatis拦截器实现，无需复杂的数据源管理
- **透明性好**：业务代码无需感知多租户逻辑，拦截器自动处理
- **扩展性强**：可以灵活添加新租户Schema
- **合规性好**：满足严格的数据合规要求
- **维护便利**：统一的数据库连接池，简化运维管理

## 🗄️ 数据隔离策略

### 1. 数据库Schema设计

#### 系统库 (auto_care_saas)
存储系统级别的公共数据：
- 租户信息表 (sys_tenant)
- 用户信息表 (sys_user)
- 角色权限表 (sys_role, sys_permission)
- 数据同步日志 (sys_data_sync_log)

#### 租户库 (auto_care_tenant_{tenant_code})
存储租户专属的业务数据：
- 车辆信息表 (mtc_vehicle_info)
- 维修任务表 (mtc_repair_task)
- 维修厂信息表 (mtc_repair_depot_info)
- 工作流实例表 (workflow_instance)

### 2. 表命名规范

| 前缀 | 说明 | 存储位置 | 示例 |
|------|------|----------|------|
| sys_ | 系统级表 | 系统库 | sys_tenant, sys_user |
| mtc_ | 多租户业务表 | 租户库 | mtc_vehicle_info |
| workflow_ | 工作流表 | 租户库 | workflow_instance |

### 3. 租户上下文管理

#### 租户识别机制
```java
// 通过Session获取租户信息
Long tenantId = SessionUtils.getTenantId();
LoginUser loginUser = SessionUtils.getLoginUser();

// 或通过JWT Token中的租户信息
{
  "sub": "user123",
  "tenantId": 1,
  "tenantCode": "EXTRACME"
}
```

#### 租户上下文管理
```java
/**
 * 租户上下文持有器
 * 管理多个schema:
 * - auto_care_saas: 存储公共账号信息
 * - auto_care_extracme: 租户2的数据
 * - auto_care_dzjt: 租户1的数据
 */
@Slf4j
public class TenantContextHolder {
    private static final ThreadLocal<Long> TENANT_ID = new ThreadLocal<>();
    private static final ThreadLocal<String> TENANT_SCHEMA = new ThreadLocal<>();

    // 定义默认和公共schema
    private static final String DEFAULT_SCHEMA = "auto_care_saas";

    /**
     * 设置租户上下文
     * @param tenantId 租户ID
     */
    public static void setTenant(Long tenantId) {
        if (tenantId == null) {
            TENANT_ID.set(null);
            TENANT_SCHEMA.set(DEFAULT_SCHEMA);
            return;
        }

        String schema = formatSchema(tenantId);
        TENANT_ID.set(tenantId);
        TENANT_SCHEMA.set(schema);
    }

    /**
     * 根据租户ID格式化schema名称
     */
    private static String formatSchema(Long tenantId) {
        if (tenantId == null) {
            return DEFAULT_SCHEMA;
        }

        switch (tenantId.intValue()) {
            case 1:
                return "auto_care_dzjt";
            case 2:
                return "auto_care_extracme";
            default:
                return DEFAULT_SCHEMA;
        }
    }
}
```

## 🔧 租户管理机制

### 1. 租户生命周期管理

#### 租户注册流程
```
1. 租户信息注册 → 2. 数据库Schema创建 → 3. 初始化数据 → 4. 配置租户映射 → 5. 激活租户
```

#### 租户配置管理
```java
@Entity
@Table(name = "sys_tenant")
public class SysTenant {
    private Long id;
    private String tenantName;      // 租户名称
    private String tenantCode;      // 租户编码
    private String contactName;     // 联系人
    private String contactPhone;    // 联系电话
    private Date expireTime;        // 过期时间
    private Integer maxUserCount;   // 最大用户数
    private Integer status;         // 状态
}
```

### 2. 租户资源管理

#### 资源配额控制
- **用户数量限制**：控制租户最大用户数
- **存储空间限制**：控制文件存储配额
- **API调用限制**：控制接口调用频率
- **并发连接限制**：控制数据库连接数

#### 资源监控
```java
@Service
public class TenantResourceMonitor {
    
    public TenantResourceUsage getResourceUsage(String tenantCode) {
        return TenantResourceUsage.builder()
            .userCount(getUserCount(tenantCode))
            .storageUsed(getStorageUsage(tenantCode))
            .apiCallCount(getApiCallCount(tenantCode))
            .build();
    }
}
```

## 🔐 安全隔离机制

### 1. 数据访问控制

#### MyBatis拦截器实现原理

项目采用MyBatis拦截器来实现多租户数据隔离，通过拦截SQL执行过程，动态修改SQL语句，为多租户表添加Schema前缀。

```java
/**
 * 多租户Schema拦截器
 * 用于自动处理多租户场景下的数据库Schema切换
 * 通过拦截MyBatis的SQL执行，动态修改SQL语句，添加租户的Schema前缀
 */
@Slf4j
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare",
              args = {Connection.class, Integer.class})
})
public class TenantSchemaInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");

        // 获取执行的方法信息
        String id = mappedStatement.getId();
        String className = id.substring(0, id.lastIndexOf("."));
        String methodName = id.substring(id.lastIndexOf(".") + 1);
        Class<?> clazz = Class.forName(className);

        // 检查类上的@TenantSchema注解
        TenantSchema tenantSchema = clazz.getAnnotation(TenantSchema.class);
        if (tenantSchema == null || !tenantSchema.value()) {
            return invocation.proceed();
        }

        // 检查方法是否在排除列表中
        if (tenantSchema.excludeMethods().length > 0 &&
            Arrays.asList(tenantSchema.excludeMethods()).contains(methodName)) {
            return invocation.proceed();
        }

        // 获取当前租户schema
        String schema = TenantContextHolder.getTenantSchema();
        if (schema == null || "auto_care_saas".equals(schema)) {
            return invocation.proceed();
        }

        // 获取原始SQL
        BoundSql boundSql = statementHandler.getBoundSql();
        String sql = boundSql.getSql();

        // 识别SQL类型并应用相应的处理逻辑
        String sqlType = identifySqlType(sql);
        String modifiedSql;

        switch (sqlType) {
            case "INSERT":
                modifiedSql = processInsertSql(sql, schema);
                break;
            case "DELETE":
                modifiedSql = processDeleteSql(sql, schema);
                break;
            case "SELECT":
                modifiedSql = processSelectSql(sql, schema);
                break;
            case "UPDATE":
                modifiedSql = processUpdateSql(sql, schema);
                break;
            default:
                modifiedSql = addTenantSchema(sql, schema);
                break;
        }

        // 反射修改SQL
        Field field = boundSql.getClass().getDeclaredField("sql");
        field.setAccessible(true);
        field.set(boundSql, modifiedSql);

        return invocation.proceed();
    }
}
```

#### @TenantSchema注解
```java
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TenantSchema {
    /**
     * 是否启用租户Schema前缀
     * @return true表示启用，false表示不启用
     */
    boolean value() default true;

    /**
     * 指定不需要添加租户Schema的方法名列表
     */
    String[] excludeMethods() default {};
}
```

#### SQL处理逻辑

拦截器针对不同类型的SQL语句采用不同的处理策略：

1. **INSERT语句处理**：
```java
private String processInsertSql(String sql, String tenant) {
    // 在"INSERT INTO 表名"模式中为表名添加schema前缀
    // 只对以"mtc_"开头的表名添加租户Schema前缀
    Matcher insertMatcher = INSERT_PATTERN.matcher(sql);
    StringBuffer result = new StringBuffer();

    while (insertMatcher.find()) {
        String tableName = insertMatcher.group(1);
        if (shouldAddTenantSchema(tableName)) {
            insertMatcher.appendReplacement(result,
                "INSERT INTO " + tenant + "." + tableName);
        } else {
            insertMatcher.appendReplacement(result,
                "INSERT INTO " + tableName);
        }
    }
    insertMatcher.appendTail(result);
    return result.toString();
}
```

2. **SELECT语句处理**：
```java
private String processSelectSql(String sql, String tenant) {
    // 处理FROM子句和JOIN子句，为表名添加schema前缀
    // 只对以"mtc_"开头的表名添加租户Schema前缀

    // 处理FROM子句
    Matcher fromMatcher = FROM_PATTERN.matcher(sql);
    StringBuffer fromResult = new StringBuffer();

    while (fromMatcher.find()) {
        String spaces = fromMatcher.group(1);
        String tableName = fromMatcher.group(2);
        if (shouldAddTenantSchema(tableName)) {
            fromMatcher.appendReplacement(fromResult,
                spaces + "FROM " + tenant + "." + tableName);
        } else {
            fromMatcher.appendReplacement(fromResult,
                spaces + "FROM " + tableName);
        }
    }
    fromMatcher.appendTail(fromResult);

    // 类似地处理JOIN子句...
    return fromResult.toString();
}
```

#### 表名过滤规则
```java
/**
 * 判断是否需要为表名添加租户Schema前缀
 * 只对以"mtc_"开头的表名添加租户Schema前缀
 */
private boolean shouldAddTenantSchema(String tableName) {
    return tableName != null && tableName.toLowerCase().startsWith("mtc_");
}
```

#### Web拦截器集成
```java
/**
 * 租户Web拦截器
 * 在请求处理前设置租户上下文
 */
@Component
public class TenantWebInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // 从Session获取租户信息
            Long tenantId = SessionUtils.getTenantId();
            LoginUser loginUser = SessionUtils.getLoginUser();

            if (tenantId == null) {
                log.warn("未获取到租户信息，用户：{}", loginUser != null ? loginUser.getUsername() : "未登录");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return false;
            }

            // 设置租户上下文
            TenantContextHolder.setTenant(tenantId);
            return true;
        } catch (Exception e) {
            log.error("租户上下文设置失败", e);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return false;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                               Object handler, Exception ex) {
        // 清理租户上下文
        TenantContextHolder.clear();
    }
}
```

#### 使用示例
```java
// 在Mapper接口上添加@TenantSchema注解
@Mapper
@TenantSchema
public interface MtcVehicleInfoExtendMapper extends MtcVehicleInfoMapper {

    // 这个方法会自动添加租户Schema前缀
    List<MtcVehicleInfo> selectByCondition(VehicleQueryDTO queryDTO);

    // 可以排除特定方法不进行租户隔离
    @TenantSchema(excludeMethods = {"selectSystemConfig"})
    SystemConfig selectSystemConfig();
}
```

### 2. 数据库Schema隔离

#### 实际Schema配置
项目中实际使用的数据库Schema配置：

| 租户ID | Schema名称 | 用途 | 说明 |
|--------|------------|------|------|
| - | auto_care_saas | 系统库 | 存储公共账号信息、用户认证、权限配置等 |
| 1 | auto_care_dzjt | 租户1数据 | 存储DZJT租户的业务数据 |
| 2 | auto_care_extracme | 租户2数据 | 存储Extracme租户的业务数据 |

#### Schema路由规则
```java
/**
 * 根据租户ID格式化schema名称
 */
private static String formatSchema(Long tenantId) {
    if (tenantId == null) {
        return "auto_care_saas";  // 默认系统库
    }

    switch (tenantId.intValue()) {
        case 1:
            return "auto_care_dzjt";      // 租户1
        case 2:
            return "auto_care_extracme";  // 租户2
        default:
            return "auto_care_saas";      // 未知租户使用系统库
    }
}
```

#### SQL转换示例
原始SQL：
```sql
SELECT * FROM mtc_vehicle_info WHERE status = 1
```

租户1执行时转换为：
```sql
SELECT * FROM auto_care_dzjt.mtc_vehicle_info WHERE status = 1
```

租户2执行时转换为：
```sql
SELECT * FROM auto_care_extracme.mtc_vehicle_info WHERE status = 1
```

系统表（sys_开头）不会被转换：
```sql
SELECT * FROM sys_user WHERE id = 1
-- 保持不变，所有租户共享系统表
```

## 📊 性能优化策略

### 1. SQL处理性能优化

#### SQL缓存机制
```java
public class TenantSchemaInterceptor implements Interceptor {
    // SQL缓存，避免重复处理相同的SQL语句
    private static final Map<String, String> sqlCache = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 1000;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 构建缓存键
        String cacheKey = schema + ":" + sql.hashCode();
        String modifiedSql = sqlCache.get(cacheKey);

        if (modifiedSql == null) {
            // 缓存未命中，需要处理SQL
            modifiedSql = processSqlByType(sql, schema);

            // 将处理后的SQL添加到缓存
            if (sqlCache.size() < MAX_CACHE_SIZE) {
                sqlCache.put(cacheKey, modifiedSql);
            }
        }

        return invocation.proceed();
    }
}
```

#### 正则表达式预编译
```java
public class TenantSchemaInterceptor {
    // 预编译正则表达式，提高性能
    private static final Pattern INSERT_PATTERN = Pattern.compile(
        "INSERT\\s+INTO\\s+([a-zA-Z_][a-zA-Z0-9_]*)",
        Pattern.CASE_INSENSITIVE
    );

    private static final Pattern FROM_PATTERN = Pattern.compile(
        "(\\s+)FROM\\s+([a-zA-Z_][a-zA-Z0-9_]*)",
        Pattern.CASE_INSENSITIVE
    );

    private static final Pattern DELETE_PATTERN = Pattern.compile(
        "DELETE\\s+FROM\\s+([a-zA-Z_][a-zA-Z0-9_]*)",
        Pattern.CASE_INSENSITIVE
    );
}
```

### 2. 缓存策略

#### 租户级缓存隔离
```java
@Cacheable(value = "vehicles", key = "#tenantId + ':' + #vehicleId")
public Vehicle getVehicle(Long tenantId, Long vehicleId) {
    // 自动通过TenantContextHolder获取租户上下文
    return vehicleRepository.findById(vehicleId);
}
```

#### 租户上下文缓存
```java
@Service
public class TenantCacheService {

    @Cacheable(value = "tenant-schema", key = "#tenantId")
    public String getTenantSchema(Long tenantId) {
        return TenantContextHolder.formatSchema(tenantId);
    }

    @CacheEvict(value = "tenant-schema", key = "#tenantId")
    public void evictTenantSchema(Long tenantId) {
        // 清除租户Schema缓存
    }
}
```

## 🚀 扩展性设计

### 1. 水平扩展

#### Schema扩展策略
```
当前Schema配置：
- auto_care_saas: 系统库（共享）
- auto_care_dzjt: 租户1专用
- auto_care_extracme: 租户2专用

扩展方案：
- 新增租户3: auto_care_tenant3
- 新增租户4: auto_care_tenant4
- 支持动态Schema创建和映射
```

#### 租户Schema动态扩展
```java
@Service
public class TenantSchemaManager {

    /**
     * 为新租户创建Schema
     */
    public void createTenantSchema(Long tenantId, String tenantCode) {
        String schemaName = "auto_care_" + tenantCode.toLowerCase();

        // 1. 创建数据库Schema
        jdbcTemplate.execute("CREATE DATABASE IF NOT EXISTS " + schemaName);

        // 2. 执行表结构初始化脚本
        initializeTenantTables(schemaName);

        // 3. 更新租户Schema映射
        updateTenantSchemaMapping(tenantId, schemaName);

        // 4. 清除相关缓存
        cacheManager.getCache("tenant-schema").evict(tenantId);
    }

    /**
     * 动态更新租户Schema映射
     */
    private void updateTenantSchemaMapping(Long tenantId, String schemaName) {
        // 更新TenantContextHolder中的映射逻辑
        // 或者使用数据库配置表来管理映射关系
    }
}
```

### 2. 功能扩展

#### 租户定制化
```java
@Service
public class TenantCustomizationService {

    public WorkflowTemplate getCustomWorkflow(Long tenantId, String taskType) {
        // 根据租户ID获取定制的工作流模板
        // 自动通过TenantSchemaInterceptor路由到对应Schema
        return workflowRepository.findByTenantAndType(tenantId, taskType);
    }

    public List<String> getTenantSpecificTables(Long tenantId) {
        // 获取租户专用表列表（mtc_开头的表）
        String schema = TenantContextHolder.getTenantSchema();
        return jdbcTemplate.queryForList(
            "SELECT table_name FROM information_schema.tables " +
            "WHERE table_schema = ? AND table_name LIKE 'mtc_%'",
            String.class, schema
        );
    }
}
```

#### 多租户注解扩展
```java
/**
 * 扩展@TenantSchema注解功能
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface TenantSchema {
    boolean value() default true;
    String[] excludeMethods() default {};

    // 新增：指定特定租户才启用
    long[] enabledTenants() default {};

    // 新增：指定表名前缀过滤规则
    String[] tablePrefix() default {"mtc_"};
}
```

## 📋 运维管理

### 1. 监控告警

#### 租户级监控指标
- 数据库连接数
- API响应时间
- 错误率统计
- 资源使用率

#### 告警规则
```yaml
alerts:
  - name: TenantDatabaseConnections
    condition: connections > 80% of max_pool_size
    action: scale_up_pool
  
  - name: TenantAPIErrors
    condition: error_rate > 5%
    action: notify_admin
```

### 2. 备份恢复

#### 租户数据备份
```bash
# 每日备份脚本
#!/bin/bash
for tenant in $(get_active_tenants); do
    mysqldump auto_care_${tenant} > backup_${tenant}_$(date +%Y%m%d).sql
done
```

#### 灾难恢复
```java
@Service
public class TenantDisasterRecovery {
    
    public void restoreTenant(String tenantCode, Date backupDate) {
        // 恢复租户数据
        String backupFile = getBackupFile(tenantCode, backupDate);
        databaseService.restore(tenantCode, backupFile);
    }
}
```

## 📞 联系方式

多租户架构相关问题请联系：
- 架构师：<EMAIL>
- DBA团队：<EMAIL>

---

> 📝 **文档版本**：v2.0  
> 🕒 **最后更新**：2025-01-19  
> 👥 **维护团队**：Auto Care Architecture Team
