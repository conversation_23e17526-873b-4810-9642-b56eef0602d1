# Auto Care SaaS 技术栈文档

> 本文档详细说明了Auto Care SaaS项目使用的技术栈，包括版本信息、配置说明和使用场景。

> **文档版本**：v2.0
> **最后更新**：2025-01-19
> **维护团队**：Auto Care Architecture Team

## 目录
- [基础技术栈](#基础技术栈)
- [数据访问层](#数据访问层)
- [工具库](#工具库)
- [测试框架](#测试框架)
- [文档工具](#文档工具)
- [开发工具](#开发工具)
- [版本控制](#版本控制)
- [构建部署](#构建部署)
- [监控运维](#监控运维)
- [安全框架](#安全框架)
- [缓存](#缓存)

## 基础技术栈

### Java
- **版本**：8
- **发行版**：Amazon Corretto
- **编译器**：javac
- **编码**：UTF-8
- **说明**：使用 Amazon Corretto 发行版，确保跨平台兼容性

### Spring Boot
- **版本**：2.7.5
- **父项目**：spring-boot-starter-parent
- **主要功能**：
  - Web：提供 Web 开发支持，包括 RESTful API 开发
  - Security：提供安全认证和授权功能
  - Validation：提供数据验证功能
  - AOP：提供面向切面编程支持
  - Test：提供测试框架支持
- **说明**：作为基础框架，提供完整的 Web 应用开发支持

### Maven
- **版本**：3.8.4
- **配置说明**：
  - 使用阿里云镜像：加速依赖下载
  - 配置 JDK 版本：确保编译环境一致性
  - 配置编码格式：统一使用 UTF-8
- **说明**：作为项目构建工具，管理项目依赖和构建流程

## 数据访问层

### MyBatis
- **版本**：2.2.2
- **主要功能**：
  - Dynamic SQL：提供动态 SQL 构建功能
  - Type Handler：提供自定义类型处理器
  - Page Helper：提供分页查询支持
- **说明**：作为 ORM 框架，提供灵活的数据访问能力

### MySQL
- **版本**：8.0
- **驱动**：com.mysql:mysql-connector-j:8.0.33
- **主要功能**：
  - 事务支持：确保数据一致性
  - 索引优化：提升查询性能
  - 主从复制：支持读写分离
- **说明**：作为主数据库，提供可靠的数据存储服务

## 工具库

### Lombok
- **版本**：1.18.26
- **主要功能**：
  - @Data：自动生成 getter/setter 等方法
  - @Builder：提供建造者模式支持
  - @Slf4j：提供日志支持
- **说明**：用于简化 Java 代码，提高开发效率

### Apache Commons
- **commons-lang3**：3.12.0
  - 提供字符串处理、日期处理等通用工具
- **commons-io**：2.11.0
  - 提供文件操作、流处理等 IO 工具
- **commons-collections4**：4.4
  - 提供增强的集合操作工具

### Guava
- **版本**：31.1-jre
- **主要功能**：
  - 集合工具：提供增强的集合操作
  - 字符串处理：提供字符串处理工具
  - 缓存：提供本地缓存实现
- **说明**：Google Guava 提供丰富的工具类库

## 测试框架

### JUnit
- **版本**：5.9.2
- **主要功能**：
  - 参数化测试：支持多参数测试场景
  - 条件测试：支持条件执行测试
  - 测试生命周期：提供完整的测试生命周期管理
- **说明**：作为单元测试框架，确保代码质量

### Mockito
- **版本**：4.11.0
- **主要功能**：
  - 方法模拟：支持方法行为模拟
  - 行为验证：验证方法调用
  - 异常模拟：支持异常场景测试
- **说明**：用于模拟对象，支持单元测试

### AssertJ
- **版本**：3.24.2
- **主要功能**：
  - 流式断言：提供流畅的断言 API
  - 集合断言：支持集合验证
  - 异常断言：支持异常验证
- **说明**：提供更易读的断言方式

## 文档工具

### Swagger
- **版本**：3.0.0
- **主要功能**：
  - API 文档：自动生成 API 文档
  - 接口测试：提供接口测试界面
  - 模型说明：自动生成数据模型文档
- **说明**：用于 API 文档生成和测试

### Asciidoctor
- **版本**：2.5.3
- **主要功能**：
  - 技术文档：支持技术文档编写
  - API 文档：支持 API 文档生成
  - 部署文档：支持部署文档编写
- **说明**：用于技术文档编写

## 开发工具

### IDE
- **IntelliJ IDEA**：主要开发 IDE，提供强大的 Java 开发支持
- **Visual Studio Code**：轻量级编辑器，用于前端开发
- **Eclipse**：备选 IDE，支持 Java 开发

### 插件
- **Lombok**：简化 Java 代码编写
- **MyBatisX**：提供 MyBatis 开发支持
- **Spring Boot Tools**：提供 Spring Boot 开发支持
- **Maven Helper**：帮助解决 Maven 依赖问题

## 版本控制

### Git
- **版本**：2.x
- **主要功能**：
  - 分支管理：支持多分支开发
  - 代码审查：支持代码审查流程
  - 持续集成：支持自动化构建
- **说明**：用于版本控制和团队协作

## 构建部署

### Docker
- **版本**：20.10
- **主要功能**：
  - 容器化：支持应用容器化部署
  - 多阶段构建：优化镜像大小
  - 镜像优化：提供镜像优化建议
- **说明**：用于应用容器化部署

### Jenkins
- **版本**：2.x
- **主要功能**：
  - 自动化构建：支持自动化构建流程
  - 自动化测试：支持自动化测试执行
  - 自动化部署：支持自动化部署流程
- **说明**：用于持续集成和部署

## 监控运维

### Prometheus
- **版本**：2.x
- **主要功能**：
  - 指标收集：收集应用性能指标
  - 告警规则：支持自定义告警规则
  - 数据可视化：支持指标数据展示
- **说明**：用于应用监控和告警

### Grafana
- **版本**：9.x
- **主要功能**：
  - 仪表盘：提供可视化监控面板
  - 告警通知：支持多渠道告警通知
  - 数据源集成：支持多种数据源
- **说明**：用于监控数据可视化

## 安全框架

### Spring Security
- **版本**：5.7.8
- **主要功能**：
  - 认证授权：提供身份认证和权限控制
  - 会话管理：支持会话安全管理
  - 密码加密：提供密码加密功能
- **说明**：提供应用安全保护

### JWT
- **版本**：0.11.5
- **主要功能**：
  - Token 生成：支持 JWT Token 生成
  - Token 验证：支持 Token 验证
  - 过期管理：支持 Token 过期控制
- **说明**：用于无状态身份认证

## 缓存

### Redis
- **版本**：6.2
- **主要功能**：
  - 数据缓存：提供高性能数据缓存
  - 分布式锁：支持分布式锁实现
  - 消息队列：支持消息队列功能
- **说明**：用于数据缓存和分布式功能

---

> 注意：本文档会定期更新，请关注版本号和最后更新时间。如有任何问题或建议，请联系 AutoCare Team。 