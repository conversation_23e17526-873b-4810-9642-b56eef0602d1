# JWT Token优化方案实现说明

## 概述

本文档描述了JWT token优化方案的实现，将用户的组织权限信息从JWT转移到Redis缓存中，以减少JWT token大小并提高权限信息更新的实时性。

## 实现目标

1. **减少JWT token大小**：将orgIds、allAccessibleOrgIds、permissions字段从JWT中移除
2. **提高权限更新实时性**：用户权限变更后无需重新登录即可获得最新权限
3. **保持API兼容性**：现有的LoginUser数据结构和API接口保持不变
4. **添加降级处理**：Redis不可用时自动降级到数据库查询

## 架构设计

### 缓存策略

- **缓存Key规则**：`user:permissions:{tenantId}:{userId}`
- **缓存过期时间**：24小时
- **缓存数据结构**：JSON格式存储权限集合、组织ID列表、可访问组织ID列表

### 数据流程

1. **登录时**：
   - 生成JWT token（仅包含基础用户信息）
   - 将权限信息缓存到Redis
   
2. **请求处理时**：
   - 从JWT解析基础用户信息
   - 从Redis获取权限信息
   - 组装完整的LoginUser对象
   
3. **权限变更时**：
   - 自动刷新对应的Redis缓存
   - 用户无需重新登录

## 核心组件

### 1. UserPermissionCacheUtils

用户权限缓存工具类，提供权限信息的Redis缓存操作。

**主要方法**：
- `cacheUserPermissions()` - 缓存用户权限信息
- `getUserPermissions()` - 获取用户权限信息
- `refreshUserPermissions()` - 刷新用户权限缓存
- `deleteUserPermissions()` - 删除用户权限缓存

**缓存数据结构**：
```java
public static class UserPermissionCache {
    private Set<String> permissions;           // 权限集合
    private List<String> orgIds;              // 直接关联组织ID
    private List<String> allAccessibleOrgIds; // 可访问所有组织ID
}
```

### 2. JwtUserInfoDTO优化

移除了以下字段以减少JWT token大小：
- `permissions` - 权限编码列表
- `orgIds` - 用户直接关联的组织ID列表  
- `allAccessibleOrgIds` - 用户可访问的所有组织ID列表

### 3. SessionUtils改造

修改`getLoginUserFromJwtToken()`方法：
- 从JWT解析基础用户信息
- 从Redis获取权限信息
- 提供Redis不可用时的降级处理

**降级处理机制**：
```java
// Redis不可用时的降级处理
if (!userPermissionCacheUtils.isRedisAvailable()) {
    permissions = queryPermissionsFromDatabase(user);
    orgIds = queryOrgIdsFromDatabase(user.getId());
    allAccessibleOrgIds = calculateAllAccessibleOrgIds(orgIds, user.getTenantId());
}
```

### 4. AuthServiceImpl改造

修改`buildJwtUserInfo()`方法：
- 查询权限信息后缓存到Redis
- 不再将权限信息存储到JWT中

## 缓存刷新机制

### 自动刷新触发点

1. **用户信息修改**（UserServiceImpl.updateUser）
2. **用户状态变更**（UserServiceImpl.updateUserStatus）
3. **用户组织关联变更**（UserServiceImpl.updateUserOrgs）
4. **角色权限变更**（RoleServiceImpl.assignPermissions）
5. **角色状态变更**（RoleServiceImpl.updateRoleStatus）

### 刷新策略

- **单用户刷新**：删除指定用户的权限缓存
- **批量刷新**：根据角色或组织变更，批量删除相关用户缓存
- **租户级刷新**：删除整个租户下所有用户的权限缓存

## 性能优化

### JWT Token大小减少

优化前后JWT token大小对比：
- **优化前**：包含完整权限信息，token可能超过4KB
- **优化后**：仅包含基础用户信息，token大小显著减少

### 缓存命中率

- **缓存过期时间**：24小时，平衡内存使用和性能
- **缓存刷新**：权限变更时主动刷新，确保数据一致性
- **降级处理**：Redis不可用时自动降级，保证系统可用性

## 多租户支持

### 租户隔离

- **缓存Key包含租户ID**：确保不同租户的权限信息隔离
- **批量操作支持租户过滤**：权限刷新时限制在指定租户范围内
- **降级处理保持租户上下文**：数据库查询时正确设置租户上下文

## 错误处理

### 异常处理策略

1. **Redis连接异常**：自动降级到数据库查询
2. **缓存解析异常**：记录错误日志，返回空权限信息
3. **权限查询异常**：记录错误日志，使用默认权限

### 日志记录

- **缓存操作日志**：记录缓存的成功/失败状态
- **降级处理日志**：记录降级原因和处理结果
- **性能监控日志**：记录缓存命中率和响应时间

## 部署注意事项

### Redis配置

- **内存配置**：根据用户数量和权限复杂度配置合适的内存
- **持久化配置**：建议开启RDB持久化，避免重启后缓存丢失
- **集群配置**：生产环境建议使用Redis集群保证高可用

### 监控指标

- **缓存命中率**：监控权限缓存的命中情况
- **降级频率**：监控Redis不可用导致的降级次数
- **响应时间**：监控权限获取的响应时间

## 测试验证

### 功能测试

1. **登录流程测试**：验证权限信息正确缓存到Redis
2. **权限获取测试**：验证从Redis正确获取权限信息
3. **权限变更测试**：验证权限变更后缓存自动刷新
4. **降级处理测试**：验证Redis不可用时的降级处理

### 性能测试

1. **JWT token大小测试**：对比优化前后的token大小
2. **缓存性能测试**：测试权限获取的响应时间
3. **并发测试**：测试高并发场景下的缓存性能

## 总结

通过将权限信息从JWT转移到Redis缓存，实现了以下目标：

1. **减少了JWT token大小**，提高了网络传输效率
2. **提高了权限更新的实时性**，用户无需重新登录
3. **保持了API兼容性**，现有代码无需大幅修改
4. **增加了系统的可靠性**，提供了完善的降级处理机制

该方案在保证功能完整性的同时，显著提升了系统的性能和用户体验。
