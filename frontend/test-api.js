#!/usr/bin/env node

/**
 * OSS分片上传API测试脚本
 * 使用Node.js测试后端接口的可用性
 */

const https = require('https');
const http = require('http');

// API配置
const API_CONFIG = {
    baseUrl: 'http://localhost:30030/auto-care-saas',
    token: 'Bearer eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDkyNjM3ODEsInVzZXJJZCI6IjIiLCJpYXQiOjE3NDkxNzczODF9.J2aiO3poRFfCWAUTcTBlddgAXu4Bojh7VpaB7p8Hnpw'
};

// 通用请求函数
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const isHttps = urlObj.protocol === 'https:';
        const client = isHttps ? https : http;
        
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || (isHttps ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': API_CONFIG.token,
                ...options.headers
            }
        };

        if (options.body) {
            requestOptions.headers['Content-Length'] = Buffer.byteLength(options.body);
        }

        const req = client.request(requestOptions, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = {
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        headers: res.headers,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        headers: res.headers,
                        rawData: data,
                        parseError: error.message
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (options.body) {
            req.write(options.body);
        }

        req.end();
    });
}

// 测试函数
async function testConnection() {
    console.log('🔗 测试后端连接...');
    try {
        const url = `${API_CONFIG.baseUrl}/api/v1/basic/dict/list`;
        const requestData = {
            pageNum: 1,
            pageSize: 10
        };

        const result = await makeRequest(url, {
            method: 'POST',
            body: JSON.stringify(requestData)
        });

        if (result.status === 200) {
            console.log('✅ 后端连接成功');
            return true;
        } else {
            console.log('❌ 后端连接失败:', result.status, result.statusText);
            console.log('响应数据:', result.data || result.rawData);
            return false;
        }
    } catch (error) {
        console.log('❌ 连接错误:', error.message);
        return false;
    }
}

async function testInitUpload() {
    console.log('🚀 测试初始化分片上传...');
    try {
        const url = `${API_CONFIG.baseUrl}/api/v1/basic/upload/resumable/init`;
        const requestData = {
            originalFileName: 'test.txt',
            fileSize: 1024,
            contentType: 'text/plain',
            category: 'test',
            chunkSize: 5242880,
            md5: 'test-md5-hash'
        };

        const result = await makeRequest(url, {
            method: 'POST',
            body: JSON.stringify(requestData)
        });

        if (result.status === 200 && result.data && result.data.data) {
            console.log('✅ 初始化成功');
            console.log('Upload ID:', result.data.data.uploadId);
            return result.data.data.uploadId;
        } else {
            console.log('❌ 初始化失败:', result.status, result.statusText);
            console.log('响应数据:', result.data || result.rawData);
            return null;
        }
    } catch (error) {
        console.log('❌ 初始化错误:', error.message);
        return null;
    }
}

async function testPresignedUrl(uploadId) {
    console.log('🔗 测试获取预签名URL...');
    try {
        const url = `${API_CONFIG.baseUrl}/api/v1/basic/upload/resumable/presigned-url/${uploadId}/1?expiration=3600`;

        const result = await makeRequest(url, {
            method: 'GET'
        });

        if (result.status === 200) {
            console.log('✅ 获取预签名URL成功');
            return true;
        } else {
            console.log('❌ 获取预签名URL失败:', result.status, result.statusText);
            console.log('响应数据:', result.data || result.rawData);
            return false;
        }
    } catch (error) {
        console.log('❌ 获取预签名URL错误:', error.message);
        return false;
    }
}

async function testCompleteUpload(uploadId) {
    console.log('✅ 测试完成分片上传...');
    try {
        const url = `${API_CONFIG.baseUrl}/api/v1/basic/upload/resumable/complete/${uploadId}`;
        const requestData = [
            {
                partNumber: 1,
                etag: 'mock-etag-12345',
                size: 1024
            }
        ];

        const result = await makeRequest(url, {
            method: 'POST',
            body: JSON.stringify(requestData)
        });

        if (result.status === 200) {
            console.log('✅ 完成上传成功');
            console.log('文件路径:', result.data?.data?.relativePath);
            return true;
        } else {
            console.log('❌ 完成上传失败:', result.status, result.statusText);
            console.log('响应数据:', result.data || result.rawData);
            return false;
        }
    } catch (error) {
        console.log('❌ 完成上传错误:', error.message);
        return false;
    }
}

// 主测试流程
async function runTests() {
    console.log('🧪 开始OSS分片上传API测试');
    console.log('后端地址:', API_CONFIG.baseUrl);
    console.log('认证Token:', API_CONFIG.token.substring(0, 30) + '...');
    console.log('');

    // 1. 测试连接
    const connectionOk = await testConnection();
    if (!connectionOk) {
        console.log('');
        console.log('❌ 测试失败：无法连接到后端服务');
        console.log('请检查：');
        console.log('1. 后端服务是否启动 (http://localhost:30030)');
        console.log('2. CORS配置是否正确');
        console.log('3. 认证token是否有效');
        return;
    }

    console.log('');

    // 2. 测试初始化
    const uploadId = await testInitUpload();
    if (!uploadId) {
        console.log('');
        console.log('❌ 测试失败：无法初始化分片上传');
        return;
    }

    console.log('');

    // 3. 测试预签名URL
    const presignedOk = await testPresignedUrl(uploadId);
    if (!presignedOk) {
        console.log('');
        console.log('❌ 测试失败：无法获取预签名URL');
        return;
    }

    console.log('');

    // 4. 测试完成上传
    const completeOk = await testCompleteUpload(uploadId);
    if (!completeOk) {
        console.log('');
        console.log('❌ 测试失败：无法完成分片上传');
        return;
    }

    console.log('');
    console.log('🎉 所有测试通过！OSS分片上传API工作正常');
}

// 运行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { runTests, API_CONFIG };
