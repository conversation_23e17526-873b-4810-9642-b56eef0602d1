const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 开发服务器配置
  devServer: {
    port: 3000, // 前端开发服务器端口
    open: true, // 自动打开浏览器
    
    // 代理配置 - 解决跨域问题
    proxy: {
      // 代理所有 /api 开头的请求到后端服务器
      '/api': {
        target: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080',
        changeOrigin: true, // 改变请求头中的origin字段
        secure: false, // 如果是https接口，需要配置这个参数
        logLevel: 'debug', // 显示代理日志
        
        // 可选：重写路径
        // pathRewrite: {
        //   '^/api': '' // 如果后端不需要/api前缀，可以去掉
        // },
        
        // 可选：自定义请求头
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        },
        
        // 错误处理
        onError: (err, req, res) => {
          console.error('代理错误:', err)
        },
        
        // 代理响应处理
        onProxyRes: (proxyRes, req, res) => {
          // 添加CORS头
          proxyRes.headers['Access-Control-Allow-Origin'] = '*'
          proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
          proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        }
      }
    },
    
    // CORS配置
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  },
  
  // 生产环境配置
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  
  // 输出目录
  outputDir: 'dist',
  
  // 静态资源目录
  assetsDir: 'static',
  
  // 是否生成source map
  productionSourceMap: process.env.NODE_ENV !== 'production',
  
  // CSS配置
  css: {
    // 是否提取CSS到单独文件
    extract: process.env.NODE_ENV === 'production',
    // 是否生成CSS source map
    sourceMap: process.env.NODE_ENV !== 'production'
  },
  
  // Webpack配置
  configureWebpack: {
    // 开发环境配置
    ...(process.env.NODE_ENV === 'development' && {
      devtool: 'eval-source-map'
    }),
    
    // 生产环境配置
    ...(process.env.NODE_ENV === 'production' && {
      // 生产环境优化
      optimization: {
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              name: 'chunk-vendors',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
              chunks: 'initial'
            },
            common: {
              name: 'chunk-common',
              minChunks: 2,
              priority: 5,
              chunks: 'initial'
            }
          }
        }
      }
    })
  },
  
  // 链式配置
  chainWebpack: config => {
    // 设置别名
    config.resolve.alias
      .set('@', require('path').resolve(__dirname, 'src'))
      .set('@components', require('path').resolve(__dirname, 'src/components'))
      .set('@utils', require('path').resolve(__dirname, 'src/utils'))
    
    // 开发环境配置
    if (process.env.NODE_ENV === 'development') {
      // 开发环境下的特殊配置
      config.plugin('define').tap(args => {
        args[0]['process.env'].VUE_APP_BUILD_TIME = JSON.stringify(new Date().toISOString())
        return args
      })
    }
    
    // 生产环境配置
    if (process.env.NODE_ENV === 'production') {
      // 移除console.log
      config.optimization.minimizer('terser').tap(args => {
        args[0].terserOptions.compress.drop_console = true
        args[0].terserOptions.compress.drop_debugger = true
        return args
      })
    }
  },
  
  // 插件配置
  pluginOptions: {
    // 可以在这里配置各种插件
  }
}) 