# OSS分片上传前端组件

这是一个完整的OSS分片上传前端解决方案，等效于Java版本的`uploadChunkToOss`方法，支持大文件断点续传上传。

## 🚀 快速开始

### 1. 环境配置

#### 1.1 创建环境变量文件

复制 `env.example` 文件为 `.env.local` 或 `.env.development`：

```bash
# 复制环境变量配置文件
cp frontend/env.example frontend/.env.local
```

#### 1.2 配置后端API地址

编辑 `.env.local` 文件：

```env
# API基础URL - 后端服务地址
VUE_APP_API_BASE_URL=http://localhost:8080

# 是否启用认证
VUE_APP_ENABLE_AUTH=true

# 是否启用调试模式
VUE_APP_ENABLE_DEBUG=true
```

#### 1.3 启动后端服务

确保Java后端服务正在运行：

```bash
# 在项目根目录下启动后端服务
mvn spring-boot:run

# 或者使用IDE启动主类
# com.extracme.saas.autocare.Application
```

后端服务默认运行在 `http://localhost:8080`

#### 1.4 启动前端开发服务器

```bash
# 进入前端目录
cd frontend

# 安装依赖（如果还没有安装）
npm install

# 启动开发服务器
npm run serve
```

前端服务默认运行在 `http://localhost:3000`

### 2. 基本使用

#### 2.1 在Vue组件中使用

```vue
<template>
  <div>
    <SimpleOssUploader />
  </div>
</template>

<script>
import SimpleOssUploader from '@/components/SimpleOssUploader.vue'

export default {
  components: {
    SimpleOssUploader
  }
}
</script>
```

#### 2.2 编程式调用

```javascript
import { uploadFileWithResumable, setAuthToken } from '@/components/OssUploaderService.js'

// 设置认证Token（如果需要）
setAuthToken('your-jwt-token')

// 上传文件
async function uploadFile(file) {
  try {
    const result = await uploadFileWithResumable(file, {
      category: 'documents',
      chunkSize: 5 * 1024 * 1024, // 5MB
      concurrency: 3,
      onProgress: (progress) => {
        console.log(`上传进度: ${progress}%`)
      }
    })
    
    console.log('上传成功:', result.fullUrl)
  } catch (error) {
    console.error('上传失败:', error.message)
  }
}
```

## 🔧 配置说明

### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `VUE_APP_API_BASE_URL` | `http://localhost:8080` | 后端API基础URL |
| `VUE_APP_API_TIMEOUT` | `30000` | API请求超时时间（毫秒） |
| `VUE_APP_ENABLE_DEBUG` | `true` | 是否启用调试模式 |
| `VUE_APP_ENABLE_AUTH` | `true` | 是否启用认证 |
| `VUE_APP_DEFAULT_CHUNK_SIZE` | `5242880` | 默认分片大小（5MB） |
| `VUE_APP_MAX_FILE_SIZE` | `104857600` | 最大文件大小（100MB） |
| `VUE_APP_DEFAULT_CONCURRENCY` | `3` | 默认并发上传数 |

### 代理配置

开发环境下，前端使用代理解决跨域问题。配置文件：`frontend/vue.config.js`

```javascript
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false
      }
    }
  }
}
```

## 🔗 后端集成

### 1. 后端API端点

确保后端提供以下API端点：

| 方法 | 端点 | 说明 |
|------|------|------|
| POST | `/api/basic/upload/resumable/init` | 初始化分片上传 |
| GET | `/api/basic/upload/resumable/presigned-url/{uploadId}/{partNumber}` | 获取预签名URL |
| POST | `/api/basic/upload/resumable/complete/{uploadId}` | 完成分片上传 |
| GET | `/api/basic/upload/resumable/abort/{uploadId}` | 取消分片上传 |

### 2. 请求格式

#### 2.1 初始化分片上传

**请求：**
```json
POST /api/basic/upload/resumable/init
Content-Type: application/json
Authorization: Bearer <token>

{
  "originalFileName": "example.pdf",
  "fileSize": 10485760,
  "contentType": "application/pdf",
  "category": "documents",
  "md5": "d41d8cd98f00b204e9800998ecf8427e",
  "chunkSize": 5242880
}
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "uploadId": "upload-session-123",
    "relativePath": "test/tenant_1/uploads/2024-01/documents/abc123.pdf",
    "bucketName": "evcard",
    "suggestedPartSize": 5242880,
    "maxPartCount": 10000,
    "timestamp": 1704067200000
  }
}
```

#### 2.2 获取预签名URL

**请求：**
```
GET /api/basic/upload/resumable/presigned-url/upload-session-123/1?expiration=3600
Authorization: Bearer <token>
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": "https://evcard.oss-cn-shanghai.aliyuncs.com/test/tenant_1/uploads/2024-01/documents/abc123.pdf?uploadId=real-oss-upload-id&partNumber=1&..."
}
```

#### 2.3 完成分片上传

**请求：**
```json
POST /api/basic/upload/resumable/complete/upload-session-123
Content-Type: application/json
Authorization: Bearer <token>

[
  {
    "partNumber": 1,
    "etag": "d41d8cd98f00b204e9800998ecf8427e",
    "partSize": 5242880
  },
  {
    "partNumber": 2,
    "etag": "098f6bcd4621d373cade4e832627b4f6",
    "partSize": 5242880
  }
]
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "relativePath": "test/tenant_1/uploads/2024-01/documents/abc123.pdf",
    "fullUrl": "https://evcard.oss-cn-shanghai.aliyuncs.com/test/tenant_1/uploads/2024-01/documents/abc123.pdf",
    "bucketName": "evcard",
    "etag": "final-file-etag",
    "totalSize": 10485760,
    "totalParts": 2,
    "tenantCode": "tenant_1"
  }
}
```

### 3. 认证配置

#### 3.1 JWT Token

前端通过以下方式设置认证Token：

```javascript
import { setAuthToken } from '@/components/OssUploaderService.js'

// 设置Token
setAuthToken('your-jwt-token')

// 或者直接存储到localStorage
localStorage.setItem('token', 'your-jwt-token')
```

#### 3.2 请求头格式

```
Authorization: Bearer <jwt-token>
```

### 4. CORS配置

确保后端配置了正确的CORS设置：

```java
@Configuration
public class CorsConfig {
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
```

## 🛠️ 开发调试

### 1. 启用调试模式

在 `.env.local` 中设置：

```env
VUE_APP_ENABLE_DEBUG=true
```

### 2. 查看网络请求

打开浏览器开发者工具，在Network标签页中可以看到：

- 初始化请求：`POST /api/basic/upload/resumable/init`
- 预签名URL请求：`GET /api/basic/upload/resumable/presigned-url/...`
- OSS上传请求：`PUT https://bucket.oss-region.aliyuncs.com/...`
- 完成请求：`POST /api/basic/upload/resumable/complete/...`

### 3. 查看详细日志

在组件中启用"显示详细日志"选项，可以看到：

- 文件选择和验证日志
- API请求和响应日志
- 分片上传进度日志
- 错误分析日志

### 4. 常见问题排查

#### 4.1 跨域问题

**症状：** 控制台出现CORS错误

**解决方案：**
1. 确保后端配置了正确的CORS设置
2. 检查 `vue.config.js` 中的代理配置
3. 确认前端请求的URL格式正确

#### 4.2 认证失败

**症状：** 返回401或403错误

**解决方案：**
1. 检查Token是否正确设置
2. 确认Token格式：`Bearer <token>`
3. 检查Token是否过期

#### 4.3 文件上传失败

**症状：** OSS返回403错误

**解决方案：**
1. 检查预签名URL是否正确
2. 确认OSS配置和权限
3. 查看详细的403错误分析日志

## 📊 功能对比

| 功能 | Java版本 | JavaScript版本 | 说明 |
|------|----------|----------------|------|
| HTTP请求 | HttpURLConnection | fetch API / XMLHttpRequest | ✅ 功能等效 |
| 进度监控 | ❌ | ✅ | 前端增强功能 |
| 错误处理 | ✅ | ✅ | ✅ 完全一致 |
| 403错误分析 | ✅ | ✅ | ✅ 完全一致 |
| 超时控制 | ✅ | ✅ | ✅ 功能等效 |
| ETag提取 | ✅ | ✅ | ✅ 完全一致 |
| 日志记录 | ✅ | ✅ | ✅ 功能等效 |
| 并发控制 | ❌ | ✅ | 前端增强功能 |
| 断点续传 | ✅ | ✅ | ✅ 功能等效 |
| 文件验证 | ✅ | ✅ | ✅ 功能等效 |

## 🔧 高级配置

### 1. 自定义API配置

```javascript
import { setApiConfig } from '@/components/OssUploaderService.js'

setApiConfig({
  baseURL: 'https://your-api-domain.com',
  timeout: 60000,
  endpoints: {
    initUpload: '/custom/upload/init',
    presignedUrl: '/custom/upload/presigned',
    completeUpload: '/custom/upload/complete',
    abortUpload: '/custom/upload/abort'
  }
})
```

### 2. 自定义上传参数

```javascript
const result = await uploadFileWithResumable(file, {
  category: 'images',
  chunkSize: 10 * 1024 * 1024, // 10MB分片
  concurrency: 5, // 5个并发
  onProgress: (progress) => {
    console.log(`总进度: ${progress}%`)
  },
  onChunkProgress: (partNumber, percentage) => {
    console.log(`分片${partNumber}: ${percentage}%`)
  }
})
```

### 3. 错误处理

```javascript
try {
  const result = await uploadFileWithResumable(file)
} catch (error) {
  if (error.message.includes('403')) {
    console.error('权限错误，请检查OSS配置')
  } else if (error.message.includes('网络')) {
    console.error('网络错误，请检查连接')
  } else {
    console.error('未知错误:', error.message)
  }
}
```

## 📱 浏览器兼容性

| 浏览器 | 版本要求 | 支持状态 |
|--------|----------|----------|
| Chrome | 60+ | ✅ 完全支持 |
| Firefox | 55+ | ✅ 完全支持 |
| Safari | 12+ | ✅ 完全支持 |
| Edge | 79+ | ✅ 完全支持 |
| IE | 11 | ⚠️ 需要polyfill |

### Polyfill配置

对于旧版浏览器，需要添加以下polyfill：

```javascript
// 在main.js中添加
import 'core-js/stable'
import 'regenerator-runtime/runtime'
```

## 🚀 性能优化

### 1. 分片大小优化

- **小文件（< 10MB）**：建议1-2MB分片
- **中等文件（10-100MB）**：建议5MB分片
- **大文件（> 100MB）**：建议10MB分片

### 2. 并发数优化

- **慢速网络**：建议1-2个并发
- **普通网络**：建议3个并发
- **高速网络**：建议5个并发

### 3. 内存优化

- 使用流式读取，避免一次性加载整个文件
- 及时释放已上传分片的内存
- 限制同时处理的分片数量

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请联系开发团队或查看项目文档。 