<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSS分片上传示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        
        .upload-area:hover {
            border-color: #007bff;
        }
        
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .progress-section {
            margin: 20px 0;
        }
        
        .progress-item {
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s;
        }
        
        .progress-fill.success {
            background: #28a745;
        }
        
        .progress-fill.error {
            background: #dc3545;
        }
        
        .logs {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-item {
            margin-bottom: 5px;
        }
        
        .log-item.error {
            color: #dc3545;
        }
        
        .log-item.warn {
            color: #ffc107;
        }
        
        .log-item.info {
            color: #007bff;
        }
        
        .log-item.debug {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>OSS分片上传示例</h1>
    <p>这个示例展示了如何调用后端OSS分片上传服务，支持大文件的分片上传、进度显示和断点续传。</p>

    <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 15px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 10px 0; color: #155724;">✅ 认证配置已完成</h4>
        <p style="margin: 0; color: #155724;">
            已配置测试用JWT token，可以直接使用。<br>
            <strong>后端地址：</strong> <code style="background: #f8f9fa; padding: 2px 4px; border-radius: 2px;">http://localhost:30030/auto-care-saas</code><br>
            <strong>认证状态：</strong> <span style="color: #28a745;">✓ 已配置</span>
        </p>
    </div>
    
    <div class="upload-area" id="uploadArea">
        <input type="file" id="fileInput" style="display: none">
        <button onclick="selectFile()">选择文件</button>
        <p>或拖拽文件到此处</p>
    </div>
    
    <div>
        <button onclick="demonstrateChunkUpload()">测试后端连接</button>
        <button onclick="cancelUpload()" id="cancelBtn" style="background: #dc3545; display: none;">取消上传</button>
        <button onclick="clearLogs()">清空日志</button>
        <button onclick="clearProgress()">清空进度</button>
    </div>
    
    <div class="progress-section" id="progressSection" style="display: none;">
        <h3>上传进度</h3>
        <div id="progressContainer"></div>
    </div>
    
    <div>
        <h3>上传日志</h3>
        <div class="logs" id="logs"></div>
    </div>

    <script type="module">
        // 后端API配置
        const API_CONFIG = {
            baseUrl: 'http://localhost:30030/auto-care-saas',
            endpoints: {
                initMultipart: '/api/v1/basic/upload/resumable/init',
                getPresignedUrl: '/api/v1/basic/upload/resumable/presigned-url',
                completeMultipart: '/api/v1/basic/upload/resumable/complete'
            }
        }

        // 获取认证token（硬编码用于测试）
        function getAuthToken() {
            // 硬编码的JWT token用于测试
            return 'Bearer eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDkyNjM3ODEsInVzZXJJZCI6IjIiLCJpYXQiOjE3NDkxNzczODF9.J2aiO3poRFfCWAUTcTBlddgAXu4Bojh7VpaB7p8Hnpw'
        }

        // OSS分片上传服务

        // HTTP请求工具
        const HttpClient = {
            async request(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': getAuthToken(),
                        ...options.headers
                    }
                }

                const response = await fetch(url, { ...defaultOptions, ...options })

                if (!response.ok) {
                    const errorText = await response.text()
                    throw new Error(`HTTP ${response.status}: ${errorText}`)
                }

                return response.json()
            },

            async uploadToPresignedUrl(presignedUrl, data, onProgress) {
                return new Promise((resolve, reject) => {
                    const xhr = new XMLHttpRequest()

                    // 记录请求开始时间，用于检测时间偏差
                    const requestStartTime = new Date()
                    addLog('debug', `请求开始时间: ${requestStartTime.toISOString()}`)

                    // 上传进度监听
                    xhr.upload.addEventListener('progress', (event) => {
                        if (event.lengthComputable && onProgress) {
                            const percentage = Math.round((event.loaded / event.total) * 100)
                            onProgress(percentage)
                        }
                    })

                    xhr.addEventListener('load', () => {
                        const requestEndTime = new Date()
                        addLog('debug', `请求结束时间: ${requestEndTime.toISOString()}`)
                        addLog('debug', `请求耗时: ${requestEndTime - requestStartTime}ms`)

                        if (xhr.status >= 200 && xhr.status < 300) {
                            const etag = xhr.getResponseHeader('ETag')
                            addLog('debug', `上传成功，ETag: ${etag}`)
                            resolve({ etag: etag ? etag.replace(/"/g, '') : null })
                        } else {
                            // 详细的错误信息记录
                            const errorDetails = {
                                status: xhr.status,
                                statusText: xhr.statusText,
                                responseText: xhr.responseText,
                                responseHeaders: xhr.getAllResponseHeaders(),
                                requestTime: requestStartTime.toISOString(),
                                responseTime: requestEndTime.toISOString()
                            }

                            addLog('error', `OSS上传失败详情: ${JSON.stringify(errorDetails, null, 2)}`)

                            // 特殊处理403错误
                            if (xhr.status === 403) {
                                this.analyzeOssSignatureError(xhr.responseText, presignedUrl, requestStartTime)
                            }

                            reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`))
                        }
                    })

                    xhr.addEventListener('error', () => {
                        addLog('error', '网络错误或CORS问题')
                        reject(new Error('Upload failed: Network error'))
                    })

                    xhr.addEventListener('timeout', () => {
                        addLog('error', '请求超时')
                        reject(new Error('Upload failed: Request timeout'))
                    })

                    // 严格按照后端集成测试的方式配置请求
                    xhr.open('PUT', presignedUrl)

                    // 设置超时时间
                    xhr.timeout = 60000 // 60秒

                    // 重要：不设置任何自定义请求头，让OSS预签名URL处理认证
                    // 后端已经在生成预签名URL时处理了Content-Type匹配问题

                    addLog('debug', `开始上传到OSS，数据大小: ${data.size} bytes`)

                    xhr.send(data)
                })
            },


        }



        // OSS分片上传服务
        const OssService = {
            async initMultipartUpload(file, category = 'upload') {
                const url = `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.initMultipart}`

                const requestData = {
                    originalFileName: file.name,
                    fileSize: file.size,
                    contentType: file.type || 'application/octet-stream',
                    category: category,
                    chunkSize: 5 * 1024 * 1024, // 5MB
                    md5: await this.calculateMD5(file)
                }

                addLog('info', `初始化分片上传: ${file.name}`)
                addLog('debug', `请求数据: ${JSON.stringify(requestData)}`)

                const response = await HttpClient.request(url, {
                    method: 'POST',
                    body: JSON.stringify(requestData)
                })

                addLog('info', `初始化成功，上传ID: ${response.data.uploadId}`)
                return response.data
            },

            async getPresignedUrl(uploadId, partNumber, expiration = 3600) {
                // 根据BasicController的接口定义，使用路径参数
                const url = `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.getPresignedUrl}/${uploadId}/${partNumber}?expiration=${expiration}`

                addLog('debug', `获取预签名URL: 分片${partNumber}`)

                const response = await HttpClient.request(url, {
                    method: 'GET'
                })

                return response.data
            },

            async completeMultipartUpload(uploadId, parts) {
                // 根据BasicController的接口定义，使用路径参数
                const url = `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.completeMultipart}/${uploadId}`

                // 根据后端接口定义，直接传递parts数组
                const requestData = parts

                addLog('info', `完成分片上传，共${parts.length}个分片`)
                addLog('debug', `请求数据: ${JSON.stringify(requestData)}`)

                const response = await HttpClient.request(url, {
                    method: 'POST',
                    body: JSON.stringify(requestData)
                })

                addLog('info', `上传完成: ${response.data.relativePath}`)
                return response.data
            },

            splitFileIntoChunks(file, chunkSize = 5 * 1024 * 1024) {
                const chunks = []

                for (let i = 0; i < file.size; i += chunkSize) {
                    const chunk = file.slice(i, i + chunkSize)
                    chunks.push({
                        partNumber: Math.floor(i / chunkSize) + 1,
                        data: chunk,
                        size: chunk.size,
                        start: i,
                        end: Math.min(i + chunkSize, file.size)
                    })
                }

                return chunks
            },

            async calculateMD5(file) {
                // 简化版MD5计算，实际项目中可以使用crypto-js等库
                return new Promise((resolve) => {
                    const reader = new FileReader()
                    reader.onload = () => {
                        // 这里简化处理，实际应该计算真实的MD5
                        const hash = btoa(file.name + file.size + file.lastModified).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32)
                        resolve(hash)
                    }
                    reader.readAsArrayBuffer(file)
                })
            },

            async uploadChunkToOss(presignedUrl, data, partNumber, onProgress) {
                addLog('debug', `开始上传分片${partNumber}到OSS`)
                addLog('debug', `预签名URL: ${presignedUrl}`)
                addLog('debug', `数据大小: ${data.size} bytes`)

                try {
                    const result = await HttpClient.uploadToPresignedUrl(presignedUrl, data, onProgress)
                    addLog('debug', `分片${partNumber}上传成功，ETag: ${result.etag}`)

                    return {
                        partNumber: partNumber,
                        etag: result.etag,
                        size: data.size
                    }
                } catch (error) {
                    addLog('error', `分片${partNumber}上传失败: ${error.message}`)

                    // 如果是403错误，提供详细的调试信息
                    if (error.message.includes('403')) {
                        addLog('error', '=== 403错误调试信息 ===')
                        addLog('error', `预签名URL: ${presignedUrl}`)
                        addLog('error', '可能的原因:')
                        addLog('error', '1. JWT token已过期或无效')
                        addLog('error', '2. 租户权限不足')
                        addLog('error', '3. OSS配置错误')
                        addLog('error', '4. 预签名URL已过期')
                        addLog('error', '5. CORS配置问题')
                        addLog('error', '=== 调试信息结束 ===')
                    }

                    throw error
                }
            },


        }

        // 全局变量
        let uploadProgress = []
        let uploading = false
        let currentUpload = null
        let uploadStartTime = null

        // 文件选择
        window.selectFile = function() {
            if (uploading) {
                addLog('warn', '正在上传中，请等待当前上传完成')
                return
            }
            document.getElementById('fileInput').click()
        }

        // 取消上传
        window.cancelUpload = function() {
            if (currentUpload && currentUpload.cancel) {
                currentUpload.cancel()
                addLog('warn', '用户取消上传')
            }
        }

        // 文件输入事件
        document.getElementById('fileInput').addEventListener('change', function(event) {
            if (event.target.files.length > 0) {
                processFile(event.target.files[0])
            }
        })

        // 拖拽事件
        const uploadArea = document.getElementById('uploadArea')

        uploadArea.addEventListener('dragover', function(event) {
            event.preventDefault()
            uploadArea.classList.add('dragover')
        })

        uploadArea.addEventListener('dragleave', function(event) {
            event.preventDefault()
            uploadArea.classList.remove('dragover')
        })

        uploadArea.addEventListener('drop', function(event) {
            event.preventDefault()
            uploadArea.classList.remove('dragover')

            if (event.dataTransfer.files.length > 0) {
                processFile(event.dataTransfer.files[0])
            }
        })

        // 处理文件
        async function processFile(file) {
            if (uploading) {
                addLog('warn', '正在上传中，请等待当前上传完成')
                return
            }

            addLog('info', `开始处理文件: ${file.name}, 大小: ${formatFileSize(file.size)}`)

            try {
                uploading = true
                uploadStartTime = Date.now()

                // 显示取消按钮
                document.getElementById('cancelBtn').style.display = 'inline-block'

                // 创建取消控制器
                const abortController = new AbortController()
                currentUpload = { cancel: () => abortController.abort() }

                // 1. 初始化分片上传
                const initResult = await OssService.initMultipartUpload(file)
                const uploadId = initResult.uploadId

                // 2. 将文件分成多个分片
                const chunkSize = 5 * 1024 * 1024 // 5MB
                const chunks = OssService.splitFileIntoChunks(file, chunkSize)

                addLog('info', `文件分片完成，共${chunks.length}个分片，每片${formatFileSize(chunkSize)}`)

                // 3. 初始化进度跟踪
                uploadProgress = chunks.map(chunk => ({
                    partNumber: chunk.partNumber,
                    percentage: 0,
                    completed: false,
                    error: false,
                    speed: 0,
                    startTime: null
                }))

                updateProgressDisplay()

                // 4. 执行分片上传
                const uploadedParts = await uploadChunks(uploadId, chunks, abortController.signal)

                // 5. 完成分片上传
                const result = await OssService.completeMultipartUpload(uploadId, uploadedParts)

                addLog('info', `✅ 文件上传成功！`)
                addLog('info', `📁 文件路径: ${result.relativePath}`)
                addLog('info', `📊 总大小: ${formatFileSize(result.totalSize)}`)
                addLog('info', `⏱️ 总耗时: ${formatDuration(Date.now() - uploadStartTime)}`)

            } catch (error) {
                if (error.name === 'AbortError') {
                    addLog('warn', '上传已取消')
                } else {
                    addLog('error', `文件上传失败: ${error.message}`)
                }
            } finally {
                uploading = false
                currentUpload = null
                // 隐藏取消按钮
                document.getElementById('cancelBtn').style.display = 'none'
            }
        }

        // 上传分片
        async function uploadChunks(uploadId, chunks, signal) {
            addLog('info', '开始上传分片...')

            const uploadedParts = []
            const concurrency = 3 // 并发上传数量

            // 分批并发上传
            for (let i = 0; i < chunks.length; i += concurrency) {
                const batch = chunks.slice(i, i + concurrency)

                const batchPromises = batch.map(async (chunk) => {
                    if (signal.aborted) {
                        throw new Error('Upload cancelled')
                    }

                    const progressIndex = uploadProgress.findIndex(p => p.partNumber === chunk.partNumber)

                    try {
                        // 记录开始时间
                        if (progressIndex >= 0) {
                            uploadProgress[progressIndex].startTime = Date.now()
                        }

                        // 获取预签名URL
                        const presignedUrl = await OssService.getPresignedUrl(uploadId, chunk.partNumber)

                        // 上传分片
                        const result = await OssService.uploadChunkToOss(
                            presignedUrl,
                            chunk.data,
                            chunk.partNumber,
                            (percentage) => {
                                if (progressIndex >= 0) {
                                    uploadProgress[progressIndex].percentage = percentage

                                    // 计算上传速度
                                    if (uploadProgress[progressIndex].startTime) {
                                        const elapsed = (Date.now() - uploadProgress[progressIndex].startTime) / 1000
                                        const uploaded = (percentage / 100) * chunk.size
                                        uploadProgress[progressIndex].speed = uploaded / elapsed
                                    }

                                    updateProgressDisplay()
                                }
                            }
                        )

                        // 标记完成
                        if (progressIndex >= 0) {
                            uploadProgress[progressIndex].completed = true
                            uploadProgress[progressIndex].percentage = 100
                            updateProgressDisplay()
                        }

                        addLog('info', `✅ 分片 ${chunk.partNumber} 上传完成`)

                        return result

                    } catch (error) {
                        if (progressIndex >= 0) {
                            uploadProgress[progressIndex].error = true
                            uploadProgress[progressIndex].errorMessage = error.message
                            updateProgressDisplay()
                        }
                        addLog('error', `❌ 分片 ${chunk.partNumber} 上传失败: ${error.message}`)
                        throw error
                    }
                })

                // 等待当前批次完成
                const batchResults = await Promise.all(batchPromises)
                uploadedParts.push(...batchResults)

                addLog('info', `批次 ${Math.floor(i / concurrency) + 1} 完成，已上传 ${uploadedParts.length}/${chunks.length} 个分片`)
            }

            return uploadedParts
        }

        // 测试连接
        window.demonstrateChunkUpload = async function() {
            try {
                addLog('info', '开始测试后端连接...')

                // 测试创建一个小文件
                const testData = new Blob(['这是一个测试文件的内容，用于验证后端API连接'], { type: 'text/plain' })
                const testFile = new File([testData], 'test.txt', { type: 'text/plain' })

                // 1. 测试初始化接口
                addLog('info', '步骤1: 测试初始化接口...')
                const initResult = await OssService.initMultipartUpload(testFile, 'test')
                addLog('info', `✅ 初始化接口测试成功，上传ID: ${initResult.uploadId}`)

                // 2. 测试获取预签名URL接口
                addLog('info', '步骤2: 测试获取预签名URL接口...')
                const presignedUrl = await OssService.getPresignedUrl(initResult.uploadId, 1)
                addLog('info', `✅ 预签名URL接口测试成功`)

                // 3. 测试上传分片
                addLog('info', '步骤3: 测试分片上传...')
                const uploadResult = await OssService.uploadChunkToOss(
                    presignedUrl,
                    testData,
                    1,
                    (percentage) => {
                        addLog('debug', `测试上传进度: ${percentage}%`)
                    }
                )
                addLog('info', `✅ 分片上传测试成功，ETag: ${uploadResult.etag}`)

                // 4. 测试完成上传
                addLog('info', '步骤4: 测试完成上传...')
                const completeResult = await OssService.completeMultipartUpload(initResult.uploadId, [uploadResult])
                addLog('info', `✅ 完成上传测试成功，文件路径: ${completeResult.relativePath}`)

                addLog('info', '🎉 所有接口测试通过！')

            } catch (error) {
                addLog('error', `❌ 接口测试失败: ${error.message}`)
                addLog('error', '请检查：')
                addLog('error', '1. 后端服务是否启动 (http://localhost:30030)')
                addLog('error', '2. 认证token是否正确')
                addLog('error', '3. 网络连接是否正常')
                addLog('error', '4. CORS配置是否正确')
            }
        }

        // 更新进度显示
        function updateProgressDisplay() {
            const progressSection = document.getElementById('progressSection')
            const progressContainer = document.getElementById('progressContainer')

            if (uploadProgress.length > 0) {
                progressSection.style.display = 'block'

                // 计算总体进度
                const totalProgress = uploadProgress.reduce((sum, p) => sum + p.percentage, 0) / uploadProgress.length
                const completedCount = uploadProgress.filter(p => p.completed).length
                const errorCount = uploadProgress.filter(p => p.error).length

                // 计算平均速度
                const avgSpeed = uploadProgress
                    .filter(p => p.speed > 0)
                    .reduce((sum, p, _, arr) => sum + p.speed / arr.length, 0)

                // 估算剩余时间
                const remainingProgress = 100 - totalProgress
                const eta = avgSpeed > 0 ? (remainingProgress / 100) * uploadProgress.length * (5 * 1024 * 1024) / avgSpeed : 0

                progressContainer.innerHTML = `
                    <div class="progress-item" style="background: #f8f9fa; border: 2px solid #007bff;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px; font-weight: bold;">
                            <span>总体进度</span>
                            <span>${totalProgress.toFixed(1)}% (${completedCount}/${uploadProgress.length})</span>
                        </div>
                        <div class="progress-bar" style="height: 12px;">
                            <div class="progress-fill ${totalProgress === 100 ? 'success' : ''}"
                                 style="width: ${totalProgress}%"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 12px; margin-top: 5px; color: #666;">
                            <span>速度: ${formatFileSize(avgSpeed)}/s</span>
                            <span>预计剩余: ${eta > 0 ? formatDuration(eta * 1000) : '--'}</span>
                            ${errorCount > 0 ? `<span style="color: #dc3545;">错误: ${errorCount}</span>` : ''}
                        </div>
                    </div>
                    ${uploadProgress.map(progress => `
                        <div class="progress-item">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>分片 ${progress.partNumber}</span>
                                <span>${progress.percentage}% ${progress.speed > 0 ? `(${formatFileSize(progress.speed)}/s)` : ''}</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill ${progress.completed ? 'success' : ''} ${progress.error ? 'error' : ''}"
                                     style="width: ${progress.percentage}%"></div>
                            </div>
                            ${progress.error ? `<div style="color: #dc3545; font-size: 12px; margin-top: 5px;">${progress.errorMessage}</div>` : ''}
                        </div>
                    `).join('')}
                `
            } else {
                progressSection.style.display = 'none'
            }
        }

        // 工具函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B'
            const k = 1024
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
            const i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        }

        function formatDuration(ms) {
            const seconds = Math.floor(ms / 1000)
            const minutes = Math.floor(seconds / 60)
            const hours = Math.floor(minutes / 60)

            if (hours > 0) {
                return `${hours}h ${minutes % 60}m ${seconds % 60}s`
            } else if (minutes > 0) {
                return `${minutes}m ${seconds % 60}s`
            } else {
                return `${seconds}s`
            }
        }

        // 添加日志
        function addLog(level, message) {
            const timestamp = new Date().toLocaleTimeString()
            const logs = document.getElementById('logs')

            const logItem = document.createElement('div')
            logItem.className = `log-item ${level}`
            logItem.innerHTML = `<span style="color: #666; margin-right: 10px;">[${timestamp}]</span>${message}`

            logs.appendChild(logItem)
            logs.scrollTop = logs.scrollHeight

            // 控制台输出
            console[level](`[${timestamp}] ${message}`)
        }

        // 清空日志
        window.clearLogs = function() {
            document.getElementById('logs').innerHTML = ''
        }

        // 清空进度
        window.clearProgress = function() {
            if (uploading) {
                addLog('warn', '正在上传中，无法清空进度')
                return
            }
            uploadProgress = []
            updateProgressDisplay()
        }

        // 初始化日志
        addLog('info', '🚀 OSS分片上传示例已加载')
        addLog('info', '📋 配置信息:')
        addLog('info', `   - 后端地址: ${API_CONFIG.baseUrl}`)
        addLog('info', `   - 初始化接口: ${API_CONFIG.endpoints.initMultipart}`)
        addLog('info', `   - 预签名URL接口: ${API_CONFIG.endpoints.getPresignedUrl}`)
        addLog('info', `   - 完成上传接口: ${API_CONFIG.endpoints.completeMultipart}`)
        addLog('info', `   - 分片大小: 5MB`)
        addLog('info', `   - 并发数量: 3`)
        addLog('info', '💡 使用说明:')
        addLog('info', '   1. 点击"测试后端连接"验证API可用性')
        addLog('info', '   2. 选择文件或拖拽文件开始上传')
        addLog('info', '   3. 上传过程中可以点击取消按钮停止上传')
    </script>
</body>
</html> 