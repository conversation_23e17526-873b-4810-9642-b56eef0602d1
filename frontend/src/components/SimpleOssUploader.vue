<template>
  <div class="oss-uploader">
    <div class="upload-area" 
         :class="{ 'drag-over': isDragOver }"
         @drop="handleDrop"
         @dragover.prevent="isDragOver = true"
         @dragleave="isDragOver = false"
         @click="selectFile">
      <div class="upload-icon">📁</div>
      <div class="upload-text">
        <p>点击选择文件或拖拽文件到此处</p>
        <p class="upload-hint">支持 {{ allowedExtensions.join(', ') }} 格式，最大 {{ maxFileSizeText }}</p>
      </div>
      <input ref="fileInput" 
             type="file" 
             style="display: none" 
             :accept="acceptTypes"
             @change="handleFileSelect">
    </div>

    <!-- 上传进度 -->
    <div v-if="uploadState.isUploading" class="upload-progress">
      <div class="progress-header">
        <span>{{ uploadState.fileName }}</span>
        <span>{{ uploadState.progress }}%</span>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: uploadState.progress + '%' }"></div>
      </div>
      <div class="progress-details">
        <span>{{ formatFileSize(uploadState.uploadedSize) }} / {{ formatFileSize(uploadState.totalSize) }}</span>
        <span>分片: {{ uploadState.completedChunks }} / {{ uploadState.totalChunks }}</span>
      </div>
      <button @click="cancelUpload" class="cancel-btn">取消上传</button>
    </div>

    <!-- 上传结果 -->
    <div v-if="uploadState.result" class="upload-result">
      <div class="result-success">
        <span class="success-icon">✅</span>
        <span>上传成功！</span>
      </div>
      <div class="result-details">
        <p><strong>文件名:</strong> {{ uploadState.result.originalFileName }}</p>
        <p><strong>大小:</strong> {{ formatFileSize(uploadState.result.totalSize) }}</p>
        <p><strong>分片数:</strong> {{ uploadState.result.totalChunks }}</p>
        <p><strong>URL:</strong> <a :href="uploadState.result.fullUrl" target="_blank">{{ uploadState.result.fullUrl }}</a></p>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="uploadState.error" class="upload-error">
      <div class="error-header">
        <span class="error-icon">❌</span>
        <span>上传失败</span>
      </div>
      <div class="error-message">{{ uploadState.error }}</div>
      <button @click="clearError" class="retry-btn">重新上传</button>
    </div>

    <!-- 日志区域 -->
    <div v-if="showLogs" class="log-area">
      <div class="log-header">
        <span>上传日志</span>
        <button @click="clearLogs" class="clear-logs-btn">清空日志</button>
      </div>
      <div class="log-content" ref="logContent">
        <div v-for="(log, index) in logs" :key="index" :class="'log-' + log.level">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>

    <!-- 配置面板 -->
    <div class="config-panel">
      <h3>上传配置</h3>
      <div class="config-item">
        <label>分片大小:</label>
        <select v-model="config.chunkSize">
          <option :value="1024 * 1024">1MB</option>
          <option :value="2 * 1024 * 1024">2MB</option>
          <option :value="5 * 1024 * 1024">5MB</option>
          <option :value="10 * 1024 * 1024">10MB</option>
        </select>
      </div>
      <div class="config-item">
        <label>并发数:</label>
        <select v-model="config.concurrency">
          <option :value="1">1</option>
          <option :value="2">2</option>
          <option :value="3">3</option>
          <option :value="5">5</option>
        </select>
      </div>
      <div class="config-item">
        <label>文件分类:</label>
        <input v-model="config.category" placeholder="例如: documents" />
      </div>
      <div class="config-item">
        <label>
          <input type="checkbox" v-model="showLogs" />
          显示详细日志
        </label>
      </div>
      <div class="config-item">
        <label>API地址:</label>
        <input v-model="config.apiBaseUrl" placeholder="例如: http://localhost:8080" />
      </div>
      <div class="config-item">
        <label>认证Token:</label>
        <input v-model="config.authToken" type="password" placeholder="Bearer token" />
      </div>
    </div>
  </div>
</template>

<script>
import { 
  uploadFileWithResumable, 
  setApiConfig, 
  setAuthToken,
  abortMultipartUpload 
} from './OssUploaderService.js'
import { 
  getConfig, 
  formatFileSize, 
  validateFileType, 
  validateFileSize,
  REQUEST_CONFIG 
} from './config.js'

export default {
  name: 'SimpleOssUploader',
  data() {
    return {
      // 拖拽状态
      isDragOver: false,
      
      // 上传状态
      uploadState: {
        isUploading: false,
        fileName: '',
        progress: 0,
        uploadedSize: 0,
        totalSize: 0,
        completedChunks: 0,
        totalChunks: 0,
        result: null,
        error: null,
        uploadId: null
      },
      
      // 配置
      config: {
        chunkSize: REQUEST_CONFIG.UPLOAD.DEFAULT_CHUNK_SIZE,
        concurrency: REQUEST_CONFIG.UPLOAD.DEFAULT_CONCURRENCY,
        category: '',
        apiBaseUrl: getConfig().API_BASE_URL,
        authToken: ''
      },
      
      // 日志
      logs: [],
      showLogs: false,
      
      // 文件限制
      allowedExtensions: REQUEST_CONFIG.UPLOAD.ALLOWED_EXTENSIONS,
      maxFileSize: REQUEST_CONFIG.UPLOAD.MAX_FILE_SIZE
    }
  },
  
  computed: {
    acceptTypes() {
      return REQUEST_CONFIG.UPLOAD.ALLOWED_TYPES.join(',')
    },
    
    maxFileSizeText() {
      return formatFileSize(this.maxFileSize)
    }
  },
  
  mounted() {
    this.initializeConfig()
  },
  
  methods: {
    /**
     * 初始化配置
     */
    initializeConfig() {
      // 设置API配置
      this.updateApiConfig()
      
      // 从localStorage恢复配置
      const savedConfig = localStorage.getItem('ossUploaderConfig')
      if (savedConfig) {
        try {
          const parsed = JSON.parse(savedConfig)
          Object.assign(this.config, parsed)
        } catch (error) {
          console.warn('恢复配置失败:', error)
        }
      }
      
      this.addLog('info', '上传组件初始化完成')
    },
    
    /**
     * 更新API配置
     */
    updateApiConfig() {
      setApiConfig({
        baseURL: this.config.apiBaseUrl,
        endpoints: {
          initUpload: '/api/basic/upload/resumable/init',
          presignedUrl: '/api/basic/upload/resumable/presigned-url',
          completeUpload: '/api/basic/upload/resumable/complete',
          abortUpload: '/api/basic/upload/resumable/abort'
        }
      })
      
      if (this.config.authToken) {
        setAuthToken(this.config.authToken)
      }
    },
    
    /**
     * 选择文件
     */
    selectFile() {
      this.$refs.fileInput.click()
    },
    
    /**
     * 处理文件选择
     */
    handleFileSelect(event) {
      const files = event.target.files
      if (files && files.length > 0) {
        this.handleFile(files[0])
      }
    },
    
    /**
     * 处理拖拽放置
     */
    handleDrop(event) {
      event.preventDefault()
      this.isDragOver = false
      
      const files = event.dataTransfer.files
      if (files && files.length > 0) {
        this.handleFile(files[0])
      }
    },
    
    /**
     * 处理文件
     */
    async handleFile(file) {
      this.addLog('info', `选择文件: ${file.name}, 大小: ${formatFileSize(file.size)}`)
      
      // 验证文件类型
      if (!validateFileType(file)) {
        const error = `不支持的文件类型: ${file.type || '未知'}`
        this.setError(error)
        this.addLog('error', error)
        return
      }
      
      // 验证文件大小
      if (!validateFileSize(file)) {
        const error = `文件大小超过限制: ${formatFileSize(file.size)} > ${this.maxFileSizeText}`
        this.setError(error)
        this.addLog('error', error)
        return
      }
      
      // 保存配置
      this.saveConfig()
      
      // 开始上传
      await this.startUpload(file)
    },
    
    /**
     * 开始上传
     */
    async startUpload(file) {
      try {
        // 重置状态
        this.resetUploadState()
        this.uploadState.isUploading = true
        this.uploadState.fileName = file.name
        this.uploadState.totalSize = file.size
        
        this.addLog('info', '开始上传文件...')
        
        // 更新API配置
        this.updateApiConfig()
        
        // 开始上传
        const result = await uploadFileWithResumable(file, {
          category: this.config.category,
          chunkSize: this.config.chunkSize,
          concurrency: this.config.concurrency,
          onProgress: this.handleProgress,
          onChunkProgress: this.handleChunkProgress
        })
        
        // 上传成功
        this.uploadState.result = result
        this.uploadState.isUploading = false
        this.addLog('info', `上传成功: ${result.fullUrl}`)
        
      } catch (error) {
        this.uploadState.isUploading = false
        this.setError(error.message)
        this.addLog('error', `上传失败: ${error.message}`)
      }
    },
    
    /**
     * 处理总体进度
     */
    handleProgress(progress) {
      this.uploadState.progress = progress
      this.uploadState.uploadedSize = Math.round((progress / 100) * this.uploadState.totalSize)
    },
    
    /**
     * 处理分片进度
     */
    handleChunkProgress(partNumber, percentage) {
      if (percentage === 100) {
        this.uploadState.completedChunks++
      }
      
      // 计算总分片数
      this.uploadState.totalChunks = Math.ceil(this.uploadState.totalSize / this.config.chunkSize)
      
      this.addLog('debug', `分片${partNumber}上传进度: ${percentage}%`)
    },
    
    /**
     * 取消上传
     */
    async cancelUpload() {
      if (this.uploadState.uploadId) {
        try {
          await abortMultipartUpload(this.uploadState.uploadId)
          this.addLog('info', '上传已取消')
        } catch (error) {
          this.addLog('warn', `取消上传失败: ${error.message}`)
        }
      }
      
      this.uploadState.isUploading = false
      this.addLog('info', '用户取消上传')
    },
    
    /**
     * 重置上传状态
     */
    resetUploadState() {
      this.uploadState = {
        isUploading: false,
        fileName: '',
        progress: 0,
        uploadedSize: 0,
        totalSize: 0,
        completedChunks: 0,
        totalChunks: 0,
        result: null,
        error: null,
        uploadId: null
      }
    },
    
    /**
     * 设置错误
     */
    setError(message) {
      this.uploadState.error = message
    },
    
    /**
     * 清除错误
     */
    clearError() {
      this.uploadState.error = null
    },
    
    /**
     * 添加日志
     */
    addLog(level, message) {
      const log = {
        time: new Date().toLocaleTimeString(),
        level,
        message
      }
      
      this.logs.push(log)
      
      // 限制日志数量
      if (this.logs.length > 100) {
        this.logs.shift()
      }
      
      // 自动滚动到底部
      this.$nextTick(() => {
        if (this.$refs.logContent) {
          this.$refs.logContent.scrollTop = this.$refs.logContent.scrollHeight
        }
      })
    },
    
    /**
     * 清空日志
     */
    clearLogs() {
      this.logs = []
    },
    
    /**
     * 保存配置
     */
    saveConfig() {
      try {
        localStorage.setItem('ossUploaderConfig', JSON.stringify(this.config))
      } catch (error) {
        console.warn('保存配置失败:', error)
      }
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize
  },
  
  watch: {
    // 监听配置变化
    config: {
      handler() {
        this.saveConfig()
        this.updateApiConfig()
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.oss-uploader {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 上传区域 */
.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.upload-area:hover,
.upload-area.drag-over {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
  color: #666;
}

.upload-hint {
  font-size: 14px;
  color: #999;
}

/* 上传进度 */
.upload-progress {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fff;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-weight: 500;
}

.progress-bar {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  transition: width 0.3s ease;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.cancel-btn {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  font-size: 14px;
}

.cancel-btn:hover {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

/* 上传结果 */
.upload-result {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  background: #f6ffed;
}

.result-success {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #52c41a;
}

.success-icon {
  margin-right: 8px;
}

.result-details p {
  margin: 8px 0;
  font-size: 14px;
  color: #666;
}

.result-details a {
  color: #1890ff;
  text-decoration: none;
  word-break: break-all;
}

.result-details a:hover {
  text-decoration: underline;
}

/* 错误信息 */
.upload-error {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #ffccc7;
  border-radius: 8px;
  background: #fff2f0;
}

.error-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #ff4d4f;
}

.error-icon {
  margin-right: 8px;
}

.error-message {
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
  word-break: break-word;
}

.retry-btn {
  padding: 6px 12px;
  border: 1px solid #ff4d4f;
  border-radius: 4px;
  background: #fff;
  color: #ff4d4f;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background: #ff4d4f;
  color: #fff;
}

/* 日志区域 */
.log-area {
  margin: 20px 0;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fff;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
  font-weight: 500;
}

.clear-logs-btn {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  font-size: 12px;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
}

.log-content > div {
  margin: 2px 0;
  padding: 2px 4px;
  border-radius: 2px;
}

.log-debug { background: #f0f0f0; color: #666; }
.log-info { background: #e6f7ff; color: #1890ff; }
.log-warn { background: #fff7e6; color: #fa8c16; }
.log-error { background: #fff2f0; color: #ff4d4f; }

.log-time {
  color: #999;
  margin-right: 8px;
}

.log-level {
  font-weight: bold;
  margin-right: 8px;
  min-width: 50px;
  display: inline-block;
}

/* 配置面板 */
.config-panel {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fff;
}

.config-panel h3 {
  margin: 0 0 16px 0;
  color: #333;
}

.config-item {
  display: flex;
  align-items: center;
  margin: 12px 0;
}

.config-item label {
  min-width: 100px;
  margin-right: 12px;
  font-size: 14px;
  color: #666;
}

.config-item select,
.config-item input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.config-item input[type="checkbox"] {
  flex: none;
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .oss-uploader {
    padding: 10px;
  }
  
  .upload-area {
    padding: 20px;
  }
  
  .config-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .config-item label {
    min-width: auto;
    margin-bottom: 4px;
  }
  
  .config-item select,
  .config-item input {
    width: 100%;
  }
}
</style> 