/**
 * 前端配置文件
 * 用于管理API配置和环境变量
 */

/**
 * 环境配置
 */
const ENV_CONFIG = {
  // 开发环境
  development: {
    API_BASE_URL: 'http://localhost:8080',
    API_TIMEOUT: 30000,
    ENABLE_DEBUG: true,
    ENABLE_AUTH: true
  },
  
  // 测试环境
  test: {
    API_BASE_URL: 'http://test-api.example.com',
    API_TIMEOUT: 30000,
    ENABLE_DEBUG: true,
    ENABLE_AUTH: true
  },
  
  // 生产环境
  production: {
    API_BASE_URL: '',  // 使用相对路径
    API_TIMEOUT: 30000,
    ENABLE_DEBUG: false,
    ENABLE_AUTH: true
  }
}

/**
 * 获取当前环境
 */
function getCurrentEnvironment() {
  // 优先使用环境变量
  if (typeof process !== 'undefined' && process.env) {
    if (process.env.NODE_ENV) {
      return process.env.NODE_ENV
    }
  }
  
  // 浏览器环境下的自动检测
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname
    
    // 本地开发环境
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return 'development'
    }
    
    // 测试环境（根据域名判断）
    if (hostname.includes('test') || hostname.includes('staging')) {
      return 'test'
    }
    
    // 生产环境
    return 'production'
  }
  
  // 默认开发环境
  return 'development'
}

/**
 * 获取当前环境配置
 */
export function getConfig() {
  const env = getCurrentEnvironment()
  const config = ENV_CONFIG[env] || ENV_CONFIG.development
  
  // 支持环境变量覆盖
  if (typeof process !== 'undefined' && process.env) {
    return {
      ...config,
      API_BASE_URL: process.env.VUE_APP_API_BASE_URL || config.API_BASE_URL,
      API_TIMEOUT: parseInt(process.env.VUE_APP_API_TIMEOUT) || config.API_TIMEOUT,
      ENABLE_DEBUG: process.env.VUE_APP_ENABLE_DEBUG === 'true' || config.ENABLE_DEBUG,
      ENABLE_AUTH: process.env.VUE_APP_ENABLE_AUTH !== 'false' && config.ENABLE_AUTH
    }
  }
  
  return config
}

/**
 * API端点配置
 */
export const API_ENDPOINTS = {
  // 基础接口
  BASE: '/api/basic',
  
  // 文件上传相关
  UPLOAD: {
    // 普通文件上传
    SIMPLE: '/api/basic/upload/simple',
    
    // 分片上传
    RESUMABLE: {
      // 初始化分片上传
      INIT: '/api/basic/upload/resumable/init',
      // 生成预签名URL
      PRESIGNED_URL: '/api/basic/upload/resumable/presigned-url',
      // 完成分片上传
      COMPLETE: '/api/basic/upload/resumable/complete',
      // 取消分片上传
      ABORT: '/api/basic/upload/resumable/abort'
    }
  },
  
  // 用户认证相关
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    USER_INFO: '/api/auth/user-info'
  },
  
  // 其他业务接口
  BUSINESS: {
    // 可以根据需要添加其他业务接口
  }
}

/**
 * 请求配置
 */
export const REQUEST_CONFIG = {
  // 默认请求头
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  
  // 认证配置
  AUTH: {
    HEADER_NAME: 'Authorization',
    TOKEN_PREFIX: 'Bearer ',
    TOKEN_STORAGE_KEY: 'token'
  },
  
  // 重试配置
  RETRY: {
    MAX_ATTEMPTS: 3,
    DELAY: 1000,
    BACKOFF_FACTOR: 2
  },
  
  // 上传配置
  UPLOAD: {
    // 默认分片大小（5MB）
    DEFAULT_CHUNK_SIZE: 5 * 1024 * 1024,
    // 最大文件大小（100MB）
    MAX_FILE_SIZE: 100 * 1024 * 1024,
    // 默认并发数
    DEFAULT_CONCURRENCY: 3,
    // 支持的文件类型
    ALLOWED_TYPES: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'application/zip',
      'application/x-rar-compressed'
    ],
    // 支持的文件扩展名
    ALLOWED_EXTENSIONS: [
      'jpg', 'jpeg', 'png', 'gif', 'bmp',
      'pdf', 'doc', 'docx', 'xls', 'xlsx',
      'ppt', 'pptx', 'txt', 'zip', 'rar'
    ]
  }
}

/**
 * 错误码配置
 */
export const ERROR_CODES = {
  // 通用错误
  SUCCESS: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
  
  // 业务错误码（根据后端定义）
  BUSINESS: {
    FILE_TOO_LARGE: 10001,
    FILE_TYPE_NOT_ALLOWED: 10002,
    UPLOAD_SESSION_NOT_FOUND: 10003,
    INVALID_PART_NUMBER: 10004,
    UPLOAD_FAILED: 10005
  }
}

/**
 * 日志级别
 */
export const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
}

/**
 * 获取日志级别
 */
export function getLogLevel() {
  const config = getConfig()
  return config.ENABLE_DEBUG ? LOG_LEVELS.DEBUG : LOG_LEVELS.INFO
}

/**
 * 工具函数：格式化文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 工具函数：验证文件类型
 */
export function validateFileType(file) {
  const { ALLOWED_TYPES, ALLOWED_EXTENSIONS } = REQUEST_CONFIG.UPLOAD
  
  // 检查MIME类型
  if (file.type && ALLOWED_TYPES.includes(file.type)) {
    return true
  }
  
  // 检查文件扩展名
  const extension = file.name.split('.').pop().toLowerCase()
  return ALLOWED_EXTENSIONS.includes(extension)
}

/**
 * 工具函数：验证文件大小
 */
export function validateFileSize(file) {
  return file.size <= REQUEST_CONFIG.UPLOAD.MAX_FILE_SIZE
}

/**
 * 工具函数：生成唯一ID
 */
export function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 工具函数：延迟执行
 */
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 工具函数：重试执行
 */
export async function retryAsync(fn, options = {}) {
  const {
    maxAttempts = REQUEST_CONFIG.RETRY.MAX_ATTEMPTS,
    delay: retryDelay = REQUEST_CONFIG.RETRY.DELAY,
    backoffFactor = REQUEST_CONFIG.RETRY.BACKOFF_FACTOR
  } = options
  
  let lastError
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      
      if (attempt === maxAttempts) {
        break
      }
      
      const waitTime = retryDelay * Math.pow(backoffFactor, attempt - 1)
      console.warn(`第${attempt}次尝试失败，${waitTime}ms后重试:`, error.message)
      await delay(waitTime)
    }
  }
  
  throw lastError
}

// 默认导出
export default {
  getConfig,
  API_ENDPOINTS,
  REQUEST_CONFIG,
  ERROR_CODES,
  LOG_LEVELS,
  getLogLevel,
  formatFileSize,
  validateFileType,
  validateFileSize,
  generateUniqueId,
  delay,
  retryAsync
} 