/**
 * OSS分片上传服务
 * 等效于Java版本的uploadChunkToOss方法的前端实现
 */

// ==================== API配置 ====================

/**
 * API配置
 */
const API_CONFIG = {
  // 基础URL配置 - 根据环境自动选择
  baseURL: getBaseURL(),
  
  // 请求超时时间
  timeout: 30000,
  
  // 默认请求头
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  
  // 认证配置
  auth: {
    // 是否启用认证
    enabled: true,
    // 认证头名称
    headerName: 'Authorization',
    // Token前缀
    tokenPrefix: 'Bearer '
  },
  
  // API端点
  endpoints: {
    // 初始化分片上传
    initUpload: '/api/basic/upload/resumable/init',
    // 生成预签名URL
    presignedUrl: '/api/basic/upload/resumable/presigned-url',
    // 完成分片上传
    completeUpload: '/api/basic/upload/resumable/complete',
    // 取消分片上传
    abortUpload: '/api/basic/upload/resumable/abort',
    // 普通文件上传
    simpleUpload: '/api/basic/upload/simple'
  }
}

/**
 * 获取基础URL
 * 根据环境变量或当前域名自动配置
 */
function getBaseURL() {
  // 优先使用环境变量
  if (typeof process !== 'undefined' && process.env) {
    if (process.env.VUE_APP_API_BASE_URL) {
      return process.env.VUE_APP_API_BASE_URL
    }
    if (process.env.NODE_ENV === 'development') {
      return 'http://localhost:8080'
    }
  }
  
  // 浏览器环境下的自动检测
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname
    
    // 本地开发环境
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return 'http://localhost:8080'
    }
    
    // 生产环境使用相对路径
    return ''
  }
  
  // 默认本地开发环境
  return 'http://localhost:8080'
}

/**
 * 获取认证Token
 */
function getAuthToken() {
  if (typeof window !== 'undefined') {
    // 从localStorage获取token
    const token = localStorage.getItem('token') || 
                  localStorage.getItem('access_token') ||
                  localStorage.getItem('authToken')
    
    if (token) {
      return token
    }
    
    // 从sessionStorage获取token
    return sessionStorage.getItem('token') || 
           sessionStorage.getItem('access_token') ||
           sessionStorage.getItem('authToken')
  }
  
  return null
}

/**
 * 构建请求头
 */
function buildHeaders(customHeaders = {}) {
  const headers = { ...API_CONFIG.headers, ...customHeaders }
  
  // 添加认证头
  if (API_CONFIG.auth.enabled) {
    const token = getAuthToken()
    if (token) {
      headers[API_CONFIG.auth.headerName] = API_CONFIG.auth.tokenPrefix + token
    }
  }
  
  return headers
}

/**
 * HTTP请求工具
 */
async function httpRequest(url, options = {}) {
  const fullUrl = url.startsWith('http') ? url : API_CONFIG.baseURL + url
  
  const requestOptions = {
    method: 'GET',
    headers: buildHeaders(options.headers),
    ...options
  }
  
  // 设置超时
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout)
  requestOptions.signal = controller.signal
  
  try {
    console.debug(`HTTP请求: ${requestOptions.method} ${fullUrl}`)
    
    const response = await fetch(fullUrl, requestOptions)
    clearTimeout(timeoutId)
    
    console.debug(`HTTP响应: ${response.status} ${response.statusText}`)
    
    // 检查响应状态
    if (!response.ok) {
      const errorText = await response.text().catch(() => '')
      throw new Error(`HTTP ${response.status}: ${response.statusText}${errorText ? ' - ' + errorText : ''}`)
    }
    
    return response
  } catch (error) {
    clearTimeout(timeoutId)
    
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接')
    }
    
    throw error
  }
}

// ==================== 后端API调用方法 ====================

/**
 * 初始化分片上传
 * @param {Object} initData - 初始化数据
 * @param {string} initData.originalFileName - 原始文件名
 * @param {number} initData.fileSize - 文件大小
 * @param {string} [initData.contentType] - 文件MIME类型
 * @param {string} [initData.category] - 文件分类
 * @param {string} [initData.md5] - 文件MD5值
 * @param {number} [initData.chunkSize] - 分片大小
 * @returns {Promise<Object>} 初始化结果
 */
export async function initializeResumableUpload(initData) {
  try {
    console.info('初始化分片上传:', initData)
    
    const response = await httpRequest(API_CONFIG.endpoints.initUpload, {
      method: 'POST',
      body: JSON.stringify(initData)
    })
    
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || '初始化分片上传失败')
    }
    
    console.info('分片上传初始化成功:', result.data)
    return result.data
    
  } catch (error) {
    console.error('初始化分片上传失败:', error)
    throw new Error(`初始化分片上传失败: ${error.message}`)
  }
}

/**
 * 获取分片上传预签名URL
 * @param {string} uploadId - 上传ID
 * @param {number} partNumber - 分片编号
 * @param {number} [expiration=3600] - 过期时间（秒）
 * @returns {Promise<string>} 预签名URL
 */
export async function getPresignedPartUploadUrl(uploadId, partNumber, expiration = 3600) {
  try {
    console.debug(`获取分片预签名URL: uploadId=${uploadId}, partNumber=${partNumber}`)
    
    const url = `${API_CONFIG.endpoints.presignedUrl}/${uploadId}/${partNumber}?expiration=${expiration}`
    const response = await httpRequest(url)
    
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || '获取预签名URL失败')
    }
    
    console.debug(`预签名URL获取成功: ${result.data}`)
    return result.data
    
  } catch (error) {
    console.error('获取预签名URL失败:', error)
    throw new Error(`获取预签名URL失败: ${error.message}`)
  }
}

/**
 * 完成分片上传
 * @param {string} uploadId - 上传ID
 * @param {Array} partETags - 分片ETag列表
 * @returns {Promise<Object>} 完成结果
 */
export async function completeMultipartUpload(uploadId, partETags) {
  try {
    console.info(`完成分片上传: uploadId=${uploadId}, partCount=${partETags.length}`)
    
    const url = `${API_CONFIG.endpoints.completeUpload}/${uploadId}`
    const response = await httpRequest(url, {
      method: 'POST',
      body: JSON.stringify(partETags)
    })
    
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || '完成分片上传失败')
    }
    
    console.info('分片上传完成成功:', result.data)
    return result.data
    
  } catch (error) {
    console.error('完成分片上传失败:', error)
    throw new Error(`完成分片上传失败: ${error.message}`)
  }
}

/**
 * 取消分片上传
 * @param {string} uploadId - 上传ID
 * @returns {Promise<void>}
 */
export async function abortMultipartUpload(uploadId) {
  try {
    console.info(`取消分片上传: uploadId=${uploadId}`)
    
    const url = `${API_CONFIG.endpoints.abortUpload}/${uploadId}`
    const response = await httpRequest(url)
    
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || '取消分片上传失败')
    }
    
    console.info('分片上传取消成功')
    
  } catch (error) {
    console.error('取消分片上传失败:', error)
    throw new Error(`取消分片上传失败: ${error.message}`)
  }
}

// ==================== 核心上传方法 ====================

/**
 * 使用预签名URL上传文件分片到OSS
 * 等效于Java版本的uploadChunkToOss方法
 * @param {Object} options - 上传选项
 * @param {string} options.presignedUrl - 预签名URL
 * @param {File|Blob|ArrayBuffer} options.data - 要上传的数据
 * @param {number} [options.partNumber] - 分片编号
 * @param {Function} [options.onProgress] - 进度回调函数
 * @param {number} [options.timeout=30000] - 超时时间（毫秒）
 * @returns {Promise<Object>} 上传结果，包含etag、partNumber、size
 */
export async function uploadChunkToOss(options) {
  const { presignedUrl, data, partNumber, onProgress, timeout = 30000 } = options
  
  // 确定数据大小
  let dataSize
  let uploadData
  
  if (data instanceof File || data instanceof Blob) {
    dataSize = data.size
    uploadData = data
  } else if (data instanceof ArrayBuffer) {
    dataSize = data.byteLength
    uploadData = new Blob([data])
  } else {
    throw new Error('不支持的数据类型，请使用File、Blob或ArrayBuffer')
  }

  console.debug(`开始上传分片到OSS: url=${presignedUrl}, dataSize=${dataSize}`)

  try {
    // 使用fetch进行PUT请求（避免axios依赖）
    const response = await uploadWithFetch(presignedUrl, uploadData, {
      timeout,
      onProgress,
      partNumber
    })

    console.debug(`HTTP响应: code=${response.status}, message=${response.statusText}`)

    // 检查响应状态码
    if (response.status !== 200) {
      const errorResponse = await response.text().catch(() => '')
      
      // 记录详细的错误信息
      console.error('上传分片失败详情:')
      console.error(`  - 响应码: ${response.status}`)
      console.error(`  - 响应消息: ${response.statusText}`)
      console.error(`  - 错误响应: ${errorResponse}`)
      console.error(`  - 预签名URL: ${presignedUrl}`)
      console.error(`  - 数据大小: ${dataSize} bytes`)

      // 分析403错误的具体原因
      if (response.status === 403) {
        analyzeHttp403Error(presignedUrl, errorResponse)
      }

      throw new Error(`上传分片失败，响应码: ${response.status}, 响应消息: ${response.statusText}, 错误详情: ${errorResponse}`)
    }

    // 获取ETag
    let etag = response.headers.get('etag')
    if (etag && etag.startsWith('"') && etag.endsWith('"')) {
      etag = etag.substring(1, etag.length - 1)
    }

    if (!etag) {
      throw new Error('响应中缺少ETag头部')
    }

    console.debug(`分片上传成功: etag=${etag}`)

    return {
      etag,
      partNumber,
      size: dataSize
    }

  } catch (error) {
    console.error(`上传分片异常: ${error.message}`)
    throw error
  }
}

/**
 * 使用fetch API上传数据，支持进度监控
 * @param {string} url - 上传URL
 * @param {Blob} data - 要上传的数据
 * @param {Object} options - 选项
 * @returns {Promise<Response>} fetch响应
 */
async function uploadWithFetch(url, data, options = {}) {
  const { timeout = 30000, onProgress, partNumber } = options
  
  // 创建AbortController用于超时控制
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)

  try {
    // 如果需要进度监控，使用XMLHttpRequest
    if (onProgress && typeof onProgress === 'function') {
      return await uploadWithXHR(url, data, options)
    }

    // 否则使用fetch
    const response = await fetch(url, {
      method: 'PUT',
      body: data,
      headers: {
        'Content-Type': 'application/octet-stream',
      },
      signal: controller.signal
    })

    clearTimeout(timeoutId)
    return response

  } catch (error) {
    clearTimeout(timeoutId)
    
    if (error.name === 'AbortError') {
      console.error(`上传超时: ${timeout}ms`)
      throw new Error('上传超时，请检查网络连接')
    }
    
    throw error
  }
}

/**
 * 使用XMLHttpRequest上传数据，支持进度监控
 * @param {string} url - 上传URL
 * @param {Blob} data - 要上传的数据
 * @param {Object} options - 选项
 * @returns {Promise<Response>} 模拟的Response对象
 */
function uploadWithXHR(url, data, options = {}) {
  const { timeout = 30000, onProgress, partNumber } = options
  
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    
    // 设置超时
    xhr.timeout = timeout
    
    // 监听上传进度
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        const percentage = Math.round((event.loaded * 100) / event.total)
        onProgress(percentage)
        console.debug(`分片${partNumber || ''}上传进度: ${percentage}%`)
      }
    })
    
    // 监听完成事件
    xhr.addEventListener('load', () => {
      // 创建模拟的Response对象
      const response = {
        status: xhr.status,
        statusText: xhr.statusText,
        headers: {
          get: (name) => xhr.getResponseHeader(name)
        },
        text: () => Promise.resolve(xhr.responseText)
      }
      resolve(response)
    })
    
    // 监听错误事件
    xhr.addEventListener('error', () => {
      reject(new Error('网络错误'))
    })
    
    // 监听超时事件
    xhr.addEventListener('timeout', () => {
      reject(new Error('上传超时'))
    })
    
    // 发送请求
    xhr.open('PUT', url)
    xhr.setRequestHeader('Content-Type', 'application/octet-stream')
    xhr.send(data)
  })
}

/**
 * 分析HTTP 403错误的具体原因
 * 等效于Java版本的analyzeHttp403Error方法
 * @param {string} presignedUrl - 预签名URL
 * @param {string} errorResponse - 错误响应内容
 * @returns {Object} 错误分析结果
 */
export function analyzeHttp403Error(presignedUrl, errorResponse) {
  console.error('=== HTTP 403错误详细分析 ===')

  const analysis = {
    urlAnalysis: {
      protocol: '',
      host: '',
      path: '',
      queryParams: {}
    },
    possibleCauses: [],
    suggestions: []
  }

  try {
    const url = new URL(presignedUrl)
    analysis.urlAnalysis = {
      protocol: url.protocol,
      host: url.host,
      path: url.pathname,
      queryParams: Object.fromEntries(url.searchParams.entries())
    }

    console.error('预签名URL分析:')
    console.error(`  - Protocol: ${url.protocol}`)
    console.error(`  - Host: ${url.host}`)
    console.error(`  - Path: ${url.pathname}`)
    console.error(`  - Query: ${url.search}`)

    // 检查查询参数
    if (url.search) {
      console.error('  - 查询参数:')
      url.searchParams.forEach((value, key) => {
        console.error(`    * ${key}: ${value}`)

        // 特别检查uploadId格式
        if (key === 'uploadId') {
          if (value.startsWith('upload-')) {
            console.error('    ❌ 检测到错误：使用了内部会话ID而非OSS真实Upload ID！')
            console.error('    💡 解决方案：确保使用session.getOssUploadId()而不是会话ID')
            analysis.possibleCauses.push('使用了错误的Upload ID格式')
            analysis.suggestions.push('检查Upload ID是否为OSS返回的真实ID')
          } else {
            console.error('    ✅ Upload ID格式看起来正确')
          }
        }
      })
    }
  } catch (error) {
    console.error(`分析预签名URL失败: ${error.message}`)
  }

  // 分析错误响应
  if (errorResponse && errorResponse.trim()) {
    console.error(`OSS错误响应: ${errorResponse}`)

    if (errorResponse.includes('SignatureDoesNotMatch')) {
      console.error('💡 这是签名不匹配错误，可能原因:')
      console.error('  1. Upload ID不正确（最常见）')
      console.error('  2. Access Key或Secret Key错误')
      console.error('  3. 系统时间不同步')
      console.error('  4. 请求参数格式错误')
      
      analysis.possibleCauses.push('签名不匹配')
      analysis.suggestions.push('检查Upload ID和访问凭证')
    } else if (errorResponse.includes('AccessDenied')) {
      console.error('💡 这是访问拒绝错误，可能原因:')
      console.error('  1. Access Key权限不足')
      console.error('  2. Bucket策略限制')
      console.error('  3. IP白名单限制')
      
      analysis.possibleCauses.push('访问权限不足')
      analysis.suggestions.push('检查OSS权限配置')
    } else if (errorResponse.includes('NoSuchUpload')) {
      console.error('💡 这是Upload不存在错误，可能原因:')
      console.error('  1. Upload ID已过期或被取消')
      console.error('  2. Upload ID格式错误')
      console.error('  3. Bucket或Object Key不匹配')
      
      analysis.possibleCauses.push('上传会话不存在')
      analysis.suggestions.push('重新初始化上传会话')
    }
  }

  // 提供通用解决建议
  console.error('🔧 通用解决建议:')
  console.error('  1. 检查OSS配置是否正确（Access Key、Secret Key、Bucket、Endpoint）')
  console.error('  2. 确认使用的是OSS返回的真实Upload ID，而不是内部会话ID')
  console.error('  3. 验证OSS权限设置，确保有分片上传权限')
  console.error('  4. 检查系统时间是否与OSS服务器同步')
  console.error('  5. 确认网络连接正常，没有防火墙阻拦')
  console.error('  6. 检查CORS配置，确保前端域名在允许列表中')

  analysis.suggestions.push(
    '检查OSS配置',
    '验证Upload ID格式',
    '确认权限设置',
    '检查CORS配置',
    '验证网络连接'
  )

  console.error('=== 403错误分析结束 ===')

  return analysis
}

// ==================== 高级上传方法 ====================

/**
 * 完整的分片上传流程
 * @param {File} file - 要上传的文件
 * @param {Object} options - 上传选项
 * @param {string} [options.category] - 文件分类
 * @param {number} [options.chunkSize=5242880] - 分片大小（默认5MB）
 * @param {number} [options.concurrency=3] - 并发上传数量
 * @param {Function} [options.onProgress] - 总体进度回调
 * @param {Function} [options.onChunkProgress] - 单个分片进度回调
 * @returns {Promise<Object>} 上传结果
 */
export async function uploadFileWithResumable(file, options = {}) {
  const {
    category,
    chunkSize = 5 * 1024 * 1024, // 默认5MB
    concurrency = 3,
    onProgress,
    onChunkProgress
  } = options
  
  try {
    console.info(`开始完整分片上传流程: ${file.name}, 大小: ${file.size} bytes`)
    
    // 1. 计算文件MD5（可选）
    let md5 = null
    try {
      md5 = await calculateFileMD5(file)
      console.debug(`文件MD5计算完成: ${md5}`)
    } catch (error) {
      console.warn('MD5计算失败，继续上传:', error.message)
    }
    
    // 2. 初始化分片上传
    const initResult = await initializeResumableUpload({
      originalFileName: file.name,
      fileSize: file.size,
      contentType: file.type,
      category,
      md5,
      chunkSize
    })
    
    console.info(`分片上传初始化成功: uploadId=${initResult.uploadId}`)
    
    // 3. 分割文件
    const chunks = splitFileIntoChunks(file, chunkSize)
    console.info(`文件分片完成，共${chunks.length}个分片`)
    
    // 4. 批量上传分片
    const partETags = await uploadMultipleChunks(chunks, {
      uploadId: initResult.uploadId,
      concurrency,
      onProgress,
      onChunkProgress
    })
    
    console.info(`所有分片上传完成，共${partETags.length}个分片`)
    
    // 5. 完成分片上传
    const completeResult = await completeMultipartUpload(initResult.uploadId, partETags)
    
    console.info(`文件上传完成: ${completeResult.fullUrl}`)
    
    return {
      ...completeResult,
      uploadId: initResult.uploadId,
      totalChunks: chunks.length,
      chunkSize
    }
    
  } catch (error) {
    console.error('完整分片上传流程失败:', error)
    throw error
  }
}

/**
 * 批量上传多个分片
 * @param {Array} chunks - 分片数组，每个元素包含data、partNumber
 * @param {Object} options - 选项
 * @param {string} options.uploadId - 上传ID
 * @param {number} [options.concurrency=3] - 并发上传数量
 * @param {Function} [options.onProgress] - 总体进度回调
 * @param {Function} [options.onChunkProgress] - 单个分片进度回调
 * @returns {Promise<Array>} 上传结果数组
 */
export async function uploadMultipleChunks(chunks, options = {}) {
  const { uploadId, concurrency = 3, onProgress, onChunkProgress } = options
  
  const results = []
  const totalChunks = chunks.length
  let completedChunks = 0

  // 分批并发上传
  for (let i = 0; i < chunks.length; i += concurrency) {
    const batch = chunks.slice(i, i + concurrency)
    
    const batchPromises = batch.map(async (chunk) => {
      try {
        // 获取预签名URL
        const presignedUrl = await getPresignedPartUploadUrl(uploadId, chunk.partNumber)
        
        // 上传分片
        const result = await uploadChunkToOss({
          presignedUrl,
          data: chunk.data,
          partNumber: chunk.partNumber,
          onProgress: (percentage) => {
            if (onChunkProgress) {
              onChunkProgress(chunk.partNumber, percentage)
            }
          }
        })

        completedChunks++
        
        // 更新总体进度
        if (onProgress) {
          const totalProgress = Math.round((completedChunks * 100) / totalChunks)
          onProgress(totalProgress)
        }

        // 返回分片ETag信息
        return {
          partNumber: result.partNumber,
          etag: result.etag,
          partSize: result.size
        }
        
      } catch (error) {
        console.error(`分片${chunk.partNumber}上传失败: ${error.message}`)
        throw error
      }
    })

    const batchResults = await Promise.allSettled(batchPromises)
    
    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value)
      } else {
        console.error(`分片${batch[index].partNumber}上传失败: ${result.reason}`)
      }
    })
  }

  return results
}

/**
 * 将文件分割成多个分片
 * @param {File} file - 要分割的文件
 * @param {number} [chunkSize=5242880] - 分片大小（字节，默认5MB）
 * @returns {Array} 分片数组
 */
export function splitFileIntoChunks(file, chunkSize = 5 * 1024 * 1024) {
  const chunks = []
  
  for (let i = 0; i < file.size; i += chunkSize) {
    const chunk = file.slice(i, i + chunkSize)
    chunks.push({
      partNumber: Math.floor(i / chunkSize) + 1,
      data: chunk,
      size: chunk.size,
      start: i,
      end: Math.min(i + chunkSize, file.size)
    })
  }
  
  return chunks
}

/**
 * 计算文件MD5哈希值
 * @param {File|Blob} file - 文件对象
 * @returns {Promise<string>} MD5哈希值
 */
export async function calculateFileMD5(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = async (event) => {
      try {
        const arrayBuffer = event.target.result
        const hashBuffer = await crypto.subtle.digest('MD5', arrayBuffer)
        const hashArray = Array.from(new Uint8Array(hashBuffer))
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
        resolve(hashHex)
      } catch (error) {
        reject(error)
      }
    }
    
    reader.onerror = () => reject(new Error('读取文件失败'))
    reader.readAsArrayBuffer(file)
  })
}

/**
 * 验证上传的文件
 * @param {string} fileUrl - 文件URL
 * @param {string} expectedMD5 - 期望的MD5值
 * @param {number} expectedSize - 期望的文件大小
 * @returns {Promise<boolean>} 验证结果
 */
export async function verifyUploadedFile(fileUrl, expectedMD5, expectedSize) {
  try {
    console.info(`验证上传的文件: ${fileUrl}`)

    // 下载文件
    const response = await fetch(fileUrl)
    
    if (!response.ok) {
      throw new Error(`下载文件失败，响应码: ${response.status}`)
    }

    // 获取文件内容
    const arrayBuffer = await response.arrayBuffer()
    
    // 验证文件大小
    if (arrayBuffer.byteLength !== expectedSize) {
      console.error(`文件大小不匹配: 期望${expectedSize}, 实际${arrayBuffer.byteLength}`)
      return false
    }

    // 验证MD5（如果提供）
    if (expectedMD5) {
      const hashBuffer = await crypto.subtle.digest('MD5', arrayBuffer)
      const hashArray = Array.from(new Uint8Array(hashBuffer))
      const actualMD5 = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
      
      if (actualMD5 !== expectedMD5) {
        console.error(`文件MD5不匹配: 期望${expectedMD5}, 实际${actualMD5}`)
        return false
      }
    }

    console.info(`文件验证成功：大小=${arrayBuffer.byteLength}, MD5=${expectedMD5}`)
    return true

  } catch (error) {
    console.error(`文件验证失败: ${error.message}`)
    return false
  }
}

// ==================== 配置和工具方法 ====================

/**
 * 设置API配置
 * @param {Object} config - 配置对象
 */
export function setApiConfig(config) {
  Object.assign(API_CONFIG, config)
}

/**
 * 获取当前API配置
 * @returns {Object} 当前配置
 */
export function getApiConfig() {
  return { ...API_CONFIG }
}

/**
 * 设置认证Token
 * @param {string} token - 认证Token
 */
export function setAuthToken(token) {
  if (typeof window !== 'undefined') {
    localStorage.setItem('token', token)
  }
}

/**
 * 清除认证Token
 */
export function clearAuthToken() {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('token')
    localStorage.removeItem('access_token')
    localStorage.removeItem('authToken')
    sessionStorage.removeItem('token')
    sessionStorage.removeItem('access_token')
    sessionStorage.removeItem('authToken')
  }
}

// 默认导出主要功能
export default {
  // 核心上传方法
  uploadChunkToOss,
  uploadFileWithResumable,
  uploadMultipleChunks,
  
  // 后端API调用
  initializeResumableUpload,
  getPresignedPartUploadUrl,
  completeMultipartUpload,
  abortMultipartUpload,
  
  // 工具方法
  splitFileIntoChunks,
  calculateFileMD5,
  verifyUploadedFile,
  analyzeHttp403Error,
  
  // 配置方法
  setApiConfig,
  getApiConfig,
  setAuthToken,
  clearAuthToken
} 