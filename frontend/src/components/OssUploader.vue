<template>
  <div class="oss-uploader">
    <div class="upload-area" @drop="handleDrop" @dragover.prevent @dragenter.prevent>
      <input 
        ref="fileInput" 
        type="file" 
        @change="handleFileSelect" 
        style="display: none"
      />
      <button @click="selectFile" :disabled="uploading">
        {{ uploading ? '上传中...' : '选择文件' }}
      </button>
      <p>或拖拽文件到此处</p>
    </div>

    <div v-if="uploadProgress.length > 0" class="progress-section">
      <h3>上传进度</h3>
      <div v-for="(progress, index) in uploadProgress" :key="index" class="progress-item">
        <div class="progress-info">
          <span>分片 {{ progress.partNumber }}</span>
          <span>{{ progress.percentage }}%</span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: progress.percentage + '%' }"
            :class="{ 'error': progress.error, 'success': progress.completed }"
          ></div>
        </div>
        <div v-if="progress.error" class="error-message">
          {{ progress.errorMessage }}
        </div>
      </div>
    </div>

    <div v-if="logs.length > 0" class="logs-section">
      <h3>上传日志</h3>
      <div class="logs-container">
        <div 
          v-for="(log, index) in logs" 
          :key="index" 
          :class="['log-item', log.level]"
        >
          <span class="log-time">{{ log.timestamp }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import axios, { AxiosProgressEvent, AxiosError } from 'axios'

// ==================== 类型定义 ====================

interface UploadChunkOptions {
  presignedUrl: string
  data: File | Blob | ArrayBuffer
  partNumber?: number
  onProgress?: (progress: number) => void
  timeout?: number
}

interface UploadChunkResult {
  etag: string
  partNumber?: number
  size: number
}

interface ProgressInfo {
  partNumber: number
  percentage: number
  completed: boolean
  error: boolean
  errorMessage?: string
  etag?: string
}

interface LogEntry {
  timestamp: string
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
}

interface Http403ErrorAnalysis {
  urlAnalysis: {
    protocol: string
    host: string
    path: string
    queryParams: Record<string, string>
  }
  possibleCauses: string[]
  suggestions: string[]
}

// ==================== 响应式数据 ====================

const fileInput = ref<HTMLInputElement>()
const uploading = ref(false)
const uploadProgress = ref<ProgressInfo[]>([])
const logs = ref<LogEntry[]>([])

// ==================== 核心上传方法 ====================

/**
 * 使用预签名URL上传文件分片到OSS
 * 等效于Java版本的uploadChunkToOss方法
 */
async function uploadChunkToOss(options: UploadChunkOptions): Promise<UploadChunkResult> {
  const { presignedUrl, data, partNumber, onProgress, timeout = 30000 } = options
  
  // 确定数据大小
  let dataSize: number
  let uploadData: Blob | ArrayBuffer
  
  if (data instanceof File || data instanceof Blob) {
    dataSize = data.size
    uploadData = data
  } else if (data instanceof ArrayBuffer) {
    dataSize = data.byteLength
    uploadData = new Blob([data])
  } else {
    throw new Error('不支持的数据类型，请使用File、Blob或ArrayBuffer')
  }

  addLog('debug', `开始上传分片到OSS: url=${presignedUrl}, dataSize=${dataSize}`)

  try {
    // 使用axios进行PUT请求
    const response = await axios.put(presignedUrl, uploadData, {
      headers: {
        'Content-Type': 'application/octet-stream',
      },
      timeout,
      onUploadProgress: (progressEvent: AxiosProgressEvent) => {
        if (progressEvent.total) {
          const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress?.(percentage)
          
          addLog('debug', `分片${partNumber || ''}上传进度: ${percentage}%`)
        }
      },
      // 禁用自动JSON解析，因为OSS返回的是XML
      transformResponse: [(data) => data],
      // 确保不会自动处理状态码
      validateStatus: () => true
    })

    addLog('debug', `HTTP响应: code=${response.status}, message=${response.statusText}`)

    // 检查响应状态码
    if (response.status !== 200) {
      const errorResponse = response.data || ''
      
      // 记录详细的错误信息
      addLog('error', '上传分片失败详情:')
      addLog('error', `  - 响应码: ${response.status}`)
      addLog('error', `  - 响应消息: ${response.statusText}`)
      addLog('error', `  - 错误响应: ${errorResponse}`)
      addLog('error', `  - 预签名URL: ${presignedUrl}`)
      addLog('error', `  - 数据大小: ${dataSize} bytes`)

      // 分析403错误的具体原因
      if (response.status === 403) {
        analyzeHttp403Error(presignedUrl, errorResponse)
      }

      throw new Error(`上传分片失败，响应码: ${response.status}, 响应消息: ${response.statusText}, 错误详情: ${errorResponse}`)
    }

    // 获取ETag
    let etag = response.headers['etag'] || response.headers['ETag']
    if (etag && etag.startsWith('"') && etag.endsWith('"')) {
      etag = etag.substring(1, etag.length - 1)
    }

    if (!etag) {
      throw new Error('响应中缺少ETag头部')
    }

    addLog('debug', `分片上传成功: etag=${etag}`)

    return {
      etag,
      partNumber,
      size: dataSize
    }

  } catch (error) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError
      
      if (axiosError.code === 'ECONNABORTED') {
        addLog('error', `上传超时: ${timeout}ms`)
        throw new Error(`上传超时，请检查网络连接`)
      }
      
      if (axiosError.response) {
        // 服务器响应了错误状态码
        const errorMessage = `HTTP错误: ${axiosError.response.status} ${axiosError.response.statusText}`
        addLog('error', errorMessage)
        throw new Error(errorMessage)
      } else if (axiosError.request) {
        // 请求发出但没有收到响应
        addLog('error', '网络错误: 无法连接到OSS服务器')
        throw new Error('网络错误，请检查网络连接')
      }
    }
    
    addLog('error', `上传分片异常: ${error instanceof Error ? error.message : String(error)}`)
    throw error
  }
}

/**
 * 使用fetch API的替代实现（备用方案）
 */
async function uploadChunkToOssWithFetch(options: UploadChunkOptions): Promise<UploadChunkResult> {
  const { presignedUrl, data, partNumber, onProgress, timeout = 30000 } = options
  
  let dataSize: number
  let uploadData: Blob | ArrayBuffer
  
  if (data instanceof File || data instanceof Blob) {
    dataSize = data.size
    uploadData = data
  } else if (data instanceof ArrayBuffer) {
    dataSize = data.byteLength
    uploadData = new Blob([data])
  } else {
    throw new Error('不支持的数据类型，请使用File、Blob或ArrayBuffer')
  }

  addLog('debug', `开始上传分片到OSS (fetch): url=${presignedUrl}, dataSize=${dataSize}`)

  // 创建AbortController用于超时控制
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)

  try {
    const response = await fetch(presignedUrl, {
      method: 'PUT',
      body: uploadData,
      headers: {
        'Content-Type': 'application/octet-stream',
      },
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    addLog('debug', `HTTP响应: code=${response.status}, message=${response.statusText}`)

    if (!response.ok) {
      const errorResponse = await response.text().catch(() => '')
      
      addLog('error', '上传分片失败详情:')
      addLog('error', `  - 响应码: ${response.status}`)
      addLog('error', `  - 响应消息: ${response.statusText}`)
      addLog('error', `  - 错误响应: ${errorResponse}`)
      
      if (response.status === 403) {
        analyzeHttp403Error(presignedUrl, errorResponse)
      }

      throw new Error(`上传分片失败，响应码: ${response.status}`)
    }

    // 获取ETag
    let etag = response.headers.get('etag')
    if (etag && etag.startsWith('"') && etag.endsWith('"')) {
      etag = etag.substring(1, etag.length - 1)
    }

    if (!etag) {
      throw new Error('响应中缺少ETag头部')
    }

    addLog('debug', `分片上传成功: etag=${etag}`)

    return {
      etag,
      partNumber,
      size: dataSize
    }

  } catch (error) {
    clearTimeout(timeoutId)
    
    if (error instanceof Error && error.name === 'AbortError') {
      addLog('error', `上传超时: ${timeout}ms`)
      throw new Error('上传超时，请检查网络连接')
    }
    
    addLog('error', `上传分片异常: ${error instanceof Error ? error.message : String(error)}`)
    throw error
  }
}

/**
 * 分析HTTP 403错误的具体原因
 * 等效于Java版本的analyzeHttp403Error方法
 */
function analyzeHttp403Error(presignedUrl: string, errorResponse: string): Http403ErrorAnalysis {
  addLog('error', '=== HTTP 403错误详细分析 ===')

  const analysis: Http403ErrorAnalysis = {
    urlAnalysis: {
      protocol: '',
      host: '',
      path: '',
      queryParams: {}
    },
    possibleCauses: [],
    suggestions: []
  }

  try {
    const url = new URL(presignedUrl)
    analysis.urlAnalysis = {
      protocol: url.protocol,
      host: url.host,
      path: url.pathname,
      queryParams: Object.fromEntries(url.searchParams.entries())
    }

    addLog('error', '预签名URL分析:')
    addLog('error', `  - Protocol: ${url.protocol}`)
    addLog('error', `  - Host: ${url.host}`)
    addLog('error', `  - Path: ${url.pathname}`)
    addLog('error', `  - Query: ${url.search}`)

    // 检查查询参数
    if (url.search) {
      addLog('error', '  - 查询参数:')
      url.searchParams.forEach((value, key) => {
        addLog('error', `    * ${key}: ${value}`)

        // 特别检查uploadId格式
        if (key === 'uploadId') {
          if (value.startsWith('upload-')) {
            addLog('error', '    ❌ 检测到错误：使用了内部会话ID而非OSS真实Upload ID！')
            addLog('error', '    💡 解决方案：确保使用session.getOssUploadId()而不是会话ID')
            analysis.possibleCauses.push('使用了错误的Upload ID格式')
            analysis.suggestions.push('检查Upload ID是否为OSS返回的真实ID')
          } else {
            addLog('error', '    ✅ Upload ID格式看起来正确')
          }
        }
      })
    }
  } catch (error) {
    addLog('error', `分析预签名URL失败: ${error instanceof Error ? error.message : String(error)}`)
  }

  // 分析错误响应
  if (errorResponse && errorResponse.trim()) {
    addLog('error', `OSS错误响应: ${errorResponse}`)

    if (errorResponse.includes('SignatureDoesNotMatch')) {
      addLog('error', '💡 这是签名不匹配错误，可能原因:')
      addLog('error', '  1. Upload ID不正确（最常见）')
      addLog('error', '  2. Access Key或Secret Key错误')
      addLog('error', '  3. 系统时间不同步')
      addLog('error', '  4. 请求参数格式错误')
      
      analysis.possibleCauses.push('签名不匹配')
      analysis.suggestions.push('检查Upload ID和访问凭证')
    } else if (errorResponse.includes('AccessDenied')) {
      addLog('error', '💡 这是访问拒绝错误，可能原因:')
      addLog('error', '  1. Access Key权限不足')
      addLog('error', '  2. Bucket策略限制')
      addLog('error', '  3. IP白名单限制')
      
      analysis.possibleCauses.push('访问权限不足')
      analysis.suggestions.push('检查OSS权限配置')
    } else if (errorResponse.includes('NoSuchUpload')) {
      addLog('error', '💡 这是Upload不存在错误，可能原因:')
      addLog('error', '  1. Upload ID已过期或被取消')
      addLog('error', '  2. Upload ID格式错误')
      addLog('error', '  3. Bucket或Object Key不匹配')
      
      analysis.possibleCauses.push('上传会话不存在')
      analysis.suggestions.push('重新初始化上传会话')
    }
  }

  // 提供通用解决建议
  addLog('error', '🔧 通用解决建议:')
  addLog('error', '  1. 检查OSS配置是否正确（Access Key、Secret Key、Bucket、Endpoint）')
  addLog('error', '  2. 确认使用的是OSS返回的真实Upload ID，而不是内部会话ID')
  addLog('error', '  3. 验证OSS权限设置，确保有分片上传权限')
  addLog('error', '  4. 检查系统时间是否与OSS服务器同步')
  addLog('error', '  5. 确认网络连接正常，没有防火墙阻拦')
  addLog('error', '  6. 检查CORS配置，确保前端域名在允许列表中')

  analysis.suggestions.push(
    '检查OSS配置',
    '验证Upload ID格式',
    '确认权限设置',
    '检查CORS配置',
    '验证网络连接'
  )

  addLog('error', '=== 403错误分析结束 ===')

  return analysis
}

// ==================== 批量上传方法 ====================

/**
 * 批量上传多个分片
 */
async function uploadMultipleChunks(chunks: Array<{
  presignedUrl: string
  data: File | Blob | ArrayBuffer
  partNumber: number
}>): Promise<UploadChunkResult[]> {
  
  // 初始化进度跟踪
  uploadProgress.value = chunks.map(chunk => ({
    partNumber: chunk.partNumber,
    percentage: 0,
    completed: false,
    error: false
  }))

  const results: UploadChunkResult[] = []
  const concurrency = 3 // 并发上传数量

  // 分批并发上传
  for (let i = 0; i < chunks.length; i += concurrency) {
    const batch = chunks.slice(i, i + concurrency)
    
    const batchPromises = batch.map(async (chunk) => {
      const progressIndex = uploadProgress.value.findIndex(p => p.partNumber === chunk.partNumber)
      
      try {
        const result = await uploadChunkToOss({
          presignedUrl: chunk.presignedUrl,
          data: chunk.data,
          partNumber: chunk.partNumber,
          onProgress: (percentage) => {
            if (progressIndex >= 0) {
              uploadProgress.value[progressIndex].percentage = percentage
            }
          }
        })

        // 更新进度状态
        if (progressIndex >= 0) {
          uploadProgress.value[progressIndex].completed = true
          uploadProgress.value[progressIndex].percentage = 100
          uploadProgress.value[progressIndex].etag = result.etag
        }

        return result
      } catch (error) {
        // 更新错误状态
        if (progressIndex >= 0) {
          uploadProgress.value[progressIndex].error = true
          uploadProgress.value[progressIndex].errorMessage = error instanceof Error ? error.message : String(error)
        }
        throw error
      }
    })

    const batchResults = await Promise.allSettled(batchPromises)
    
    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value)
      } else {
        addLog('error', `分片${batch[index].partNumber}上传失败: ${result.reason}`)
      }
    })
  }

  return results
}

// ==================== 文件处理方法 ====================

/**
 * 处理文件选择
 */
function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    const file = target.files[0]
    processFile(file)
  }
}

/**
 * 处理文件拖拽
 */
function handleDrop(event: DragEvent) {
  event.preventDefault()
  if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
    const file = event.dataTransfer.files[0]
    processFile(file)
  }
}

/**
 * 选择文件
 */
function selectFile() {
  fileInput.value?.click()
}

/**
 * 处理文件
 */
async function processFile(file: File) {
  addLog('info', `开始处理文件: ${file.name}, 大小: ${file.size} bytes`)
  
  // 这里可以添加文件分片和上传逻辑
  // 示例：将文件分成多个分片并上传
  const chunkSize = 256 * 1024 // 256KB
  const chunks = []
  
  for (let i = 0; i < file.size; i += chunkSize) {
    const chunk = file.slice(i, i + chunkSize)
    chunks.push({
      partNumber: Math.floor(i / chunkSize) + 1,
      data: chunk,
      presignedUrl: '' // 这里需要从后端获取预签名URL
    })
  }
  
  addLog('info', `文件分片完成，共${chunks.length}个分片`)
}

// ==================== 工具方法 ====================

/**
 * 添加日志
 */
function addLog(level: LogEntry['level'], message: string) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push({ timestamp, level, message })
  
  // 控制台输出
  console[level](`[${timestamp}] ${message}`)
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value.splice(0, logs.value.length - 100)
  }
}

/**
 * 清空日志
 */
function clearLogs() {
  logs.value = []
}

/**
 * 清空进度
 */
function clearProgress() {
  uploadProgress.value = []
}

// ==================== 导出方法供外部使用 ====================

defineExpose({
  uploadChunkToOss,
  uploadChunkToOssWithFetch,
  uploadMultipleChunks,
  analyzeHttp403Error,
  clearLogs,
  clearProgress
})
</script>

<style scoped>
.oss-uploader {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  margin-bottom: 20px;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #007bff;
}

.upload-area button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-bottom: 10px;
}

.upload-area button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.progress-section {
  margin-bottom: 20px;
}

.progress-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s;
}

.progress-fill.success {
  background: #28a745;
}

.progress-fill.error {
  background: #dc3545;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
}

.logs-section {
  margin-top: 20px;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  background: #f8f9fa;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.debug .log-message {
  color: #6c757d;
}

.log-item.info .log-message {
  color: #007bff;
}

.log-item.warn .log-message {
  color: #ffc107;
}

.log-item.error .log-message {
  color: #dc3545;
}
</style> 