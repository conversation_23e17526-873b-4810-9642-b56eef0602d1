<template>
  <div id="app">
    <header class="app-header">
      <h1>🚀 OSS分片上传系统</h1>
      <p>支持大文件断点续传的前端上传解决方案</p>
    </header>

    <main class="app-main">
      <!-- 配置面板 -->
      <div class="config-panel">
        <h2>⚙️ 系统配置</h2>
        <div class="config-grid">
          <div class="config-item">
            <label>后端API地址:</label>
            <input v-model="apiConfig.baseURL" type="text" placeholder="http://localhost:8080">
          </div>
          <div class="config-item">
            <label>认证Token:</label>
            <input v-model="authToken" type="text" placeholder="Bearer your-jwt-token">
          </div>
          <div class="config-item">
            <label>分片大小 (MB):</label>
            <select v-model="uploadConfig.chunkSizeMB">
              <option value="1">1 MB</option>
              <option value="2">2 MB</option>
              <option value="5">5 MB</option>
              <option value="10">10 MB</option>
            </select>
          </div>
          <div class="config-item">
            <label>并发数:</label>
            <select v-model="uploadConfig.concurrency">
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="5">5</option>
            </select>
          </div>
        </div>
        <div class="config-actions">
          <button @click="applyConfig" class="btn btn-primary">应用配置</button>
          <button @click="testConnection" class="btn btn-secondary">测试连接</button>
        </div>
      </div>

      <!-- 上传组件 -->
      <div class="upload-section">
        <h2>📁 文件上传</h2>
        <SimpleOssUploader 
          :key="uploaderKey"
          @upload-success="handleUploadSuccess"
          @upload-error="handleUploadError"
          @upload-progress="handleUploadProgress"
        />
      </div>

      <!-- 状态显示 -->
      <div class="status-section">
        <h2>📊 系统状态</h2>
        <div class="status-grid">
          <div class="status-item" :class="{ 'status-success': connectionStatus === 'connected', 'status-error': connectionStatus === 'error' }">
            <span class="status-label">后端连接:</span>
            <span class="status-value">{{ connectionStatusText }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">上传进度:</span>
            <span class="status-value">{{ uploadProgress }}%</span>
          </div>
          <div class="status-item">
            <span class="status-label">已上传文件:</span>
            <span class="status-value">{{ uploadedFiles.length }}</span>
          </div>
        </div>
      </div>

      <!-- 上传历史 -->
      <div class="history-section" v-if="uploadedFiles.length > 0">
        <h2>📋 上传历史</h2>
        <div class="file-list">
          <div v-for="file in uploadedFiles" :key="file.id" class="file-item">
            <div class="file-info">
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
              <span class="file-time">{{ formatTime(file.uploadTime) }}</span>
            </div>
            <div class="file-actions">
              <a :href="file.url" target="_blank" class="btn btn-link">查看</a>
              <button @click="copyUrl(file.url)" class="btn btn-link">复制链接</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 日志面板 -->
      <div class="log-section" v-if="showLogs">
        <h2>📝 系统日志</h2>
        <div class="log-container">
          <div v-for="log in logs" :key="log.id" class="log-item" :class="`log-${log.level}`">
            <span class="log-time">{{ formatTime(log.time) }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <div class="log-actions">
          <button @click="clearLogs" class="btn btn-secondary">清空日志</button>
          <button @click="showLogs = false" class="btn btn-secondary">隐藏日志</button>
        </div>
      </div>
    </main>

    <footer class="app-footer">
      <div class="footer-actions">
        <button @click="showLogs = !showLogs" class="btn btn-ghost">
          {{ showLogs ? '隐藏日志' : '显示日志' }}
        </button>
        <button @click="resetAll" class="btn btn-ghost">重置所有</button>
      </div>
      <p>&copy; 2024 OSS分片上传系统 - 基于Vue 3开发</p>
    </footer>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick } from 'vue'
import SimpleOssUploader from './components/SimpleOssUploader.vue'
import { setApiConfig, setAuthToken, testApiConnection } from './components/OssUploaderService.js'

export default {
  name: 'App',
  components: {
    SimpleOssUploader
  },
  setup() {
    // 响应式数据
    const uploaderKey = ref(0)
    const connectionStatus = ref('unknown')
    const uploadProgress = ref(0)
    const showLogs = ref(false)
    
    // 配置数据
    const apiConfig = reactive({
      baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080'
    })
    
    const authToken = ref('')
    
    const uploadConfig = reactive({
      chunkSizeMB: 5,
      concurrency: 3
    })
    
    // 状态数据
    const uploadedFiles = ref([])
    const logs = ref([])
    
    // 计算属性
    const connectionStatusText = ref('未知')
    
    // 方法
    const addLog = (level, message) => {
      logs.value.unshift({
        id: Date.now() + Math.random(),
        time: new Date(),
        level,
        message
      })
      
      // 限制日志数量
      if (logs.value.length > 100) {
        logs.value = logs.value.slice(0, 100)
      }
    }
    
    const applyConfig = async () => {
      try {
        // 应用API配置
        setApiConfig({
          baseURL: apiConfig.baseURL,
          timeout: 30000
        })
        
        // 设置认证Token
        if (authToken.value) {
          setAuthToken(authToken.value)
        }
        
        // 强制重新渲染上传组件
        uploaderKey.value++
        
        addLog('info', '配置已应用')
        
        // 测试连接
        await testConnection()
        
      } catch (error) {
        addLog('error', `配置应用失败: ${error.message}`)
      }
    }
    
    const testConnection = async () => {
      try {
        connectionStatus.value = 'testing'
        connectionStatusText.value = '测试中...'
        
        const result = await testApiConnection()
        
        if (result.success) {
          connectionStatus.value = 'connected'
          connectionStatusText.value = '已连接'
          addLog('success', '后端连接测试成功')
        } else {
          connectionStatus.value = 'error'
          connectionStatusText.value = '连接失败'
          addLog('error', `连接测试失败: ${result.error}`)
        }
      } catch (error) {
        connectionStatus.value = 'error'
        connectionStatusText.value = '连接错误'
        addLog('error', `连接测试异常: ${error.message}`)
      }
    }
    
    const handleUploadSuccess = (result) => {
      uploadedFiles.value.unshift({
        id: Date.now(),
        name: result.originalFileName || '未知文件',
        size: result.totalSize || 0,
        url: result.fullUrl,
        uploadTime: new Date()
      })
      
      uploadProgress.value = 0
      addLog('success', `文件上传成功: ${result.originalFileName}`)
    }
    
    const handleUploadError = (error) => {
      uploadProgress.value = 0
      addLog('error', `文件上传失败: ${error.message}`)
    }
    
    const handleUploadProgress = (progress) => {
      uploadProgress.value = Math.round(progress)
    }
    
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
    
    const formatTime = (date) => {
      return date.toLocaleString('zh-CN')
    }
    
    const copyUrl = async (url) => {
      try {
        await navigator.clipboard.writeText(url)
        addLog('info', '链接已复制到剪贴板')
      } catch (error) {
        addLog('error', '复制链接失败')
      }
    }
    
    const clearLogs = () => {
      logs.value = []
      addLog('info', '日志已清空')
    }
    
    const resetAll = () => {
      uploadedFiles.value = []
      logs.value = []
      uploadProgress.value = 0
      connectionStatus.value = 'unknown'
      connectionStatusText.value = '未知'
      uploaderKey.value++
      addLog('info', '系统已重置')
    }
    
    // 生命周期
    onMounted(async () => {
      addLog('info', '系统初始化完成')
      
      // 从localStorage恢复Token
      const savedToken = localStorage.getItem('token')
      if (savedToken) {
        authToken.value = savedToken
      }
      
      // 自动应用初始配置
      await nextTick()
      await applyConfig()
    })
    
    return {
      // 数据
      uploaderKey,
      connectionStatus,
      connectionStatusText,
      uploadProgress,
      showLogs,
      apiConfig,
      authToken,
      uploadConfig,
      uploadedFiles,
      logs,
      
      // 方法
      applyConfig,
      testConnection,
      handleUploadSuccess,
      handleUploadError,
      handleUploadProgress,
      formatFileSize,
      formatTime,
      copyUrl,
      clearLogs,
      resetAll
    }
  }
}
</script>

<style scoped>
/* 全局样式 */
#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #2c3e50;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 600;
}

.app-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* 主体样式 */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 面板通用样式 */
.config-panel,
.upload-section,
.status-section,
.history-section,
.log-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.config-panel h2,
.upload-section h2,
.status-section h2,
.history-section h2,
.log-section h2 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

/* 配置面板 */
.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.config-item {
  display: flex;
  flex-direction: column;
}

.config-item label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #555;
}

.config-item input,
.config-item select {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.3s;
}

.config-item input:focus,
.config-item select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.config-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* 状态面板 */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ddd;
}

.status-item.status-success {
  border-left-color: #28a745;
  background: #f8fff9;
}

.status-item.status-error {
  border-left-color: #dc3545;
  background: #fff8f8;
}

.status-label {
  font-weight: 500;
  color: #666;
}

.status-value {
  font-weight: 600;
  color: #2c3e50;
}

/* 文件列表 */
.file-list {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  transition: background-color 0.3s;
}

.file-item:hover {
  background: #f8f9fa;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.file-name {
  font-weight: 500;
  color: #2c3e50;
}

.file-size,
.file-time {
  font-size: 0.85rem;
  color: #666;
}

.file-actions {
  display: flex;
  gap: 0.5rem;
}

/* 日志面板 */
.log-container {
  max-height: 400px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
}

.log-item {
  display: flex;
  gap: 1rem;
  padding: 0.25rem 0;
  border-bottom: 1px solid #eee;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  white-space: nowrap;
}

.log-level {
  font-weight: 600;
  min-width: 60px;
}

.log-level.log-info {
  color: #17a2b8;
}

.log-level.log-success {
  color: #28a745;
}

.log-level.log-error {
  color: #dc3545;
}

.log-level.log-warning {
  color: #ffc107;
}

.log-message {
  flex: 1;
  word-break: break-word;
}

.log-actions {
  margin-top: 1rem;
  display: flex;
  gap: 1rem;
}

/* 按钮样式 */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.btn-link {
  background: transparent;
  color: #667eea;
  padding: 0.5rem 1rem;
}

.btn-link:hover {
  background: #f8f9fa;
  color: #5a6fd8;
}

.btn-ghost {
  background: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.btn-ghost:hover {
  background: #f8f9fa;
  border-color: #bbb;
}

/* 底部样式 */
.app-footer {
  background: #f8f9fa;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e1e5e9;
  text-align: center;
}

.footer-actions {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.app-footer p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-main {
    padding: 1rem;
  }
  
  .app-header {
    padding: 1.5rem 1rem;
  }
  
  .app-header h1 {
    font-size: 2rem;
  }
  
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .file-actions {
    align-self: stretch;
    justify-content: flex-end;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.config-panel,
.upload-section,
.status-section,
.history-section,
.log-section {
  animation: fadeIn 0.5s ease-out;
}
</style> 