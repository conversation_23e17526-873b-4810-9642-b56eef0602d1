# 🚀 OSS分片上传 - 快速启动指南

本指南将帮助您在5分钟内完成前后端集成配置，实现OSS分片上传功能。

## ⚡ 快速配置步骤

### 步骤1：配置环境变量

创建前端环境配置文件：

```bash
# 复制环境变量模板
cp frontend/env.example frontend/.env.local
```

编辑 `frontend/.env.local`：

```env
# 后端API地址
VUE_APP_API_BASE_URL=http://localhost:8080

# 启用调试和认证
VUE_APP_ENABLE_DEBUG=true
VUE_APP_ENABLE_AUTH=true
```

### 步骤2：启动后端服务

```bash
# 在项目根目录执行
mvn spring-boot:run
```

确保后端服务在 `http://localhost:8080` 运行正常。

### 步骤3：启动前端服务

```bash
# 进入前端目录
cd frontend

# 安装依赖（首次运行）
npm install

# 启动开发服务器
npm run serve
```

前端服务将在 `http://localhost:3000` 启动。

### 步骤4：测试上传功能

1. 打开浏览器访问 `http://localhost:3000`
2. 使用 `SimpleOssUploader` 组件测试文件上传
3. 查看浏览器控制台和网络请求确认功能正常

## 🔧 配置验证

### 检查后端API

访问以下URL确认后端API正常：

```bash
# 检查后端健康状态
curl http://localhost:8080/actuator/health

# 检查API端点（需要认证）
curl -H "Authorization: Bearer <your-token>" \
     http://localhost:8080/api/basic/upload/resumable/init
```

### 检查前端代理

在浏览器开发者工具的Network标签页中，确认：

1. 前端请求 `/api/*` 被正确代理到后端
2. 没有CORS错误
3. 认证头正确传递

## 🛠️ 常见问题解决

### 问题1：CORS跨域错误

**解决方案：**
1. 确认 `vue.config.js` 代理配置正确
2. 检查后端CORS配置
3. 重启前端开发服务器

### 问题2：认证失败（401/403）

**解决方案：**
1. 在组件配置中设置正确的Token
2. 确认Token格式：`Bearer <token>`
3. 检查Token是否过期

### 问题3：文件上传失败

**解决方案：**
1. 检查OSS配置和权限
2. 查看详细错误日志
3. 确认预签名URL格式正确

## 📝 快速测试代码

### 测试组件使用

```vue
<template>
  <div>
    <h1>OSS上传测试</h1>
    <SimpleOssUploader />
  </div>
</template>

<script>
import SimpleOssUploader from '@/components/SimpleOssUploader.vue'

export default {
  name: 'UploadTest',
  components: {
    SimpleOssUploader
  },
  mounted() {
    // 设置测试Token（如果需要）
    localStorage.setItem('token', 'your-test-token')
  }
}
</script>
```

### 测试编程式调用

```javascript
import { uploadFileWithResumable, setAuthToken } from '@/components/OssUploaderService.js'

// 设置认证
setAuthToken('your-test-token')

// 测试上传
async function testUpload() {
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.onchange = async (e) => {
    const file = e.target.files[0]
    if (file) {
      try {
        console.log('开始上传:', file.name)
        
        const result = await uploadFileWithResumable(file, {
          category: 'test',
          onProgress: (progress) => {
            console.log(`进度: ${progress}%`)
          }
        })
        
        console.log('上传成功:', result)
        alert(`上传成功！\n文件URL: ${result.fullUrl}`)
        
      } catch (error) {
        console.error('上传失败:', error)
        alert(`上传失败: ${error.message}`)
      }
    }
  }
  fileInput.click()
}

// 调用测试
testUpload()
```

## 🔍 调试技巧

### 启用详细日志

在组件中勾选"显示详细日志"选项，或在代码中设置：

```javascript
import { setApiConfig } from '@/components/OssUploaderService.js'

setApiConfig({
  debug: true,
  logLevel: 'debug'
})
```

### 网络请求监控

在浏览器开发者工具中监控以下请求：

1. **初始化请求**：`POST /api/basic/upload/resumable/init`
2. **预签名URL**：`GET /api/basic/upload/resumable/presigned-url/...`
3. **OSS上传**：`PUT https://bucket.oss-region.aliyuncs.com/...`
4. **完成上传**：`POST /api/basic/upload/resumable/complete/...`

### 错误分析

如果遇到403错误，系统会自动分析原因：

```javascript
// 403错误会自动触发详细分析
// 查看控制台输出的分析结果
```

## 📋 配置检查清单

- [ ] 后端服务正常运行（端口8080）
- [ ] 前端服务正常运行（端口3000）
- [ ] 环境变量配置正确
- [ ] 代理配置生效
- [ ] OSS配置和权限正确
- [ ] 认证Token设置正确
- [ ] 网络请求无CORS错误
- [ ] 文件上传功能正常

## 🎯 下一步

配置完成后，您可以：

1. 自定义上传参数（分片大小、并发数等）
2. 集成到您的业务系统中
3. 添加自定义的进度显示和错误处理
4. 配置生产环境的部署参数

## 📞 获取帮助

如果遇到问题：

1. 查看详细的错误日志
2. 检查网络请求和响应
3. 参考完整的README文档
4. 联系开发团队获取支持

---

**恭喜！** 您已经成功配置了OSS分片上传功能。现在可以开始使用这个强大的文件上传解决方案了！ 