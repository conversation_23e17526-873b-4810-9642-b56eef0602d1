{"name": "oss-uploader-frontend", "version": "1.0.0", "description": "OSS分片上传前端组件", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"core-js": "^3.8.3", "vue": "^3.2.13"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-pwa": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "@vue/test-utils": "^2.0.0-0", "@vue/vue3-jest": "^27.0.0-alpha.1", "babel-jest": "^27.0.6", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^8.0.3", "jest": "^27.0.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "@vue/standard"], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false}, "rules": {"no-console": "off", "no-debugger": "off"}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"jest": true}}]}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "jest": {"preset": "@vue/cli-plugin-unit-jest"}, "keywords": ["oss", "upload", "chunk", "resumable", "vue", "frontend"], "author": "Your Team", "license": "MIT", "repository": {"type": "git", "url": "your-repository-url"}, "bugs": {"url": "your-issues-url"}, "homepage": "your-homepage-url"}