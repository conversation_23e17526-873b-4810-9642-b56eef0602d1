# OSS分片上传前端实现

## 概述

这是一个完整的OSS分片上传前端实现，支持大文件的分片上传、进度显示、断点续传和错误处理。

## 文件说明

### 1. OssUploadExample.html
主要的上传示例页面，包含完整的分片上传功能：
- 文件选择和拖拽上传
- 实时进度显示
- 上传速度和剩余时间估算
- 取消上传功能
- 详细的日志记录

### 2. ApiTest.html
API接口测试页面，用于验证后端接口的可用性：
- 基础连接测试
- 初始化分片上传测试
- 获取预签名URL测试
- 完整流程测试

### 3. test-api.js
Node.js测试脚本，可以在命令行中测试API接口：
```bash
node test-api.js
```

## 后端配置修改

### 1. CORS配置
已添加 `src/main/java/com/extracme/saas/autocare/config/CorsConfig.java`：
- 允许所有来源的跨域请求
- 支持所有HTTP方法
- 暴露必要的响应头（如ETag）

### 2. 安全配置更新
已更新 `src/main/java/com/extracme/saas/autocare/config/WebSecurityConfig.java`：
- 集成CORS配置
- 保持现有的安全策略

## API接口配置

### 正确的接口路径
```javascript
const API_CONFIG = {
    baseUrl: 'http://localhost:30030/auto-care-saas',
    endpoints: {
        initMultipart: '/api/v1/basic/upload/resumable/init',
        getPresignedUrl: '/api/v1/basic/upload/resumable/presigned-url',
        completeMultipart: '/api/v1/basic/upload/resumable/complete'
    }
}
```

### 认证配置
已硬编码测试用JWT token：
```javascript
function getAuthToken() {
    return 'Bearer eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDkyNjM3ODEsInVzZXJJZCI6IjIiLCJpYXQiOjE3NDkxNzczODF9.J2aiO3poRFfCWAUTcTBlddgAXu4Bojh7VpaB7p8Hnpw'
}
```

## 使用步骤

### 1. 启动后端服务
确保后端服务在 `http://localhost:30030` 运行

### 2. 测试API连接
打开 `ApiTest.html` 页面，点击各个测试按钮验证接口可用性

### 3. 使用上传功能
打开 `OssUploadExample.html` 页面：
1. 点击"测试后端连接"验证API
2. 选择文件或拖拽文件开始上传
3. 查看实时进度和日志

## 功能特性

### 分片上传
- 默认分片大小：5MB
- 支持并发上传（3个并发）
- 自动重试机制

### 进度显示
- 总体进度条
- 单个分片进度
- 上传速度显示
- 剩余时间估算

### 错误处理
- 网络错误捕获
- HTTP状态码检查
- 用户友好的错误提示
- 详细的错误日志

### 用户体验
- 文件拖拽支持
- 取消上传功能
- 防重复上传
- 清晰的状态反馈

## 故障排除

### 1. 连接失败
检查项目：
- 后端服务是否启动
- 端口是否正确（30030）
- CORS配置是否生效

### 2. 认证失败
检查项目：
- JWT token是否有效
- token格式是否正确
- 用户权限是否足够

### 3. 上传失败
检查项目：
- OSS配置是否正确
- 网络连接是否稳定
- 文件大小是否超限

## 开发说明

### 自定义配置
如需修改配置，请更新以下部分：
- API_CONFIG：后端地址和接口路径
- getAuthToken()：认证token获取方式
- 分片大小：默认5MB，可根据需要调整

### 扩展功能
可以添加的功能：
- 断点续传
- 文件类型限制
- 上传队列管理
- 批量上传

## 技术栈

- 原生JavaScript (ES6+)
- Fetch API
- XMLHttpRequest (用于上传进度)
- Promise/async-await
- 现代CSS样式
