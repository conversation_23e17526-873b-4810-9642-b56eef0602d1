# 前端环境变量配置示例
# 复制此文件为 .env.local 或 .env.development 并修改相应配置

# ==================== API配置 ====================

# API基础URL - 后端服务地址
VUE_APP_API_BASE_URL=http://localhost:8080

# API请求超时时间（毫秒）
VUE_APP_API_TIMEOUT=30000

# ==================== 调试配置 ====================

# 是否启用调试模式
VUE_APP_ENABLE_DEBUG=true

# 是否启用详细日志
VUE_APP_ENABLE_VERBOSE_LOGS=true

# ==================== 认证配置 ====================

# 是否启用认证
VUE_APP_ENABLE_AUTH=true

# 认证Token存储键名
VUE_APP_AUTH_TOKEN_KEY=token

# ==================== 上传配置 ====================

# 默认分片大小（字节）
VUE_APP_DEFAULT_CHUNK_SIZE=5242880

# 最大文件大小（字节）
VUE_APP_MAX_FILE_SIZE=104857600

# 默认并发上传数
VUE_APP_DEFAULT_CONCURRENCY=3

# ==================== 开发环境特殊配置 ====================

# 开发环境下是否跳过SSL验证
VUE_APP_DEV_SKIP_SSL=true

# 开发环境下是否启用Mock数据
VUE_APP_DEV_ENABLE_MOCK=false

# ==================== 生产环境配置 ====================

# 生产环境API地址（留空使用相对路径）
VUE_APP_PROD_API_BASE_URL=

# 生产环境是否启用调试
VUE_APP_PROD_ENABLE_DEBUG=false 