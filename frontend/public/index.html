<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>OSS分片上传系统</title>
    <meta name="description" content="支持大文件断点续传的OSS分片上传前端解决方案">
    <meta name="keywords" content="OSS,上传,分片,断点续传,Vue">
    <style>
      /* 加载动画 */
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }
      
      .loading-content {
        text-align: center;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem auto;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 重置样式 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f5f5f5;
      }
    </style>
  </head>
  <body>
    <noscript>
      <div style="text-align: center; padding: 2rem; background: #fff3cd; color: #856404; border: 1px solid #ffeaa7;">
        <h2>JavaScript未启用</h2>
        <p>此应用需要JavaScript才能正常运行，请在浏览器中启用JavaScript。</p>
      </div>
    </noscript>
    
    <div id="app">
      <!-- 加载动画 -->
      <div class="loading">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <h2>OSS分片上传系统</h2>
          <p>正在加载中...</p>
        </div>
      </div>
    </div>
    
    <!-- 构建时会自动注入脚本 -->
  </body>
</html> 