# Mybatis开发规范

## 1. Model规范
- 实体类必须由mybatis-generator生成，禁止手动编写
- 实体类应放在model/entity目录下
- 在编写查询前必须仔细阅读对应的实体类定义
- 禁止在查询中使用实体类中不存在的字段

## 2. Mapper规范
### 命名规范
- 基础Mapper: {EntityName}Mapper (自动生成)
- 扩展Mapper: {EntityName}ExtendMapper (手动编写)
- 动态SQL支持类: {EntityName}DynamicSqlSupport (自动生成)

### 规则要求
- 基础Mapper和DynamicSqlSupport必须由mybatis-generator生成，禁止手动编写
- 禁止使用XML配置文件
- ExtendMapper仅用于定义复杂查询方法接口，具体实现在Repository中完成
- 禁止在ExtendMapper中重复定义基础Mapper已有的方法（如selectOne、selectMany等）
- 所有查询必须使用Dynamic SQL
- ExtendMapper中的方法必须有明确的业务含义
- 使用循环处理替代批量操作

### 示例代码（仅展示需要手动编写的部分）
```java
// 扩展Mapper示例 - 需要手动编写
@Mapper
public interface UserExtendMapper extends UserMapper {
    // 只定义业务方法接口，不包含实现
    List<User> findByDepartment(Long departmentId);
    boolean hasPermission(Long userId, String permCode);
}
```

## 3. Repository规范
### 命名规范
- 接口: Table{EntityName}Service
- 实现类: Table{EntityName}ServiceImpl

### 规则要求
- 继承DefaultTableService，根据需求实现DefaultTableService定义的方法
- 在编写实现前，必须先阅读相关实体类的定义
- 使用@Autowired进行依赖注入
- 禁止直接使用Mapper访问数据库
- 事务控制在Service层处理
- 使用MyBatis Dynamic SQL构建查询
- 查询条件中的字段必须与实体类定义匹配
- 空集合返回应使用Collections.emptyList()
- 计数查询应使用count()方法而不是实体查询
- 查询方法必须处理空结果情况

### 示例代码
```java
// 首先查看实体类定义
@Generated("org.mybatis.generator.api.MyBatisGenerator")
public class User {
    private Long id;
    private String username;
    private Long departmentId;
    private Boolean isActive;  // 注意字段名称和类型
    // ... 其他字段
}

// Repository实现

public interface TableUserServiceImpl extends DefaultTableService<User, Long> {
    List<User> findByDepartment(Long departmentId);
}

@Repository
public class TableUserServiceImpl implements TableUserService {
    @Autowired
    private UserExtendMapper userExtendMapper;
    
    @Override
    public List<User> findByDepartment(Long departmentId) {
        // 使用实体类中存在的字段构建查询
        SelectStatementProvider selectStatement = select(user.allColumns())
            .from(user)
            .where(user.departmentId, isEqualTo(departmentId))
            .and(user.isActive, isEqualTo(true))  // 使用正确的字段名
            .build()
            .render(RenderingStrategies.MYBATIS3);
        
        return Optional.ofNullable(userExtendMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }
    
    @Override
    public boolean hasPermission(Long userId, String permCode) {
        SelectStatementProvider selectStatement = select(count())  // 使用count而不是实体
            .from(userPermission)
            .where(userPermission.userId, isEqualTo(userId))
            .and(userPermission.permissionCode, isEqualTo(permCode))
            .build()
            .render(RenderingStrategies.MYBATIS3);
            
        return Optional.ofNullable(userExtendMapper.selectOne(selectStatement))
            .map(count -> (Long)count > 0)  // 确保类型转换
            .orElse(false);
    }
}
```

## 4. Service规范
### 命名规范
- 接口: {EntityName}Service
- 实现类: {EntityName}ServiceImpl

### 规则要求
- 必须通过Repository访问数据库
- 处理业务逻辑和事务控制

### 示例代码
```java
@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private TableUserService tableUserService;
    
    @Override
    public List<User> findByDepartment(Long departmentId) {
        return tableUserService.findByDepartment(departmentId);
    }
}
```

## 5. 目录结构
```
src/main/java/com/example
├── model
│   └── entity
│       └── User.java                    (自动生成)
├── mapper
│   ├── base
│   │   └── UserMapper.java             (自动生成)
│   ├── extend
│   │   └── UserExtendMapper.java       (手动编写)
│   └── support
│       └── UserDynamicSqlSupport.java  (自动生成)
├── repository
│   ├── TableUserService.java        (手动编写)
│   └── impl
│       └── TableUserServiceImpl.java (手动编写)
└── service
    ├── UserService.java                (手动编写)
    └── impl
        └── UserServiceImpl.java        (手动编写)
```

## 6. 代码检查清单
- 禁止使用XML配置文件，应采用MyBatis Dynamic SQL构建查询
- 检查是否存在手动生成的model类（禁止）
- 检查是否存在手动生成的mapper.base（禁止）
- 检查是否存在手动生成的DynamicSqlSupport类（禁止）
- 检查ExtendMapper是否只包含业务方法定义
- 检查ExtendMapper是否重复定义基础方法
- 检查查询条件中的字段是否都在实体类中定义
- 检查字段名称和类型是否与实体类匹配
- 检查Repository是否正确处理空结果情况
- 检查计数查询是否使用count()而不是实体
- 检查Service是否添加了合适的事务注解

## 7. 开发流程
1. 首先通过mybatis-generator生成实体类和基础Mapper
2. 仔细阅读生成的实体类定义，了解所有字段
3. 根据业务需求定义ExtendMapper接口方法
4. 在Repository中实现查询逻辑，确保使用实体类中存在的字段
5. 在Service层添加事务控制和业务逻辑 

## 8. Dynamic SQL 查询规范

### 8.1 查询标准模式

在使用 MyBatis Dynamic SQL 进行查询操作时，应遵循以下标准模式，特别是对于包含可选条件和模糊查询的场景：

```java
@Override
public List<SysUser> findMerchantAdmins(String merchantName, String adminName, String mobile, Integer status) {
    // 构建查询条件
    List<SysUser> users = userExtendMapper.select(c -> {
        // 设置必要的基础条件
        c.where(sysUser.accountType, isEqualTo((byte) 1)); // 账号类型为1表示商户管理员
        
        // 使用isLikeWhenPresent处理可选的模糊查询条件
        // 当参数不为空时才会添加此条件，避免了大量的if判断
        c.and(sysUser.nickname, isLikeWhenPresent(transFuzzyQueryParam(adminName)));
        c.and(sysUser.mobile, isLikeWhenPresent(transFuzzyQueryParam(mobile)));
        
        // 使用isEqualToWhenPresent处理可选的精确匹配条件
        // 当参数不为null时才会添加此条件
        c.and(sysUser.status, isEqualToWhenPresent(status));
        
        // 设置排序
        c.orderBy(sysUser.id.descending());
        return c;
    });
    
    // 如果需要进一步处理结果，可以在这里进行
    if (StringUtils.hasText(merchantName)) {
        return users.stream()
            .filter(user -> {
                if (user.getTenantId() == null) {
                    return false;
                }
                
                // 处理关联查询
                SysTenant tenant = tableTenantService.selectById(user.getTenantId());
                if (tenant == null || tenant.getTenantName() == null) {
                    return false;
                }
                return tenant.getTenantName().contains(merchantName);
            })
            .collect(Collectors.toList());
    }
    
    return users;
}

/**
 * 转换模糊查询参数，添加前后百分号
 * 
 * @param param 原始参数
 * @return 转换后的参数，如果原始参数为空则返回null
 */
private String transFuzzyQueryParam(String param) {
    return StringUtils.hasText(param) ? "%" + param + "%" : null;
}
```

### 8.2 最佳实践说明

1. **使用 `isLikeWhenPresent` 和 `isEqualToWhenPresent`**：
   - 这些方法会自动处理参数为null或空的情况，只有当参数有值时才会添加查询条件
   - 避免了大量的if判断，使代码更加简洁清晰
   - 减少了出错的可能性

2. **使用 `transFuzzyQueryParam` 方法处理模糊查询参数**：
   - 封装了添加前后百分号的逻辑，使代码更加统一
   - 当参数为空时返回null，配合 `isLikeWhenPresent` 使用，避免添加无效条件

3. **分离SQL查询和内存处理**：
   - 对于无法通过SQL直接查询的条件（如关联查询），可以先获取基础数据，再在内存中进行过滤
   - 这种方式在数据量不大的情况下效率更高，避免了复杂的多表连接查询

4. **使用方法链式调用**：
   - 使用 `c.where().and().and()` 的链式调用方式，使代码结构清晰
   - 便于阅读和维护

5. **明确的排序条件**：
   - 始终添加明确的排序条件，避免结果顺序不确定 