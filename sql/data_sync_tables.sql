-- 多租户数据同步服务相关表结构
-- 数据库：auto_care_saas

-- 1. 数据同步日志表（系统级表，不按租户分表）
CREATE TABLE `sys_data_sync_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_no` varchar(64) NOT NULL COMMENT '同步批次号',
  `target_table` varchar(64) NOT NULL COMMENT '目标表名',
  `sync_status` varchar(16) NOT NULL COMMENT '同步状态：SUCCESS、FAILED、PROCESSING',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '失败原因',
  `source_ip` varchar(64) DEFAULT NULL COMMENT '请求来源IP',
  `sync_start_time` datetime NOT NULL COMMENT '同步开始时间',
  `sync_end_time` datetime DEFAULT NULL COMMENT '同步结束时间',
  `sync_duration` bigint(20) DEFAULT NULL COMMENT '同步耗时（毫秒）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`) COMMENT '批次号索引',
  KEY `idx_tenant_id` (`tenant_id`) COMMENT '租户ID索引',
  KEY `idx_tenant_table` (`tenant_code`, `target_table`) COMMENT '租户表名复合索引',
  KEY `idx_sync_status` (`sync_status`) COMMENT '同步状态索引',
  KEY `idx_sync_time` (`sync_start_time`) COMMENT '同步时间索引',
  KEY `idx_source_data` (`source_data_id`) COMMENT '源数据标识索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步日志表';

-- 2. 租户同步密钥管理表（系统级表，不需要租户隔离）
CREATE TABLE `sys_tenant_sync_key` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `tenant_code` varchar(32) NOT NULL COMMENT '租户编码',
  `sync_key` varchar(128) NOT NULL COMMENT '同步密钥（用于租户身份识别）',
  `encryption_key` varchar(512) DEFAULT NULL COMMENT '加密密钥（Base64编码，已废弃）',
  `key_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '密钥状态：1-启用，0-禁用',
  `key_generate_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '密钥生成时间',
  `key_expire_time` datetime DEFAULT NULL COMMENT '密钥过期时间（NULL表示永不过期）',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `usage_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '使用次数',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_id` (`tenant_id`) COMMENT '租户ID唯一索引',
  UNIQUE KEY `uk_tenant_code` (`tenant_code`) COMMENT '租户编码唯一索引',
  UNIQUE KEY `uk_sync_key` (`sync_key`) COMMENT '同步密钥唯一索引',
  KEY `idx_key_status` (`key_status`) COMMENT '密钥状态索引',
  KEY `idx_last_used` (`last_used_time`) COMMENT '最后使用时间索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户同步密钥管理表';

-- 插入示例数据（可选）
-- 注意：实际使用时需要根据真实的租户信息和生成的同步密钥进行插入

-- 示例：为租户ID为1的租户生成同步密钥
-- INSERT INTO `sys_tenant_sync_key` (`tenant_id`, `tenant_code`, `sync_key`, `key_status`, `remark`, `create_by`)
-- VALUES (1, 'TENANT_001', 'SYNC_KEY_TENANT_001_2024', 1, '租户001的数据同步密钥', 'system');

-- 创建数据库视图（可选）- 用于监控同步状态
CREATE VIEW `v_data_sync_summary` AS
SELECT
    `target_table`,
    DATE(`sync_start_time`) AS `sync_date`,
    COUNT(*) AS `total_count`,
    SUM(CASE WHEN `sync_status` = 'SUCCESS' THEN 1 ELSE 0 END) AS `success_count`,
    SUM(CASE WHEN `sync_status` = 'FAILED' THEN 1 ELSE 0 END) AS `failed_count`,
    AVG(`sync_duration`) AS `avg_duration`,
    MAX(`sync_start_time`) AS `last_sync_time`
FROM `sys_data_sync_log`
WHERE `sync_start_time` >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY `target_table`, DATE(`sync_start_time`)
ORDER BY `sync_date` DESC, `target_table`;

-- 创建存储过程：清理过期的同步日志
DELIMITER ;;
CREATE PROCEDURE `sp_clean_expired_sync_logs`(IN `days_to_keep` INT)
BEGIN
    DECLARE `expire_date` DATETIME;
    DECLARE `deleted_count` INT DEFAULT 0;
    
    -- 参数验证
    IF `days_to_keep` <= 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '保留天数必须大于0';
    END IF;
    
    -- 计算过期时间
    SET `expire_date` = DATE_SUB(NOW(), INTERVAL `days_to_keep` DAY);
    
    -- 删除过期记录
    DELETE FROM `sys_data_sync_log`
    WHERE `create_time` < `expire_date`;
    
    -- 获取删除的记录数
    SET `deleted_count` = ROW_COUNT();
    
    -- 返回结果
    SELECT `deleted_count` AS `deleted_records`, `expire_date` AS `expire_before`;
END;;
DELIMITER ;

-- 创建定时任务（可选）- 每天凌晨2点清理30天前的日志
-- 注意：需要开启事件调度器：SET GLOBAL event_scheduler = ON;
/*
CREATE EVENT `evt_clean_sync_logs`
ON SCHEDULE EVERY 1 DAY
STARTS '2024-01-01 02:00:00'
DO
  CALL `sp_clean_expired_sync_logs`(30);
*/

-- 创建索引优化查询性能
-- 复合索引：表+状态+时间
CREATE INDEX `idx_table_status_time` ON `sys_data_sync_log` (`target_table`, `sync_status`, `sync_start_time`);

-- 复合索引：批次号+状态
CREATE INDEX `idx_batch_status` ON `sys_data_sync_log` (`batch_no`, `sync_status`);

-- 为租户密钥表创建过期时间索引
CREATE INDEX `idx_key_expire` ON `sys_tenant_sync_key` (`key_expire_time`);

-- 权限设置建议（根据实际情况调整）
-- 为数据同步服务创建专用数据库用户
-- CREATE USER 'data_sync_user'@'%' IDENTIFIED BY 'strong_password_here';
-- GRANT SELECT, INSERT, UPDATE ON auto_care_saas.sys_data_sync_log TO 'data_sync_user'@'%';
-- GRANT SELECT, UPDATE ON auto_care_saas.sys_tenant_sync_key TO 'data_sync_user'@'%';
-- GRANT SELECT ON auto_care_saas.mtc_* TO 'data_sync_user'@'%';  -- 允许查询多租户表
-- FLUSH PRIVILEGES;

-- 表结构说明：
-- 1. sys_data_sync_log表为系统级表，不按租户分表，记录所有租户的数据同步日志
-- 2. sys_tenant_sync_key表为系统级表，存储所有租户的密钥信息
-- 3. 所有表都包含完整的审计字段（create_time, create_by, update_time, update_by）
-- 4. 使用utf8mb4字符集支持emoji和特殊字符
-- 5. 创建了必要的索引以优化查询性能
-- 6. 提供了数据清理的存储过程和定时任务
-- 7. 包含了监控视图用于统计同步状态
