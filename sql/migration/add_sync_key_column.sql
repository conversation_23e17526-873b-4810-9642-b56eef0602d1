-- 数据同步功能升级：添加sync_key字段
-- 执行时间：2024年
-- 说明：为sys_tenant_sync_key表添加sync_key字段，用于替代加密密钥进行租户识别

-- 1. 为sys_tenant_sync_key表添加sync_key字段
ALTER TABLE `sys_tenant_sync_key` 
ADD COLUMN `sync_key` varchar(128) NOT NULL COMMENT '同步密钥（用于租户身份识别）' AFTER `tenant_code`;

-- 2. 添加sync_key字段的唯一索引
ALTER TABLE `sys_tenant_sync_key` 
ADD UNIQUE KEY `uk_sync_key` (`sync_key`) COMMENT '同步密钥唯一索引';

-- 3. 修改encryption_key字段为可空（已废弃）
ALTER TABLE `sys_tenant_sync_key` 
MODIFY COLUMN `encryption_key` varchar(512) DEFAULT NULL COMMENT '加密密钥（Base64编码，已废弃）';

-- 4. 为现有数据生成sync_key（如果表中已有数据）
-- 注意：这里使用租户编码和时间戳生成临时的sync_key，实际使用时应该通过程序生成更安全的密钥
UPDATE `sys_tenant_sync_key` 
SET `sync_key` = CONCAT('SYNC_KEY_', `tenant_code`, '_', UNIX_TIMESTAMP(NOW()), '_TEMP')
WHERE `sync_key` IS NULL OR `sync_key` = '';

-- 5. 验证数据完整性
-- 检查是否所有记录都有sync_key
SELECT COUNT(*) as total_records, 
       COUNT(sync_key) as records_with_sync_key,
       COUNT(*) - COUNT(sync_key) as records_without_sync_key
FROM `sys_tenant_sync_key`;

-- 6. 显示更新后的表结构
DESCRIBE `sys_tenant_sync_key`;

-- 执行完成后的注意事项：
-- 1. 请通过TenantIdentificationService.generateSyncKey()方法为每个租户重新生成正式的同步密钥
-- 2. 更新第三方系统的配置，使用新的syncKey参数替代原来的加密参数
-- 3. 测试新的数据同步接口，确保租户识别功能正常工作
-- 4. 可以考虑在后续版本中删除encryption_key字段（如果确认不再使用）
