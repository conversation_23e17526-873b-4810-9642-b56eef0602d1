# OSS集成测试环境变量配置示例
# 复制此文件为 .env 并填入真实的OSS配置信息

# ==================== OSS配置 ====================

# OSS Access Key ID（必需）
# 从阿里云控制台获取：https://ram.console.aliyun.com/manage/ak
OSS_ACCESS_KEY_ID=your-real-access-key-id

# OSS Access Key Secret（必需）
# 从阿里云控制台获取：https://ram.console.aliyun.com/manage/ak
OSS_ACCESS_KEY_SECRET=your-real-access-key-secret

# OSS Endpoint（必需）
# 根据您的Bucket所在地域设置，例如：
# 华东1（杭州）：https://oss-cn-hangzhou.aliyuncs.com
# 华东2（上海）：https://oss-cn-shanghai.aliyuncs.com
# 华北1（青岛）：https://oss-cn-qingdao.aliyuncs.com
# 华北2（北京）：https://oss-cn-beijing.aliyuncs.com
# 华南1（深圳）：https://oss-cn-shenzhen.aliyuncs.com
OSS_ENDPOINT=https://oss-cn-shanghai.aliyuncs.com

# OSS Bucket名称（必需）
# 用于测试的Bucket名称，建议使用专门的测试Bucket
OSS_BUCKET_NAME=your-test-bucket-name

# OSS Base URL（必需）
# 文件访问的基础URL，格式：https://{bucket-name}.{endpoint-domain}
OSS_BASE_URL=https://your-test-bucket-name.oss-cn-shanghai.aliyuncs.com

# ==================== 使用说明 ====================

# 1. 复制此文件为 .env：
#    cp .env.example .env

# 2. 编辑 .env 文件，填入真实的OSS配置信息

# 3. 加载环境变量：
#    source .env

# 4. 运行集成测试：
#    ./scripts/run-oss-integration-test.sh
#    或
#    mvn test -Dtest=OssServiceIntegrationTest

# ==================== 权限要求 ====================

# 确保您的OSS Access Key具有以下权限：
# - oss:PutObject          (上传文件)
# - oss:GetObject          (下载文件)
# - oss:DeleteObject       (删除文件，用于测试清理)
# - oss:InitiateMultipartUpload    (初始化分片上传)
# - oss:UploadPart         (上传分片)
# - oss:CompleteMultipartUpload    (完成分片上传)
# - oss:AbortMultipartUpload       (取消分片上传)

# ==================== 安全提醒 ====================

# ⚠️ 重要：
# 1. 不要将 .env 文件提交到版本控制系统
# 2. 不要在代码中硬编码Access Key信息
# 3. 定期轮换Access Key
# 4. 使用最小权限原则设置OSS权限
# 5. 建议使用专门的测试Bucket，避免影响生产数据

# ==================== 故障排除 ====================

# 如果遇到问题，请检查：
# 1. Access Key ID和Secret是否正确
# 2. Bucket是否存在且在指定地域
# 3. 网络连接是否正常
# 4. 权限设置是否正确
# 5. Endpoint地址是否与Bucket地域匹配
