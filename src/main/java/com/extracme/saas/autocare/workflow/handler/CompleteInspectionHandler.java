package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 检查完成处理器
 * 用于处理车辆检查完成后的工作流转换
 */
@Slf4j
@Component
public class CompleteInspectionHandler implements ActivityTransitionHandler {

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handle(WorkflowContext context) {
        // 1. 获取当前流程实例
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();

        try {
            // 2. 状态校验
            validateInspectionStatus(businessId);
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);

            // 3. 更新流程实例状态
            // 这里可以更新维修任务的检查时间
            mtcRepairTask.setVehicleCheckTime(new Date());
            tableRepairTaskService.updateSelectiveById(mtcRepairTask);

            // 4. 记录活动日志
            MtcOperatorLog newOperateLog = new MtcOperatorLog();
            newOperateLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            newOperateLog.setRecordId(Long.parseLong(businessId));
            newOperateLog.setOpeContent("完成车辆检查");
            newOperateLog.setRemark("车辆检查");
            newOperateLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(newOperateLog);

            // 5. 通知外部系统
            notifyExternalSystems(businessId);
        } catch (Exception e) {
            throw new WorkflowException(-1, "处理车辆检查完成事件失败: " + e.getMessage());
        }
    }

    /**
     * 验证检查状态
     * 
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateInspectionStatus(String businessId) throws WorkflowException {
        // 验证当前状态是否允许完成检查
        // 例如：检查必要的检查项是否已完成
        // 如果验证失败，抛出 WorkflowException
    }

    /**
     * 通知外部系统
     * 
     * @param businessId 业务ID
     */
    private void notifyExternalSystems(String businessId) {
        // 通知相关系统车辆检查已完成
        // externalSystemNotifier.notifyWutong(businessId, "INSPECTION_COMPLETE");
    }
}
