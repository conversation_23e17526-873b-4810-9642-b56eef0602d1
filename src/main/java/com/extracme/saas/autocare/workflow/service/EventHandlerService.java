package com.extracme.saas.autocare.workflow.service;

import com.extracme.saas.autocare.workflow.core.WorkflowContext;

/**
 * 事件处理器服务接口
 * 
 * <AUTHOR>
 * @date 2024/05/15
 */
public interface EventHandlerService {

    /**
     * 处理事件
     *
     * @param context 工作流上下文
     * @param tenantId 租户ID
     * @return 处理结果
     */
    boolean handleEvent(WorkflowContext context, String tenantId);

    /**
     * 获取支持的事件处理器列表
     *
     * @param eventType 事件类型
     * @param taskType 任务类型
     * @param tenantId 租户ID
     * @return 处理器名称列表
     */
    String[] getSupportedHandlers(String eventType, Integer taskType, String tenantId);
}
