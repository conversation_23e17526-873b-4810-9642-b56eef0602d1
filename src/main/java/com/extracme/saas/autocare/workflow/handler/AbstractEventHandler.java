package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

/**
 * 抽象事件处理器
 * 实现DefaultEventHandler接口的通用逻辑，提供模板方法和钩子方法
 * 
 * <AUTHOR>
 * @date 2024/05/15
 */
@Slf4j
public abstract class AbstractEventHandler implements DefaultEventHandler {

    /**
     * 默认优先级
     */
    private static final int DEFAULT_PRIORITY = 100;

    /**
     * 处理事件的模板方法
     *
     * @param context 工作流上下文
     * @param tenantId 租户ID
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handle(WorkflowContext context, String tenantId) {
        String eventType = context.getTriggerEvent();
        WorkflowInstance instance = context.getWorkflowInstance();
        
        if (instance == null) {
            log.error("工作流实例为空，无法处理事件: {}", eventType);
            return false;
        }
        
        String businessId = instance.getBusinessId();
        log.info("开始处理事件: {}, 业务ID: {}, 租户ID: {}", eventType, businessId, tenantId);
        
        try {
            // 1. 前置处理
            beforeHandle(context, tenantId);
            
            // 2. 执行业务逻辑
            doHandle(context, tenantId);
            
            // 3. 后置处理
            afterHandle(context, tenantId);
            
            log.info("事件处理成功: {}, 业务ID: {}, 租户ID: {}", eventType, businessId, tenantId);
            return true;
        } catch (WorkflowException e) {
            log.error("事件处理失败: {}, 业务ID: {}, 租户ID: {}, 错误: {}", 
                    eventType, businessId, tenantId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("事件处理异常: {}, 业务ID: {}, 租户ID: {}", 
                    eventType, businessId, tenantId, e);
            throw new WorkflowException(-1, "事件处理异常: " + e.getMessage());
        }
    }

    /**
     * 判断是否可以处理指定事件
     *
     * @param eventType 事件类型
     * @param taskType 任务类型
     * @return 是否可以处理
     */
    @Override
    public boolean canHandle(String eventType, Integer taskType) {
        // 事件类型匹配
        boolean eventTypeMatch = StringUtils.equals(eventType, getSupportedEventType());
        
        // 任务类型匹配
        boolean taskTypeMatch = taskType != null && taskType.equals(getSupportedTaskType());
        
        return eventTypeMatch && taskTypeMatch;
    }

    /**
     * 获取处理器优先级，默认为100
     * 子类可以覆盖此方法提供自定义优先级
     *
     * @return 优先级
     */
    @Override
    public int getPriority() {
        return DEFAULT_PRIORITY;
    }

    /**
     * 前置处理，在主处理逻辑之前执行
     * 子类可以覆盖此方法提供自定义前置处理逻辑
     *
     * @param context 工作流上下文
     * @param tenantId 租户ID
     */
    protected void beforeHandle(WorkflowContext context, String tenantId) {
        // 默认实现为空，子类可以覆盖
    }

    /**
     * 执行业务逻辑，子类必须实现此方法
     *
     * @param context 工作流上下文
     * @param tenantId 租户ID
     */
    protected abstract void doHandle(WorkflowContext context, String tenantId);

    /**
     * 后置处理，在主处理逻辑之后执行
     * 子类可以覆盖此方法提供自定义后置处理逻辑
     *
     * @param context 工作流上下文
     * @param tenantId 租户ID
     */
    protected void afterHandle(WorkflowContext context, String tenantId) {
        // 默认实现为空，子类可以覆盖
    }
}
