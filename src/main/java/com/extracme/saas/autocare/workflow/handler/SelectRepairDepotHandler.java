package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityDefinitionEnum;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 选择修理厂处理器
 * 处理选择修理厂事件的工作流处理器
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Slf4j
@Component
public class SelectRepairDepotHandler extends AbstractEventHandler {

    /**
     * 事件类型：选择修理厂
     */
    private static final String EVENT_TYPE_SELECT_REPAIR_DEPOT = "SELECT_REPAIR_DEPOT";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    /**
     * 验证状态
     *
     * @param businessId 业务ID（任务编号）
     * @throws WorkflowException 工作流异常
     */
    private void validateStatus(String businessId) throws WorkflowException {
        // 检查工作流实例是否存在
        WorkflowInstance instance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (instance == null) {
            throw new WorkflowException(-1, "未找到工作流实例：" + businessId);
        }

        // 检查当前节点是否处于待分配修理厂状态，且状态为"UNPROCESSED"
        if (!instance.getCurrentActivityCode().equals(ActivityDefinitionEnum.PENDING_ASSIGNMENT.getCode()) ||
                !instance.getStatusCode().equals(ActivityStatusEnum.UNPROCESSED.getCode())) {
            throw new WorkflowException(-1, "当前节点状态不允许选择修理厂");
        }
    }

    /**
     * 通知外部系统
     *
     * @param businessId 业务ID
     */
    private void notifyExternalSystems(String businessId) {
        // 通知相关系统已选择修理厂
        log.info("通知外部系统已选择修理厂：{}", businessId);
        // 这里可以添加实际的外部系统通知逻辑
        // 例如：调用外部API通知梧桐系统、保险系统等
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_SELECT_REPAIR_DEPOT;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "选择修理厂处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();

        log.info("选择修理厂处理器处理事件: {}, 业务ID: {}", getSupportedEventType(), businessId);

        try {
            // 1. 状态校验
            validateStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 3. 验证修理厂信息是否已设置
            if (mtcRepairTask.getRepairDepotId() == null || mtcRepairTask.getRepairDepotName() == null) {
                throw new WorkflowException(-1, "维修任务未设置修理厂信息");
            }

            // 4. 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setRecordId(mtcRepairTask.getId());
            operatorLog.setOpeContent("选择 修理厂");
            operatorLog.setRemark("选择修理厂: " + mtcRepairTask.getRepairDepotName());
            operatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            operatorLog.setCreatedTime(new Date());
            operatorLog.setCreateBy(SessionUtils.getNickname());
            tableOperatorLogService.insertSelective(operatorLog);

            // 5. 通知外部系统
            notifyExternalSystems(businessId);

            log.info("选择修理厂事件处理成功: {}", businessId);
        } catch (Exception e) {
            log.error("处理选择修理厂事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理选择修理厂事件失败：" + e.getMessage());
        }
    }
}