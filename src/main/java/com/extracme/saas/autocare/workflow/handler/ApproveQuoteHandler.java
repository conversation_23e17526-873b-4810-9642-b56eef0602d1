package com.extracme.saas.autocare.workflow.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.dto.JingYouLossNotify;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.JingYouService;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 核损核价审核通过处理器
 * 处理核损核价活动节点审核通过流转至车辆维修的逻辑
 */
@Slf4j
@Component
public class ApproveQuoteHandler extends AbstractEventHandler {

    /**
     * 事件类型：审核通过
     */
    private static final String EVENT_TYPE_APPROVE_QUOTE = "APPROVE_QUOTE";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private JingYouService jingYouService;

    /**
     * 验证核损核价状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateApprovalStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于核损核价环节
        if (!workflowInstance.getCurrentActivityCode().equals("LOSS_ASSESSMENT")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许核损核价审核通过");
        }
        // 验证是否核损核价的处理中状态
        if (!workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new WorkflowException(-1, "当前实例状态不允许核损核价审核通过");
        }
    }

    /**
     * 调用精友API通知状态变更
     *
     * @param mtcRepairTask 维修任务
     */
    private void notifyToJingYou(MtcRepairTask mtcRepairTask) {
        // 判断是否使用精友系统
        if (mtcRepairTask.getPartsLibraryType() == 2) {
            JingYouLossNotify jingYouLossNotify = new JingYouLossNotify("016", "核损通过", "07", "核损通过");
            jingYouService.lossNotify(mtcRepairTask.getTaskNo(), jingYouLossNotify);
        }
    }

    /**
     * 通知外部系统
     *
     * @param businessId 业务ID
     */
    private void notifyExternalSystems(String businessId) {
        // 通知相关系统核损核价已审核通过
        log.info("通知外部系统核损核价已审核通过：{}", businessId);
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_APPROVE_QUOTE;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "核损核价-审核通过处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();

        try {
            // 1. 状态校验
            validateApprovalStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 3. 调用精友API核损通过
            notifyToJingYou(mtcRepairTask);

            // 4. 记录操作日志
            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setRecordId(mtcRepairTask.getId());
            mtcOperatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setRemark("核损核价");

            // 构建操作内容
            String opeContent = "审核通过，核损金额为：" + mtcRepairTask.getVehicleInsuranceTotalAmount() + "元";
            if (null != mtcRepairTask.getCustPaysDirect() && mtcRepairTask.getCustPaysDirect() > 0) {
                if (mtcRepairTask.getCustPaysDirect() == 1) {
                    opeContent = opeContent + "，客户直付：是";
                    if (null != mtcRepairTask.getCustAmount()) {
                        opeContent = opeContent + "，金额：" + mtcRepairTask.getCustAmount() + "元";
                    }
                } else if (mtcRepairTask.getCustPaysDirect() == 0) {
                    opeContent = opeContent + "，客户直付：否";
                }
            }
            
            mtcOperatorLog.setOpeContent(opeContent);
            mtcOperatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(mtcOperatorLog);
            
            // 5. 添加车辆维修流程日志
            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setRecordId(mtcRepairTask.getId());
            mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcProcessLog.setRemark("车辆维修");
            mtcProcessLog.setOpeContent("维修中");
            mtcProcessLog.setCurrentActivityCode("IN_REPAIR"); // 下一个环节为车辆维修
            tableOperatorLogService.insertSelective(mtcProcessLog);

            // 6. 通知外部系统
            notifyExternalSystems(businessId);
            
        } catch (Exception e) {
            log.error("处理核损核价审核通过事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理核损核价审核通过事件失败：" + e.getMessage());
        }
    }
}