package com.extracme.saas.autocare.workflow.handler;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.dto.JingYouLossNotify;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.JingYouService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 退回核损核价处理器
 * 处理核损核价活动节点退回至维修报价节点的逻辑
 */
@Slf4j
@Component
public class RejectLossAssessmentHandler extends AbstractEventHandler {

    /**
     * 事件类型：退回核损核价
     */
    private static final String EVENT_TYPE_REJECT_LOSS_ASSESSMENT = "REJECT_LOSS_ASSESSMENT";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private JingYouService jingYouService;
    
    /**
     * 验证节点状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateLossAssessmentStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于核损核价环节
        if (!workflowInstance.getCurrentActivityCode().equals("LOSS_ASSESSMENT")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许退回核损核价");
        }
        // 验证是否核损核价的处理中状态
        if (!workflowInstance.getStatusCode().equals(ActivityStatusEnum.PROCESSING.getCode())) {
            throw new WorkflowException(-1, "当前实例状态不允许退回核损核价");
        }
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_REJECT_LOSS_ASSESSMENT;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "核损核价-退回处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();
        LoginUser loginUser = SessionUtils.getLoginUser();

        try {
            log.info("开始处理退回核损核价，业务ID：{}", businessId);
            // 获取维修任务ID
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 1. 状态校验
            validateLossAssessmentStatus(businessId);

            // 2. 更新维修任务状态为待核损核价
            MtcRepairTask update = new MtcRepairTask();
            update.setResurveyFlag(Short.valueOf("0"));
            update.setRecoveryFlag(Short.valueOf("0"));
            update.setRecoveryAmount(BigDecimal.ZERO);
            update.setInsuranceAmount(BigDecimal.ZERO);
            update.setAccDepAmount(BigDecimal.ZERO);
            update.setOutageLossAmount(BigDecimal.ZERO);
            update.setVehicleLossAmount(BigDecimal.ZERO);
            update.setTrailerRescueAmount(BigDecimal.ZERO);
            update.setDutySituation(Integer.valueOf("0"));
            update.setNuclearLossReversionFlag(Integer.valueOf("1"));
            update.setExamineLevel(Short.valueOf("0"));
            update.setVerificationRejectLevel(loginUser.getApprovalLevel().toString());
            tableRepairTaskService.updateSelectiveById(update);

            // 3. 调用精友API通知状态变更
            notifyToJingYou(mtcRepairTask);

            // 4. 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setRecordId(mtcRepairTask.getId());
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setCreateBy(SessionUtils.getNickname());
            operatorLog.setCreatedTime(new Date());
            operatorLog.setRemark("核损核价");
            operatorLog.setOpeContent("退回核损核价至维修报价，金额为：" + mtcRepairTask.getVehicleInsuranceTotalAmount() + "元");
            operatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());

            tableOperatorLogService.insertSelective(operatorLog);
            log.info("核损核价已退回至维修报价，业务ID：{}", businessId);

            // 5. 添加流程log
            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setRecordId(mtcRepairTask.getId());
            mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcProcessLog.setCreateBy(SessionUtils.getNickname());
            mtcProcessLog.setCreatedTime(new Date());
            mtcProcessLog.setRemark("维修报价");
            mtcProcessLog.setOpeContent("被退回");
            tableOperatorLogService.insertSelective(mtcProcessLog);
        } catch (Exception e) {
            log.error("处理退回核损核价事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理退回核损核价事件失败：" + e.getMessage());
        }
    }

    /**
     * 调用精友API通知状态变更
     *
     * @param mtcRepairTask 维修任务
     */
    private void notifyToJingYou(MtcRepairTask mtcRepairTask) {
        // 判断是否使用精友系统
        if (mtcRepairTask.getPartsLibraryType() == 2) {
            JingYouLossNotify jingYouLossNotify = new JingYouLossNotify("014", "核损退回到定损", "05", "核损退回到定损");
            jingYouService.lossNotify(mtcRepairTask.getTaskNo(), jingYouLossNotify);
        }
    }
}