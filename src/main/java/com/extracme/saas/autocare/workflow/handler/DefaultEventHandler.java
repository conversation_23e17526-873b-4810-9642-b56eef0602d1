package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.workflow.core.WorkflowContext;

/**
 * 默认事件处理器接口
 * 定义事件处理的核心方法和通用逻辑
 * 
 * <AUTHOR>
 * @date 2024/05/15
 */
public interface DefaultEventHandler {

    /**
     * 处理事件
     *
     * @param context 工作流上下文，包含当前流程实例、源节点、目标节点等信息
     * @param tenantId 租户ID
     * @return 处理结果，true表示处理成功，false表示处理失败
     */
    boolean handle(WorkflowContext context, String tenantId);

    /**
     * 判断是否可以处理指定事件
     *
     * @param eventType 事件类型
     * @param taskType 任务类型
     * @return 是否可以处理
     */
    boolean canHandle(String eventType, Integer taskType);

    /**
     * 获取处理器优先级
     * 数值越小优先级越高
     *
     * @return 优先级
     */
    int getPriority();

    /**
     * 获取处理器支持的事件类型
     *
     * @return 事件类型
     */
    String getSupportedEventType();

    /**
     * 获取处理器支持的任务类型
     *
     * @return 任务类型
     */
    Integer getSupportedTaskType();

    /**
     * 获取处理器名称
     *
     * @return 处理器名称
     */
    String getHandlerName();
}
