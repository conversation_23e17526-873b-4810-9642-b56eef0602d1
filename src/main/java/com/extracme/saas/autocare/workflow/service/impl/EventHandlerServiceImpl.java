package com.extracme.saas.autocare.workflow.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import com.extracme.saas.autocare.workflow.handler.DefaultEventHandler;
import com.extracme.saas.autocare.workflow.handler.EventHandlerRegistry;
import com.extracme.saas.autocare.workflow.service.EventHandlerService;
import com.extracme.saas.autocare.enums.TaskTypeEnum;
import com.extracme.saas.autocare.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;

/**
 * 事件处理器服务实现类
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
@Slf4j
@Service
public class EventHandlerServiceImpl implements EventHandlerService {

    @Autowired
    private EventHandlerRegistry eventHandlerRegistry;

    @Override
    public boolean handleEvent(WorkflowContext context, String tenantId) {
        if (context == null) {
            throw new BusinessException("工作流上下文不能为空");
        }

        String eventType = context.getTriggerEvent();
        // 从 WorkflowInstance 中获取 workflowId，然后查询对应的 taskType
        // 这里假设 WorkflowInstance 中没有直接的 taskType 字段，需要通过 repairTypeId 获取
        Integer taskType = TaskTypeEnum.ACCIDENT_REPAIR.getCode(); // 默认使用事故维修类型

        // 在实际项目中，应该通过 workflowId 查询对应的 WorkflowTemplate 获取 taskType
        // 或者通过 businessId 查询对应的 MtcRepairTask 获取 repairTypeId

        log.info("开始处理事件: {}, 任务类型: {}, 租户ID: {}", eventType, taskType, tenantId);

        // 两级查找策略获取处理器
        DefaultEventHandler handler = findEventHandler(eventType, taskType, tenantId);
        if (handler == null) {
            log.warn("未找到事件处理器: {}, 任务类型: {}, 租户ID: {}", eventType, taskType, tenantId);
            return false;
        }

        // 处理事件
        return handler.handle(context, tenantId);
    }

    @Override
    public String[] getSupportedHandlers(String eventType, Integer taskType, String tenantId) {
        List<DefaultEventHandler> handlers = eventHandlerRegistry.getHandlers(eventType, taskType, tenantId);

        return handlers.stream()
                .map(DefaultEventHandler::getHandlerName)
                .toArray(String[]::new);
    }

    /**
     * 两级查找策略获取事件处理器
     * 第一级：查找具体任务类型的处理器（eventType + taskType + tenantId）
     * 第二级：查找通用处理器（eventType + taskType=-1 + tenantId）
     *
     * @param eventType 事件类型
     * @param taskType 任务类型
     * @param tenantId 租户ID
     * @return 事件处理器，如果找不到则返回null
     */
    private DefaultEventHandler findEventHandler(String eventType, Integer taskType, String tenantId) {
        // 第一级查找：查找具体任务类型的处理器
        DefaultEventHandler handler = eventHandlerRegistry.getHandler(eventType, taskType, tenantId);
        if (handler != null) {
            log.info("使用具体任务类型处理器: {}, 事件类型: {}, 任务类型: {}",
                    handler.getHandlerName(), eventType, taskType);
            return handler;
        }

        // 第二级查找：查找通用处理器（taskType=-1表示适用于所有任务类型）
        handler = eventHandlerRegistry.getHandler(eventType, -1, tenantId);
        if (handler != null) {
            log.info("使用通用处理器: {}, 事件类型: {}, 原任务类型: {}",
                    handler.getHandlerName(), eventType, taskType);
            return handler;
        }

        // 两级都找不到
        return null;
    }
}
