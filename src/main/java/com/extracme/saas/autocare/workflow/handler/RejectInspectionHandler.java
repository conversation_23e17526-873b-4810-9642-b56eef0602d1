package com.extracme.saas.autocare.workflow.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 验收不通过处理器
 * 处理验收活动节点审核通过流转至车辆交接的逻辑
 */
@Slf4j
@Component
public class RejectInspectionHandler extends AbstractEventHandler {

    /**
     * 事件类型：验收通过
     */
    private static final String EVENT_TYPE_REJECT_INSPECTION = "REJECT_INSPECTION";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    /**
     * 验证验收状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateInspectionStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于验收环节
        if (!workflowInstance.getCurrentActivityCode().equals("QUALITY_INSPECTION")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许验收通过");
        }
        // 验证是否验收的处理中状态
        if (!workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new WorkflowException(-1, "当前实例状态不允许验收通过");
        }
    }

    /**
     * 更新维修任务验收信息
     *
     * @param mtcRepairTask 维修任务
     */
    private void updateInspectionInfo(MtcRepairTask mtcRepairTask) {
        // 更新验收时间和状态
        // mtcRepairTask.setInspectionTime(new Date());
        // mtcRepairTask.setInspectionStatus(1); // 1表示验收通过
        tableRepairTaskService.updateSelectiveById(mtcRepairTask);
        log.info("更新维修任务验收信息，任务ID：{}", mtcRepairTask.getId());
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_REJECT_INSPECTION;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "验收-不通过处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();

        try {
            // 1. 状态校验
            validateInspectionStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 3. 更新验收信息
            updateInspectionInfo(mtcRepairTask);

            // 4. 记录操作日志
            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setRemark("车辆验收");
            mtcOperatorLog.setRecordId(mtcRepairTask.getId());
            mtcOperatorLog.setOpeContent("验收不通过");
            mtcOperatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(mtcOperatorLog);

            // 5. 通知外部系统
            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setRemark("车辆维修");
            mtcProcessLog.setRecordId(mtcRepairTask.getId());
            mtcProcessLog.setOpeContent("被驳回");
            mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            tableOperatorLogService.insertSelective(mtcProcessLog);

        } catch (Exception e) {
            log.error("处理验收不通过事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理验收不通过事件失败：" + e.getMessage());
        }
    }
}