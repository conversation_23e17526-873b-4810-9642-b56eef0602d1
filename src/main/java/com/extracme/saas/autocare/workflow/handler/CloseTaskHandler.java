package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.VehicleLeavingFactoryService;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 维修任务关闭处理器
 * 处理维修任务关闭的逻辑
 */
@Slf4j
@Component
public class CloseTaskHandler extends AbstractEventHandler {

    /**
     * 事件类型：关闭维修任务
     */
    private static final String EVENT_TYPE_CLOSE_TASK = "CLOSE_TASK";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private VehicleLeavingFactoryService vehicleLeavingFactoryService;

    /**
     * 验证任务状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateTaskStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }

        // 验证当前实例状态是否允许关闭
        String statusCode = workflowInstance.getStatusCode();
        boolean isValidStatus = !statusCode.equals(ActivityStatusEnum.CLOSED.getCode()) &&
                !statusCode.equals(ActivityStatusEnum.COMPLETED.getCode());

        if (!isValidStatus) {
            throw new WorkflowException(-1, "当前任务状态不允许关闭");
        }
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_CLOSE_TASK;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "维修任务-关闭处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();

        try {
            log.info("开始关闭维修任务，业务ID：{}", businessId);
            // 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 1. 状态校验
            validateTaskStatus(businessId);

            // 2. 关闭出厂登记
            vehicleLeavingFactoryService.closeLeavingFactory(businessId);

            // 3. 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setRemark("关闭任务");
            operatorLog.setOpeContent("关闭维修任务");
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            operatorLog.setRecordId(mtcRepairTask.getId());

            tableOperatorLogService.insertSelective(operatorLog);
            log.info("维修任务已关闭，业务ID：{}", businessId);
        } catch (Exception e) {
            log.error("处理关闭维修任务事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理关闭维修任务事件失败：" + e.getMessage());
        }
    }
}