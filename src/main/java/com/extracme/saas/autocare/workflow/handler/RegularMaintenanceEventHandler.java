package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.TaskTypeEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 常规保养事件处理器
 * 处理常规保养类型的工作流事件
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
@Slf4j
@Component
public class RegularMaintenanceEventHandler extends AbstractEventHandler {

    /**
     * 事件类型：完成交接
     */
    private static final String EVENT_TYPE_COMPLETE_HANDOVER = "COMPLETE_HANDOVER_111";

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        // 获取当前流程实例
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();

        log.info("常规保养事件处理器处理事件: {}, 业务ID: {}", getSupportedEventType(), businessId);

        try {
            // 1. 状态校验
            validateStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务: " + businessId);
            }

            // 3. 更新维修任务状态
            mtcRepairTask.setVehicleReciveTime(new Date()); // 使用车辆接收时间表示交接完成
            // 常规保养特有的处理逻辑
            mtcRepairTask.setRepairTypeId(TaskTypeEnum.REGULAR_MAINTENANCE.getCode()); // 设置保养类型为常规保养
            mtcRepairTask.setRepairTypeName("常规保养");
            tableRepairTaskService.updateSelectiveById(mtcRepairTask);

            // 4. 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setRecordId(Long.parseLong(businessId));
            operatorLog.setOpeContent("常规保养-确认交接");
            operatorLog.setRemark("常规保养流程");
            operatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(operatorLog);

            // 5. 通知外部系统
            notifyExternalSystems(businessId);

        } catch (Exception e) {
            log.error("常规保养事件处理失败: {}", e.getMessage(), e);
            throw new WorkflowException(-1, "常规保养事件处理失败: " + e.getMessage());
        }
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_COMPLETE_HANDOVER;
    }

    @Override
    public Integer getSupportedTaskType() {
        return TaskTypeEnum.REGULAR_MAINTENANCE.getCode();
    }

    @Override
    public String getHandlerName() {
        return "常规保养事件处理器";
    }

    /**
     * 验证状态
     *
     * @param businessId 业务ID
     */
    private void validateStatus(String businessId) {
        // 验证当前状态是否允许完成交接
        // 例如：检查必要信息是否已填写完整
        // 如果验证失败，抛出 WorkflowException
    }

    /**
     * 通知外部系统
     *
     * @param businessId 业务ID
     */
    private void notifyExternalSystems(String businessId) {
        // 常规保养可能需要通知不同的外部系统
        log.info("通知保养系统常规保养交接完成: {}", businessId);
    }
}
