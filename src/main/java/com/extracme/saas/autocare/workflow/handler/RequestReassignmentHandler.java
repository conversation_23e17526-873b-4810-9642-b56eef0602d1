package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 申请改派处理器
 * 处理维修任务申请改派的逻辑
 */
@Slf4j
@Component
public class RequestReassignmentHandler extends AbstractEventHandler {

    /**
     * 事件类型：申请改派
     */
    private static final String EVENT_TYPE_REQUEST_REASSIGNMENT = "REQUEST_REASSIGNMENT";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    /**
     * 验证改派申请状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateReassignmentStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        
        // 验证当前实例状态是否允许申请改派
        // 这里可以根据实际业务需求定义哪些状态允许申请改派
        String currentActivityCode = workflowInstance.getCurrentActivityCode();
        String statusCode = workflowInstance.getStatusCode();
        
        // 示例：只有在特定活动节点且特定状态下才允许申请改派
        boolean isValidActivity = currentActivityCode.equals("VEHICLE_TRANSFER") || 
                                 currentActivityCode.equals("REPAIR_QUOTATION");

        boolean isValidStatus = statusCode.equals("UNPROCESSED") ||
                statusCode.equals("PROCESSING") ||
                statusCode.equals("REJECTED");
        
        if (!isValidActivity) {
            throw new WorkflowException(-1, "当前活动节点不允许申请改派");
        }
        
        if (!isValidStatus) {
            throw new WorkflowException(-1, "当前状态不允许申请改派");
        }
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_REQUEST_REASSIGNMENT;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "维修任务-申请改派处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();
        LoginUser loginUser = SessionUtils.getLoginUser();

        try {
            log.info("开始处理维修任务改派申请，业务ID：{}", businessId);
            
            // 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 1. 状态校验
            validateReassignmentStatus(businessId);
            
            // 2. 清空任务修理厂相关信息
            mtcRepairTask.setRepairDepotId("");
            mtcRepairTask.setRepairDepotName("");
            mtcRepairTask.setRepairDepotOrgId("");
            mtcRepairTask.setRepairDepotSapCode("");
            mtcRepairTask.setRepairGrade("");
            mtcRepairTask.setTaxRate("");
            
            // 保存更改
            tableRepairTaskService.updateSelectiveById(mtcRepairTask);
            
            // 3. 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setUpdateBy(loginUser.getUsername());
            operatorLog.setUpdatedTime(new Date());
            operatorLog.setCreateBy(loginUser.getUsername());
            operatorLog.setCreatedTime(new Date());
            operatorLog.setRemark("维修任务改派");
            operatorLog.setOpeContent("申请改派维修任务");
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            operatorLog.setRecordId(mtcRepairTask.getId());
            
            tableOperatorLogService.insertSelective(operatorLog);
            
            log.info("维修任务改派申请已提交，业务ID：{}", businessId);
        } catch (Exception e) {
            log.error("处理维修任务改派申请失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理维修任务改派申请失败：" + e.getMessage());
        }
    }
}
