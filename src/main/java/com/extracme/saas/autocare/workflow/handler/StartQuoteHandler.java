package com.extracme.saas.autocare.workflow.handler;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 开始维修报价处理器
 * 处理维修报价活动节点从未处理状态转为处理中状态的逻辑
 */
@Slf4j
@Component
public class StartQuoteHandler extends AbstractEventHandler {

    /**
     * 事件类型：开始维修报价报价
     */
    private static final String EVENT_TYPE_START_QUOTE = "START_QUOTE";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    /**
     * 验证节点状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateQuoteStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于维修报价环节
        if (!workflowInstance.getCurrentActivityCode().equals("REPAIR_QUOTATION")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许开始报价");
        }
        // 验证是否维修报价的未处理状态
        if (!workflowInstance.getStatusCode().equals(ActivityStatusEnum.UNPROCESSED.getCode())) {
            throw new WorkflowException(-1, "当前实例状态不允许开始报价");
        }
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_START_QUOTE;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "维修报价-开始处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();
        LoginUser loginUser = SessionUtils.getLoginUser();

        try {
            log.info("开始处理维修报价，业务ID：{}", businessId);
            // 获取维修任务ID
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 1. 状态校验
            validateQuoteStatus(businessId);

            // 2. 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setUpdateBy(loginUser.getUsername());
            operatorLog.setUpdatedTime(new Date());
            operatorLog.setCreateBy(loginUser.getUsername());
            operatorLog.setCreatedTime(new Date());
            operatorLog.setRemark("维修报价");
            operatorLog.setOpeContent("开始处理维修报价");
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            operatorLog.setRecordId(mtcRepairTask.getId());

            tableOperatorLogService.insertSelective(operatorLog);
            log.info("维修报价状态已更新为处理中，业务ID：{}", businessId);
        } catch (Exception e) {
            log.error("处理开始报价事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理开始报价事件失败：" + e.getMessage());
        }
    }
}