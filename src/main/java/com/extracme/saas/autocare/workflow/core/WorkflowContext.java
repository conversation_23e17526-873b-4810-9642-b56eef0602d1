package com.extracme.saas.autocare.workflow.core;

import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import lombok.Builder;
import lombok.Data;

/**
 * 工作流上下文
 * 包含流程转换过程中需要的各种信息
 */
@Data
@Builder
public class WorkflowContext {

    /**
     * 当前流程实例
     */
    private WorkflowInstance workflowInstance;

    /**
     * 来源活动节点编码
     */
    private String fromActivityId;

    /**
     * 目标活动节点编码
     */
    private String toActivityId;

    /**
     * 触发事件名称
     */
    private String triggerEvent;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 附加参数
     */
    private Object extraParams;
}
