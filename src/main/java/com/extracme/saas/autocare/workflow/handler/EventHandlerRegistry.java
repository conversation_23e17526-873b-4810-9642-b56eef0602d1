package com.extracme.saas.autocare.workflow.handler;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.lang.NonNull;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 事件处理器注册中心
 * 负责管理和查找事件处理器
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
@Slf4j
@Component
public class EventHandlerRegistry implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 系统默认处理器映射
     * 格式: eventType:taskType -> List<DefaultEventHandler>
     */
    private final Map<String, List<DefaultEventHandler>> defaultHandlerMap = new ConcurrentHashMap<>();

    /**
     * 租户自定义处理器映射
     * 格式: tenantId:eventType:taskType -> List<DefaultEventHandler>
     */
    private final Map<String, List<DefaultEventHandler>> tenantHandlerMap = new ConcurrentHashMap<>();

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        log.info("初始化事件处理器注册中心");
    }

    /**
     * 应用上下文刷新事件处理
     * 自动扫描并注册所有事件处理器
     *
     * @param event 上下文刷新事件
     */
    @Override
    public void onApplicationEvent(@NonNull ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() == null) {
            registerAllHandlers();
        }
    }

    /**
     * 注册所有处理器
     */
    private void registerAllHandlers() {
        log.info("开始扫描并注册所有事件处理器");

        // 获取所有DefaultEventHandler实现类
        Map<String, DefaultEventHandler> handlerBeans = applicationContext.getBeansOfType(DefaultEventHandler.class);

        for (DefaultEventHandler handler : handlerBeans.values()) {
            registerHandler(handler);
        }

        log.info("事件处理器注册完成，共注册 {} 个处理器", handlerBeans.size());
    }

    /**
     * 注册处理器
     *
     * @param handler 处理器
     */
    public void registerHandler(DefaultEventHandler handler) {
        String handlerName = handler.getClass().getName();

        // 判断是否为租户自定义处理器
        if (isTenantHandler(handlerName)) {
            // 提取租户ID
            String tenantId = extractTenantId(handlerName);
            registerTenantHandler(handler, tenantId);
            log.info("注册租户自定义处理器: {}, 租户ID: {}", handlerName, tenantId);
        } else {
            registerDefaultHandler(handler);
            log.info("注册系统默认处理器: {}", handlerName);
        }
    }

    /**
     * 注册系统默认处理器
     *
     * @param handler 处理器
     */
    private void registerDefaultHandler(DefaultEventHandler handler) {
        String key = buildHandlerKey(handler.getSupportedEventType(), handler.getSupportedTaskType());

        synchronized (defaultHandlerMap) {
            defaultHandlerMap.computeIfAbsent(key, k -> new ArrayList<>()).add(handler);

            // 按优先级排序
            defaultHandlerMap.get(key).sort(Comparator.comparingInt(DefaultEventHandler::getPriority));
        }
    }

    /**
     * 注册租户自定义处理器
     *
     * @param handler 处理器
     * @param tenantId 租户ID
     */
    private void registerTenantHandler(DefaultEventHandler handler, String tenantId) {
        String key = buildTenantHandlerKey(tenantId, handler.getSupportedEventType(), handler.getSupportedTaskType());

        synchronized (tenantHandlerMap) {
            tenantHandlerMap.computeIfAbsent(key, k -> new ArrayList<>()).add(handler);

            // 按优先级排序
            tenantHandlerMap.get(key).sort(Comparator.comparingInt(DefaultEventHandler::getPriority));
        }
    }

    /**
     * 获取处理器
     *
     * @param eventType 事件类型
     * @param taskType 任务类型
     * @param tenantId 租户ID
     * @return 处理器列表，按优先级排序
     */
    public List<DefaultEventHandler> getHandlers(String eventType, Integer taskType, String tenantId) {
        List<DefaultEventHandler> handlers = new ArrayList<>();

        // 1. 先查找租户自定义处理器
        if (StringUtils.isNotBlank(tenantId)) {
            String tenantKey = buildTenantHandlerKey(tenantId, eventType, taskType);
            List<DefaultEventHandler> tenantHandlers = tenantHandlerMap.get(tenantKey);
            if (tenantHandlers != null && !tenantHandlers.isEmpty()) {
                handlers.addAll(tenantHandlers);
            }
        }

        // 2. 如果没有找到租户自定义处理器，则使用系统默认处理器
        if (handlers.isEmpty()) {
            String defaultKey = buildHandlerKey(eventType, taskType);
            List<DefaultEventHandler> defaultHandlers = defaultHandlerMap.get(defaultKey);
            if (defaultHandlers != null && !defaultHandlers.isEmpty()) {
                handlers.addAll(defaultHandlers);
            }
        }

        return handlers;
    }

    /**
     * 获取处理器
     *
     * @param eventType 事件类型
     * @param taskType 任务类型
     * @param tenantId 租户ID
     * @return 优先级最高的处理器，如果没有找到则返回null
     */
    public DefaultEventHandler getHandler(String eventType, Integer taskType, String tenantId) {
        List<DefaultEventHandler> handlers = getHandlers(eventType, taskType, tenantId);
        return handlers.isEmpty() ? null : handlers.get(0);
    }

    /**
     * 获取所有处理器
     *
     * @return 所有处理器列表
     */
    public List<DefaultEventHandler> getAllHandlers() {
        List<DefaultEventHandler> allHandlers = new ArrayList<>();

        // 添加所有系统默认处理器
        allHandlers.addAll(defaultHandlerMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList()));

        // 添加所有租户自定义处理器
        allHandlers.addAll(tenantHandlerMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList()));

        return allHandlers;
    }

    /**
     * 构建处理器键
     *
     * @param eventType 事件类型
     * @param taskType 任务类型
     * @return 处理器键
     */
    private String buildHandlerKey(String eventType, Integer taskType) {
        return eventType + ":" + taskType;
    }

    /**
     * 构建租户处理器键
     *
     * @param tenantId 租户ID
     * @param eventType 事件类型
     * @param taskType 任务类型
     * @return 租户处理器键
     */
    private String buildTenantHandlerKey(String tenantId, String eventType, Integer taskType) {
        return tenantId + ":" + eventType + ":" + taskType;
    }

    /**
     * 判断是否为租户自定义处理器
     * 命名规范: {TenantId}{EventType}EventHandler
     *
     * @param handlerName 处理器名称
     * @return 是否为租户自定义处理器
     */
    private boolean isTenantHandler(String handlerName) {
        // 提取类名
        String className = handlerName.substring(handlerName.lastIndexOf('.') + 1);

        // 判断是否符合租户处理器命名规范
        return className.matches("^[A-Za-z0-9]+[A-Z][a-z]+[A-Za-z0-9]*EventHandler$")
                && !className.equals("AbstractEventHandler")
                && !className.equals("DefaultEventHandler");
    }

    /**
     * 从处理器名称中提取租户ID
     *
     * @param handlerName 处理器名称
     * @return 租户ID
     */
    private String extractTenantId(String handlerName) {
        // 提取类名
        String className = handlerName.substring(handlerName.lastIndexOf('.') + 1);

        // 提取租户ID
        // 假设命名规范为: {TenantId}{EventType}EventHandler
        // 例如: ExtracmeAccidentRepairEventHandler

        // 先去掉EventHandler后缀
        String withoutSuffix = className.replace("EventHandler", "");

        // 找到第一个大写字母的位置
        int firstUpperCase = -1;
        for (int i = 0; i < withoutSuffix.length(); i++) {
            if (Character.isUpperCase(withoutSuffix.charAt(i))) {
                firstUpperCase = i;
                break;
            }
        }

        // 提取租户ID
        return firstUpperCase > 0 ? withoutSuffix.substring(0, firstUpperCase) : withoutSuffix;
    }
}
