# 多租户事件处理服务架构

本文档描述了多租户事件处理服务架构的设计和实现，该架构用于处理车辆维修系统中的各类事件，支持多租户自定义扩展。

## 1. 架构概述

多租户事件处理服务架构基于以下核心组件：

- **DefaultEventHandler**：事件处理器接口，定义事件处理的核心方法
- **AbstractEventHandler**：抽象事件处理器，实现通用逻辑
- **具体事件处理器**：针对不同业务类型的具体实现
- **EventHandlerRegistry**：事件处理器注册中心，管理和查找处理器
- **EventHandlerService**：事件处理服务，提供对外接口

## 2. 核心组件

### 2.1 DefaultEventHandler 接口

定义事件处理器的核心方法：

```java
public interface DefaultEventHandler {
    boolean handle(WorkflowContext context, String tenantId);
    boolean canHandle(String eventType, Integer taskType);
    int getPriority();
    String getSupportedEventType();
    Integer getSupportedTaskType();
    String getHandlerName();
}
```

### 2.2 AbstractEventHandler 抽象类

实现 DefaultEventHandler 接口的通用逻辑，提供模板方法和钩子方法：

```java
public abstract class AbstractEventHandler implements DefaultEventHandler {
    @Override
    public boolean handle(WorkflowContext context, String tenantId) {
        // 前置处理
        beforeHandle(context, tenantId);
        
        // 执行业务逻辑
        doHandle(context, tenantId);
        
        // 后置处理
        afterHandle(context, tenantId);
        
        return true;
    }
    
    protected abstract void doHandle(WorkflowContext context, String tenantId);
}
```

### 2.3 具体事件处理器

针对不同业务类型的具体实现，例如：

- **AccidentRepairEventHandler**：事故维修事件处理器
- **RegularMaintenanceEventHandler**：常规保养事件处理器

### 2.4 EventHandlerRegistry

事件处理器注册中心，管理和查找处理器：

```java
@Component
public class EventHandlerRegistry implements ApplicationListener<ContextRefreshedEvent> {
    // 系统默认处理器映射
    private final Map<String, List<DefaultEventHandler>> defaultHandlerMap = new ConcurrentHashMap<>();
    
    // 租户自定义处理器映射
    private final Map<String, List<DefaultEventHandler>> tenantHandlerMap = new ConcurrentHashMap<>();
    
    // 获取处理器
    public DefaultEventHandler getHandler(String eventType, Integer taskType, String tenantId) {
        // 先查找租户自定义处理器，如果不存在则使用系统默认处理器
    }
}
```

### 2.5 EventHandlerService

事件处理服务，提供对外接口：

```java
@Service
public class EventHandlerServiceImpl implements EventHandlerService {
    @Override
    public boolean handleEvent(WorkflowContext context, String tenantId) {
        // 获取处理器并处理事件
    }
}
```

## 3. 多租户自定义扩展机制

### 3.1 命名规范

租户自定义处理器命名规范：`{TenantId}{EventType}EventHandler`

例如：`ExtracmeAccidentRepairEventHandler`

### 3.2 加载机制

系统在启动时自动扫描并注册所有处理器：

1. 实现 `ApplicationListener<ContextRefreshedEvent>` 接口
2. 在 `onApplicationEvent` 方法中扫描并注册所有处理器
3. 根据处理器名称判断是否为租户自定义处理器

### 3.3 优先级机制

处理器查找逻辑：

1. 先查找租户自定义处理器
2. 如果不存在则使用系统默认处理器
3. 根据处理器优先级排序，优先级越高的处理器越先执行

## 4. 使用示例

### 4.1 创建自定义处理器

```java
@Component
public class MyTenantAccidentRepairEventHandler extends AbstractEventHandler {
    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        // 实现自定义处理逻辑
    }
    
    @Override
    public String getSupportedEventType() {
        return "COMPLETE_HANDOVER";
    }
    
    @Override
    public Integer getSupportedTaskType() {
        return TaskTypeEnum.ACCIDENT_REPAIR.getCode();
    }
    
    @Override
    public int getPriority() {
        return 50; // 优先级高于默认处理器
    }
}
```

### 4.2 在服务中使用

```java
@Service
public class MyService {
    @Autowired
    private EventHandlerService eventHandlerService;
    
    public void processEvent(WorkflowContext context, String tenantId) {
        eventHandlerService.handleEvent(context, tenantId);
    }
}
```

## 5. 单元测试

使用 JUnit 和 Mockito 进行单元测试：

```java
@SpringBootTest
class EventHandlerTest {
    @Autowired
    private EventHandlerService eventHandlerService;
    
    @Test
    void testEventHandling() {
        // 创建工作流上下文
        WorkflowContext context = WorkflowContext.builder()
                .workflowInstance(workflowInstance)
                .triggerEvent("COMPLETE_HANDOVER")
                .build();
        
        // 处理事件
        boolean result = eventHandlerService.handleEvent(context, "tenantId");
        
        // 验证结果
        assertTrue(result);
    }
}
```

## 6. 注意事项

1. 租户自定义处理器必须遵循命名规范
2. 处理器优先级越小越优先执行
3. 处理器必须注册为 Spring Bean
4. 处理器应该是无状态的，以确保线程安全
5. 处理器应该处理异常并记录日志
