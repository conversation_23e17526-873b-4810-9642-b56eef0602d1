package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.dto.JingYouLossNotify;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.JingYouService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 提交报价处理器
 * 处理维修报价活动节点流转至核损核价的逻辑
 */
@Slf4j
@Component
public class SubmitQuoteHandler extends AbstractEventHandler {

    /**
     * 事件类型：提交报价
     */
    private static final String EVENT_TYPE_SUBMIT_QUOTE = "SUBMIT_QUOTE";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private JingYouService jingYouService;

    /**
     * 验证报价状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateQuoteStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于维修报价环节
        if (!workflowInstance.getCurrentActivityCode().equals("REPAIR_QUOTATION")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许提交报价");
        }
        // 验证是否维修报价的处理中状态
        if (!workflowInstance.getStatusCode().equals(ActivityStatusEnum.PROCESSING.getCode()) &&
                !workflowInstance.getStatusCode().equals(ActivityStatusEnum.REJECTED.getCode())) {
            throw new WorkflowException(-1, "当前实例状态不允许提交报价");
        }
    }

    /**
     * 调用精友API通知状态变更
     *
     * @param mtcRepairTask 维修任务
     */
    private void notifyToJingYou(MtcRepairTask mtcRepairTask) {
        // 判断是否使用精友系统
        if (mtcRepairTask.getPartsLibraryType() == 2) {
            JingYouLossNotify jingYouLossNotify = new JingYouLossNotify("012", "定损提交到核损", "05", "定损提交到核损");
            jingYouService.lossNotify(mtcRepairTask.getTaskNo(), jingYouLossNotify);
        }
    }

    /**
     * 通知外部系统
     *
     * @param businessId 业务ID
     */
    private void notifyExternalSystems(String businessId) {
        // todo 2非转自费的事故维修任务
        // todo 2同步业财定损单金额
        // todo 2同步业财图片
        // todo 2梧桐维修任务增加日志
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_SUBMIT_QUOTE;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "维修报价-提交审核处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();

        // 记录开始处理时间（用于监听者模式记录处理时间）
        try {
            // 1. 状态校验
            validateQuoteStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 3. 调用精友API提交定损到核损
            notifyToJingYou(mtcRepairTask);

            // 4. 记录操作日志
            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setRecordId(mtcRepairTask.getId());
            mtcOperatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCreateBy(SessionUtils.getNickname());
            mtcOperatorLog.setCreatedTime(new Date());
            mtcOperatorLog.setRemark("维修报价");
            // 构建操作内容
            String opeContent = "提交审核，金额为：" + mtcRepairTask.getRepairInsuranceTotalAmount() + "元,";
            if (null != mtcRepairTask.getCustPaysDirect()) {
                if (mtcRepairTask.getCustPaysDirect() > 0) {
                    if (mtcRepairTask.getCustPaysDirect() == 1) {
                        opeContent = opeContent + " 是否客户直付:是";
                        if (null != mtcRepairTask.getCustAmount()) {
                            opeContent = opeContent + ", 金额"
                                    + mtcRepairTask.getCustAmount();
                        }
                    } else if (mtcRepairTask.getCustPaysDirect() == 0) {
                        opeContent = opeContent + " 是否客户直付:否";
                    }
                }
            }
            mtcOperatorLog.setOpeContent(opeContent.toString());
            mtcOperatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(mtcOperatorLog);

            // 添加流程日志
            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setRecordId(mtcRepairTask.getId());
            mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcProcessLog.setCreateBy(SessionUtils.getNickname());
            mtcProcessLog.setCreatedTime(new Date());
            // todo 不一定是核损核价
            mtcProcessLog.setRemark("核损核价");
            mtcProcessLog.setOpeContent("待处理");
            mtcProcessLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(mtcProcessLog);

            // 5. 通知外部系统
            notifyExternalSystems(businessId);
        } catch (Exception e) {
            log.error("处理提交报价事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理提交报价事件失败：" + e.getMessage());
        }
    }
}
