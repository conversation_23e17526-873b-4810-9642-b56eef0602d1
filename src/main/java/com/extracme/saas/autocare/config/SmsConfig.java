package com.extracme.saas.autocare.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 短信配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sms.cryun")
public class SmsConfig {
    
    /**
     * 主账号配置
     */
    private Account main = new Account();
    
    /**
     * API配置
     */
    private Api api = new Api();
    
    @Data
    public static class Account {
        /**
         * 访问密钥
         */
        private String accessKey;
        
        /**
         * 密钥
         */
        private String secret;
        
        /**
         * 签名ID
         */
        private String sign;
    }
    
    @Data
    public static class Api {
        /**
         * API主机地址
         */
        private String host = "http://api.1cloudsp.com";
        
        /**
         * 单条发送路径
         */
        private String singlePath = "/api/v2/single_send";
        
        /**
         * 批量发送路径
         */
        private String batchPath = "/api/v2/send";
        
        /**
         * 字符集
         */
        private String charset = "UTF-8";
        
        /**
         * 连接超时时间（毫秒）
         */
        private int connectionTimeout = 30000;
        
        /**
         * 读取超时时间（毫秒）
         */
        private int readTimeout = 60000;
        
        /**
         * 批量发送最大数量
         */
        private int maxBatchSize = 50000;
    }
}