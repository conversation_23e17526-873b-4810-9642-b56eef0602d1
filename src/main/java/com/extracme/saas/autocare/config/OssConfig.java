package com.extracme.saas.autocare.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 阿里云 OSS 配置类
 */
@Configuration
@ConfigurationProperties(prefix = "oss")
@Data
public class OssConfig {

    /**
     * OSS 服务端点
     */
    private String endpoint;

    /**
     * 访问密钥 ID
     */
    private String accessKeyId;

    /**
     * 访问密钥 Secret
     */
    private String accessKeySecret;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 短期存储桶名称
     */
    private String bucketShortTime;

    /**
     * STS角色ARN（用于获取临时凭证）
     */
    private String roleArn = "acs:ram::1199455603890798:role/evcardtesttmpwrite";

    /**
     * STS会话名称
     */
    private String roleSessionName = "oss-upload-session";

    /**
     * STS临时凭证有效期（秒），默认1小时
     */
    private Long durationSeconds = 3600L;

    /**
     * 是否启用STS临时凭证模式
     */
    private boolean stsEnabled = false;

    /**
     * 文件上传配置
     */
    private Upload upload;

    /**
     * 文件上传配置类
     */
    @Data
    public static class Upload {
        /**
         * 最大文件大小
         */
        private String maxFileSize = "100MB";

        /**
         * 允许的文件扩展名
         */
        private List<String> allowedExtensions;

        /**
         * OSS 访问基础 URL
         */
        private String baseUrl;

        /**
         * 上传路径前缀
         */
        private String env;

        /**
         * 获取最大文件大小（字节）
         */
        public long getMaxFileSizeInBytes() {
            String size = maxFileSize.toUpperCase();
            if (size.endsWith("KB")) {
                return Long.parseLong(size.substring(0, size.length() - 2)) * 1024;
            } else if (size.endsWith("MB")) {
                return Long.parseLong(size.substring(0, size.length() - 2)) * 1024 * 1024;
            } else if (size.endsWith("GB")) {
                return Long.parseLong(size.substring(0, size.length() - 2)) * 1024 * 1024 * 1024;
            } else {
                return Long.parseLong(size);
            }
        }
    }

}
