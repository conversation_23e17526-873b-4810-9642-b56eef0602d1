package com.extracme.saas.autocare.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@Configuration
@Data
public class SystemConfig {

    @Value("${jyInterfaceUrl}")
    private String jyInterfaceUrl;

    @Value("${assessmentRefreshURL}")
    private String assessmentRefreshURL;

    @Value("${evaluateRefreshURL}")
    private String evaluateRefreshURL;

    @Value("${assessmentBackURL}")
    private String assessmentBackURL;
    
    @Value("${evaluateBackURL}")
    private String evaluateBackURL;

    @Value("${bfcInsureUrl}")
    private String bfcInsureUrl;

}
