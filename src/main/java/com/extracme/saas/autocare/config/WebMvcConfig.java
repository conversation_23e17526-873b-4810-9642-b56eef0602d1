package com.extracme.saas.autocare.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.extracme.saas.autocare.interceptor.AuthInterceptor;

import org.springframework.lang.NonNull;

import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {
    
    private final AuthInterceptor authInterceptor;
    
    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/api/**")  // 拦截所有API请求
                .excludePathPatterns(        // 排除不需要拦截的路径
                    "/api/v1/auth/login/mobile",
                    "/api/v1/auth/code/send",
                    "/api/v1/data-sync/**",     // 数据同步接口允许匿名访问
                    "/swagger-ui/**",
                    "/swagger-resources/**",
                    "/v3/api-docs/**"
                );
    }
} 