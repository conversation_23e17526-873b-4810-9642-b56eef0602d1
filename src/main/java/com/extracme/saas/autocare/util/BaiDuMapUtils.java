package com.extracme.saas.autocare.util;

import com.alibaba.fastjson.JSONObject;
import com.extracme.saas.autocare.model.baidumap.BaiduPositionName;
import com.extracme.saas.autocare.model.baidumap.WgsToBaiduBean;
import lombok.extern.slf4j.Slf4j;

/**
 * 百度地图工具类
 * 提供百度地图API相关的工具方法，包括地理编码、逆地理编码、坐标转换等功能
 *
 * <AUTHOR>
 */
@Slf4j
public class BaiDuMapUtils {

	/**
	 * 驾车最优路线API
	 **/
	public static final String DRIVER_API_URL = "http://api.map.baidu.com/directionlite/v1/driving?"
			+ "origin=originCode&destination=destinationCode&ak=nIG1y4n4FAPiXafpPzp3KcvXiMru5uUC";

	/**
	 * 地理编码
	 */
	public static final String OPPOSITE_MAP = "http://api.map.baidu.com/geocoder/v2/?"
			+ "address=addressContent&output=json&ak=nIG1y4n4FAPiXafpPzp3KcvXiMru5uUC&callback=";

	/**
	 * 地理逆编码
	 */
	public static final String POSITE_MAP = "http://api.map.baidu.com/geocoder/v2/?"
			+ "callback=&location=position&output=json&pois=1&ak=nIG1y4n4FAPiXafpPzp3KcvXiMru5uUC";

	/**
	 * 百度API 大地坐标系转百度坐标系
	 */
	public static final String WGS_TO_BAIDU = "http://api.map.baidu.com/geoconv/v1/?coords=coordsString&from=1&to=5&ak=nIG1y4n4FAPiXafpPzp3KcvXiMru5uUC";

	/**
	 * 驾车最优路线V2版本API
	 */
	public static final String DRIVER_API_V2_URL = "http://api.map.baidu.com/direction/v2/driving?"
			+ "origin=originCode&destination=destinationCode&ak=W9iYXHFsSU9Zrs3gX4yDV3l3HztM98f9";

	/**
	 * 步行API
	 */
	public static final String WALK_API_URL = "http://api.map.baidu.com/directionlite/v1/walking?origin=originCode&destination=destinationCode&ak=W9iYXHFsSU9Zrs3gX4yDV3l3HztM98f9";

	/**
	 * 全球逆地理编码
	 *
	 * @param lat Baidu纬度
	 * @param lng Baidu经度
	 * @return 百度位置信息对象
	 */
	public static BaiduPositionName getBaiduPositionNameByPosition(String lat, String lng) {
		String posite_map_url = POSITE_MAP.replace("position", lat + "," + lng);
		try {
			String resultJson = HttpUtils.sendRestPost(posite_map_url, null);
			BaiduPositionName baiduPositionName = JSONObject.parseObject(resultJson, BaiduPositionName.class);
			if (baiduPositionName != null && baiduPositionName.getStatus() == 0) {
				return baiduPositionName;
			} else {
				return null;
			}
		} catch (Exception e) {
			log.error("全球逆地理编码", e);
			return null;
		}
	}

	/**
	 * GPS转百度逆地理编码
	 *
	 * @param lat GPS纬度
	 * @param lng GPS经度
	 * @return 百度位置信息对象
	 */
	public static BaiduPositionName getBaiduPositionNameByGpsPosition(String lat, String lng) {
		WgsToBaiduBean wgsToBaidu = BaiDuMapUtils.getWgsToBaidu(lat, lng);
		if (wgsToBaidu != null) {
			Float longx = wgsToBaidu.getResult().get(0).getX();
			Float laty = wgsToBaidu.getResult().get(0).getY();
			if (longx != null && laty != null) {
				return BaiDuMapUtils.getBaiduPositionNameByPosition(laty.toString(), longx.toString());
			}
		}
		return null;
	}

	/**
	 * 基于大地坐标系转百度坐标系
	 *
	 * @param lat GPS纬度
	 * @param lng GPS经度
	 * @return WGS84转百度坐标结果对象
	 */
	public static WgsToBaiduBean getWgsToBaidu(String lat, String lng) {
		String driver_api_url = WGS_TO_BAIDU.replace("coordsString", lng + "," + lat);
		try {
			String resultJson = HttpUtils.sendRestPost(driver_api_url, null);
			WgsToBaiduBean result = JSONObject.parseObject(resultJson, WgsToBaiduBean.class);
			if (result != null && result.getStatus() == 0) {
				return result;
			} else {
				return null;
			}
		} catch (Exception e) {
			log.error("基于大地坐标系转百度坐标系", e);
			return null;
		}
	}
}
