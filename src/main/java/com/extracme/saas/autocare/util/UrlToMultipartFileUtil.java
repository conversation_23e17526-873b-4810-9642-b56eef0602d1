package com.extracme.saas.autocare.util;


import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * URL转换为MultipartFile
 */
public class UrlToMultipartFileUtil {
    /**
     * 将URL文件转换为MultipartFile
     */
    public static MultipartFile urlToMultipartFile(String fileUrl, String fileName) throws IOException {
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        try (InputStream inputStream = connection.getInputStream()) {
            FileItem fileItem = createFileItem(inputStream, fileName);
            return new CommonsMultipartFile(fileItem);
        }
    }

    private static FileItem createFileItem(InputStream inputStream, String fileName) throws IOException {
        FileItem fileItem = new DiskFileItemFactory().createItem(
                "file",
                "application/octet-stream",
                false,
                fileName
        );

        try (OutputStream outputStream = fileItem.getOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        return fileItem;
    }
}
