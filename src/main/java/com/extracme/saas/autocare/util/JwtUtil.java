package com.extracme.saas.autocare.util;

import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Component
public class JwtUtil {

    private static final Logger log = LoggerFactory.getLogger(JwtUtil.class);

    private static final String USER_INFO_CLAIM_KEY = "userInfo";

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private Long expiration;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 使用用户ID生成token（向后兼容）
     */
    public String generateToken(String userId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        return generateToken(claims);
    }

    /**
     * 使用自定义claims生成token
     */
    public String generateToken(Map<String, Object> claims) {
        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration * 1000))
                .signWith(SignatureAlgorithm.HS256, secret)
                .compact();
    }

    public String getUserIdFromToken(String token) {
        Object userId = getClaimFromToken(token, claims -> claims.get("userId"));
        return userId != null ? userId.toString() : null;
    }

    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parser().setSigningKey(secret).parseClaimsJws(token).getBody();
    }

    public Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    public Boolean validateToken(String token) {
        try {
            getAllClaimsFromToken(token);
            return !isTokenExpired(token);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 使用完整用户信息生成token
     * 将用户信息序列化为JSON字符串存储在JWT claims中
     *
     * @param userInfo 用户信息DTO
     * @return JWT token
     */
    public String generateTokenWithUserInfo(JwtUserInfoDTO userInfo) {
        try {
            Map<String, Object> claims = new HashMap<>();

            // 保持向后兼容性，仍然存储userId
            claims.put("userId", userInfo.getUserId().toString());

            // 将完整用户信息序列化为JSON字符串存储
            String userInfoJson = objectMapper.writeValueAsString(userInfo);
            claims.put(USER_INFO_CLAIM_KEY, userInfoJson);

            // 生成token
            String token = generateToken(claims);

            // 检查token大小并记录警告
            int tokenSize = token.length();
            int userInfoSize = userInfoJson.length();

            log.debug("生成包含完整用户信息的JWT token，用户ID: {}, 用户信息大小: {} 字符, token总大小: {} 字符",
                     userInfo.getUserId(), userInfoSize, tokenSize);

            // 如果token过大，记录警告
            if (tokenSize > 4096) { // 4KB警告阈值
                log.warn("JWT token大小过大: {} 字符，建议优化用户信息结构。用户ID: {}",
                        tokenSize, userInfo.getUserId());
            }

            return token;
        } catch (JsonProcessingException e) {
            log.error("序列化用户信息失败，用户ID: {}", userInfo.getUserId(), e);
            throw new RuntimeException("生成JWT token失败：用户信息序列化错误", e);
        }
    }

    /**
     * 从token中获取完整用户信息
     * 从JWT claims中反序列化用户信息
     *
     * @param token JWT token
     * @return 用户信息DTO，如果解析失败返回null
     */
    public JwtUserInfoDTO getUserInfoFromToken(String token) {
        try {
            Object userInfoObj = getClaimFromToken(token, claims -> claims.get(USER_INFO_CLAIM_KEY));
            if (userInfoObj == null) {
                log.warn("JWT token中不包含用户信息，可能是旧版本token");
                return null;
            }

            String userInfoJson = userInfoObj.toString();
            JwtUserInfoDTO userInfo = objectMapper.readValue(userInfoJson, JwtUserInfoDTO.class);

            log.debug("从JWT token中解析用户信息成功，用户ID: {}", userInfo.getUserId());
            return userInfo;
        } catch (Exception e) {
            log.error("从JWT token中解析用户信息失败", e);
            return null;
        }
    }

    /**
     * 检查token是否包含完整用户信息
     * 用于判断是否为新版本的token
     *
     * @param token JWT token
     * @return 是否包含完整用户信息
     */
    public Boolean hasUserInfo(String token) {
        try {
            Object userInfoObj = getClaimFromToken(token, claims -> claims.get(USER_INFO_CLAIM_KEY));
            return userInfoObj != null;
        } catch (Exception e) {
            log.warn("检查JWT token用户信息时发生异常", e);
            return false;
        }
    }

    /**
     * 获取JWT token大小统计信息
     * 用于监控和优化token大小
     *
     * @param token JWT token
     * @return token大小信息
     */
    public TokenSizeInfo getTokenSizeInfo(String token) {
        try {
            int totalSize = token.length();

            // 获取用户信息部分的大小
            int userInfoSize = 0;
            if (hasUserInfo(token)) {
                Object userInfoObj = getClaimFromToken(token, claims -> claims.get(USER_INFO_CLAIM_KEY));
                if (userInfoObj != null) {
                    userInfoSize = userInfoObj.toString().length();
                }
            }

            return new TokenSizeInfo(totalSize, userInfoSize);
        } catch (Exception e) {
            log.warn("获取JWT token大小信息时发生异常", e);
            return new TokenSizeInfo(token.length(), 0);
        }
    }

    /**
     * Token大小信息
     */
    public static class TokenSizeInfo {
        private final int totalSize;
        private final int userInfoSize;

        public TokenSizeInfo(int totalSize, int userInfoSize) {
            this.totalSize = totalSize;
            this.userInfoSize = userInfoSize;
        }

        public int getTotalSize() {
            return totalSize;
        }

        public int getUserInfoSize() {
            return userInfoSize;
        }

        public int getHeaderAndSignatureSize() {
            return totalSize - userInfoSize;
        }

        public double getUserInfoRatio() {
            return totalSize > 0 ? (double) userInfoSize / totalSize : 0.0;
        }

        @Override
        public String toString() {
            return String.format("TokenSizeInfo{total=%d, userInfo=%d, ratio=%.2f%%}",
                               totalSize, userInfoSize, getUserInfoRatio() * 100);
        }
    }
}