package com.extracme.saas.autocare.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.extracme.saas.autocare.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;

/**
 * XML工具类
 * 
 * <AUTHOR>
 * @date 2019-03-18 14:10
 */
@Slf4j
public class XmlUtils {
    
    /**
     * 将xml字符串转为Map（String→Document→Map）
     * 
     * @param xmlString xml字符串
     * @return Map<String, Object> 转换后的Map对象
     * @throws BusinessException 当XML解析失败时抛出业务异常
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> xmlToMap(String xmlString) {
        if (StringUtils.isBlank(xmlString)) {
            log.warn("XML字符串为空，返回空Map");
            return new HashMap<>(16);
        }
        
        Map<String, Object> map = new HashMap<>(16);
        Document document;
        try {
            document = DocumentHelper.parseText(xmlString);
        } catch (DocumentException e) {
            log.error("XML解析失败: {}", e.getMessage(), e);
            throw new BusinessException("XML解析失败: " + e.getMessage());
        }
        
        Element rootElement = document.getRootElement();
        if (rootElement == null) {
            log.warn("XML根元素为空，返回空Map");
            return map;
        }
        
        for (Iterator<Element> iterator = rootElement.elementIterator(); iterator.hasNext(); ) {
            Element element = iterator.next();
            List<Element> list = element.elements();
            if (CollectionUtils.isEmpty(list)) {
                map.put(element.getName(), element.getText());
            } else {
                map.put(element.getName(), elementToMap(element));
            }
        }
        return map;
    }

    /**
     * 将Element对象转为Map（String→Document→Element→Map）
     * 
     * @param element element对象
     * @return Map<String, Object> 转换后的Map对象
     */
    @SuppressWarnings({"unchecked"})
    private static Map<String, Object> elementToMap(Element element) {
        if (element == null) {
            log.warn("Element对象为空，返回空Map");
            return new HashMap<>(16);
        }
        
        Map<String, Object> map = new HashMap<>(16);
        List<Element> elementList = element.elements();
        
        for (Element childElement : elementList) {
            String elementName = childElement.getName();
            if (CollectionUtils.isEmpty(childElement.elements())) {
                // 处理叶子节点
                handleLeafElement(map, childElement, elementName);
            } else {
                // 处理非叶子节点
                handleNonLeafElement(map, childElement, elementName);
            }
        }
        return map;
    }
    
    /**
     * 处理叶子节点元素
     * 
     * @param map 目标Map
     * @param childElement 子元素
     * @param elementName 元素名称
     */
    @SuppressWarnings("unchecked")
    private static void handleLeafElement(Map<String, Object> map, Element childElement, String elementName) {
        String elementText = childElement.getText();
        Object existingValue = map.get(elementName);
        
        if (existingValue == null) {
            map.put(elementName, elementText);
        } else {
            List<Object> mapList;
            if (existingValue instanceof List) {
                mapList = (List<Object>) existingValue;
                mapList.add(elementText);
            } else {
                mapList = new ArrayList<>();
                mapList.add(existingValue);
                mapList.add(elementText);
                map.put(elementName, mapList);
            }
        }
    }
    
    /**
     * 处理非叶子节点元素
     * 
     * @param map 目标Map
     * @param childElement 子元素
     * @param elementName 元素名称
     */
    @SuppressWarnings("unchecked")
    private static void handleNonLeafElement(Map<String, Object> map, Element childElement, String elementName) {
        Map<String, Object> childMap = elementToMap(childElement);
        Object existingValue = map.get(elementName);
        
        if (existingValue == null) {
            map.put(elementName, childMap);
        } else {
            List<Object> mapList;
            if (existingValue instanceof List) {
                mapList = (List<Object>) existingValue;
                mapList.add(childMap);
            } else {
                mapList = new ArrayList<>();
                mapList.add(existingValue);
                mapList.add(childMap);
                map.put(elementName, mapList);
            }
        }
    }
}
