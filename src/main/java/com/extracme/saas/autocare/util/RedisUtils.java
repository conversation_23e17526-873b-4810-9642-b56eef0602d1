package com.extracme.saas.autocare.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis 工具类
 * 提供常用的 Redis 操作方法，包括基础操作、数据结构操作、分布式锁等功能
 * 
 * <AUTHOR>
 * @date 2024/03/21
 */
@Slf4j
@Component
public class RedisUtils {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    // =============================基础操作=============================

    /**
     * 指定缓存失效时间
     * 
     * @param key  键
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("设置缓存失效时间失败，key: {}, time: {}", key, time, e);
            return false;
        }
    }

    /**
     * 根据key获取过期时间
     * 
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key) {
        try {
            return redisTemplate.getExpire(key, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取缓存过期时间失败，key: {}", key, e);
            return -1;
        }
    }

    /**
     * 判断key是否存在
     * 
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("判断key是否存在失败，key: {}", key, e);
            return false;
        }
    }

    /**
     * 删除缓存
     * 
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public void del(String... key) {
        try {
            if (key != null && key.length > 0) {
                if (key.length == 1) {
                    redisTemplate.delete(key[0]);
                } else {
                    redisTemplate.delete((Collection<String>) CollectionUtils.arrayToList(key));
                }
            }
        } catch (Exception e) {
            log.error("删除缓存失败，key: {}", Arrays.toString(key), e);
        }
    }

    // ============================String=============================

    /**
     * 普通缓存获取
     * 
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        try {
            return key == null ? null : redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("获取缓存失败，key: {}", key, e);
            return null;
        }
    }

    /**
     * 普通缓存放入
     * 
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("设置缓存失败，key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 普通缓存放入并设置时间
     * 
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("设置缓存失败，key: {}, value: {}, time: {}", key, value, time, e);
            return false;
        }
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta 要增加几(大于0)
     * @return 递增后的值
     */
    public long incr(String key, long delta) {
        try {
            if (delta < 0) {
                throw new RuntimeException("递增因子必须大于0");
            }
            Long result = redisTemplate.opsForValue().increment(key, delta);
            return result != null ? result : 0L;
        } catch (Exception e) {
            log.error("递增操作失败，key: {}, delta: {}", key, delta, e);
            throw e;
        }
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     * @return 递减后的值
     */
    public long decr(String key, long delta) {
        try {
            if (delta < 0) {
                throw new RuntimeException("递减因子必须大于0");
            }
            Long result = redisTemplate.opsForValue().increment(key, -delta);
            return result != null ? result : 0L;
        } catch (Exception e) {
            log.error("递减操作失败，key: {}, delta: {}", key, delta, e);
            throw e;
        }
    }

    /**
     * 字符串追加
     * 
     * @param key   键
     * @param value 要追加的值
     * @return 追加后字符串的长度
     */
    public Integer append(String key, String value) {
        try {
            return redisTemplate.opsForValue().append(key, value);
        } catch (Exception e) {
            log.error("字符串追加失败，key: {}, value: {}", key, value, e);
            return null;
        }
    }

    // ================================Map=================================

    /**
     * HashGet
     * 
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return 值
     */
    public Object hget(String key, String item) {
        try {
            return redisTemplate.opsForHash().get(key, item);
        } catch (Exception e) {
            log.error("Hash获取失败，key: {}, item: {}", key, item, e);
            return null;
        }
    }

    /**
     * 获取hashKey对应的所有键值
     * 
     * @param key 键
     * @return 对应的多个键值
     */
    public Map<Object, Object> hmget(String key) {
        try {
            return redisTemplate.opsForHash().entries(key);
        } catch (Exception e) {
            log.error("Hash获取所有键值失败，key: {}", key, e);
            return new HashMap<>();
        }
    }

    /**
     * HashSet
     * 
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     */
    public boolean hmset(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            log.error("Hash设置多个键值失败，key: {}, map: {}", key, map, e);
            return false;
        }
    }

    /**
     * HashSet 并设置时间
     * 
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("Hash设置多个键值并设置时间失败，key: {}, map: {}, time: {}", key, map, time, e);
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     * 
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            log.error("Hash设置单个键值失败，key: {}, item: {}, value: {}", key, item, value, e);
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     * 
     * @param key   键
     * @param item  项
     * @param value 值
     * @param time  时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value, long time) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("Hash设置单个键值并设置时间失败，key: {}, item: {}, value: {}, time: {}", key, item, value, time, e);
            return false;
        }
    }

    /**
     * 删除hash表中的值
     * 
     * @param key  键 不能为null
     * @param item 项 可以使多个 不能为null
     */
    public void hdel(String key, Object... item) {
        try {
            redisTemplate.opsForHash().delete(key, item);
        } catch (Exception e) {
            log.error("Hash删除失败，key: {}, item: {}", key, Arrays.toString(item), e);
        }
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     */
    public boolean hHasKey(String key, String item) {
        try {
            return redisTemplate.opsForHash().hasKey(key, item);
        } catch (Exception e) {
            log.error("判断Hash是否存在失败，key: {}, item: {}", key, item, e);
            return false;
        }
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key  键
     * @param item 项
     * @param by   要增加几(大于0)
     * @return 递增后的值
     */
    public double hincr(String key, String item, double by) {
        try {
            return redisTemplate.opsForHash().increment(key, item, by);
        } catch (Exception e) {
            log.error("Hash递增失败，key: {}, item: {}, by: {}", key, item, by, e);
            throw e;
        }
    }

    /**
     * hash递减
     *
     * @param key  键
     * @param item 项
     * @param by   要减少记(小于0)
     * @return 递减后的值
     */
    public double hdecr(String key, String item, double by) {
        try {
            return redisTemplate.opsForHash().increment(key, item, -by);
        } catch (Exception e) {
            log.error("Hash递减失败，key: {}, item: {}, by: {}", key, item, by, e);
            throw e;
        }
    }

    // ============================Set=============================

    /**
     * 根据key获取Set中的所有值
     *
     * @param key 键
     * @return Set集合
     */
    public Set<Object> sGet(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            log.error("获取Set所有值失败，key: {}", key, e);
            return new HashSet<>();
        }
    }

    /**
     * 根据value从一个set中查询,是否存在
     *
     * @param key   键
     * @param value 值
     * @return true 存在 false不存在
     */
    public boolean sHasKey(String key, Object value) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, value));
        } catch (Exception e) {
            log.error("判断Set是否存在失败，key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSet(String key, Object... values) {
        try {
            Long result = redisTemplate.opsForSet().add(key, values);
            return result != null ? result : 0L;
        } catch (Exception e) {
            log.error("Set添加失败，key: {}, values: {}", key, Arrays.toString(values), e);
            return 0;
        }
    }

    /**
     * 将set数据放入缓存
     *
     * @param key    键
     * @param time   时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSetAndTime(String key, long time, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, values);
            if (time > 0) {
                expire(key, time);
            }
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.error("Set添加并设置时间失败，key: {}, time: {}, values: {}", key, time, Arrays.toString(values), e);
            return 0;
        }
    }

    /**
     * 获取set缓存的长度
     *
     * @param key 键
     * @return 长度
     */
    public long sGetSetSize(String key) {
        try {
            Long result = redisTemplate.opsForSet().size(key);
            return result != null ? result : 0L;
        } catch (Exception e) {
            log.error("获取Set长度失败，key: {}", key, e);
            return 0;
        }
    }

    /**
     * 移除值为value的
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 移除的个数
     */
    public long setRemove(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().remove(key, values);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.error("Set移除失败，key: {}, values: {}", key, Arrays.toString(values), e);
            return 0;
        }
    }

    // ===============================List=================================

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束 0 到 -1代表所有值
     * @return List集合
     */
    public List<Object> lGet(String key, long start, long end) {
        try {
            return redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            log.error("获取List范围值失败，key: {}, start: {}, end: {}", key, start, end, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取list缓存的长度
     *
     * @param key 键
     * @return 长度
     */
    public long lGetListSize(String key) {
        try {
            Long result = redisTemplate.opsForList().size(key);
            return result != null ? result : 0L;
        } catch (Exception e) {
            log.error("获取List长度失败，key: {}", key, e);
            return 0;
        }
    }

    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return 值
     */
    public Object lGetIndex(String key, long index) {
        try {
            return redisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            log.error("获取List索引值失败，key: {}, index: {}", key, index, e);
            return null;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean lSet(String key, Object value) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            log.error("List添加失败，key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return true成功 false失败
     */
    public boolean lSet(String key, Object value, long time) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("List添加并设置时间失败，key: {}, value: {}, time: {}", key, value, time, e);
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean lSet(String key, List<Object> value) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            return true;
        } catch (Exception e) {
            log.error("List批量添加失败，key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return true成功 false失败
     */
    public boolean lSet(String key, List<Object> value, long time) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("List批量添加并设置时间失败，key: {}, value: {}, time: {}", key, value, time, e);
            return false;
        }
    }

    /**
     * 根据索引修改list中的某条数据
     *
     * @param key   键
     * @param index 索引
     * @param value 值
     * @return true成功 false失败
     */
    public boolean lUpdateIndex(String key, long index, Object value) {
        try {
            redisTemplate.opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            log.error("List更新索引值失败，key: {}, index: {}, value: {}", key, index, value, e);
            return false;
        }
    }

    /**
     * 移除N个值为value
     *
     * @param key   键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     */
    public long lRemove(String key, long count, Object value) {
        try {
            Long remove = redisTemplate.opsForList().remove(key, count, value);
            return remove != null ? remove : 0L;
        } catch (Exception e) {
            log.error("List移除失败，key: {}, count: {}, value: {}", key, count, value, e);
            return 0;
        }
    }

    /**
     * 从列表左侧弹出一个元素
     *
     * @param key 键
     * @return 弹出的元素
     */
    public Object lLeftPop(String key) {
        try {
            return redisTemplate.opsForList().leftPop(key);
        } catch (Exception e) {
            log.error("List左侧弹出失败，key: {}", key, e);
            return null;
        }
    }

    /**
     * 从列表右侧弹出一个元素
     *
     * @param key 键
     * @return 弹出的元素
     */
    public Object lRightPop(String key) {
        try {
            return redisTemplate.opsForList().rightPop(key);
        } catch (Exception e) {
            log.error("List右侧弹出失败，key: {}", key, e);
            return null;
        }
    }

    /**
     * 从列表左侧推入一个元素
     *
     * @param key   键
     * @param value 值
     * @return 推入后列表的长度
     */
    public Long lLeftPush(String key, Object value) {
        try {
            return redisTemplate.opsForList().leftPush(key, value);
        } catch (Exception e) {
            log.error("List左侧推入失败，key: {}, value: {}", key, value, e);
            return null;
        }
    }

    /**
     * 从列表右侧推入一个元素
     *
     * @param key   键
     * @param value 值
     * @return 推入后列表的长度
     */
    public Long lRightPush(String key, Object value) {
        try {
            return redisTemplate.opsForList().rightPush(key, value);
        } catch (Exception e) {
            log.error("List右侧推入失败，key: {}, value: {}", key, value, e);
            return null;
        }
    }

    // ============================ZSet=============================

    /**
     * 向有序集合添加一个成员
     *
     * @param key   键
     * @param value 值
     * @param score 分数
     * @return true成功 false失败
     */
    public boolean zAdd(String key, Object value, double score) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForZSet().add(key, value, score));
        } catch (Exception e) {
            log.error("ZSet添加失败，key: {}, value: {}, score: {}", key, value, score, e);
            return false;
        }
    }

    /**
     * 获取有序集合的成员数
     *
     * @param key 键
     * @return 成员数
     */
    public Long zCard(String key) {
        try {
            return redisTemplate.opsForZSet().size(key);
        } catch (Exception e) {
            log.error("获取ZSet成员数失败，key: {}", key, e);
            return 0L;
        }
    }

    /**
     * 计算在有序集合中指定区间分数的成员数
     *
     * @param key 键
     * @param min 最小分数
     * @param max 最大分数
     * @return 成员数
     */
    public Long zCount(String key, double min, double max) {
        try {
            return redisTemplate.opsForZSet().count(key, min, max);
        } catch (Exception e) {
            log.error("ZSet计算区间成员数失败，key: {}, min: {}, max: {}", key, min, max, e);
            return 0L;
        }
    }

    /**
     * 获取有序集合中指定成员的索引
     *
     * @param key   键
     * @param value 值
     * @return 索引
     */
    public Long zRank(String key, Object value) {
        try {
            return redisTemplate.opsForZSet().rank(key, value);
        } catch (Exception e) {
            log.error("获取ZSet成员索引失败，key: {}, value: {}", key, value, e);
            return null;
        }
    }

    /**
     * 获取有序集合中指定成员的分数值
     *
     * @param key   键
     * @param value 值
     * @return 分数值
     */
    public Double zScore(String key, Object value) {
        try {
            return redisTemplate.opsForZSet().score(key, value);
        } catch (Exception e) {
            log.error("获取ZSet成员分数失败，key: {}, value: {}", key, value, e);
            return null;
        }
    }

    /**
     * 通过索引区间返回有序集合成指定区间内的成员
     *
     * @param key   键
     * @param start 开始位置
     * @param end   结束位置
     * @return 成员集合
     */
    public Set<Object> zRange(String key, long start, long end) {
        try {
            return redisTemplate.opsForZSet().range(key, start, end);
        } catch (Exception e) {
            log.error("获取ZSet范围成员失败，key: {}, start: {}, end: {}", key, start, end, e);
            return new HashSet<>();
        }
    }

    /**
     * 移除有序集合中的一个或多个成员
     *
     * @param key    键
     * @param values 值
     * @return 移除的成员数量
     */
    public Long zRemove(String key, Object... values) {
        try {
            return redisTemplate.opsForZSet().remove(key, values);
        } catch (Exception e) {
            log.error("ZSet移除成员失败，key: {}, values: {}", key, Arrays.toString(values), e);
            return 0L;
        }
    }

    /**
     * 移除有序集合中给定的排名区间的所有成员
     *
     * @param key   键
     * @param start 开始位置
     * @param end   结束位置
     * @return 移除的成员数量
     */
    public Long zRemoveRange(String key, long start, long end) {
        try {
            return redisTemplate.opsForZSet().removeRange(key, start, end);
        } catch (Exception e) {
            log.error("ZSet移除范围成员失败，key: {}, start: {}, end: {}", key, start, end, e);
            return 0L;
        }
    }

    // ============================分布式锁=============================

    /**
     * 尝试获取分布式锁
     *
     * @param lockKey    锁的key
     * @param requestId  请求标识
     * @param expireTime 超期时间(秒)
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, String requestId, long expireTime) {
        try {
            String result = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, requestId, expireTime, TimeUnit.SECONDS) ? "OK" : null;
            return "OK".equals(result);
        } catch (Exception e) {
            log.error("获取分布式锁失败，lockKey: {}, requestId: {}, expireTime: {}", lockKey, requestId, expireTime, e);
            return false;
        }
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey   锁的key
     * @param requestId 请求标识
     * @return 是否释放成功
     */
    public boolean releaseLock(String lockKey, String requestId) {
        try {
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            Object result = stringRedisTemplate.execute(
                    (org.springframework.data.redis.core.script.RedisScript<Long>)
                    org.springframework.data.redis.core.script.RedisScript.of(script, Long.class),
                    Collections.singletonList(lockKey),
                    requestId
            );
            return Long.valueOf(1).equals(result);
        } catch (Exception e) {
            log.error("释放分布式锁失败，lockKey: {}, requestId: {}", lockKey, requestId, e);
            return false;
        }
    }

    // ============================缓存模式=============================

    /**
     * 缓存穿透保护的获取或设置方法
     * 如果缓存中不存在，则调用数据加载器获取数据并缓存
     *
     * @param key        缓存键
     * @param expireTime 过期时间(秒)
     * @param dataLoader 数据加载器
     * @param <T>        数据类型
     * @return 数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getOrSet(String key, long expireTime, Supplier<T> dataLoader) {
        try {
            // 先从缓存获取
            Object cached = get(key);
            if (cached != null) {
                return (T) cached;
            }

            // 缓存不存在，使用分布式锁防止缓存击穿
            String lockKey = "lock:" + key;
            String requestId = UUID.randomUUID().toString();

            if (tryLock(lockKey, requestId, 10)) {
                try {
                    // 再次检查缓存（双重检查）
                    cached = get(key);
                    if (cached != null) {
                        return (T) cached;
                    }

                    // 加载数据
                    T data = dataLoader.get();
                    if (data != null) {
                        // 设置缓存
                        set(key, data, expireTime);
                    } else {
                        // 防止缓存穿透，设置空值缓存，较短过期时间
                        set(key, "", Math.min(expireTime, 300));
                    }
                    return data;
                } finally {
                    releaseLock(lockKey, requestId);
                }
            } else {
                // 获取锁失败，等待一段时间后重试
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                cached = get(key);
                return cached != null ? (T) cached : dataLoader.get();
            }
        } catch (Exception e) {
            log.error("缓存获取或设置失败，key: {}", key, e);
            // 缓存失败时直接调用数据加载器
            return dataLoader.get();
        }
    }

    /**
     * 批量删除缓存（支持通配符）
     *
     * @param pattern 匹配模式
     * @return 删除的key数量
     */
    public long deleteByPattern(String pattern) {
        try {
            Set<String> keys = stringRedisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                stringRedisTemplate.delete(keys);
                return keys.size();
            }
            return 0;
        } catch (Exception e) {
            log.error("批量删除缓存失败，pattern: {}", pattern, e);
            return 0;
        }
    }

    /**
     * 获取匹配模式的所有key
     *
     * @param pattern 匹配模式
     * @return key集合
     */
    public Set<String> keys(String pattern) {
        try {
            return stringRedisTemplate.keys(pattern);
        } catch (Exception e) {
            log.error("获取匹配key失败，pattern: {}", pattern, e);
            return new HashSet<>();
        }
    }

    /**
     * 检查Redis连接状态
     *
     * @return true连接正常 false连接异常
     */
    public boolean ping() {
        try {
            String pong = stringRedisTemplate.execute((org.springframework.data.redis.core.RedisCallback<String>) connection -> {
                return connection.ping();
            });
            return "PONG".equals(pong);
        } catch (Exception e) {
            log.error("Redis连接检查失败", e);
            return false;
        }
    }

    /**
     * 获取Redis信息
     *
     * @return Redis信息
     */
    public String info() {
        try {
            return stringRedisTemplate.execute((org.springframework.data.redis.core.RedisCallback<String>) connection -> {
                Object info = connection.info();
                return info != null ? info.toString() : "无法获取Redis信息";
            });
        } catch (Exception e) {
            log.error("获取Redis信息失败", e);
            return "获取Redis信息失败: " + e.getMessage();
        }
    }
}
