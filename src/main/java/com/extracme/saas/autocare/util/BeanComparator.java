package com.extracme.saas.autocare.util;

import com.extracme.saas.autocare.annotation.FieldRemark;
import lombok.extern.slf4j.Slf4j;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * 比较字段值
 * <AUTHOR>
 * @date 2025-05-29 14:08
 *
 */
@Slf4j
public class BeanComparator {

    /**
     * 比较两个对象属性差异
     * @param obj1 对象1
     * @param obj2 对象2
     * @return 差异列表（字段名、注解说明、值1、值2）
     */
    public static List<Map<String, String>> compareObjects(Object obj1, Object obj2) {
        List<Map<String, String>> differences = new ArrayList<>();
        if (obj1 == null || obj2 == null) {
            log.error("比较对象不能为空");
            return null;
        }

        Class<?> clazz = obj1.getClass();
        if (!clazz.equals(obj2.getClass())) {
            log.error("两个对象类型不一致");
            return null;
        }

        for (Field field : clazz.getDeclaredFields()) {
            try {
                field.setAccessible(true);  // 允许访问私有字段
                String fieldName = field.getName();
                if ("createBy".equals(fieldName) || "updateBy".equals(fieldName)
                        || "createdTime".equals(fieldName) || "updatedTime".equals(fieldName)) {
                    continue;
                }
                Object value1 = field.get(obj1);
                Object value2 = field.get(obj2);
                FieldRemark fieldRemark = field.getAnnotation(FieldRemark.class);
                String remarkName = (fieldRemark != null) ? fieldRemark.value() : field.getName();

                // 处理BigDecimal的精度问题
                if (value1 instanceof BigDecimal && value2 instanceof BigDecimal) {
                    BigDecimal bd1 = (BigDecimal) value1;
                    BigDecimal bd2 = (BigDecimal) value2;
                    if (bd1.compareTo(bd2) != 0) { // 使用compareTo忽略精度差异
                        Map<String, String> diff = new HashMap<>();
                        diff.put("fieldName", remarkName);
                        diff.put("oldValue", String.valueOf(value1));
                        diff.put("newValue", String.valueOf(value2));
                        differences.add(diff);
                    }
                } else if (!Objects.equals(value1, value2)) {
                    Map<String, String> diff = new HashMap<>();
                    diff.put("fieldName", remarkName);
                    diff.put("oldValue", String.valueOf(value1));
                    diff.put("newValue", String.valueOf(value2));
                    differences.add(diff);
                }
            } catch (IllegalAccessException e) {
                log.error("反射访问字段失败: {}", field.getName(), e);
                return null;
            }
        }
        return differences;
    }
}
