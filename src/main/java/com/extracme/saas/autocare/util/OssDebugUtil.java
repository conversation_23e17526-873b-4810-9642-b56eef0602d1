package com.extracme.saas.autocare.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.HttpMethod;
import com.extracme.saas.autocare.config.OssConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.net.URL;
import java.util.Date;

/**
 * OSS调试工具类
 * 用于诊断OSS签名问题和配置问题
 */
@Slf4j
public class OssDebugUtil {

    /**
     * 验证OSS配置
     */
    public static void validateOssConfig(OssConfig ossConfig, OSS ossClient) {
        log.info("=== OSS配置验证开始 ===");
        
        if (ossConfig == null) {
            log.error("❌ OSS配置为空");
            return;
        }
        
        log.info("✅ OSS配置对象存在");
        log.info("📋 Bucket名称: {}", ossConfig.getBucketName());
        log.info("📋 Endpoint: {}", ossConfig.getEndpoint());
        
        if (ossConfig.getUpload() != null) {
            log.info("📋 Base URL: {}", ossConfig.getUpload().getBaseUrl());
        } else {
            log.warn("⚠️ Upload配置为空");
        }
        
        if (ossClient == null) {
            log.error("❌ OSS客户端为空");
        } else {
            log.info("✅ OSS客户端存在");
        }
        
        log.info("=== OSS配置验证结束 ===");
    }

    /**
     * 调试预签名URL生成
     */
    public static void debugPresignedUrl(OSS ossClient, String bucketName, String objectKey, 
                                       String uploadId, int partNumber, long expiration) {
        log.info("=== 预签名URL调试开始 ===");
        
        try {
            // 验证参数
            log.info("📋 参数验证:");
            log.info("  - Bucket: {}", bucketName);
            log.info("  - Object Key: {}", objectKey);
            log.info("  - Upload ID: {}", uploadId);
            log.info("  - Part Number: {}", partNumber);
            log.info("  - Expiration: {}s", expiration);
            
            if (!StringUtils.hasText(bucketName)) {
                log.error("❌ Bucket名称为空");
                return;
            }
            
            if (!StringUtils.hasText(objectKey)) {
                log.error("❌ Object Key为空");
                return;
            }
            
            if (!StringUtils.hasText(uploadId)) {
                log.error("❌ Upload ID为空");
                return;
            }
            
            if (partNumber < 1 || partNumber > 10000) {
                log.error("❌ Part Number无效: {}", partNumber);
                return;
            }
            
            // 构造请求
            log.info("🔧 构造预签名URL请求...");
            Date expirationDate = new Date(System.currentTimeMillis() + expiration * 1000);
            
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                bucketName, objectKey, HttpMethod.PUT);
            request.setExpiration(expirationDate);
            request.addQueryParameter("partNumber", String.valueOf(partNumber));
            request.addQueryParameter("uploadId", uploadId);
            
            log.info("📋 请求详情:");
            log.info("  - HTTP Method: {}", HttpMethod.PUT);
            log.info("  - Expiration: {}", expirationDate);
            log.info("  - Query Parameters: partNumber={}, uploadId={}", partNumber, uploadId);
            
            // 生成URL
            log.info("🚀 生成预签名URL...");
            URL url = ossClient.generatePresignedUrl(request);
            String presignedUrl = url.toString();
            
            log.info("✅ 预签名URL生成成功");
            log.info("🔗 URL: {}", presignedUrl);
            
            // 分析URL组成
            analyzePresignedUrl(presignedUrl);
            
        } catch (Exception e) {
            log.error("❌ 预签名URL生成失败", e);
            log.error("💡 错误分析:");
            
            if (e.getMessage().contains("signature")) {
                log.error("  - 这是签名错误，可能的原因:");
                log.error("    1. Access Key ID或Secret Key不正确");
                log.error("    2. Upload ID不是有效的OSS Upload ID");
                log.error("    3. Object Key路径格式不正确");
                log.error("    4. 时间同步问题");
            }
            
            if (e.getMessage().contains("bucket")) {
                log.error("  - 这是Bucket相关错误，检查:");
                log.error("    1. Bucket名称是否正确");
                log.error("    2. Bucket是否存在");
                log.error("    3. 是否有访问权限");
            }
        }
        
        log.info("=== 预签名URL调试结束 ===");
    }

    /**
     * 分析预签名URL的组成
     */
    private static void analyzePresignedUrl(String presignedUrl) {
        log.info("🔍 URL分析:");
        
        try {
            URL url = new URL(presignedUrl);
            log.info("  - Protocol: {}", url.getProtocol());
            log.info("  - Host: {}", url.getHost());
            log.info("  - Path: {}", url.getPath());
            log.info("  - Query: {}", url.getQuery());
            
            // 分析查询参数
            if (StringUtils.hasText(url.getQuery())) {
                String[] params = url.getQuery().split("&");
                log.info("  - Query Parameters:");
                for (String param : params) {
                    String[] kv = param.split("=", 2);
                    if (kv.length == 2) {
                        log.info("    * {}: {}", kv[0], kv[1]);
                    }
                }
            }
            
        } catch (Exception e) {
            log.warn("URL分析失败: {}", e.getMessage());
        }
    }

    /**
     * 测试OSS连接
     */
    public static void testOssConnection(OSS ossClient, String bucketName) {
        log.info("=== OSS连接测试开始 ===");
        
        try {
            log.info("🔧 测试Bucket访问权限...");
            boolean exists = ossClient.doesBucketExist(bucketName);
            
            if (exists) {
                log.info("✅ Bucket存在且可访问: {}", bucketName);
            } else {
                log.error("❌ Bucket不存在或无访问权限: {}", bucketName);
            }
            
        } catch (Exception e) {
            log.error("❌ OSS连接测试失败", e);
            log.error("💡 可能的原因:");
            log.error("  1. 网络连接问题");
            log.error("  2. Access Key配置错误");
            log.error("  3. Endpoint配置错误");
            log.error("  4. 权限不足");
        }
        
        log.info("=== OSS连接测试结束 ===");
    }

    /**
     * 验证Upload ID格式
     */
    public static boolean validateUploadId(String uploadId) {
        if (!StringUtils.hasText(uploadId)) {
            log.warn("⚠️ Upload ID为空");
            return false;
        }
        
        // OSS的Upload ID通常是一个长字符串，包含字母、数字和特殊字符
        if (uploadId.length() < 10) {
            log.warn("⚠️ Upload ID长度可能不正确: {}", uploadId);
            return false;
        }
        
        // 检查是否是我们内部生成的ID（以"upload-"开头）
        if (uploadId.startsWith("upload-")) {
            log.error("❌ 检测到内部会话ID被用作OSS Upload ID: {}", uploadId);
            log.error("💡 这是常见错误！应该使用OSS返回的真实Upload ID");
            return false;
        }
        
        log.info("✅ Upload ID格式验证通过: {}", uploadId);
        return true;
    }

    /**
     * 记录详细的错误信息
     */
    public static void logDetailedError(String operation, Exception e) {
        log.error("=== {} 详细错误信息 ===", operation);
        log.error("错误类型: {}", e.getClass().getSimpleName());
        log.error("错误消息: {}", e.getMessage());
        
        if (e.getCause() != null) {
            log.error("根本原因: {}", e.getCause().getMessage());
        }
        
        // 针对常见错误提供解决建议
        String message = e.getMessage();
        if (message != null) {
            if (message.contains("signature")) {
                log.error("💡 签名错误解决建议:");
                log.error("  1. 检查Access Key ID和Secret Key是否正确");
                log.error("  2. 确认使用的是OSS返回的真实Upload ID");
                log.error("  3. 检查系统时间是否同步");
                log.error("  4. 验证Object Key路径格式");
            }
            
            if (message.contains("NoSuchUpload")) {
                log.error("💡 Upload不存在错误解决建议:");
                log.error("  1. 确认Upload ID是否正确");
                log.error("  2. 检查Upload是否已过期");
                log.error("  3. 验证Bucket和Object Key是否匹配");
            }
        }
        
        log.error("=== 错误信息结束 ===");
    }
}
