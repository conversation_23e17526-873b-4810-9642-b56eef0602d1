package com.extracme.saas.autocare.util;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;

/**
 * IP地址获取工具类
 *
 * <p>提供获取客户端真实IP地址的工具方法，支持各种代理和负载均衡环境。</p>
 *
 * <h3>支持的代理请求头：</h3>
 * <ul>
 *   <li>X-Forwarded-For - 标准的代理转发头</li>
 *   <li>X-Real-IP - Nginx等反向代理常用</li>
 *   <li>Proxy-Client-IP - Apache等代理服务器</li>
 *   <li>WL-Proxy-Client-IP - WebLogic代理</li>
 *   <li>HTTP_CLIENT_IP - 某些代理服务器</li>
 *   <li>HTTP_X_FORWARDED_FOR - 某些代理服务器</li>
 * </ul>
 *
 * <h3>处理逻辑：</h3>
 * <ul>
 *   <li>按优先级依次检查各种代理请求头</li>
 *   <li>跳过空值、"unknown"等无效值</li>
 *   <li>处理多级代理情况（取第一个有效IP）</li>
 *   <li>最后回退到直接连接的远程地址</li>
 * </ul>
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
public class IpUtils {

    /**
     * 未知IP标识
     */
    private static final String UNKNOWN = "unknown";

    /**
     * 本地回环地址
     */
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * 私有构造函数，防止实例化
     */
    private IpUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * 获取客户端真实IP地址
     *
     * <p>通过检查各种代理请求头来获取客户端的真实IP地址。</p>
     * <p>在多级代理环境中，会返回最原始的客户端IP地址。</p>
     *
     * <h3>检查顺序：</h3>
     * <ol>
     *   <li>X-Forwarded-For</li>
     *   <li>X-Real-IP</li>
     *   <li>Proxy-Client-IP</li>
     *   <li>WL-Proxy-Client-IP</li>
     *   <li>HTTP_CLIENT_IP</li>
     *   <li>HTTP_X_FORWARDED_FOR</li>
     *   <li>request.getRemoteAddr()</li>
     * </ol>
     *
     * @param request HTTP请求对象，不能为null
     * @return 客户端IP地址，如果无法获取则返回"unknown"
     * @throws IllegalArgumentException 如果request参数为null
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            log.warn("HttpServletRequest参数为null，无法获取客户端IP地址");
            throw new IllegalArgumentException("HttpServletRequest cannot be null");
        }

        String ip = null;

        // 1. 检查 X-Forwarded-For 头（最常用的代理头）
        ip = getIpFromHeader(request, "X-Forwarded-For");
        if (isValidIp(ip)) {
            log.debug("从X-Forwarded-For头获取到IP: {}", ip);
            return extractFirstValidIp(ip);
        }

        // 2. 检查 X-Real-IP 头（Nginx等常用）
        ip = getIpFromHeader(request, "X-Real-IP");
        if (isValidIp(ip)) {
            log.debug("从X-Real-IP头获取到IP: {}", ip);
            return ip.trim();
        }

        // 3. 检查 Proxy-Client-IP 头
        ip = getIpFromHeader(request, "Proxy-Client-IP");
        if (isValidIp(ip)) {
            log.debug("从Proxy-Client-IP头获取到IP: {}", ip);
            return extractFirstValidIp(ip);
        }

        // 4. 检查 WL-Proxy-Client-IP 头（WebLogic）
        ip = getIpFromHeader(request, "WL-Proxy-Client-IP");
        if (isValidIp(ip)) {
            log.debug("从WL-Proxy-Client-IP头获取到IP: {}", ip);
            return extractFirstValidIp(ip);
        }

        // 5. 检查 HTTP_CLIENT_IP 头
        ip = getIpFromHeader(request, "HTTP_CLIENT_IP");
        if (isValidIp(ip)) {
            log.debug("从HTTP_CLIENT_IP头获取到IP: {}", ip);
            return extractFirstValidIp(ip);
        }

        // 6. 检查 HTTP_X_FORWARDED_FOR 头
        ip = getIpFromHeader(request, "HTTP_X_FORWARDED_FOR");
        if (isValidIp(ip)) {
            log.debug("从HTTP_X_FORWARDED_FOR头获取到IP: {}", ip);
            return extractFirstValidIp(ip);
        }

        // 7. 最后使用直接连接的远程地址
        ip = request.getRemoteAddr();
        if (isValidIp(ip)) {
            log.debug("从RemoteAddr获取到IP: {}", ip);
            return ip.trim();
        }

        log.warn("无法获取有效的客户端IP地址，所有方法都返回了无效值");
        return UNKNOWN;
    }

    /**
     * 从请求头中获取IP地址
     *
     * @param request HTTP请求对象
     * @param headerName 请求头名称
     * @return IP地址字符串，可能为null
     */
    private static String getIpFromHeader(HttpServletRequest request, String headerName) {
        try {
            return request.getHeader(headerName);
        } catch (Exception e) {
            log.warn("获取请求头 {} 时发生异常: {}", headerName, e.getMessage());
            return null;
        }
    }

    /**
     * 检查IP地址是否有效
     *
     * @param ip IP地址字符串
     * @return 如果IP有效返回true，否则返回false
     */
    private static boolean isValidIp(String ip) {
        return ip != null 
            && !ip.trim().isEmpty() 
            && !UNKNOWN.equalsIgnoreCase(ip.trim());
    }

    /**
     * 从可能包含多个IP的字符串中提取第一个有效IP
     *
     * <p>在多级代理环境中，X-Forwarded-For等头可能包含多个IP地址，
     * 格式如：client_ip, proxy1_ip, proxy2_ip</p>
     * <p>第一个IP通常是真实的客户端IP地址。</p>
     *
     * @param ipString 可能包含多个IP的字符串
     * @return 第一个有效的IP地址
     */
    private static String extractFirstValidIp(String ipString) {
        if (ipString == null || ipString.trim().isEmpty()) {
            return UNKNOWN;
        }

        // 处理多个IP的情况（用逗号分隔）
        if (ipString.contains(",")) {
            String[] ips = ipString.split(",");
            for (String singleIp : ips) {
                String trimmedIp = singleIp.trim();
                if (isValidIp(trimmedIp) && !UNKNOWN.equalsIgnoreCase(trimmedIp)) {
                    log.debug("从多IP字符串中提取到第一个有效IP: {}", trimmedIp);
                    return trimmedIp;
                }
            }
        }

        return ipString.trim();
    }

    /**
     * 检查IP地址是否为本地地址
     *
     * @param ip IP地址
     * @return 如果是本地地址返回true，否则返回false
     */
    public static boolean isLocalhost(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        
        String trimmedIp = ip.trim();
        return LOCALHOST_IPV4.equals(trimmedIp) 
            || LOCALHOST_IPV6.equals(trimmedIp)
            || "localhost".equalsIgnoreCase(trimmedIp);
    }

    /**
     * 检查IP地址是否为私有地址
     *
     * <p>私有IP地址范围：</p>
     * <ul>
     *   <li>10.0.0.0 - **************</li>
     *   <li>********** - **************</li>
     *   <li>*********** - ***************</li>
     * </ul>
     *
     * @param ip IP地址
     * @return 如果是私有地址返回true，否则返回false
     */
    public static boolean isPrivateIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }

        try {
            String[] parts = ip.trim().split("\\.");
            if (parts.length != 4) {
                return false;
            }

            int first = Integer.parseInt(parts[0]);
            int second = Integer.parseInt(parts[1]);

            // 10.0.0.0 - **************
            if (first == 10) {
                return true;
            }

            // ********** - **************
            if (first == 172 && second >= 16 && second <= 31) {
                return true;
            }

            // *********** - ***************
            if (first == 192 && second == 168) {
                return true;
            }

            return false;
        } catch (NumberFormatException e) {
            log.debug("IP地址格式不正确，无法判断是否为私有地址: {}", ip);
            return false;
        }
    }
} 