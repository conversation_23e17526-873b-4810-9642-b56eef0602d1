package com.extracme.saas.autocare.util;

import com.extracme.saas.autocare.config.OssConfig;
import com.extracme.saas.autocare.model.dto.FileDTO;
import com.extracme.saas.autocare.model.entity.MtcVehicleRepairPic;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 维修任务图片工具类
 */
@Slf4j
public class RepairPicUtil {

    /**
     * 获取需要插入的图片列表
     *
     * @param existingPics 已存在的图片列表
     * @param newPics      新的图片URL列表
     * @param picType      图片类型
     * @return 需要插入的图片URL列表
     */
    public static List<FileDTO> getInsertPics(List<MtcVehicleRepairPic> existingPics, List<FileDTO> newPics, Integer picType) {
        if (CollectionUtils.isEmpty(newPics)) {
            return new ArrayList<>();
        }

        // 获取已存在的同类型图片URL列表
        List<String> existingUrls = existingPics.stream()
                .filter(pic -> picType.equals(pic.getPicType()))
                .map(MtcVehicleRepairPic::getPicUrl)
                .collect(Collectors.toList());

        // 过滤出需要新增的图片URL
        return newPics.stream()
                .filter(file -> {
                    if (file == null || StringUtils.isEmpty(file.getUrl())) {
                        return false; // 忽略URL为空的情况
                    }
                    
                    // 获取相对路径格式的URL
                    String relativeUrl = removeUrlPrefix(file.getUrl());
                    
                    // 检查两种情况：
                    // 1. 数据库中存储的是相对路径，与转换后的相对路径比较
                    // 2. 数据库中存储的是完整URL，与原始完整URL比较
                    return !existingUrls.contains(relativeUrl) && !existingUrls.contains(file.getUrl());
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取需要删除的图片列表
     *
     * @param existingPics 已存在的图片列表
     * @param newPics      新的图片URL列表
     * @param picType      图片类型
     * @return 需要删除的图片列表
     */
    public static List<MtcVehicleRepairPic> getDeletePics(List<MtcVehicleRepairPic> existingPics, List<FileDTO> newPics, Integer picType) {
        if (CollectionUtils.isEmpty(existingPics)) {
            return new ArrayList<>();
        }

        // 过滤出同类型的图片
        List<MtcVehicleRepairPic> typedPics = existingPics.stream()
                .filter(pic -> picType.equals(pic.getPicType()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(typedPics)) {
            return new ArrayList<>();
        }

        // 如果新图片列表为空，则删除所有同类型图片
        if (CollectionUtils.isEmpty(newPics)) {
            return typedPics;
        }

        List<String> urlList = newPics.stream().map(FileDTO::getUrl).collect(Collectors.toList());

        // 过滤出需要删除的图片
        return typedPics.stream()
                .filter(file -> {
                    if (file == null || StringUtils.isEmpty(file.getPicUrl())) {
                        return false; // 忽略URL为空的情况
                    }

                    // 获取相对路径格式的URL
                    String completeFileUrl = getCompleteFileUrl(file.getPicUrl());

                    // 检查两种情况：
                    // 1. 数据库中存储的是相对路径，转换成完整URL后比较
                    // 2. 数据库中存储的是完整URL，直接比较
                    return !urlList.contains(completeFileUrl) && !urlList.contains(file.getPicUrl());
                })
                .collect(Collectors.toList());
    }

    /**
     * 将图片URL列表转换为MtcVehicleRepairPic对象列表
     *
     * @param picUrls  图片URL列表
     * @param picType  图片类型
     * @param taskNo   任务编号
     * @param operator 操作人
     * @return MtcVehicleRepairPic对象列表
     */
    public static List<MtcVehicleRepairPic> transferStringToVehicleRepairPic(List<FileDTO> picUrls, Integer picType, String taskNo, String operator) {
        if (CollectionUtils.isEmpty(picUrls)) {
            return new ArrayList<>();
        }

        Date now = new Date();

        return picUrls.stream().map(file -> {
            MtcVehicleRepairPic pic = new MtcVehicleRepairPic();
            pic.setTaskNo(taskNo);
            pic.setName(file.getName());
            pic.setPicType(picType);
            pic.setPicUrl(removeUrlPrefix(file.getUrl()));
            pic.setCreateBy(operator);
            pic.setCreatedTime(now);
            pic.setUpdateBy(operator);
            pic.setUpdatedTime(now);
            return pic;
        }).collect(Collectors.toList());
    }

    /**
     * 从URL中移除前缀部分
     *
     * @param url 完整的URL
     * @return 移除前缀后的URL
     */
    private static String removeUrlPrefix(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        
        try {
            // 获取OssConfig中的配置
            OssConfig ossConfig = SpringContextUtil.getBean(OssConfig.class);
            String baseUrl = ossConfig.getUpload().getBaseUrl();
            String env = ossConfig.getUpload().getEnv();
            
            if (StringUtils.isEmpty(baseUrl)) {
                return url;
            }
            
            // 构建完整前缀
            String fullPrefix = baseUrl;
            if (!fullPrefix.endsWith("/") && !StringUtils.isEmpty(env)) {
                fullPrefix += "/";
            }
            
            if (!StringUtils.isEmpty(env)) {
                fullPrefix += env;
                if (!fullPrefix.endsWith("/")) {
                    fullPrefix += "/";
                }
            }
            
            // 移除前缀
            if (url.startsWith(fullPrefix)) {
                return url.substring(fullPrefix.length());
            }
            
            // 处理URL不包含完整前缀的情况
            // 尝试解析URL并提取路径部分
            URL parsedUrl = new URL(url);
            String path = parsedUrl.getPath();
            
            // 如果路径包含环境目录，则移除环境目录部分
            if (!StringUtils.isEmpty(env) && path.startsWith("/" + env + "/")) {
                return path.substring(env.length() + 2); // 移除 "/env/" 部分
            } else if (path.startsWith("/")) {
                return path.substring(1); // 移除开头的 "/"
            }
            
            return url;
        } catch (Exception e) {
            // 发生异常时记录日志并返回原始URL
            log.warn("移除URL前缀失败: {}", e.getMessage());
            return url;
        }
    }

    /**
     * 获取完整的文件URL
     * 如果URL已经是完整URL（以http://或https://开头），则直接返回
     * 否则，拼接baseUrl和env前缀
     *
     * @param fileUrl 文件URL（可能是相对路径）
     * @return 完整的文件URL
     */
    private static String getCompleteFileUrl(String fileUrl) {
        if (StringUtils.isEmpty(fileUrl)) {
            return fileUrl;
        }
        
        // 如果已经是完整URL，直接返回
        if (fileUrl.startsWith("http://") || fileUrl.startsWith("https://")) {
            return fileUrl;
        }
        
        try {
            // 获取OssConfig中的配置
            OssConfig ossConfig = SpringContextUtil.getBean(OssConfig.class);
            String baseUrl = ossConfig.getUpload().getBaseUrl();
            String env = ossConfig.getUpload().getEnv();
            
            if (StringUtils.isEmpty(baseUrl)) {
                return fileUrl;
            }
            
            // 构建完整URL
            StringBuilder fullUrl = new StringBuilder(baseUrl);
            
            // 处理baseUrl末尾的斜杠
            if (!baseUrl.endsWith("/")) {
                fullUrl.append("/");
            }
            
            // 添加环境目录
            if (StringUtils.isNotEmpty(env)) {
                fullUrl.append(env);
                if (!env.endsWith("/")) {
                    fullUrl.append("/");
                }
            }
            
            // 处理fileUrl开头的斜杠，避免重复
            if (fileUrl.startsWith("/")) {
                fileUrl = fileUrl.substring(1);
            }
            
            // 拼接文件路径
            fullUrl.append(fileUrl);
            
            return fullUrl.toString();
        } catch (Exception e) {
            log.warn("构建完整文件URL失败: {}", e.getMessage());
            return fileUrl;
        }
    }
}
