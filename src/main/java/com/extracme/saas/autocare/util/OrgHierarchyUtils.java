package com.extracme.saas.autocare.util;

import com.extracme.saas.autocare.repository.TableOrgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 组织层级遍历工具类
 * 提供组织层级结构的遍历和权限计算功能
 */
@Slf4j
@Component
public class OrgHierarchyUtils {

    @Autowired
    private TableOrgInfoService orgInfoService;

    /**
     * 根据用户直接关联的组织ID列表，计算用户可访问的所有组织ID列表
     * 包括直接关联的组织及其所有子组织
     * 使用优化的批量查询模式，避免N+1查询问题
     *
     * @param directOrgIds 用户直接关联的组织ID列表
     * @return 用户可访问的所有组织ID列表（扁平化）
     */
    public List<String> calculateAllAccessibleOrgIds(List<String> directOrgIds) {
        if (directOrgIds == null || directOrgIds.isEmpty()) {
            log.debug("用户没有直接关联的组织，返回空列表");
            return Collections.emptyList();
        }

        try {
            log.debug("开始计算用户可访问的组织列表，直接关联组织数量: {}", directOrgIds.size());

            // 使用优化的批量查询方法，一次性查询所有组织数据并在内存中构建层级关系
            List<String> allAccessibleOrgIds = orgInfoService.findAllDescendantOrgIdsBatch(directOrgIds);

            log.debug("计算完成，用户可访问的组织总数: {}，包含组织: {}",
                     allAccessibleOrgIds.size(), allAccessibleOrgIds);

            return allAccessibleOrgIds;
        } catch (Exception e) {
            log.error("计算用户可访问组织列表时发生错误，直接关联组织: {}", directOrgIds, e);
            // 发生错误时返回直接关联的组织列表，确保基本功能可用
            return new ArrayList<>(directOrgIds);
        }
    }

    /**
     * 验证组织层级结构的完整性
     * 检查是否存在循环引用或孤立节点
     * 使用优化的批量查询模式
     *
     * @param orgIds 要验证的组织ID列表
     * @return 验证结果，true表示结构正常
     */
    public boolean validateOrgHierarchy(List<String> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return true;
        }

        try {
            // 尝试计算层级结构，如果存在循环引用会被检测到
            List<String> result = orgInfoService.findAllDescendantOrgIdsBatch(orgIds);
            log.debug("组织层级结构验证通过，输入组织数: {}, 计算结果数: {}",
                     orgIds.size(), result.size());
            return true;
        } catch (Exception e) {
            log.warn("组织层级结构验证失败，可能存在循环引用或数据异常: {}", orgIds, e);
            return false;
        }
    }

    /**
     * 获取组织层级统计信息
     * 用于调试和监控
     * 使用优化的批量查询模式
     *
     * @param directOrgIds 直接关联的组织ID列表
     * @return 层级统计信息
     */
    public OrgHierarchyStats getHierarchyStats(List<String> directOrgIds) {
        if (directOrgIds == null || directOrgIds.isEmpty()) {
            return new OrgHierarchyStats(0, 0, 0);
        }

        try {
            List<String> allAccessibleOrgIds = orgInfoService.findAllDescendantOrgIdsBatch(directOrgIds);
            int directCount = directOrgIds.size();
            int totalCount = allAccessibleOrgIds.size();
            int childCount = totalCount - directCount;

            return new OrgHierarchyStats(directCount, childCount, totalCount);
        } catch (Exception e) {
            log.error("获取组织层级统计信息失败", e);
            return new OrgHierarchyStats(directOrgIds.size(), 0, directOrgIds.size());
        }
    }

    /**
     * 组织层级统计信息
     */
    public static class OrgHierarchyStats {
        private final int directOrgCount;      // 直接关联组织数量
        private final int childOrgCount;       // 子组织数量
        private final int totalAccessibleCount; // 总可访问组织数量

        public OrgHierarchyStats(int directOrgCount, int childOrgCount, int totalAccessibleCount) {
            this.directOrgCount = directOrgCount;
            this.childOrgCount = childOrgCount;
            this.totalAccessibleCount = totalAccessibleCount;
        }

        public int getDirectOrgCount() {
            return directOrgCount;
        }

        public int getChildOrgCount() {
            return childOrgCount;
        }

        public int getTotalAccessibleCount() {
            return totalAccessibleCount;
        }

        @Override
        public String toString() {
            return String.format("OrgHierarchyStats{直接关联=%d, 子组织=%d, 总可访问=%d}", 
                               directOrgCount, childOrgCount, totalAccessibleCount);
        }
    }
}
