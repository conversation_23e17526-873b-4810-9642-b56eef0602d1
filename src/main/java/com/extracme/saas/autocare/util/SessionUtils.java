package com.extracme.saas.autocare.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;

/**
 * Session工具类
 */
@Component
public class SessionUtils implements ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(SessionUtils.class);

    private static final String SESSION_USER_KEY = "LOGIN_USER";
    private static final String SESSION_PERMISSIONS_KEY = "USER_PERMISSIONS";
    private static final String SESSION_TOKEN_KEY = "USER_TOKEN";
    private static final String SESSION_TENANT_CODE_KEY = "TENANT_CODE";

    private static ApplicationContext applicationContext;
    private static JwtUtil jwtUtil;
    private static TableUserService tableUserService;
    private static TablePermissionService tablePermissionService;
    private static TableUserOrgService tableUserOrgService;
    private static OrgHierarchyUtils orgHierarchyUtils;
    private static UserPermissionCacheUtils userPermissionCacheUtils;

    @Override
    public void setApplicationContext(@NonNull ApplicationContext context) throws BeansException {
        applicationContext = context;
        jwtUtil = applicationContext.getBean(JwtUtil.class);
        tableUserService = applicationContext.getBean(TableUserService.class);
        tablePermissionService = applicationContext.getBean(TablePermissionService.class);
        tableUserOrgService = applicationContext.getBean(TableUserOrgService.class);
        orgHierarchyUtils = applicationContext.getBean(OrgHierarchyUtils.class);
        userPermissionCacheUtils = applicationContext.getBean(UserPermissionCacheUtils.class);
    }

    /**
     * 获取当前请求的HttpSession
     */
    public static HttpSession getSession() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.error("当前线程没有绑定Request上下文，线程ID: {}, 线程名称: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName());
            throw new IllegalStateException("当前线程没有绑定Request上下文");
        }
        HttpServletRequest request = attributes.getRequest();
        HttpSession session = request.getSession();

        log.debug("获取Session，ID: {}, 创建时间: {}, 最后访问时间: {}, 最大不活动间隔: {}秒, 请求URI: {}, 线程ID: {}",
                session.getId(),
                formatDate(new Date(session.getCreationTime())),
                formatDate(new Date(session.getLastAccessedTime())),
                session.getMaxInactiveInterval(),
                request.getRequestURI(),
                Thread.currentThread().getId());

        return session;
    }

    /**
     * 格式化日期为可读字符串
     */
    private static String formatDate(Date date) {
        if (date == null) return "null";
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).toString();
    }

    /**
     * 获取当前登录用户
     * 优先从JWT token中解析完整用户信息，如果token中没有完整信息则回退到数据库查询（向后兼容）
     * 如果没有JWT token则尝试从session中获取用户信息
     * 返回构建好的LoginUser对象
     */
    public static LoginUser getLoginUser() {
        try {
            // 1. 尝试从请求头中获取JWT token
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                log.error("当前线程没有绑定Request上下文，线程ID: {}, 线程名称: {}",
                        Thread.currentThread().getId(), Thread.currentThread().getName());
                return null;
            }

            HttpServletRequest request = attributes.getRequest();
            String token = request.getHeader("Authorization");

            // 如果token以Bearer开头，去掉前缀
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            // 2. 如果没有找到token或token为空，尝试从session中获取
            if (token == null || token.isEmpty()) {
                log.debug("请求头中未找到有效的JWT token，尝试从session中获取用户信息");
                return getLoginUserFromSession();
            }

            // 3. 验证token有效性
            if (!jwtUtil.validateToken(token)) {
                log.warn("JWT token无效或已过期，token: {}", token);
                return null;
            }

            // 4. 尝试从token中获取完整用户信息（新版本token）
            if (jwtUtil.hasUserInfo(token)) {
                return getLoginUserFromJwtToken(token);
            }

            // 5. 回退到数据库查询方式（旧版本token，向后兼容）
            log.debug("JWT token中不包含完整用户信息，回退到数据库查询方式");
            return getLoginUserFromDatabase(token);

        } catch (IllegalStateException e) {
            log.error("获取登录用户异常 - 当前线程没有绑定Request上下文, 线程ID: {}, 线程名称: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(), e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("获取登录用户异常 - 线程ID: {}, 线程名称: {}, 异常类型: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(),
                    e.getClass().getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从JWT token中解析用户信息并从Redis获取权限信息（优化版本）
     *
     * @param token JWT token
     * @return LoginUser对象
     */
    private static LoginUser getLoginUserFromJwtToken(String token) {
        try {
            log.debug("从JWT token中解析用户基础信息，从Redis获取权限信息");

            // 从token中获取基础用户信息
            JwtUserInfoDTO userInfo = jwtUtil.getUserInfoFromToken(token);
            if (userInfo == null) {
                log.warn("无法从JWT token中解析用户信息");
                return null;
            }

            // 构建SysUser对象
            SysUser user = new SysUser();
            user.setId(userInfo.getUserId());
            user.setUsername(userInfo.getUsername());
            user.setNickname(userInfo.getNickname());
            // JWT中存储的是完整手机号
            user.setMobile(userInfo.getMobile());
            // 邮箱字段已从JWT中移除
            user.setEmail(null);
            user.setAccountType(userInfo.getAccountType());
            user.setApprovalLevel(userInfo.getApprovalLevel());
            user.setStatus(userInfo.getStatus());
            // 设置关联修理厂ID
            user.setRepairDepotId(userInfo.getRepairDepotId());
            // 设置保险公司ID
            user.setInsuranceCompanyId(userInfo.getInsuranceCompanyId());
            user.setTenantId(userInfo.getTenantId());
            // 创建时间和更新时间已从JWT中移除，设置为null
            user.setCreatedTime(null);
            user.setUpdatedTime(null);

            // 从Redis获取权限信息
            Set<String> permissions = Collections.emptySet();
            List<String> orgIds = Collections.emptyList();
            List<String> allAccessibleOrgIds = Collections.emptyList();

            try {
                if (userPermissionCacheUtils != null && userPermissionCacheUtils.isRedisAvailable()) {
                    UserPermissionCacheUtils.UserPermissionCache permissionCache =
                        userPermissionCacheUtils.getUserPermissions(userInfo.getTenantId(), userInfo.getUserId());

                    if (permissionCache != null) {
                        permissions = permissionCache.getPermissions() != null ?
                                     permissionCache.getPermissions() : Collections.emptySet();
                        orgIds = permissionCache.getOrgIds() != null ?
                                permissionCache.getOrgIds() : Collections.emptyList();
                        allAccessibleOrgIds = permissionCache.getAllAccessibleOrgIds() != null ?
                                             permissionCache.getAllAccessibleOrgIds() : Collections.emptyList();

                        log.debug("从Redis缓存获取权限信息成功，用户ID: {}, 权限数量: {}, 组织数量: {}, 可访问组织数量: {}",
                                 userInfo.getUserId(), permissions.size(), orgIds.size(), allAccessibleOrgIds.size());
                    } else {
                        log.debug("Redis缓存中未找到权限信息，将使用数据库降级查询，用户ID: {}", userInfo.getUserId());
                        // 降级处理：从数据库查询权限信息
                        permissions = queryPermissionsFromDatabase(user);
                        orgIds = queryOrgIdsFromDatabase(user.getId());
                        allAccessibleOrgIds = calculateAllAccessibleOrgIds(orgIds, user.getTenantId());
                    }
                } else {
                    log.warn("Redis不可用，使用数据库降级查询权限信息，用户ID: {}", userInfo.getUserId());
                    // 降级处理：从数据库查询权限信息
                    permissions = queryPermissionsFromDatabase(user);
                    orgIds = queryOrgIdsFromDatabase(user.getId());
                    allAccessibleOrgIds = calculateAllAccessibleOrgIds(orgIds, user.getTenantId());
                }
            } catch (Exception e) {
                log.error("获取权限信息失败，使用数据库降级查询，用户ID: {}", userInfo.getUserId(), e);
                // 降级处理：从数据库查询权限信息
                permissions = queryPermissionsFromDatabase(user);
                orgIds = queryOrgIdsFromDatabase(user.getId());
                allAccessibleOrgIds = calculateAllAccessibleOrgIds(orgIds, user.getTenantId());
            }

            // 构建LoginUser对象
            LoginUser loginUser = new LoginUser();
            loginUser.setUser(user);
            loginUser.setPermissions(permissions);
            loginUser.setOrgIds(orgIds);
            loginUser.setAllAccessibleOrgIds(allAccessibleOrgIds);
            loginUser.setToken(token);
            loginUser.setTenantId(userInfo.getTenantId());
            loginUser.setTenantCode(userInfo.getTenantCode());
            loginUser.setTenantName(userInfo.getTenantName());
            loginUser.setIpaddr(userInfo.getIpaddr());

            // 设置时间信息（JWT中存储的已经是时间戳）
            if (userInfo.getLoginTime() != null) {
                loginUser.setLoginTime(userInfo.getLoginTime());
            }
            if (userInfo.getExpireTime() != null) {
                loginUser.setExpireTime(userInfo.getExpireTime());
            }

            log.debug("用户信息构建成功，用户ID: {}, 权限数量: {}, 组织数量: {}, 可访问组织数量: {}",
                     userInfo.getUserId(), permissions.size(), orgIds.size(), allAccessibleOrgIds.size());

            return loginUser;
        } catch (Exception e) {
            log.error("从JWT token中解析用户信息失败", e);
            return null;
        }
    }

    /**
     * 从数据库查询用户信息（旧版本token，向后兼容）
     *
     * @param token JWT token
     * @return LoginUser对象
     */
    private static LoginUser getLoginUserFromDatabase(String token) {
        try {
            log.debug("使用数据库查询方式获取用户信息（向后兼容）");

            // 从token中获取用户ID
            String userIdStr = jwtUtil.getUserIdFromToken(token);
            if (userIdStr == null) {
                log.warn("无法从JWT token中获取用户ID，token: {}", token);
                return null;
            }

            Long userId = Long.parseLong(userIdStr);

            // 根据用户ID查询用户信息
            SysUser user = tableUserService.selectById(userId);
            if (user == null) {
                log.warn("根据用户ID未找到用户信息，userId: {}", userId);
                return null;
            }

            // 获取用户权限
            Set<String> permissions;
            if (user.getAccountType() != null && user.getAccountType() == 0) {
                // 超级管理员：自动获得系统中所有可用权限
                List<SysPermission> allPermissions = tablePermissionService.findAll();
                permissions = allPermissions.stream()
                    .map(SysPermission::getPermissionCode)
                    .collect(Collectors.toSet());
                log.debug("超级管理员用户 {} 自动获得所有权限，权限数量: {}", userId, permissions.size());
            } else {
                // 普通用户：通过角色关联查询权限
                List<SysPermission> permissionList = tablePermissionService.findByUserId(userId);
                permissions = permissionList.stream()
                    .map(SysPermission::getPermissionCode)
                    .collect(Collectors.toSet());
                log.debug("普通用户 {} 通过角色关联获得权限，权限数量: {}", userId, permissions.size());
            }

            // 查询并设置租户编码
            String tenantCode = "default";
            String tenantName = "";
            if (user.getTenantId() != null) {
                try {
                    if (applicationContext != null) {
                        TableTenantService tableTenantService = applicationContext.getBean(TableTenantService.class);
                        SysTenant tenant = tableTenantService.selectById(user.getTenantId());
                        if (tenant != null && tenant.getTenantCode() != null) {
                            tenantCode = tenant.getTenantCode();
                            tenantName = tenant.getTenantName();
                        }
                    }
                } catch (Exception e) {
                    log.warn("查询租户编码失败，租户ID: {}, 错误: {}", user.getTenantId(), e.getMessage());
                }
            }

            // 查询用户关联的机构列表
            List<String> orgIds = Collections.emptyList();
            try {
                if (tableUserOrgService != null) {
                    orgIds = tableUserOrgService.findOrgIdsByUserId(userId);
                }
            } catch (Exception e) {
                log.warn("查询用户机构关联失败，用户ID: {}, 错误: {}", userId, e.getMessage());
            }

            // 计算用户可访问的所有机构ID列表（包括子机构）
            List<String> allAccessibleOrgIds = Collections.emptyList();
            try {
                if (orgHierarchyUtils != null && !orgIds.isEmpty()) {
                    // 临时设置租户上下文，确保组织层级查询限制在当前租户范围内
                    Long currentTenantId = TenantContextHolder.getTenantId();
                    boolean needSetTenantContext = (currentTenantId == null || !currentTenantId.equals(user.getTenantId()));

                    if (needSetTenantContext) {
                        log.debug("临时设置租户上下文，租户ID: {}", user.getTenantId());
                        TenantContextHolder.setTenant(user.getTenantId());
                    }

                    try {
                        allAccessibleOrgIds = orgHierarchyUtils.calculateAllAccessibleOrgIds(orgIds);
                        log.debug("用户 {} 的组织权限计算完成，直接关联组织: {}, 可访问组织总数: {}",
                                 userId, orgIds.size(), allAccessibleOrgIds.size());
                    } finally {
                        // 如果是临时设置的租户上下文，需要恢复原状态
                        if (needSetTenantContext) {
                            if (currentTenantId != null) {
                                TenantContextHolder.setTenant(currentTenantId);
                                log.debug("恢复租户上下文，租户ID: {}", currentTenantId);
                            } else {
                                TenantContextHolder.clear();
                                log.debug("清理租户上下文");
                            }
                        }
                    }
                } else {
                    allAccessibleOrgIds = new ArrayList<>(orgIds);
                }
            } catch (Exception e) {
                log.warn("计算用户可访问组织列表失败，用户ID: {}, 直接关联组织: {}, 错误: {}",
                        userId, orgIds, e.getMessage());
                allAccessibleOrgIds = new ArrayList<>(orgIds);
            }

            // 构建LoginUser对象
            LoginUser loginUser = new LoginUser();
            loginUser.setUser(user);
            loginUser.setPermissions(permissions);
            loginUser.setOrgIds(orgIds);
            loginUser.setAllAccessibleOrgIds(allAccessibleOrgIds);
            loginUser.setToken(token);
            loginUser.setTenantId(user.getTenantId());
            loginUser.setTenantCode(tenantCode);
            loginUser.setTenantName(tenantName);
            loginUser.setLoginTime(System.currentTimeMillis());

            // 从JWT中获取过期时间并设置到LoginUser中
            Date expirationDate = jwtUtil.getExpirationDateFromToken(token);
            if (expirationDate != null) {
                loginUser.setExpireTime(expirationDate.getTime());
            }

            log.debug("数据库查询用户信息成功，用户ID: {}, 权限数量: {}, 组织数量: {}",
                     userId, permissions.size(), orgIds.size());

            return loginUser;
        } catch (Exception e) {
            log.error("数据库查询用户信息失败", e);
            return null;
        }
    }

    /**
     * 从Session中获取登录用户信息（兼容旧方式）
     */
    private static LoginUser getLoginUserFromSession() {
        try {
            HttpSession session = getSession();
            Object userObj = session.getAttribute(SESSION_USER_KEY);

            // 记录详细的会话信息
            log.info("从Session获取登录用户信息 - 会话ID: {}, 会话是否为新创建: {}, 会话创建时间: {}, 会话最后访问时间: {}, 会话最大不活动间隔: {}秒, 线程ID: {}, 线程名称: {}",
                    session.getId(),
                    session.isNew(),
                    formatDate(new Date(session.getCreationTime())),
                    formatDate(new Date(session.getLastAccessedTime())),
                    session.getMaxInactiveInterval(),
                    Thread.currentThread().getId(),
                    Thread.currentThread().getName());

            if (userObj == null) {
                log.warn("会话中不存在用户信息 - 会话ID: {}, 线程ID: {}",
                        session.getId(), Thread.currentThread().getId());
                return null;
            }

            if (!(userObj instanceof LoginUser)) {
                log.error("会话中的用户信息类型错误 - 会话ID: {}, 实际类型: {}, 线程ID: {}",
                        session.getId(), userObj.getClass().getName(), Thread.currentThread().getId());
                return null;
            }

            LoginUser loginUser = (LoginUser) userObj;

            // 检查用户信息是否完整
            if (loginUser.getUser() == null) {
                log.warn("登录用户的User对象为空 - 会话ID: {}, 线程ID: {}",
                        session.getId(), Thread.currentThread().getId());
            }

            // 检查Token是否过期
            if (loginUser.getExpireTime() != null) {
                long currentTime = System.currentTimeMillis();
                long expireTime = loginUser.getExpireTime();

                if (currentTime > expireTime) {
                    log.warn("用户Token已过期 - 会话ID: {}, 过期时间: {}, 当前时间: {}, 线程ID: {}",
                            session.getId(),
                            formatDate(new Date(expireTime)),
                            formatDate(new Date(currentTime)),
                            Thread.currentThread().getId());
                }
            }

            return loginUser;
        } catch (Exception e) {
            log.error("从Session获取登录用户异常 - 线程ID: {}, 线程名称: {}, 异常类型: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(),
                    e.getClass().getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取当前用户权限
     */
    @SuppressWarnings("unchecked")
    public static Set<String> getUserPermissions() {
        try {
            HttpSession session = getSession();
            Object permissionsObj = session.getAttribute(SESSION_PERMISSIONS_KEY);

            if (permissionsObj == null) {
                log.warn("会话中不存在权限信息 - 会话ID: {}, 线程ID: {}",
                        session.getId(), Thread.currentThread().getId());
                return null;
            }

            if (!(permissionsObj instanceof Set)) {
                log.error("会话中的权限信息类型错误 - 会话ID: {}, 实际类型: {}, 线程ID: {}",
                        session.getId(), permissionsObj.getClass().getName(), Thread.currentThread().getId());
                return null;
            }

            return (Set<String>) permissionsObj;
        } catch (Exception e) {
            log.error("获取用户权限异常 - 线程ID: {}, 线程名称: {}, 异常类型: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(),
                    e.getClass().getName(), e.getMessage());
            return null;
        }
    }

    /**
     * 获取当前用户Token
     */
    public static String getUserToken() {
        try {
            HttpSession session = getSession();
            Object tokenObj = session.getAttribute(SESSION_TOKEN_KEY);

            if (tokenObj == null) {
                log.warn("会话中不存在Token信息 - 会话ID: {}, 线程ID: {}",
                        session.getId(), Thread.currentThread().getId());
                return null;
            }

            if (!(tokenObj instanceof String)) {
                log.error("会话中的Token信息类型错误 - 会话ID: {}, 实际类型: {}, 线程ID: {}",
                        session.getId(), tokenObj.getClass().getName(), Thread.currentThread().getId());
                return null;
            }

            return (String) tokenObj;
        } catch (Exception e) {
            log.error("获取用户Token异常 - 线程ID: {}, 线程名称: {}, 异常类型: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(),
                    e.getClass().getName(), e.getMessage());
            return null;
        }
    }

    /**
     * 获取当前用户ID
     */
    public static Long getUserId() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null && loginUser.getUser() != null ? loginUser.getUser().getId() : null;
    }

    /**
     * 获取当前用户名
     */
    public static String getUsername() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null && loginUser.getUser() != null ? loginUser.getUser().getUsername() : null;
    }

    /**
     * 获取当前用户昵称
     */
    public static String getNickname() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null && loginUser.getUser() != null ? loginUser.getUser().getNickname() : null;
    }

    /**
     * 获取当前用户租户ID
     */
    public static Long getTenantId() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null ? loginUser.getTenantId() : null;
    }

    /**
     * 获取当前用户租户编码
     * 优先从LoginUser对象中获取，如果没有则从session中获取，最后返回默认值
     */
    public static String getTenantCode() {
        try {
            // 1. 优先从LoginUser对象中获取tenantCode
            LoginUser loginUser = getLoginUser();
            if (loginUser != null && loginUser.getTenantCode() != null) {
                return loginUser.getTenantCode();
            }

            // 2. 如果LoginUser中没有，尝试从session中获取
            HttpSession session = getSession();
            Object tenantCodeObj = session.getAttribute(SESSION_TENANT_CODE_KEY);

            if (tenantCodeObj == null) {
                log.warn("会话中不存在租户编码信息 - 会话ID: {}, 线程ID: {}",
                        session.getId(), Thread.currentThread().getId());
                return "default";
            }

            if (!(tenantCodeObj instanceof String)) {
                log.error("会话中的租户编码信息类型错误 - 会话ID: {}, 实际类型: {}, 线程ID: {}",
                        session.getId(), tenantCodeObj.getClass().getName(), Thread.currentThread().getId());
                return "default";
            }

            return (String) tenantCodeObj;
        } catch (Exception e) {
            log.error("获取租户编码异常 - 线程ID: {}, 线程名称: {}, 异常类型: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(),
                    e.getClass().getName(), e.getMessage());
            return "default";
        }
    }

    /**
     * 清除Session中的用户信息
     */
    public static void clearUserInfo() {
        HttpSession session = getSession();
        session.removeAttribute(SESSION_USER_KEY);
        session.removeAttribute(SESSION_PERMISSIONS_KEY);
        session.removeAttribute(SESSION_TOKEN_KEY);
        session.removeAttribute(SESSION_TENANT_CODE_KEY);
    }

    /**
     * 获取当前用户关联的机构ID列表
     */
    public static List<String> getUserOrgIds() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null && loginUser.getOrgIds() != null ?
               loginUser.getOrgIds() : Collections.emptyList();
    }

    /**
     * 获取当前用户可访问的所有机构ID列表（包括子机构）
     *
     * @return 扁平化的机构ID列表
     */
    public static List<String> getAllAccessibleOrgIds() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null ? loginUser.getAllAccessibleOrgIds() : Collections.emptyList();
    }

    /**
     * 获取当前用户直接关联的机构ID列表
     *
     * @return 用户直接关联的机构ID列表
     */
    public static List<String> getDirectOrgIds() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null ? loginUser.getDirectOrgIds() : Collections.emptyList();
    }

    /**
     * 获取当前用户的主要机构ID（向后兼容）
     *
     * @deprecated 建议使用 getAllAccessibleOrgIds() 或 getDirectOrgIds() 方法
     */
    @Deprecated
    public static String getPrimaryOrgId() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null ? loginUser.getPrimaryOrgId() : null;
    }

    /**
     * 检查当前用户是否有指定机构的访问权限
     */
    public static boolean hasOrgAccess(String orgId) {
        LoginUser loginUser = getLoginUser();
        return loginUser != null && loginUser.hasOrgAccess(orgId);
    }

    /**
     * 判断当前登录用户是否为超级管理员
     * 超级管理员的判断条件：当前登录用户不为空 && 用户实体不为空 && accountType 不为空 && accountType == 0
     *
     * @return true-超级管理员，false-普通用户或未登录
     */
    public static boolean isSuperAdmin() {
        LoginUser currentLoginUser = getLoginUser();
        return currentLoginUser != null &&
               currentLoginUser.getUser() != null &&
               currentLoginUser.getUser().getAccountType() != null &&
               currentLoginUser.getUser().getAccountType() == 0;
    }

    /**
     * 从数据库查询用户权限信息（降级处理）
     *
     * @param user 用户对象
     * @return 权限集合
     */
    private static Set<String> queryPermissionsFromDatabase(SysUser user) {
        try {
            if (user.getAccountType() != null && user.getAccountType() == 0) {
                // 超级管理员：自动获得系统中所有可用权限
                List<SysPermission> allPermissions = tablePermissionService.findAll();
                Set<String> permissions = allPermissions.stream()
                    .map(SysPermission::getPermissionCode)
                    .collect(Collectors.toSet());
                log.debug("超级管理员用户 {} 自动获得所有权限，权限数量: {}", user.getId(), permissions.size());
                return permissions;
            } else {
                // 普通用户：通过角色关联查询权限
                List<SysPermission> permissionList = tablePermissionService.findByUserId(user.getId());
                Set<String> permissions = permissionList.stream()
                    .map(SysPermission::getPermissionCode)
                    .collect(Collectors.toSet());
                log.debug("普通用户 {} 通过角色关联获得权限，权限数量: {}", user.getId(), permissions.size());
                return permissions;
            }
        } catch (Exception e) {
            log.error("从数据库查询用户权限失败，用户ID: {}", user.getId(), e);
            return Collections.emptySet();
        }
    }

    /**
     * 从数据库查询用户组织关联信息（降级处理）
     *
     * @param userId 用户ID
     * @return 组织ID列表
     */
    private static List<String> queryOrgIdsFromDatabase(Long userId) {
        try {
            if (tableUserOrgService != null) {
                return tableUserOrgService.findOrgIdsByUserId(userId);
            }
        } catch (Exception e) {
            log.error("从数据库查询用户组织关联失败，用户ID: {}", userId, e);
        }
        return Collections.emptyList();
    }

    /**
     * 计算用户可访问的所有组织ID列表（降级处理）
     *
     * @param orgIds 直接关联的组织ID列表
     * @param tenantId 租户ID
     * @return 可访问的所有组织ID列表
     */
    private static List<String> calculateAllAccessibleOrgIds(List<String> orgIds, Long tenantId) {
        try {
            if (orgHierarchyUtils != null && !orgIds.isEmpty()) {
                // 临时设置租户上下文，确保组织层级查询限制在当前租户范围内
                Long currentTenantId = TenantContextHolder.getTenantId();
                boolean needSetTenantContext = (currentTenantId == null || !currentTenantId.equals(tenantId));

                if (needSetTenantContext) {
                    log.debug("临时设置租户上下文，租户ID: {}", tenantId);
                    TenantContextHolder.setTenant(tenantId);
                }

                try {
                    List<String> allAccessibleOrgIds = orgHierarchyUtils.calculateAllAccessibleOrgIds(orgIds);
                    log.debug("组织权限计算完成，直接关联组织: {}, 可访问组织总数: {}",
                             orgIds.size(), allAccessibleOrgIds.size());
                    return allAccessibleOrgIds;
                } finally {
                    // 如果是临时设置的租户上下文，需要恢复原状态
                    if (needSetTenantContext) {
                        if (currentTenantId != null) {
                            TenantContextHolder.setTenant(currentTenantId);
                            log.debug("恢复租户上下文，租户ID: {}", currentTenantId);
                        } else {
                            TenantContextHolder.clear();
                            log.debug("清理租户上下文");
                        }
                    }
                }
            } else {
                return new ArrayList<>(orgIds);
            }
        } catch (Exception e) {
            log.error("计算用户可访问组织列表失败，直接关联组织: {}", orgIds, e);
            return new ArrayList<>(orgIds);
        }
    }
}