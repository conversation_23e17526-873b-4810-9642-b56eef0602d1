package com.extracme.saas.autocare.util;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;

import lombok.extern.slf4j.Slf4j;

/**
 * Excel导入工具类 - EasyExcel增强版
 * 提供通用的Excel数据导入功能，支持泛型配置和两阶段处理
 * 第一阶段：数据校验，第二阶段：批量保存
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
public class ExcelImportUtil {

    /**
     * 数据验证器接口
     * 用于验证Excel行数据
     */
    @FunctionalInterface
    public interface DataValidator<T> {
        /**
         * 验证数据
         * @param data 数据对象
         * @param rowIndex 行索引（从1开始，不包含表头）
         * @throws Exception 验证异常
         */
        void validate(T data, int rowIndex) throws Exception;
    }

    /**
     * 数据保存器接口
     * 用于批量保存验证通过的数据
     */
    @FunctionalInterface
    public interface DataSaver<T> {
        /**
         * 批量保存数据
         * @param dataList 数据列表
         * @throws Exception 保存异常
         */
        void saveAll(List<T> dataList) throws Exception;
    }

    /**
     * 进度回调接口
     * 用于报告处理进度
     */
    @FunctionalInterface
    public interface ProgressCallback {
        /**
         * 进度回调
         * @param current 当前处理行数
         * @param total 总行数
         * @param phase 处理阶段：VALIDATION-校验阶段，SAVING-保存阶段
         */
        void onProgress(int current, int total, String phase);
    }

    /**
     * 导入配置类 - EasyExcel版本
     */
    public static class ImportConfig<T> {
        private Class<T> dataClass;
        private DataValidator<T> dataValidator;
        private DataSaver<T> dataSaver;
        private ProgressCallback progressCallback;
        private int maxRows = 10000;
        private boolean skipEmptyRows = true;
        private Function<Integer, String> errorMessageBuilder;

        public ImportConfig(Class<T> dataClass) {
            this.dataClass = dataClass;
            this.errorMessageBuilder = rowIndex ->
                String.format("第%d行数据处理失败", rowIndex);
        }

        // Getters and Setters
        public Class<T> getDataClass() { return dataClass; }
        public DataValidator<T> getDataValidator() { return dataValidator; }
        public DataSaver<T> getDataSaver() { return dataSaver; }
        public ProgressCallback getProgressCallback() { return progressCallback; }
        public int getMaxRows() { return maxRows; }
        public boolean isSkipEmptyRows() { return skipEmptyRows; }
        public Function<Integer, String> getErrorMessageBuilder() { return errorMessageBuilder; }

        public ImportConfig<T> setDataValidator(DataValidator<T> dataValidator) {
            this.dataValidator = dataValidator;
            return this;
        }

        public ImportConfig<T> setDataSaver(DataSaver<T> dataSaver) {
            this.dataSaver = dataSaver;
            return this;
        }

        public ImportConfig<T> setProgressCallback(ProgressCallback progressCallback) {
            this.progressCallback = progressCallback;
            return this;
        }

        public ImportConfig<T> setMaxRows(int maxRows) {
            this.maxRows = maxRows;
            return this;
        }

        public ImportConfig<T> setSkipEmptyRows(boolean skipEmptyRows) {
            this.skipEmptyRows = skipEmptyRows;
            return this;
        }

        public ImportConfig<T> setErrorMessageBuilder(Function<Integer, String> errorMessageBuilder) {
            this.errorMessageBuilder = errorMessageBuilder;
            return this;
        }
    }

    /**
     * 导入结果类
     */
    public static class ImportResult<T> {
        private List<T> data;
        private int totalRows;
        private int successRows;
        private int failedRows;
        private List<String> errorMessages;

        public ImportResult() {
            this.data = new ArrayList<>();
            this.errorMessages = new ArrayList<>();
        }

        // Getters and Setters
        public List<T> getData() { return data; }
        public int getTotalRows() { return totalRows; }
        public int getSuccessRows() { return successRows; }
        public int getFailedRows() { return failedRows; }
        public List<String> getErrorMessages() { return errorMessages; }

        public void setTotalRows(int totalRows) { this.totalRows = totalRows; }
        public void setSuccessRows(int successRows) { this.successRows = successRows; }
        public void setFailedRows(int failedRows) { this.failedRows = failedRows; }

        public void addData(T item) { this.data.add(item); }
        public void addErrorMessage(String message) { this.errorMessages.add(message); }

        public boolean hasErrors() { return !errorMessages.isEmpty(); }
        public boolean isSuccess() { return failedRows == 0; }
    }

    /**
     * 两阶段导入Excel数据
     * 第一阶段：数据校验，第二阶段：批量保存
     *
     * @param inputStream Excel文件输入流
     * @param config 导入配置
     * @param <T> 数据类型
     * @return 导入结果
     */
    public static <T> ImportResult<T> importFromStream(InputStream inputStream, ImportConfig<T> config) {
        log.info("开始两阶段导入Excel数据，数据类型: {}", config.getDataClass().getSimpleName());

        ImportResult<T> result = new ImportResult<>();

        try {
            // 第一阶段：数据校验
            List<T> validatedData = validatePhase(inputStream, config, result);

            if (result.getTotalRows() == 0){
                result.addErrorMessage("导入内容不能为空");
            }
            // 如果校验阶段有错误，直接返回
            if (result.hasErrors()) {
                log.warn("数据校验阶段发现错误，终止导入流程。错误数量: {}", result.getErrorMessages().size());
                return result;
            }

            // 第二阶段：批量保存
            savePhase(validatedData, config, result);

            log.info("两阶段导入完成，总计: {}, 成功: {}, 失败: {}",
                result.getTotalRows(), result.getSuccessRows(), result.getFailedRows());

        } catch (Exception e) {
            log.error("Excel导入失败: {}", e.getMessage(), e);
            result.addErrorMessage("Excel导入失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 第一阶段：数据校验
     */
    private static <T> List<T> validatePhase(InputStream inputStream, ImportConfig<T> config, ImportResult<T> result) {
        log.info("开始第一阶段：数据校验");

        List<T> validatedData = new ArrayList<>();
        ValidationListener<T> listener = new ValidationListener<>(config, validatedData, result);

        try {
            EasyExcel.read(inputStream, config.getDataClass(), listener)
                    .sheet()
                    .doRead();

            result.setTotalRows(listener.getTotalRows());
            result.setSuccessRows(listener.getSuccessRows());
            result.setFailedRows(listener.getFailedRows());

            log.info("数据校验阶段完成，总计: {}, 成功: {}, 失败: {}",
                listener.getTotalRows(), listener.getSuccessRows(), listener.getFailedRows());

        } catch (Exception e) {
            log.error("数据校验阶段失败: {}", e.getMessage(), e);
            result.addErrorMessage("数据校验失败: " + e.getMessage());
        }

        return validatedData;
    }

    /**
     * 第二阶段：批量保存
     */
    private static <T> void savePhase(List<T> validatedData, ImportConfig<T> config, ImportResult<T> result) {
        log.info("开始第二阶段：批量保存，数据量: {}", validatedData.size());

        if (validatedData.isEmpty()) {
            log.warn("没有有效数据需要保存");
            return;
        }

        if (config.getDataSaver() == null) {
            log.warn("未配置数据保存器，跳过保存阶段");
            return;
        }

        try {
            // 进度回调
            if (config.getProgressCallback() != null) {
                config.getProgressCallback().onProgress(0, validatedData.size(), "SAVING");
            }

            // 批量保存
            config.getDataSaver().saveAll(validatedData);

            // 进度回调
            if (config.getProgressCallback() != null) {
                config.getProgressCallback().onProgress(validatedData.size(), validatedData.size(), "SAVING");
            }

            log.info("批量保存完成，保存数据量: {}", validatedData.size());

        } catch (Exception e) {
            log.error("批量保存失败: {}", e.getMessage(), e);
            result.addErrorMessage(e.getMessage());
            // 保存失败时，将成功行数设为0
            result.setSuccessRows(0);
            result.setFailedRows(validatedData.size());
        }
    }

    /**
     * 数据校验监听器
     * 用于EasyExcel读取时进行数据校验
     */
    private static class ValidationListener<T> implements ReadListener<T> {
        private final ImportConfig<T> config;
        private final List<T> validatedData;
        private final ImportResult<T> result;
        private int totalRows = 0;
        private int successRows = 0;
        private int failedRows = 0;

        public ValidationListener(ImportConfig<T> config, List<T> validatedData, ImportResult<T> result) {
            this.config = config;
            this.validatedData = validatedData;
            this.result = result;
        }

        @Override
        public void invoke(T data, AnalysisContext context) {
            totalRows++;
            int currentRow = context.readRowHolder().getRowIndex() + 1; // EasyExcel行号从0开始，转换为从1开始

            try {
                // 进度回调
                if (config.getProgressCallback() != null) {
                    config.getProgressCallback().onProgress(totalRows, -1, "VALIDATION");
                }

                // 数据验证
                if (config.getDataValidator() != null) {
                    config.getDataValidator().validate(data, currentRow);
                }

                // 验证通过，添加到有效数据列表
                validatedData.add(data);
                successRows++;

                log.debug("第{}行数据校验通过", currentRow);

            } catch (Exception e) {
                failedRows++;
                String errorMessage = config.getErrorMessageBuilder().apply(currentRow) + ": " + e.getMessage();
                result.addErrorMessage(errorMessage);
                log.error("第{}行数据校验失败: {}", currentRow, e.getMessage());
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            log.info("数据校验监听器处理完成，总计: {}, 成功: {}, 失败: {}", totalRows, successRows, failedRows);
        }

        public int getTotalRows() { return totalRows; }
        public int getSuccessRows() { return successRows; }
        public int getFailedRows() { return failedRows; }
    }

    /**
     * 构建器模式创建导入配置 - EasyExcel版本
     */
    public static class ImportConfigBuilder<T> {
        private Class<T> dataClass;
        private DataValidator<T> dataValidator;
        private DataSaver<T> dataSaver;
        private ProgressCallback progressCallback;
        private int maxRows = 10000;
        private boolean skipEmptyRows = true;
        private Function<Integer, String> errorMessageBuilder;

        public ImportConfigBuilder(Class<T> dataClass) {
            this.dataClass = dataClass;
        }

        public ImportConfigBuilder<T> dataValidator(DataValidator<T> dataValidator) {
            this.dataValidator = dataValidator;
            return this;
        }

        public ImportConfigBuilder<T> dataSaver(DataSaver<T> dataSaver) {
            this.dataSaver = dataSaver;
            return this;
        }

        public ImportConfigBuilder<T> progressCallback(ProgressCallback progressCallback) {
            this.progressCallback = progressCallback;
            return this;
        }

        public ImportConfigBuilder<T> maxRows(int maxRows) {
            this.maxRows = maxRows;
            return this;
        }

        public ImportConfigBuilder<T> skipEmptyRows(boolean skipEmptyRows) {
            this.skipEmptyRows = skipEmptyRows;
            return this;
        }

        public ImportConfigBuilder<T> errorMessageBuilder(Function<Integer, String> errorMessageBuilder) {
            this.errorMessageBuilder = errorMessageBuilder;
            return this;
        }

        public ImportConfig<T> build() {
            if (dataClass == null) {
                throw new IllegalArgumentException("dataClass 不能为空");
            }

            ImportConfig<T> config = new ImportConfig<>(dataClass);
            config.setDataValidator(dataValidator)
                  .setDataSaver(dataSaver)
                  .setProgressCallback(progressCallback)
                  .setMaxRows(maxRows)
                  .setSkipEmptyRows(skipEmptyRows);

            if (errorMessageBuilder != null) {
                config.setErrorMessageBuilder(errorMessageBuilder);
            }

            return config;
        }
    }

    /**
     * 创建构建器
     */
    public static <T> ImportConfigBuilder<T> builder(Class<T> dataClass) {
        return new ImportConfigBuilder<>(dataClass);
    }
}
