package com.extracme.saas.autocare.util;

import com.extracme.saas.autocare.model.security.LoginUser;
import lombok.extern.slf4j.Slf4j;

/**
 * 操作人工具类
 * 提供统一的操作人获取逻辑
 */
@Slf4j
public class OperatorUtils {

    /**
     * 系统操作的默认操作人
     */
    public static final String SYSTEM_OPERATOR = "system";

    /**
     * 获取当前操作人
     * 在数据同步等场景下，可能没有正常的用户会话，需要提供默认值
     * 
     * @return 操作人用户名，如果无法获取则返回系统默认操作人
     */
    public static String getCurrentOperator() {
        try {
            LoginUser loginUser = SessionUtils.getLoginUser();
            if (loginUser != null && loginUser.getUser() != null) {
                String username = loginUser.getUser().getUsername();
                if (username != null && !username.trim().isEmpty()) {
                    return username;
                }
            }
        } catch (Exception e) {
            // 在数据同步等场景下，可能没有正常的用户会话，记录警告但不抛出异常
            log.warn("获取当前操作人失败，使用默认值 '{}'，错误：{}", SYSTEM_OPERATOR, e.getMessage());
        }
        
        log.debug("未找到有效的用户会话，使用系统默认操作人：{}", SYSTEM_OPERATOR);
        return SYSTEM_OPERATOR; // 系统操作的默认操作人
    }

    /**
     * 获取当前操作人，如果获取失败则使用指定的默认操作人
     * 
     * @param defaultOperator 默认操作人
     * @return 操作人用户名
     */
    public static String getCurrentOperator(String defaultOperator) {
        try {
            LoginUser loginUser = SessionUtils.getLoginUser();
            if (loginUser != null && loginUser.getUser() != null) {
                String username = loginUser.getUser().getUsername();
                if (username != null && !username.trim().isEmpty()) {
                    return username;
                }
            }
        } catch (Exception e) {
            log.warn("获取当前操作人失败，使用指定默认值 '{}'，错误：{}", defaultOperator, e.getMessage());
        }
        
        log.debug("未找到有效的用户会话，使用指定默认操作人：{}", defaultOperator);
        return defaultOperator != null ? defaultOperator : SYSTEM_OPERATOR;
    }

    /**
     * 检查当前是否有有效的用户会话
     * 
     * @return true 如果有有效的用户会话，false 否则
     */
    public static boolean hasValidUserSession() {
        try {
            LoginUser loginUser = SessionUtils.getLoginUser();
            return loginUser != null && loginUser.getUser() != null 
                   && loginUser.getUser().getUsername() != null 
                   && !loginUser.getUser().getUsername().trim().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
}
