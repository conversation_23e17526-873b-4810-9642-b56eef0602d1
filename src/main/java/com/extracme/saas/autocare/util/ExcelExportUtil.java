package com.extracme.saas.autocare.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.WriteHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;


/**
 * Excel导出工具类 - EasyExcel增强版
 * 支持泛型数据导出、分批处理、大数据量导出优化
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
public class ExcelExportUtil {

    /**
     * 游标数据提供者接口
     * 支持基于ID的游标分页查询，避免深分页性能问题
     */
    @FunctionalInterface
    public interface CursorDataProvider<T> {
        /**
         * 获取数据列表（游标分页）
         * @param lastId 上一批次最后一条记录的ID（首次查询传入0）
         * @return 数据列表
         */
        List<T> getData(Long lastId);
    }

    /**
     * 进度回调接口
     */
    @FunctionalInterface
    public interface ProgressCallback {
        /**
         * 进度回调
         * @param current 当前处理数量
         * @param total 总数量（如果未知则为-1）
         */
        void onProgress(int current, int total);
    }

    /**
     * 导出配置类 - EasyExcel版本
     */
    public static class ExportConfig<T> {
        private Class<T> dataClass;
        private String fileName;
        private String sheetName;
        private CursorDataProvider<T> cursorDataProvider;
        private ProgressCallback progressCallback;
        private int batchSize = 1000;
        private List<WriteHandler> writeHandlers;

        public ExportConfig(Class<T> dataClass, String fileName, CursorDataProvider<T> cursorDataProvider) {
            this.dataClass = dataClass;
            this.fileName = fileName;
            this.cursorDataProvider = cursorDataProvider;
            this.sheetName = "Sheet1";
        }

        // Getters and Setters
        public Class<T> getDataClass() { return dataClass; }
        public String getFileName() { return fileName; }
        public String getSheetName() { return sheetName; }
        public CursorDataProvider<T> getCursorDataProvider() { return cursorDataProvider; }
        public ProgressCallback getProgressCallback() { return progressCallback; }
        public int getBatchSize() { return batchSize; }
        public List<WriteHandler> getWriteHandlers() { return writeHandlers; }

        public ExportConfig<T> setSheetName(String sheetName) {
            this.sheetName = sheetName;
            return this;
        }

        public ExportConfig<T> setBatchSize(int batchSize) {
            this.batchSize = batchSize;
            return this;
        }

        public ExportConfig<T> setProgressCallback(ProgressCallback progressCallback) {
            this.progressCallback = progressCallback;
            return this;
        }

        public ExportConfig<T> setWriteHandlers(List<WriteHandler> writeHandlers) {
            this.writeHandlers = writeHandlers;
            return this;
        }
    }

    /**
     * 分批导出Excel到HTTP响应
     * 支持大数据量导出，避免内存溢出
     *
     * @param config 导出配置
     * @param response HTTP响应对象
     * @param <T> 数据类型
     */
    public static <T> void exportToResponse(ExportConfig<T> config, HttpServletResponse response) {
        log.info("开始分批导出Excel文件: {}, 数据类型: {}", config.getFileName(), config.getDataClass().getSimpleName());

        try {
            // 设置响应头，支持中文文件名
            setResponseHeaders(response, config.getFileName());

            // 收集所有数据
            List<T> allData = collectAllData(config);

            // 创建EasyExcel写入器并写入数据
            com.alibaba.excel.write.builder.ExcelWriterBuilder writeBuilder =
                EasyExcel.write(response.getOutputStream(), config.getDataClass());

            // 添加自定义写入处理器
            if (!CollectionUtils.isEmpty(config.getWriteHandlers())) {
                for (WriteHandler handler : config.getWriteHandlers()) {
                    writeBuilder.registerWriteHandler(handler);
                }
            }

            // 写入数据
            writeBuilder.sheet(config.getSheetName()).doWrite(allData);

            log.info("Excel文件导出成功: {}, 总行数: {}", config.getFileName(), allData.size());

        } catch (IOException e) {
            log.error("Excel导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出失败：文件写入异常", e);
        } catch (Exception e) {
            log.error("Excel导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出失败：" + e.getMessage(), e);
        }
    }

    /**
     * 简单导出方法
     * 适用于小数据量的一次性导出
     *
     * @param dataList 数据列表
     * @param dataClass 数据类型
     * @param fileName 文件名
     * @param response HTTP响应对象
     * @param <T> 数据类型
     */
    public static <T> void simpleExport(List<T> dataList, Class<T> dataClass, String fileName, HttpServletResponse response) {
        log.info("开始简单导出Excel文件: {}, 数据量: {}", fileName, dataList.size());

        try {
            // 设置响应头
            setResponseHeaders(response, fileName);

            // 使用EasyExcel写入
            EasyExcel.write(response.getOutputStream(), dataClass)
                    .sheet("Sheet1")
                    .doWrite(dataList);

            log.info("简单导出完成: {}", fileName);

        } catch (IOException e) {
            log.error("简单导出失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出失败：文件写入异常", e);
        }
    }

    /**
     * 收集所有数据（游标分页）
     * 基于ID的游标分页，避免深分页性能问题
     *
     * @param config 导出配置
     * @return 所有数据列表
     */
    private static <T> List<T> collectAllData(ExportConfig<T> config) {
        List<T> allData = new java.util.ArrayList<>();
        Long lastId = 0L;
        int totalCount = 0;
        int batchCount = 0;

        log.info("开始使用游标分页收集数据，批次大小: {}", config.getBatchSize());

        while (true) {
            List<T> dataList = config.getCursorDataProvider().getData(lastId);

            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }

            batchCount++;
            log.debug("收集数据批次: {}, 起始ID: {}, 数据量: {}", batchCount, lastId, dataList.size());

            // 添加到总数据列表
            allData.addAll(dataList);
            totalCount += dataList.size();

            // 进度回调
            if (config.getProgressCallback() != null) {
                config.getProgressCallback().onProgress(totalCount, -1);
            }

            // 如果返回的数据量小于批次大小，说明已经是最后一批
            if (dataList.size() < config.getBatchSize()) {
                break;
            }

            // 更新游标位置：获取当前批次最后一条记录的ID
            // 假设数据对象有getId()方法，这里使用反射获取
            try {
                T lastRecord = dataList.get(dataList.size() - 1);
                java.lang.reflect.Method getIdMethod = lastRecord.getClass().getMethod("getId");
                lastId = (Long) getIdMethod.invoke(lastRecord);
                log.debug("更新游标位置，lastId: {}", lastId);
            } catch (Exception e) {
                log.error("获取记录ID失败，无法继续游标分页: {}", e.getMessage(), e);
                break;
            }
        }

        log.info("游标分页数据收集完成，总批次: {}, 总数据量: {}", batchCount, allData.size());
        return allData;
    }

    /**
     * 设置HTTP响应头
     * 支持中文文件名编码
     */
    private static void setResponseHeaders(HttpServletResponse response, String fileName) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            // 处理中文文件名编码
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);

            log.debug("设置响应头完成，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("设置响应头失败: {}", e.getMessage(), e);
            throw new RuntimeException("设置响应头失败", e);
        }
    }

    /**
     * 构建器模式创建导出配置 - EasyExcel版本
     */
    public static class ExportConfigBuilder<T> {
        private Class<T> dataClass;
        private String fileName;
        private String sheetName = "Sheet1";
        private CursorDataProvider<T> cursorDataProvider;
        private ProgressCallback progressCallback;
        private int batchSize = 1000;
        private List<WriteHandler> writeHandlers;

        public ExportConfigBuilder(Class<T> dataClass) {
            this.dataClass = dataClass;
        }

        public ExportConfigBuilder<T> fileName(String fileName) {
            this.fileName = fileName;
            return this;
        }

        public ExportConfigBuilder<T> sheetName(String sheetName) {
            this.sheetName = sheetName;
            return this;
        }

        public ExportConfigBuilder<T> cursorDataProvider(CursorDataProvider<T> cursorDataProvider) {
            this.cursorDataProvider = cursorDataProvider;
            return this;
        }

        public ExportConfigBuilder<T> progressCallback(ProgressCallback progressCallback) {
            this.progressCallback = progressCallback;
            return this;
        }

        public ExportConfigBuilder<T> batchSize(int batchSize) {
            this.batchSize = batchSize;
            return this;
        }

        public ExportConfigBuilder<T> writeHandlers(List<WriteHandler> writeHandlers) {
            this.writeHandlers = writeHandlers;
            return this;
        }

        public ExportConfig<T> build() {
            if (dataClass == null || fileName == null || cursorDataProvider == null) {
                throw new IllegalArgumentException("dataClass, fileName, cursorDataProvider 不能为空");
            }

            ExportConfig<T> config = new ExportConfig<>(dataClass, fileName, cursorDataProvider);
            config.setSheetName(sheetName)
                  .setBatchSize(batchSize)
                  .setProgressCallback(progressCallback)
                  .setWriteHandlers(writeHandlers);

            return config;
        }
    }

    /**
     * 创建构建器
     */
    public static <T> ExportConfigBuilder<T> builder(Class<T> dataClass) {
        return new ExportConfigBuilder<>(dataClass);
    }
}
