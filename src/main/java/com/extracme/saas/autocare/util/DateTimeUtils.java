package com.extracme.saas.autocare.util;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 时间工具类
 */
@Slf4j
public class DateTimeUtils {

    public static final String DATE_TYPE1 = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_TYPE2 = "yyyyMMddHHmmss";

    public static final String DATE_TYPE3 = "yyyy-MM-dd";

    public static final String DATE_TYPE4 = "yyyyMMdd";

    public static final String DATE_TYPE5 = "yyyy-MM-dd'T'HH:mm'Z'";

    /**
     * 将LocalDateTime对象转换为字符串
     * 
     * @param localDateTime LocalDateTime对象
     * @param pattern       日期时间格式，如"yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期时间字符串
     */
    public static String localDateTimeToString(LocalDateTime localDateTime, String pattern) {
        if (localDateTime == null || pattern == null || pattern.isEmpty()) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(formatter);
    }

    /**
     * 将字符串转换为LocalDateTime对象
     * 
     * @param dateTimeStr 日期时间字符串
     * @param pattern     日期时间格式，如"yyyy-MM-dd HH:mm:ss"
     * @return 解析后的LocalDateTime对象
     */
    public static LocalDateTime stringToLocalDateTime(String dateTimeStr, String pattern) {
        if (dateTimeStr == null || pattern == null || pattern.isEmpty()) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        try {
            return LocalDateTime.parse(dateTimeStr, formatter);
        } catch (Exception e) {
            return null; // 或者可以抛出异常，根据具体需求处理
        }
    }

    /**
     * 将Date对象转换为字符串
     * 
     * @param date    Date对象
     * @param pattern 日期时间格式，如"yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期时间字符串
     */
    public static String dateToString(Date date, String pattern) {
        if (date == null || pattern == null || pattern.isEmpty()) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * 将字符串转换为Date对象
     * 
     * @param dateStr 日期时间字符串
     * @param pattern 日期时间格式，如"yyyy-MM-dd HH:mm:ss"
     * @return 解析后的Date对象
     */
    public static Date stringToDate(String dateStr, String pattern) {
        if (dateStr == null || pattern == null || pattern.isEmpty()) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            return sdf.parse(dateStr);
        } catch (Exception e) {
            return null; // 或者可以抛出异常，根据具体需求处理
        }
    }

    /**
     * 将LocalDateTime对象转换为Date对象
     * 
     * @param localDateTime LocalDateTime对象
     * @return 转换后的Date对象
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将Date对象转换为LocalDateTime对象
     * 
     * @param date Date对象
     * @return 转换后的LocalDateTime对象
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static String formatTargetPattern(String dateTime, String sourcePattern, String targetPattern) {
        if (dateTime == null || dateTime == "") {
            return "";
        }
        try {
            // 定义输入格式
            SimpleDateFormat inputFormat = new SimpleDateFormat(sourcePattern);
            // 定义输出格式
            SimpleDateFormat outputFormat = new SimpleDateFormat(targetPattern);
            // 解析输入的时间
            Date date = inputFormat.parse(dateTime);
            // 格式化输出
            return outputFormat.format(date);
        } catch (Exception e) {
            log.error("formatTargetPattern exception ", e);
            return "";
        }
    }

    // 在工具类中
    public static Date addOneDay(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static void main(String[] args) {
        System.out.println(DateUtil.now());
    }
}
