package com.extracme.saas.autocare.util;

import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 用户权限缓存工具类
 * 提供用户权限信息的Redis缓存操作，包括组织权限和系统权限
 * 
 * 缓存Key规则：
 * - 用户权限：user:permissions:{tenantId}:{userId}
 * - 用户组织：user:orgs:{tenantId}:{userId}
 * - 用户可访问组织：user:accessible-orgs:{tenantId}:{userId}
 * 
 * <AUTHOR>
 * @date 2024/12/24
 */
@Slf4j
@Component
public class UserPermissionCacheUtils {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private TableUserRoleService tableUserRoleService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 缓存过期时间（秒）- 24小时
     */
    private static final long CACHE_EXPIRE_TIME = 24 * 60 * 60;

    /**
     * 缓存Key前缀
     */
    private static final String PERMISSIONS_KEY_PREFIX = "user:permissions:";

    /**
     * 用户权限缓存数据结构
     */
    public static class UserPermissionCache {
        private Set<String> permissions;
        private List<String> orgIds;
        private List<String> allAccessibleOrgIds;

        public UserPermissionCache() {}

        public UserPermissionCache(Set<String> permissions, List<String> orgIds, List<String> allAccessibleOrgIds) {
            this.permissions = permissions;
            this.orgIds = orgIds;
            this.allAccessibleOrgIds = allAccessibleOrgIds;
        }

        public Set<String> getPermissions() {
            return permissions;
        }

        public void setPermissions(Set<String> permissions) {
            this.permissions = permissions;
        }

        public List<String> getOrgIds() {
            return orgIds;
        }

        public void setOrgIds(List<String> orgIds) {
            this.orgIds = orgIds;
        }

        public List<String> getAllAccessibleOrgIds() {
            return allAccessibleOrgIds;
        }

        public void setAllAccessibleOrgIds(List<String> allAccessibleOrgIds) {
            this.allAccessibleOrgIds = allAccessibleOrgIds;
        }
    }

    /**
     * 缓存用户权限信息
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param permissions 权限集合
     * @param orgIds 直接关联的组织ID列表
     * @param allAccessibleOrgIds 可访问的所有组织ID列表
     * @return 是否缓存成功
     */
    public boolean cacheUserPermissions(Long tenantId, Long userId, Set<String> permissions, 
                                       List<String> orgIds, List<String> allAccessibleOrgIds) {
        try {
            UserPermissionCache cache = new UserPermissionCache(permissions, orgIds, allAccessibleOrgIds);
            String key = buildPermissionKey(tenantId, userId);
            String cacheValue = objectMapper.writeValueAsString(cache);
            
            boolean success = redisUtils.set(key, cacheValue, CACHE_EXPIRE_TIME);
            
            if (success) {
                log.debug("用户权限信息缓存成功，租户ID: {}, 用户ID: {}, 权限数量: {}, 组织数量: {}, 可访问组织数量: {}",
                         tenantId, userId, 
                         permissions != null ? permissions.size() : 0,
                         orgIds != null ? orgIds.size() : 0,
                         allAccessibleOrgIds != null ? allAccessibleOrgIds.size() : 0);
            } else {
                log.warn("用户权限信息缓存失败，租户ID: {}, 用户ID: {}", tenantId, userId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("缓存用户权限信息异常，租户ID: {}, 用户ID: {}", tenantId, userId, e);
            return false;
        }
    }

    /**
     * 获取用户权限信息
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 用户权限缓存对象，如果不存在或解析失败返回null
     */
    public UserPermissionCache getUserPermissions(Long tenantId, Long userId) {
        try {
            String key = buildPermissionKey(tenantId, userId);
            Object cacheValue = redisUtils.get(key);
            
            if (cacheValue == null) {
                log.debug("用户权限信息缓存不存在，租户ID: {}, 用户ID: {}", tenantId, userId);
                return null;
            }
            
            UserPermissionCache cache = objectMapper.readValue(cacheValue.toString(), UserPermissionCache.class);
            
            log.debug("用户权限信息缓存命中，租户ID: {}, 用户ID: {}, 权限数量: {}, 组织数量: {}, 可访问组织数量: {}",
                     tenantId, userId,
                     cache.getPermissions() != null ? cache.getPermissions().size() : 0,
                     cache.getOrgIds() != null ? cache.getOrgIds().size() : 0,
                     cache.getAllAccessibleOrgIds() != null ? cache.getAllAccessibleOrgIds().size() : 0);
            
            return cache;
        } catch (Exception e) {
            log.error("获取用户权限信息缓存异常，租户ID: {}, 用户ID: {}", tenantId, userId, e);
            return null;
        }
    }

    /**
     * 删除用户权限缓存
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    public boolean deleteUserPermissions(Long tenantId, Long userId) {
        try {
            String key = buildPermissionKey(tenantId, userId);
            redisUtils.del(key);
            
            log.debug("用户权限信息缓存删除成功，租户ID: {}, 用户ID: {}", tenantId, userId);
            return true;
        } catch (Exception e) {
            log.error("删除用户权限信息缓存异常，租户ID: {}, 用户ID: {}", tenantId, userId, e);
            return false;
        }
    }

    /**
     * 刷新用户权限缓存
     * 删除现有缓存，强制下次访问时重新从数据库加载
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 是否刷新成功
     */
    public boolean refreshUserPermissions(Long tenantId, Long userId) {
        log.info("刷新用户权限缓存，租户ID: {}, 用户ID: {}", tenantId, userId);
        return deleteUserPermissions(tenantId, userId);
    }

    /**
     * 批量删除租户下所有用户的权限缓存
     *
     * @param tenantId 租户ID
     * @return 删除的缓存数量
     */
    public long deleteAllUserPermissionsByTenant(Long tenantId) {
        try {
            String pattern = PERMISSIONS_KEY_PREFIX + tenantId + ":*";
            long deletedCount = redisUtils.deleteByPattern(pattern);
            
            log.info("批量删除租户权限缓存完成，租户ID: {}, 删除数量: {}", tenantId, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("批量删除租户权限缓存异常，租户ID: {}", tenantId, e);
            return 0;
        }
    }

    /**
     * 检查Redis连接状态
     *
     * @return Redis是否可用
     */
    public boolean isRedisAvailable() {
        try {
            return redisUtils.ping();
        } catch (Exception e) {
            log.warn("Redis连接检查失败", e);
            return false;
        }
    }

    /**
     * 构建权限缓存Key
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 缓存Key
     */
    private String buildPermissionKey(Long tenantId, Long userId) {
        return PERMISSIONS_KEY_PREFIX + tenantId + ":" + userId;
    }

    /**
     * 获取用户权限集合（便捷方法）
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 权限集合，如果缓存不存在返回空集合
     */
    public Set<String> getUserPermissionSet(Long tenantId, Long userId) {
        UserPermissionCache cache = getUserPermissions(tenantId, userId);
        return cache != null && cache.getPermissions() != null ? 
               cache.getPermissions() : Collections.emptySet();
    }

    /**
     * 获取用户组织ID列表（便捷方法）
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 组织ID列表，如果缓存不存在返回空列表
     */
    public List<String> getUserOrgIds(Long tenantId, Long userId) {
        UserPermissionCache cache = getUserPermissions(tenantId, userId);
        return cache != null && cache.getOrgIds() != null ? 
               cache.getOrgIds() : Collections.emptyList();
    }

    /**
     * 获取用户可访问的所有组织ID列表（便捷方法）
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 可访问组织ID列表，如果缓存不存在返回空列表
     */
    public List<String> getUserAllAccessibleOrgIds(Long tenantId, Long userId) {
        UserPermissionCache cache = getUserPermissions(tenantId, userId);
        return cache != null && cache.getAllAccessibleOrgIds() != null ?
               cache.getAllAccessibleOrgIds() : Collections.emptyList();
    }

    /**
     * 刷新多个用户的权限缓存
     *
     * @param tenantId 租户ID
     * @param userIds 用户ID列表
     * @return 成功刷新的用户数量
     */
    public int refreshMultipleUserPermissions(Long tenantId, List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (Long userId : userIds) {
            if (refreshUserPermissions(tenantId, userId)) {
                successCount++;
            }
        }

        log.info("批量刷新用户权限缓存完成，租户ID: {}, 总数: {}, 成功: {}",
                 tenantId, userIds.size(), successCount);
        return successCount;
    }

    /**
     * 根据角色ID刷新相关用户的权限缓存
     * 当角色权限发生变更时调用
     *
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 成功刷新的用户数量
     */
    public int refreshUserPermissionsByRole(Long tenantId, Long roleId) {
        try {
            log.info("角色权限变更，开始刷新相关用户缓存，租户ID: {}, 角色ID: {}", tenantId, roleId);

            // 查询拥有该角色且属于指定租户的所有用户ID
            List<Long> userIds = tableUserRoleService.findUserIdsByRoleIdAndTenantId(roleId, tenantId);

            if (userIds.isEmpty()) {
                log.debug("角色ID: {} 在租户ID: {} 下没有关联的用户，无需刷新缓存", roleId, tenantId);
                return 0;
            }

            // 批量刷新用户权限缓存
            int successCount = 0;
            for (Long userId : userIds) {
                if (refreshUserPermissions(tenantId, userId)) {
                    successCount++;
                }
            }

            log.info("角色权限变更，成功刷新 {} 个用户的权限缓存，角色ID: {}, 租户ID: {}, 总用户数: {}",
                    successCount, roleId, tenantId, userIds.size());
            return successCount;
        } catch (Exception e) {
            log.error("根据角色ID刷新用户权限缓存失败，租户ID: {}, 角色ID: {}", tenantId, roleId, e);
            return 0;
        }
    }

    /**
     * 根据组织ID刷新相关用户的权限缓存
     * 当组织结构发生变更时调用
     *
     * @param tenantId 租户ID
     * @param orgId 组织ID
     * @return 成功刷新的用户数量
     */
    public int refreshUserPermissionsByOrg(Long tenantId, String orgId) {
        try {
            log.info("组织结构变更，需要刷新相关用户缓存，租户ID: {}, 组织ID: {}", tenantId, orgId);

            // 删除整个租户的权限缓存，强制重新加载
            return (int) deleteAllUserPermissionsByTenant(tenantId);
        } catch (Exception e) {
            log.error("根据组织ID刷新用户权限缓存失败，租户ID: {}, 组织ID: {}", tenantId, orgId, e);
            return 0;
        }
    }
}
