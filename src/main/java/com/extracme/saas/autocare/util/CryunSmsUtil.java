package com.extracme.saas.autocare.util;

import java.net.URLEncoder;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.extracme.saas.autocare.config.SmsConfig;
import com.extracme.saas.autocare.model.dto.SendSmsDTO;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 创瑞短信工具类
 * 
 * <p>提供短信发送功能，支持单条和批量发送</p>
 * <p>支持多账号配置，根据消息类型和服务类型自动选择账号</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
@Component
@Slf4j
public class CryunSmsUtil {

    /** HTTP成功状态码 */
    private static final int HTTP_SUCCESS = 200;

    /** 接口响应成功标志 */
    private static final String SUCCESS = "0";

    /** 短信模板ID（暂时不使用） */
    // private static final String SINGLE_TEMPLATE_ID = "XXXXXX";

    /** 发送统计计数器 */
    private static final AtomicLong sendCounter = new AtomicLong(0);
    private static final AtomicLong successCounter = new AtomicLong(0);
    private static final AtomicLong failureCounter = new AtomicLong(0);

    /**
     * 获取短信配置
     * 使用SpringContextUtil动态获取Bean，解决静态字段依赖注入问题
     * 
     * @return SmsConfig配置对象
     */
    private static SmsConfig getSmsConfig() {
        try {
            return SpringContextUtil.getBean(SmsConfig.class);
        } catch (Exception e) {
            log.error("获取SmsConfig配置失败", e);
            throw new RuntimeException("短信配置未初始化，请检查Spring配置", e);
        }
    }

    /**
     * 发送短信（静态方法，保持向后兼容）
     * 
     * @param sendSmsDTO 短信发送DTO
     * @param isBatchSend 是否批量发送
     * @return 批次ID，发送失败返回null
     * @throws IllegalArgumentException 参数校验失败
     */
    public static String sendSms(SendSmsDTO sendSmsDTO, boolean isBatchSend) {
        return doSendSms(sendSmsDTO, isBatchSend);
    }

    /**
     * 发送短信（实例方法）
     * 
     * @param sendSmsDTO 短信发送DTO
     * @param isBatchSend 是否批量发送
     * @return 批次ID，发送失败返回null
     * @throws IllegalArgumentException 参数校验失败
     */
    public static String doSendSms(SendSmsDTO sendSmsDTO, boolean isBatchSend) {
        // 参数校验
        validateSendSmsDTO(sendSmsDTO);
        
        long startTime = System.currentTimeMillis();
        String batchId = null;
        
        try {
            sendCounter.incrementAndGet();
            
            if (isBatchSend) {
                String mobiles = String.join(",", sendSmsDTO.getMobiles());
                batchId = sendBatch(mobiles, sendSmsDTO.getContent());
            } else {
                // 由于单条短信发送接口必须配模板，这里直接使用批量接口
                batchId = sendBatch(sendSmsDTO.getMobile(), sendSmsDTO.getContent());
            }
            
            if (batchId != null) {
                successCounter.incrementAndGet();
            } else {
                failureCounter.incrementAndGet();
            }
            
            return batchId;
            
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            log.info("短信发送完成 - 耗时:{}ms, 成功率:{}/{}, 批次ID:{}", 
                duration, successCounter.get(), sendCounter.get(), batchId);
        }
    }

    /**
     * 校验短信发送DTO
     * 
     * @param sendSmsDTO 短信发送DTO
     * @throws IllegalArgumentException 参数校验失败
     */
    private static void validateSendSmsDTO(SendSmsDTO sendSmsDTO) {
        if (sendSmsDTO == null) {
            throw new IllegalArgumentException("短信发送参数不能为空");
        }
        
        if (!StringUtils.hasText(sendSmsDTO.getContent())) {
            throw new IllegalArgumentException("短信内容不能为空");
        }
        
        if (sendSmsDTO.getContent().length() > 500) {
            throw new IllegalArgumentException("短信内容长度不能超过500字符");
        }
        
        // 校验手机号
        if (sendSmsDTO.getMobiles() != null && !sendSmsDTO.getMobiles().isEmpty()) {
            for (String mobile : sendSmsDTO.getMobiles()) {
                validateMobile(mobile);
            }
        } else if (StringUtils.hasText(sendSmsDTO.getMobile())) {
            validateMobile(sendSmsDTO.getMobile());
        } else {
            throw new IllegalArgumentException("手机号不能为空");
        }
    }

    /**
     * 校验手机号格式
     * 
     * @param mobile 手机号
     * @throws IllegalArgumentException 手机号格式不正确
     */
    private static void validateMobile(String mobile) {
        if (!StringUtils.hasText(mobile)) {
            throw new IllegalArgumentException("手机号不能为空");
        }
        
        if (!mobile.matches("^1[3-9]\\d{9}$")) {
            throw new IllegalArgumentException("手机号格式不正确: " + mobile);
        }
    }

    /**
     * 根据消息类型和服务类型获取账号配置
     * 
     * @param messageType 消息类型：0-短信，1-语音(只有验证码)
     * @param serviceType 服务类型：0-通知，1-营销（创蓝），2-验证码，3-营销（梦网）
     * @return 账号配置数组 [accessKey, secret, sign]
     */
    private static String[] getAccountConfig() {
        SmsConfig smsConfig = getSmsConfig();
        SmsConfig.Account account = smsConfig.getMain();
        log.debug("使用挚极账号配置");
        
        return new String[]{account.getAccessKey(), account.getSecret(), account.getSign()};
    }

    /**
     * 批量发送短信（静态方法）
     * 
     * @param mobiles 手机号列表（逗号分隔）
     * @param content 短信内容
     * @param messageType 消息类型
     * @param serviceType 服务类型
     * @return 批次ID，发送失败返回null
     */
    private static String sendBatch(String mobiles, String content) {
        SmsConfig smsConfig = getSmsConfig();
        
        // 检查批量发送数量限制
        int mobileCount = mobiles.split(",").length;
        if (mobileCount > smsConfig.getApi().getMaxBatchSize()) {
            log.error("创瑞-批量短信发送数量超过限制 - 当前数量:{}, 最大限制:{}, 手机号:{}", 
                mobileCount, smsConfig.getApi().getMaxBatchSize(), mobiles);
            throw new IllegalArgumentException("批量发送数量不能超过" + smsConfig.getApi().getMaxBatchSize());
        }

        HttpClient httpClient = createHttpClient(smsConfig);
        String batchUrl = smsConfig.getApi().getHost() + smsConfig.getApi().getBatchPath();
        PostMethod postMethod = new PostMethod(batchUrl);
        
        configurePostMethod(postMethod, smsConfig);

        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        
        try {
            String[] accountConfig = getAccountConfig();
            NameValuePair[] data = {
                new NameValuePair("accesskey", accountConfig[0]),
                new NameValuePair("secret", accountConfig[1]),
                new NameValuePair("sign", accountConfig[2]),
                new NameValuePair("mobile", mobiles),
                new NameValuePair("content", URLEncoder.encode(content, smsConfig.getApi().getCharset()))
            };

            log.info("创瑞-批量短信发送开始 - 手机号数量:{}, UUID:{}, 内容长度:{}", 
                mobileCount, uuid, content.length());
            log.debug("创瑞-批量短信发送参数 - 手机号:{}, 请求参数:{}, UUID:{}", 
                mobiles, JSON.toJSONString(data), uuid);

            postMethod.setRequestBody(data);
            postMethod.setRequestHeader("Connection", "close");

            int statusCode = httpClient.executeMethod(postMethod);
            if (statusCode != HTTP_SUCCESS) {
                throw new RuntimeException("HTTP请求失败，状态码: " + statusCode);
            }

            String responseBody = postMethod.getResponseBodyAsString();
            Resp resp = JSON.parseObject(responseBody, Resp.class);
            
            if (!SUCCESS.equals(resp.getCode())) {
                throw new RuntimeException("接口响应失败，错误码: " + resp.getCode() + ", 错误信息: " + resp.getMsg());
            }

            log.info("创瑞-批量短信发送成功 - UUID:{}, 批次ID:{}", uuid, resp.getBatchId());
            return resp.getBatchId();
            
        } catch (Exception e) {
            log.error("创瑞-批量短信发送异常 - 手机号数量:{}, UUID:{}, 异常信息:{}", 
                mobileCount, uuid, e.getMessage(), e);
            return null;
        } finally {
            postMethod.releaseConnection();
        }
    }

    /**
     * 创建HTTP客户端
     * 
     * @param smsConfig 短信配置
     * @return HttpClient实例
     */
    private static HttpClient createHttpClient(SmsConfig smsConfig) {
        HttpClient httpClient = new HttpClient();
        
        // 设置超时时间
        httpClient.getHttpConnectionManager().getParams()
            .setConnectionTimeout(smsConfig.getApi().getConnectionTimeout());
        httpClient.getHttpConnectionManager().getParams()
            .setSoTimeout(smsConfig.getApi().getReadTimeout());
            
        return httpClient;
    }

    /**
     * 配置POST方法
     * 
     * @param postMethod POST方法实例
     * @param smsConfig 短信配置
     */
    private static void configurePostMethod(PostMethod postMethod, SmsConfig smsConfig) {
        postMethod.getParams().setContentCharset(smsConfig.getApi().getCharset());
        postMethod.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, 
            new DefaultHttpMethodRetryHandler());
    }

    /**
     * 获取发送统计信息
     * 
     * @return 统计信息字符串
     */
    public static String getStatistics() {
        long total = sendCounter.get();
        long success = successCounter.get();
        long failure = failureCounter.get();
        double successRate = total > 0 ? (double) success / total * 100 : 0;
        
        return String.format("短信发送统计 - 总数:%d, 成功:%d, 失败:%d, 成功率:%.2f%%", 
            total, success, failure, successRate);
    }

    /**
     * 重置统计计数器
     */
    public static void resetStatistics() {
        sendCounter.set(0);
        successCounter.set(0);
        failureCounter.set(0);
        log.info("短信发送统计计数器已重置");
    }

    /**
     * 短信接口响应实体
     */
    @Data
    private static class Resp {
        /**
         * 响应码
         */
        private String code;
        
        /**
         * 响应消息
         */
        private String msg;
        
        /**
         * 批次ID
         */
        private String batchId;
    }

    /**
     * 测试方法（仅用于开发测试）
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        log.warn("此方法仅用于开发测试，生产环境请勿使用");
        // 测试代码已移除，建议使用单元测试
        String mobile = "15202164405";
        String content = "的：5116。如非本人操作，请忽略本短信。";
        String batchId = sendBatch(mobile, content);
        System.out.println(batchId);
    }
}
