package com.extracme.saas.autocare.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * HTTP工具类
 * 提供XML和RESTful风格的HTTP请求功能
 * 
 * <AUTHOR>
 * @date 2025-07-15 13:13
 * @version 2.0
 */
@Slf4j
public class HttpUtils {

    /** HTTP连接超时时间（毫秒） */
    public static final int HTTP_CONNECT_TIMEOUT = 30000;
    
    /** HTTP读取超时时间（毫秒） */
    public static final int HTTP_READ_TIMEOUT = 30000;
    
    /** 默认缓冲区大小 */
    private static final int DEFAULT_BUFFER_SIZE = 8192;
    
    /** GBK字符集名称 */
    private static final String CHARSET_GBK = "GBK";
    
    /** UTF-8字符集 */
    private static final String CHARSET_UTF8 = StandardCharsets.UTF_8.name();
    
    /** JSON内容类型 */
    private static final String CONTENT_TYPE_JSON = "application/json";

    /**
     * 发送XML格式的POST请求
     * 
     * @param xmlString XML请求参数，不能为空
     * @param urlStr 请求URL地址，不能为空
     * @return 服务器响应内容，如果请求失败返回空字符串
     * @throws IllegalArgumentException 当参数为空时抛出
     */
    public static String sendXmlPost(String xmlString, String urlStr) {
        validateParameters(xmlString, urlStr);
        
        log.info("发送XML POST请求，URL: {}", urlStr);
        log.debug("请求XML内容: {}", xmlString);
        
        StringBuilder resultBuilder = new StringBuilder();
        URLConnection urlConnection = null;
        BufferedOutputStream outputStream = null;
        BufferedReader inputReader = null;
        
        try {
            URL url = new URL(urlStr);
            urlConnection = url.openConnection();
            
            // 配置连接参数
            configureConnection(urlConnection);
            
            // 发送请求数据
            outputStream = new BufferedOutputStream(urlConnection.getOutputStream());
            outputStream.write(xmlString.getBytes(CHARSET_GBK));
            outputStream.flush();
            
            // 读取响应数据
            inputReader = new BufferedReader(
                    new InputStreamReader(urlConnection.getInputStream(), CHARSET_GBK));
            
            String line;
            while ((line = inputReader.readLine()) != null) {
                resultBuilder.append(line);
            }
            
            String result = resultBuilder.toString();
            log.debug("XML POST响应内容: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("发送XML POST请求失败，URL: {}, 错误信息: {}", urlStr, e.getMessage(), e);
            return StringUtils.EMPTY;
        } finally {
            // 确保资源正确关闭
            closeQuietly(inputReader);
            closeQuietly(outputStream);
            closeConnection(urlConnection);
        }
    }

    /**
     * 发送RESTful风格的POST请求
     * 
     * @param url 请求URL地址，不能为空
     * @param parameter JSON格式的请求参数，可以为空
     * @return JSON格式的响应内容，如果请求失败返回null
     * @throws IllegalArgumentException 当URL参数为空时抛出
     */
    public static String sendRestPost(String url, String parameter) {
        if (StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("URL参数不能为空");
        }
        
        log.info("发送REST POST请求，URL: {}", url);
        log.debug("请求参数: {}", parameter);
        
        HttpURLConnection httpConnection = null;
        OutputStream outputStream = null;
        OutputStreamWriter writer = null;
        InputStream inputStream = null;
        ByteArrayOutputStream responseStream = null;
        
        try {
            URL urlObj = new URL(url);
            httpConnection = (HttpURLConnection) urlObj.openConnection();
            
            // 配置HTTP连接
            configureHttpConnection(httpConnection);
            
            // 发送请求数据
            if (StringUtils.isNotBlank(parameter)) {
                outputStream = httpConnection.getOutputStream();
                writer = new OutputStreamWriter(outputStream, CHARSET_UTF8);
                IOUtils.write(parameter, writer);
                writer.flush();
            }
            
            // 读取响应
            String responseData = readHttpResponse(httpConnection);
            
            // 解析并返回JSON响应
            JSONObject response = parseJsonResponse(responseData);
            String result = JSON.toJSONString(response);
            
            log.debug("REST POST响应内容: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("发送REST POST请求失败，URL: {}, 错误信息: {}", url, e.getMessage(), e);
            return null;
        } finally {
            // 确保资源正确关闭
            closeQuietly(writer);
            closeQuietly(outputStream);
            closeQuietly(inputStream);
            closeQuietly(responseStream);
            closeConnection(httpConnection);
        }
    }
    
    /**
     * 验证必要参数
     * 
     * @param xmlString XML字符串
     * @param urlStr URL字符串
     */
    private static void validateParameters(String xmlString, String urlStr) {
        if (StringUtils.isBlank(xmlString)) {
            throw new IllegalArgumentException("XML参数不能为空");
        }
        if (StringUtils.isBlank(urlStr)) {
            throw new IllegalArgumentException("URL参数不能为空");
        }
    }
    
    /**
     * 配置URL连接参数
     * 
     * @param connection URL连接对象
     */
    private static void configureConnection(URLConnection connection) {
        connection.setUseCaches(false);
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.setConnectTimeout(HTTP_CONNECT_TIMEOUT);
        connection.setReadTimeout(HTTP_READ_TIMEOUT);
    }
    
    /**
     * 配置HTTP连接参数
     * 
     * @param connection HTTP连接对象
     */
    private static void configureHttpConnection(HttpURLConnection connection) {
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.setUseCaches(false);
        connection.setRequestProperty("Content-Type", CONTENT_TYPE_JSON);
        connection.setConnectTimeout(HTTP_CONNECT_TIMEOUT);
        connection.setReadTimeout(HTTP_READ_TIMEOUT);
        
        try {
            connection.connect();
        } catch (Exception e) {
            log.warn("HTTP连接建立失败: {}", e.getMessage());
        }
    }
    
    /**
     * 读取HTTP响应内容
     * 
     * @param connection HTTP连接对象
     * @return 响应内容字符串
     * @throws IOException 读取异常
     */
    private static String readHttpResponse(HttpURLConnection connection) throws IOException {
        InputStream inputStream = null;
        ByteArrayOutputStream responseStream = new ByteArrayOutputStream();
        
        try {
            int responseCode = connection.getResponseCode();
            
            if (responseCode == HttpURLConnection.HTTP_OK || 
                responseCode == HttpURLConnection.HTTP_CREATED) {
                inputStream = connection.getInputStream();
            } else if (responseCode == HttpURLConnection.HTTP_BAD_REQUEST) {
                inputStream = connection.getErrorStream();
            } else {
                log.warn("HTTP响应码异常: {}", responseCode);
                inputStream = connection.getErrorStream();
            }
            
            if (inputStream != null) {
                byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    responseStream.write(buffer, 0, bytesRead);
                }
            }
            
            return responseStream.toString(CHARSET_UTF8);
            
        } finally {
            closeQuietly(inputStream);
            closeQuietly(responseStream);
        }
    }
    
    /**
     * 解析JSON响应
     * 
     * @param responseData 响应数据
     * @return JSON对象
     */
    private static JSONObject parseJsonResponse(String responseData) {
        if (StringUtils.isNotBlank(responseData)) {
            try {
                return JSONObject.parseObject(responseData);
            } catch (Exception e) {
                log.warn("JSON解析失败，返回原始数据: {}", e.getMessage());
                JSONObject fallback = new JSONObject();
                fallback.put("rawData", responseData);
                return fallback;
            }
        }
        return new JSONObject();
    }
    
    /**
     * 安全关闭可关闭资源
     * 
     * @param closeable 可关闭的资源
     */
    private static void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                log.debug("关闭资源时发生异常: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 安全关闭连接
     * 
     * @param connection 连接对象
     */
    private static void closeConnection(URLConnection connection) {
        if (connection instanceof HttpURLConnection) {
            try {
                ((HttpURLConnection) connection).disconnect();
            } catch (Exception e) {
                log.debug("断开HTTP连接时发生异常: {}", e.getMessage());
            }
        }
    }
}
