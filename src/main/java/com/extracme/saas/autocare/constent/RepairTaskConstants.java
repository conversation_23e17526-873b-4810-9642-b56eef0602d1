package com.extracme.saas.autocare.constent;

import java.math.BigDecimal;

/**
 * 维修任务常量类
 */
public class RepairTaskConstants {

    /**
     * 当前环节 - 车辆维修
     */
    public static final Long CURRENT_TACHE_VEHICLE_REPAIR = 6L;
    
    /**
     * 当前环节 - 车辆验收
     */
    public static final Long CURRENT_TACHE_CHECK = 7L;
    
    /**
     * 车辆维修任务状态 - 已完成
     */
    public static final String VEHICLE_REPAIR_COMPLETED = "600";
    
    /**
     * 车辆验收任务状态 - 验收中
     */
    public static final String VEHICLE_CHECK_ACCEPTANCE = "700";
    
    /**
     * 图片类型 - 损坏部位图片
     */
    public static final BigDecimal PIC_TYPE_DAMAGED_PART = BigDecimal.valueOf(1);
    
    /**
     * 图片类型 - 维修图片（验收图片）
     */
    public static final BigDecimal PIC_TYPE_REPAIR = BigDecimal.valueOf(2);
    
    /**
     * 图片类型 - 验收视频
     */
    public static final BigDecimal PIC_TYPE_CHECK_VIDEO = BigDecimal.valueOf(3);
    
    /**
     * 图片类型 - 事故责任认定书图片
     */
    public static final BigDecimal PIC_TYPE_ACCIDENT_LIABILITY = BigDecimal.valueOf(4);
    
    /**
     * 图片类型 - 保司定损单图片
     */
    public static final BigDecimal PIC_TYPE_INSURANCE_LOSS = BigDecimal.valueOf(5);
    
    /**
     * 图片类型 - 我方驾驶证图片
     */
    public static final BigDecimal PIC_TYPE_DRIVER_LICENSE = BigDecimal.valueOf(6);
    
    /**
     * 图片类型 - 客户直付凭证
     */
    public static final BigDecimal PIC_TYPE_CUST_PAYMENT = BigDecimal.valueOf(22);
    
    /**
     * 是否超时 - 是
     */
    public static final String IS_OVER_TIME_YES = "0";
    
    /**
     * 是否超时 - 否
     */
    public static final String IS_OVER_TIME_NO = "1";
}
