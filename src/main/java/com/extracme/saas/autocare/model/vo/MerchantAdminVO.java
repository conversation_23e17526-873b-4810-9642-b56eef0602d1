package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 商户管理员VO
 */
@Data
@ApiModel(description = "商户管理员信息VO")
public class MerchantAdminVO {
    
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long id;
    
    @ApiModelProperty(value = "商户ID", example = "1")
    private Long tenantId;
    
    @ApiModelProperty(value = "商户名称", example = "测试商户")
    private String tenantName;
    
    @ApiModelProperty(value = "用户名", example = "admin")
    private String username;
    
    @ApiModelProperty(value = "管理员姓名", example = "张三")
    private String nickname;
    
    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String mobile;
    
    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;
    
    @ApiModelProperty(value = "状态：0-禁用，1-启用", example = "1")
    private Integer status;
    
    @ApiModelProperty(value = "角色名称列表")
    private java.util.List<String> roleNames;
    
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;
    
    @ApiModelProperty(value = "最后登录时间")
    private Date lastLoginTime;
}
