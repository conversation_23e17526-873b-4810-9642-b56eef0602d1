package com.extracme.saas.autocare.model.vo.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 进度节点视图对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("进度节点视图")
public class ProgressNodeVO {

    /**
     * 活动节点ID
     */
    @ApiModelProperty("活动节点ID")
    private Long activityId;

    /**
     * 活动节点名称
     */
    @ApiModelProperty("活动节点名称")
    private String activityName;

    /**
     * 节点状态
     */
    @ApiModelProperty("节点状态")
    private String status;

    /**
     * 备注说明
     */
    @ApiModelProperty("备注说明")
    private String remarks;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 节点顺序
     */
    @ApiModelProperty("节点顺序")
    private Integer sequence;
}