package com.extracme.saas.autocare.model.dto;

import java.util.List;

import javax.validation.constraints.Size;

import org.apache.commons.collections4.CollectionUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车辆维修信息更新DTO
 */
@Data
@ApiModel(description = "车辆维修信息更新DTO")
public class VehicleInRepairUpdateDTO {
    
    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", required = true, example = "REPAIR20240601001")
    @Size(max = 50, message = "任务编号长度不能超过50个字符")
    private String taskNo;
    
    /**
     * 超时原因
     */
    @ApiModelProperty(value = "超时原因", example = "配件供应延迟")
    @Size(max = 200, message = "超时原因长度不能超过200个字符")
    private String overTimeReasons;
    
    /**
     * 维修图片（验收图片）
     */
    @ApiModelProperty(value = "维修图片（验收图片）", notes = "图片URL列表，最多48张")
    private List<FileDTO> repairPicture;
    
    /**
     * 验收视频
     */
    @ApiModelProperty(value = "验收视频", notes = "视频URL列表")
    private List<FileDTO> checkVideo;
    
    /**
     * 检查图片数量是否超出范围
     * @return 是否超出范围
     */
    @ApiModelProperty(hidden = true)
    public boolean pictureSizeOutOfRange() {
        if (CollectionUtils.isNotEmpty(repairPicture) && repairPicture.size() > 48) {
            return true;
        }
        return false;
    }
}
