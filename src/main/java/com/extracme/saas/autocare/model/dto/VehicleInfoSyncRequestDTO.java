package com.extracme.saas.autocare.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 车辆信息同步请求DTO
 */
@Data
@ApiModel(description = "车辆信息同步请求DTO")
public class VehicleInfoSyncRequestDTO {

    @ApiModelProperty(value = "同步密钥（用于租户身份识别）", required = true)
    @NotNull(message = "同步密钥不能为空")
    private String syncKey;

    @ApiModelProperty(value = "批量同步数据列表", required = true)
    @NotEmpty(message = "同步数据列表不能为空")
    @Valid
    private List<VehicleInfoSyncDataDTO> batchData;

    /**
     * 车辆信息同步数据DTO
     */
    @Data
    @ApiModel(description = "车辆信息同步数据DTO")
    public static class VehicleInfoSyncDataDTO {

        @ApiModelProperty(value = "主键ID（用于判断新增或更新）", required = false, example = "1")
        private Long id;

        @ApiModelProperty(value = "车架号", required = true, example = "LSGGG54X8EH123456")
        @NotBlank(message = "车架号不能为空")
        private String vin;

        @ApiModelProperty(value = "车牌号", required = true, example = "沪A12345")
        @NotBlank(message = "车牌号不能为空")
        private String vehicleNo;

        @ApiModelProperty(value = "车型ID", required = false, example = "1")
        private Long vehicleModelId;

        @ApiModelProperty(value = "维修时所属公司", required = false, example = "ORG001")
        private String vehicleOrgId;

        @ApiModelProperty(value = "维修时运营公司", required = false, example = "ORG002")
        private String operationOrgId;

        @ApiModelProperty(value = "产品线", required = false, example = "2")
        private Integer productLine;

        @ApiModelProperty(value = "子产品线", required = false, example = "4")
        private Integer subProductLine;

        @ApiModelProperty(value = "实际运营标签", required = false, example = "2")
        private Integer factOperateTag;

        @ApiModelProperty(value = "发动机号", required = false, example = "ENG123456")
        private String engineId;

        @ApiModelProperty(value = "注册日期", required = false, example = "2023-01-01")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date registerDate;

        @ApiModelProperty(value = "交强险开始日期", required = false, example = "2023-01-01")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date tciStartdate;

        @ApiModelProperty(value = "交强险结束日期", required = false, example = "2024-01-01")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date tciEnddate;

        @ApiModelProperty(value = "保险归属", required = false, example = "1")
        private Integer insuranceBelongs;

        @ApiModelProperty(value = "保险公司名称", required = false, example = "中国人保")
        private String insuranceCompanyName;

        @ApiModelProperty(value = "资产状态: 0=在建工程, 1=固定资产, 2=固定资产（待报废）, 3=报废, 4=固定资产(待处置), 5=固定资产(已处置), 6=以租代售, 7=库存商品, 8=已处置（未过户）", required = false)
        private Integer propertyStatus;
    }
}
