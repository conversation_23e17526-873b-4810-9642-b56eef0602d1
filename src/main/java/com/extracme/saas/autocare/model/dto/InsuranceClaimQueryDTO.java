package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "保险索赔用印申请列表查询DTO")
public class InsuranceClaimQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "单据状态(1-待处理 2-处理中 3-已完成 4-已关闭)")
    private Integer documentStatus;

    @ApiModelProperty(value = "申请单据号")
    private String documentNumber;

}