package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 核损核价信息DTO
 */
@Data
@ApiModel(description = "核损核价更新DTO")
public class LossAssessmentUpdateDTO {

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", required = true, example = "TASK202403150001")
    private String taskNo;

    /**
     * 确认车损类型 1：车辆原因 2：客户原因
     */
    @ApiModelProperty(value = "确认车损类型", notes = "1：车辆原因 2：客户原因", example = "1")
    private Integer confirmCarDamageType;

    /**
     * 损坏部位图片
     */
    @ApiModelProperty(value = "损坏部位图片", notes = "图片URL列表，最多48张", example = "[\"http://example.com/image1.jpg\"]")
    private List<FileDTO> damagedPartPicture;

    /**
     * 损坏部位视频
     */
    @ApiModelProperty(value = "损坏部位视频", notes = "视频URL列表", example = "[\"http://example.com/video1.mp4\"]")
    private List<FileDTO> damagedPartVideo;

    /**
     * 总里程数
     */
    @ApiModelProperty(value = "总里程数", example = "50000.00")
    private BigDecimal totalMileage;

    /**
     * 客户是否直付 0:否 1:是
     */
    @ApiModelProperty(value = "客户是否直付", notes = "0:否 1:是", example = "1")
    private Integer custPaysDirect;

    /**
     * 客户支付金额
     */
    @ApiModelProperty(value = "客户支付金额", example = "1000.00")
    private BigDecimal custAmount;

    /**
     * 客户直付凭证
     */
    @ApiModelProperty(value = "客户直付凭证", notes = "图片URL列表", example = "[\"http://example.com/receipt1.jpg\"]")
    private List<FileDTO> custPicture;

    @ApiModelProperty(value = "用户承担金额", example = "300.00")
    private BigDecimal userAssumedAmount;

    @ApiModelProperty(value = "非用户承担金额", example = "300.00")
    private BigDecimal notUserAssumedAmount;

    /**
     * 预估理赔金额
     */
    @ApiModelProperty(value = "预估理赔金额", example = "2000.00", required = true)
    @DecimalMin(value = "0", message = "预估保险理赔价格不能小于0")
    @Digits(integer = 10, fraction = 2, message = "预估理赔金额格式不正确，整数位最多10位，小数位最多2位")
    private BigDecimal estimatedClaimAmount;

    /**
     * 定损单金额
     */
    @ApiModelProperty(value = "定损单金额", example = "2000.00")
    @DecimalMin(value = "0", message = "定损单金额不能小于0")
    @Digits(integer = 10, fraction = 2, message = "定损单金额格式不正确，整数位最多10位，小数位最多2位")
    private BigDecimal lossOrderAmount;
}
