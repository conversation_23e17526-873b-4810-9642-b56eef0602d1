package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.constraints.Size;

import org.apache.commons.collections4.CollectionUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车辆维修验收申请DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "车辆维修验收申请DTO")
public class VehicleInRepairInspectionDTO extends VehicleInRepairUpdateDTO {
    
    /**
     * 验收申请备注
     */
    @ApiModelProperty(value = "验收申请备注", example = "维修已完成，请验收")
    @Size(max = 500, message = "验收申请备注长度不能超过500个字符")
    private String inspectionRemark;
    
    /**
     * 维修完成说明
     */
    @ApiModelProperty(value = "维修完成说明", example = "已完成所有维修项目")
    @Size(max = 500, message = "维修完成说明长度不能超过500个字符")
    private String completionDescription;
    
    /**
     * 实际维修费用
     */
    @ApiModelProperty(value = "实际维修费用", example = "1500.00")
    private BigDecimal actualRepairCost;
    
    /**
     * 维修项目列表ID
     */
    @ApiModelProperty(value = "维修项目列表ID", notes = "已完成的维修项目ID列表")
    private List<Long> completedItemIds;
    
    /**
     * 检查图片和视频数量是否超出范围
     * @return 是否超出范围
     */
    @Override
    @ApiModelProperty(hidden = true)
    public boolean pictureSizeOutOfRange() {
        // 先检查父类的图片是否超出范围
        if (super.pictureSizeOutOfRange()) {
            return true;
        }
        
        // 检查验收视频是否超出范围
        if (CollectionUtils.isNotEmpty(getCheckVideo()) && getCheckVideo().size() > 10) {
            return true;
        }
        
        return false;
    }
}
