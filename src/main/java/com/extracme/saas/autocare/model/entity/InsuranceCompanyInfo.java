package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   保险公司信息
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table insurance_company_info
 */
public class InsuranceCompanyInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   主键，自增
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.company_name")
    private String companyName;

    /**
     * Database Column Remarks:
     *   公司简称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.company_abbreviation")
    private String companyAbbreviation;

    /**
     * Database Column Remarks:
     *   联系电话
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.contact_number")
    private String contactNumber;

    /**
     * Database Column Remarks:
     *   状态（1：启用 0：禁用）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.update_by")
    private String updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.company_name")
    public String getCompanyName() {
        return companyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.company_name")
    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.company_abbreviation")
    public String getCompanyAbbreviation() {
        return companyAbbreviation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.company_abbreviation")
    public void setCompanyAbbreviation(String companyAbbreviation) {
        this.companyAbbreviation = companyAbbreviation == null ? null : companyAbbreviation.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.contact_number")
    public String getContactNumber() {
        return contactNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.contact_number")
    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber == null ? null : contactNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyName=").append(companyName);
        sb.append(", companyAbbreviation=").append(companyAbbreviation);
        sb.append(", contactNumber=").append(contactNumber);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}