package com.extracme.saas.autocare.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 车辆出厂登记导出Excel实体
 *
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(25)
@ColumnWidth(15)
public class VehicleLeavingFactoryExportExcel {

    /**
     * 任务编号
     */
    @ExcelProperty(value = "任务编号", index = 0)
    @ColumnWidth(20)
    private String taskNo;

    /**
     * 车牌号
     */
    @ExcelProperty(value = "车牌号", index = 1)
    @ColumnWidth(15)
    private String vehicleNo;

    /**
     * 车架号
     */
    @ExcelProperty(value = "车架号", index = 2)
    @ColumnWidth(20)
    private String vin;

    /**
     * 车型名称
     */
    @ExcelProperty(value = "车型", index = 3)
    @ColumnWidth(20)
    private String vehicleModelInfo;

    /**
     * 修理类型名称
     */
    @ExcelProperty(value = "修理类型", index = 4)
    @ColumnWidth(15)
    private String repairTypeName;

    /**
     * 当前活动名
     */
    @ExcelProperty(value = "当前环节", index = 5)
    @ColumnWidth(20)
    private String currentActivityName;

    /**
     * 当前状态名
     */
    @ExcelProperty(value = "当前状态", index = 6)
    @ColumnWidth(20)
    private String statusName;

    /**
     * 修理厂名称
     */
    @ExcelProperty(value = "修理厂名称", index = 7)
    @ColumnWidth(25)
    private String repairDepotName;

    /**
     * 车辆所属组织机构名称
     */
    @ExcelProperty(value = "车辆运营公司", index = 8)
    @ColumnWidth(20)
    private String orgName;

    /**
     * 任务流入时间（字符串格式）
     */
    @ExcelProperty(value = "任务流入时间", index = 9)
    @ColumnWidth(20)
    private String taskInflowTimeString;

    /**
     * 任务验收时间（字符串格式）
     */
    @ExcelProperty(value = "任务验收时间", index = 10)
    @ColumnWidth(20)
    private String vehicleCheckTimeString;

    /**
     * 车辆出厂时间（字符串格式）
     */
    @ExcelProperty(value = "车辆出厂时间", index = 11)
    @ColumnWidth(20)
    private String deliveryTimeString;
}