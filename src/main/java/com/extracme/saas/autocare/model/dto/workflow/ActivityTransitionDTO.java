package com.extracme.saas.autocare.model.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 活动节点转换规则DTO
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Data
@ApiModel(description = "活动节点转换规则DTO")
public class ActivityTransitionDTO {



    @ApiModelProperty(value = "所属流程模板编号", required = true, example = "1")
    @NotNull(message = "流程模板编号不能为空")
    private Long workflowId;

    @ApiModelProperty(value = "转换起始节点编号", required = true, example = "HANDOVER")
    private String fromActivityCode;

    @ApiModelProperty(value = "目标节点编号", required = true, example = "INSPECTION")
    @NotBlank(message = "目标节点编号不能为空")
    private String toActivityCode;

    @ApiModelProperty(value = "触发事件名称", required = true, example = "COMPLETE_HANDOVER")
    @NotBlank(message = "触发事件名称不能为空")
    private String triggerEvent;

    @ApiModelProperty(value = "条件判断处理器类名", example = "com.extracme.saas.autocare.workflow.handler.ConditionHandler")
    private String conditionHandler;

    @ApiModelProperty(value = "处理逻辑类名", required = true, example = "com.extracme.saas.autocare.workflow.handler.DefaultTransitionHandler")
    @NotBlank(message = "处理逻辑类名不能为空")
    private String handlerClass;



    @ApiModelProperty(value = "规则说明", example = "车辆交接完成转入进保预审")
    private String description;
}
