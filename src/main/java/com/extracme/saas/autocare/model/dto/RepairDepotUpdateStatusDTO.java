package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "维修厂修改状态DTO")
public class RepairDepotUpdateStatusDTO {

    @ApiModelProperty(value = "维修厂ID不能为空", required = true)
    @NotNull(message = "维修厂ID不能为空")
    private Long id;

    @ApiModelProperty(value = "维修厂状态(0-无效 1-有效)", required = true)
    @NotNull(message = "维修厂状态不能为空")
    private Integer repairDepotStatus;

}