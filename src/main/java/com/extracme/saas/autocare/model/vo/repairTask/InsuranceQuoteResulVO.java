package com.extracme.saas.autocare.model.vo.repairTask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "定损报价结果视图对象")
public class InsuranceQuoteResulVO {
    
    @ApiModelProperty(value = "序号")
    private String id;
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;
    
    @ApiModelProperty(value = "车辆所属组织机构ID")
    private String orgId;
    
    @ApiModelProperty(value = "车辆所属组织机构名称")
    private String orgName;
    
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    
    @ApiModelProperty(value = "车型名称")
    private String vehicleModelInfo;
    
    @ApiModelProperty(value = "车架号")
    private String vin;
    
    @ApiModelProperty(value = "车辆保险所属")
    private String insuranceCompanyName;
    
    @ApiModelProperty(value = "修理类型ID", notes = "1:事故维修 2:自费维修 3:车辆保养")
    private String repairTypeId;
    
    @ApiModelProperty(value = "修理类型名称")
    private String repairTypeName;
    
    @ApiModelProperty(value = "修理级别", example = "A,B,C")
    private String repairGrade;
    
    @ApiModelProperty(value = "修理厂ID")
    private String repairDepotOrgId;
    
    @ApiModelProperty(value = "修理厂名称")
    private String repairDepotName;
    
    @ApiModelProperty(value = "任务创建时间")
    private String taskInflowTime;
    
    @ApiModelProperty(value = "车辆接收时间")
    private String vehicleReciveTime;
    
    @ApiModelProperty(value = "定损报价任务进度", notes = "200:未处理 210:处理中 220:改派中 230:已完成 240:被驳回")
    private String insuranceQuoteTaskSchedule;
    
    @ApiModelProperty(value = "是否当前用户处理中", notes = "0是 1其他")
    private String isUpdate;
    
    @ApiModelProperty(value = "改派驳回原因")
    private String reassignmentRejectReasons;
    
    @ApiModelProperty(value = "修理厂类型", notes = "0-合作修理厂 1-非合作修理厂")
    private Integer repairDepotType;
    
    @ApiModelProperty(value = "车辆业务状态", notes = "0：分时租赁 1：长租 3：短租 4：公务用车")
    private Integer renttype;
    
    @ApiModelProperty(value = "实际操作标记")
    private Integer factOperateTag;
    
    @ApiModelProperty(value = "事故转自费标识", notes = "0否 1是")
    private Integer reviewToSelFeeFlag;
}
