package com.extracme.saas.autocare.model.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "备注信息vo")
public class ViewRepairRemarkVO {

    @ApiModelProperty(value = "id", example = "1")
    private Long id;
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "活动节点编码")
    private String activityCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    @ApiModelProperty(value = "是否属于当前用户")
    private Integer isOwner;
}
