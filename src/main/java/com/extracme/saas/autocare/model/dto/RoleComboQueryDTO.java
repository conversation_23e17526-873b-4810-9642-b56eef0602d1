package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 角色下拉列表查询DTO
 */
@Data
@ApiModel(description = "角色下拉列表查询DTO")
public class RoleComboQueryDTO {
    
    /**
     * 租户ID，必填
     * 超级管理员可以查询指定租户的角色列表
     * 普通用户传入的tenantId会被忽略，强制使用当前用户的租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true, example = "1", notes = "超级管理员可查询指定租户角色，普通用户自动使用当前租户ID")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;
}
