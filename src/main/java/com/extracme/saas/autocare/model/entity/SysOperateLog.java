package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   操作日志表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table sys_operate_log
 */
public class SysOperateLog implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   租户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.tenant_id")
    private Long tenantId;

    /**
     * Database Column Remarks:
     *   模块类型（1：用户管理 2：系统管理（角色、权限）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.module_type")
    private Integer moduleType;

    /**
     * Database Column Remarks:
     *   操作类型（1：新增 2：修改 3：删除 4：禁用 5：启用 ）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.operate_type")
    private Integer operateType;

    /**
     * Database Column Remarks:
     *   操作内容描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.content")
    private String content;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.misc_desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   是否有效（0 无效 1 有效）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.update_by")
    private String updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operate_log")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.tenant_id")
    public Long getTenantId() {
        return tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.tenant_id")
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.module_type")
    public Integer getModuleType() {
        return moduleType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.module_type")
    public void setModuleType(Integer moduleType) {
        this.moduleType = moduleType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.operate_type")
    public Integer getOperateType() {
        return operateType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.operate_type")
    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.content")
    public String getContent() {
        return content;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.content")
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.misc_desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.misc_desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operate_log")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", moduleType=").append(moduleType);
        sb.append(", operateType=").append(operateType);
        sb.append(", content=").append(content);
        sb.append(", miscDesc=").append(miscDesc);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}