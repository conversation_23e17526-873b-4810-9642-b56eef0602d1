package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:27
 */
public class EvalLossInfoSynchronize {
    /**
     * 定损单号
     */
    private String lossNo = StringUtils.EMPTY;
    /**
     * 报案号
     */
    private String reportCode = StringUtils.EMPTY;
    /**
     * 车损标的主键
     */
    private String dmgVhclId = StringUtils.EMPTY;
    /**
     * 定损单类型
     */
    private String evalLossType = "1";
    /**
     * 状态代码
     */
    private String statusCode = StringUtils.EMPTY;
    /**
     * 状态名称
     */
    private String statusName = StringUtils.EMPTY;
    /**
     * 操作人所属分机构代码
     */
    private String comCode = StringUtils.EMPTY;
    /**
     * 操作人所属分机构名称
     */
    private String company = StringUtils.EMPTY;
    /**
     * 操作人所属中支代码
     */
    private String branchComCode = StringUtils.EMPTY;
    /**
     * 操作人所属中支名称
     */
    private String branchComName = StringUtils.EMPTY;
    /**
     * 操作人代码
     */
    private String handlerCode = StringUtils.EMPTY;
    /**
     * 操作人名称
     */
    private String handlerName = StringUtils.EMPTY;
    /**
     * 操作环节
     */
    private String operationLink = StringUtils.EMPTY;
    /**
     * 操作结果
     */
    private String operationResults = StringUtils.EMPTY;
    /**
     * 整单备注
     */
    private String operationOpinion = StringUtils.EMPTY;
    /**
     * 自核价标记
     */
    private String selfEstiFlag = "0";
    /**
     * 自核损标记
     */
    private String selfApproveFlag = "0";

    @XmlElement(name = "LossNo")
    public String getLossNo() {
        return lossNo;
    }

    public void setLossNo(String lossNo) {
        this.lossNo = lossNo;
    }

    @XmlElement(name = "ReportCode")
    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }

    @XmlElement(name = "DmgVhclId")
    public String getDmgVhclId() {
        return dmgVhclId;
    }

    public void setDmgVhclId(String dmgVhclId) {
        this.dmgVhclId = dmgVhclId;
    }

    @XmlElement(name = "EvalLossType")
    public String getEvalLossType() {
        return evalLossType;
    }

    public void setEvalLossType(String evalLossType) {
        this.evalLossType = evalLossType;
    }

    @XmlElement(name = "StatusCode")
    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    @XmlElement(name = "StatusName")
    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    @XmlElement(name = "ComCode")
    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    @XmlElement(name = "Company")
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    @XmlElement(name = "BranchComCode")
    public String getBranchComCode() {
        return branchComCode;
    }

    public void setBranchComCode(String branchComCode) {
        this.branchComCode = branchComCode;
    }

    @XmlElement(name = "BranchComName")
    public String getBranchComName() {
        return branchComName;
    }

    public void setBranchComName(String branchComName) {
        this.branchComName = branchComName;
    }

    @XmlElement(name = "HandlerCode")
    public String getHandlerCode() {
        return handlerCode;
    }

    public void setHandlerCode(String handlerCode) {
        this.handlerCode = handlerCode;
    }

    @XmlElement(name = "HandlerName")
    public String getHandlerName() {
        return handlerName;
    }

    public void setHandlerName(String handlerName) {
        this.handlerName = handlerName;
    }

    @XmlElement(name = "OperationLink")
    public String getOperationLink() {
        return operationLink;
    }

    public void setOperationLink(String operationLink) {
        this.operationLink = operationLink;
    }

    @XmlElement(name = "OperationResults")
    public String getOperationResults() {
        return operationResults;
    }

    public void setOperationResults(String operationResults) {
        this.operationResults = operationResults;
    }

    @XmlElement(name = "OperationOpinion")
    public String getOperationOpinion() {
        return operationOpinion;
    }

    public void setOperationOpinion(String operationOpinion) {
        this.operationOpinion = operationOpinion;
    }

    @XmlElement(name = "SelfEstiFlag")
    public String getSelfEstiFlag() {
        return selfEstiFlag;
    }

    public void setSelfEstiFlag(String selfEstiFlag) {
        this.selfEstiFlag = selfEstiFlag;
    }

    @XmlElement(name = "SelfApproveFlag")
    public String getSelfApproveFlag() {
        return selfApproveFlag;
    }

    public void setSelfApproveFlag(String selfApproveFlag) {
        this.selfApproveFlag = selfApproveFlag;
    }
}
