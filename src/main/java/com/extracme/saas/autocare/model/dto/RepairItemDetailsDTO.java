package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "维修项目明细DTO")
public class RepairItemDetailsDTO {

    @ApiModelProperty(value = "换件项目明细")
    private List<MtcLossFitInfoDTO> mtcLossFitInfoBOList;

    @ApiModelProperty(value = "工时项目明细")
    private List<MtcLossRepairInfoDTO> mtcLossRepairInfoBOList;

    @ApiModelProperty(value = "维修项目明细(保养和终端任务)（自有配件库）")
    private List<MtcRepairItemCheckInfoDTO> mtcRepairItemCheckInfoDTOList;

    @ApiModelProperty(value = "预审维修项目明细（自有配件库）")
    private List<MtcRepairItemCheckInfoDTO> reviewRepairItemCheckInfoDTOList;
}