package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * 文件上传 DTO
 */
@Data
@ApiModel(description = "文件上传DTO")
public class FileUploadDTO {

    @ApiModelProperty(value = "上传的文件", required = true)
    @NotNull(message = "上传文件不能为空")
    private MultipartFile file;

    @ApiModelProperty(value = "文件分类目录", example = "documents")
    private String category;

    @ApiModelProperty(value = "是否启用断点续传", example = "true")
    private Boolean enableResumable = true;

    @ApiModelProperty(value = "分片大小（字节）", example = "5242880")
    private Long partSize = 5 * 1024 * 1024L; // 默认 5MB

    @ApiModelProperty(value = "上传任务ID（断点续传时使用）")
    private String uploadId;

    @ApiModelProperty(value = "已上传的分片编号列表（断点续传时使用）")
    private String uploadedParts;
}
