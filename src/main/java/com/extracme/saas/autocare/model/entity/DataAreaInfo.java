package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table data_area_info
 */
public class DataAreaInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.areaid")
    private Long areaid;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.area")
    private String area;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.fatherid")
    private Long fatherid;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.lon")
    private BigDecimal lon;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.lat")
    private BigDecimal lat;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.areaid")
    public Long getAreaid() {
        return areaid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.areaid")
    public void setAreaid(Long areaid) {
        this.areaid = areaid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.area")
    public String getArea() {
        return area;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.area")
    public void setArea(String area) {
        this.area = area == null ? null : area.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.fatherid")
    public Long getFatherid() {
        return fatherid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.fatherid")
    public void setFatherid(Long fatherid) {
        this.fatherid = fatherid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.lon")
    public BigDecimal getLon() {
        return lon;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.lon")
    public void setLon(BigDecimal lon) {
        this.lon = lon;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.lat")
    public BigDecimal getLat() {
        return lat;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.lat")
    public void setLat(BigDecimal lat) {
        this.lat = lat;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", areaid=").append(areaid);
        sb.append(", area=").append(area);
        sb.append(", fatherid=").append(fatherid);
        sb.append(", lon=").append(lon);
        sb.append(", lat=").append(lat);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}