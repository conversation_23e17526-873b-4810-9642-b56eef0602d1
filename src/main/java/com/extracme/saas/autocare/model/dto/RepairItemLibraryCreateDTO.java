package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "维修项目创建DTO")
public class RepairItemLibraryCreateDTO {

    @ApiModelProperty(value = "项目名称",example = "维修项目名称")
    @NotBlank(message = "项目名称不能为空")
    private String itemName;

    @ApiModelProperty(value = "项目类型 1：保养 2：终端 3：维修",example = "1")
    @NotNull(message = "项目类型不能为空")
    private Integer itemType;

    @ApiModelProperty(value = "工时费全国市场价",example = "10.00")
    @NotNull(message = "工时费全国市场价不能为空")
    private BigDecimal hourFeeNationalMarketPrice;

    @ApiModelProperty(value = "工时费全国上限")
    private BigDecimal hourFeeNationalHighestPrice;

    @ApiModelProperty(value = "材料费全国市场价",example = "10.00")
    @NotNull(message = "材料费全国市场价不能为空")
    private BigDecimal materialCostNationalMarketPrice;

    @ApiModelProperty(value = "材料费全国上限")
    private BigDecimal materialCostNationalHighestPrice;

    @ApiModelProperty(value = "备注",example = "备注")
    private String remark;

    /**
     * 车型ID
     */
    @ApiModelProperty(value = "车型ID",example = "1")
    @NotNull(message = "车型不能为空")
    private Long vehicleModelSeq;

    /**
     * 保养周期
     */
    @ApiModelProperty(value = "保养周期(月)",example = "1")
    private Integer maintenanceCycle;

    /**
     * 保养里程
     */
    @ApiModelProperty(value = "保养里程(KM)",example = "1")
    private String maintenanceMileage;

    @ApiModelProperty(value = "是否可编辑  0：否 1：是",example = "1")
    private Integer editable;
}