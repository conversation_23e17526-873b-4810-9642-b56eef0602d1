package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 审批层级更新DTO
 */
@Data
@ApiModel(description = "审批层级更新DTO")
public class ApprovalLevelsUpdateDTO {

    @ApiModelProperty(value = "审批层级ID", required = true, example = "1")
    @NotNull(message = "审批层级ID不能为空")
    private Long id;

    @ApiModelProperty(value = "审批层级（0：无，1：一级，2：二级，3：三级，4：四级，5：五级）", required = true, example = "1")
    @NotNull(message = "审批层级不能为空")
    @Min(value = 0, message = "审批层级最小值为0")
    @Max(value = 5, message = "审批层级最大值为5")
    private Integer approvalLevel;

    @ApiModelProperty(value = "自主审批金额", required = true, example = "10000.00")
    @NotNull(message = "自主审批金额不能为空")
    @DecimalMin(value = "0.00", message = "自主审批金额不能小于0")
    private BigDecimal selfApprovalAmount;
}
