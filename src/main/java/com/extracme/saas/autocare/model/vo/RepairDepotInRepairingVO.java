package com.extracme.saas.autocare.model.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 修理厂在修车辆明细视图对象
 */
@Data
@ApiModel(description = "修理厂在修车辆明细视图对象")
public class RepairDepotInRepairingVO {

    @ApiModelProperty(value = "车辆运营单位", example = "上海环球车享汽车租赁有限公司")
    private String orgName;

    @ApiModelProperty(value = "任务编号", example = "MTC202301010001")
    private String taskNo;

    @ApiModelProperty(value = "车牌号", example = "沪A88888")
    private String vehicleNo;

    @ApiModelProperty(value = "车型", example = "荣威Ei5")
    private String vehicleModelInfo;

    @ApiModelProperty(value = "车架号", example = "LSV1234567890ABCD")
    private String vin;

    @ApiModelProperty(value = "车辆保险所属", example = "中国平安")
    private String insuranceCompanyName;

    @ApiModelProperty(value = "修理类型", example = "事故维修")
    private String repairTypeName;

    @ApiModelProperty(value = "修理级别", example = "A")
    private String repairGrade;

    @ApiModelProperty(value = "车辆接收时间", example = "2023-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date vehicleReciveTime;

    @ApiModelProperty(value = "预计修理完成时间", example = "2023-01-03 18:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectedRepairComplete;

    @ApiModelProperty(value = "车辆业务状态", example = "1", notes = "0：分时租赁 1：长租 3：短租 4：公务用车")
    private Integer renttype;

}