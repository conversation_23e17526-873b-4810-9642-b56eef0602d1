package com.extracme.saas.autocare.model.vo.workflow;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 任务进度视图对象
 * 用于返回根据任务编号查询的完整进度信息
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("任务进度视图")
public class TaskProgressVO {

    /**
     * 任务编号（业务ID）
     */
    @ApiModelProperty("任务编号")
    private String businessId;

    /**
     * 工作流实例ID
     */
    @ApiModelProperty("工作流实例ID")
    private Long instanceId;

    /**
     * 工作流模板ID
     */
    @ApiModelProperty("工作流模板ID")
    private Long workflowId;

    /**
     * 工作流模板名称
     */
    @ApiModelProperty("工作流模板名称")
    private String workflowName;

    /**
     * 当前活动节点编码
     */
    @ApiModelProperty("当前活动节点编码")
    private String currentActivityCode;

    /**
     * 当前活动节点名称
     */
    @ApiModelProperty("当前活动节点名称")
    private String currentActivityName;

    /**
     * 流程状态
     */
    @ApiModelProperty("流程状态")
    private String status;

    /**
     * 流程状态名称
     */
    @ApiModelProperty("流程状态名称")
    private String statusName;

    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private Integer tenantId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 流程模板的所有活动节点列表
     */
    @ApiModelProperty("流程模板活动节点列表")
    private List<ActivityDefinitionVO> activityDefinitions;

    /**
     * 已执行的活动实例详情列表
     */
    @ApiModelProperty("已执行活动实例列表")
    private List<ActivityInstanceVO> activityInstances;
}
