package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 租户更新DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "租户更新DTO")
public class TenantUpdateDTO extends TenantDTO {
    
    @ApiModelProperty(value = "租户ID", required = true, example = "1")
    @NotNull(message = "租户ID不能为空")
    private Long id;
}
