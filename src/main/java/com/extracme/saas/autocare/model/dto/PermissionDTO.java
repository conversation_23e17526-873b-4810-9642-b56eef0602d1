package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 权限创建/更新DTO
 */
@Data
@ApiModel(description = "权限创建/更新DTO")
public class PermissionDTO {

    @ApiModelProperty(value = "权限ID（更新时必填）", example = "1")
    private Long id;

    @ApiModelProperty(value = "权限名称", required = true, example = "用户管理")
    @NotBlank(message = "权限名称不能为空")
    private String permissionName;

    @ApiModelProperty(value = "权限编码", example = "5a8d3e2f1b7c9a6d4f8e2c1b7a", notes = "创建时自动生成UUID，更新时必填")
    private String permissionCode;

    @ApiModelProperty(value = "权限类型", required = true, example = "menu", notes = "menu-菜单，button-按钮，hiddenMenu-隐藏菜单")
    @NotBlank(message = "权限类型不能为空")
    private String permissionType;

    @ApiModelProperty(value = "父级权限ID", example = "0")
    private Long parentId;

    @ApiModelProperty(value = "路由路径", example = "/system/user")
    private String path;

    @ApiModelProperty(value = "组件路径", example = "system/user/index")
    private String component;

    @ApiModelProperty(value = "图标", example = "el-icon-user")
    private String icon;

    @ApiModelProperty(value = "排序", example = "1")
    private Integer sort;

    @ApiModelProperty(value = "状态：0-禁用，1-启用", example = "1", notes = "默认为1-启用")
    private Integer status = 1;
}
