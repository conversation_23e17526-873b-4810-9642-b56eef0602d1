package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 分片ETag信息
 */
@Data
@ApiModel(description = "分片ETag信息")
public class PartETagInfo {

    @ApiModelProperty(value = "分片编号（从1开始）", required = true, example = "1")
    @NotNull(message = "分片编号不能为空")
    @Min(value = 1, message = "分片编号必须从1开始")
    private Integer partNumber;

    @ApiModelProperty(value = "分片ETag", required = true)
    @NotBlank(message = "分片ETag不能为空")
    private String eTag;

    @ApiModelProperty(value = "分片大小（字节）", example = "5242880")
    private Long partSize;

    public PartETagInfo() {
    }

    public PartETagInfo(Integer partNumber, String eTag) {
        this.partNumber = partNumber;
        this.eTag = eTag;
    }

    public PartETagInfo(Integer partNumber, String eTag, Long partSize) {
        this.partNumber = partNumber;
        this.eTag = eTag;
        this.partSize = partSize;
    }
}
