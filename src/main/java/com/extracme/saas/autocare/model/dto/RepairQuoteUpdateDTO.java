package com.extracme.saas.autocare.model.dto;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 维修报价更新DTO
 */
@Data
@ApiModel(description = "维修报价更新DTO")
public class RepairQuoteUpdateDTO {
    
    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", example = "REPAIR20240601001")
    @Size(max = 50, message = "任务编号长度不能超过50个字符")
    private String taskNo;
    
    /**
     * 预计修理天数
     */
    @ApiModelProperty(value = "预计修理天数", example = "3")
    @Min(value = 1, message = "预计修理天数必须大于0")
    private Long expectedRepairDays;

    /**
     * 定损单金额
     */
    @ApiModelProperty(value = "定损单金额", example = "2000.00")
    @DecimalMin(value = "0", message = "定损单金额不能小于0")
    @Digits(integer = 10, fraction = 2, message = "定损单金额格式不正确，整数位最多10位，小数位最多2位")
    private BigDecimal lossOrderAmount;
    
    /**
     * 损坏部位图片
     */
    @ApiModelProperty(value = "损坏部位图片", notes = "图片URL列表，最多48张")
    private List<FileDTO> damagedPartPicture;
    
    /**
     * 损坏部位视频
     */
    @ApiModelProperty(value = "损坏部位视频", notes = "视频URL列表")
    private List<FileDTO> damagedPartVideo;
    
    /**
     * 事故责任认定书图片
     */
    @ApiModelProperty(value = "事故责任认定书图片", notes = "图片URL列表，最多4张")
    private List<FileDTO> accidentLiabilityConfirmationPicture;
    
    /**
     * 保司定损单图片
     */
    @ApiModelProperty(value = "保司定损单图片", notes = "图片URL列表，最多4张")
    private List<FileDTO> insuranceCompanyLossOrderPicture;
    
    /**
     * 我方驾驶证图片
     */
    @ApiModelProperty(value = "我方驾驶证图片", notes = "图片URL列表，最多4张")
    private List<FileDTO> ourDriverLicensePicture;
    
    /**
     * 客户直付标识（0:否, 1:是）
     */
    @ApiModelProperty(value = "是否客户直付 0-否 1-是", example = "1")
    @Min(value = 0, message = "是否客户直付只能是0或1")
    @Max(value = 1, message = "是否客户直付只能是0或1")
    private Integer custPaysDirect;
    
    /**
     * 客户直付金额
     */
    @ApiModelProperty(value = "客户直付金额", example = "500.00")
    @DecimalMin(value = "0", message = "客户直付金额不能小于0")
    @Digits(integer = 10, fraction = 2, message = "客户直付金额格式不正确，整数位最多10位，小数位最多2位")
    private BigDecimal custAmount;
    
    /**
     * 客户直付凭证
     */
    @ApiModelProperty(value = "客户直付凭证", notes = "图片URL列表")
    private List<FileDTO> custPicture;

    @ApiModelProperty(value = "用户承担金额", example = "300.00")
    private BigDecimal userAssumedAmount;

    @ApiModelProperty(value = "非用户承担金额", example = "300.00")
    private BigDecimal notUserAssumedAmount;

    /**
     * 预估理赔金额
     */
    @ApiModelProperty(value = "预估理赔金额", example = "2000.00")
    @DecimalMin(value = "0", message = "预估保险理赔价格不能小于0")
    @Digits(integer = 10, fraction = 2, message = "预估理赔金额格式不正确，整数位最多10位，小数位最多2位")
    private BigDecimal estimatedClaimAmount;
    
    /**
     * 检查图片数量是否超出范围
     * @return 是否超出范围
     */
    @ApiModelProperty(hidden = true)
    public boolean pictureSizeOutOfRange() {
        if (CollectionUtils.isNotEmpty(damagedPartPicture) && damagedPartPicture.size() > 48) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(accidentLiabilityConfirmationPicture) && accidentLiabilityConfirmationPicture.size() > 4) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(insuranceCompanyLossOrderPicture) && insuranceCompanyLossOrderPicture.size() > 4) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(ourDriverLicensePicture) && ourDriverLicensePicture.size() > 4) {
            return true;
        }
        return false;
    }
}
