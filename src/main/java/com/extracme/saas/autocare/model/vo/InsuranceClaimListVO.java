package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "保险索赔用印申请信息对象")
public class InsuranceClaimListVO {

    @ApiModelProperty(value = "保险索赔用印申请ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "申请单据号")
    private String documentNumber;

    @ApiModelProperty(value = "车牌号", example = "A12345")
    private String vin;

    @ApiModelProperty(value = "车牌号", example = "粤A12345")
    private String licensePlate;

    @ApiModelProperty(value = "登记人", example = "张三")
    private String registrant;

    @ApiModelProperty(value = "所属保司名称", example = "中国平安保险")
    private String insuranceCompany;

    @ApiModelProperty(value = "公司简称")
    private String companyAbbreviation;

    @ApiModelProperty(value = "登记时间", example = "2023-01-15")
    private String registrationDate;

    @ApiModelProperty(value = "出险日志", example = "2023-01-15")
    private String accidentDate;

    @ApiModelProperty(value = "车辆出险时所属公司", example = "XX运输公司")
    private String propertyOrgName;

    @ApiModelProperty(value = "车辆出险时运营公司", example = "YY网约车平台")
    private String operationOrgName;

    @ApiModelProperty(value = "单据状态(1-待处理 2-处理中 3-已完成 4-已关闭)")
    private Integer documentStatus;
}