package com.extracme.saas.autocare.model.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 工作流模板状态更新请求对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("工作流模板状态更新请求")
public class WorkflowTemplateStatusDTO {

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空")
    @ApiModelProperty(value = "模板ID", required = true, example = "1")
    private Long templateId;

    /**
     * 是否启用
     * 1: 启用
     * 0: 禁用
     */
    @NotNull(message = "启用状态不能为空")
    @ApiModelProperty(value = "是否启用", required = true, example = "1", notes = "1:启用, 0:禁用")
    private Integer isActive;
} 