package com.extracme.saas.autocare.model.jingyou;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:09
 */
public class BodyBeanAssBack {
    /**
     * 定损信息
     */
    private EvalLossInfoAssBack evalLossInfoAssBack;
    /**
     * 车辆信息
     */
    private VehicleInfoAssBack vehicleInfoAssBack;
    /**
     * 损失部位信息
     */
    private List<CollisionPartsAssBack> collisionPartsAssBackList;
    /**
     * 定损换件信息
     */
    private List<LossFitInfoAssBack> lossFitInfoAssBackList;
    /**
     * 定损修理信息
     */
    private List<LossRepairInfoAssBack> lossRepairInfoAssBackList;
    /**
     * 定损外修信息
     */
    private List<LossOuterRepairInfoAssBack> lossOuterRepairInfoAssBackList;
    /**
     * 定损修理合计信息
     */
    private List<LossRepairSumInfoAssBack> lossRepairSumInfoAssBackList;
    /**
     * 定损辅料信息
     */
    private List<LossAssistInfoAssBack> lossAssistInfoAssBackList;

    @XmlElement(name = "EvalLossInfo")
    public EvalLossInfoAssBack getEvalLossInfoAssBack() {
        return evalLossInfoAssBack;
    }

    public void setEvalLossInfoAssBack(EvalLossInfoAssBack evalLossInfoAssBack) {
        this.evalLossInfoAssBack = evalLossInfoAssBack;
    }

    @XmlElement(name = "VehicleInfo")
    public VehicleInfoAssBack getVehicleInfoAssBack() {
        return vehicleInfoAssBack;
    }

    public void setVehicleInfoAssBack(VehicleInfoAssBack vehicleInfoAssBack) {
        this.vehicleInfoAssBack = vehicleInfoAssBack;
    }

    @XmlElementWrapper(name = "CollisionPartsList")
    @XmlElement(name = "CollisionParts")
    public List<CollisionPartsAssBack> getCollisionPartsAssBackList() {
        return collisionPartsAssBackList;
    }

    public void setCollisionPartsAssBackList(List<CollisionPartsAssBack> collisionPartsAssBackList) {
        this.collisionPartsAssBackList = collisionPartsAssBackList;
    }

    @XmlElementWrapper(name = "LossFitInfo")
    @XmlElement(name = "Item")
    public List<LossFitInfoAssBack> getLossFitInfoAssBackList() {
        return lossFitInfoAssBackList;
    }

    public void setLossFitInfoAssBackList(List<LossFitInfoAssBack> lossFitInfoAssBackList) {
        this.lossFitInfoAssBackList = lossFitInfoAssBackList;
    }

    @XmlElementWrapper(name = "LossRepairInfo")
    @XmlElement(name = "Item")
    public List<LossRepairInfoAssBack> getLossRepairInfoAssBackList() {
        return lossRepairInfoAssBackList;
    }

    public void setLossRepairInfoAssBackList(List<LossRepairInfoAssBack> lossRepairInfoAssBackList) {
        this.lossRepairInfoAssBackList = lossRepairInfoAssBackList;
    }

    @XmlElementWrapper(name = "LossOuterRepairInfo")
    @XmlElement(name = "Item")
    public List<LossOuterRepairInfoAssBack> getLossOuterRepairInfoAssBackList() {
        return lossOuterRepairInfoAssBackList;
    }

    public void setLossOuterRepairInfoAssBackList(List<LossOuterRepairInfoAssBack> lossOuterRepairInfoAssBackList) {
        this.lossOuterRepairInfoAssBackList = lossOuterRepairInfoAssBackList;
    }

    @XmlElementWrapper(name = "LossRepairSumInfo")
    @XmlElement(name = "Item")
    public List<LossRepairSumInfoAssBack> getLossRepairSumInfoAssBackList() {
        return lossRepairSumInfoAssBackList;
    }

    public void setLossRepairSumInfoAssBackList(List<LossRepairSumInfoAssBack> lossRepairSumInfoAssBackList) {
        this.lossRepairSumInfoAssBackList = lossRepairSumInfoAssBackList;
    }

    @XmlElementWrapper(name = "LossAssistInfo")
    @XmlElement(name = "Item")
    public List<LossAssistInfoAssBack> getLossAssistInfoAssBackList() {
        return lossAssistInfoAssBackList;
    }

    public void setLossAssistInfoAssBackList(List<LossAssistInfoAssBack> lossAssistInfoAssBackList) {
        this.lossAssistInfoAssBackList = lossAssistInfoAssBackList;
    }
}
