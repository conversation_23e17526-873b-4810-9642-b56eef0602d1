package com.extracme.saas.autocare.model.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "维修厂修改DTO")
public class RepairDepotUpdateDTO {

    @ApiModelProperty(value = "维修厂ID不能为空" ,required = true)
    @NotNull(message = "维修厂ID不能为空")
    private Long id;

    @ApiModelProperty(value = "修理厂名称", example = "修理厂A", required = true)
    @NotBlank(message = "修理厂名称不能为空")
    private String repairDepotName;

    @ApiModelProperty(value = "是否保养点(0-否 1-是)", example = "1", required = true)
//    @NotNull(message = "是否保养点不能为空")
    private Integer maintenancePoint;

    @ApiModelProperty(value = "修理厂等级", example = "A", required = true)
    @NotBlank(message = "修理厂等级不能为空")
    private String repairDepotGrade;

    @ApiModelProperty(value = "主要合作子公司组织机构ID", example = "ORG12345", required = true)
//    @NotBlank(message = "主要合作子公司组织机构不能为空")
    private String mainOrgId;

    @ApiModelProperty(value = "其他组织机构", example = "ORG12346")
//    @NotEmpty(message = "其他组织机构不能为空")
    private List<String> otherOrgIdList;

    @ApiModelProperty(value = "修理厂地址（省）ID", example = "PROV12345", required = true)
    @NotNull(message = "修理厂地址（省）不能为空")
    private Long provinceId;

    @ApiModelProperty(value = "修理厂地址（市）ID", example = "CITY12345", required = true)
    @NotNull(message = "修理厂地址（市）不能为空")
    private Long cityId;

    @ApiModelProperty(value = "修理厂地址（区）ID", example = "AREA12345", required = true)
    @NotNull(message = "修理厂地址（区）不能为空")
    private Long areaId;

    @ApiModelProperty(value = "修理厂详细地址", example = "详细地址A", required = true)
    @NotBlank(message = "修理厂详细地址不能为空")
    private String address;

    @ApiModelProperty(value = "事故联系人", example = "李四")
    @Size(max = 20, message = "事故联系人的输入长度不能超过20位")
    private String accidentContacts;

    @ApiModelProperty(value = "事故联系电话", example = "12345678901")
    @Size(max = 50, message = "事故联系电话的输入长度不能超过50位")
    private String accidentTel;

    @ApiModelProperty(value = "维保联系人", example = "王五")
    @Size(max = 20, message = "维保联系人的输入长度不能超过20位")
    private String maintenanceContacts;

    @ApiModelProperty(value = "维保联系电话", example = "12345678902")
    @Size(max = 50, message = "维保联系电话的输入长度不能超过50位")
    private String maintenanceTel;

    @ApiModelProperty(value = "修理厂经度", example = "120.123456", required = true)
//    @NotBlank(message = "修理厂经度不能为空")
    private String repairDepotLongitude;

    @ApiModelProperty(value = "修理厂纬度", example = "30.123456", required = true)
//    @NotBlank(message = "修理厂纬度不能为空")
    private String repairDepotLatitude;

    @ApiModelProperty(value = "状态（0-无效 1-有效）", example = "1", required = true)
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "负责人", example = "张三", required = true)
    @Size(max = 20, message = "负责人的输入长度不能超过20位")
//    @NotBlank(message = "负责人不能为空")
    private String linkmanName;

    @ApiModelProperty(value = "可修类目(0-全部 1-外观 2-易损件)", example = "0", required = true)
//    @NotNull(message = "可修类目不能为空")
    private int canRepairItem;

    @ApiModelProperty(value = "营业时间类型(0-全天 1-其他)", example = "1", required = true)
    @NotNull(message = "营业时间类型不能为空")
    private Integer businessType;

    @ApiModelProperty(value = "营业开始时间", example = "09:00", required = true)
    private String businessStartTime;

    @ApiModelProperty(value = "营业结束时间", example = "18:00", required = true)
    private String businessEndTime;

    @ApiModelProperty(value = "是否保修点(0-否 1-是)", example = "1", required = true)
//    @NotNull(message = "是否保修点不能为空")
    private int warrantyPoint;

    @ApiModelProperty(value = "维修厂保修车型表")
    private List<Long> warrantyVehicleModelList;

    @ApiModelProperty(value = "维修厂修理车型车型表", example = "车型A,车型B")
//    @NotEmpty(message = "维修厂修理车型车型表不能为空")
    private List<Long> repairVehicleModelList;

    @ApiModelProperty(value = "合作模式(1-长租 2-分时 3-短租)", required = true)
//    @NotEmpty(message = "合作模式不能为空")
    private List<Integer> cooperationModeList;

    @ApiModelProperty(value = "车型是否全选标记 0:没有全选 1:全部选中", example = "1")
    private Integer vehicleModelAllFlag;

    @ApiModelProperty(value = "供应商编码-sap", example = "SAP12345", required = true)
    private String repairDepotSapCode;

    @ApiModelProperty(value = "供应商名", example = "供应商A", required = true)
    private String repairDepotSupplierName;

    @ApiModelProperty(value = "税率", example = "0.13", required = true)
//    @NotBlank(message = "税率不能为空")
    private String taxRate;

}