package com.extracme.saas.autocare.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

@Data
@ContentRowHeight(20)
@HeadRowHeight(25)
@ColumnWidth(15)
public class RepairTaskProcessExportExcel {

    /**
     * 任务编号
     */
    @ExcelProperty(value = "任务编号", index = 0)
    @ColumnWidth(20)
    private String taskNo;

    /**
     * 车牌号
     */
    @ExcelProperty(value = "车牌号", index = 1)
    @ColumnWidth(20)
    private String vehicleNo;

    /**
     * 车架号
     */
    @ExcelProperty(value = "车架号", index = 2)
    @ColumnWidth(20)
    private String vin;

    /**
     * 当前活动名
     */
    @ExcelProperty(value = "当前环节", index = 3)
    @ColumnWidth(20)
    private String currentActivityName;

    /**
     * 当前状态名
     */
    @ExcelProperty(value = "当前状态", index = 4)
    @ColumnWidth(20)
    private String statusName;

    /**
     * 修理厂类型
     */
    @ExcelProperty(value = "修理厂类型", index = 5)
    @ColumnWidth(20)
    private String repairDepotTypeString;

    /**
     * 修理类型
     */
    @ExcelProperty(value = "修理类型", index = 6)
    @ColumnWidth(20)
    private String repairTypeName;

    /**
     * 修理厂名称
     */
    @ExcelProperty(value = "修理厂名称", index = 7)
    @ColumnWidth(20)
    private String repairDepotName;

    /**
     * 车型
     */
    @ExcelProperty(value = "车型", index = 8)
    @ColumnWidth(20)
    private String vehicleModelInfo;

    /**
     * 任务所属公司
     */
    @ExcelProperty(value = "任务所属公司", index = 9)
    @ColumnWidth(20)
    private String orgName;

    /**
     * 任务创建时间
     */
    @ExcelProperty(value = "任务创建时间", index = 10)
    @ColumnWidth(20)
    private String taskCreateTime;
}
