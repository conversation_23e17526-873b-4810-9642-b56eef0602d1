package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 登录请求DTO
 */
@ApiModel(value = "登录参数", description = "用户登录所需的参数对象")
@Data
public class LoginDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", required = true, example = "13800138000")
    private String mobile;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码", required = true, example = "123456")
    private String code;

    /**
     * 租户ID（可选，当用户有多个商户账号时需要指定）
     */
    @ApiModelProperty(value = "租户ID", required = false, example = "1")
    private Long tenantId;

    /**
     * 登录类型：SMS-短信登录
     */
    private String loginType;

}