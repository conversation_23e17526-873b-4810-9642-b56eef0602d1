package com.extracme.saas.autocare.model.dto.repairTask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "事故任务DTO")
public class AccidentTaskDTO {
    
    @ApiModelProperty(value = "事故编号")
    private String accidentNo;
    
    @ApiModelProperty(value = "保司报案号")
    private String insuranceCaseNo;
    
    @ApiModelProperty(value = "事故状态", example = "1", notes = "1=待定损 2=待理赔 3=结案审批中 4=已结案")
    private Integer accidentStatus;
    
    @ApiModelProperty(value = "出险日期", example = "2023-01-01")
    private String accidentDate;
    
    @ApiModelProperty(value = "事故简述")
    private String simpleAccidentDesc;
    
    @ApiModelProperty(value = "是否关联维修任务", example = "true")
    private Boolean hasRepairTask;
}
