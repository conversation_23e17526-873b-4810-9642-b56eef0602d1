package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-13 10:34
 */
public class FactoryInfo {
    /**
     * 修理厂ID
     */
    private String factoryId= StringUtils.EMPTY;
    /**
     * 机构编码
     */
    private String comCode= StringUtils.EMPTY;
    /**
     * 机构名称
     */
    private String comName= StringUtils.EMPTY;
    /**
     * 修理厂所在省编码
     */
    private String provinceCode= StringUtils.EMPTY;
    /**
     * 修理厂所在省名称
     */
    private String provinceName= StringUtils.EMPTY;
    /**
     * 修理厂所在市编码
     */
    private String cityCode= StringUtils.EMPTY;
    /**
     * 修理厂所在市名称
     */
    private String cityName= StringUtils.EMPTY;
    /**
     * 修理厂编码
     */
    private String factoryCode= StringUtils.EMPTY;
    /**
     * 修理厂名称
     */
    private String factoryName= StringUtils.EMPTY;
    /**
     * 修理厂资质（1：一类 2：二类 3：三类）
     */
    private String factoryQualification= StringUtils.EMPTY;
    /**
     * 修理厂类型(0：非4S店 1：4S店)
     */
    private String factoryType= StringUtils.EMPTY;
    /**
     * 价格方案模式（1：标准模式 2：标记品牌模式 3：承修品牌模式 4：所有品牌模式 5：4S店全品牌模式）
     */
    private String priceSchemaMode= "1";
    /**
     * 换件折扣
     */
    private String fitsDiscountRate= "1";
    /**
     * 工时折扣
     */
    private String repairDiscountRate= "1";
    /**
     * 换件管理费率
     */
    private String fitsManageRate= "0";
    /**
     * 拆装工时单价
     */
    private String assemblyFacManHour= "100";
    /**
     * 喷漆工时单价
     */
    private String paintFacManHour= "100";
    /**
     * 修理工时单价
     */
    private String repairFacManHour= "100";

    @XmlElement(name = "FactoryId")
    public String getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(String factoryId) {
        this.factoryId = factoryId;
    }

    @XmlElement(name = "ComCode")
    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    @XmlElement(name = "ComName")
    public String getComName() {
        return comName;
    }

    public void setComName(String comName) {
        this.comName = comName;
    }

    @XmlElement(name = "ProvinceCode")
    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    @XmlElement(name = "ProvinceName")
    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    @XmlElement(name = "CityCode")
    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    @XmlElement(name = "CityName")
    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    @XmlElement(name = "FactoryCode")
    public String getFactoryCode() {
        return factoryCode;
    }

    public void setFactoryCode(String factoryCode) {
        this.factoryCode = factoryCode;
    }

    @XmlElement(name = "FactoryName")
    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    @XmlElement(name = "FactoryQualification")
    public String getFactoryQualification() {
        return factoryQualification;
    }

    public void setFactoryQualification(String factoryQualification) {
        this.factoryQualification = factoryQualification;
    }

    @XmlElement(name = "FactoryType")
    public String getFactoryType() {
        return factoryType;
    }

    public void setFactoryType(String factoryType) {
        this.factoryType = factoryType;
    }

    @XmlElement(name = "PriceSchemaMode")
    public String getPriceSchemaMode() {
        return priceSchemaMode;
    }

    public void setPriceSchemaMode(String priceSchemaMode) {
        this.priceSchemaMode = priceSchemaMode;
    }

    @XmlElement(name = "FitsDiscountRate")
    public String getFitsDiscountRate() {
        return fitsDiscountRate;
    }

    public void setFitsDiscountRate(String fitsDiscountRate) {
        this.fitsDiscountRate = fitsDiscountRate;
    }

    @XmlElement(name = "RepairDiscountRate")
    public String getRepairDiscountRate() {
        return repairDiscountRate;
    }

    public void setRepairDiscountRate(String repairDiscountRate) {
        this.repairDiscountRate = repairDiscountRate;
    }

    @XmlElement(name = "FitsManageRate")
    public String getFitsManageRate() {
        return fitsManageRate;
    }

    public void setFitsManageRate(String fitsManageRate) {
        this.fitsManageRate = fitsManageRate;
    }

    @XmlElement(name = "AssemblyFacManHour")
    public String getAssemblyFacManHour() {
        return assemblyFacManHour;
    }

    public void setAssemblyFacManHour(String assemblyFacManHour) {
        this.assemblyFacManHour = assemblyFacManHour;
    }

    @XmlElement(name = "PaintFacManHour")
    public String getPaintFacManHour() {
        return paintFacManHour;
    }

    public void setPaintFacManHour(String paintFacManHour) {
        this.paintFacManHour = paintFacManHour;
    }

    @XmlElement(name = "RepairFacManHour")
    public String getRepairFacManHour() {
        return repairFacManHour;
    }

    public void setRepairFacManHour(String repairFacManHour) {
        this.repairFacManHour = repairFacManHour;
    }
}
