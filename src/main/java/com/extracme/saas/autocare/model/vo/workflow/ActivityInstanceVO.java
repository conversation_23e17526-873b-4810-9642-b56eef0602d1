package com.extracme.saas.autocare.model.vo.workflow;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动实例视图对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("活动实例视图")
public class ActivityInstanceVO {

    /**
     * 活动实例记录ID
     */
    @ApiModelProperty("活动实例记录ID")
    private Long id;

    /**
     * 目标活动节点ID
     */
    @ApiModelProperty("目标活动节点ID")
    private Long toActivityId;

    /**
     * 目标活动节点名称
     */
    @ApiModelProperty("目标活动节点名称")
    private String toActivityName;

    /**
     * 来源节点ID
     */
    @ApiModelProperty("来源节点ID")
    private Long fromActivityId;

    /**
     * 来源节点名称
     */
    @ApiModelProperty("来源节点名称")
    private String fromActivityName;

    /**
     * 触发转换的规则ID
     */
    @ApiModelProperty("触发转换的规则ID")
    private Long transitionId;

    /**
     * 活动开始时间
     */
    @ApiModelProperty("活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty("活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /**
     * 活动总耗时（秒）
     */
    @ApiModelProperty("活动总耗时（秒）")
    private Integer duration;

    /**
     * 当前状态ID
     */
    @ApiModelProperty("当前状态ID")
    private Long currentStatusId;

    /**
     * 当前状态名称
     */
    @ApiModelProperty("当前状态名称")
    private String currentStatusName;

    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String operator;

    /**
     * 备注说明
     */
    @ApiModelProperty("备注说明")
    private String remarks;

    /**
     * 活动节点转换规则信息
     */
    @ApiModelProperty("活动节点转换规则信息")
    private ActivityTransitionVO activityTransition;


} 