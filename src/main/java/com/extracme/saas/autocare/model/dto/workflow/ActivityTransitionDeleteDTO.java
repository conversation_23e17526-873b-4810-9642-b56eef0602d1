package com.extracme.saas.autocare.model.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 活动节点转换规则删除DTO
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Data
@ApiModel(description = "活动节点转换规则删除DTO")
public class ActivityTransitionDeleteDTO {

    @ApiModelProperty(value = "活动节点转换规则编号", required = true, example = "1")
    @NotNull(message = "活动节点转换规则编号不能为空")
    private Long id;

    @ApiModelProperty(value = "是否级联删除关联的状态转换规则", example = "true")
    private Boolean cascadeDelete = true;
}
