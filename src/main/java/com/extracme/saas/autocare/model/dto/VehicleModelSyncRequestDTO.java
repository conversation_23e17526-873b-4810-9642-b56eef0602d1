package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 车型信息同步请求DTO
 */
@Data
@ApiModel(description = "车型信息同步请求DTO")
public class VehicleModelSyncRequestDTO {

    @ApiModelProperty(value = "同步密钥（用于租户身份识别）", required = true)
    @NotNull(message = "同步密钥不能为空")
    private String syncKey;

    @ApiModelProperty(value = "批量同步数据列表", required = true)
    @NotEmpty(message = "同步数据列表不能为空")
    @Valid
    private List<VehicleModelSyncDataDTO> batchData;

    /**
     * 车型信息同步数据DTO
     */
    @Data
    @ApiModel(description = "车型信息同步数据DTO")
    public static class VehicleModelSyncDataDTO {

        @ApiModelProperty(value = "主键ID（用于判断新增或更新）", required = false, example = "1")
        private Long id;

        @ApiModelProperty(value = "车型名称", required = true, example = "丰田卡罗拉")
        @NotBlank(message = "车型名称不能为空")
        private String vehicleModelName;
    }
}
