package com.extracme.saas.autocare.model.jingyou;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:08
 */
@XmlRootElement(name = "PACKET")
public class PacketBeanAssessment {
    private HeadBean headBean;
    private BodyBeanAssessment bodyBeanAssessment;

    @XmlElement(name = "HEAD")
    public HeadBean getHeadBean() {
        return headBean;
    }

    public void setHeadBean(HeadBean headBean) {
        this.headBean = headBean;
    }

    @XmlElement(name = "BODY")
    public BodyBeanAssessment getBodyBeanAssessment() {
        return bodyBeanAssessment;
    }

    public void setBodyBeanAssessment(BodyBeanAssessment bodyBeanAssessment) {
        this.bodyBeanAssessment = bodyBeanAssessment;
    }
}
