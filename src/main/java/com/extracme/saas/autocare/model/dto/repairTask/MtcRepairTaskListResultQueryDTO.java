package com.extracme.saas.autocare.model.dto.repairTask;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 维修任务流程查询一览入参
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "维修任务流程查询DTO")
public class MtcRepairTaskListResultQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "车辆运营单位")
    private String orgId;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "流程模板id")
    private Long workflowId;

    @ApiModelProperty(value = "修理类型")
    private Integer repairTypeId;

    @ApiModelProperty(value = "当前环节")
    private String activityCode;

    @ApiModelProperty(value = "维修厂机构id")
    private String repairDepotOrgId;

    @ApiModelProperty(value = "任务创建开始时间")
    private Date taskInflowStartTime;

    @ApiModelProperty(value = "任务创建结束时间")
    private Date taskInflowEndTime;

    @ApiModelProperty(value = "车辆接收开始时间")
    private Date vehicleReciveStartTime;

    @ApiModelProperty(value = "车辆接收结束时间")
    private Date vehicleReciveEndTime;

    @ApiModelProperty(value = "车型id")
    private Long vehicleModelId;

    @ApiModelProperty(value = "业务状态")
    private Integer renttype;

    @ApiModelProperty(value = "实际运营标签")
    private Integer factOperateTag;

    @ApiModelProperty(value = "事故编号")
    private String accidentNo;
}