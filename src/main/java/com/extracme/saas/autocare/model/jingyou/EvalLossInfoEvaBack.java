package com.extracme.saas.autocare.model.jingyou;


import javax.xml.bind.annotation.XmlElement;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-20 14:44
 */
public class EvalLossInfoEvaBack {
    /**
     * 定损单号
     */
    private String lossNo = StringUtils.EMPTY;
    /**
     * 报案号
     */
    private String reportCode = StringUtils.EMPTY;
    /**
     * 车损标的主键
     */
    private String dmgVhclId = StringUtils.EMPTY;
    /**
     * 施救费用
     */
    private BigDecimal auditSalvageFee = BigDecimal.ZERO;
    /**
     * 折扣残值
     */
    private BigDecimal auditRemnantFee = BigDecimal.ZERO;
    /**
     * 核损换件合计
     */
    private BigDecimal auditPartSum = BigDecimal.ZERO;
    /**
     * 核损工时合计
     */
    private BigDecimal auditRepiarSum = BigDecimal.ZERO;
    /**
     * 核损辅料合计
     */
    private BigDecimal auditMateSum = BigDecimal.ZERO;
    /**
     * 核损管理费合计
     */
    private BigDecimal totalManageSum = BigDecimal.ZERO;
    /**
     * 自付合计
     */
    private BigDecimal selfPaySum = BigDecimal.ZERO;
    /**
     * 外修合计
     */
    private BigDecimal outerSum = BigDecimal.ZERO;
    /**
     * 减损合计
     */
    private BigDecimal derogationSum = BigDecimal.ZERO;
    /**
     * 核损员代码
     */
    private String handlerCode = StringUtils.EMPTY;
    /**
     * 备注
     */
    private String remark = StringUtils.EMPTY;
    /**
     * 核损整单合计
     */
    private BigDecimal totalSum = BigDecimal.ZERO;
    /**
     * 全损整车损失金额
     */
    private BigDecimal allLoseSum = BigDecimal.ZERO;
    /**
     * 全损整车残值金额
     */
    private BigDecimal allLoseRemainsSum = BigDecimal.ZERO;
    /**
     * 全损整车施救费金额
     */
    private BigDecimal allLoseSalvSum = BigDecimal.ZERO;
    /**
     * 全损合计金额
     */
    private BigDecimal allLoseTotalSum = BigDecimal.ZERO;

    @XmlElement(name = "LossNo")
    public String getLossNo() {
        return lossNo;
    }

    public void setLossNo(String lossNo) {
        this.lossNo = lossNo;
    }

    @XmlElement(name = "ReportCode")
    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }

    @XmlElement(name = "DmgVhclId")
    public String getDmgVhclId() {
        return dmgVhclId;
    }

    public void setDmgVhclId(String dmgVhclId) {
        this.dmgVhclId = dmgVhclId;
    }

    @XmlElement(name = "AuditSalvageFee")
    public BigDecimal getAuditSalvageFee() {
        return auditSalvageFee;
    }

    public void setAuditSalvageFee(BigDecimal auditSalvageFee) {
        this.auditSalvageFee = auditSalvageFee;
    }

    @XmlElement(name = "AuditRemnantFee")
    public BigDecimal getAuditRemnantFee() {
        return auditRemnantFee;
    }

    public void setAuditRemnantFee(BigDecimal auditRemnantFee) {
        this.auditRemnantFee = auditRemnantFee;
    }

    @XmlElement(name = "AuditPartSum")
    public BigDecimal getAuditPartSum() {
        return auditPartSum;
    }

    public void setAuditPartSum(BigDecimal auditPartSum) {
        this.auditPartSum = auditPartSum;
    }

    @XmlElement(name = "AuditRepiarSum")
    public BigDecimal getAuditRepiarSum() {
        return auditRepiarSum;
    }

    public void setAuditRepiarSum(BigDecimal auditRepiarSum) {
        this.auditRepiarSum = auditRepiarSum;
    }

    @XmlElement(name = "AuditMateSum")
    public BigDecimal getAuditMateSum() {
        return auditMateSum;
    }

    public void setAuditMateSum(BigDecimal auditMateSum) {
        this.auditMateSum = auditMateSum;
    }

    @XmlElement(name = "TotalManageSum")
    public BigDecimal getTotalManageSum() {
        return totalManageSum;
    }

    public void setTotalManageSum(BigDecimal totalManageSum) {
        this.totalManageSum = totalManageSum;
    }

    @XmlElement(name = "SelfPaySum")
    public BigDecimal getSelfPaySum() {
        return selfPaySum;
    }

    public void setSelfPaySum(BigDecimal selfPaySum) {
        this.selfPaySum = selfPaySum;
    }

    @XmlElement(name = "OuterSum")
    public BigDecimal getOuterSum() {
        return outerSum;
    }

    public void setOuterSum(BigDecimal outerSum) {
        this.outerSum = outerSum;
    }

    @XmlElement(name = "DerogationSum")
    public BigDecimal getDerogationSum() {
        return derogationSum;
    }

    public void setDerogationSum(BigDecimal derogationSum) {
        this.derogationSum = derogationSum;
    }

    @XmlElement(name = "HandlerCode")
    public String getHandlerCode() {
        return handlerCode;
    }

    public void setHandlerCode(String handlerCode) {
        this.handlerCode = handlerCode;
    }

    @XmlElement(name = "Remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @XmlElement(name = "TotalSum")
    public BigDecimal getTotalSum() {
        return totalSum;
    }

    public void setTotalSum(BigDecimal totalSum) {
        this.totalSum = totalSum;
    }

    @XmlElement(name = "AllLoseSum")
    public BigDecimal getAllLoseSum() {
        return allLoseSum;
    }

    public void setAllLoseSum(BigDecimal allLoseSum) {
        this.allLoseSum = allLoseSum;
    }

    @XmlElement(name = "AllLoseRemainsSum")
    public BigDecimal getAllLoseRemainsSum() {
        return allLoseRemainsSum;
    }

    public void setAllLoseRemainsSum(BigDecimal allLoseRemainsSum) {
        this.allLoseRemainsSum = allLoseRemainsSum;
    }

    @XmlElement(name = "AllLoseSalvSum")
    public BigDecimal getAllLoseSalvSum() {
        return allLoseSalvSum;
    }

    public void setAllLoseSalvSum(BigDecimal allLoseSalvSum) {
        this.allLoseSalvSum = allLoseSalvSum;
    }

    @XmlElement(name = "AllLoseTotalSum")
    public BigDecimal getAllLoseTotalSum() {
        return allLoseTotalSum;
    }

    public void setAllLoseTotalSum(BigDecimal allLoseTotalSum) {
        this.allLoseTotalSum = allLoseTotalSum;
    }
}
