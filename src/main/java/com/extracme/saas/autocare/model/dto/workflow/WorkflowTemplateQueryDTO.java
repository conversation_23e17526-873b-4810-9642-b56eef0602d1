package com.extracme.saas.autocare.model.dto.workflow;

import com.extracme.saas.autocare.enums.TaskTypeEnum;
import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工作流模板查询请求对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("工作流模板查询请求")
public class WorkflowTemplateQueryDTO extends BasePageDTO {

    /**
     * 流程模板名称
     */
    @ApiModelProperty("流程模板名称")
    private String workflowName;

    /**
     * 任务类型：
     * 0 - 自建任务
     * 1 - 事故维修
     * 2 - 自费维修
     * 3 - 车辆保养
     * 4 - 轮胎任务
     * 5 - 自费维修(原车辆保养)
     * 6 - 常规保养
     * 7 - 终端维修
     * @see TaskTypeEnum
     */
    @ApiModelProperty(value = "任务类型", example = "1", notes = "0:自建任务, 1:事故维修, 2:自费维修, 3:车辆保养, 4:轮胎任务, 5:自费维修(原车辆保养), 6:常规保养, 7:终端维修")
    private Integer taskType;

    /**
     * 修理厂类型
     */
    @ApiModelProperty("修理厂类型")
    private Integer repairFactoryType;

    /**
     * 子产品线
     */
    @ApiModelProperty("子产品线")
    private Integer subProductLine;

    /**
     * 配件库类型
     * 1: 自有配件库
     * 2: 精友配件库
     */
    @ApiModelProperty(value = "配件库类型", example = "1", notes = "1:自有配件库, 2:精友配件库")
    private Integer partsLibraryType;

    /**
     * 是否启用
     * 1: 启用
     * 0: 禁用
     */
    @ApiModelProperty(value = "是否启用", example = "1", notes = "1:启用, 0:禁用")
    private Integer isActive;

    /**
     * 租户ID（用于多租户数据隔离）
     */
    @ApiModelProperty(value = "租户ID", example = "1", notes = "用于多租户数据隔离，普通用户自动设置为当前租户ID")
    private Integer tenantId;
}