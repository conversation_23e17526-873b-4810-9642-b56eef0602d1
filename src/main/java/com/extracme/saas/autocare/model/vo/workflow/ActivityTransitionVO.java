package com.extracme.saas.autocare.model.vo.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 活动节点转换规则视图对象
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Data
@ApiModel(description = "活动节点转换规则视图对象")
public class ActivityTransitionVO {

    @ApiModelProperty(value = "转换规则编号")
    private Long id;

    @ApiModelProperty(value = "所属流程模板编号")
    private Long workflowId;

    @ApiModelProperty(value = "转换起始节点编号")
    private String fromActivityCode;

    @ApiModelProperty(value = "目标节点编号")
    private String toActivityCode;

    @ApiModelProperty(value = "触发事件名称")
    private String triggerEvent;

    @ApiModelProperty(value = "条件判断处理器类名")
    private String conditionHandler;

    @ApiModelProperty(value = "处理逻辑类名")
    private String handlerClass;

    @ApiModelProperty(value = "租户编号")
    private Integer tenantId;

    @ApiModelProperty(value = "规则说明")
    private String description;

    @ApiModelProperty(value = "关联的状态转换规则列表")
    private List<ActivityStatusTransitionVO> statusTransitions;
}
