package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@ApiModel(description = "维修金额DTO")
public class RepairCustInfoDTO {
    
    @ApiModelProperty(value = "任务id")
    private Long id;
    
    @ApiModelProperty(value = "维修任务编号")
    private String taskNo;
    
    @ApiModelProperty(value = "是否客户直付 1-是 2-否")
    private Integer custPaysDirect;
    
    @ApiModelProperty(value = "客户直付金额")
    private BigDecimal custAmount;
    
    @ApiModelProperty(value = "用户承担金额")
    private BigDecimal userAssumedAmount;
    
    @ApiModelProperty(value = "非用户承担金额")
    private BigDecimal notUserAssumedAmount;
    
    @ApiModelProperty(value = "客户直付凭证")
    private List<FileDTO> custPicture;
}
