package com.extracme.saas.autocare.model.dto;

import lombok.Data;

/**
 * 租户识别结果DTO
 */
@Data
public class TenantIdentificationResultDTO {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 识别是否成功
     */
    private boolean success;

    /**
     * 错误信息（识别失败时）
     */
    private String errorMessage;

    /**
     * 创建成功的识别结果
     */
    public static TenantIdentificationResultDTO success(Long tenantId, String tenantCode) {
        TenantIdentificationResultDTO result = new TenantIdentificationResultDTO();
        result.setTenantId(tenantId);
        result.setTenantCode(tenantCode);
        result.setSuccess(true);
        return result;
    }

    /**
     * 创建失败的识别结果
     */
    public static TenantIdentificationResultDTO failure(String errorMessage) {
        TenantIdentificationResultDTO result = new TenantIdentificationResultDTO();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
