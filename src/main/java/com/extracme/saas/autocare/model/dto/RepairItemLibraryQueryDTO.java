package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "维修项目列表查询DTO")
public class RepairItemLibraryQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "项目编号")
    private String itemNo;

    @ApiModelProperty(value = "项目名称")
    private String itemName;

    @ApiModelProperty(value = "项目类型 1：保养 2：终端 3：维修")
    private Integer itemType;

    @ApiModelProperty(value = "车型ID")
    private Long vehicleModelSeq;

    @ApiModelProperty(value = "状态(0-无效 1-有效)")
    private Integer status;


} 