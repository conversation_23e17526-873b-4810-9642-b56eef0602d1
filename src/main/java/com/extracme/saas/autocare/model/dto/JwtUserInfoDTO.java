package com.extracme.saas.autocare.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * JWT Token中存储的用户信息DTO
 * 用于在JWT token中存储完整的用户信息，避免每次请求都查询数据库
 *
 * 优化说明：
 * 1. 使用短字段名减少token大小
 * 2. 移除非必要字段（创建时间、更新时间等）
 * 3. 使用时间戳而非格式化日期字符串
 * 4. 移除敏感信息（邮箱等）
 */
@Data
public class JwtUserInfoDTO {

    /**
     * 用户ID
     */
    @JsonProperty("uid")
    private Long userId;

    /**
     * 用户名
     */
    @JsonProperty("un")
    private String username;

    /**
     * 昵称
     */
    @JsonProperty("nn")
    private String nickname;

    /**
     * 手机号（完整存储）
     */
    @JsonProperty("mb")
    private String mobile;

    /**
     * 邮箱（移除，减少token大小和安全风险）
     */
    // private String email;

    /**
     * 账户类型（0-超级管理员，1-普通用户）
     */
    @JsonProperty("at")
    private Integer accountType;

    /**
     * 审批层级
     */
    @JsonProperty("al")
    private Integer approvalLevel;

    /**
     * 用户状态（0-正常，1-禁用）
     */
    @JsonProperty("st")
    private Integer status;

    /**
     * 关联修理厂ID
     */
    @JsonProperty("rd")
    private String repairDepotId;

    /**
     * 保险公司ID
     */
    @JsonProperty("ic")
    private Long insuranceCompanyId;

    // 权限编码列表、组织ID列表已移至Redis缓存中，减少JWT token大小
    // 这些信息将通过UserPermissionCacheUtils从Redis中获取

    /**
     * 租户ID
     */
    @JsonProperty("tid")
    private Long tenantId;

    /**
     * 租户编码
     */
    @JsonProperty("tc")
    private String tenantCode;

    /**
     * 租户名称
     */
    @JsonProperty("tn")
    private String tenantName;

    /**
     * 登录时间（时间戳，减少存储空间）
     */
    @JsonProperty("lt")
    private Long loginTime;

    /**
     * Token过期时间（时间戳，减少存储空间）
     */
    @JsonProperty("et")
    private Long expireTime;

    /**
     * 登录IP地址（可选，用于安全审计）
     */
    @JsonProperty("ip")
    private String ipaddr;

    // 移除创建时间和更新时间，减少token大小
    // 这些信息在token中不是必需的，可以在需要时从数据库查询

    /**
     * 获取登录时间的Date对象
     */
    @JsonIgnore
    public Date getLoginTimeAsDate() {
        return loginTime != null ? new Date(loginTime) : null;
    }

    /**
     * 设置登录时间
     */
    @JsonIgnore
    public void setLoginTimeFromDate(Date date) {
        this.loginTime = date != null ? date.getTime() : null;
    }

    /**
     * 获取过期时间的Date对象
     */
    @JsonIgnore
    public Date getExpireTimeAsDate() {
        return expireTime != null ? new Date(expireTime) : null;
    }

    /**
     * 设置过期时间
     */
    @JsonIgnore
    public void setExpireTimeFromDate(Date date) {
        this.expireTime = date != null ? date.getTime() : null;
    }


}
