package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "修理项目信息列表对象")
public class RepairProjectListVO {

    @ApiModelProperty(value = "修理项目ID")
    private Long id;

    @ApiModelProperty(value = "所属单位code")
    private String orgId;

    @ApiModelProperty(value = "所属单位名称")
    private String orgName;

    @ApiModelProperty(value = "修理名称")
    private String repairName;

    @ApiModelProperty(value = "修理分组ID")
    private Long groupingId;

    @ApiModelProperty(value = "修理分组名称")
    private String groupingName;

    @ApiModelProperty(value = "本地市场价(元)")
    private BigDecimal localMarketPrice;

    @ApiModelProperty(value = "状态（1：有效 0：无效）")
    private Integer status;

    @ApiModelProperty(value = "是否支持另行报价 1-是 0-否")
    private Integer separateQuotation;

}