package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table data_city_info
 */
public class DataCityInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.cityid")
    private Long cityid;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.city")
    private String city;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.fatherid")
    private Long fatherid;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.lon")
    private BigDecimal lon;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.lat")
    private BigDecimal lat;

    /**
     * Database Column Remarks:
     *   是否投入运营 0：不投入  1：投入运营
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   是否常用(0否，1是)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.in_common_use")
    private Integer inCommonUse;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.cityid")
    public Long getCityid() {
        return cityid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.cityid")
    public void setCityid(Long cityid) {
        this.cityid = cityid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.city")
    public String getCity() {
        return city;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.city")
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.fatherid")
    public Long getFatherid() {
        return fatherid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.fatherid")
    public void setFatherid(Long fatherid) {
        this.fatherid = fatherid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.lon")
    public BigDecimal getLon() {
        return lon;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.lon")
    public void setLon(BigDecimal lon) {
        this.lon = lon;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.lat")
    public BigDecimal getLat() {
        return lat;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.lat")
    public void setLat(BigDecimal lat) {
        this.lat = lat;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.in_common_use")
    public Integer getInCommonUse() {
        return inCommonUse;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.in_common_use")
    public void setInCommonUse(Integer inCommonUse) {
        this.inCommonUse = inCommonUse;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", cityid=").append(cityid);
        sb.append(", city=").append(city);
        sb.append(", fatherid=").append(fatherid);
        sb.append(", lon=").append(lon);
        sb.append(", lat=").append(lat);
        sb.append(", status=").append(status);
        sb.append(", inCommonUse=").append(inCommonUse);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}