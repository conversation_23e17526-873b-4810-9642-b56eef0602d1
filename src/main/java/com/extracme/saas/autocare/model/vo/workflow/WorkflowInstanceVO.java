package com.extracme.saas.autocare.model.vo.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工作流实例视图对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("工作流实例视图")
public class WorkflowInstanceVO {

    /**
     * 实例ID
     */
    @ApiModelProperty("实例ID")
    private Long id;

    /**
     * 工作流模板ID
     */
    @ApiModelProperty("工作流模板ID")
    private Long workflowId;

    /**
     * 工作流模板名称
     */
    @ApiModelProperty("工作流模板名称")
    private String workflowName;

    /**
     * 业务对象ID
     */
    @ApiModelProperty("业务对象ID")
    private String businessId;

    /**
     * 当前活动节点ID
     */
    @ApiModelProperty("当前活动节点ID")
    private Long currentActivityId;

    /**
     * 当前活动节点名称
     */
    @ApiModelProperty("当前活动节点名称")
    private String currentActivityName;

    /**
     * 流程状态
     */
    @ApiModelProperty("流程状态")
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
} 