package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-20 15:19
 */
public class LossRepairInfoEvaBack {
    /**
     * 定损系统修理唯一ID
     */
    private String repairId = StringUtils.EMPTY;
    /**
     * 核损工时费
     */
    private BigDecimal auditManpowerFee = BigDecimal.ZERO;
    /**
     * 核损工时数
     */
    private BigDecimal apprHour = BigDecimal.ZERO;
    /**
     * 审核状态
     */
    private String checkState = StringUtils.EMPTY;
    /**
     * 核损备注
     */
    private String remark = StringUtils.EMPTY;

    @XmlElement(name = "RepairId")
    public String getRepairId() {
        return repairId;
    }

    public void setRepairId(String repairId) {
        this.repairId = repairId;
    }

    @XmlElement(name = "AuditManpowerFee")
    public BigDecimal getAuditManpowerFee() {
        return auditManpowerFee;
    }

    public void setAuditManpowerFee(BigDecimal auditManpowerFee) {
        this.auditManpowerFee = auditManpowerFee;
    }

    @XmlElement(name = "ApprHour")
    public BigDecimal getApprHour() {
        return apprHour;
    }

    public void setApprHour(BigDecimal apprHour) {
        this.apprHour = apprHour;
    }

    @XmlElement(name = "CheckState")
    public String getCheckState() {
        return checkState;
    }

    public void setCheckState(String checkState) {
        this.checkState = checkState;
    }

    @XmlElement(name = "Remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
