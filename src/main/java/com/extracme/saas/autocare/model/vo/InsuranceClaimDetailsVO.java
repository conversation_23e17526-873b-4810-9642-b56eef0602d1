package com.extracme.saas.autocare.model.vo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "保险索赔用印申请信息对象")
public class InsuranceClaimDetailsVO {

    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "出险日期", example = "2025-07-03")
    private String accidentDate;

    @ApiModelProperty(value = "情况描述")
    private String remark;

    @ApiModelProperty(value = "需用印附件信息")
    private List<FileUploadResultVO> attachments;

    @ApiModelProperty(value = "其他附件信息")
    private List<FileUploadResultVO> otherAttachments;

    @ApiModelProperty(value = "已用印附件信息")
    private List<FileUploadResultVO> finishAttachment;
}