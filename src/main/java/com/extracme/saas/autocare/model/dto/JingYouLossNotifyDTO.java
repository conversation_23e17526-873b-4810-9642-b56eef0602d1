package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 精友状态变更通知DTO
 */
@Data
@ApiModel(description = "精友状态变更通知参数")
public class JingYouLossNotifyDTO {

    @NotBlank(message = "任务编号不能为空")
    @ApiModelProperty(value = "任务编号", required = true, example = "TASK123456")
    private String taskNo;

    @NotNull(message = "通知数据不能为空")
    @ApiModelProperty(value = "通知数据", required = true)
    private JingYouLossNotify jingYouLossNotify;
}