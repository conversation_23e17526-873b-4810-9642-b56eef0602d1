package com.extracme.saas.autocare.model.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 权限删除DTO
 */
@Data
@NoArgsConstructor
@ApiModel(description = "权限删除DTO")
public class PermissionDeleteDTO {

    @ApiModelProperty(value = "权限ID", required = true, example = "1")
    @NotNull(message = "权限ID不能为空")
    private Long id;

    @ApiModelProperty(value = "是否强制删除", example = "false",
                     notes = "true：即使权限被角色使用也删除；false：权限被使用时抛出异常")
    private Boolean forceDelete = false;

    /**
     * 兼容性构造函数：支持直接传递权限ID数字
     * 当客户端直接发送数字时，Jackson会调用此构造函数
     */
    @JsonCreator
    public PermissionDeleteDTO(@JsonProperty("id") Long id) {
        this.id = id;
        this.forceDelete = false;
    }

    /**
     * 完整构造函数
     */
    public PermissionDeleteDTO(Long id, Boolean forceDelete) {
        this.id = id;
        this.forceDelete = forceDelete != null ? forceDelete : false;
    }
}
