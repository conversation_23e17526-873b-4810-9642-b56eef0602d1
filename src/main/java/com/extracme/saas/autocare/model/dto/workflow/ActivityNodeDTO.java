package com.extracme.saas.autocare.model.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 活动节点数据传输对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("活动节点")
public class ActivityNodeDTO {

    /**
     * 活动编码
     */
    @NotBlank(message = "活动编码不能为空")
    @ApiModelProperty(value = "活动编码", required = true, example = "HANDOVER")
    private String activityCode;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    @ApiModelProperty(value = "活动名称", required = true, example = "车辆交接")
    private String activityName;

    /**
     * 活动描述
     */
    @ApiModelProperty("活动描述")
    private String description;

    /**
     * 节点顺序
     */
    @NotNull(message = "节点顺序不能为空")
    @ApiModelProperty(value = "节点顺序", required = true, example = "1")
    private Integer sequence;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用", example = "true")
    private Boolean isEnabled = true;
} 