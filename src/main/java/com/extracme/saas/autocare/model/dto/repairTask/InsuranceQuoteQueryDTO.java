package com.extracme.saas.autocare.model.dto.repairTask;

import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "保险定损报价查询DTO")
public class InsuranceQuoteQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "车辆所属机构Id")
    private String orgId;
    
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;
    
    @ApiModelProperty(value = "修理厂名称")
    private String repairDepotName;
    
    @ApiModelProperty(value = "任务创建时间范围开始")
    private String taskInflowTimeStart;
    
    @ApiModelProperty(value = "任务创建时间范围结束")
    private String taskInflowTimeEnd;
    
    @ApiModelProperty(value = "车辆接收时间范围开始")
    private String vehicleReciveTimeStart;
    
    @ApiModelProperty(value = "车辆接收时间范围结束")
    private String vehicleReciveTimeEnd;
    
    @ApiModelProperty(value = "车架号")
    private String vin;
    
    @ApiModelProperty(value = "车型ID")
    private String vehicleModelSeq;
    
    @ApiModelProperty(value = "修理类型", notes = "1:事故维修 2:自费维修 3:车辆保养")
    private String repairTypeId;
    
    @ApiModelProperty(value = "车辆保险所属")
    private String insuranceCompanyName;
    
    @ApiModelProperty(value = "定损报价任务进度", notes = "200:未处理 210:处理中 220:改派中 230:已完成 240:被驳回")
    private String insuranceQuoteTaskSchedule;
    
    @ApiModelProperty(value = "登录用户Id")
    private String repairDepotOrgId;

    @ApiModelProperty(value = "登录用户修理厂id")
    private String repairDepotId;

    @ApiModelProperty(value = "当前环节")
    private Long currentTache;

    @ApiModelProperty(value = "车辆业务状态", notes = "0：分时租赁 1：长租 3：短租 4：公务用车")
    private Integer renttype;

    @ApiModelProperty(value = "全部车辆业务状态")
    private List<Integer> renttypeList;

    @ApiModelProperty(value = "实际操作标记")
    private Integer factOperateTag;

    @ApiModelProperty(value = "事故编号")
    private String accidentNo;
    
    @ApiModelProperty(value = "事故转自费标识", notes = "0否 1是")
    private Integer reviewToSelFeeFlag;
    
    // 以下是字符串处理方法，保留原有逻辑
    public void setVehicleNo(String vehicleNo) {
        if (StringUtils.isNotBlank(vehicleNo)) {
            vehicleNo = vehicleNo.trim();
        }
        this.vehicleNo = vehicleNo;
    }

    public void setTaskNo(String taskNo) {
        if (StringUtils.isNotBlank(taskNo)) {
            taskNo = taskNo.trim();
        }
        this.taskNo = taskNo;
    }

    public void setRepairDepotName(String repairDepotName) {
        if (StringUtils.isNotBlank(repairDepotName)) {
            repairDepotName = repairDepotName.trim();
        }
        this.repairDepotName = repairDepotName;
    }

    public void setVin(String vin) {
        if (StringUtils.isNotBlank(vin)) {
            vin = vin.trim();
        }
        this.vin = vin;
    }

    public void setInsuranceCompanyName(String insuranceCompanyName) {
        if (StringUtils.isNotBlank(insuranceCompanyName)) {
            insuranceCompanyName = insuranceCompanyName.trim();
        }
        this.insuranceCompanyName = insuranceCompanyName;
    }
}
