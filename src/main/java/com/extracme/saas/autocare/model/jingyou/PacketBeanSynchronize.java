package com.extracme.saas.autocare.model.jingyou;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:08
 */
@XmlRootElement(name = "PACKET")
public class PacketBeanSynchronize {
    private HeadBean headBean;
    private BodyBeanSynchronize bodyBeanSynchronize;

    @XmlElement(name = "HEAD")
    public HeadBean getHeadBean() {
        return headBean;
    }

    public void setHeadBean(HeadBean headBean) {
        this.headBean = headBean;
    }

    @XmlElement(name = "BODY")
    public BodyBeanSynchronize getBodyBeanSynchronize() {
        return bodyBeanSynchronize;
    }

    public void setBodyBeanSynchronize(BodyBeanSynchronize bodyBeanSynchronize) {
        this.bodyBeanSynchronize = bodyBeanSynchronize;
    }
}
