package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   数据字典表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table data_dict_info
 */
public class DataDictInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   数据字典名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_name")
    private String dataName;

    /**
     * Database Column Remarks:
     *   数据字典编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_code")
    private String dataCode;

    /**
     * Database Column Remarks:
     *   数据字段value值类型 1-数字 2-字符串
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.code_type")
    private Integer codeType;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   字典值 JSON文本
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_value")
    private String dataValue;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_name")
    public String getDataName() {
        return dataName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_name")
    public void setDataName(String dataName) {
        this.dataName = dataName == null ? null : dataName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_code")
    public String getDataCode() {
        return dataCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_code")
    public void setDataCode(String dataCode) {
        this.dataCode = dataCode == null ? null : dataCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.code_type")
    public Integer getCodeType() {
        return codeType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.code_type")
    public void setCodeType(Integer codeType) {
        this.codeType = codeType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_value")
    public String getDataValue() {
        return dataValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_value")
    public void setDataValue(String dataValue) {
        this.dataValue = dataValue == null ? null : dataValue.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dataName=").append(dataName);
        sb.append(", dataCode=").append(dataCode);
        sb.append(", codeType=").append(codeType);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", dataValue=").append(dataValue);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}