package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;



@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "用户查询DTO")
public class UserQueryDTO extends BasePageDTO {
    
    @ApiModelProperty(value = "真实姓名", example = "张三")
    private String nickname;

    @ApiModelProperty(value = "手机号", example = "138")
    private String mobile;

    @ApiModelProperty(value = "关联角色ID", example = "1")
    private Long roleId;


} 