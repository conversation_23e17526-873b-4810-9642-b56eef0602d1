package com.extracme.saas.autocare.model.vo.repairTask;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "定损报价结果汇总视图对象")
public class InsuranceQuoteAllResultVO {
    
    @ApiModelProperty(value = "定损报价一览列表")
    private PageInfo<InsuranceQuoteResulVO> pageList;
    
    @ApiModelProperty(value = "未处理数量")
    private Long noHandle;
    
    @ApiModelProperty(value = "被驳回数量")
    private Long rejected;
    
    @ApiModelProperty(value = "待审核数量")
    private Long needReview;
    
    @ApiModelProperty(value = "转自费数量")
    private Long toSelfFee;
    
    @ApiModelProperty(value = "已通过数量")
    private Long pass;
    
    @ApiModelProperty(value = "已关闭数量")
    private Long closed;
    
    @ApiModelProperty(value = "待改派数量")
    private Long reassignment;
}
