package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:09
 */
public class HeadBean {
    /**
     * 用户名
     */
    private String userCode = "jy";
    /**
     * 密码
     */
    private String password= "jy";
    /**
     * 请求来源代码
     */
    private String requestSourceCode= "EXTRACME";
    /**
     * 请求来源名称
     */
    private String requestSourceName= "挚极";
    /**
     * 请求类型
     */
    private String requestType= StringUtils.EMPTY;
    /**
     * 操作时间
     */
    private String operatingTime= StringUtils.EMPTY;

    @XmlElement(name = "UserCode")
    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    @XmlElement(name = "Password")
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @XmlElement(name = "RequestSourceCode")
    public String getRequestSourceCode() {
        return requestSourceCode;
    }

    public void setRequestSourceCode(String requestSourceCode) {
        this.requestSourceCode = requestSourceCode;
    }

    @XmlElement(name = "RequestSourceName")
    public String getRequestSourceName() {
        return requestSourceName;
    }

    public void setRequestSourceName(String requestSourceName) {
        this.requestSourceName = requestSourceName;
    }

    @XmlElement(name = "RequestType")
    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    @XmlElement(name = "OperatingTime")
    public String getOperatingTime() {
        return operatingTime;
    }

    public void setOperatingTime(String operatingTime) {
        this.operatingTime = operatingTime;
    }
}
