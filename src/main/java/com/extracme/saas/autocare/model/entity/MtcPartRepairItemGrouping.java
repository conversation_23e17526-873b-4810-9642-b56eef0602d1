package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   零件修理项目分组
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_part_repair_item_grouping
 */
public class MtcPartRepairItemGrouping implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   零件分组ID/修理项目分组ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_id")
    private Long groupingId;

    /**
     * Database Column Remarks:
     *   零件分组名称/修理项目分组名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_name")
    private String groupingName;

    /**
     * Database Column Remarks:
     *   分组类型 1:零件分组 2:修理项目分组
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_type")
    private Integer groupingType;

    /**
     * Database Column Remarks:
     *   父节点ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.father_id")
    private Long fatherId;

    /**
     * Database Column Remarks:
     *   层次编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.level_no")
    private Integer levelNo;

    /**
     * Database Column Remarks:
     *   子项目有无标记 1:有子项目 0:无子项目
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.child_flag")
    private Integer childFlag;

    /**
     * Database Column Remarks:
     *   状态（1：有效 0：无效）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_id")
    public Long getGroupingId() {
        return groupingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_id")
    public void setGroupingId(Long groupingId) {
        this.groupingId = groupingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_name")
    public String getGroupingName() {
        return groupingName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_name")
    public void setGroupingName(String groupingName) {
        this.groupingName = groupingName == null ? null : groupingName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_type")
    public Integer getGroupingType() {
        return groupingType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_type")
    public void setGroupingType(Integer groupingType) {
        this.groupingType = groupingType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.father_id")
    public Long getFatherId() {
        return fatherId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.father_id")
    public void setFatherId(Long fatherId) {
        this.fatherId = fatherId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.level_no")
    public Integer getLevelNo() {
        return levelNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.level_no")
    public void setLevelNo(Integer levelNo) {
        this.levelNo = levelNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.child_flag")
    public Integer getChildFlag() {
        return childFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.child_flag")
    public void setChildFlag(Integer childFlag) {
        this.childFlag = childFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", groupingId=").append(groupingId);
        sb.append(", groupingName=").append(groupingName);
        sb.append(", groupingType=").append(groupingType);
        sb.append(", fatherId=").append(fatherId);
        sb.append(", levelNo=").append(levelNo);
        sb.append(", childFlag=").append(childFlag);
        sb.append(", status=").append(status);
        sb.append(", remark=").append(remark);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}