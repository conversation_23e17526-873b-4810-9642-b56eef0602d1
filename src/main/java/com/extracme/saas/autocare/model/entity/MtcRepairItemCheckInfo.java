package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_repair_item_check_info
 */
public class MtcRepairItemCheckInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.task_no")
    private String taskNo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   配件id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_id")
    private Long itemId;

    /**
     * Database Column Remarks:
     *   配件编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_no")
    private String itemNo;

    /**
     * Database Column Remarks:
     *   配件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_name")
    private String itemName;

    /**
     * Database Column Remarks:
     *   配件类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_type")
    private Integer itemType;

    /**
     * Database Column Remarks:
     *   配件数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_number")
    private Integer itemNumber;

    /**
     * Database Column Remarks:
     *   核损数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_number")
    private Integer viewNumber;

    /**
     * Database Column Remarks:
     *   0非进保预审 1进保预审
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_pre_review_status")
    private Integer insurancePreReviewStatus;

    /**
     * Database Column Remarks:
     *   定损材料费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_material_cost_price")
    private BigDecimal insuranceQuoteMaterialCostPrice;

    /**
     * Database Column Remarks:
     *   定损工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_hour_fee_price")
    private BigDecimal insuranceQuoteHourFeePrice;

    /**
     * Database Column Remarks:
     *   定损合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_amount")
    private BigDecimal insuranceQuoteAmount;

    /**
     * Database Column Remarks:
     *   核损材料费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_material_cost_price")
    private BigDecimal viewMaterialCostPrice;

    /**
     * Database Column Remarks:
     *   核损工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_hour_fee_price")
    private BigDecimal viewHourFeePrice;

    /**
     * Database Column Remarks:
     *   核损总计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_amount")
    private BigDecimal viewAmount;

    /**
     * Database Column Remarks:
     *   核损状态 0未核损 1通过 2价格异议 3建议剔除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.check_status")
    private Integer checkStatus;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.task_no")
    public String getTaskNo() {
        return taskNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.task_no")
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo == null ? null : taskNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_id")
    public Long getItemId() {
        return itemId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_id")
    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_no")
    public String getItemNo() {
        return itemNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_no")
    public void setItemNo(String itemNo) {
        this.itemNo = itemNo == null ? null : itemNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_name")
    public String getItemName() {
        return itemName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_name")
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_type")
    public Integer getItemType() {
        return itemType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_type")
    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_number")
    public Integer getItemNumber() {
        return itemNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_number")
    public void setItemNumber(Integer itemNumber) {
        this.itemNumber = itemNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_number")
    public Integer getViewNumber() {
        return viewNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_number")
    public void setViewNumber(Integer viewNumber) {
        this.viewNumber = viewNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_pre_review_status")
    public Integer getInsurancePreReviewStatus() {
        return insurancePreReviewStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_pre_review_status")
    public void setInsurancePreReviewStatus(Integer insurancePreReviewStatus) {
        this.insurancePreReviewStatus = insurancePreReviewStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_material_cost_price")
    public BigDecimal getInsuranceQuoteMaterialCostPrice() {
        return insuranceQuoteMaterialCostPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_material_cost_price")
    public void setInsuranceQuoteMaterialCostPrice(BigDecimal insuranceQuoteMaterialCostPrice) {
        this.insuranceQuoteMaterialCostPrice = insuranceQuoteMaterialCostPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_hour_fee_price")
    public BigDecimal getInsuranceQuoteHourFeePrice() {
        return insuranceQuoteHourFeePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_hour_fee_price")
    public void setInsuranceQuoteHourFeePrice(BigDecimal insuranceQuoteHourFeePrice) {
        this.insuranceQuoteHourFeePrice = insuranceQuoteHourFeePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_amount")
    public BigDecimal getInsuranceQuoteAmount() {
        return insuranceQuoteAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_amount")
    public void setInsuranceQuoteAmount(BigDecimal insuranceQuoteAmount) {
        this.insuranceQuoteAmount = insuranceQuoteAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_material_cost_price")
    public BigDecimal getViewMaterialCostPrice() {
        return viewMaterialCostPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_material_cost_price")
    public void setViewMaterialCostPrice(BigDecimal viewMaterialCostPrice) {
        this.viewMaterialCostPrice = viewMaterialCostPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_hour_fee_price")
    public BigDecimal getViewHourFeePrice() {
        return viewHourFeePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_hour_fee_price")
    public void setViewHourFeePrice(BigDecimal viewHourFeePrice) {
        this.viewHourFeePrice = viewHourFeePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_amount")
    public BigDecimal getViewAmount() {
        return viewAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_amount")
    public void setViewAmount(BigDecimal viewAmount) {
        this.viewAmount = viewAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.check_status")
    public Integer getCheckStatus() {
        return checkStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.check_status")
    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", vin=").append(vin);
        sb.append(", itemId=").append(itemId);
        sb.append(", itemNo=").append(itemNo);
        sb.append(", itemName=").append(itemName);
        sb.append(", itemType=").append(itemType);
        sb.append(", itemNumber=").append(itemNumber);
        sb.append(", viewNumber=").append(viewNumber);
        sb.append(", insurancePreReviewStatus=").append(insurancePreReviewStatus);
        sb.append(", insuranceQuoteMaterialCostPrice=").append(insuranceQuoteMaterialCostPrice);
        sb.append(", insuranceQuoteHourFeePrice=").append(insuranceQuoteHourFeePrice);
        sb.append(", insuranceQuoteAmount=").append(insuranceQuoteAmount);
        sb.append(", viewMaterialCostPrice=").append(viewMaterialCostPrice);
        sb.append(", viewHourFeePrice=").append(viewHourFeePrice);
        sb.append(", viewAmount=").append(viewAmount);
        sb.append(", checkStatus=").append(checkStatus);
        sb.append(", remark=").append(remark);
        sb.append(", status=").append(status);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}