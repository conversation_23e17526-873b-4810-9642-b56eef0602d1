package com.extracme.saas.autocare.model.vo;

import com.extracme.saas.autocare.model.dto.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "维修任务详情视图对象")
public class RepairTaskDetailsVO {

    @ApiModelProperty(value = "维修任务ID", example = "1001")
    private Long id;

    @ApiModelProperty(value = "任务编号", example = "RT202401010001")
    private String taskNo;

    @ApiModelProperty(value = "当前节点编码", example = "RT202401010001")
    private String currentActivityCode;

    @ApiModelProperty(value = "当前状态编码", example = "RT202401010001")
    private String statusCode;

    @ApiModelProperty(value = "配件库类型(1:自有配件库 2:精友配件库)", example = "1")
    private Integer partsLibraryType;

    @ApiModelProperty(value = "任务所属公司id")
    private String orgId;

    @ApiModelProperty(value = "任务所属公司名")
    private String orgName;

    @ApiModelProperty(value = "维修厂ID", example = "2001")
    private String repairDepotId;

    @ApiModelProperty(value = "维修厂名称", example = "XX汽车维修中心")
    private String repairDepotName;

    @ApiModelProperty(value = "维修厂地址", example = "上海市浦东新区XX路XX号")
    private String repairDepotAddress;

    @ApiModelProperty(value = "维修类型(1-保养 2-维修 3-事故维修)", example = "1")
    private Integer repairTypeId;

    @ApiModelProperty(value = "维修类型名称", example = "保养")
    private String repairTypeName;

    @ApiModelProperty(value = "实例id")
    private Long instanceId;

    @ApiModelProperty(value = "是否超时")
    private Integer isOverTime;

    @ApiModelProperty(value = "核损核价审核级别")
    private Integer advancedAuditLevel;

    @ApiModelProperty(value = "核损核价占据人id")
    private Long verificationLossTaskOperId;

    @ApiModelProperty(value = "核损核价占据人用户名")
    private String verificationLossTaskUsername;

    @ApiModelProperty(value = "修理厂等级")
    private String repairDepotGrade;

    @ApiModelProperty(value = "修理厂类型(1:合作修理厂 2:非合作修理厂)")
    private Integer repairDepotType;

    /**
     * 车辆信息
     */
    @ApiModelProperty(value = "车辆信息")
    private VehicleInfoDTO vehicleInfo;

    /**
     * 创建信息
     */
    @ApiModelProperty(value = "创建信息")
    private RepairTaskCreateInfoVO createInfo;

    /**
     * 维修信息登记
     */
    @ApiModelProperty(value = "维修信息")
    private RepairInfoDTO repairInfo;

    /**
     * 事故登记信息
     */
    @ApiModelProperty(value = "事故信息")
    private AccidentInfoDTO accidentInfo;

    /**
     * 报价合计
     */
    @ApiModelProperty(value = "报价合计")
    private QuotationSummaryDTO quotationSummary;

    /**
     * 费用明细
     */
    @ApiModelProperty(value = "费用明细")
    private RepairFeeDetailDTO repairFeeDetail;

    /**
     * 是否客户直付
     */
    @ApiModelProperty(value = "客户付款信息")
    private CustomerPaymentDTO customerPayment;

    /**
     * 验收信息登记
     */
    @ApiModelProperty(value = "验收信息")
    private AcceptanceInfoDTO acceptanceInfo;

    /**
     * 维修项目明细
     */
    @ApiModelProperty(value = "维修项目明细")
    private RepairItemDetailsDTO repairItemDetails;

    /**
     * 维修图片明细
     */
    @ApiModelProperty(value = "维修图片明细")
    private RepairPicturesDTO repairPictures;

    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
}
