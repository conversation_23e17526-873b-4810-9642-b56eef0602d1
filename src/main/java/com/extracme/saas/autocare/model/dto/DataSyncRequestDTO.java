package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 数据同步请求DTO
 */
@Data
@ApiModel(description = "数据同步请求DTO")
public class DataSyncRequestDTO {

    @ApiModelProperty(value = "目标表名", required = true, example = "mtc_vehicle_info")
    @NotBlank(message = "目标表名不能为空")
    private String targetTable;



    @ApiModelProperty(value = "同步数据（JSON格式）", required = true)
    @NotBlank(message = "同步数据不能为空")
    private String data;

    @ApiModelProperty(value = "批量同步数据列表", required = false)
    private List<BatchSyncDataDTO> batchData;

    /**
     * 批量同步数据DTO
     */
    @Data
    @ApiModel(description = "批量同步数据DTO")
    public static class BatchSyncDataDTO {

        @ApiModelProperty(value = "源数据标识", required = true)
        @NotBlank(message = "源数据标识不能为空")
        private String sourceDataId;

        @ApiModelProperty(value = "数据内容（JSON格式）", required = true)
        @NotBlank(message = "数据内容不能为空")
        private String data;
    }
}
