package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table workflow_instance
 */
public class WorkflowInstance implements Serializable {
    /**
     * Database Column Remarks:
     *   流程实例编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   所采用的流程模板编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.workflow_id")
    private Long workflowId;

    /**
     * Database Column Remarks:
     *   业务对象编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.business_id")
    private String businessId;

    /**
     * Database Column Remarks:
     *   租户编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.tenant_id")
    private Integer tenantId;

    /**
     * Database Column Remarks:
     *   当前所在的活动节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.current_activity_code")
    private String currentActivityCode;

    /**
     * Database Column Remarks:
     *   流程状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.status_code")
    private String statusCode;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.update_by")
    private String updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.workflow_id")
    public Long getWorkflowId() {
        return workflowId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.workflow_id")
    public void setWorkflowId(Long workflowId) {
        this.workflowId = workflowId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.business_id")
    public String getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.business_id")
    public void setBusinessId(String businessId) {
        this.businessId = businessId == null ? null : businessId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.tenant_id")
    public Integer getTenantId() {
        return tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.tenant_id")
    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.current_activity_code")
    public String getCurrentActivityCode() {
        return currentActivityCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.current_activity_code")
    public void setCurrentActivityCode(String currentActivityCode) {
        this.currentActivityCode = currentActivityCode == null ? null : currentActivityCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.status_code")
    public String getStatusCode() {
        return statusCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.status_code")
    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode == null ? null : statusCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", workflowId=").append(workflowId);
        sb.append(", businessId=").append(businessId);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", currentActivityCode=").append(currentActivityCode);
        sb.append(", statusCode=").append(statusCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}