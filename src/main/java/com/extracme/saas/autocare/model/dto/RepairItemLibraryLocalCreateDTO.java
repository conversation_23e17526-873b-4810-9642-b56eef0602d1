package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "本地维修项目创建DTO")
public class RepairItemLibraryLocalCreateDTO {

    @ApiModelProperty(value = "维修项目库项目id")
    @NotNull(message = "维修项目库项目id不能为空")
    private Long itemId;

    @ApiModelProperty(value = "运营公司id")
    @NotBlank(message = "运营公司id不能为空")
    private String orgId;

    @ApiModelProperty(value = "运营公司名称")
    @NotBlank(message = "运营公司名称不能为空")
    private String orgName;

    @ApiModelProperty(value = "工时费本地市场价")
    @NotNull(message = "工时费本地市场价不能为空")
    private BigDecimal hourFeeLocalMarketPrice;

    @ApiModelProperty(value = "工时费本地上限")
    private BigDecimal hourFeeLocalHighestPrice;

    @ApiModelProperty(value = "材料费本地市场价")
    @NotNull(message = "材料费本地市场价不能为空")
    private BigDecimal materialCostLocalMarketPrice;

    @ApiModelProperty(value = "材料费本地上限")
    private BigDecimal materialCostLocalHighestPrice;

}