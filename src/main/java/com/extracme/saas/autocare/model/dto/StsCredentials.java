package com.extracme.saas.autocare.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * STS临时凭证信息
 */
@Data
public class StsCredentials {

    /**
     * 临时访问密钥ID
     */
    private String accessKeyId;

    /**
     * 临时访问密钥Secret
     */
    private String accessKeySecret;

    /**
     * 安全令牌
     */
    private String securityToken;

    /**
     * 凭证过期时间
     */
    private LocalDateTime expiration;

    /**
     * 凭证获取时间
     */
    private LocalDateTime requestTime;

    /**
     * 检查凭证是否有效
     * 
     * @return true如果凭证有效，false如果已过期
     */
    public boolean isValid() {
        return expiration != null && LocalDateTime.now().isBefore(expiration);
    }

    /**
     * 检查凭证是否即将过期（剩余时间少于指定分钟数）
     * 
     * @param minutesBeforeExpiry 过期前的分钟数
     * @return true如果即将过期，false否则
     */
    public boolean isExpiringSoon(int minutesBeforeExpiry) {
        if (expiration == null) {
            return true;
        }
        return LocalDateTime.now().plusMinutes(minutesBeforeExpiry).isAfter(expiration);
    }

    /**
     * 获取剩余有效时间（分钟）
     * 
     * @return 剩余有效时间，如果已过期返回0
     */
    public long getRemainingMinutes() {
        if (expiration == null) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(expiration)) {
            return 0;
        }
        return java.time.Duration.between(now, expiration).toMinutes();
    }
}
