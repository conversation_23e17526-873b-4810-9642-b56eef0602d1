package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车辆维修保存信息DTO
 */
@Data
public class InRepairSubmitDTO {
    
    @NotBlank(message = "任务编号不能为空")
    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "超时原因")
    private String overTimeReasons;
    
    @NotNull(message = "验收图片不能为空")
    @Size(min = 1, message = "至少上传一张验收图片")
    @ApiModelProperty(value = "维修图片（验收图片）")
    private List<FileDTO> repairPicture;

    @NotNull(message = "验收视频不能为空")
    @Size(min = 1, message = "至少上传一个验收视频")
    @ApiModelProperty(value = "验收视频")
    private List<FileDTO> checkVideo;

    /**
     * 客户是否直付 1:是 2:否
     */
    @NotNull(message = "客户直付标识不能为空")
    @Min(value = 0, message = "客户直付标识只能是1或2")
    @Max(value = 1, message = "客户直付标识只能是1或2")
    @ApiModelProperty(value = "客户是否直付", notes = "1:是 2:否", example = "1")
    private Integer custPaysDirect;

    /**
     * 客户支付金额
     */
    @ApiModelProperty(value = "客户支付金额", example = "1000.00")
    private BigDecimal custAmount;

    /**
     * 客户直付凭证
     */
    @ApiModelProperty(value = "客户直付凭证", notes = "图片URL列表", example = "[\"http://example.com/receipt1.jpg\"]")
    private List<FileDTO> custPicture;

    @ApiModelProperty(value = "用户承担金额", example = "300.00")
    private BigDecimal userAssumedAmount;

    @ApiModelProperty(value = "非用户承担金额", example = "300.00")
    private BigDecimal notUserAssumedAmount;
}