package com.extracme.saas.autocare.model.jingyou;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:09
 */
public class BodyBeanEvaBack {
    /**
     * 定损单信息
     */
    private EvalLossInfoEvaBack evalLossInfoEvaBack;
    /**
     * 定损换件信息
     */
    private List<LossFitInfoEvaBack> lossFitInfoEvaBackList;
    /**
     * 核损修理信息
     */
    private List<LossRepairInfoEvaBack> lossRepairInfoEvaBackList;
    /**
     * 核损修理合计信息
     */
    private List<LossRepairSumInfoEvaBack> lossRepairSumInfoEvaBackList;
    /**
     * 核损外修信息
     */
    private List<LossOuterRepairInfoEvaBack> lossOuterRepairInfoEvaBackList;
    /**
     * 定损辅料信息
     */
    private List<LossAssistInfoEvaBack> lossAssistInfoEvaBackList;

    @XmlElement(name = "EvalLossInfo")
    public EvalLossInfoEvaBack getEvalLossInfoEvaBack() {
        return evalLossInfoEvaBack;
    }

    public void setEvalLossInfoEvaBack(EvalLossInfoEvaBack evalLossInfoEvaBack) {
        this.evalLossInfoEvaBack = evalLossInfoEvaBack;
    }

    @XmlElementWrapper(name = "LossFitInfo")
    @XmlElement(name = "Item")
    public List<LossFitInfoEvaBack> getLossFitInfoEvaBackList() {
        return lossFitInfoEvaBackList;
    }

    public void setLossFitInfoEvaBackList(List<LossFitInfoEvaBack> lossFitInfoEvaBackList) {
        this.lossFitInfoEvaBackList = lossFitInfoEvaBackList;
    }

    @XmlElementWrapper(name = "LossRepairInfo")
    @XmlElement(name = "Item")
    public List<LossRepairInfoEvaBack> getLossRepairInfoEvaBackList() {
        return lossRepairInfoEvaBackList;
    }

    public void setLossRepairInfoEvaBackList(List<LossRepairInfoEvaBack> lossRepairInfoEvaBackList) {
        this.lossRepairInfoEvaBackList = lossRepairInfoEvaBackList;
    }

    @XmlElementWrapper(name = "LossRepairSumInfo")
    @XmlElement(name = "Item")
    public List<LossRepairSumInfoEvaBack> getLossRepairSumInfoEvaBackList() {
        return lossRepairSumInfoEvaBackList;
    }

    public void setLossRepairSumInfoEvaBackList(List<LossRepairSumInfoEvaBack> lossRepairSumInfoEvaBackList) {
        this.lossRepairSumInfoEvaBackList = lossRepairSumInfoEvaBackList;
    }

    @XmlElementWrapper(name = "LossOuterRepairInfo")
    @XmlElement(name = "Item")
    public List<LossOuterRepairInfoEvaBack> getLossOuterRepairInfoEvaBackList() {
        return lossOuterRepairInfoEvaBackList;
    }

    public void setLossOuterRepairInfoEvaBackList(List<LossOuterRepairInfoEvaBack> lossOuterRepairInfoEvaBackList) {
        this.lossOuterRepairInfoEvaBackList = lossOuterRepairInfoEvaBackList;
    }

    @XmlElementWrapper(name = "LossAssistInfo")
    @XmlElement(name = "Item")
    public List<LossAssistInfoEvaBack> getLossAssistInfoEvaBackList() {
        return lossAssistInfoEvaBackList;
    }

    public void setLossAssistInfoEvaBackList(List<LossAssistInfoEvaBack> lossAssistInfoEvaBackList) {
        this.lossAssistInfoEvaBackList = lossAssistInfoEvaBackList;
    }
}
