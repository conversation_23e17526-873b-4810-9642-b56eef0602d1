package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table data_province_info
 */
public class DataProvinceInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.provinceid")
    private Long provinceid;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.province")
    private String province;

    /**
     * Database Column Remarks:
     *   是否常用(0否，1是)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.in_common_use")
    private Integer inCommonUse;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.provinceid")
    public Long getProvinceid() {
        return provinceid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.provinceid")
    public void setProvinceid(Long provinceid) {
        this.provinceid = provinceid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.province")
    public String getProvince() {
        return province;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.province")
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.in_common_use")
    public Integer getInCommonUse() {
        return inCommonUse;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.in_common_use")
    public void setInCommonUse(Integer inCommonUse) {
        this.inCommonUse = inCommonUse;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", provinceid=").append(provinceid);
        sb.append(", province=").append(province);
        sb.append(", inCommonUse=").append(inCommonUse);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}