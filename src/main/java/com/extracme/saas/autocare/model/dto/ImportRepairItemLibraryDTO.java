package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "批量操作维修项目")
public class ImportRepairItemLibraryDTO {

    @ApiModelProperty(value = "文件路径", required = true)
    @NotNull(message = "文件路径不能为空")
    private String filePath;

    @ApiModelProperty(value = "操作类型(1-新增 2-修改)", required = true)
    @NotNull(message = "操作类型不能为空")
    private Integer type;

}