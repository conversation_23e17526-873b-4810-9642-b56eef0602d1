package com.extracme.saas.autocare.model.vo.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 工作流实例详情视图对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("工作流实例详情视图")
public class WorkflowInstanceDetailVO extends WorkflowInstanceVO {

    /**
     * 活动实例记录列表
     */
    @ApiModelProperty("活动实例记录列表")
    private List<ActivityInstanceVO> activityInstances;
} 