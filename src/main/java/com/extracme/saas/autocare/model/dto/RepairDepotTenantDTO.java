package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 修理厂租户查询DTO
 */
@Data
@ApiModel(description = "修理厂租户查询DTO")
public class RepairDepotTenantDTO {
    
    /**
     * 租户ID，非必填
     * 如果不传或为空，则使用当前登录用户的租户ID
     */
    @ApiModelProperty(value = "租户ID", required = false, example = "1")
    private Long tenantId;
}
