package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "事故信息DTO")
public class AccidentInfoDTO {

    @ApiModelProperty(value = "预估理赔金额")
    private BigDecimal estimatedClaimAmount;

    @ApiModelProperty(value = "定损单金额", example = "1500.00")
    private BigDecimal lossOrderAmount;
}