package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "在修维修任务信息vo")
public class RepairTaskCurrentListVO {

    @ApiModelProperty(value = "维修任务ID", example = "1001")
    private Long id;

    @ApiModelProperty(value = "任务编号", example = "RT202401010001")
    private String taskNo;

    @ApiModelProperty(value = "当前节点编码", example = "RT202401010001")
    private String currentActivityCode;

    @ApiModelProperty(value = "当前状态编码", example = "RT202401010001")
    private String statusCode;

    @ApiModelProperty(value = "配件库类型(1:自有配件库 2:精友配件库)", example = "1")
    private Integer partsLibraryType;

    @ApiModelProperty(value = "任务所属公司id")
    private String orgId;

    @ApiModelProperty(value = "任务所属公司名")
    private String orgName;

    @ApiModelProperty(value = "车辆运营公司id")
    private String vehicleOperateOrgId;

    @ApiModelProperty(value = "车辆运营公司名称")
    private String vehicleOperateOrgName;

    @ApiModelProperty(value = "维修厂ID", example = "2001")
    private String repairDepotId;

    @ApiModelProperty(value = "维修厂名称", example = "XX汽车维修中心")
    private String repairDepotName;

    @ApiModelProperty(value = "修理厂类型")
    private Integer repairDepotType;

    @ApiModelProperty(value = "修理厂等级")
    private String repairDepotGrade;

    @ApiModelProperty(value = "维修类型(1-保养 2-维修 3-事故维修)", example = "1")
    private Integer repairTypeId;

    @ApiModelProperty(value = "维修类型名称")
    private String repairTypeName;

    @ApiModelProperty(value = "出厂登记状态 1-已登记 2-未登记 3-已关闭")
    private Integer leavingStatus;
}
