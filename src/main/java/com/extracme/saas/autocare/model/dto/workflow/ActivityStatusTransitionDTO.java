package com.extracme.saas.autocare.model.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 节点状态转换规则DTO
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Data
@ApiModel(description = "节点状态转换规则DTO")
public class ActivityStatusTransitionDTO {


    @ApiModelProperty(value = "活动编码", example = "HANDOVER")
    private String activityCode;

    @ApiModelProperty(value = "起始状态编码", required = true, example = "1")
    private String fromStatusCode;

    @ApiModelProperty(value = "目标状态编码", required = true, example = "2")
    @NotBlank(message = "目标状态编码不能为空")
    private String toStatusCode;

    @ApiModelProperty(value = "规则说明", example = "开始处理车辆交接")
    private String description;
}
