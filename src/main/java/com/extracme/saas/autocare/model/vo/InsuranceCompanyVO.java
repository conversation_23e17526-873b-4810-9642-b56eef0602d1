package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "保险公司信息对象")
public class InsuranceCompanyVO {

    @ApiModelProperty(value = "ID", example = "12345")
    private Long id;

    @ApiModelProperty(value = "公司名称", example = "环球车享")
    private String companyName;

    @ApiModelProperty(value = "公司简称", example = "修理厂A")
    private String companyAbbreviation;

    @ApiModelProperty(value = "联系电话", example = "RD12345")
    private String contactNumber;

    @ApiModelProperty(value = "保司状态(1-启用 0-禁用)", example = "1")
    private Integer status;

}