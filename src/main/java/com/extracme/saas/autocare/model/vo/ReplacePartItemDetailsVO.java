package com.extracme.saas.autocare.model.vo;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "换件项目信息详情对象")
public class ReplacePartItemDetailsVO {

    @ApiModelProperty(value = "换件项目ID")
    private Long id;

    @ApiModelProperty(value = "车辆运营单位ID")
    private String orgId;

    @ApiModelProperty(value = "零件名称")
    private String partName;

    @ApiModelProperty(value = "零件分组ID")
    private Long groupingId;

    @ApiModelProperty(value = "零件分组名称")
    private String groupingName;

    @ApiModelProperty(value = "原厂零件号")
    private String originalFactoryPartNo;

    @ApiModelProperty(value = "原厂零件名称")
    private String originalFactoryPartName;

    @ApiModelProperty(value = "系统市场价(元)")
    private BigDecimal sysMarketPrice;

    @ApiModelProperty(value = "系统专修价(元)")
    private BigDecimal sysMajorInPrice;

    @ApiModelProperty(value = "本地市场价(元)")
    private BigDecimal localMarketPrice;

    @ApiModelProperty(value = "状态（1：有效 0：无效）")
    private Integer status;

    @ApiModelProperty(value = "本地专修价(元)")
    private BigDecimal localMajorInPrice;

    @ApiModelProperty(value = "车型SEQ")
    private Long vehicleModelSeq;

    @ApiModelProperty(value = "是否支持另行报价 1-是 0-否")
    private Integer separateQuotation;

    @ApiModelProperty(value = "备注")
    private String remark;

}