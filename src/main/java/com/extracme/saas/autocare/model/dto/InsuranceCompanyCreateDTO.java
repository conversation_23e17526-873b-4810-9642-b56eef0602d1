package com.extracme.saas.autocare.model.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "保司创建DTO")
public class InsuranceCompanyCreateDTO {

    @ApiModelProperty(value = "公司名称")
    @NotBlank(message = "公司名称不能为空")
    private String companyName;

    @ApiModelProperty(value = "公司简称", example = "0")
    @NotBlank(message = "公司简称不能为空")
    private String companyAbbreviation;

    @ApiModelProperty(value = "联系电话")
    @NotBlank(message = "联系电话不能为空")
    private String contactNumber;

    @ApiModelProperty(value = "状态（1-启用 0-禁用）")
    @NotNull(message = "状态不能为空")
    private Integer status;
}