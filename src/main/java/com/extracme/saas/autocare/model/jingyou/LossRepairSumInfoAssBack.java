package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-20 11:05
 */
public class LossRepairSumInfoAssBack {
    /**
     * 工种编码
     */
    private String workTypeCode = StringUtils.EMPTY;
    /**
     * 项目数量
     */
    private Integer itemCount = 0;
    /**
     * 参考工时费
     */
    private BigDecimal referencePrice = BigDecimal.ZERO;
    /**
     * 工种折扣
     */
    private BigDecimal hourDiscount = BigDecimal.ZERO;
    /**
     * 折后参考工时费
     */
    private BigDecimal discountRefPrice = BigDecimal.ZERO;
    /**
     * 定损工时费
     */
    private BigDecimal evalRepairSum = BigDecimal.ZERO;

    @XmlElement(name = "WorkTypeCode")
    public String getWorkTypeCode() {
        return workTypeCode;
    }

    public void setWorkTypeCode(String workTypeCode) {
        this.workTypeCode = workTypeCode;
    }

    @XmlElement(name = "ItemCount")
    public Integer getItemCount() {
        return itemCount;
    }

    public void setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
    }

    @XmlElement(name = "ReferencePrice")
    public BigDecimal getReferencePrice() {
        return referencePrice;
    }

    public void setReferencePrice(BigDecimal referencePrice) {
        this.referencePrice = referencePrice;
    }

    @XmlElement(name = "HourDiscount")
    public BigDecimal getHourDiscount() {
        return hourDiscount;
    }

    public void setHourDiscount(BigDecimal hourDiscount) {
        this.hourDiscount = hourDiscount;
    }

    @XmlElement(name = "DiscountRefPrice")
    public BigDecimal getDiscountRefPrice() {
        return discountRefPrice;
    }

    public void setDiscountRefPrice(BigDecimal discountRefPrice) {
        this.discountRefPrice = discountRefPrice;
    }

    @XmlElement(name = "EvalRepairSum")
    public BigDecimal getEvalRepairSum() {
        return evalRepairSum;
    }

    public void setEvalRepairSum(BigDecimal evalRepairSum) {
        this.evalRepairSum = evalRepairSum;
    }
}
