package com.extracme.saas.autocare.model.vo;

import com.extracme.saas.autocare.model.dto.RepairItemCheckInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 维修项目核损信息视图对象
 * 用于前端请求参数的接收和验证
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "维修项目核损信息视图对象")
public class RepairItemCheckInfoVO {

    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空")
    @Size(max = 50, message = "任务编号长度不能超过50个字符")
    @ApiModelProperty(value = "任务编号", required = true, example = "REPAIR20240601001")
    private String taskNo;

    /**
     * 维修项目核损信息列表
     */
    @NotEmpty(message = "维修项目核损信息列表不能为空")
    @Valid
    @ApiModelProperty(value = "维修项目核损信息列表", required = true)
    private List<RepairItemCheckInfoDTO> list;
}