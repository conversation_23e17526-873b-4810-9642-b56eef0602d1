package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-20 13:17
 */
public class LossAssistInfoAssBack {
    /**
     * 辅料明细主键
     */
    private String assistId = StringUtils.EMPTY;
    /**
     * 辅料名称
     */
    private String itemName = StringUtils.EMPTY;
    /**
     * 数量
     */
    private BigDecimal count = BigDecimal.ZERO;
    /**
     * 定损辅料单价
     */
    private BigDecimal materialFee = BigDecimal.ZERO;
    /**
     * 定损辅料合计
     */
    private BigDecimal evalMateSum = BigDecimal.ZERO;
    /**
     * 自定义辅料标记
     */
    private Integer selfConfigFlag = 0;
    /**
     * 险种代码
     */
    private Integer itemCoverCode = 0;
    /**
     * 备注
     */
    private String remark = StringUtils.EMPTY;
    /**
     * 重开次数
     */
    private String clmTms = StringUtils.EMPTY;

    @XmlElement(name = "AssistId")
    public String getAssistId() {
        return assistId;
    }

    public void setAssistId(String assistId) {
        this.assistId = assistId;
    }

    @XmlElement(name = "ItemName")
    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    @XmlElement(name = "Count")
    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    @XmlElement(name = "MaterialFee")
    public BigDecimal getMaterialFee() {
        return materialFee;
    }

    public void setMaterialFee(BigDecimal materialFee) {
        this.materialFee = materialFee;
    }

    @XmlElement(name = "EvalMateSum")
    public BigDecimal getEvalMateSum() {
        return evalMateSum;
    }

    public void setEvalMateSum(BigDecimal evalMateSum) {
        this.evalMateSum = evalMateSum;
    }

    @XmlElement(name = "SelfConfigFlag")
    public Integer getSelfConfigFlag() {
        return selfConfigFlag;
    }

    public void setSelfConfigFlag(Integer selfConfigFlag) {
        this.selfConfigFlag = selfConfigFlag;
    }

    @XmlElement(name = "ItemCoverCode")
    public Integer getItemCoverCode() {
        return itemCoverCode;
    }

    public void setItemCoverCode(Integer itemCoverCode) {
        this.itemCoverCode = itemCoverCode;
    }

    @XmlElement(name = "Remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @XmlElement(name = "ClmTms")
    public String getClmTms() {
        return clmTms;
    }

    public void setClmTms(String clmTms) {
        this.clmTms = clmTms;
    }
}
