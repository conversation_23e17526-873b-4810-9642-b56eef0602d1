package com.extracme.saas.autocare.model.bo;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 换件项目业务对象
 */
@Data
public class ReplaceItemBO {
    /**
     * 项目ID
     */
    private Long id;
    
    /**
     * 零件名称
     */
    private String partName;
    
    /**
     * 零件编号
     */
    private String partNo;
    
    /**
     * 零件分组
     */
    private String groupName;
    
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    
    /**
     * 数量
     */
    private Integer quantity;
    
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
}
