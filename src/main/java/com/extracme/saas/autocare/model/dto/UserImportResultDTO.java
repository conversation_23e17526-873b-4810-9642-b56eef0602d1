package com.extracme.saas.autocare.model.dto;

import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户导入结果DTO
 */
@Data
public class UserImportResultDTO {

    /**
     * 总记录数
     */
    private Integer totalCount = 0;

    /**
     * 成功记录数
     */
    private Integer successCount = 0;

    /**
     * 失败记录数
     */
    private Integer failCount = 0;

    /**
     * 错误详情列表
     */
    private List<ErrorDetail> errors = new ArrayList<>();

    /**
     * 错误详情
     */
    @Data
    public static class ErrorDetail {
        /**
         * Excel行号
         */
        private Integer row;

        /**
         * 错误信息
         */
        private String message;

        public ErrorDetail(Integer row, String message) {
            this.row = row;
            this.message = message;
        }
    }

    /**
     * 设置成功记录数
     */
    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    /**
     * 设置失败记录数
     */
    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }

    /**
     * 设置总记录数
     */
    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 添加错误信息
     */
    public void addError(Integer row, String message) {
        this.errors.add(new ErrorDetail(row, message));
        this.failCount++;
    }

    /**
     * 增加成功计数
     */
    public void incrementSuccess() {
        this.successCount++;
    }

    public void incrementFailCount() {
        this.failCount++;
    }

    public void incrementFailCount(int count) {
        this.failCount += count;
    }
} 