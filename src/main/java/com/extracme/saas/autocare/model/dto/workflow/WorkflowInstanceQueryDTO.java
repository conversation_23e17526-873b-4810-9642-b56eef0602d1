package com.extracme.saas.autocare.model.dto.workflow;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工作流实例查询请求对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("工作流实例查询请求")
public class WorkflowInstanceQueryDTO extends BasePageDTO {

    /**
     * 工作流模板ID
     */
    @ApiModelProperty("工作流模板ID")
    private Long workflowId;

    /**
     * 业务对象ID
     */
    @ApiModelProperty("业务对象ID")
    private String businessId;

    /**
     * 当前活动节点ID
     */
    @ApiModelProperty("当前活动节点编码")
    private String currentActivityCode;

    /**
     * 流程状态
     */
    @ApiModelProperty("流程状态")
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private String endTime;
}