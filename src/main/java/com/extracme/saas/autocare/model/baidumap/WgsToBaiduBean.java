package com.extracme.saas.autocare.model.baidumap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * WGS84坐标系转百度坐标系响应结果Bean
 * 用于封装百度地图API坐标转换接口的返回数据
 * 
 * <AUTHOR>
 * @createTime 2019年11月19日 上午9:40:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WgsToBaiduBean {
    
    /**
     * 状态码，0表示成功
     */
    private Integer status;
    
    /**
     * 转换结果列表
     */
    private List<Result> result;
    
    /**
     * 坐标转换结果内部类
     * 包含转换后的百度坐标系经纬度信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        
        /**
         * 经度（百度坐标系）
         */
        private Float x;
        
        /**
         * 纬度（百度坐标系）
         */
        private Float y;
    }
}
