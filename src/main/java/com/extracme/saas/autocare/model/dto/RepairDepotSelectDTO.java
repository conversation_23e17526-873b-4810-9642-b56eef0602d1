package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 选择修理厂DTO
 * 用于维修任务选择修理厂接口的请求参数
 */
@Data
@ApiModel(description = "选择修理厂请求参数")
public class RepairDepotSelectDTO {
    
    /**
     * 维修任务ID
     */
    @NotNull(message = "维修任务ID不能为空")
    @ApiModelProperty(value = "维修任务ID", required = true, example = "1001")
    private Long taskId;
    
    /**
     * 修理厂ID
     */
    @NotNull(message = "修理厂ID不能为空")
    @ApiModelProperty(value = "修理厂ID", required = true, example = "2001")
    private String repairDepotId;
}