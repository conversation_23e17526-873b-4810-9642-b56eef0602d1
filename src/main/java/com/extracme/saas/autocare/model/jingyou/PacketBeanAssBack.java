package com.extracme.saas.autocare.model.jingyou;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:08
 */
@XmlRootElement(name = "PACKET")
public class PacketBeanAssBack {
    private HeadBeanAssBack headBeanAssBack;
    private BodyBeanAssBack bodyBeanAssBack;

    @XmlElement(name = "HEAD")
    public HeadBeanAssBack getHeadBeanAssBack() {
        return headBeanAssBack;
    }

    public void setHeadBeanAssBack(HeadBeanAssBack headBeanAssBack) {
        this.headBeanAssBack = headBeanAssBack;
    }

    @XmlElement(name = "BODY")
    public BodyBeanAssBack getBodyBeanAssBack() {
        return bodyBeanAssBack;
    }

    public void setBodyBeanAssBack(BodyBeanAssBack bodyBeanAssBack) {
        this.bodyBeanAssBack = bodyBeanAssBack;
    }
}
