package com.extracme.saas.autocare.model.jingyou;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:08
 */
@XmlRootElement(name = "PACKET")
public class PacketBeanEvaluate {
    private HeadBean headBean;
    private BodyBeanEvaluate bodyBeanEvaluate;

    @XmlElement(name = "HEAD")
    public HeadBean getHeadBean() {
        return headBean;
    }

    public void setHeadBean(HeadBean headBean) {
        this.headBean = headBean;
    }

    @XmlElement(name = "BODY")
    public BodyBeanEvaluate getBodyBeanEvaluate() {
        return bodyBeanEvaluate;
    }

    public void setBodyBeanEvaluate(BodyBeanEvaluate bodyBeanEvaluate) {
        this.bodyBeanEvaluate = bodyBeanEvaluate;
    }
}
