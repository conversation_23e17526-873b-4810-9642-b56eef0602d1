package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table activity_definition
 */
public class ActivityDefinition implements Serializable {
    /**
     * Database Column Remarks:
     *   活动节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   活动编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.activity_code")
    private String activityCode;

    /**
     * Database Column Remarks:
     *   活动名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.activity_name")
    private String activityName;

    /**
     * Database Column Remarks:
     *   节点顺序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.sequence")
    private Integer sequence;

    /**
     * Database Column Remarks:
     *   是否启用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.is_enabled")
    private Integer isEnabled;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   活动描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.description")
    private String description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.activity_code")
    public String getActivityCode() {
        return activityCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.activity_code")
    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.activity_name")
    public String getActivityName() {
        return activityName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.activity_name")
    public void setActivityName(String activityName) {
        this.activityName = activityName == null ? null : activityName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.sequence")
    public Integer getSequence() {
        return sequence;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.sequence")
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.is_enabled")
    public Integer getIsEnabled() {
        return isEnabled;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.is_enabled")
    public void setIsEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.description")
    public String getDescription() {
        return description;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.description")
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", activityName=").append(activityName);
        sb.append(", sequence=").append(sequence);
        sb.append(", isEnabled=").append(isEnabled);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", description=").append(description);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}