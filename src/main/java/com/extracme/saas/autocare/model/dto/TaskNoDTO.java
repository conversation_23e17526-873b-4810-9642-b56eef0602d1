package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 任务编号DTO
 * 用于接口传递任务编号参数
 */
@Data
@ApiModel(description = "任务编号DTO")
public class TaskNoDTO {
    
    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空")
    @Size(max = 50, message = "任务编号长度不能超过50个字符")
    @ApiModelProperty(value = "任务编号", required = true, example = "REPAIR20240601001")
    private String taskNo;
}