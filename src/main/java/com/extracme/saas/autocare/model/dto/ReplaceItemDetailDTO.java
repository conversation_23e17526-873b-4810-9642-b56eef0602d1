package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

@Data
@ApiModel(description = "换件项目明细DTO")
public class ReplaceItemDetailDTO {

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "任务编号", required = true)
    @NotNull(message = "换件项目明细：任务编号不能为空！")
    @Size(min = 1, max = 50, message = "换件项目明细：任务编号的输入长度不能超过50位！")
    private String taskNo;
    @ApiModelProperty(value = "价格方案 0:本地市场价 1:自定义项目", required = true)
    @NotNull(message = "换件项目明细：价格方案不能为空！")
    @Pattern(message = "换件项目明细：价格方案必须输入整数", regexp = "^[0-9]*$")
    @Size(min = 1, max = 1, message = "换件项目明细：价格方案的输入长度不能超过1位！")
    private String priceProgramme;

    @ApiModelProperty(value = "零件分组ID/修理项目分组ID", required = true)
    @NotNull(message = "换件项目明细：零件分组ID不能为空！")
    @Size(min = 1, max = 20, message = "换件项目明细：零件分组ID的输入长度不能超过20位！")
    @Pattern(message = "换件项目明细：零件分组ID必须输入整数", regexp = "^[0-9]*$")
    private String groupingId;

    @ApiModelProperty(value = "零件分组名称/修理项目分组名称", required = true)
    @NotNull(message = "换件项目明细：零件分组名称不能为空！")
    @Size(min = 1, max = 50, message = "换件项目明细：零件分组名称的输入长度不能超过50位！")
    private String groupingName;

    @ApiModelProperty(value = "零件名称", required = true)
    @NotNull(message = "换件项目明细：零件名称不能为空！")
    @Size(min = 1, max = 50, message = "换件项目明细：零件名称的输入长度不能超过50位！")
    private String partName;

    @ApiModelProperty(value = "数量", required = true)
    @NotNull(message = "换件项目明细：数量不能为空！")
    @Size(min = 1, max = 11, message = "换件项目明细：数量长度的输入长度不能超过11位！")
    @Pattern(message = "换件项目明细：数量必须输入整数", regexp = "^[0-9]*$")
    private String partNumber;

    @ApiModelProperty(value = "单价", required = true)
    @NotNull(message = "换件项目明细：单价不能为空！")
    @Pattern(message = "换件项目明细：单价的整数位不能大于7位,小数位不能大于位,小数位不能大于2位！", regexp = "^[0-9]+(.[0-9]{1,2})?$")
    @DecimalMax(message = "换件项目明细：单价的整数位不能大于7位,小数位不能大于2位！", value = "9999999")
    @DecimalMin(message = "换件项目明细：单价的整数位不能大于7位,小数位不能大于2位！", value = "0")
    private String unitPrice;

    @ApiModelProperty(value = "定损金额")
    @Pattern(message = "换件项目明细：定损金额的整数位不能大于7位,小数位不能大于2位！", regexp = "^[0-9]+(.[0-9]{1,2})?$")
    @DecimalMax(message = "换件项目明细：定损金额的整数位不能大于7位,小数位不能大于2位！", value = "9999999")
    @DecimalMin(message = "换件项目明细：定损金额的整数位不能大于7位,小数位不能大于2位！", value = "0")
    private String insuranceQuoteAmount;

    @ApiModelProperty(value = "原厂零件号", required = true)
    @NotBlank(message = "换件项目明细：原厂零件号不能为空")
    @Size(min = 1, max = 50, message = "换件项目明细：原厂零件号的输入长度不能超过50位且不能为空！")
    private String originalFactoryPartNo;
}
