package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   维修车辆信息表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_vehicle_info
 */
public class MtcVehicleInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   主键，自增
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_no")
    private String vehicleNo;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_model_id")
    private Long vehicleModelId;

    /**
     * Database Column Remarks:
     *   车辆所属公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_org_id")
    private String vehicleOrgId;

    /**
     * Database Column Remarks:
     *   车辆运营公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.operation_org_id")
    private String operationOrgId;

    /**
     * Database Column Remarks:
     *   产品线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.product_line")
    private Integer productLine;

    /**
     * Database Column Remarks:
     *   子产品线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.sub_product_line")
    private Integer subProductLine;

    /**
     * Database Column Remarks:
     *   实际运营标签
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.fact_operate_tag")
    private Integer factOperateTag;

    /**
     * Database Column Remarks:
     *   资产状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.property_status")
    private Integer propertyStatus;

    /**
     * Database Column Remarks:
     *   总里程数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.total_mileage")
    private Integer totalMileage;

    /**
     * Database Column Remarks:
     *   发动机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.engine_id")
    private String engineId;

    /**
     * Database Column Remarks:
     *   注册日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.register_date")
    private Date registerDate;

    /**
     * Database Column Remarks:
     *   交强险开始日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.tci_startdate")
    private Date tciStartdate;

    /**
     * Database Column Remarks:
     *   交强险结束日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.tci_enddate")
    private Date tciEnddate;

    /**
     * Database Column Remarks:
     *   保险所属id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.insurance_belongs")
    private Long insuranceBelongs;

    /**
     * Database Column Remarks:
     *   保险所属名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.insurance_company_name")
    private String insuranceCompanyName;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.update_by")
    private String updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_no")
    public String getVehicleNo() {
        return vehicleNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_no")
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo == null ? null : vehicleNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_model_id")
    public Long getVehicleModelId() {
        return vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_model_id")
    public void setVehicleModelId(Long vehicleModelId) {
        this.vehicleModelId = vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_org_id")
    public String getVehicleOrgId() {
        return vehicleOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_org_id")
    public void setVehicleOrgId(String vehicleOrgId) {
        this.vehicleOrgId = vehicleOrgId == null ? null : vehicleOrgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.operation_org_id")
    public String getOperationOrgId() {
        return operationOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.operation_org_id")
    public void setOperationOrgId(String operationOrgId) {
        this.operationOrgId = operationOrgId == null ? null : operationOrgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.product_line")
    public Integer getProductLine() {
        return productLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.product_line")
    public void setProductLine(Integer productLine) {
        this.productLine = productLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.sub_product_line")
    public Integer getSubProductLine() {
        return subProductLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.sub_product_line")
    public void setSubProductLine(Integer subProductLine) {
        this.subProductLine = subProductLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.fact_operate_tag")
    public Integer getFactOperateTag() {
        return factOperateTag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.fact_operate_tag")
    public void setFactOperateTag(Integer factOperateTag) {
        this.factOperateTag = factOperateTag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.property_status")
    public Integer getPropertyStatus() {
        return propertyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.property_status")
    public void setPropertyStatus(Integer propertyStatus) {
        this.propertyStatus = propertyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.total_mileage")
    public Integer getTotalMileage() {
        return totalMileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.total_mileage")
    public void setTotalMileage(Integer totalMileage) {
        this.totalMileage = totalMileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.engine_id")
    public String getEngineId() {
        return engineId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.engine_id")
    public void setEngineId(String engineId) {
        this.engineId = engineId == null ? null : engineId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.register_date")
    public Date getRegisterDate() {
        return registerDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.register_date")
    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.tci_startdate")
    public Date getTciStartdate() {
        return tciStartdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.tci_startdate")
    public void setTciStartdate(Date tciStartdate) {
        this.tciStartdate = tciStartdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.tci_enddate")
    public Date getTciEnddate() {
        return tciEnddate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.tci_enddate")
    public void setTciEnddate(Date tciEnddate) {
        this.tciEnddate = tciEnddate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.insurance_belongs")
    public Long getInsuranceBelongs() {
        return insuranceBelongs;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.insurance_belongs")
    public void setInsuranceBelongs(Long insuranceBelongs) {
        this.insuranceBelongs = insuranceBelongs;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.insurance_company_name")
    public String getInsuranceCompanyName() {
        return insuranceCompanyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.insurance_company_name")
    public void setInsuranceCompanyName(String insuranceCompanyName) {
        this.insuranceCompanyName = insuranceCompanyName == null ? null : insuranceCompanyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", vin=").append(vin);
        sb.append(", vehicleNo=").append(vehicleNo);
        sb.append(", vehicleModelId=").append(vehicleModelId);
        sb.append(", vehicleOrgId=").append(vehicleOrgId);
        sb.append(", operationOrgId=").append(operationOrgId);
        sb.append(", productLine=").append(productLine);
        sb.append(", subProductLine=").append(subProductLine);
        sb.append(", factOperateTag=").append(factOperateTag);
        sb.append(", propertyStatus=").append(propertyStatus);
        sb.append(", totalMileage=").append(totalMileage);
        sb.append(", engineId=").append(engineId);
        sb.append(", registerDate=").append(registerDate);
        sb.append(", tciStartdate=").append(tciStartdate);
        sb.append(", tciEnddate=").append(tciEnddate);
        sb.append(", insuranceBelongs=").append(insuranceBelongs);
        sb.append(", insuranceCompanyName=").append(insuranceCompanyName);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}