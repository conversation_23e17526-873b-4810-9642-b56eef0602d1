package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "维修工时信息DTO")
public class LossRepairInfoDTO {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "项目名称")
    private String itemName;

    @ApiModelProperty(value = "工种")
    private String repairModeCode;

    @ApiModelProperty(value = "工时单价")
    private BigDecimal repairUnitPrice;

    @ApiModelProperty(value = "工时数")
    private BigDecimal apprHour;

    @ApiModelProperty(value = "工时费小计")
    private BigDecimal auditManpowerFee;
}
