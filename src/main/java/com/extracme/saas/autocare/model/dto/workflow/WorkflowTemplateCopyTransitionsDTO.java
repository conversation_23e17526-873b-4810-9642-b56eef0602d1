package com.extracme.saas.autocare.model.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 复制工作流模板节点转换规则请求对象
 *
 * <AUTHOR>
 * @date 2024/06/18
 */
@Data
@ApiModel("复制工作流模板节点转换规则请求")
public class WorkflowTemplateCopyTransitionsDTO {

    /**
     * 源模板ID
     */
    @NotNull(message = "源模板ID不能为空")
    @ApiModelProperty(value = "源模板ID", required = true, example = "1", notes = "要复制转换规则的源工作流模板ID")
    private Long sourceTemplateId;

    /**
     * 目标模板ID
     */
    @NotNull(message = "目标模板ID不能为空")
    @ApiModelProperty(value = "目标模板ID", required = true, example = "2", notes = "接收转换规则的目标工作流模板ID")
    private Long targetTemplateId;
}
