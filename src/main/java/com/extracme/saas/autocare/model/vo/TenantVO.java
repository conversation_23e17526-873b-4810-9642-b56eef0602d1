package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 租户信息VO
 */
@Data
@ApiModel(description = "租户信息VO")
public class TenantVO {
    
    @ApiModelProperty(value = "租户ID", example = "1")
    private Long id;
    
    @ApiModelProperty(value = "租户名称", example = "测试租户")
    private String tenantName;
    
    @ApiModelProperty(value = "租户编码", example = "TEST_TENANT")
    private String tenantCode;
    
    @ApiModelProperty(value = "联系人姓名", example = "张三")
    private String contactName;
    
    @ApiModelProperty(value = "联系人电话", example = "13800138000")
    private String contactPhone;
    
    @ApiModelProperty(value = "联系人邮箱", example = "<EMAIL>")
    private String contactEmail;
    
    @ApiModelProperty(value = "租约过期时间")
    private Date expireTime;
    
    @ApiModelProperty(value = "最大用户数量", example = "100")
    private Integer maxUserCount;
    
    @ApiModelProperty(value = "状态：0-禁用，1-启用", example = "1")
    private Integer status;
    
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;
    
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
}
