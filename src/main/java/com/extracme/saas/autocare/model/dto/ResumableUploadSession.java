package com.extracme.saas.autocare.model.dto;

import lombok.Data;

/**
 * 断点续传上传会话信息
 */
@Data
public class ResumableUploadSession {

    /**
     * 上传ID（内部会话ID）
     */
    private String uploadId;

    /**
     * OSS分片上传ID（真实的OSS uploadId）
     */
    private String ossUploadId;

    /**
     * 文件相对路径
     */
    private String relativePath;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    private String contentType;

    /**
     * 文件分类
     */
    private String category;

    /**
     * 文件MD5值
     */
    private String md5;

    /**
     * 分片大小
     */
    private Long chunkSize;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 创建时间戳
     */
    private Long createTime;

    public ResumableUploadSession() {
        this.createTime = System.currentTimeMillis();
    }

    public ResumableUploadSession(String uploadId, String relativePath, String bucketName) {
        this();
        this.uploadId = uploadId;
        this.relativePath = relativePath;
        this.bucketName = bucketName;
    }

    public ResumableUploadSession(String uploadId, String ossUploadId, String relativePath, String bucketName) {
        this();
        this.uploadId = uploadId;
        this.ossUploadId = ossUploadId;
        this.relativePath = relativePath;
        this.bucketName = bucketName;
    }
}
