package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-13 10:56
 */
public class FactoryBrand {
    /**
     * 品牌编码
     */
    private String brandCode = StringUtils.EMPTY;
    /**
     * 品牌名称
     */
    private String brandName = StringUtils.EMPTY;
    /**
     * 换件折扣率
     */
    private String brandPartDiscountRate = "1";
    /**
     * 工时折扣率
     */
    private String brandRepairDiscountRate = "1";
    /**
     * 是否特约（0：否 1：是）
     */
    private String specialFlag = "0";

    @XmlElement(name = "BrandCode")
    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    @XmlElement(name = "BrandName")
    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    @XmlElement(name = "BrandPartDiscountRate")
    public String getBrandPartDiscountRate() {
        return brandPartDiscountRate;
    }

    public void setBrandPartDiscountRate(String brandPartDiscountRate) {
        this.brandPartDiscountRate = brandPartDiscountRate;
    }

    @XmlElement(name = "BrandRepairDiscountRate")
    public String getBrandRepairDiscountRate() {
        return brandRepairDiscountRate;
    }

    public void setBrandRepairDiscountRate(String brandRepairDiscountRate) {
        this.brandRepairDiscountRate = brandRepairDiscountRate;
    }

    @XmlElement(name = "SpecialFlag")
    public String getSpecialFlag() {
        return specialFlag;
    }

    public void setSpecialFlag(String specialFlag) {
        this.specialFlag = specialFlag;
    }
}
