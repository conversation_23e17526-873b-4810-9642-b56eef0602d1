package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "保司信息修改状态DTO")
public class InsuranceCompanyUpdateStatusDTO {

    @ApiModelProperty(value = "保司信息ID不能为空", required = true)
    @NotNull(message = "保司信息ID不能为空")
    private Long id;

    @ApiModelProperty(value = "保司信息状态(0-禁用 1-启用)", required = true)
    @NotNull(message = "保司信息状态不能为空")
    private Integer status;

}