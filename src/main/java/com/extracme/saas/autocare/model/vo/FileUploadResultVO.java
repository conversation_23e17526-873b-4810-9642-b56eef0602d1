package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件上传结果 VO
 */
@Data
@ApiModel(description = "文件上传结果VO")
public class FileUploadResultVO {

    @ApiModelProperty(value = "文件名", example = "document.pdf")
    private String fileName;

    @ApiModelProperty(value = "原始文件名", example = "用户手册.pdf")
    private String originalFileName;

    @ApiModelProperty(value = "文件相对路径", example = "/extracme/uploads/2024-06-04/abc123.pdf")
    private String relativePath;

    @ApiModelProperty(value = "完整的 OSS 访问 URL", example = "https://evcard.oss-cn-shanghai.aliyuncs.com/extracme/uploads/2024-06-04/abc123.pdf")
    private String fullUrl;

    @ApiModelProperty(value = "文件大小（字节）", example = "1048576")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型", example = "application/pdf")
    private String contentType;

    @ApiModelProperty(value = "文件扩展名", example = "pdf")
    private String fileExtension;

    @ApiModelProperty(value = "上传时间", example = "2024-06-04T10:30:00")
    private LocalDateTime uploadTime;

    @ApiModelProperty(value = "租户编码", example = "extracme")
    private String tenantCode;

    @ApiModelProperty(value = "上传用户ID", example = "123")
    private Long uploadUserId;

    @ApiModelProperty(value = "上传用户名", example = "张三")
    private String uploadUserName;

    @ApiModelProperty(value = "文件MD5值", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String md5;

    @ApiModelProperty(value = "是否为断点续传", example = "true")
    private Boolean isResumable;

    @ApiModelProperty(value = "上传进度百分比", example = "100")
    private Integer progress;
}
