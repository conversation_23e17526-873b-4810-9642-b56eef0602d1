package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据字典简化VO，只包含基本字段
 */
@Data
@ApiModel(description = "数据字典简化信息")
public class DataDictSimpleVO {
    
    @ApiModelProperty(value = "ID")
    private Long id;
    
    @ApiModelProperty(value = "数据字典名称")
    private String dataName;
    
    @ApiModelProperty(value = "数据字典编码")
    private String dataCode;
    
    @ApiModelProperty(value = "数据字段value值类型 1-数字 2-字符串")
    private Integer codeType;
}
