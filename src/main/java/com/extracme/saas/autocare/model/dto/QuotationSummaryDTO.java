package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "报价合计DTO")
public class QuotationSummaryDTO {

    @ApiModelProperty(value = "修理厂报价-换件金额合计", example = "1000.00")
    private BigDecimal repairReplaceTotalAmount;

    @ApiModelProperty(value = "修理厂报价-修理金额合计", example = "500.00")
    private BigDecimal repairRepairTotalAmount;

    @ApiModelProperty(value = "修理厂报价-其他合计（元）", example = "200.00")
    private BigDecimal repairOtherTotalAmount;

    @ApiModelProperty(value = "修理厂报价-维修总金额", example = "1700.00")
    private BigDecimal repairInsuranceTotalAmount;

    @ApiModelProperty(value = "车管核价-换件金额合计", example = "950.00")
    private BigDecimal vehicleReplaceTotalAmount;

    @ApiModelProperty(value = "车管核价-修理金额合计", example = "480.00")
    private BigDecimal vehicleRepairTotalAmount;

    @ApiModelProperty(value = "车管核价-其他合计（元）", example = "180.00")
    private BigDecimal vehicleOtherTotalAmount;

    @ApiModelProperty(value = "车管核价-维修总金额", example = "1610.00")
    private BigDecimal vehicleInsuranceTotalAmount;
}