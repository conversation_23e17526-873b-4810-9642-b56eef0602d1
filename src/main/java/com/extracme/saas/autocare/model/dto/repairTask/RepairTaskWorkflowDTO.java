package com.extracme.saas.autocare.model.dto.repairTask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "维修任务工作流DTO")
public class RepairTaskWorkflowDTO {

    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "当前活动节点")
    private String currentActivityCode;

    @ApiModelProperty(value = "当前状态")
    private String statusCode;

    @ApiModelProperty(value = "出厂状态")
    private Integer leavingStatus;
}