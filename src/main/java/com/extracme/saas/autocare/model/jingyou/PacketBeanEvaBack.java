package com.extracme.saas.autocare.model.jingyou;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:08
 */
@XmlRootElement(name = "PACKET")
public class PacketBeanEvaBack {
    private HeadBeanEvaBack headBeanEvaBack;
    private BodyBeanEvaBack bodyBeanEvaBack;

    @XmlElement(name = "HEAD")
    public HeadBeanEvaBack getHeadBeanEvaBack() {
        return headBeanEvaBack;
    }

    public void setHeadBeanEvaBack(HeadBeanEvaBack headBeanEvaBack) {
        this.headBeanEvaBack = headBeanEvaBack;
    }

    @XmlElement(name = "BODY")
    public BodyBeanEvaBack getBodyBeanEvaBack() {
        return bodyBeanEvaBack;
    }

    public void setBodyBeanEvaBack(BodyBeanEvaBack bodyBeanEvaBack) {
        this.bodyBeanEvaBack = bodyBeanEvaBack;
    }
}
