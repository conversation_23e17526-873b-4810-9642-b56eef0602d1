package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 断点续传初始化请求DTO
 */
@Data
@ApiModel(description = "断点续传初始化请求参数")
public class ResumableUploadInitDTO {

    @ApiModelProperty(value = "原始文件名", required = true, example = "document.pdf")
    @NotBlank(message = "文件名不能为空")
    private String originalFileName;

    @ApiModelProperty(value = "文件大小（字节）", required = true, example = "10485760")
    @NotNull(message = "文件大小不能为空")
    @Positive(message = "文件大小必须大于0")
    private Long fileSize;

    @ApiModelProperty(value = "文件MIME类型", example = "application/pdf")
    private String contentType;

    @ApiModelProperty(value = "文件分类目录", example = "documents")
    private String category;

    @ApiModelProperty(value = "文件MD5值（可选，用于秒传）", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String md5;

    @ApiModelProperty(value = "分片大小（字节）", example = "5242880", notes = "默认5MB，可选参数")
    private Long chunkSize;

    @ApiModelProperty(value = "租户ID（可选，默认使用当前用户租户）", hidden = true)
    private Long tenantId;

    public ResumableUploadInitDTO() {
        this.chunkSize = 5 * 1024 * 1024L; // 默认5MB
    }
}
