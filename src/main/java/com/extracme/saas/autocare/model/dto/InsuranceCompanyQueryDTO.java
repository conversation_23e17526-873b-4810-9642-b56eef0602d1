package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "保司列表查询DTO")
public class InsuranceCompanyQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "保司名称")
    private String companyName;

    @ApiModelProperty(value = "状态(0-无效 1-有效)")
    private Integer status;


} 