package com.extracme.saas.autocare.model.jingyou;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:08
 */
@XmlRootElement(name = "PACKET")
public class PacketBeanView {
    private HeadBean headBean;
    private BodyBeanView bodyBeanView;

    @XmlElement(name = "HEAD")
    public HeadBean getHeadBean() {
        return headBean;
    }

    public void setHeadBean(HeadBean headBean) {
        this.headBean = headBean;
    }

    @XmlElement(name = "BODY")
    public BodyBeanView getBodyBeanView() {
        return bodyBeanView;
    }

    public void setBodyBeanView(BodyBeanView bodyBeanView) {
        this.bodyBeanView = bodyBeanView;
    }
}
