package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "租户查询DTO")
public class TenantQueryDTO extends BasePageDTO {
    
    @ApiModelProperty(value = "租户名称", example = "测试")
    private String tenantName;
    
    @ApiModelProperty(value = "租户编码", example = "TEST")
    private String tenantCode;
    
    @ApiModelProperty(value = "状态：0-禁用，1-启用", example = "1")
    private Integer status;
}
