package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 审批层级VO
 */
@Data
@ApiModel(description = "审批层级VO")
public class ApprovalLevelsVO {

    @ApiModelProperty(value = "审批层级ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "审批层级（0：无，1：一级，2：二级，3：三级，4：四级，5：五级）", example = "1")
    private Integer approvalLevel;

    @ApiModelProperty(value = "审批层级名称", example = "一级审批")
    private String approvalLevelName;

    @ApiModelProperty(value = "自主审批金额", example = "10000.00")
    private BigDecimal selfApprovalAmount;

    @ApiModelProperty(value = "是否有下一级审批", example = "1")
    private Boolean hasNextLevel;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
