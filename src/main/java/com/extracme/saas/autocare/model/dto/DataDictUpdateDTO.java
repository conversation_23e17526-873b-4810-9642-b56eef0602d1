package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 数据字典更新DTO
 * @param <T> 字典值的类型，可以是String、Integer等
 */
@Data
@ApiModel(description = "数据字典更新DTO")
public class DataDictUpdateDTO<T> {

    @ApiModelProperty(value = "数据字典ID", required = true, example = "1")
    @NotNull(message = "字典ID不能为空")
    private Long id;

    @ApiModelProperty(value = "数据字典名称", required = true, example = "性别")
    @NotBlank(message = "字典名称不能为空")
    private String dataName;

    @ApiModelProperty(value = "数据字典编码", required = true, example = "SEX")
    @NotBlank(message = "字典编码不能为空")
    private String dataCode;

    @ApiModelProperty(value = "数据字段value值类型 1-数字 2-字符串", required = true, example = "1")
    @NotNull(message = "字段类型不能为空")
    private Integer codeType;

    @ApiModelProperty(value = "字典值列表", notes = "根据codeType的值，T可以是Integer或String类型")
    private List<DataDictDTO<T>> dictItems;
}
