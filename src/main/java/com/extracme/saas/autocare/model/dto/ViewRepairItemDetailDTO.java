package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "维修项目详情视图DTO")
public class ViewRepairItemDetailDTO {
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;
    
    @ApiModelProperty(value = "项目ID")
    private String id;
    
    @ApiModelProperty(value = "价格方案 0:本地市场价 1:自定义项目")
    private BigDecimal priceProgramme;
    
    @ApiModelProperty(value = "零件分组ID/修理项目分组ID")
    private Long groupingId;
    
    @ApiModelProperty(value = "零件分组名称/修理项目分组名称")
    private String groupingName;
    
    @ApiModelProperty(value = "修理名称")
    private String repairName;
    
    @ApiModelProperty(value = "修理金额")
    private String repairAmount;
    
    @ApiModelProperty(value = "核价金额")
    private String viewAmount;
    
    @ApiModelProperty(value = "核价意见")
    private String remark;
    
    @ApiModelProperty(value = "是否异议 0:否 1:是")
    private String isObjection;
}
