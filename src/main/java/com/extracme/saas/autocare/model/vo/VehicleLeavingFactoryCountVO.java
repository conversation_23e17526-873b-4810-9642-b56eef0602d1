package com.extracme.saas.autocare.model.vo;

import com.extracme.saas.autocare.model.vo.base.BasePageVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车辆出厂登记统计VO
 *
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Data
@ApiModel(description = "车辆出厂登记统计VO")
public class VehicleLeavingFactoryCountVO {
    
    /**
     * 车辆验收出来的列表
     */
    @ApiModelProperty(value = "车辆验收出来的列表")
    private BasePageVO<VehicleLeavingFactoryResultVO> pageInfo;
    
    /**
     * 待验收数量
     */
    @ApiModelProperty(value = "待验收数量", example = "10")
    private Long waitCount;

    /**
     * 完成数量
     */
    @ApiModelProperty(value = "完成数量", example = "20")
    private Long finishCount;

    /**
     * 关闭数量
     */
    @ApiModelProperty(value = "关闭数量", example = "5")
    private Long closeCount;
}
