package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-13 14:08
 */
public class LossCoverVehicle {
    /**
     * 主键
     */
    private String id = StringUtils.EMPTY;
    /**
     * 实际价值
     */
    private BigDecimal realPrice = BigDecimal.ZERO;
    /**
     * 新车购置价
     */
    private BigDecimal vehiclePrice = BigDecimal.ZERO;
    /**
     * 车损险承保保额
     */
    private String insuredAmount = StringUtils.EMPTY;
    /**
     * 国产/进口（01：国产 02：进口）
     */
    private String isImport = StringUtils.EMPTY;
    /**
     * 车辆种类代码
     */
    private String vehicleType = StringUtils.EMPTY;
    /**
     * 车辆种类名称
     */
    private String vehicleTypeName = StringUtils.EMPTY;
    /**
     * 车身颜色代码
     */
    private String carColor = StringUtils.EMPTY;
    /**
     * 车身颜色名称
     */
    private String carColorName = StringUtils.EMPTY;
    /**
     * 初次登记年月
     */
    private String enrolDate = StringUtils.EMPTY;
    /**
     * 使用性质
     */
    private String useProperty = StringUtils.EMPTY;
    /**
     * 行驶区域
     */
    private String driverArea = StringUtils.EMPTY;
    /**
     * 座位
     */
    private Integer seat = 0;
    /**
     * 车架号（VIN码）
     */
    private String vinNo = StringUtils.EMPTY;
    /**
     * 发动机号
     */
    private String engineNo = StringUtils.EMPTY;
    /**
     * 车辆厂牌型号
     */
    private String vehicleModel = StringUtils.EMPTY;
    /**
     * 车牌号码
     */
    private String plateNum = StringUtils.EMPTY;
    /**
     * 功率
     */
    private BigDecimal power = BigDecimal.ZERO;
    /**
     * 排量
     */
    private String displacement = StringUtils.EMPTY;
    /**
     * 吨位
     */
    private BigDecimal tonnage = BigDecimal.ZERO;
    /**
     * 车牌颜色
     */
    private String plateColor = StringUtils.EMPTY;
    /**
     * 创建时间
     */
    private String createTime = StringUtils.EMPTY;
    /**
     * 制造年月
     */
    private String makeDate = StringUtils.EMPTY;
    /**
     * 防盗装置
     */
    private String guardAlarm = StringUtils.EMPTY;
    /**
     * 免验标志
     */
    private String exemptFlag = StringUtils.EMPTY;
    /**
     * 所属性质
     */
    private String belongProperty = StringUtils.EMPTY;

    @XmlElement(name = "Id")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @XmlElement(name = "RealPrice")
    public BigDecimal getRealPrice() {
        return realPrice;
    }

    public void setRealPrice(BigDecimal realPrice) {
        this.realPrice = realPrice;
    }

    @XmlElement(name = "VehiclePrice")
    public BigDecimal getVehiclePrice() {
        return vehiclePrice;
    }

    public void setVehiclePrice(BigDecimal vehiclePrice) {
        this.vehiclePrice = vehiclePrice;
    }

    @XmlElement(name = "InsuredAmount")
    public String getInsuredAmount() {
        return insuredAmount;
    }

    public void setInsuredAmount(String insuredAmount) {
        this.insuredAmount = insuredAmount;
    }

    @XmlElement(name = "IsImport")
    public String getIsImport() {
        return isImport;
    }

    public void setIsImport(String isImport) {
        this.isImport = isImport;
    }

    @XmlElement(name = "VehicleType")
    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    @XmlElement(name = "VehicleTypeName")
    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    @XmlElement(name = "CarColor")
    public String getCarColor() {
        return carColor;
    }

    public void setCarColor(String carColor) {
        this.carColor = carColor;
    }

    @XmlElement(name = "CarColorName")
    public String getCarColorName() {
        return carColorName;
    }

    public void setCarColorName(String carColorName) {
        this.carColorName = carColorName;
    }

    @XmlElement(name = "EnrolDate")
    public String getEnrolDate() {
        return enrolDate;
    }

    public void setEnrolDate(String enrolDate) {
        this.enrolDate = enrolDate;
    }

    @XmlElement(name = "UseProperty")
    public String getUseProperty() {
        return useProperty;
    }

    public void setUseProperty(String useProperty) {
        this.useProperty = useProperty;
    }

    @XmlElement(name = "DriverArea")
    public String getDriverArea() {
        return driverArea;
    }

    public void setDriverArea(String driverArea) {
        this.driverArea = driverArea;
    }

    @XmlElement(name = "Seat")
    public Integer getSeat() {
        return seat;
    }

    public void setSeat(Integer seat) {
        this.seat = seat;
    }

    @XmlElement(name = "VinNo")
    public String getVinNo() {
        return vinNo;
    }

    public void setVinNo(String vinNo) {
        this.vinNo = vinNo;
    }

    @XmlElement(name = "EngineNo")
    public String getEngineNo() {
        return engineNo;
    }

    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo;
    }

    @XmlElement(name = "VehicleModel")
    public String getVehicleModel() {
        return vehicleModel;
    }

    public void setVehicleModel(String vehicleModel) {
        this.vehicleModel = vehicleModel;
    }

    @XmlElement(name = "PlateNum")
    public String getPlateNum() {
        return plateNum;
    }

    public void setPlateNum(String plateNum) {
        this.plateNum = plateNum;
    }

    @XmlElement(name = "Power")
    public BigDecimal getPower() {
        return power;
    }

    public void setPower(BigDecimal power) {
        this.power = power;
    }

    @XmlElement(name = "Displacement")
    public String getDisplacement() {
        return displacement;
    }

    public void setDisplacement(String displacement) {
        this.displacement = displacement;
    }

    @XmlElement(name = "Tonnage")
    public BigDecimal getTonnage() {
        return tonnage;
    }

    public void setTonnage(BigDecimal tonnage) {
        this.tonnage = tonnage;
    }

    @XmlElement(name = "PlateColor")
    public String getPlateColor() {
        return plateColor;
    }

    public void setPlateColor(String plateColor) {
        this.plateColor = plateColor;
    }

    @XmlElement(name = "CreateTime")
    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @XmlElement(name = "MakeDate")
    public String getMakeDate() {
        return makeDate;
    }

    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    @XmlElement(name = "GuardAlarm")
    public String getGuardAlarm() {
        return guardAlarm;
    }

    public void setGuardAlarm(String guardAlarm) {
        this.guardAlarm = guardAlarm;
    }

    @XmlElement(name = "ExemptFlag")
    public String getExemptFlag() {
        return exemptFlag;
    }

    public void setExemptFlag(String exemptFlag) {
        this.exemptFlag = exemptFlag;
    }

    @XmlElement(name = "BelongProperty")
    public String getBelongProperty() {
        return belongProperty;
    }

    public void setBelongProperty(String belongProperty) {
        this.belongProperty = belongProperty;
    }
}
