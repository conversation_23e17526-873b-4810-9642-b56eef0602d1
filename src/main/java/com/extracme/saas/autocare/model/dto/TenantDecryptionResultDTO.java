package com.extracme.saas.autocare.model.dto;

import lombok.Data;

/**
 * 租户解密结果DTO
 */
@Data
public class TenantDecryptionResultDTO {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 解密后的原始数据
     */
    private String decryptedData;

    /**
     * 解密是否成功
     */
    private boolean success;

    /**
     * 错误信息（解密失败时）
     */
    private String errorMessage;

    /**
     * 创建成功的解密结果
     */
    public static TenantDecryptionResultDTO success(Long tenantId, String tenantCode, String decryptedData) {
        TenantDecryptionResultDTO result = new TenantDecryptionResultDTO();
        result.setTenantId(tenantId);
        result.setTenantCode(tenantCode);
        result.setDecryptedData(decryptedData);
        result.setSuccess(true);
        return result;
    }

    /**
     * 创建失败的解密结果
     */
    public static TenantDecryptionResultDTO failure(String errorMessage) {
        TenantDecryptionResultDTO result = new TenantDecryptionResultDTO();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
