package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:27
 */
public class EvalLossInfoView {
    /**
     * 车损标的主键
     */
    private String dmgVhclId = StringUtils.EMPTY;
    /**
     * 定损单号（任务编号）
     */
    private String lossNo = StringUtils.EMPTY;
    /**
     * 报案号（任务编号）
     */
    private String reportCode = StringUtils.EMPTY;

    @XmlElement(name = "DmgVhclId")
    public String getDmgVhclId() {
        return dmgVhclId;
    }

    public void setDmgVhclId(String dmgVhclId) {
        this.dmgVhclId = dmgVhclId;
    }

    @XmlElement(name = "LossNo")
    public String getLossNo() {
        return lossNo;
    }

    public void setLossNo(String lossNo) {
        this.lossNo = lossNo;
    }

    @XmlElement(name = "ReportCode")
    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }
}
