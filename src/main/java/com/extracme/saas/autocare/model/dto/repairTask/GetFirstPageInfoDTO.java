package com.extracme.saas.autocare.model.dto.repairTask;

import java.util.List;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "获取首页信息DTO")
public class GetFirstPageInfoDTO extends BasePageDTO {

    @ApiModelProperty(value = "登录机构ID集合")
    private List<String> loginOrgIds;

    @ApiModelProperty(value = "任务所属公司编码", example = "R001")
    private String orgId;

    @ApiModelProperty(value = "修理厂ID", example = "R001")
    private String repairDepotId;

    @ApiModelProperty(value = "修理厂类型", example = "R001")
    private Integer repairDepotType;

    @ApiModelProperty(value = "修理厂状态", example = "R001")
    private Integer status;

    @ApiModelProperty(value = "数量类型 1:任务 2：车辆")
    private Integer numType;
}