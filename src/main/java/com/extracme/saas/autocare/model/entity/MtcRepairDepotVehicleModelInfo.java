package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   维修厂可修理车型表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_repair_depot_vehicle_model_info
 */
public class MtcRepairDepotVehicleModelInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.repair_depot_id")
    private String repairDepotId;

    /**
     * Database Column Remarks:
     *   车型SEQ
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.vehicle_model_seq")
    private Long vehicleModelSeq;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.repair_depot_id")
    public String getRepairDepotId() {
        return repairDepotId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.repair_depot_id")
    public void setRepairDepotId(String repairDepotId) {
        this.repairDepotId = repairDepotId == null ? null : repairDepotId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.vehicle_model_seq")
    public Long getVehicleModelSeq() {
        return vehicleModelSeq;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.vehicle_model_seq")
    public void setVehicleModelSeq(Long vehicleModelSeq) {
        this.vehicleModelSeq = vehicleModelSeq;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", repairDepotId=").append(repairDepotId);
        sb.append(", vehicleModelSeq=").append(vehicleModelSeq);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}