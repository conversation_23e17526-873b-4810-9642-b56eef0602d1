package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-19 16:51
 */
public class VehicleInfoAssBack {
    /**
     * 发动机型号
     */
    private String engineType = StringUtils.EMPTY;
    /**
     * 燃料类型
     */
    private String fuelType = StringUtils.EMPTY;
    /**
     * 车型产地
     */
    private String vehicleOrigin = StringUtils.EMPTY;
    /**
     * 定损车种
     */
    private String vehicleType = StringUtils.EMPTY;

    @XmlElement(name = "EngineType")
    public String getEngineType() {
        return engineType;
    }

    public void setEngineType(String engineType) {
        this.engineType = engineType;
    }

    @XmlElement(name = "FuelType")
    public String getFuelType() {
        return fuelType;
    }

    public void setFuelType(String fuelType) {
        this.fuelType = fuelType;
    }

    @XmlElement(name = "VehicleOrigin")
    public String getVehicleOrigin() {
        return vehicleOrigin;
    }

    public void setVehicleOrigin(String vehicleOrigin) {
        this.vehicleOrigin = vehicleOrigin;
    }

    @XmlElement(name = "VehicleType")
    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }
}
