package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 维修金额DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "维修金额DTO")
public class RepairAmountDTO {
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;
    
    @ApiModelProperty(value = "维修总金额")
    private BigDecimal repairTotalAmount;
    
    @ApiModelProperty(value = "非用户承担金额")
    private BigDecimal notUserAssumedAmount;
    
    @ApiModelProperty(value = "预估保险理赔价格")
    private BigDecimal estimatedClaimAmount;

    @ApiModelProperty(value = "客户直付金额")
    private BigDecimal custAmount;
    
    @ApiModelProperty(value = "用户承担金额")
    private BigDecimal userAssumedAmount;

    public RepairAmountDTO(String taskNo, BigDecimal repairTotalAmount, BigDecimal userAssumedAmount, BigDecimal estimatedClaimAmount) {
        this.taskNo = taskNo;
        this.repairTotalAmount = repairTotalAmount;
        this.userAssumedAmount = userAssumedAmount;
        this.estimatedClaimAmount = estimatedClaimAmount;
    }
}
