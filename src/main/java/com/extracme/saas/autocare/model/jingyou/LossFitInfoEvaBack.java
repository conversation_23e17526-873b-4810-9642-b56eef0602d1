package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-20 15:08
 */
public class LossFitInfoEvaBack {
    /**
     * 定损系统零件唯一ID
     */
    private String partId = StringUtils.EMPTY;
    /**
     * 核损换件单价
     */
    private BigDecimal auditMaterialFee = BigDecimal.ZERO;
    /**
     * 核损数量
     */
    private BigDecimal auditCount = BigDecimal.ZERO;
    /**
     * 核损换件小计
     */
    private BigDecimal apprPartSum = BigDecimal.ZERO;
    /**
     * 自付比例
     */
    private BigDecimal selfPayPrice = BigDecimal.ZERO;
    /**
     * 核损残值
     */
    private BigDecimal apprRemainsPrice = BigDecimal.ZERO;
    /**
     * 核损管理费率
     */
    private BigDecimal manageRate = BigDecimal.ZERO;
    /**
     * 核损管理费
     */
    private BigDecimal apprManageFee = BigDecimal.ZERO;
    /**
     * 核损状态
     */
    private String checkState = StringUtils.EMPTY;
    /**
     * 核损备注
     */
    private String remark = StringUtils.EMPTY;
    /**
     * 核损回收标记
     */
    private String apprBackFlag = StringUtils.EMPTY;
    /**
     * 复勘标记
     */
    private String recheckFlag = StringUtils.EMPTY;

    @XmlElement(name = "PartId")
    public String getPartId() {
        return partId;
    }

    public void setPartId(String partId) {
        this.partId = partId;
    }

    @XmlElement(name = "AuditMaterialFee")
    public BigDecimal getAuditMaterialFee() {
        return auditMaterialFee;
    }

    public void setAuditMaterialFee(BigDecimal auditMaterialFee) {
        this.auditMaterialFee = auditMaterialFee;
    }

    @XmlElement(name = "AuditCount")
    public BigDecimal getAuditCount() {
        return auditCount;
    }

    public void setAuditCount(BigDecimal auditCount) {
        this.auditCount = auditCount;
    }

    @XmlElement(name = "ApprPartSum")
    public BigDecimal getApprPartSum() {
        return apprPartSum;
    }

    public void setApprPartSum(BigDecimal apprPartSum) {
        this.apprPartSum = apprPartSum;
    }

    @XmlElement(name = "SelfPayPrice")
    public BigDecimal getSelfPayPrice() {
        return selfPayPrice;
    }

    public void setSelfPayPrice(BigDecimal selfPayPrice) {
        this.selfPayPrice = selfPayPrice;
    }

    @XmlElement(name = "ApprRemainsPrice")
    public BigDecimal getApprRemainsPrice() {
        return apprRemainsPrice;
    }

    public void setApprRemainsPrice(BigDecimal apprRemainsPrice) {
        this.apprRemainsPrice = apprRemainsPrice;
    }

    @XmlElement(name = "ManageRate")
    public BigDecimal getManageRate() {
        return manageRate;
    }

    public void setManageRate(BigDecimal manageRate) {
        this.manageRate = manageRate;
    }

    @XmlElement(name = "ApprManageFee")
    public BigDecimal getApprManageFee() {
        return apprManageFee;
    }

    public void setApprManageFee(BigDecimal apprManageFee) {
        this.apprManageFee = apprManageFee;
    }

    @XmlElement(name = "CheckState")
    public String getCheckState() {
        return checkState;
    }

    public void setCheckState(String checkState) {
        this.checkState = checkState;
    }

    @XmlElement(name = "Remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @XmlElement(name = "ApprBackFlag")
    public String getApprBackFlag() {
        return apprBackFlag;
    }

    public void setApprBackFlag(String apprBackFlag) {
        this.apprBackFlag = apprBackFlag;
    }

    @XmlElement(name = "RecheckFlag")
    public String getRecheckFlag() {
        return recheckFlag;
    }

    public void setRecheckFlag(String recheckFlag) {
        this.recheckFlag = recheckFlag;
    }
}
