package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   数据同步日志表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table sys_data_sync_log
 */
public class SysDataSyncLog implements Serializable {
    /**
     * Database Column Remarks:
     *   主键ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   同步批次号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.batch_no")
    private String batchNo;

    /**
     * Database Column Remarks:
     *   租户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.tenant_id")
    private Long tenantId;

    /**
     * Database Column Remarks:
     *   租户编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.tenant_code")
    private String tenantCode;

    /**
     * Database Column Remarks:
     *   目标表名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.target_table")
    private String targetTable;

    /**
     * Database Column Remarks:
     *   同步状态：SUCCESS、FAILED、PROCESSING
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_status")
    private String syncStatus;

    /**
     * Database Column Remarks:
     *   失败原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.error_message")
    private String errorMessage;

    /**
     * Database Column Remarks:
     *   请求来源IP
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.source_ip")
    private String sourceIp;

    /**
     * Database Column Remarks:
     *   同步开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_start_time")
    private Date syncStartTime;

    /**
     * Database Column Remarks:
     *   同步结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_end_time")
    private Date syncEndTime;

    /**
     * Database Column Remarks:
     *   同步耗时（毫秒）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_duration")
    private Long syncDuration;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.update_by")
    private String updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.batch_no")
    public String getBatchNo() {
        return batchNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.batch_no")
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.tenant_id")
    public Long getTenantId() {
        return tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.tenant_id")
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.tenant_code")
    public String getTenantCode() {
        return tenantCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.tenant_code")
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode == null ? null : tenantCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.target_table")
    public String getTargetTable() {
        return targetTable;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.target_table")
    public void setTargetTable(String targetTable) {
        this.targetTable = targetTable == null ? null : targetTable.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_status")
    public String getSyncStatus() {
        return syncStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_status")
    public void setSyncStatus(String syncStatus) {
        this.syncStatus = syncStatus == null ? null : syncStatus.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.error_message")
    public String getErrorMessage() {
        return errorMessage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.error_message")
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage == null ? null : errorMessage.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.source_ip")
    public String getSourceIp() {
        return sourceIp;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.source_ip")
    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp == null ? null : sourceIp.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_start_time")
    public Date getSyncStartTime() {
        return syncStartTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_start_time")
    public void setSyncStartTime(Date syncStartTime) {
        this.syncStartTime = syncStartTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_end_time")
    public Date getSyncEndTime() {
        return syncEndTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_end_time")
    public void setSyncEndTime(Date syncEndTime) {
        this.syncEndTime = syncEndTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_duration")
    public Long getSyncDuration() {
        return syncDuration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_duration")
    public void setSyncDuration(Long syncDuration) {
        this.syncDuration = syncDuration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", tenantCode=").append(tenantCode);
        sb.append(", targetTable=").append(targetTable);
        sb.append(", syncStatus=").append(syncStatus);
        sb.append(", errorMessage=").append(errorMessage);
        sb.append(", sourceIp=").append(sourceIp);
        sb.append(", syncStartTime=").append(syncStartTime);
        sb.append(", syncEndTime=").append(syncEndTime);
        sb.append(", syncDuration=").append(syncDuration);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}