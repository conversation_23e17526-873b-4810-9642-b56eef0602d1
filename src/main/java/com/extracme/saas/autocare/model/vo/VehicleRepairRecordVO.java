package com.extracme.saas.autocare.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆维修记录视图对象
 */
@Data
@ApiModel(description = "车辆维修记录视图对象")
public class VehicleRepairRecordVO {
    
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;
    
    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", example = "MTC202405010001")
    private String taskNo;
    
    /**
     * 修理类型ID
     */
    @ApiModelProperty(value = "修理类型ID", example = "1", notes = "1:事故维修 2:自费维修 3:车辆保养")
    private Integer repairTypeId;
    
    /**
     * 修理类型名称
     */
    @ApiModelProperty(value = "修理类型名称", example = "事故维修")
    private String repairTypeName;
    
    /**
     * 修理厂名称
     */
    @ApiModelProperty(value = "修理厂名称", example = "上海某汽车修理厂")
    private String repairDepotName;
    
    /**
     * 总维修费用
     */
    @ApiModelProperty(value = "总维修费用", example = "1234.56")
    private BigDecimal vehicleRepairTotalAmount;
    
    /**
     * 任务流入时间
     */
    @ApiModelProperty(value = "任务流入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskInflowTime;

    /**
     * 任务创建时间
     */
    @ApiModelProperty(value = "任务创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskCreateTime;
    
    /**
     * 验收时间
     */
    @ApiModelProperty(value = "验收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date vehicleCheckTime;
    
    /**
     * 总里程数
     */
    @ApiModelProperty(value = "总里程数", example = "12345")
    private String totalMileage;
    
    /**
     * 终端里程数
     */
    @ApiModelProperty(value = "终端里程数", example = "12345")
    private String terminalMileage;
    
    /**
     * 换件项目
     */
    @ApiModelProperty(value = "换件项目", example = "前保险杠,左前叶子板")
    private String partNames;
    
    /**
     * 修理项目
     */
    @ApiModelProperty(value = "修理项目", example = "钣金修复,喷漆")
    private String repairNames;
    
    /**
     * 是否重复换件项目
     */
    @ApiModelProperty(value = "是否重复换件项目", example = "前保险杠")
    private String repeatPartNames;
    
    /**
     * 是否重复修理项目
     */
    @ApiModelProperty(value = "是否重复修理项目", example = "钣金修复")
    private String repeatRepairNames;
}
