package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维修项目核损信息DTO
 */
@Data
@ApiModel(description = "维修项目核损信息DTO")
public class RepairItemCheckInfoDTO {
    
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;
    
    @ApiModelProperty(value = "车辆VIN码")
    private String vin;
    
    @ApiModelProperty(value = "维修项目ID")
    private Long itemId;
    
    @ApiModelProperty(value = "项目编号")
    private String itemNo;
    
    @ApiModelProperty(value = "项目名称")
    private String itemName;
    
    @ApiModelProperty(value = "项目类型 1：保养 2：终端 3：维修")
    private Integer itemType;
    
    @ApiModelProperty(value = "项目数量")
    private Integer itemNumber;
    
    @ApiModelProperty(value = "核价数量")
    private Integer viewNumber;
    
    @ApiModelProperty(value = "预审状态（0非进保预审 1进保预审）")
    private Integer insurancePreReviewStatus;
    
    @ApiModelProperty(value = "报价材料费")
    private BigDecimal insuranceQuoteMaterialCostPrice;
    
    @ApiModelProperty(value = "报价工时费")
    private BigDecimal insuranceQuoteHourFeePrice;
    
    @ApiModelProperty(value = "报价总金额")
    private BigDecimal insuranceQuoteAmount;
    
    @ApiModelProperty(value = "核价材料费")
    private BigDecimal viewMaterialCostPrice;
    
    @ApiModelProperty(value = "核价工时费")
    private BigDecimal viewHourFeePrice;
    
    @ApiModelProperty(value = "核价总金额")
    private BigDecimal viewAmount;
    
    @ApiModelProperty(value = "核价状态")
    private Integer checkStatus;
    
    @ApiModelProperty(value = "材料费上限")
    private BigDecimal materialCostHighestPrice;
    
    @ApiModelProperty(value = "工时费上限")
    private BigDecimal hourFeeHighestPrice;

    @ApiModelProperty(value = "备注")
    private String remark;
}