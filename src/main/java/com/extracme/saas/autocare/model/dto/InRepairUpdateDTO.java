package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车辆维修保存信息DTO
 */
@Data
public class InRepairUpdateDTO {
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "超时原因")
    private String overTimeReasons;
    
    @ApiModelProperty(value = "维修图片（验收图片）")
    private List<FileDTO> repairPicture;

    @ApiModelProperty(value = "验收视频")
    private List<FileDTO> checkVideo;

    /**
     * 客户是否直付 0:否 1:是
     */
    @ApiModelProperty(value = "客户是否直付", notes = "0:否 1:是", example = "1")
    private Integer custPaysDirect;

    /**
     * 客户支付金额
     */
    @ApiModelProperty(value = "客户支付金额", example = "1000.00")
    private BigDecimal custAmount;

    /**
     * 客户直付凭证
     */
    @ApiModelProperty(value = "客户直付凭证", notes = "图片URL列表", example = "[\"http://example.com/receipt1.jpg\"]")
    private List<FileDTO> custPicture;

    @ApiModelProperty(value = "用户承担金额", example = "300.00")
    private BigDecimal userAssumedAmount;

    @ApiModelProperty(value = "非用户承担金额", example = "300.00")
    private BigDecimal notUserAssumedAmount;
}
