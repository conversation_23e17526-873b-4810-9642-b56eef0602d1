package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-19 17:13
 */
public class CollisionPartsAssBack {
    /**
     * 损失部位
     */
    private String collisionWay = StringUtils.EMPTY;
    /**
     * 损失程度
     */
    private String collisionDegree = StringUtils.EMPTY;

    @XmlElement(name = "CollisionWay")
    public String getCollisionWay() {
        return collisionWay;
    }

    public void setCollisionWay(String collisionWay) {
        this.collisionWay = collisionWay;
    }

    @XmlElement(name = "CollisionDegree")
    public String getCollisionDegree() {
        return collisionDegree;
    }

    public void setCollisionDegree(String collisionDegree) {
        this.collisionDegree = collisionDegree;
    }
}
