package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "换件项目列表查询DTO")
public class ReplacePartItemQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "车辆运营单位ID")
    private String orgId;

    @ApiModelProperty(value = "零件名称")
    private String partName;

    @ApiModelProperty(value = "零件分组ID")
    private Long groupingId;

    @ApiModelProperty(value = "状态（1：有效 0：无效）")
    private Integer status;

    @ApiModelProperty(value = "车型SEQ")
    private Long vehicleModelSeq;

} 