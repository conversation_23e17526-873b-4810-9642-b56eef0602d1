package com.extracme.saas.autocare.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 维修项目库导出Excel实体类
 * 用于维修项目数据的Excel导出
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(25)
@ColumnWidth(15)
public class RepairItemLibraryExportExcel {

    /**
     * 项目编号
     */
    @ExcelProperty(value = "项目编号", index = 0)
    @ColumnWidth(20)
    private String itemNo;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称", index = 1)
    @ColumnWidth(25)
    private String itemName;

    /**
     * 项目类型
     */
    @ExcelProperty(value = "项目类型", index = 2)
    @ColumnWidth(15)
    private String itemTypeName;

    /**
     * 车型信息
     */
    @ExcelProperty(value = "车型", index = 3)
    @ColumnWidth(20)
    private String vehicleModelInfo;

    /**
     * 工时费全国市场价
     */
    @ExcelProperty(value = "工时费全国市场价", index = 4)
    @ColumnWidth(20)
    private BigDecimal hourFeeNationalMarketPrice;

    /**
     * 工时费全国上限
     */
    @ExcelProperty(value = "工时费全国上限", index = 5)
    @ColumnWidth(20)
    private BigDecimal hourFeeNationalHighestPrice;

    /**
     * 材料费全国市场价
     */
    @ExcelProperty(value = "材料费全国市场价", index = 6)
    @ColumnWidth(20)
    private BigDecimal materialCostNationalMarketPrice;

    /**
     * 材料费全国上限
     */
    @ExcelProperty(value = "材料费全国上限", index = 7)
    @ColumnWidth(20)
    private BigDecimal materialCostNationalHighestPrice;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", index = 8)
    @ColumnWidth(15)
    private String statusName;
}
