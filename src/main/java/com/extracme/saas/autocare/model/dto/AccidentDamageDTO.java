package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;

import org.apache.commons.lang3.StringUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "事故理赔金额信息")
public class AccidentDamageDTO {

    @ApiModelProperty(value = "事故编号")
    private String accidentNo;

    @ApiModelProperty(value = "总损失金额")
    private String totalDamageAmount;

    @ApiModelProperty(value = "我方保险理赔金额")
    private String ownerInsuranceAmount;

    @ApiModelProperty(value = "我方自费金额")
    private String ownerExpenseAmount;

    @ApiModelProperty(value = "三者承担金额")
    private String thirdExpenseAmount;

    @ApiModelProperty(value = "我方车损是否登记过", notes = "1：登记过 2：未登记")
    private Integer hasOwnerDamageLoad;

    @ApiModelProperty(value = "本车全损处置", notes = "1：是 2：否")
    private Integer vehicleTotalLossDisposalFlag;

    /**
     * 获取预估理赔金额
     *
     * @return 预估理赔金额
     */
    public BigDecimal getClaimAmount() {
        if (hasClaimAmount()) {
            return new BigDecimal(this.ownerInsuranceAmount).add(new BigDecimal(this.thirdExpenseAmount));
        }
        return null;
    }

    /**
     * 预估理赔金额是否审核通过（事故任务）
     *
     * @return 预估理赔金额
     */
    public boolean hasClaimAmount() {
        if (null == hasOwnerDamageLoad || hasOwnerDamageLoad != 1) {
            return false;
        }

        return !(StringUtils.isBlank(this.ownerInsuranceAmount) && StringUtils.isBlank(this.thirdExpenseAmount));
    }

    /**
     * 获取四大金额String
     *
     * @return 四大金额String
     */
    public String toSting() {
        return "定损单总损失金额：" + getTotalDamageAmount() +
                "我方保险赔付金额：" + getOwnerInsuranceAmount() +
                "三者承担金额：" + getThirdExpenseAmount() +
                "我方自费金额：" + getOwnerExpenseAmount();
    }
}
