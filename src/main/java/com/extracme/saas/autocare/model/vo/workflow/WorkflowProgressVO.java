package com.extracme.saas.autocare.model.vo.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 工作流进度视图对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("工作流进度视图")
public class WorkflowProgressVO {

    /**
     * 实例ID
     */
    @ApiModelProperty("实例ID")
    private Long instanceId;

    /**
     * 工作流模板名称
     */
    @ApiModelProperty("工作流模板名称")
    private String workflowName;

    /**
     * 当前活动节点名称
     */
    @ApiModelProperty("当前活动节点名称")
    private String currentActivityName;

    /**
     * 流程状态
     */
    @ApiModelProperty("流程状态")
    private String status;

    /**
     * 进度节点列表
     */
    @ApiModelProperty("进度节点列表")
    private List<ProgressNodeVO> progressNodes;
} 