package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商户管理员查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "商户管理员查询DTO")
public class MerchantAdminQueryDTO extends BasePageDTO {
    
    @ApiModelProperty(value = "商户名称", example = "测试商户")
    private String merchantName;
    
    @ApiModelProperty(value = "管理员姓名", example = "张三")
    private String adminName;
    
    @ApiModelProperty(value = "手机号", example = "138")
    private String mobile;
    
    @ApiModelProperty(value = "状态：0-禁用，1-启用", example = "1")
    private Integer status;
}
