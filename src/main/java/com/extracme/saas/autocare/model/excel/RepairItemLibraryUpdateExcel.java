package com.extracme.saas.autocare.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 维修项目库更新Excel实体类
 * 用于批量更新维修项目的Excel导入
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(25)
@ColumnWidth(15)
public class RepairItemLibraryUpdateExcel {

    /**
     * 项目编号（必填）
     * 用于标识要更新的维修项目
     */
    @ExcelProperty(value = "项目编号", index = 0)
    @NotBlank(message = "项目编号不能为空")
    @ColumnWidth(20)
    private String itemNo;

    /**
     * 项目名称（必填）
     */
    @ExcelProperty(value = "项目名称", index = 1)
    @NotBlank(message = "项目名称不能为空")
    @ColumnWidth(25)
    private String itemName;

    /**
     * 工时费全国市场价（必填）
     * 必须为正数，最多两位小数
     */
    @ExcelProperty(value = "工时费全国市场价", index = 2)
    @NotNull(message = "工时费全国市场价不能为空")
    @ColumnWidth(20)
    private BigDecimal hourFeeNationalMarketPrice;

    /**
     * 工时费全国上限（可选）
     * 必须为正数，最多两位小数
     */
    @ExcelProperty(value = "工时费全国上限", index = 3)
    @ColumnWidth(20)
    private BigDecimal hourFeeNationalHighestPrice;

    /**
     * 材料费全国市场价（必填）
     * 必须为正数，最多两位小数
     */
    @ExcelProperty(value = "材料费全国市场价", index = 4)
    @NotNull(message = "材料费全国市场价不能为空")
    @ColumnWidth(20)
    private BigDecimal materialCostNationalMarketPrice;

    /**
     * 材料费全国上限（可选）
     * 必须为正数，最多两位小数
     */
    @ExcelProperty(value = "材料费全国上限", index = 5)
    @ColumnWidth(20)
    private BigDecimal materialCostNationalHighestPrice;

    /**
     * 状态（必填）
     * 可选值：启用、禁用
     */
    @ExcelProperty(value = "状态", index = 6)
    @NotBlank(message = "状态不能为空")
    @ColumnWidth(15)
    private String status;
}
