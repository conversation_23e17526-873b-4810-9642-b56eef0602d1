package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-20 15:27
 */
public class LossOuterRepairInfoEvaBack {
    /**
     * 外修项目主键
     */
    private String outerId = StringUtils.EMPTY;
    /**
     * 外修项目名称
     */
    private String outerName = StringUtils.EMPTY;
    /**
     * 参考工时费
     */
    private BigDecimal referencePrice = BigDecimal.ZERO;
    /**
     * 自定义标记
     */
    private String repairHandaddFlag = StringUtils.EMPTY;
    /**
     * 外修项目核损金额
     */
    private BigDecimal evalOuterPirce = BigDecimal.ZERO;
    /**
     * 外修项目减损金额
     */
    private BigDecimal derogationPrice = BigDecimal.ZERO;
    /**
     * 配件项目名称
     */
    private String derogationItemName = StringUtils.EMPTY;
    /**
     * 配件零件号
     */
    private String derogationItemCode = StringUtils.EMPTY;
    /**
     * 配件价格类型
     */
    private String derogationPriceType = StringUtils.EMPTY;
    /**
     * 换件金额
     */
    private BigDecimal partPrice = BigDecimal.ZERO;
    /**
     * 外修修理厂ID
     */
    private String repairFactoryId = StringUtils.EMPTY;
    /**
     * 外修修理厂名称
     */
    private String repairFactoryName = StringUtils.EMPTY;
    /**
     * 外修修理厂代码
     */
    private String repairFactoryCode = StringUtils.EMPTY;
    /**
     * 险种代码
     */
    private String itemCoverCode = StringUtils.EMPTY;
    /**
     * 核损状态
     */
    private String checkState = StringUtils.EMPTY;
    /**
     * 备注
     */
    private String remark = StringUtils.EMPTY;
    /**
     * 外修费用小计金额
     */
    private BigDecimal repairOuterSum = BigDecimal.ZERO;
    /**
     * 外修配件参考价
     */
    private BigDecimal referencePartPrice = BigDecimal.ZERO;
    /**
     * 外修数量
     */
    private BigDecimal outItemAmount = BigDecimal.ZERO;

    @XmlElement(name = "OuterId")
    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    @XmlElement(name = "OuterName")
    public String getOuterName() {
        return outerName;
    }

    public void setOuterName(String outerName) {
        this.outerName = outerName;
    }

    @XmlElement(name = "ReferencePrice")
    public BigDecimal getReferencePrice() {
        return referencePrice;
    }

    public void setReferencePrice(BigDecimal referencePrice) {
        this.referencePrice = referencePrice;
    }

    @XmlElement(name = "RepairHandaddFlag")
    public String getRepairHandaddFlag() {
        return repairHandaddFlag;
    }

    public void setRepairHandaddFlag(String repairHandaddFlag) {
        this.repairHandaddFlag = repairHandaddFlag;
    }

    @XmlElement(name = "EvalOuterPirce")
    public BigDecimal getEvalOuterPirce() {
        return evalOuterPirce;
    }

    public void setEvalOuterPirce(BigDecimal evalOuterPirce) {
        this.evalOuterPirce = evalOuterPirce;
    }

    @XmlElement(name = "DerogationPrice")
    public BigDecimal getDerogationPrice() {
        return derogationPrice;
    }

    public void setDerogationPrice(BigDecimal derogationPrice) {
        this.derogationPrice = derogationPrice;
    }

    @XmlElement(name = "DerogationItemName")
    public String getDerogationItemName() {
        return derogationItemName;
    }

    public void setDerogationItemName(String derogationItemName) {
        this.derogationItemName = derogationItemName;
    }

    @XmlElement(name = "DerogationItemCode")
    public String getDerogationItemCode() {
        return derogationItemCode;
    }

    public void setDerogationItemCode(String derogationItemCode) {
        this.derogationItemCode = derogationItemCode;
    }

    @XmlElement(name = "DerogationPriceType")
    public String getDerogationPriceType() {
        return derogationPriceType;
    }

    public void setDerogationPriceType(String derogationPriceType) {
        this.derogationPriceType = derogationPriceType;
    }

    @XmlElement(name = "PartPrice")
    public BigDecimal getPartPrice() {
        return partPrice;
    }

    public void setPartPrice(BigDecimal partPrice) {
        this.partPrice = partPrice;
    }

    @XmlElement(name = "RepairFactoryId")
    public String getRepairFactoryId() {
        return repairFactoryId;
    }

    public void setRepairFactoryId(String repairFactoryId) {
        this.repairFactoryId = repairFactoryId;
    }

    @XmlElement(name = "RepairFactoryName")
    public String getRepairFactoryName() {
        return repairFactoryName;
    }

    public void setRepairFactoryName(String repairFactoryName) {
        this.repairFactoryName = repairFactoryName;
    }

    @XmlElement(name = "RepairFactoryCode")
    public String getRepairFactoryCode() {
        return repairFactoryCode;
    }

    public void setRepairFactoryCode(String repairFactoryCode) {
        this.repairFactoryCode = repairFactoryCode;
    }

    @XmlElement(name = "ItemCoverCode")
    public String getItemCoverCode() {
        return itemCoverCode;
    }

    public void setItemCoverCode(String itemCoverCode) {
        this.itemCoverCode = itemCoverCode;
    }

    @XmlElement(name = "CheckState")
    public String getCheckState() {
        return checkState;
    }

    public void setCheckState(String checkState) {
        this.checkState = checkState;
    }

    @XmlElement(name = "Remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @XmlElement(name = "RepairOuterSum")
    public BigDecimal getRepairOuterSum() {
        return repairOuterSum;
    }

    public void setRepairOuterSum(BigDecimal repairOuterSum) {
        this.repairOuterSum = repairOuterSum;
    }

    @XmlElement(name = "ReferencePartPrice")
    public BigDecimal getReferencePartPrice() {
        return referencePartPrice;
    }

    public void setReferencePartPrice(BigDecimal referencePartPrice) {
        this.referencePartPrice = referencePartPrice;
    }

    @XmlElement(name = "OutItemAmount")
    public BigDecimal getOutItemAmount() {
        return outItemAmount;
    }

    public void setOutItemAmount(BigDecimal outItemAmount) {
        this.outItemAmount = outItemAmount;
    }
}
