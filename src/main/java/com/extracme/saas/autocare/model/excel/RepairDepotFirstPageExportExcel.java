package com.extracme.saas.autocare.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

import lombok.Data;

/**
 * 维修厂首页统计信息导出Excel实体
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(25)
@ColumnWidth(15)
public class RepairDepotFirstPageExportExcel {

    /**
     * 修理厂名称
     */
    @ExcelProperty(value = "修理厂名称", index = 0)
    @ColumnWidth(25)
    private String repairDepotName;

    /**
     * 修理厂类型
     */
    @ExcelProperty(value = "修理厂类型", index = 1)
    @ColumnWidth(15)
    private String repairDepotTypeString;

    /**
     * 在修任务数
     */
    @ExcelProperty(value = "在修任务数", index = 2)
    @ColumnWidth(15)
    private Long currentRepairNum;

    /**
     * 今日任务数
     */
    @ExcelProperty(value = "今日进厂数", index = 3)
    @ColumnWidth(15)
    private Long todayAddNum;

    /**
     * 今日出厂车辆数
     */
    @ExcelProperty(value = "今日出厂数", index = 4)
    @ColumnWidth(15)
    private Long todayOutNum;

    @ExcelProperty(value = "待接车", index = 5)
    @ColumnWidth(15)
    private Long vehicleTransferNum;

    /**
     * 报价中
     */
    @ExcelProperty(value = "报价中", index = 6)
    @ColumnWidth(15)
    private Long repairQuotationNum;

    /**
     * 核价中
     */
    @ExcelProperty(value = "核价中", index = 7)
    @ColumnWidth(15)
    private Long lossAssessmentNum;

    /**
     * 维修中
     */
    @ExcelProperty(value = "维修中", index = 8)
    @ColumnWidth(15)
    private Long inRepairNum;

    /**
     * 待验收
     */
    @ExcelProperty(value = "待验收", index = 9)
    @ColumnWidth(15)
    private Long qualityInspectionNum;
}