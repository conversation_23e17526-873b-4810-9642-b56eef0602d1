package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-20 09:27
 */
public class LossRepairInfoAssBack {
    /**
     * 定损明细主键
     */
    private String repairId = StringUtils.EMPTY;
    /**
     * 修理方式代码
     */
    private String repairModeCode = StringUtils.EMPTY;
    /**
     * 项目名称
     */
    private String itemName = StringUtils.EMPTY;
    /**
     * 工时费
     */
    private BigDecimal manpowerFee = BigDecimal.ZERO;
    /**
     * 工时参考价格
     */
    private BigDecimal manpowerRefFee = BigDecimal.ZERO;
    /**
     * 自定义修理标记
     */
    private Integer selfConfigFlag = 0;
    /**
     * 险种代码
     */
    private Integer itemCoverCode = 0;
    /**
     * 备注
     */
    private String remark = StringUtils.EMPTY;
    /**
     * 重开次数
     */
    private String clmTms = StringUtils.EMPTY;
    /**
     * 工时数
     */
    private BigDecimal evalHour = BigDecimal.ZERO;
    /**
     * 工时单价
     */
    private BigDecimal repairUnitPrice = BigDecimal.ZERO;
    /**
     * 损失程度代码
     */
    private String repairLevelCode = StringUtils.EMPTY;
    /**
     * 损失程度名称
     */
    private String repairLevelName = StringUtils.EMPTY;

    @XmlElement(name = "RepairId")
    public String getRepairId() {
        return repairId;
    }

    public void setRepairId(String repairId) {
        this.repairId = repairId;
    }

    @XmlElement(name = "RepairModeCode")
    public String getRepairModeCode() {
        return repairModeCode;
    }

    public void setRepairModeCode(String repairModeCode) {
        this.repairModeCode = repairModeCode;
    }

    @XmlElement(name = "ItemName")
    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    @XmlElement(name = "ManpowerFee")
    public BigDecimal getManpowerFee() {
        return manpowerFee;
    }

    public void setManpowerFee(BigDecimal manpowerFee) {
        this.manpowerFee = manpowerFee;
    }

    @XmlElement(name = "ManpowerRefFee")
    public BigDecimal getManpowerRefFee() {
        return manpowerRefFee;
    }

    public void setManpowerRefFee(BigDecimal manpowerRefFee) {
        this.manpowerRefFee = manpowerRefFee;
    }

    @XmlElement(name = "SelfConfigFlag")
    public Integer getSelfConfigFlag() {
        return selfConfigFlag;
    }

    public void setSelfConfigFlag(Integer selfConfigFlag) {
        this.selfConfigFlag = selfConfigFlag;
    }

    @XmlElement(name = "ItemCoverCode")
    public Integer getItemCoverCode() {
        return itemCoverCode;
    }

    public void setItemCoverCode(Integer itemCoverCode) {
        this.itemCoverCode = itemCoverCode;
    }

    @XmlElement(name = "Remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @XmlElement(name = "ClmTms")
    public String getClmTms() {
        return clmTms;
    }

    public void setClmTms(String clmTms) {
        this.clmTms = clmTms;
    }

    @XmlElement(name = "EvalHour")
    public BigDecimal getEvalHour() {
        return evalHour;
    }

    public void setEvalHour(BigDecimal evalHour) {
        this.evalHour = evalHour;
    }

    @XmlElement(name = "RepairUnitPrice")
    public BigDecimal getRepairUnitPrice() {
        return repairUnitPrice;
    }

    public void setRepairUnitPrice(BigDecimal repairUnitPrice) {
        this.repairUnitPrice = repairUnitPrice;
    }

    @XmlElement(name = "RepairLevelCode")
    public String getRepairLevelCode() {
        return repairLevelCode;
    }

    public void setRepairLevelCode(String repairLevelCode) {
        this.repairLevelCode = repairLevelCode;
    }

    @XmlElement(name = "RepairLevelName")
    public String getRepairLevelName() {
        return repairLevelName;
    }

    public void setRepairLevelName(String repairLevelName) {
        this.repairLevelName = repairLevelName;
    }
}
