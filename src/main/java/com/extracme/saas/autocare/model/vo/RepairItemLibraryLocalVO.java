package com.extracme.saas.autocare.model.vo;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "维修项目本地库信息")
public class RepairItemLibraryLocalVO {

    private Long id;

    @ApiModelProperty(value = "维修项目库项目id")
    private Long itemId;

    @ApiModelProperty(value = "项目编号")
    private String itemNo;

    @ApiModelProperty(value = "运营公司id")
    private String orgId;

    @ApiModelProperty(value = "运营公司名称")
    private String orgName;

    @ApiModelProperty(value = "工时费本地市场价")
    private BigDecimal hourFeeLocalMarketPrice;

    @ApiModelProperty(value = "工时费本地上限")
    private BigDecimal hourFeeLocalHighestPrice;

    @ApiModelProperty(value = "材料费本地市场价")
    private BigDecimal materialCostLocalMarketPrice;

    @ApiModelProperty(value = "材料费本地上限")
    private BigDecimal materialCostLocalHighestPrice;

    @ApiModelProperty(value = "状态(0：无效 1：有效)")
    private Integer status;

}
