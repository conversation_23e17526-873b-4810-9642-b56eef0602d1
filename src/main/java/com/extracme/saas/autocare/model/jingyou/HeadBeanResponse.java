package com.extracme.saas.autocare.model.jingyou;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:09
 */
public class HeadBeanResponse {
    /**
     * 返回类型代码
     */
    private String responseCode = "000";
    /**
     * 错误描述
     */
    private String errorMessage = "成功";

    @XmlElement(name = "ResponseCode")
    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    @XmlElement(name = "ErrorMessage")
    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
