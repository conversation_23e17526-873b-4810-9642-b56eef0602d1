package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-20 15:37
 */
public class LossAssistInfoEvaBack {
    /**
     * 辅料项目ID
     */
    private String assistId = StringUtils.EMPTY;
    /**
     * 辅料核损单价
     */
    private BigDecimal auditPrice = BigDecimal.ZERO;
    /**
     * 辅料核损数量
     */
    private BigDecimal auditCount = BigDecimal.ZERO;
    /**
     * 辅料核损小计
     */
    private BigDecimal apprMateSum = BigDecimal.ZERO;
    /**
     * 核损状态
     */
    private String checkState = StringUtils.EMPTY;
    /**
     * 核损备注
     */
    private String remark = StringUtils.EMPTY;

    @XmlElement(name = "AssistId")
    public String getAssistId() {
        return assistId;
    }

    public void setAssistId(String assistId) {
        this.assistId = assistId;
    }

    @XmlElement(name = "AuditPrice")
    public BigDecimal getAuditPrice() {
        return auditPrice;
    }

    public void setAuditPrice(BigDecimal auditPrice) {
        this.auditPrice = auditPrice;
    }

    @XmlElement(name = "AuditCount")
    public BigDecimal getAuditCount() {
        return auditCount;
    }

    public void setAuditCount(BigDecimal auditCount) {
        this.auditCount = auditCount;
    }

    @XmlElement(name = "ApprMateSum")
    public BigDecimal getApprMateSum() {
        return apprMateSum;
    }

    public void setApprMateSum(BigDecimal apprMateSum) {
        this.apprMateSum = apprMateSum;
    }

    @XmlElement(name = "CheckState")
    public String getCheckState() {
        return checkState;
    }

    public void setCheckState(String checkState) {
        this.checkState = checkState;
    }

    @XmlElement(name = "Remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
