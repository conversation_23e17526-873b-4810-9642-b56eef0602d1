package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:09
 */
public class HeadBeanEvaBack {
    /**
     * 用户名
     */
    private String userCode = StringUtils.EMPTY;
    /**
     * 密码
     */
    private String password = StringUtils.EMPTY;
    /**
     * 请求类型
     */
    private String requestType = StringUtils.EMPTY;
    /**
     * 提交/退回标志（1.提交0.退回）
     */
    private String isPassFlag = StringUtils.EMPTY;

    @XmlElement(name = "UserCode")
    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    @XmlElement(name = "Password")
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @XmlElement(name = "RequestType")
    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    @XmlElement(name = "IsPassFlag")
    public String getIsPassFlag() {
        return isPassFlag;
    }

    public void setIsPassFlag(String isPassFlag) {
        this.isPassFlag = isPassFlag;
    }
}
