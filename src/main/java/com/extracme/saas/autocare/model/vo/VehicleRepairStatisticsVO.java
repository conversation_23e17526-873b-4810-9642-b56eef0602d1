package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "首页看板数量统计")
public class VehicleRepairStatisticsVO {

    /** 当前在修总数量 */
    @ApiModelProperty(value = "当前在修总数量", example = "1")
    private Long currentRepairTotalNum;

    /** 今日进修理厂数量 */
    @ApiModelProperty(value = "今日进修理厂数量", example = "1")
    private Long todayInFactoryNum;

    /** 今日出修理厂数量 */
    @ApiModelProperty(value = "今日出修理厂数量", example = "1")
    private Long todayOutFactoryNum;
}
