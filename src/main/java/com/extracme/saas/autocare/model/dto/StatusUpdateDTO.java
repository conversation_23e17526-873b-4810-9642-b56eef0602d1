package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 状态更新DTO
 * 用于用户、角色等实体的状态更新操作
 */
@Data
@ApiModel(description = "状态更新DTO")
public class StatusUpdateDTO {

    /**
     * 实体ID（用户ID、角色ID等）
     */
    @NotNull(message = "ID不能为空")
    @ApiModelProperty(value = "实体ID", required = true, example = "1")
    private Long id;

    /**
     * 状态值：0-禁用，1-启用
     */
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值只能是0或1")
    @Max(value = 1, message = "状态值只能是0或1")
    @ApiModelProperty(value = "状态：0-禁用，1-启用", required = true, example = "1")
    private Integer status;
}
