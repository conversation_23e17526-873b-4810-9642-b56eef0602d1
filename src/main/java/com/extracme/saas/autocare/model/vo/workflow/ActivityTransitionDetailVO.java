package com.extracme.saas.autocare.model.vo.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动节点转换规则详情视图对象
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Data
@ApiModel(description = "活动节点转换规则详情视图对象")
public class ActivityTransitionDetailVO {

    @ApiModelProperty(value = "活动节点转换规则")
    private ActivityTransitionVO activityTransition;


}
