package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 组织架构树结构VO
 */
@Data
@ApiModel(description = "组织架构树结构VO")
public class OrgInfoTreeVO {

    /**
     * 机构code
     */
    @ApiModelProperty("机构code")
    private String orgId;

    /**
     * 机构id
     */
    @ApiModelProperty("机构id")
    private String orgName;


    @ApiModelProperty(value = "子节点")
    private List<OrgInfoTreeVO> children;
}
