package com.extracme.saas.autocare.model.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户创建DTO
 */
@Data
public class UserCreateDTO {

    

    /**
     * 昵称，2-20位字符
     */
    @Size(min = 2, max = 20, message = "昵称长度必须在2-20位之间")
    @ApiModelProperty(value = "昵称，2-20位字符", example = "张三")
    private String nickname;

    /**
     * 手机号，11位
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @ApiModelProperty(value = "手机号，11位", required = true, example = "13800138000")
    private String mobile;

    /**
     * 邮箱地址
     */
    @Email(message = "邮箱格式不正确")
    @ApiModelProperty(value = "邮箱地址", example = "<EMAIL>")
    private String email;

    /**
     * 组织ID列表（可选，支持多机构关联）
     */
    @ApiModelProperty(value = "组织ID列表（可选）", example = "[\"ORG001\", \"ORG002\"]")
    private List<String> orgIds;

    /**
     * 角色ID列表
     */
    @NotEmpty(message = "角色不能为空")
    @ApiModelProperty(value = "角色ID列表", required = true, example = "[1, 2]")
    private List<Long> roleIds;

    /**
     * 状态：0-禁用，1-启用
     */
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    @ApiModelProperty(value = "状态：0-禁用，1-启用", required = true, example = "1")
    private Integer status = 1;

    /**
     * 审批层级（0：无，1：一级，2：二级，3：三级，4：四级，5：五级）
     */
    @Min(value = 0, message = "审批层级最小值为0")
    @Max(value = 5, message = "审批层级最大值为5")
    @ApiModelProperty(value = "审批层级（0：无，1：一级，2：二级，3：三级，4：四级，5：五级）", example = "1")
    private Integer approvalLevel;

    /**
     * 关联维修站点ID
     */
    @ApiModelProperty(value = "关联维修站点ID，用于维修站点类型用户", example = "DEPOT001")
    private String repairDepotId;

    /**
     * 账号类型：0-超级管理员，1-运营人员，2-修理厂，3-保险公司
     */
    @Min(value = 0, message = "账号类型值不正确，0:超级管理员 1:运营人员 2:修理厂 3:保险公司")
    @Max(value = 3, message = "账号类型值不正确，0:超级管理员 1:运营人员 2:修理厂 3:保险公司")
    @ApiModelProperty(value = "账号类型：0-超级管理员 1-运营人员 2-修理厂 3-保险公司", example = "0")
    private Integer accountType;

    /**
     * 保险公司ID（当accountType=3时必填）
     */
    @ApiModelProperty(value = "保险公司ID（当accountType=3时必填）", example = "1")
    private Long insuranceCompanyId;

    /**
     * 租户ID（可选，仅超级管理员可指定，普通用户自动设置为当前租户）
     */
    @ApiModelProperty(value = "租户ID（可选，仅超级管理员可指定，普通用户自动设置为当前租户）", example = "1")
    private Long tenantId;
}