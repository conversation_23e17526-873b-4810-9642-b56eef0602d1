package com.extracme.saas.autocare.model.dto;

import lombok.Data;

/**
 * 同步数据结果DTO
 */
@Data
public class SyncDataResultDTO {

    /**
     * 同步是否成功
     */
    private boolean success;

    /**
     * 目标数据ID（新增或更新后的ID）
     */
    private Long targetDataId;

    /**
     * 错误信息（失败时）
     */
    private String errorMessage;

    /**
     * 操作类型：INSERT-新增，UPDATE-更新
     */
    private OperationType operationType;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        INSERT, UPDATE
    }

    /**
     * 创建成功的同步结果
     */
    public static SyncDataResultDTO success(Long targetDataId, OperationType operationType) {
        SyncDataResultDTO result = new SyncDataResultDTO();
        result.setSuccess(true);
        result.setTargetDataId(targetDataId);
        result.setOperationType(operationType);
        return result;
    }

    /**
     * 创建成功的同步结果（兼容旧版本）
     */
    public static SyncDataResultDTO success(Long targetDataId) {
        return success(targetDataId, null);
    }

    /**
     * 创建失败的同步结果
     */
    public static SyncDataResultDTO failure(String errorMessage, OperationType operationType) {
        SyncDataResultDTO result = new SyncDataResultDTO();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setOperationType(operationType);
        return result;
    }

    /**
     * 创建失败的同步结果（兼容旧版本）
     */
    public static SyncDataResultDTO failure(String errorMessage) {
        return failure(errorMessage, null);
    }
}
