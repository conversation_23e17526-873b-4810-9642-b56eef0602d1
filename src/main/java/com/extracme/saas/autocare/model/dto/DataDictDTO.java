package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据字典项DTO
 * @param <T> 值的类型，可以是String、Integer等
 */
@Data
@ApiModel(description = "数据字典项DTO")
public class DataDictDTO<T> {

    @ApiModelProperty(value = "名称", required = true, example = "男")
    private String name;

    @ApiModelProperty(value = "编码", required = true, example = "1")
    private T code;

    @ApiModelProperty(value = "备注", required = false, example = "这是一个备注")
    private String remark;
}
