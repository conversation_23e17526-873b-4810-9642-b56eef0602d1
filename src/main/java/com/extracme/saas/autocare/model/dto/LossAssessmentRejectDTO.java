package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 核损核价信息DTO
 */
@Data
@ApiModel(description = "核损核价更新DTO")
public class LossAssessmentRejectDTO {

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", required = true, example = "TASK202403150001")
    private String taskNo;

    /**
     * 核损核价驳回原因
     */
    @ApiModelProperty(value = "核损核价驳回原因", required = true, example = "材料不全")
    private String verificationRejectReasons;

    /**
     * 核损核价驳回原因详情
     */
    @ApiModelProperty(value = "核损核价驳回原因详情", required = true, example = "缺少车辆损失照片")
    private String verificationRejectReasonsDetail;
}