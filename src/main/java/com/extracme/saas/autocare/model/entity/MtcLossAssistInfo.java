package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   定损辅料信息
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_loss_assist_info
 */
public class MtcLossAssistInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.task_no")
    private String taskNo;

    /**
     * Database Column Remarks:
     *   辅料明细主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.assist_id")
    private String assistId;

    /**
     * Database Column Remarks:
     *   辅料名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.item_name")
    private String itemName;

    /**
     * Database Column Remarks:
     *   数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.count")
    private BigDecimal count;

    /**
     * Database Column Remarks:
     *   定损辅料单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.material_fee")
    private BigDecimal materialFee;

    /**
     * Database Column Remarks:
     *   定损辅料合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.eval_mate_sum")
    private BigDecimal evalMateSum;

    /**
     * Database Column Remarks:
     *   自定义辅料标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.self_config_flag")
    private Integer selfConfigFlag;

    /**
     * Database Column Remarks:
     *   险种代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.item_cover_code")
    private Integer itemCoverCode;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   重开次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.clm_tms")
    private String clmTms;

    /**
     * Database Column Remarks:
     *   辅料核损单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_price")
    private BigDecimal auditPrice;

    /**
     * Database Column Remarks:
     *   辅料核损数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_count")
    private BigDecimal auditCount;

    /**
     * Database Column Remarks:
     *   辅料核损小计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.appr_mate_sum")
    private BigDecimal apprMateSum;

    /**
     * Database Column Remarks:
     *   核损状态（00：待处理 01：通过 02：价格异议 03：建议剔除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.check_state")
    private String checkState;

    /**
     * Database Column Remarks:
     *   核损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_remark")
    private String auditRemark;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.misc_Desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.task_no")
    public String getTaskNo() {
        return taskNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.task_no")
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo == null ? null : taskNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.assist_id")
    public String getAssistId() {
        return assistId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.assist_id")
    public void setAssistId(String assistId) {
        this.assistId = assistId == null ? null : assistId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.item_name")
    public String getItemName() {
        return itemName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.item_name")
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.count")
    public BigDecimal getCount() {
        return count;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.count")
    public void setCount(BigDecimal count) {
        this.count = count;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.material_fee")
    public BigDecimal getMaterialFee() {
        return materialFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.material_fee")
    public void setMaterialFee(BigDecimal materialFee) {
        this.materialFee = materialFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.eval_mate_sum")
    public BigDecimal getEvalMateSum() {
        return evalMateSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.eval_mate_sum")
    public void setEvalMateSum(BigDecimal evalMateSum) {
        this.evalMateSum = evalMateSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.self_config_flag")
    public Integer getSelfConfigFlag() {
        return selfConfigFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.self_config_flag")
    public void setSelfConfigFlag(Integer selfConfigFlag) {
        this.selfConfigFlag = selfConfigFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.item_cover_code")
    public Integer getItemCoverCode() {
        return itemCoverCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.item_cover_code")
    public void setItemCoverCode(Integer itemCoverCode) {
        this.itemCoverCode = itemCoverCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.clm_tms")
    public String getClmTms() {
        return clmTms;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.clm_tms")
    public void setClmTms(String clmTms) {
        this.clmTms = clmTms == null ? null : clmTms.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_price")
    public BigDecimal getAuditPrice() {
        return auditPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_price")
    public void setAuditPrice(BigDecimal auditPrice) {
        this.auditPrice = auditPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_count")
    public BigDecimal getAuditCount() {
        return auditCount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_count")
    public void setAuditCount(BigDecimal auditCount) {
        this.auditCount = auditCount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.appr_mate_sum")
    public BigDecimal getApprMateSum() {
        return apprMateSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.appr_mate_sum")
    public void setApprMateSum(BigDecimal apprMateSum) {
        this.apprMateSum = apprMateSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.check_state")
    public String getCheckState() {
        return checkState;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.check_state")
    public void setCheckState(String checkState) {
        this.checkState = checkState == null ? null : checkState.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_remark")
    public String getAuditRemark() {
        return auditRemark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_remark")
    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark == null ? null : auditRemark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.misc_Desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.misc_Desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", assistId=").append(assistId);
        sb.append(", itemName=").append(itemName);
        sb.append(", count=").append(count);
        sb.append(", materialFee=").append(materialFee);
        sb.append(", evalMateSum=").append(evalMateSum);
        sb.append(", selfConfigFlag=").append(selfConfigFlag);
        sb.append(", itemCoverCode=").append(itemCoverCode);
        sb.append(", remark=").append(remark);
        sb.append(", clmTms=").append(clmTms);
        sb.append(", auditPrice=").append(auditPrice);
        sb.append(", auditCount=").append(auditCount);
        sb.append(", apprMateSum=").append(apprMateSum);
        sb.append(", checkState=").append(checkState);
        sb.append(", auditRemark=").append(auditRemark);
        sb.append(", status=").append(status);
        sb.append(", miscDesc=").append(miscDesc);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}