package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "修理项目列表查询DTO")
public class RepairProjectQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "所属单位ID")
    private String orgId;

    @ApiModelProperty(value = "修理名称")
    private String repairName;

    @ApiModelProperty(value = "修理类型分组ID")
    private Long groupingId;

    @ApiModelProperty(value = "状态（1：有效 0：无效）")
    private Integer status;
}