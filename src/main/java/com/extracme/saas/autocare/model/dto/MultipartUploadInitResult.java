package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分片上传初始化结果
 */
@Data
@ApiModel(description = "分片上传初始化结果")
public class MultipartUploadInitResult {

    @ApiModelProperty(value = "分片上传ID", required = true)
    private String uploadId;

    @ApiModelProperty(value = "文件相对路径", required = true)
    private String relativePath;

    @ApiModelProperty(value = "存储桶名称", required = true)
    private String bucketName;

    @ApiModelProperty(value = "建议的分片大小（字节）", example = "5242880")
    private Long suggestedPartSize;

    @ApiModelProperty(value = "最大分片数量", example = "10000")
    private Integer maxPartCount;

    @ApiModelProperty(value = "初始化时间戳")
    private Long timestamp;

    public MultipartUploadInitResult() {
        this.timestamp = System.currentTimeMillis();
        this.suggestedPartSize = 5 * 1024 * 1024L; // 默认5MB
        this.maxPartCount = 10000; // OSS最大分片数量
    }

    public MultipartUploadInitResult(String uploadId, String relativePath, String bucketName) {
        this();
        this.uploadId = uploadId;
        this.relativePath = relativePath;
        this.bucketName = bucketName;
    }
}
