package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "维修图片明细DTO")
public class RepairPicturesDTO {

    @ApiModelProperty(value = "损坏部位图片", notes = "对应picType=1")
    private List<FileDTO> damagedPartPicture;

    @ApiModelProperty(value = "维修图片（验收图片）", notes = "对应picType=2")
    private List<FileDTO> repairPicture;

    @ApiModelProperty(value = "创建图片", notes = "对应picType=3")
    private List<FileDTO> createPicture;

    @ApiModelProperty(value = "创建视频", notes = "对应picType=4")
    private List<FileDTO> createVideo;

    @ApiModelProperty(value = "行驶证图片", notes = "对应picType=10")
    private List<FileDTO> drivingLicensePicture;

    @ApiModelProperty(value = "保单图片", notes = "对应picType=11")
    private List<FileDTO> policyPicture;

    @ApiModelProperty(value = "事故图片", notes = "对应picType=12")
    private List<FileDTO> accidentPicture;

    @ApiModelProperty(value = "车损图片(标的车)", notes = "对应picType=13")
    private List<FileDTO> damageAPicture;

    @ApiModelProperty(value = "车损图片(三者车)", notes = "对应picType=14")
    private List<FileDTO> damageBPicture;

    @ApiModelProperty(value = "理赔材料", notes = "对应picType=15")
    private List<FileDTO> claimsPicture;

    @ApiModelProperty(value = "其他图片", notes = "对应picType=16")
    private List<FileDTO> otherPicture;

    @ApiModelProperty(value = "验收视频", notes = "对应picType=17")
    private List<FileDTO> checkVideo;

    @ApiModelProperty(value = "维修后图片", notes = "对应picType=18")
    private List<FileDTO> afterPic;

    @ApiModelProperty(value = "事故责任认定书图片", notes = "对应picType=19")
    private List<FileDTO> accidentLiabilityConfirmationPicture;

    @ApiModelProperty(value = "保司定损单图片", notes = "对应picType=20")
    private List<FileDTO> insuranceCompanyLossOrderPicture;

    @ApiModelProperty(value = "我方驾驶证图片", notes = "对应picType=21")
    private List<FileDTO> ourDriverLicensePicture;

    @ApiModelProperty(value = "客户直付凭证", notes = "对应picType=22")
    private List<FileDTO> custPicture;

    @ApiModelProperty(value = "损坏部位视频", notes = "对应picType=23")
    private List<FileDTO> damagedPartVideo;
}