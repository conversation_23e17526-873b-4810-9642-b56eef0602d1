package com.extracme.saas.autocare.model.vo.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动节点定义视图对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("活动节点定义视图")
public class ActivityDefinitionVO {

    /**
     * 活动节点编号
     */
    @ApiModelProperty("活动节点编号")
    private Long id;

    /**
     * 活动编码
     */
    @ApiModelProperty("活动编码")
    private String activityCode;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String activityName;
}
