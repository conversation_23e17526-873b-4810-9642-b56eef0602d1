package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   操作日志表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table sys_operation_log
 */
public class SysOperationLog implements Serializable {
    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   操作人ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.user_id")
    private Long userId;

    /**
     * Database Column Remarks:
     *   操作人用户名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.username")
    private String username;

    /**
     * Database Column Remarks:
     *   操作人所属机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.org_id")
    private Long orgId;

    /**
     * Database Column Remarks:
     *   租户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.tenant_id")
    private Long tenantId;

    /**
     * Database Column Remarks:
     *   操作模块
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.module")
    private String module;

    /**
     * Database Column Remarks:
     *   操作类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.operation")
    private String operation;

    /**
     * Database Column Remarks:
     *   请求方法
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.method")
    private String method;

    /**
     * Database Column Remarks:
     *   操作IP
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.ip")
    private String ip;

    /**
     * Database Column Remarks:
     *   操作地点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.location")
    private String location;

    /**
     * Database Column Remarks:
     *   用户代理
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.user_agent")
    private String userAgent;

    /**
     * Database Column Remarks:
     *   操作状态：0-失败，1-成功
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.status")
    private Boolean status;

    /**
     * Database Column Remarks:
     *   操作时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.operation_time")
    private Date operationTime;

    /**
     * Database Column Remarks:
     *   请求参数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.params")
    private String params;

    /**
     * Database Column Remarks:
     *   错误消息
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.error_msg")
    private String errorMsg;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.user_id")
    public Long getUserId() {
        return userId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.user_id")
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.username")
    public String getUsername() {
        return username;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.username")
    public void setUsername(String username) {
        this.username = username == null ? null : username.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.org_id")
    public Long getOrgId() {
        return orgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.org_id")
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.tenant_id")
    public Long getTenantId() {
        return tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.tenant_id")
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.module")
    public String getModule() {
        return module;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.module")
    public void setModule(String module) {
        this.module = module == null ? null : module.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.operation")
    public String getOperation() {
        return operation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.operation")
    public void setOperation(String operation) {
        this.operation = operation == null ? null : operation.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.method")
    public String getMethod() {
        return method;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.method")
    public void setMethod(String method) {
        this.method = method == null ? null : method.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.ip")
    public String getIp() {
        return ip;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.ip")
    public void setIp(String ip) {
        this.ip = ip == null ? null : ip.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.location")
    public String getLocation() {
        return location;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.location")
    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.user_agent")
    public String getUserAgent() {
        return userAgent;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.user_agent")
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent == null ? null : userAgent.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.status")
    public Boolean getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.status")
    public void setStatus(Boolean status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.operation_time")
    public Date getOperationTime() {
        return operationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.operation_time")
    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.params")
    public String getParams() {
        return params;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.params")
    public void setParams(String params) {
        this.params = params == null ? null : params.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.error_msg")
    public String getErrorMsg() {
        return errorMsg;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.error_msg")
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg == null ? null : errorMsg.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", username=").append(username);
        sb.append(", orgId=").append(orgId);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", module=").append(module);
        sb.append(", operation=").append(operation);
        sb.append(", method=").append(method);
        sb.append(", ip=").append(ip);
        sb.append(", location=").append(location);
        sb.append(", userAgent=").append(userAgent);
        sb.append(", status=").append(status);
        sb.append(", operationTime=").append(operationTime);
        sb.append(", params=").append(params);
        sb.append(", errorMsg=").append(errorMsg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}