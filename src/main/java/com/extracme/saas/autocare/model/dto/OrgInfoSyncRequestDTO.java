package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 机构信息同步请求DTO
 */
@Data
@ApiModel(description = "机构信息同步请求DTO")
public class OrgInfoSyncRequestDTO {

    @ApiModelProperty(value = "同步密钥（用于租户身份识别）", required = true)
    @NotNull(message = "同步密钥不能为空")
    private String syncKey;

    @ApiModelProperty(value = "批量同步数据列表", required = true)
    @NotEmpty(message = "同步数据列表不能为空")
    @Valid
    private List<OrgInfoSyncDataDTO> batchData;

    /**
     * 机构信息同步数据DTO
     */
    @Data
    @ApiModel(description = "机构信息同步数据DTO")
    public static class OrgInfoSyncDataDTO {

        @ApiModelProperty(value = "唯一标识", required = true, example = "ORG001")
        @NotBlank(message = "唯一标识不能为空")
        private String uniqueId;

        @ApiModelProperty(value = "机构ID", required = true, example = "ORG001")
        @NotBlank(message = "机构ID不能为空")
        private String orgId;

        @ApiModelProperty(value = "机构名称", required = true, example = "上海分公司")
        @NotBlank(message = "机构名称不能为空")
        private String orgName;

        @ApiModelProperty(value = "父资源id", example = "1")
        private String parentId;
    }
}
