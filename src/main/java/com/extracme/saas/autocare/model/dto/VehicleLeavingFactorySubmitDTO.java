package com.extracme.saas.autocare.model.dto;

import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 提交车辆出厂登记DTO
 *
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Data
@ApiModel(description = "提交车辆出厂登记DTO")
public class VehicleLeavingFactorySubmitDTO {

    /**
     * 出厂登记ID
     */
    @ApiModelProperty(value = "出厂登记ID", example = "1001", required = true)
    private Long leavingFactoryId;

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", example = "TASK20240525001", required = true)
    private String taskNo;
    
    /**
     * 提车人姓名
     */
    @ApiModelProperty(value = "提车人姓名", example = "张三", required = true)
    private String name;
    
    /**
     * 提车人联系方式
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    @ApiModelProperty(value = "提车人联系方式", example = "13800138000", required = true)
    private String phoneNumber;
    
    /**
     * 车辆出厂时间
     */
    @NotNull(message = "车辆出厂时间不能为空")
    @ApiModelProperty(value = "车辆出厂时间", example = "2024-05-25 14:30:00", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryTime;              
    
    /**
     * 出厂图片
     */
    @ApiModelProperty(value = "出厂图片", example = "[\"https://example.com/image1.jpg\"]", required = true)
    private List<String> deliveryPictures;
    
    /**
     * 提车凭证
     */
    @ApiModelProperty(value = "提车凭证", example = "[\"https://example.com/voucher1.jpg\"]")
    private List<String> takeVouchers;

    /**
     * 备注
     */
    @Length(max = 1000, message = "备注长度不能超过1000")
    @ApiModelProperty(value = "备注", example = "车辆已完成维修，可以提车")
    private String remark;
}
