package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   定损修理信息
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_loss_repair_info
 */
public class MtcLossRepairInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.task_no")
    private String taskNo;

    /**
     * Database Column Remarks:
     *   定损明细主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_id")
    private String repairId;

    /**
     * Database Column Remarks:
     *   修理方式代码（1：喷漆项目 2：钣金项目 3：电工项目 4：机修项目 5：拆装项目）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_mode_code")
    private String repairModeCode;

    /**
     * Database Column Remarks:
     *   项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.item_name")
    private String itemName;

    /**
     * Database Column Remarks:
     *   工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.manpower_fee")
    private BigDecimal manpowerFee;

    /**
     * Database Column Remarks:
     *   工时参考价格
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.manpower_ref_fee")
    private BigDecimal manpowerRefFee;

    /**
     * Database Column Remarks:
     *   自定义修理标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.self_config_flag")
    private Integer selfConfigFlag;

    /**
     * Database Column Remarks:
     *   险种代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.item_cover_code")
    private Integer itemCoverCode;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   重开次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.clm_tms")
    private String clmTms;

    /**
     * Database Column Remarks:
     *   工时数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.eval_hour")
    private BigDecimal evalHour;

    /**
     * Database Column Remarks:
     *   工时单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_unit_price")
    private BigDecimal repairUnitPrice;

    /**
     * Database Column Remarks:
     *   损失程度代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_level_code")
    private String repairLevelCode;

    /**
     * Database Column Remarks:
     *   损失程度名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_level_name")
    private String repairLevelName;

    /**
     * Database Column Remarks:
     *   核损工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.audit_manpower_fee")
    private BigDecimal auditManpowerFee;

    /**
     * Database Column Remarks:
     *   核损工时数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.appr_hour")
    private BigDecimal apprHour;

    /**
     * Database Column Remarks:
     *   审核状态（00：待处理 01：通过 02：价格异议 03：建议剔除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.check_state")
    private String checkState;

    /**
     * Database Column Remarks:
     *   核损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.audit_remark")
    private String auditRemark;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.misc_Desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.task_no")
    public String getTaskNo() {
        return taskNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.task_no")
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo == null ? null : taskNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_id")
    public String getRepairId() {
        return repairId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_id")
    public void setRepairId(String repairId) {
        this.repairId = repairId == null ? null : repairId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_mode_code")
    public String getRepairModeCode() {
        return repairModeCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_mode_code")
    public void setRepairModeCode(String repairModeCode) {
        this.repairModeCode = repairModeCode == null ? null : repairModeCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.item_name")
    public String getItemName() {
        return itemName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.item_name")
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.manpower_fee")
    public BigDecimal getManpowerFee() {
        return manpowerFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.manpower_fee")
    public void setManpowerFee(BigDecimal manpowerFee) {
        this.manpowerFee = manpowerFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.manpower_ref_fee")
    public BigDecimal getManpowerRefFee() {
        return manpowerRefFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.manpower_ref_fee")
    public void setManpowerRefFee(BigDecimal manpowerRefFee) {
        this.manpowerRefFee = manpowerRefFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.self_config_flag")
    public Integer getSelfConfigFlag() {
        return selfConfigFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.self_config_flag")
    public void setSelfConfigFlag(Integer selfConfigFlag) {
        this.selfConfigFlag = selfConfigFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.item_cover_code")
    public Integer getItemCoverCode() {
        return itemCoverCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.item_cover_code")
    public void setItemCoverCode(Integer itemCoverCode) {
        this.itemCoverCode = itemCoverCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.clm_tms")
    public String getClmTms() {
        return clmTms;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.clm_tms")
    public void setClmTms(String clmTms) {
        this.clmTms = clmTms == null ? null : clmTms.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.eval_hour")
    public BigDecimal getEvalHour() {
        return evalHour;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.eval_hour")
    public void setEvalHour(BigDecimal evalHour) {
        this.evalHour = evalHour;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_unit_price")
    public BigDecimal getRepairUnitPrice() {
        return repairUnitPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_unit_price")
    public void setRepairUnitPrice(BigDecimal repairUnitPrice) {
        this.repairUnitPrice = repairUnitPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_level_code")
    public String getRepairLevelCode() {
        return repairLevelCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_level_code")
    public void setRepairLevelCode(String repairLevelCode) {
        this.repairLevelCode = repairLevelCode == null ? null : repairLevelCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_level_name")
    public String getRepairLevelName() {
        return repairLevelName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_level_name")
    public void setRepairLevelName(String repairLevelName) {
        this.repairLevelName = repairLevelName == null ? null : repairLevelName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.audit_manpower_fee")
    public BigDecimal getAuditManpowerFee() {
        return auditManpowerFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.audit_manpower_fee")
    public void setAuditManpowerFee(BigDecimal auditManpowerFee) {
        this.auditManpowerFee = auditManpowerFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.appr_hour")
    public BigDecimal getApprHour() {
        return apprHour;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.appr_hour")
    public void setApprHour(BigDecimal apprHour) {
        this.apprHour = apprHour;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.check_state")
    public String getCheckState() {
        return checkState;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.check_state")
    public void setCheckState(String checkState) {
        this.checkState = checkState == null ? null : checkState.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.audit_remark")
    public String getAuditRemark() {
        return auditRemark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.audit_remark")
    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark == null ? null : auditRemark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.misc_Desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.misc_Desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", repairId=").append(repairId);
        sb.append(", repairModeCode=").append(repairModeCode);
        sb.append(", itemName=").append(itemName);
        sb.append(", manpowerFee=").append(manpowerFee);
        sb.append(", manpowerRefFee=").append(manpowerRefFee);
        sb.append(", selfConfigFlag=").append(selfConfigFlag);
        sb.append(", itemCoverCode=").append(itemCoverCode);
        sb.append(", remark=").append(remark);
        sb.append(", clmTms=").append(clmTms);
        sb.append(", evalHour=").append(evalHour);
        sb.append(", repairUnitPrice=").append(repairUnitPrice);
        sb.append(", repairLevelCode=").append(repairLevelCode);
        sb.append(", repairLevelName=").append(repairLevelName);
        sb.append(", auditManpowerFee=").append(auditManpowerFee);
        sb.append(", apprHour=").append(apprHour);
        sb.append(", checkState=").append(checkState);
        sb.append(", auditRemark=").append(auditRemark);
        sb.append(", status=").append(status);
        sb.append(", miscDesc=").append(miscDesc);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}