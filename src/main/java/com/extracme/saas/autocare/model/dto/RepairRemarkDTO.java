package com.extracme.saas.autocare.model.dto;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维修备注数据传输对象
 */
@Data
@ApiModel(description = "维修备注DTO")
public class RepairRemarkDTO {
    
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;
    
    @ApiModelProperty(value = "维修阶段")
    private String activityCode;
    
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;
    
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
}