package com.extracme.saas.autocare.model.dto;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "获取维修数量dto")
public class GetRepairNumDTO {

    @ApiModelProperty(value = "登录机构ID集合")
    private List<String> loginOrgIds;

    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @ApiModelProperty(value = "维修厂ID")
    private String repairDepotId;

    public GetRepairNumDTO(List<String> loginOrgIds, String orgId, String repairDepotId) {
        this.loginOrgIds = loginOrgIds;
        this.orgId = orgId;
        this.repairDepotId = repairDepotId;
    }
}
