package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   定损单信息
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_loss_info
 */
public class MtcLossInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.task_no")
    private String taskNo;

    /**
     * Database Column Remarks:
     *   定损车型编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_certain_code")
    private String vehCertainCode;

    /**
     * Database Column Remarks:
     *   定损车型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_certain_name")
    private String vehCertainName;

    /**
     * Database Column Remarks:
     *   车组编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_group_code")
    private String vehGroupCode;

    /**
     * Database Column Remarks:
     *   车组名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.group_name")
    private String groupName;

    /**
     * Database Column Remarks:
     *   定损品牌编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_brand_code")
    private String vehBrandCode;

    /**
     * Database Column Remarks:
     *   定损品牌名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.brand_name")
    private String brandName;

    /**
     * Database Column Remarks:
     *   自定义车型标志（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_config_flag")
    private String selfConfigFlag;

    /**
     * Database Column Remarks:
     *   定损施救费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.salvage_fee")
    private BigDecimal salvageFee;

    /**
     * Database Column Remarks:
     *   定损折扣残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.remnant_fee")
    private BigDecimal remnantFee;

    /**
     * Database Column Remarks:
     *   定损管理费合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.manage_fee")
    private BigDecimal manageFee;

    /**
     * Database Column Remarks:
     *   定损换件合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_part_sum")
    private BigDecimal evalPartSum;

    /**
     * Database Column Remarks:
     *   定损工时合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_repair_sum")
    private BigDecimal evalRepairSum;

    /**
     * Database Column Remarks:
     *   定损辅料合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_mate_sum")
    private BigDecimal evalMateSum;

    /**
     * Database Column Remarks:
     *   定损自付合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_pay_sum")
    private BigDecimal selfPaySum;

    /**
     * Database Column Remarks:
     *   定损外修合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.outer_sum")
    private BigDecimal outerSum;

    /**
     * Database Column Remarks:
     *   定损外修减损合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.derogation_sum")
    private BigDecimal derogationSum;

    /**
     * Database Column Remarks:
     *   定损合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.sum_loss_amount")
    private BigDecimal sumLossAmount;

    /**
     * Database Column Remarks:
     *   定损员代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.handler_code")
    private String handlerCode;

    /**
     * Database Column Remarks:
     *   定损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   价格类型（1：4S价格 2：市场价格 99：其他）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.price_type")
    private String priceType;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_id")
    private String repairFacId;

    /**
     * Database Column Remarks:
     *   修理厂代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_code")
    private String repairFacCode;

    /**
     * Database Column Remarks:
     *   修理厂类型（0：综合修理厂 1：4s店修理厂）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_type")
    private String repairFacType;

    /**
     * Database Column Remarks:
     *   修理厂资质（1：一类厂 2：二类厂 3：三类厂）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.factory_qualification")
    private String factoryQualification;

    /**
     * Database Column Remarks:
     *   修理厂联系方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_phone")
    private String repairFacPhone;

    /**
     * Database Column Remarks:
     *   修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_name")
    private String repairFacName;

    /**
     * Database Column Remarks:
     *   VIN码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vin_no")
    private String vinNo;

    /**
     * Database Column Remarks:
     *   发动机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.engine_no")
    private String engineNo;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.plate_no")
    private String plateNo;

    /**
     * Database Column Remarks:
     *   初登日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.enrol_date")
    private String enrolDate;

    /**
     * Database Column Remarks:
     *   自核价标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_esti_flag")
    private String selfEstiFlag;

    /**
     * Database Column Remarks:
     *   自核损标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_approve_flag")
    private String selfApproveFlag;

    /**
     * Database Column Remarks:
     *   险别代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.insurance_code")
    private String insuranceCode;

    /**
     * Database Column Remarks:
     *   险别名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.insurance_name")
    private String insuranceName;

    /**
     * Database Column Remarks:
     *   组织机构代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.mix_code")
    private String mixCode;

    /**
     * Database Column Remarks:
     *   定型方式（1：承保车型匹配方式 2：VIN码定型方式 3：智能定型方式 4：模糊定型方式 5：VIN码定型出车型，但无理赔车型 6：自定义车型）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_setting_mode")
    private String vehicleSettingMode;

    /**
     * Database Column Remarks:
     *   定损车型与承保车型是否匹配（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.model_match_flag")
    private String modelMatchFlag;

    /**
     * Database Column Remarks:
     *   定损方式（01：修复定损 02：推定全损 03：实际全损 04：协议定损）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_type_code")
    private String evalTypeCode;

    /**
     * Database Column Remarks:
     *   事故类型（1：碰撞 2：涉水 3：火自爆 4：盗抢）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.accident_cause_code")
    private String accidentCauseCode;

    /**
     * Database Column Remarks:
     *   重开次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.clm_tms")
    private String clmTms;

    /**
     * Database Column Remarks:
     *   定损全损整车损失金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_sum")
    private BigDecimal allLoseSum;

    /**
     * Database Column Remarks:
     *   定损全损整车残值金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_remains_sum")
    private BigDecimal allLoseRemainsSum;

    /**
     * Database Column Remarks:
     *   定损全损整车施救费金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_salv_sum")
    private BigDecimal allLoseSalvSum;

    /**
     * Database Column Remarks:
     *   定损全损合计金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_total_sum")
    private BigDecimal allLoseTotalSum;

    /**
     * Database Column Remarks:
     *   换件折扣
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.part_discount_percent")
    private BigDecimal partDiscountPercent;

    /**
     * Database Column Remarks:
     *   发动机型号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.engine_type")
    private String engineType;

    /**
     * Database Column Remarks:
     *   燃料类型（01：汽油 02：柴油）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.fuel_type")
    private String fuelType;

    /**
     * Database Column Remarks:
     *   车型产地（01：国产 02：进口）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_origin")
    private String vehicleOrigin;

    /**
     * Database Column Remarks:
     *   定损车种
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_type")
    private String vehicleType;

    /**
     * Database Column Remarks:
     *   核损施救费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_salvage_fee")
    private BigDecimal auditSalvageFee;

    /**
     * Database Column Remarks:
     *   核损折扣残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_remnant_fee")
    private BigDecimal auditRemnantFee;

    /**
     * Database Column Remarks:
     *   核损换件合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_part_sum")
    private BigDecimal auditPartSum;

    /**
     * Database Column Remarks:
     *   核损工时合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_repiar_sum")
    private BigDecimal auditRepiarSum;

    /**
     * Database Column Remarks:
     *   核损辅料合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_mate_sum")
    private BigDecimal auditMateSum;

    /**
     * Database Column Remarks:
     *   核损管理费合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.total_manage_sum")
    private BigDecimal totalManageSum;

    /**
     * Database Column Remarks:
     *   核损自付合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_self_pay_sum")
    private BigDecimal auditSelfPaySum;

    /**
     * Database Column Remarks:
     *   核损外修合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_outer_sum")
    private BigDecimal auditOuterSum;

    /**
     * Database Column Remarks:
     *   核损减损合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_derogation_sum")
    private BigDecimal auditDerogationSum;

    /**
     * Database Column Remarks:
     *   核损员代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_handler_code")
    private String auditHandlerCode;

    /**
     * Database Column Remarks:
     *   核损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_remark")
    private String auditRemark;

    /**
     * Database Column Remarks:
     *   核损整单合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.total_sum")
    private BigDecimal totalSum;

    /**
     * Database Column Remarks:
     *   核损全损整车损失金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_sum")
    private BigDecimal auditAllLoseSum;

    /**
     * Database Column Remarks:
     *   核损全损整车残值金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_remains_sum")
    private BigDecimal auditAllLoseRemainsSum;

    /**
     * Database Column Remarks:
     *   核损全损整车施救费金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_salv_sum")
    private BigDecimal auditAllLoseSalvSum;

    /**
     * Database Column Remarks:
     *   核损全损合计金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_total_sum")
    private BigDecimal auditAllLoseTotalSum;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.misc_Desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.task_no")
    public String getTaskNo() {
        return taskNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.task_no")
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo == null ? null : taskNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_certain_code")
    public String getVehCertainCode() {
        return vehCertainCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_certain_code")
    public void setVehCertainCode(String vehCertainCode) {
        this.vehCertainCode = vehCertainCode == null ? null : vehCertainCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_certain_name")
    public String getVehCertainName() {
        return vehCertainName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_certain_name")
    public void setVehCertainName(String vehCertainName) {
        this.vehCertainName = vehCertainName == null ? null : vehCertainName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_group_code")
    public String getVehGroupCode() {
        return vehGroupCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_group_code")
    public void setVehGroupCode(String vehGroupCode) {
        this.vehGroupCode = vehGroupCode == null ? null : vehGroupCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.group_name")
    public String getGroupName() {
        return groupName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.group_name")
    public void setGroupName(String groupName) {
        this.groupName = groupName == null ? null : groupName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_brand_code")
    public String getVehBrandCode() {
        return vehBrandCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_brand_code")
    public void setVehBrandCode(String vehBrandCode) {
        this.vehBrandCode = vehBrandCode == null ? null : vehBrandCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.brand_name")
    public String getBrandName() {
        return brandName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.brand_name")
    public void setBrandName(String brandName) {
        this.brandName = brandName == null ? null : brandName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_config_flag")
    public String getSelfConfigFlag() {
        return selfConfigFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_config_flag")
    public void setSelfConfigFlag(String selfConfigFlag) {
        this.selfConfigFlag = selfConfigFlag == null ? null : selfConfigFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.salvage_fee")
    public BigDecimal getSalvageFee() {
        return salvageFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.salvage_fee")
    public void setSalvageFee(BigDecimal salvageFee) {
        this.salvageFee = salvageFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.remnant_fee")
    public BigDecimal getRemnantFee() {
        return remnantFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.remnant_fee")
    public void setRemnantFee(BigDecimal remnantFee) {
        this.remnantFee = remnantFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.manage_fee")
    public BigDecimal getManageFee() {
        return manageFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.manage_fee")
    public void setManageFee(BigDecimal manageFee) {
        this.manageFee = manageFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_part_sum")
    public BigDecimal getEvalPartSum() {
        return evalPartSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_part_sum")
    public void setEvalPartSum(BigDecimal evalPartSum) {
        this.evalPartSum = evalPartSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_repair_sum")
    public BigDecimal getEvalRepairSum() {
        return evalRepairSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_repair_sum")
    public void setEvalRepairSum(BigDecimal evalRepairSum) {
        this.evalRepairSum = evalRepairSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_mate_sum")
    public BigDecimal getEvalMateSum() {
        return evalMateSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_mate_sum")
    public void setEvalMateSum(BigDecimal evalMateSum) {
        this.evalMateSum = evalMateSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_pay_sum")
    public BigDecimal getSelfPaySum() {
        return selfPaySum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_pay_sum")
    public void setSelfPaySum(BigDecimal selfPaySum) {
        this.selfPaySum = selfPaySum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.outer_sum")
    public BigDecimal getOuterSum() {
        return outerSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.outer_sum")
    public void setOuterSum(BigDecimal outerSum) {
        this.outerSum = outerSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.derogation_sum")
    public BigDecimal getDerogationSum() {
        return derogationSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.derogation_sum")
    public void setDerogationSum(BigDecimal derogationSum) {
        this.derogationSum = derogationSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.sum_loss_amount")
    public BigDecimal getSumLossAmount() {
        return sumLossAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.sum_loss_amount")
    public void setSumLossAmount(BigDecimal sumLossAmount) {
        this.sumLossAmount = sumLossAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.handler_code")
    public String getHandlerCode() {
        return handlerCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.handler_code")
    public void setHandlerCode(String handlerCode) {
        this.handlerCode = handlerCode == null ? null : handlerCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.price_type")
    public String getPriceType() {
        return priceType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.price_type")
    public void setPriceType(String priceType) {
        this.priceType = priceType == null ? null : priceType.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_id")
    public String getRepairFacId() {
        return repairFacId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_id")
    public void setRepairFacId(String repairFacId) {
        this.repairFacId = repairFacId == null ? null : repairFacId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_code")
    public String getRepairFacCode() {
        return repairFacCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_code")
    public void setRepairFacCode(String repairFacCode) {
        this.repairFacCode = repairFacCode == null ? null : repairFacCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_type")
    public String getRepairFacType() {
        return repairFacType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_type")
    public void setRepairFacType(String repairFacType) {
        this.repairFacType = repairFacType == null ? null : repairFacType.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.factory_qualification")
    public String getFactoryQualification() {
        return factoryQualification;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.factory_qualification")
    public void setFactoryQualification(String factoryQualification) {
        this.factoryQualification = factoryQualification == null ? null : factoryQualification.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_phone")
    public String getRepairFacPhone() {
        return repairFacPhone;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_phone")
    public void setRepairFacPhone(String repairFacPhone) {
        this.repairFacPhone = repairFacPhone == null ? null : repairFacPhone.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_name")
    public String getRepairFacName() {
        return repairFacName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_name")
    public void setRepairFacName(String repairFacName) {
        this.repairFacName = repairFacName == null ? null : repairFacName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vin_no")
    public String getVinNo() {
        return vinNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vin_no")
    public void setVinNo(String vinNo) {
        this.vinNo = vinNo == null ? null : vinNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.engine_no")
    public String getEngineNo() {
        return engineNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.engine_no")
    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo == null ? null : engineNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.plate_no")
    public String getPlateNo() {
        return plateNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.plate_no")
    public void setPlateNo(String plateNo) {
        this.plateNo = plateNo == null ? null : plateNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.enrol_date")
    public String getEnrolDate() {
        return enrolDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.enrol_date")
    public void setEnrolDate(String enrolDate) {
        this.enrolDate = enrolDate == null ? null : enrolDate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_esti_flag")
    public String getSelfEstiFlag() {
        return selfEstiFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_esti_flag")
    public void setSelfEstiFlag(String selfEstiFlag) {
        this.selfEstiFlag = selfEstiFlag == null ? null : selfEstiFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_approve_flag")
    public String getSelfApproveFlag() {
        return selfApproveFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_approve_flag")
    public void setSelfApproveFlag(String selfApproveFlag) {
        this.selfApproveFlag = selfApproveFlag == null ? null : selfApproveFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.insurance_code")
    public String getInsuranceCode() {
        return insuranceCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.insurance_code")
    public void setInsuranceCode(String insuranceCode) {
        this.insuranceCode = insuranceCode == null ? null : insuranceCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.insurance_name")
    public String getInsuranceName() {
        return insuranceName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.insurance_name")
    public void setInsuranceName(String insuranceName) {
        this.insuranceName = insuranceName == null ? null : insuranceName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.mix_code")
    public String getMixCode() {
        return mixCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.mix_code")
    public void setMixCode(String mixCode) {
        this.mixCode = mixCode == null ? null : mixCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_setting_mode")
    public String getVehicleSettingMode() {
        return vehicleSettingMode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_setting_mode")
    public void setVehicleSettingMode(String vehicleSettingMode) {
        this.vehicleSettingMode = vehicleSettingMode == null ? null : vehicleSettingMode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.model_match_flag")
    public String getModelMatchFlag() {
        return modelMatchFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.model_match_flag")
    public void setModelMatchFlag(String modelMatchFlag) {
        this.modelMatchFlag = modelMatchFlag == null ? null : modelMatchFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_type_code")
    public String getEvalTypeCode() {
        return evalTypeCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_type_code")
    public void setEvalTypeCode(String evalTypeCode) {
        this.evalTypeCode = evalTypeCode == null ? null : evalTypeCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.accident_cause_code")
    public String getAccidentCauseCode() {
        return accidentCauseCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.accident_cause_code")
    public void setAccidentCauseCode(String accidentCauseCode) {
        this.accidentCauseCode = accidentCauseCode == null ? null : accidentCauseCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.clm_tms")
    public String getClmTms() {
        return clmTms;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.clm_tms")
    public void setClmTms(String clmTms) {
        this.clmTms = clmTms == null ? null : clmTms.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_sum")
    public BigDecimal getAllLoseSum() {
        return allLoseSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_sum")
    public void setAllLoseSum(BigDecimal allLoseSum) {
        this.allLoseSum = allLoseSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_remains_sum")
    public BigDecimal getAllLoseRemainsSum() {
        return allLoseRemainsSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_remains_sum")
    public void setAllLoseRemainsSum(BigDecimal allLoseRemainsSum) {
        this.allLoseRemainsSum = allLoseRemainsSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_salv_sum")
    public BigDecimal getAllLoseSalvSum() {
        return allLoseSalvSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_salv_sum")
    public void setAllLoseSalvSum(BigDecimal allLoseSalvSum) {
        this.allLoseSalvSum = allLoseSalvSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_total_sum")
    public BigDecimal getAllLoseTotalSum() {
        return allLoseTotalSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_total_sum")
    public void setAllLoseTotalSum(BigDecimal allLoseTotalSum) {
        this.allLoseTotalSum = allLoseTotalSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.part_discount_percent")
    public BigDecimal getPartDiscountPercent() {
        return partDiscountPercent;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.part_discount_percent")
    public void setPartDiscountPercent(BigDecimal partDiscountPercent) {
        this.partDiscountPercent = partDiscountPercent;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.engine_type")
    public String getEngineType() {
        return engineType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.engine_type")
    public void setEngineType(String engineType) {
        this.engineType = engineType == null ? null : engineType.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.fuel_type")
    public String getFuelType() {
        return fuelType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.fuel_type")
    public void setFuelType(String fuelType) {
        this.fuelType = fuelType == null ? null : fuelType.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_origin")
    public String getVehicleOrigin() {
        return vehicleOrigin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_origin")
    public void setVehicleOrigin(String vehicleOrigin) {
        this.vehicleOrigin = vehicleOrigin == null ? null : vehicleOrigin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_type")
    public String getVehicleType() {
        return vehicleType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_type")
    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType == null ? null : vehicleType.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_salvage_fee")
    public BigDecimal getAuditSalvageFee() {
        return auditSalvageFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_salvage_fee")
    public void setAuditSalvageFee(BigDecimal auditSalvageFee) {
        this.auditSalvageFee = auditSalvageFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_remnant_fee")
    public BigDecimal getAuditRemnantFee() {
        return auditRemnantFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_remnant_fee")
    public void setAuditRemnantFee(BigDecimal auditRemnantFee) {
        this.auditRemnantFee = auditRemnantFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_part_sum")
    public BigDecimal getAuditPartSum() {
        return auditPartSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_part_sum")
    public void setAuditPartSum(BigDecimal auditPartSum) {
        this.auditPartSum = auditPartSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_repiar_sum")
    public BigDecimal getAuditRepiarSum() {
        return auditRepiarSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_repiar_sum")
    public void setAuditRepiarSum(BigDecimal auditRepiarSum) {
        this.auditRepiarSum = auditRepiarSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_mate_sum")
    public BigDecimal getAuditMateSum() {
        return auditMateSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_mate_sum")
    public void setAuditMateSum(BigDecimal auditMateSum) {
        this.auditMateSum = auditMateSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.total_manage_sum")
    public BigDecimal getTotalManageSum() {
        return totalManageSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.total_manage_sum")
    public void setTotalManageSum(BigDecimal totalManageSum) {
        this.totalManageSum = totalManageSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_self_pay_sum")
    public BigDecimal getAuditSelfPaySum() {
        return auditSelfPaySum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_self_pay_sum")
    public void setAuditSelfPaySum(BigDecimal auditSelfPaySum) {
        this.auditSelfPaySum = auditSelfPaySum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_outer_sum")
    public BigDecimal getAuditOuterSum() {
        return auditOuterSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_outer_sum")
    public void setAuditOuterSum(BigDecimal auditOuterSum) {
        this.auditOuterSum = auditOuterSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_derogation_sum")
    public BigDecimal getAuditDerogationSum() {
        return auditDerogationSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_derogation_sum")
    public void setAuditDerogationSum(BigDecimal auditDerogationSum) {
        this.auditDerogationSum = auditDerogationSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_handler_code")
    public String getAuditHandlerCode() {
        return auditHandlerCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_handler_code")
    public void setAuditHandlerCode(String auditHandlerCode) {
        this.auditHandlerCode = auditHandlerCode == null ? null : auditHandlerCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_remark")
    public String getAuditRemark() {
        return auditRemark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_remark")
    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark == null ? null : auditRemark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.total_sum")
    public BigDecimal getTotalSum() {
        return totalSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.total_sum")
    public void setTotalSum(BigDecimal totalSum) {
        this.totalSum = totalSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_sum")
    public BigDecimal getAuditAllLoseSum() {
        return auditAllLoseSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_sum")
    public void setAuditAllLoseSum(BigDecimal auditAllLoseSum) {
        this.auditAllLoseSum = auditAllLoseSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_remains_sum")
    public BigDecimal getAuditAllLoseRemainsSum() {
        return auditAllLoseRemainsSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_remains_sum")
    public void setAuditAllLoseRemainsSum(BigDecimal auditAllLoseRemainsSum) {
        this.auditAllLoseRemainsSum = auditAllLoseRemainsSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_salv_sum")
    public BigDecimal getAuditAllLoseSalvSum() {
        return auditAllLoseSalvSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_salv_sum")
    public void setAuditAllLoseSalvSum(BigDecimal auditAllLoseSalvSum) {
        this.auditAllLoseSalvSum = auditAllLoseSalvSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_total_sum")
    public BigDecimal getAuditAllLoseTotalSum() {
        return auditAllLoseTotalSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_total_sum")
    public void setAuditAllLoseTotalSum(BigDecimal auditAllLoseTotalSum) {
        this.auditAllLoseTotalSum = auditAllLoseTotalSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.misc_Desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.misc_Desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", vehCertainCode=").append(vehCertainCode);
        sb.append(", vehCertainName=").append(vehCertainName);
        sb.append(", vehGroupCode=").append(vehGroupCode);
        sb.append(", groupName=").append(groupName);
        sb.append(", vehBrandCode=").append(vehBrandCode);
        sb.append(", brandName=").append(brandName);
        sb.append(", selfConfigFlag=").append(selfConfigFlag);
        sb.append(", salvageFee=").append(salvageFee);
        sb.append(", remnantFee=").append(remnantFee);
        sb.append(", manageFee=").append(manageFee);
        sb.append(", evalPartSum=").append(evalPartSum);
        sb.append(", evalRepairSum=").append(evalRepairSum);
        sb.append(", evalMateSum=").append(evalMateSum);
        sb.append(", selfPaySum=").append(selfPaySum);
        sb.append(", outerSum=").append(outerSum);
        sb.append(", derogationSum=").append(derogationSum);
        sb.append(", sumLossAmount=").append(sumLossAmount);
        sb.append(", handlerCode=").append(handlerCode);
        sb.append(", remark=").append(remark);
        sb.append(", priceType=").append(priceType);
        sb.append(", repairFacId=").append(repairFacId);
        sb.append(", repairFacCode=").append(repairFacCode);
        sb.append(", repairFacType=").append(repairFacType);
        sb.append(", factoryQualification=").append(factoryQualification);
        sb.append(", repairFacPhone=").append(repairFacPhone);
        sb.append(", repairFacName=").append(repairFacName);
        sb.append(", vinNo=").append(vinNo);
        sb.append(", engineNo=").append(engineNo);
        sb.append(", plateNo=").append(plateNo);
        sb.append(", enrolDate=").append(enrolDate);
        sb.append(", selfEstiFlag=").append(selfEstiFlag);
        sb.append(", selfApproveFlag=").append(selfApproveFlag);
        sb.append(", insuranceCode=").append(insuranceCode);
        sb.append(", insuranceName=").append(insuranceName);
        sb.append(", mixCode=").append(mixCode);
        sb.append(", vehicleSettingMode=").append(vehicleSettingMode);
        sb.append(", modelMatchFlag=").append(modelMatchFlag);
        sb.append(", evalTypeCode=").append(evalTypeCode);
        sb.append(", accidentCauseCode=").append(accidentCauseCode);
        sb.append(", clmTms=").append(clmTms);
        sb.append(", allLoseSum=").append(allLoseSum);
        sb.append(", allLoseRemainsSum=").append(allLoseRemainsSum);
        sb.append(", allLoseSalvSum=").append(allLoseSalvSum);
        sb.append(", allLoseTotalSum=").append(allLoseTotalSum);
        sb.append(", partDiscountPercent=").append(partDiscountPercent);
        sb.append(", engineType=").append(engineType);
        sb.append(", fuelType=").append(fuelType);
        sb.append(", vehicleOrigin=").append(vehicleOrigin);
        sb.append(", vehicleType=").append(vehicleType);
        sb.append(", auditSalvageFee=").append(auditSalvageFee);
        sb.append(", auditRemnantFee=").append(auditRemnantFee);
        sb.append(", auditPartSum=").append(auditPartSum);
        sb.append(", auditRepiarSum=").append(auditRepiarSum);
        sb.append(", auditMateSum=").append(auditMateSum);
        sb.append(", totalManageSum=").append(totalManageSum);
        sb.append(", auditSelfPaySum=").append(auditSelfPaySum);
        sb.append(", auditOuterSum=").append(auditOuterSum);
        sb.append(", auditDerogationSum=").append(auditDerogationSum);
        sb.append(", auditHandlerCode=").append(auditHandlerCode);
        sb.append(", auditRemark=").append(auditRemark);
        sb.append(", totalSum=").append(totalSum);
        sb.append(", auditAllLoseSum=").append(auditAllLoseSum);
        sb.append(", auditAllLoseRemainsSum=").append(auditAllLoseRemainsSum);
        sb.append(", auditAllLoseSalvSum=").append(auditAllLoseSalvSum);
        sb.append(", auditAllLoseTotalSum=").append(auditAllLoseTotalSum);
        sb.append(", status=").append(status);
        sb.append(", miscDesc=").append(miscDesc);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}