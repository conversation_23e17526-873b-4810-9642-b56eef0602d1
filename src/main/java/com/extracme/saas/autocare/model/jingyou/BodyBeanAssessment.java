package com.extracme.saas.autocare.model.jingyou;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:09
 */
public class BodyBeanAssessment {
    /**
     * 定损单信息
     */
    private ReqInfoAssessment reqInfoAssessment;
    /**
     * 定损信息
     */
    private EvalLossInfoAssessment evalLossInfoAssessment;
    /**
     *
     */
    private FactoryInfo factoryInfo;
    /**
     *
     */
    private List<FactoryBrand> factoryBrandList;
    /**
     * 保单信息
     */
    private List<LossPolicy> lossPolicyList;
    /**
     * 承保车辆信息（标的车）
     */
    private LossCoverVehicle lossCoverVehicle;
    /**
     * 承保险别
     */
    private List<LossInsured> lossInsuredList;
    /**
     * 报案信息
     */
    private LossReporting lossReporting;

    @XmlElement(name = "ReqInfo")
    public ReqInfoAssessment getReqInfoAssessment() {
        return reqInfoAssessment;
    }

    public void setReqInfoAssessment(ReqInfoAssessment reqInfoAssessment) {
        this.reqInfoAssessment = reqInfoAssessment;
    }

    @XmlElement(name = "EvalLossInfo")
    public EvalLossInfoAssessment getEvalLossInfoAssessment() {
        return evalLossInfoAssessment;
    }

    public void setEvalLossInfoAssessment(EvalLossInfoAssessment evalLossInfoAssessment) {
        this.evalLossInfoAssessment = evalLossInfoAssessment;
    }

    @XmlElement(name = "FactoryInfo")
    public FactoryInfo getFactoryInfo() {
        return factoryInfo;
    }

    public void setFactoryInfo(FactoryInfo factoryInfo) {
        this.factoryInfo = factoryInfo;
    }

    @XmlElementWrapper(name = "FactoryBrand")
    @XmlElement(name = "Item")
    public List<FactoryBrand> getFactoryBrandList() {
        return factoryBrandList;
    }

    public void setFactoryBrandList(List<FactoryBrand> factoryBrandList) {
        this.factoryBrandList = factoryBrandList;
    }

    @XmlElementWrapper(name = "LossPolicy")
    @XmlElement(name = "Item")
    public List<LossPolicy> getLossPolicyList() {
        return lossPolicyList;
    }

    public void setLossPolicyList(List<LossPolicy> lossPolicyList) {
        this.lossPolicyList = lossPolicyList;
    }

    @XmlElement(name = "LossCoverVehicle")
    public LossCoverVehicle getLossCoverVehicle() {
        return lossCoverVehicle;
    }

    public void setLossCoverVehicle(LossCoverVehicle lossCoverVehicle) {
        this.lossCoverVehicle = lossCoverVehicle;
    }

    @XmlElementWrapper(name = "LossInsured")
    @XmlElement(name = "Item")
    public List<LossInsured> getLossInsuredList() {
        return lossInsuredList;
    }

    public void setLossInsuredList(List<LossInsured> lossInsuredList) {
        this.lossInsuredList = lossInsuredList;
    }

    @XmlElement(name = "LossReporting")
    public LossReporting getLossReporting() {
        return lossReporting;
    }

    public void setLossReporting(LossReporting lossReporting) {
        this.lossReporting = lossReporting;
    }
}
