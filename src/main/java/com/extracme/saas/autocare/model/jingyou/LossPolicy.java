package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-13 13:52
 */
public class LossPolicy {
    /**
     * 主键
     */
    private String id = StringUtils.EMPTY;
    /**
     * 保单号
     */
    private String policyCode = StringUtils.EMPTY;
    /**
     * 报案号
     */
    private String reportCode = StringUtils.EMPTY;
    /**
     * 保险起期
     */
    private String insureBgnDate = StringUtils.EMPTY;
    /**
     * 保险止期
     */
    private String insureEndDate = StringUtils.EMPTY;
    /**
     * 被保险人姓名
     */
    private String insuredPerson = "环球车享";
    /**
     * 承保机构代码
     */
    private String companyCode = StringUtils.EMPTY;
    /**
     * 承保机构名称
     */
    private String companyName = StringUtils.EMPTY;
    /**
     * 总保额
     */
    private String totalInsSum = StringUtils.EMPTY;
    /**
     * 险种类别
     */
    private String riskType = "DZ";
    /**
     * 险种代码
     */
    private String riskCode = StringUtils.EMPTY;
    /**
     * 险种名称
     */
    private String riskName = StringUtils.EMPTY;
    /**
     * 客户类型代码
     */
    private String customerTypeCode = StringUtils.EMPTY;
    /**
     * 客户类型名称
     */
    private String customerTypeName = StringUtils.EMPTY;
    /**
     * 业务渠道代码
     */
    private String channelCode = StringUtils.EMPTY;
    /**
     * 业务渠道名称
     */
    private String channelName = StringUtils.EMPTY;
    /**
     * 出单日期
     */
    private String billDate = StringUtils.EMPTY;
    /**
     * 投保日期
     */
    private String policyHoldDate = StringUtils.EMPTY;
    /**
     * 车辆所有人
     */
    private String carOwner = StringUtils.EMPTY;
    /**
     * 历史出险次数
     */
    private String accidentNum = StringUtils.EMPTY;

    @XmlElement(name = "Id")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @XmlElement(name = "PolicyCode")
    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    @XmlElement(name = "ReportCode")
    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }

    @XmlElement(name = "InsureBgnDate")
    public String getInsureBgnDate() {
        return insureBgnDate;
    }

    public void setInsureBgnDate(String insureBgnDate) {
        this.insureBgnDate = insureBgnDate;
    }

    @XmlElement(name = "InsureEndDate")
    public String getInsureEndDate() {
        return insureEndDate;
    }

    public void setInsureEndDate(String insureEndDate) {
        this.insureEndDate = insureEndDate;
    }

    @XmlElement(name = "InsuredPerson")
    public String getInsuredPerson() {
        return insuredPerson;
    }

    public void setInsuredPerson(String insuredPerson) {
        this.insuredPerson = insuredPerson;
    }

    @XmlElement(name = "CompanyCode")
    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    @XmlElement(name = "CompanyName")
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    @XmlElement(name = "TotalInsSum")
    public String getTotalInsSum() {
        return totalInsSum;
    }

    public void setTotalInsSum(String totalInsSum) {
        this.totalInsSum = totalInsSum;
    }

    @XmlElement(name = "RiskType")
    public String getRiskType() {
        return riskType;
    }

    public void setRiskType(String riskType) {
        this.riskType = riskType;
    }

    @XmlElement(name = "RiskCode")
    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    @XmlElement(name = "RiskName")
    public String getRiskName() {
        return riskName;
    }

    public void setRiskName(String riskName) {
        this.riskName = riskName;
    }

    @XmlElement(name = "CustomerTypeCode")
    public String getCustomerTypeCode() {
        return customerTypeCode;
    }

    public void setCustomerTypeCode(String customerTypeCode) {
        this.customerTypeCode = customerTypeCode;
    }

    @XmlElement(name = "CustomerTypeName")
    public String getCustomerTypeName() {
        return customerTypeName;
    }

    public void setCustomerTypeName(String customerTypeName) {
        this.customerTypeName = customerTypeName;
    }

    @XmlElement(name = "ChannelCode")
    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    @XmlElement(name = "ChannelName")
    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    @XmlElement(name = "BillDate")
    public String getBillDate() {
        return billDate;
    }

    public void setBillDate(String billDate) {
        this.billDate = billDate;
    }

    @XmlElement(name = "PolicyHoldDate")
    public String getPolicyHoldDate() {
        return policyHoldDate;
    }

    public void setPolicyHoldDate(String policyHoldDate) {
        this.policyHoldDate = policyHoldDate;
    }

    @XmlElement(name = "CarOwner")
    public String getCarOwner() {
        return carOwner;
    }

    public void setCarOwner(String carOwner) {
        this.carOwner = carOwner;
    }

    @XmlElement(name = "AccidentNum")
    public String getAccidentNum() {
        return accidentNum;
    }

    public void setAccidentNum(String accidentNum) {
        this.accidentNum = accidentNum;
    }
}
