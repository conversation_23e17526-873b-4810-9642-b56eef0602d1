package com.extracme.saas.autocare.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 车辆出厂登记详情VO
 *
 * <AUTHOR>
 * @date 2024/05/25
 */
@Data
@ApiModel(description = "车辆出厂登记详情VO")
public class VehicleLeavingFactoryDetailsVO {
    
    /**
     * 出厂登记ID
     */
    @ApiModelProperty(value = "出厂登记ID", example = "1001")
    private Long leavingFactoryId;
    
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", example = "1001")
    private Long id;

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", example = "TASK20240525001")
    private String taskNo;
    
    /**
     * 提车人姓名
     */
    @ApiModelProperty(value = "提车人姓名", example = "张三")
    private String name;
    
    /**
     * 提车人联系方式
     */
    @ApiModelProperty(value = "提车人联系方式", example = "13800138000")
    private String phoneNumber;
    
    /**
     * 车辆出厂时间
     */
    @ApiModelProperty(value = "车辆出厂时间", example = "2024-05-25 14:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryTime;
    
    /**
     * 出厂图片
     */
    @ApiModelProperty(value = "出厂图片", example = "[\"https://example.com/image1.jpg\"]")
    private List<String> deliveryPictures;
    
    /**
     * 提车凭证
     */
    @ApiModelProperty(value = "提车凭证", example = "[\"https://example.com/voucher1.jpg\"]")
    private List<String> takeVouchers;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-05-25 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "车辆已完成维修，可以提车")
    private String remark;

    /**
     * 出厂状态 1:未登记 2:已登记 3:已关闭
     */
    @ApiModelProperty(value = "出厂状态", example = "2", notes = "1:未登记 2:已登记 3:已关闭")
    private Integer leavingStatus;
    
    /**
     * 出厂状态描述
     */
    @ApiModelProperty(value = "出厂状态描述", example = "已登记")
    private String leavingStatusDesc;
    
    /**
     * 维修任务流入时间
     */
    @ApiModelProperty(value = "维修任务流入时间", example = "2024-05-20 09:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date repairTaskInflowTime;

    /**
     * 维修任务接收时间
     */
    @ApiModelProperty(value = "维修任务接收时间", example = "2024-05-20 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date repairTaskReceiveTime;

    /**
     * 车辆验收时间
     */
    @ApiModelProperty(value = "车辆验收时间", example = "2024-05-25 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date vehicleCheckTime;

    /**
     * 修理厂名称
     */
    @ApiModelProperty(value = "修理厂名称", example = "北京修理厂")
    private String repairDepotName;
}
