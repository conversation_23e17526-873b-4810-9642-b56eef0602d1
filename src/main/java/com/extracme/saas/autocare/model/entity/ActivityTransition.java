package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table activity_transition
 */
public class ActivityTransition implements Serializable {
    /**
     * Database Column Remarks:
     *   转换规则编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   所属流程模板编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.workflow_id")
    private Long workflowId;

    /**
     * Database Column Remarks:
     *   转换起始节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.from_activity_code")
    private String fromActivityCode;

    /**
     * Database Column Remarks:
     *   目标节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.to_activity_code")
    private String toActivityCode;

    /**
     * Database Column Remarks:
     *   触发事件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.trigger_event")
    private String triggerEvent;

    /**
     * Database Column Remarks:
     *   条件判断处理器类名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.condition_handler")
    private String conditionHandler;

    /**
     * Database Column Remarks:
     *   处理逻辑类名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.handler_class")
    private String handlerClass;

    /**
     * Database Column Remarks:
     *   租户编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.tenant_id")
    private Integer tenantId;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   规则说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.description")
    private String description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.workflow_id")
    public Long getWorkflowId() {
        return workflowId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.workflow_id")
    public void setWorkflowId(Long workflowId) {
        this.workflowId = workflowId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.from_activity_code")
    public String getFromActivityCode() {
        return fromActivityCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.from_activity_code")
    public void setFromActivityCode(String fromActivityCode) {
        this.fromActivityCode = fromActivityCode == null ? null : fromActivityCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.to_activity_code")
    public String getToActivityCode() {
        return toActivityCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.to_activity_code")
    public void setToActivityCode(String toActivityCode) {
        this.toActivityCode = toActivityCode == null ? null : toActivityCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.trigger_event")
    public String getTriggerEvent() {
        return triggerEvent;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.trigger_event")
    public void setTriggerEvent(String triggerEvent) {
        this.triggerEvent = triggerEvent == null ? null : triggerEvent.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.condition_handler")
    public String getConditionHandler() {
        return conditionHandler;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.condition_handler")
    public void setConditionHandler(String conditionHandler) {
        this.conditionHandler = conditionHandler == null ? null : conditionHandler.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.handler_class")
    public String getHandlerClass() {
        return handlerClass;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.handler_class")
    public void setHandlerClass(String handlerClass) {
        this.handlerClass = handlerClass == null ? null : handlerClass.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.tenant_id")
    public Integer getTenantId() {
        return tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.tenant_id")
    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.description")
    public String getDescription() {
        return description;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.description")
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", workflowId=").append(workflowId);
        sb.append(", fromActivityCode=").append(fromActivityCode);
        sb.append(", toActivityCode=").append(toActivityCode);
        sb.append(", triggerEvent=").append(triggerEvent);
        sb.append(", conditionHandler=").append(conditionHandler);
        sb.append(", handlerClass=").append(handlerClass);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", description=").append(description);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}