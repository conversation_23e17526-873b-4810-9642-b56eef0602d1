package com.extracme.saas.autocare.model.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "保险索赔申请创建DTO")
public class InsuranceClaimCreateDTO {

    @ApiModelProperty(value = "车牌号")
    @NotBlank(message = "车牌号不能为空")
    private String licensePlate;

    @ApiModelProperty(value = "出险日期", example = "2025-07-03")
    @NotBlank(message = "出险日期不能为空")
    private String accidentDate;

    @ApiModelProperty(value = "情况描述")
    @NotBlank(message = "情况描述不能为空")
    private String remark;

    @ApiModelProperty(value = "需用印附件信息")
    @NotEmpty(message = "需用印附件信息不能为空")
    private List<FileDTO> attachments;

    @ApiModelProperty(value = "其他附件信息")
    private List<FileDTO> otherAttachments;
}