package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "辅料信息DTO")
public class LossAssistInfoDTO {
    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "辅料明细主键")
    private String assistId;

    @ApiModelProperty(value = "辅料名称")
    private String itemName;

    @ApiModelProperty(value = "数量")
    private BigDecimal count;

    @ApiModelProperty(value = "定损辅料单价")
    private BigDecimal materialFee;

    @ApiModelProperty(value = "定损辅料合计")
    private BigDecimal evalMateSum;

    @ApiModelProperty(value = "自定义辅料标记", notes = "0：否 1：是")
    private Integer selfConfigFlag;

    @ApiModelProperty(value = "险种代码")
    private Integer itemCoverCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "重开次数")
    private String clmTms;

    @ApiModelProperty(value = "辅料核损单价")
    private BigDecimal auditPrice;

    @ApiModelProperty(value = "辅料核损数量")
    private BigDecimal auditCount;

    @ApiModelProperty(value = "辅料核损小计")
    private BigDecimal apprMateSum;

    @ApiModelProperty(value = "核损状态", notes = "00：待处理 01：通过 02：价格异议 03：建议剔除")
    private String checkState;

    @ApiModelProperty(value = "核损备注")
    private String auditRemark;
}
