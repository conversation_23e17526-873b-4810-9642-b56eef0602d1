package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-20 15:24
 */
public class LossRepairSumInfoEvaBack {
    /**
     * 工种编码
     */
    private String workTypeCode = StringUtils.EMPTY;
    /**
     * 项目数量
     */
    private Integer itemCount = 0;
    /**
     * 核损工时费
     */
    private BigDecimal apprRepairSum = BigDecimal.ZERO;

    @XmlElement(name = "WorkTypeCode")
    public String getWorkTypeCode() {
        return workTypeCode;
    }

    public void setWorkTypeCode(String workTypeCode) {
        this.workTypeCode = workTypeCode;
    }

    @XmlElement(name = "ItemCount")
    public Integer getItemCount() {
        return itemCount;
    }

    public void setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
    }

    @XmlElement(name = "ApprRepairSum")
    public BigDecimal getApprRepairSum() {
        return apprRepairSum;
    }

    public void setApprRepairSum(BigDecimal apprRepairSum) {
        this.apprRepairSum = apprRepairSum;
    }
}
