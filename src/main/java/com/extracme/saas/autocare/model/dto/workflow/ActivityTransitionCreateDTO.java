package com.extracme.saas.autocare.model.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 活动节点转换规则创建DTO
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Data
@ApiModel(description = "活动节点转换规则创建DTO")
public class ActivityTransitionCreateDTO {

    @ApiModelProperty(value = "活动节点转换规则", required = true)
    @NotNull(message = "活动节点转换规则不能为空")
    @Valid
    private ActivityTransitionDTO activityTransition;

    @ApiModelProperty(value = "节点状态转换规则列表")
    @Valid
    private List<ActivityStatusTransitionDTO> statusTransitions;
}
