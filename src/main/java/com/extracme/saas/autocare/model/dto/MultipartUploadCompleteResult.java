package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分片上传完成结果
 */
@Data
@ApiModel(description = "分片上传完成结果")
public class MultipartUploadCompleteResult {

    @ApiModelProperty(value = "文件相对路径", required = true)
    private String relativePath;

    @ApiModelProperty(value = "文件完整访问URL", required = true)
    private String fullUrl;

    @ApiModelProperty(value = "存储桶名称", required = true)
    private String bucketName;

    @ApiModelProperty(value = "文件ETag", required = true)
    private String eTag;

    @ApiModelProperty(value = "文件总大小（字节）")
    private Long totalSize;

    @ApiModelProperty(value = "分片总数")
    private Integer totalParts;

    @ApiModelProperty(value = "完成时间戳")
    private Long timestamp;

    @ApiModelProperty(value = "租户编码")
    private String tenantCode;

    public MultipartUploadCompleteResult() {
        this.timestamp = System.currentTimeMillis();
    }

    public MultipartUploadCompleteResult(String relativePath, String fullUrl, String bucketName, String eTag) {
        this();
        this.relativePath = relativePath;
        this.fullUrl = fullUrl;
        this.bucketName = bucketName;
        this.eTag = eTag;
    }
}
