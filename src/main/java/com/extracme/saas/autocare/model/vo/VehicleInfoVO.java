package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "车辆信息DTO")
public class VehicleInfoVO {

    @ApiModelProperty(value = "主键，自增")
    private Long id;

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "车型ID")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名")
    private String vehicleModelName;

    @ApiModelProperty(value = "维修时所属公司ID")
    private String vehicleOrgId;

    @ApiModelProperty(value = "维修时所属公司名")
    private String vehicleOrgName;

    @ApiModelProperty(value = "维修时运营公司ID")
    private String operationOrgId;

    @ApiModelProperty(value = "维修时运营公司名")
    private String operationOrgName;

    @ApiModelProperty(value = "产品线(1-车管中心 2-长租 3-短租 4-公务用车)", example = "2")
    private Integer productLine;

    @ApiModelProperty(value = "维修时子产品线(1-携程短租 2-门店短租 3-分时短租 4-普通长租 5-时行长租 6-平台业务长租 7-政企业务长租 8-网约车业务长租)", example = "4")
    private Integer subProductLine;
    
    @ApiModelProperty(value = "维修时实际运营标签")
    private Integer factOperateTag;

    @ApiModelProperty(value = "资产状态")
    private Integer propertyStatus;

    @ApiModelProperty(value = "总里程")
    private Integer totalMileage;

    @ApiModelProperty(value = "发动机号")
    private String engineId;

    @ApiModelProperty(value = "注册日期")
    private Date registerDate;

    @ApiModelProperty(value = "交强险开始日期")
    private Date tciStartdate;

    @ApiModelProperty(value = "交强险结束日期")
    private Date tciEnddate;

    @ApiModelProperty(value = "保险所属id")
    private Long insuranceBelongs;

    @ApiModelProperty(value = "保险所属名")
    private String insuranceCompanyName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;
}