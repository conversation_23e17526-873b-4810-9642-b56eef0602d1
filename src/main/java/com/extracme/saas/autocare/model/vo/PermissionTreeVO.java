package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 权限树节点VO
 */
@Data
@ApiModel(description = "权限树节点VO")
public class PermissionTreeVO {

    @ApiModelProperty(value = "权限ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "权限名称", example = "系统管理")
    private String permissionName;

    @ApiModelProperty(value = "权限编码", example = "system:manage")
    private String permissionCode;

    @ApiModelProperty(value = "权限类型", example = "menu", notes = "menu-菜单，button-按钮，hiddenMenu-隐藏菜单")
    private String permissionType;

    @ApiModelProperty(value = "父级权限ID", example = "0")
    private Long parentId;

    @ApiModelProperty(value = "路由路径", example = "/system")
    private String path;

    @ApiModelProperty(value = "组件路径", example = "system/index")
    private String component;

    @ApiModelProperty(value = "图标", example = "el-icon-setting")
    private String icon;

    @ApiModelProperty(value = "排序", example = "1")
    private Integer sort;

    @ApiModelProperty(value = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    @ApiModelProperty(value = "子节点")
    private List<PermissionTreeVO> children;
}
