package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-19 17:15
 */
public class LossFitInfoAssBack {
    /**
     * 定损明细主键
     */
    private String partId = StringUtils.EMPTY;
    /**
     * 零配件原厂编码
     */
    private String partCode = StringUtils.EMPTY;
    /**
     * 项目名称
     */
    private String itemName = StringUtils.EMPTY;
    /**
     * 系统4S店价
     */
    private BigDecimal sysGuidePrice = BigDecimal.ZERO;
    /**
     * 系统市场价
     */
    private BigDecimal sysMarketPrice = BigDecimal.ZERO;
    /**
     * 本地4S店价
     */
    private BigDecimal localGuidePrice = BigDecimal.ZERO;
    /**
     * 本地市场原厂价
     */
    private BigDecimal localMarketPrice = BigDecimal.ZERO;
    /**
     * 本地品牌价
     */
    private BigDecimal localBrandPrice = BigDecimal.ZERO;
    /**
     * 本地适用价
     */
    private BigDecimal localApplicablePrice = BigDecimal.ZERO;
    /**
     * 本地再制造价
     */
    private BigDecimal localRemanufacturePrice = BigDecimal.ZERO;
    /**
     * 定损参考价
     */
    private BigDecimal localPrice = BigDecimal.ZERO;
    /**
     * 定损配件数量
     */
    private Integer count = 0;
    /**
     * 定损配件单价
     */
    private BigDecimal materialFee = BigDecimal.ZERO;
    /**
     * 自定义配件标记
     */
    private Integer selfConfigFlag = 0;
    /**
     * 险种代码
     */
    private Integer itemCoverCode = 0;
    /**
     * 备注
     */
    private String remark = StringUtils.EMPTY;
    /**
     * 参考价格类型编码
     */
    private String chgCompSetCode = StringUtils.EMPTY;
    /**
     * 回收标志
     */
    private String fitBackFlag = StringUtils.EMPTY;
    /**
     * 残值
     */
    private BigDecimal remainsPrice = BigDecimal.ZERO;
    /**
     * 待检测标志
     */
    private String detectedFlag = StringUtils.EMPTY;
    /**
     * 直供测标志
     */
    private String directSupplyFlag = StringUtils.EMPTY;
    /**
     * 直供商
     */
    private String directSupplier = StringUtils.EMPTY;
    /**
     * 管理费率
     */
    private BigDecimal manageSingleRate = BigDecimal.ZERO;
    /**
     * 管理费
     */
    private BigDecimal manageSingleFee = BigDecimal.ZERO;
    /**
     * 换件合计
     */
    private BigDecimal evalPartSum = BigDecimal.ZERO;
    /**
     * 自付比例
     */
    private BigDecimal selfPayRate = BigDecimal.ZERO;
    /**
     * 回收方式
     */
    private BigDecimal recyclePartFlag = BigDecimal.ZERO;
    /**
     * 首次定损金额
     */
    private BigDecimal evalPartSumFirst = BigDecimal.ZERO;
    /**
     * 配件角标
     */
    private String cornerMark = StringUtils.EMPTY;
    /**
     * 重开次数
     */
    private String clmTms = StringUtils.EMPTY;
    /**
     * 是否涉水
     */
    private String ifWading = StringUtils.EMPTY;
    /**
     * 复检标志
     */
    private String recheckFlag = StringUtils.EMPTY;

    @XmlElement(name = "PartId")
    public String getPartId() {
        return partId;
    }

    public void setPartId(String partId) {
        this.partId = partId;
    }

    @XmlElement(name = "PartCode")
    public String getPartCode() {
        return partCode;
    }

    public void setPartCode(String partCode) {
        this.partCode = partCode;
    }

    @XmlElement(name = "ItemName")
    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    @XmlElement(name = "SysGuidePrice")
    public BigDecimal getSysGuidePrice() {
        return sysGuidePrice;
    }

    public void setSysGuidePrice(BigDecimal sysGuidePrice) {
        this.sysGuidePrice = sysGuidePrice;
    }

    @XmlElement(name = "SysMarketPrice")
    public BigDecimal getSysMarketPrice() {
        return sysMarketPrice;
    }

    public void setSysMarketPrice(BigDecimal sysMarketPrice) {
        this.sysMarketPrice = sysMarketPrice;
    }

    @XmlElement(name = "LocalGuidePrice")
    public BigDecimal getLocalGuidePrice() {
        return localGuidePrice;
    }

    public void setLocalGuidePrice(BigDecimal localGuidePrice) {
        this.localGuidePrice = localGuidePrice;
    }

    @XmlElement(name = "LocalMarketPrice")
    public BigDecimal getLocalMarketPrice() {
        return localMarketPrice;
    }

    public void setLocalMarketPrice(BigDecimal localMarketPrice) {
        this.localMarketPrice = localMarketPrice;
    }

    @XmlElement(name = "LocalBrandPrice")
    public BigDecimal getLocalBrandPrice() {
        return localBrandPrice;
    }

    public void setLocalBrandPrice(BigDecimal localBrandPrice) {
        this.localBrandPrice = localBrandPrice;
    }

    @XmlElement(name = "LocalApplicablePrice")
    public BigDecimal getLocalApplicablePrice() {
        return localApplicablePrice;
    }

    public void setLocalApplicablePrice(BigDecimal localApplicablePrice) {
        this.localApplicablePrice = localApplicablePrice;
    }

    @XmlElement(name = "LocalRemanufacturePrice")
    public BigDecimal getLocalRemanufacturePrice() {
        return localRemanufacturePrice;
    }

    public void setLocalRemanufacturePrice(BigDecimal localRemanufacturePrice) {
        this.localRemanufacturePrice = localRemanufacturePrice;
    }

    @XmlElement(name = "LocalPrice")
    public BigDecimal getLocalPrice() {
        return localPrice;
    }

    public void setLocalPrice(BigDecimal localPrice) {
        this.localPrice = localPrice;
    }

    @XmlElement(name = "Count")
    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @XmlElement(name = "MaterialFee")
    public BigDecimal getMaterialFee() {
        return materialFee;
    }

    public void setMaterialFee(BigDecimal materialFee) {
        this.materialFee = materialFee;
    }

    @XmlElement(name = "SelfConfigFlag")
    public Integer getSelfConfigFlag() {
        return selfConfigFlag;
    }

    public void setSelfConfigFlag(Integer selfConfigFlag) {
        this.selfConfigFlag = selfConfigFlag;
    }

    @XmlElement(name = "ItemCoverCode")
    public Integer getItemCoverCode() {
        return itemCoverCode;
    }

    public void setItemCoverCode(Integer itemCoverCode) {
        this.itemCoverCode = itemCoverCode;
    }

    @XmlElement(name = "Remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @XmlElement(name = "ChgCompSetCode")
    public String getChgCompSetCode() {
        return chgCompSetCode;
    }

    public void setChgCompSetCode(String chgCompSetCode) {
        this.chgCompSetCode = chgCompSetCode;
    }

    @XmlElement(name = "FitBackFlag")
    public String getFitBackFlag() {
        return fitBackFlag;
    }

    public void setFitBackFlag(String fitBackFlag) {
        this.fitBackFlag = fitBackFlag;
    }

    @XmlElement(name = "FitBackFlag")
    public BigDecimal getRemainsPrice() {
        return remainsPrice;
    }

    public void setRemainsPrice(BigDecimal remainsPrice) {
        this.remainsPrice = remainsPrice;
    }

    @XmlElement(name = "DetectedFlag")
    public String getDetectedFlag() {
        return detectedFlag;
    }

    public void setDetectedFlag(String detectedFlag) {
        this.detectedFlag = detectedFlag;
    }

    @XmlElement(name = "DirectSupplyFlag")
    public String getDirectSupplyFlag() {
        return directSupplyFlag;
    }

    public void setDirectSupplyFlag(String directSupplyFlag) {
        this.directSupplyFlag = directSupplyFlag;
    }

    @XmlElement(name = "DirectSupplier")
    public String getDirectSupplier() {
        return directSupplier;
    }

    public void setDirectSupplier(String directSupplier) {
        this.directSupplier = directSupplier;
    }

    @XmlElement(name = "ManageSingleRate")
    public BigDecimal getManageSingleRate() {
        return manageSingleRate;
    }

    public void setManageSingleRate(BigDecimal manageSingleRate) {
        this.manageSingleRate = manageSingleRate;
    }

    @XmlElement(name = "ManageSingleFee")
    public BigDecimal getManageSingleFee() {
        return manageSingleFee;
    }

    public void setManageSingleFee(BigDecimal manageSingleFee) {
        this.manageSingleFee = manageSingleFee;
    }

    @XmlElement(name = "EvalPartSum")
    public BigDecimal getEvalPartSum() {
        return evalPartSum;
    }

    public void setEvalPartSum(BigDecimal evalPartSum) {
        this.evalPartSum = evalPartSum;
    }

    @XmlElement(name = "SelfPayRate")
    public BigDecimal getSelfPayRate() {
        return selfPayRate;
    }

    public void setSelfPayRate(BigDecimal selfPayRate) {
        this.selfPayRate = selfPayRate;
    }

    @XmlElement(name = "RecyclePartFlag")
    public BigDecimal getRecyclePartFlag() {
        return recyclePartFlag;
    }

    public void setRecyclePartFlag(BigDecimal recyclePartFlag) {
        this.recyclePartFlag = recyclePartFlag;
    }

    @XmlElement(name = "EvalPartSumFirst")
    public BigDecimal getEvalPartSumFirst() {
        return evalPartSumFirst;
    }

    public void setEvalPartSumFirst(BigDecimal evalPartSumFirst) {
        this.evalPartSumFirst = evalPartSumFirst;
    }

    @XmlElement(name = "CornerMark")
    public String getCornerMark() {
        return cornerMark;
    }

    public void setCornerMark(String cornerMark) {
        this.cornerMark = cornerMark;
    }

    @XmlElement(name = "ClmTms")
    public String getClmTms() {
        return clmTms;
    }

    public void setClmTms(String clmTms) {
        this.clmTms = clmTms;
    }

    @XmlElement(name = "IfWading")
    public String getIfWading() {
        return ifWading;
    }

    public void setIfWading(String ifWading) {
        this.ifWading = ifWading;
    }

    @XmlElement(name = "RecheckFlag")
    public String getRecheckFlag() {
        return recheckFlag;
    }

    public void setRecheckFlag(String recheckFlag) {
        this.recheckFlag = recheckFlag;
    }
}
