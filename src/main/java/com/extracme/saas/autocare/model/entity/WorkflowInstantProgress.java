package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table workflow_instant_progress
 */
public class WorkflowInstantProgress implements Serializable {
    /**
     * Database Column Remarks:
     *   进度记录编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   所属流程实例编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.instance_id")
    private Long instanceId;

    /**
     * Database Column Remarks:
     *   活动节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.activity_code")
    private String activityCode;

    /**
     * Database Column Remarks:
     *   节点状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.status_code")
    private String statusCode;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   备注说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.remarks")
    private String remarks;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.instance_id")
    public Long getInstanceId() {
        return instanceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.instance_id")
    public void setInstanceId(Long instanceId) {
        this.instanceId = instanceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.activity_code")
    public String getActivityCode() {
        return activityCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.activity_code")
    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.status_code")
    public String getStatusCode() {
        return statusCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.status_code")
    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode == null ? null : statusCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.remarks")
    public String getRemarks() {
        return remarks;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.remarks")
    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", instanceId=").append(instanceId);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", statusCode=").append(statusCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", remarks=").append(remarks);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}