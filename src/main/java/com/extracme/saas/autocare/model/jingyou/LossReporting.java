package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-13 14:32
 */
public class LossReporting {
    /**
     * 主键
     */
    private String id = StringUtils.EMPTY;
    /**
     * 报案号
     */
    private String reportCode = StringUtils.EMPTY;
    /**
     * 出险原因代码
     */
    private String accidentCauseCode = StringUtils.EMPTY;
    /**
     * 出险原因名称
     */
    private String accidentCauseName = StringUtils.EMPTY;
    /**
     * 报案人姓名
     */
    private String reportPersonName = StringUtils.EMPTY;
    /**
     * 报案时间
     */
    private String reportTime = StringUtils.EMPTY;
    /**
     * 出险时间
     */
    private String accidentTime = StringUtils.EMPTY;
    /**
     * 现场报案
     */
    private String isCurrentReport = StringUtils.EMPTY;
    /**
     * 承保机构编码
     */
    private String insuranceCompanyCode = StringUtils.EMPTY;
    /**
     * 承保机构名称
     */
    private String insuranceCompanyName = StringUtils.EMPTY;
    /**
     * 出险地点
     */
    private String accidentPlace = StringUtils.EMPTY;
    /**
     * 出险经过
     */
    private String accidentCourse = StringUtils.EMPTY;
    /**
     * 是否有车损（0：无 1：有）
     */
    private String carLossFlag = "0";
    /**
     * 是否有人伤（0：无 1：有）
     */
    private String injureLossFlag = "0";
    /**
     * 是否有物损（0：无 1：有）
     */
    private String cargoLossFlag = "0";
    /**
     * 是否有盗窃（0：无 1：有）
     */
    private String thiefLossFlag = "0";
    /**
     * 死伤人数
     */
    private String injureNum = StringUtils.EMPTY;
    /**
     * 出险区域
     */
    private String accidentArea = StringUtils.EMPTY;

    @XmlElement(name = "Id")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @XmlElement(name = "ReportCode")
    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }

    @XmlElement(name = "AccidentCauseCode")
    public String getAccidentCauseCode() {
        return accidentCauseCode;
    }

    public void setAccidentCauseCode(String accidentCauseCode) {
        this.accidentCauseCode = accidentCauseCode;
    }

    @XmlElement(name = "AccidentCauseName")
    public String getAccidentCauseName() {
        return accidentCauseName;
    }

    public void setAccidentCauseName(String accidentCauseName) {
        this.accidentCauseName = accidentCauseName;
    }

    @XmlElement(name = "ReportPersonName")
    public String getReportPersonName() {
        return reportPersonName;
    }

    public void setReportPersonName(String reportPersonName) {
        this.reportPersonName = reportPersonName;
    }

    @XmlElement(name = "ReportTime")
    public String getReportTime() {
        return reportTime;
    }

    public void setReportTime(String reportTime) {
        this.reportTime = reportTime;
    }

    @XmlElement(name = "AccidentTime")
    public String getAccidentTime() {
        return accidentTime;
    }

    public void setAccidentTime(String accidentTime) {
        this.accidentTime = accidentTime;
    }

    @XmlElement(name = "IsCurrentReport")
    public String getIsCurrentReport() {
        return isCurrentReport;
    }

    public void setIsCurrentReport(String isCurrentReport) {
        this.isCurrentReport = isCurrentReport;
    }

    @XmlElement(name = "InsuranceCompanyCode")
    public String getInsuranceCompanyCode() {
        return insuranceCompanyCode;
    }

    public void setInsuranceCompanyCode(String insuranceCompanyCode) {
        this.insuranceCompanyCode = insuranceCompanyCode;
    }

    @XmlElement(name = "InsuranceCompanyName")
    public String getInsuranceCompanyName() {
        return insuranceCompanyName;
    }

    public void setInsuranceCompanyName(String insuranceCompanyName) {
        this.insuranceCompanyName = insuranceCompanyName;
    }

    @XmlElement(name = "AccidentPlace")
    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace;
    }

    @XmlElement(name = "AccidentCourse")
    public String getAccidentCourse() {
        return accidentCourse;
    }

    public void setAccidentCourse(String accidentCourse) {
        this.accidentCourse = accidentCourse;
    }

    @XmlElement(name = "CarLossFlag")
    public String getCarLossFlag() {
        return carLossFlag;
    }

    public void setCarLossFlag(String carLossFlag) {
        this.carLossFlag = carLossFlag;
    }

    @XmlElement(name = "InjureLossFlag")
    public String getInjureLossFlag() {
        return injureLossFlag;
    }

    public void setInjureLossFlag(String injureLossFlag) {
        this.injureLossFlag = injureLossFlag;
    }

    @XmlElement(name = "CargoLossFlag")
    public String getCargoLossFlag() {
        return cargoLossFlag;
    }

    public void setCargoLossFlag(String cargoLossFlag) {
        this.cargoLossFlag = cargoLossFlag;
    }

    @XmlElement(name = "ThiefLossFlag")
    public String getThiefLossFlag() {
        return thiefLossFlag;
    }

    public void setThiefLossFlag(String thiefLossFlag) {
        this.thiefLossFlag = thiefLossFlag;
    }

    @XmlElement(name = "InjureNum")
    public String getInjureNum() {
        return injureNum;
    }

    public void setInjureNum(String injureNum) {
        this.injureNum = injureNum;
    }

    @XmlElement(name = "AccidentArea")
    public String getAccidentArea() {
        return accidentArea;
    }

    public void setAccidentArea(String accidentArea) {
        this.accidentArea = accidentArea;
    }
}
