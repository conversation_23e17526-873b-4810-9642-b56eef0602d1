package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   租户同步密钥管理表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table sys_tenant_sync_key
 */
public class SysTenantSyncKey implements Serializable {
    /**
     * Database Column Remarks:
     *   主键ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   租户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.tenant_id")
    private Long tenantId;

    /**
     * Database Column Remarks:
     *   租户编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.tenant_code")
    private String tenantCode;

    /**
     * Database Column Remarks:
     *   同步密钥（用于租户身份识别）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.sync_key")
    private String syncKey;

    /**
     * Database Column Remarks:
     *   加密密钥（Base64编码，已废弃）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.encryption_key")
    private String encryptionKey;

    /**
     * Database Column Remarks:
     *   密钥状态：1-启用，0-禁用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_status")
    private Integer keyStatus;

    /**
     * Database Column Remarks:
     *   密钥生成时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_generate_time")
    private Date keyGenerateTime;

    /**
     * Database Column Remarks:
     *   密钥过期时间（NULL表示永不过期）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_expire_time")
    private Date keyExpireTime;

    /**
     * Database Column Remarks:
     *   最后使用时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.last_used_time")
    private Date lastUsedTime;

    /**
     * Database Column Remarks:
     *   使用次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.usage_count")
    private Long usageCount;

    /**
     * Database Column Remarks:
     *   备注信息
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.update_by")
    private String updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.tenant_id")
    public Long getTenantId() {
        return tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.tenant_id")
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.tenant_code")
    public String getTenantCode() {
        return tenantCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.tenant_code")
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode == null ? null : tenantCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.sync_key")
    public String getSyncKey() {
        return syncKey;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.sync_key")
    public void setSyncKey(String syncKey) {
        this.syncKey = syncKey == null ? null : syncKey.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.encryption_key")
    public String getEncryptionKey() {
        return encryptionKey;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.encryption_key")
    public void setEncryptionKey(String encryptionKey) {
        this.encryptionKey = encryptionKey == null ? null : encryptionKey.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_status")
    public Integer getKeyStatus() {
        return keyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_status")
    public void setKeyStatus(Integer keyStatus) {
        this.keyStatus = keyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_generate_time")
    public Date getKeyGenerateTime() {
        return keyGenerateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_generate_time")
    public void setKeyGenerateTime(Date keyGenerateTime) {
        this.keyGenerateTime = keyGenerateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_expire_time")
    public Date getKeyExpireTime() {
        return keyExpireTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_expire_time")
    public void setKeyExpireTime(Date keyExpireTime) {
        this.keyExpireTime = keyExpireTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.last_used_time")
    public Date getLastUsedTime() {
        return lastUsedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.last_used_time")
    public void setLastUsedTime(Date lastUsedTime) {
        this.lastUsedTime = lastUsedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.usage_count")
    public Long getUsageCount() {
        return usageCount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.usage_count")
    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", tenantCode=").append(tenantCode);
        sb.append(", syncKey=").append(syncKey);
        sb.append(", encryptionKey=").append(encryptionKey);
        sb.append(", keyStatus=").append(keyStatus);
        sb.append(", keyGenerateTime=").append(keyGenerateTime);
        sb.append(", keyExpireTime=").append(keyExpireTime);
        sb.append(", lastUsedTime=").append(lastUsedTime);
        sb.append(", usageCount=").append(usageCount);
        sb.append(", remark=").append(remark);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}