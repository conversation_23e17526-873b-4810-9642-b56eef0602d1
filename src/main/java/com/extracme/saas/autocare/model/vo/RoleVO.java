package com.extracme.saas.autocare.model.vo;

import java.util.List;

import lombok.Data;

@Data
public class RoleVO {
    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色类型：1-超级管理员，2-普通管理员，3-普通用户
     */
    private Integer roleType;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 角色状态（0-禁用，1-启用）
     */
    private Integer status;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;

    /**
     * 权限树
     */
    private List<PermissionTreeVO> permissionTree;
}