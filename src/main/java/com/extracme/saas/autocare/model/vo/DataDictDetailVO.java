package com.extracme.saas.autocare.model.vo;

import java.util.Date;
import java.util.List;

import com.extracme.saas.autocare.model.dto.DataDictDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据字典详情VO
 * @param <T> 字典值的类型，可以是String、Integer等
 */
@Data
@ApiModel(description = "数据字典详情信息")
public class DataDictDetailVO<T> {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "数据字典名称")
    private String dataName;

    @ApiModelProperty(value = "数据字典编码")
    private String dataCode;

    @ApiModelProperty(value = "数据字段value值类型 1-数字 2-字符串")
    private Integer codeType;

    @ApiModelProperty(value = "字典项列表")
    private List<DataDictDTO<T>> dictItems;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
