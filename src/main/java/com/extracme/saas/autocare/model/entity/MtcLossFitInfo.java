package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   定损换件信息
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_loss_fit_info
 */
public class MtcLossFitInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.task_no")
    private String taskNo;

    /**
     * Database Column Remarks:
     *   定损明细主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.part_id")
    private String partId;

    /**
     * Database Column Remarks:
     *   零配件原厂编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.part_code")
    private String partCode;

    /**
     * Database Column Remarks:
     *   项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.item_name")
    private String itemName;

    /**
     * Database Column Remarks:
     *   系统4S店价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.sys_guide_price")
    private BigDecimal sysGuidePrice;

    /**
     * Database Column Remarks:
     *   系统市场价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.sys_market_price")
    private BigDecimal sysMarketPrice;

    /**
     * Database Column Remarks:
     *   本地4S店价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_guide_price")
    private BigDecimal localGuidePrice;

    /**
     * Database Column Remarks:
     *   本地市场原厂价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_market_price")
    private BigDecimal localMarketPrice;

    /**
     * Database Column Remarks:
     *   本地品牌价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_brand_price")
    private BigDecimal localBrandPrice;

    /**
     * Database Column Remarks:
     *   本地适用价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_applicable_price")
    private BigDecimal localApplicablePrice;

    /**
     * Database Column Remarks:
     *   本地再制造价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_remanufacture_price")
    private BigDecimal localRemanufacturePrice;

    /**
     * Database Column Remarks:
     *   定损参考价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_price")
    private BigDecimal localPrice;

    /**
     * Database Column Remarks:
     *   定损配件数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.count")
    private Integer count;

    /**
     * Database Column Remarks:
     *   定损配件单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.material_fee")
    private BigDecimal materialFee;

    /**
     * Database Column Remarks:
     *   自定义配件标记（0：系统点选 1：手工自定义 2：标准点选 ）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_config_flag")
    private Integer selfConfigFlag;

    /**
     * Database Column Remarks:
     *   险种代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.item_cover_code")
    private Integer itemCoverCode;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   参考价格类型编码（1：4S价格 2：市场原厂价格 3：品牌价格 4：适用价格 5：再制造价格）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.chg_comp_set_code")
    private String chgCompSetCode;

    /**
     * Database Column Remarks:
     *   回收标志（0：不回收 1：回收）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.fit_back_flag")
    private String fitBackFlag;

    /**
     * Database Column Remarks:
     *   残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.remains_price")
    private BigDecimal remainsPrice;

    /**
     * Database Column Remarks:
     *   待检测标志（0：无需检测 1：待检测）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.detected_flag")
    private String detectedFlag;

    /**
     * Database Column Remarks:
     *   直供测标志（0：非直供 1：直供）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.direct_supply_flag")
    private String directSupplyFlag;

    /**
     * Database Column Remarks:
     *   直供商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.direct_supplier")
    private String directSupplier;

    /**
     * Database Column Remarks:
     *   管理费率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_single_rate")
    private BigDecimal manageSingleRate;

    /**
     * Database Column Remarks:
     *   管理费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_single_fee")
    private BigDecimal manageSingleFee;

    /**
     * Database Column Remarks:
     *   换件合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.eval_part_sum")
    private BigDecimal evalPartSum;

    /**
     * Database Column Remarks:
     *   自付比例
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_pay_rate")
    private BigDecimal selfPayRate;

    /**
     * Database Column Remarks:
     *   回收方式（1：需回收）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.recycle_part_flag")
    private String recyclePartFlag;

    /**
     * Database Column Remarks:
     *   首次定损金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.eval_part_sum_first")
    private BigDecimal evalPartSumFirst;

    /**
     * Database Column Remarks:
     *   配件角标（第一位：是否待检测 第二位：是否改装配件 第三位：是否高价值配件 第四位：是否退回新增项目 第五位：是否风险项目 第六位：是否询价配件 第七位：是否直供配件）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.corner_mark")
    private String cornerMark;

    /**
     * Database Column Remarks:
     *   重开次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.clm_tms")
    private String clmTms;

    /**
     * Database Column Remarks:
     *   是否涉水（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.if_wading")
    private String ifWading;

    /**
     * Database Column Remarks:
     *   复检标志
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.recheck_flag")
    private String recheckFlag;

    /**
     * Database Column Remarks:
     *   核损换件单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_material_fee")
    private BigDecimal auditMaterialFee;

    /**
     * Database Column Remarks:
     *   核损数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_count")
    private BigDecimal auditCount;

    /**
     * Database Column Remarks:
     *   核损换件小计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_part_sum")
    private BigDecimal apprPartSum;

    /**
     * Database Column Remarks:
     *   自付比例
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_pay_price")
    private BigDecimal selfPayPrice;

    /**
     * Database Column Remarks:
     *   核损残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_remains_price")
    private BigDecimal apprRemainsPrice;

    /**
     * Database Column Remarks:
     *   核损管理费率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_rate")
    private BigDecimal manageRate;

    /**
     * Database Column Remarks:
     *   核损管理费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_manage_fee")
    private BigDecimal apprManageFee;

    /**
     * Database Column Remarks:
     *   核损状态（00：待处理 01：通过 02：价格异议 03：建议剔除 04：建议修复）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.check_state")
    private String checkState;

    /**
     * Database Column Remarks:
     *   核损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_remark")
    private String auditRemark;

    /**
     * Database Column Remarks:
     *   核损回收标记（0：不回收 1：回收）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_back_flag")
    private String apprBackFlag;

    /**
     * Database Column Remarks:
     *   复勘标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_recheck_flag")
    private String auditRecheckFlag;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.misc_Desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.task_no")
    public String getTaskNo() {
        return taskNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.task_no")
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo == null ? null : taskNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.part_id")
    public String getPartId() {
        return partId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.part_id")
    public void setPartId(String partId) {
        this.partId = partId == null ? null : partId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.part_code")
    public String getPartCode() {
        return partCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.part_code")
    public void setPartCode(String partCode) {
        this.partCode = partCode == null ? null : partCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.item_name")
    public String getItemName() {
        return itemName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.item_name")
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.sys_guide_price")
    public BigDecimal getSysGuidePrice() {
        return sysGuidePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.sys_guide_price")
    public void setSysGuidePrice(BigDecimal sysGuidePrice) {
        this.sysGuidePrice = sysGuidePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.sys_market_price")
    public BigDecimal getSysMarketPrice() {
        return sysMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.sys_market_price")
    public void setSysMarketPrice(BigDecimal sysMarketPrice) {
        this.sysMarketPrice = sysMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_guide_price")
    public BigDecimal getLocalGuidePrice() {
        return localGuidePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_guide_price")
    public void setLocalGuidePrice(BigDecimal localGuidePrice) {
        this.localGuidePrice = localGuidePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_market_price")
    public BigDecimal getLocalMarketPrice() {
        return localMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_market_price")
    public void setLocalMarketPrice(BigDecimal localMarketPrice) {
        this.localMarketPrice = localMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_brand_price")
    public BigDecimal getLocalBrandPrice() {
        return localBrandPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_brand_price")
    public void setLocalBrandPrice(BigDecimal localBrandPrice) {
        this.localBrandPrice = localBrandPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_applicable_price")
    public BigDecimal getLocalApplicablePrice() {
        return localApplicablePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_applicable_price")
    public void setLocalApplicablePrice(BigDecimal localApplicablePrice) {
        this.localApplicablePrice = localApplicablePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_remanufacture_price")
    public BigDecimal getLocalRemanufacturePrice() {
        return localRemanufacturePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_remanufacture_price")
    public void setLocalRemanufacturePrice(BigDecimal localRemanufacturePrice) {
        this.localRemanufacturePrice = localRemanufacturePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_price")
    public BigDecimal getLocalPrice() {
        return localPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_price")
    public void setLocalPrice(BigDecimal localPrice) {
        this.localPrice = localPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.count")
    public Integer getCount() {
        return count;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.count")
    public void setCount(Integer count) {
        this.count = count;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.material_fee")
    public BigDecimal getMaterialFee() {
        return materialFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.material_fee")
    public void setMaterialFee(BigDecimal materialFee) {
        this.materialFee = materialFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_config_flag")
    public Integer getSelfConfigFlag() {
        return selfConfigFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_config_flag")
    public void setSelfConfigFlag(Integer selfConfigFlag) {
        this.selfConfigFlag = selfConfigFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.item_cover_code")
    public Integer getItemCoverCode() {
        return itemCoverCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.item_cover_code")
    public void setItemCoverCode(Integer itemCoverCode) {
        this.itemCoverCode = itemCoverCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.chg_comp_set_code")
    public String getChgCompSetCode() {
        return chgCompSetCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.chg_comp_set_code")
    public void setChgCompSetCode(String chgCompSetCode) {
        this.chgCompSetCode = chgCompSetCode == null ? null : chgCompSetCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.fit_back_flag")
    public String getFitBackFlag() {
        return fitBackFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.fit_back_flag")
    public void setFitBackFlag(String fitBackFlag) {
        this.fitBackFlag = fitBackFlag == null ? null : fitBackFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.remains_price")
    public BigDecimal getRemainsPrice() {
        return remainsPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.remains_price")
    public void setRemainsPrice(BigDecimal remainsPrice) {
        this.remainsPrice = remainsPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.detected_flag")
    public String getDetectedFlag() {
        return detectedFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.detected_flag")
    public void setDetectedFlag(String detectedFlag) {
        this.detectedFlag = detectedFlag == null ? null : detectedFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.direct_supply_flag")
    public String getDirectSupplyFlag() {
        return directSupplyFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.direct_supply_flag")
    public void setDirectSupplyFlag(String directSupplyFlag) {
        this.directSupplyFlag = directSupplyFlag == null ? null : directSupplyFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.direct_supplier")
    public String getDirectSupplier() {
        return directSupplier;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.direct_supplier")
    public void setDirectSupplier(String directSupplier) {
        this.directSupplier = directSupplier == null ? null : directSupplier.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_single_rate")
    public BigDecimal getManageSingleRate() {
        return manageSingleRate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_single_rate")
    public void setManageSingleRate(BigDecimal manageSingleRate) {
        this.manageSingleRate = manageSingleRate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_single_fee")
    public BigDecimal getManageSingleFee() {
        return manageSingleFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_single_fee")
    public void setManageSingleFee(BigDecimal manageSingleFee) {
        this.manageSingleFee = manageSingleFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.eval_part_sum")
    public BigDecimal getEvalPartSum() {
        return evalPartSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.eval_part_sum")
    public void setEvalPartSum(BigDecimal evalPartSum) {
        this.evalPartSum = evalPartSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_pay_rate")
    public BigDecimal getSelfPayRate() {
        return selfPayRate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_pay_rate")
    public void setSelfPayRate(BigDecimal selfPayRate) {
        this.selfPayRate = selfPayRate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.recycle_part_flag")
    public String getRecyclePartFlag() {
        return recyclePartFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.recycle_part_flag")
    public void setRecyclePartFlag(String recyclePartFlag) {
        this.recyclePartFlag = recyclePartFlag == null ? null : recyclePartFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.eval_part_sum_first")
    public BigDecimal getEvalPartSumFirst() {
        return evalPartSumFirst;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.eval_part_sum_first")
    public void setEvalPartSumFirst(BigDecimal evalPartSumFirst) {
        this.evalPartSumFirst = evalPartSumFirst;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.corner_mark")
    public String getCornerMark() {
        return cornerMark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.corner_mark")
    public void setCornerMark(String cornerMark) {
        this.cornerMark = cornerMark == null ? null : cornerMark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.clm_tms")
    public String getClmTms() {
        return clmTms;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.clm_tms")
    public void setClmTms(String clmTms) {
        this.clmTms = clmTms == null ? null : clmTms.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.if_wading")
    public String getIfWading() {
        return ifWading;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.if_wading")
    public void setIfWading(String ifWading) {
        this.ifWading = ifWading == null ? null : ifWading.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.recheck_flag")
    public String getRecheckFlag() {
        return recheckFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.recheck_flag")
    public void setRecheckFlag(String recheckFlag) {
        this.recheckFlag = recheckFlag == null ? null : recheckFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_material_fee")
    public BigDecimal getAuditMaterialFee() {
        return auditMaterialFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_material_fee")
    public void setAuditMaterialFee(BigDecimal auditMaterialFee) {
        this.auditMaterialFee = auditMaterialFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_count")
    public BigDecimal getAuditCount() {
        return auditCount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_count")
    public void setAuditCount(BigDecimal auditCount) {
        this.auditCount = auditCount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_part_sum")
    public BigDecimal getApprPartSum() {
        return apprPartSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_part_sum")
    public void setApprPartSum(BigDecimal apprPartSum) {
        this.apprPartSum = apprPartSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_pay_price")
    public BigDecimal getSelfPayPrice() {
        return selfPayPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_pay_price")
    public void setSelfPayPrice(BigDecimal selfPayPrice) {
        this.selfPayPrice = selfPayPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_remains_price")
    public BigDecimal getApprRemainsPrice() {
        return apprRemainsPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_remains_price")
    public void setApprRemainsPrice(BigDecimal apprRemainsPrice) {
        this.apprRemainsPrice = apprRemainsPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_rate")
    public BigDecimal getManageRate() {
        return manageRate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_rate")
    public void setManageRate(BigDecimal manageRate) {
        this.manageRate = manageRate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_manage_fee")
    public BigDecimal getApprManageFee() {
        return apprManageFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_manage_fee")
    public void setApprManageFee(BigDecimal apprManageFee) {
        this.apprManageFee = apprManageFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.check_state")
    public String getCheckState() {
        return checkState;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.check_state")
    public void setCheckState(String checkState) {
        this.checkState = checkState == null ? null : checkState.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_remark")
    public String getAuditRemark() {
        return auditRemark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_remark")
    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark == null ? null : auditRemark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_back_flag")
    public String getApprBackFlag() {
        return apprBackFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_back_flag")
    public void setApprBackFlag(String apprBackFlag) {
        this.apprBackFlag = apprBackFlag == null ? null : apprBackFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_recheck_flag")
    public String getAuditRecheckFlag() {
        return auditRecheckFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_recheck_flag")
    public void setAuditRecheckFlag(String auditRecheckFlag) {
        this.auditRecheckFlag = auditRecheckFlag == null ? null : auditRecheckFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.misc_Desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.misc_Desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", partId=").append(partId);
        sb.append(", partCode=").append(partCode);
        sb.append(", itemName=").append(itemName);
        sb.append(", sysGuidePrice=").append(sysGuidePrice);
        sb.append(", sysMarketPrice=").append(sysMarketPrice);
        sb.append(", localGuidePrice=").append(localGuidePrice);
        sb.append(", localMarketPrice=").append(localMarketPrice);
        sb.append(", localBrandPrice=").append(localBrandPrice);
        sb.append(", localApplicablePrice=").append(localApplicablePrice);
        sb.append(", localRemanufacturePrice=").append(localRemanufacturePrice);
        sb.append(", localPrice=").append(localPrice);
        sb.append(", count=").append(count);
        sb.append(", materialFee=").append(materialFee);
        sb.append(", selfConfigFlag=").append(selfConfigFlag);
        sb.append(", itemCoverCode=").append(itemCoverCode);
        sb.append(", remark=").append(remark);
        sb.append(", chgCompSetCode=").append(chgCompSetCode);
        sb.append(", fitBackFlag=").append(fitBackFlag);
        sb.append(", remainsPrice=").append(remainsPrice);
        sb.append(", detectedFlag=").append(detectedFlag);
        sb.append(", directSupplyFlag=").append(directSupplyFlag);
        sb.append(", directSupplier=").append(directSupplier);
        sb.append(", manageSingleRate=").append(manageSingleRate);
        sb.append(", manageSingleFee=").append(manageSingleFee);
        sb.append(", evalPartSum=").append(evalPartSum);
        sb.append(", selfPayRate=").append(selfPayRate);
        sb.append(", recyclePartFlag=").append(recyclePartFlag);
        sb.append(", evalPartSumFirst=").append(evalPartSumFirst);
        sb.append(", cornerMark=").append(cornerMark);
        sb.append(", clmTms=").append(clmTms);
        sb.append(", ifWading=").append(ifWading);
        sb.append(", recheckFlag=").append(recheckFlag);
        sb.append(", auditMaterialFee=").append(auditMaterialFee);
        sb.append(", auditCount=").append(auditCount);
        sb.append(", apprPartSum=").append(apprPartSum);
        sb.append(", selfPayPrice=").append(selfPayPrice);
        sb.append(", apprRemainsPrice=").append(apprRemainsPrice);
        sb.append(", manageRate=").append(manageRate);
        sb.append(", apprManageFee=").append(apprManageFee);
        sb.append(", checkState=").append(checkState);
        sb.append(", auditRemark=").append(auditRemark);
        sb.append(", apprBackFlag=").append(apprBackFlag);
        sb.append(", auditRecheckFlag=").append(auditRecheckFlag);
        sb.append(", status=").append(status);
        sb.append(", miscDesc=").append(miscDesc);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}