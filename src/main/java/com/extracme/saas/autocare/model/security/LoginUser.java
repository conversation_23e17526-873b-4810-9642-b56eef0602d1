package com.extracme.saas.autocare.model.security;

import com.extracme.saas.autocare.model.entity.SysUser;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 登录用户身份权限
 */
@Data
public class LoginUser implements UserDetails {
    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 租户名
     */
    private String tenantName;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 用户信息
     */
    private SysUser user;

    /**
     * 用户直接关联的机构ID列表
     */
    private List<String> orgIds;

    /**
     * 用户可访问的所有机构ID列表（包括直接关联的机构及其所有子机构）
     */
    private List<String> allAccessibleOrgIds;

    /**
     * 距离过期剩余天数
     * 基于租户的过期时间计算，如果已过期则返回负数或0
     */
    private Integer daysUntilExpiry;

    public LoginUser() {
    }

    public LoginUser(SysUser user, Set<String> permissions) {
        this.user = user;
        this.permissions = permissions;
    }

    @JsonIgnore
    @Override
    public String getPassword() {
        // 数据库实体中没有password字段，返回空字符串
        return "";
    }

    @Override
    public String getUsername() {
        return user.getUsername();
    }

    /**
     * 获取用户可访问的所有机构ID列表（扁平化）
     * 包括用户直接关联的机构以及这些机构的所有子机构
     *
     * @return 扁平化的机构ID列表，如果没有关联机构则返回空列表
     */
    @JsonIgnore
    public List<String> getAllAccessibleOrgIds() {
        if (allAccessibleOrgIds == null || allAccessibleOrgIds.isEmpty()) {
            return Collections.emptyList();
        }
        return new ArrayList<>(allAccessibleOrgIds);
    }

    /**
     * 获取用户直接关联的机构ID列表
     *
     * @return 用户直接关联的机构ID列表
     */
    @JsonIgnore
    public List<String> getDirectOrgIds() {
        return orgIds != null ? new ArrayList<>(orgIds) : Collections.emptyList();
    }

    /**
     * 检查用户是否关联了指定机构
     *
     * @param orgId 机构ID
     * @return 是否关联
     */
    @JsonIgnore
    public boolean hasOrgAccess(String orgId) {
        return orgIds != null && orgIds.contains(orgId);
    }

    /**
     * 获取主要机构ID（向后兼容）
     * 返回用户关联的第一个机构ID，如果没有关联机构则返回null
     *
     * @deprecated 建议使用 getAllAccessibleOrgIds() 或 getDirectOrgIds() 方法
     * @return 主要机构ID
     */
    @Deprecated
    @JsonIgnore
    public String getPrimaryOrgId() {
        if (orgIds != null && !orgIds.isEmpty()) {
            return orgIds.get(0);
        }
        return null;
    }

    /**
     * 获取用户审批层级
     * @return 审批层级
     */
    public Integer getApprovalLevel() {
        return user != null ? user.getApprovalLevel() : null;
    }

    /**
     * 获取保险公司ID
     * @return 保险公司ID
     */
    public Long getInsuranceCompanyId() {
        return user != null ? user.getInsuranceCompanyId() : null;
    }

    /**
     * 账户是否未过期,过期无法验证
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否解锁,锁定的用户无法进行身份验证
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     */
    @JsonIgnore
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     */
    @JsonIgnore
    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }
}