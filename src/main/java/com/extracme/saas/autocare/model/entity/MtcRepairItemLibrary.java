package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   维修项目库表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_repair_item_library
 */
public class MtcRepairItemLibrary implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   序号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.serial_number")
    private Integer serialNumber;

    /**
     * Database Column Remarks:
     *   项目编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_no")
    private String itemNo;

    /**
     * Database Column Remarks:
     *   项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_name")
    private String itemName;

    /**
     * Database Column Remarks:
     *   项目类型 1：保养 2：终端 3：维修
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_type")
    private Integer itemType;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.vehicle_model_seq")
    private Long vehicleModelSeq;

    /**
     * Database Column Remarks:
     *   车型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.vehicle_model_info")
    private String vehicleModelInfo;

    /**
     * Database Column Remarks:
     *   工时费全国市场价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.hour_fee_national_market_price")
    private BigDecimal hourFeeNationalMarketPrice;

    /**
     * Database Column Remarks:
     *   工时费全国上限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.hour_fee_national_highest_price")
    private BigDecimal hourFeeNationalHighestPrice;

    /**
     * Database Column Remarks:
     *   材料费全国市场价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.material_cost_national_market_price")
    private BigDecimal materialCostNationalMarketPrice;

    /**
     * Database Column Remarks:
     *   材料费全国上限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.material_cost_national_highest_price")
    private BigDecimal materialCostNationalHighestPrice;

    /**
     * Database Column Remarks:
     *   保养周期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.maintenance_cycle")
    private Integer maintenanceCycle;

    /**
     * Database Column Remarks:
     *   保养里程数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.maintenance_mileage")
    private BigDecimal maintenanceMileage;

    /**
     * Database Column Remarks:
     *   是否常用 0：否 1：是 
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.is_commonly_used")
    private Integer isCommonlyUsed;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   是否可编辑  0：否 1：是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.editable")
    private Integer editable;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.misc_Desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.serial_number")
    public Integer getSerialNumber() {
        return serialNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.serial_number")
    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_no")
    public String getItemNo() {
        return itemNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_no")
    public void setItemNo(String itemNo) {
        this.itemNo = itemNo == null ? null : itemNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_name")
    public String getItemName() {
        return itemName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_name")
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_type")
    public Integer getItemType() {
        return itemType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_type")
    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.vehicle_model_seq")
    public Long getVehicleModelSeq() {
        return vehicleModelSeq;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.vehicle_model_seq")
    public void setVehicleModelSeq(Long vehicleModelSeq) {
        this.vehicleModelSeq = vehicleModelSeq;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.vehicle_model_info")
    public String getVehicleModelInfo() {
        return vehicleModelInfo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.vehicle_model_info")
    public void setVehicleModelInfo(String vehicleModelInfo) {
        this.vehicleModelInfo = vehicleModelInfo == null ? null : vehicleModelInfo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.hour_fee_national_market_price")
    public BigDecimal getHourFeeNationalMarketPrice() {
        return hourFeeNationalMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.hour_fee_national_market_price")
    public void setHourFeeNationalMarketPrice(BigDecimal hourFeeNationalMarketPrice) {
        this.hourFeeNationalMarketPrice = hourFeeNationalMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.hour_fee_national_highest_price")
    public BigDecimal getHourFeeNationalHighestPrice() {
        return hourFeeNationalHighestPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.hour_fee_national_highest_price")
    public void setHourFeeNationalHighestPrice(BigDecimal hourFeeNationalHighestPrice) {
        this.hourFeeNationalHighestPrice = hourFeeNationalHighestPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.material_cost_national_market_price")
    public BigDecimal getMaterialCostNationalMarketPrice() {
        return materialCostNationalMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.material_cost_national_market_price")
    public void setMaterialCostNationalMarketPrice(BigDecimal materialCostNationalMarketPrice) {
        this.materialCostNationalMarketPrice = materialCostNationalMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.material_cost_national_highest_price")
    public BigDecimal getMaterialCostNationalHighestPrice() {
        return materialCostNationalHighestPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.material_cost_national_highest_price")
    public void setMaterialCostNationalHighestPrice(BigDecimal materialCostNationalHighestPrice) {
        this.materialCostNationalHighestPrice = materialCostNationalHighestPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.maintenance_cycle")
    public Integer getMaintenanceCycle() {
        return maintenanceCycle;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.maintenance_cycle")
    public void setMaintenanceCycle(Integer maintenanceCycle) {
        this.maintenanceCycle = maintenanceCycle;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.maintenance_mileage")
    public BigDecimal getMaintenanceMileage() {
        return maintenanceMileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.maintenance_mileage")
    public void setMaintenanceMileage(BigDecimal maintenanceMileage) {
        this.maintenanceMileage = maintenanceMileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.is_commonly_used")
    public Integer getIsCommonlyUsed() {
        return isCommonlyUsed;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.is_commonly_used")
    public void setIsCommonlyUsed(Integer isCommonlyUsed) {
        this.isCommonlyUsed = isCommonlyUsed;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.editable")
    public Integer getEditable() {
        return editable;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.editable")
    public void setEditable(Integer editable) {
        this.editable = editable;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.misc_Desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.misc_Desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", serialNumber=").append(serialNumber);
        sb.append(", itemNo=").append(itemNo);
        sb.append(", itemName=").append(itemName);
        sb.append(", itemType=").append(itemType);
        sb.append(", vehicleModelSeq=").append(vehicleModelSeq);
        sb.append(", vehicleModelInfo=").append(vehicleModelInfo);
        sb.append(", hourFeeNationalMarketPrice=").append(hourFeeNationalMarketPrice);
        sb.append(", hourFeeNationalHighestPrice=").append(hourFeeNationalHighestPrice);
        sb.append(", materialCostNationalMarketPrice=").append(materialCostNationalMarketPrice);
        sb.append(", materialCostNationalHighestPrice=").append(materialCostNationalHighestPrice);
        sb.append(", maintenanceCycle=").append(maintenanceCycle);
        sb.append(", maintenanceMileage=").append(maintenanceMileage);
        sb.append(", isCommonlyUsed=").append(isCommonlyUsed);
        sb.append(", remark=").append(remark);
        sb.append(", status=").append(status);
        sb.append(", editable=").append(editable);
        sb.append(", miscDesc=").append(miscDesc);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}