package com.extracme.saas.autocare.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆出厂登记结果VO
 *
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Data
@ApiModel(description = "车辆出厂登记结果VO")
public class VehicleLeavingFactoryResultVO {
    
    /**
     * id主键
     */
    @ApiModelProperty(value = "id主键", example = "1001")
    private Long id;

    /**
     * 出厂登记ID
     */
    @ApiModelProperty(value = "出厂登记ID", example = "1001")
    private Long leavingFactoryId;
    
    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", example = "TASK20240525001")
    private String taskNo;
    
    /**
     * 车辆所属组织机构ID
     */
    @ApiModelProperty(value = "车辆所属组织机构ID", example = "ORG001")
    private String orgId;
    
    /**
     * 车辆所属组织机构名称
     */
    @ApiModelProperty(value = "车辆所属组织机构名称", example = "上海分公司")
    private String orgName;
    
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号", example = "沪A12345")
    private String vehicleNo;
    
    /**
     * 车型ID
     */
    @ApiModelProperty(value = "车型ID", example = "1001")
    private Long vehicleModelSeq;
    
    /**
     * 车型名称
     */
    @ApiModelProperty(value = "车型名称", example = "奥迪A6L")
    private String vehicleModelInfo;
    
    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号", example = "LSVAU2183C2184235")
    private String vin;
    
    /**
     * 修理类型ID 1:事故维修 2:自费维修 3:车辆保养
     */
    @ApiModelProperty(value = "修理类型ID", example = "1", notes = "1:事故维修 2:自费维修 3:车辆保养")
    private Integer repairTypeId;
    
    /**
     * 修理类型名称
     */
    @ApiModelProperty(value = "修理类型名称", example = "事故维修")
    private String repairTypeName;
    
    /**
     * 修理厂ID
     */
    @ApiModelProperty(value = "修理厂ID", example = "REPAIR001")
    private String repairDepotOrgId;
    
    /**
     * 修理厂名称
     */
    @ApiModelProperty(value = "修理厂名称", example = "上海XX汽车修理厂")
    private String repairDepotName;
    
    /**
     * 修理厂sap供应商编号
     */
    @ApiModelProperty(value = "修理厂sap供应商编号", example = "SAP001")
    private String repairDepotSapCode;
    
    /**
     * sap发送状态 0未发送 1已发送 2发送失败 3需重新发送(2022.01 弃用，状态由业财系统控制)
     */
    @ApiModelProperty(value = "sap发送状态", example = "1", notes = "0:未发送 1:已发送 2:发送失败 3:需重新发送(2022.01 弃用，状态由业财系统控制)")
    private Integer sapSendStatus;
    
    /**
     * sap同步标识
     */
    @ApiModelProperty(value = "sap同步标识", example = "1")
    private Integer sapSyncFlag;
    
    /**
     * 业财系统记账状态：NEW 待记账； DON记账成功; FAL记账失败；NONE无需记账; ERROR 数据异常
     */
    @ApiModelProperty(value = "业财系统记账状态", example = "DON", notes = "NEW:待记账 DON:记账成功 FAL:记账失败 NONE:无需记账 ERROR:数据异常")
    private String accountingStatus;
    
    /**
     * 任务创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "任务创建时间", example = "2024-05-20 09:00:00")
    private Date taskInflowTime;
    
    /**
     * 车辆接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "车辆接收时间", example = "2024-05-20 10:00:00")
    private Date vehicleReciveTime;
    
    /**
     * 是否需要复勘 0:否 1:是
     */
    @ApiModelProperty(value = "是否需要复勘", example = "0", notes = "0:否 1:是")
    private BigDecimal resurveyFlag;
    
    /**
     * 是否超时 (0:维修超时 1:未超时)
     */
    @ApiModelProperty(value = "是否超时", example = "1", notes = "0:维修超时 1:未超时")
    private String timeOut;
    
    /**
     * 子任务编号
     */
    @ApiModelProperty(value = "子任务编号", example = "SUB20240525001")
    private String subtaskNo;
    
    /**
     * 修理等级
     */
    @ApiModelProperty(value = "修理等级", example = "A")
    private String repairGrade;
    
    /**
     * 车辆验收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "车辆验收时间", example = "2024-05-25 12:00:00")
    private Date vehicleCheckTime;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间", example = "2024-05-25 13:00:00")
    private String submitDateTime;

    /**
     * 当前活动节点
     */
    @ApiModelProperty(value = "当前活动节点")
    private String currentActivityCode;

    /**
     * 当前活动节点
     */
    @ApiModelProperty(value = "当前活动节点")
    private String currentActivityName;

    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    private String statusCode;

    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态名")
    private String statusName;

    /**
     * 提车时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "提车时间", example = "2024-05-25 14:30:00")
    private Date deliveryTime;
    
    /**
     * 车辆业务状态（0：分时租赁 1：长租 3：短租 4：公务用车）
     */
    @ApiModelProperty(value = "车辆业务状态", example = "1", notes = "0:分时租赁 1:长租 3:短租 4:公务用车")
    private Integer renttype;

    /**
     * 实际运营标签(0-未投车辆 1-短租运营车辆 2-长租运营车辆 3-备库车辆 4-待退运车辆 5-已处置车辆 6-特殊车辆)
     */
    @ApiModelProperty(value = "实际运营标签", example = "2", notes = "0:未投车辆 1:短租运营车辆 2:长租运营车辆 3:备库车辆 4:待退运车辆 5:已处置车辆 6:特殊车辆")
    private Integer factOperateTag;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间", example = "2024-05-25 15:00:00")
    private Date updatedTime;

    /**
     * 总金额
     */
    @ApiModelProperty(value = "总金额", example = "5000.00")
    private BigDecimal totalMoney;

    /**
     * 车辆保险所属
     */
    @ApiModelProperty(value = "车辆保险所属", example = "中国人保")
    private String insuranceCompanyName;

    /**
     * 事故转自费标识：0否 1是
     */
    @ApiModelProperty(value = "事故转自费标识", example = "0", notes = "0:否 1:是")
    private Integer reviewToSelFeeFlag;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "已完成")
    private String status;

    /**
     * 是否提交上级 0:未提交 1:已提交
     */
    @ApiModelProperty(value = "是否提交上级", example = "1", notes = "0:未提交 1:已提交")
    private Integer examineLevel;

    /**
     *  1-已登记 2-未登记 3-已关闭
     */
    @ApiModelProperty(value = "出厂状态", example = "1", notes = "1-已登记 2-未登记 3-已关闭")
    private Integer leavingStatus;
}
