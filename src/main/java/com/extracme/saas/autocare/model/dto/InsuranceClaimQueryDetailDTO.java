package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "保险索赔用印申请明细查询DTO")
public class InsuranceClaimQueryDetailDTO  {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @ApiModelProperty(value = "申请单据号")
    @NotBlank(message = "申请单据号不能为空")
    private String documentNumber;
}