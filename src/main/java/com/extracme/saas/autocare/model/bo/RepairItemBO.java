package com.extracme.saas.autocare.model.bo;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 维修项目业务对象
 */
@Data
public class RepairItemBO {
    /**
     * 项目ID
     */
    private Long id;
    
    /**
     * 项目名称
     */
    private String itemName;
    
    /**
     * 项目类型 1:保养 2:终端 3:维修
     */
    private Integer itemType;
    
    /**
     * 工时费
     */
    private BigDecimal hourFee;
    
    /**
     * 材料费
     */
    private BigDecimal materialCost;
    
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
}
