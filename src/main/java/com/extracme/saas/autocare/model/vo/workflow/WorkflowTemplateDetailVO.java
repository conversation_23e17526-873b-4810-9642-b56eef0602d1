package com.extracme.saas.autocare.model.vo.workflow;

import com.extracme.saas.autocare.model.dto.workflow.ActivityNodeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 工作流模板详情视图对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("工作流模板详情视图")
public class WorkflowTemplateDetailVO extends WorkflowTemplateVO {

    /**
     * 活动节点列表
     */
    @ApiModelProperty("活动节点列表")
    private List<ActivityNodeDTO> activityNodes;
} 