package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "修理项目创建DTO")
public class RepairProjectCreateDTO {

    @ApiModelProperty(value = "修理名称")
    @NotBlank(message = "修理名称不能为空")
    private String repairName;

    @ApiModelProperty(value = "修理分组ID")
    @NotNull(message = "修理分组ID不能为空")
    private Long groupingId;

    @ApiModelProperty(value = "系统市场价(元)")
    private BigDecimal sysMarketPrice;

    @ApiModelProperty(value = "系统专修价(元)")
    private BigDecimal sysMajorInPrice;

    @ApiModelProperty(value = "本地市场价(元)")
    @NotNull(message = "本地市场价不能为空")
    private BigDecimal localMarketPrice;

    @ApiModelProperty(value = "本地专修价(元)")
    private BigDecimal localMajorInPrice;

    @ApiModelProperty(value = "状态（1：有效 0：无效）")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "是否支持另行报价 1-是 0-否")
    @NotNull(message = "是否支持另行报价不能为空")
    private Integer separateQuotation;

    @ApiModelProperty(value = "备注")
    private String remark;
}