package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:27
 */
public class EvalLossInfoEvaluate {
    /**
     * 车损标的主键
     */
    private String dmgVhclId = StringUtils.EMPTY;
    /**
     * 定损单号（任务编号）
     */
    private String lossNo = StringUtils.EMPTY;
    /**
     * 报案号（任务编号）
     */
    private String reportCode = StringUtils.EMPTY;
    /**
     * 核损员所属分机构代码
     */
    private String apprComCode = StringUtils.EMPTY;
    /**
     * 核损员所属分机构名称
     */
    private String apprCompany = StringUtils.EMPTY;
    /**
     * 核损员所属中支代码
     */
    private String apprBranchComCode = StringUtils.EMPTY;
    /**
     * 核损员所属中支名称
     */
    private String apprBranchComName = StringUtils.EMPTY;
    /**
     * 核损员代码
     */
    private String apprHandlerCode = StringUtils.EMPTY;
    /**
     * 核损员名称
     */
    private String apprHandlerName = StringUtils.EMPTY;
    /**
     * 定损单备注
     */
    private String evalRemark = StringUtils.EMPTY;


    @XmlElement(name = "DmgVhclId")
    public String getDmgVhclId() {
        return dmgVhclId;
    }

    public void setDmgVhclId(String dmgVhclId) {
        this.dmgVhclId = dmgVhclId;
    }

    @XmlElement(name = "LossNo")
    public String getLossNo() {
        return lossNo;
    }

    public void setLossNo(String lossNo) {
        this.lossNo = lossNo;
    }

    @XmlElement(name = "ReportCode")
    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }

    @XmlElement(name = "ApprComCode")
    public String getApprComCode() {
        return apprComCode;
    }

    public void setApprComCode(String apprComCode) {
        this.apprComCode = apprComCode;
    }

    @XmlElement(name = "ApprCompany")
    public String getApprCompany() {
        return apprCompany;
    }

    public void setApprCompany(String apprCompany) {
        this.apprCompany = apprCompany;
    }

    @XmlElement(name = "ApprBranchComCode")
    public String getApprBranchComCode() {
        return apprBranchComCode;
    }

    public void setApprBranchComCode(String apprBranchComCode) {
        this.apprBranchComCode = apprBranchComCode;
    }

    @XmlElement(name = "ApprBranchComName")
    public String getApprBranchComName() {
        return apprBranchComName;
    }

    public void setApprBranchComName(String apprBranchComName) {
        this.apprBranchComName = apprBranchComName;
    }

    @XmlElement(name = "ApprHandlerCode")
    public String getApprHandlerCode() {
        return apprHandlerCode;
    }

    public void setApprHandlerCode(String apprHandlerCode) {
        this.apprHandlerCode = apprHandlerCode;
    }

    @XmlElement(name = "ApprHandlerName")
    public String getApprHandlerName() {
        return apprHandlerName;
    }

    public void setApprHandlerName(String apprHandlerName) {
        this.apprHandlerName = apprHandlerName;
    }

    @XmlElement(name = "EvalRemark")
    public String getEvalRemark() {
        return evalRemark;
    }

    public void setEvalRemark(String evalRemark) {
        this.evalRemark = evalRemark;
    }
}
