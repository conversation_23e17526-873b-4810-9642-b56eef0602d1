package com.extracme.saas.autocare.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
@ApiModel(description = "维修任务流程列表视图对象")
public class RepairTaskProcessListVO {

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Long id;

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    /**
     * 任务所属单位id
     */
    @ApiModelProperty(value = "任务所属单位id")
    private String orgId;

    /**
     * 任务所属单位
     */
    @ApiModelProperty(value = "任务所属单位")
    private String orgName;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型")
    private String vehicleModelInfo;

    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号")
    private String vin;

    /**
     * 车辆保险所属
     */
    @ApiModelProperty(value = "车辆保险所属")
    private String insuranceCompanyName;

    /**
     * 修理类型id
     */
    @ApiModelProperty(value = "修理类型id")
    private Integer repairTypeId;

    /**
     * 修理类型
     */
    @ApiModelProperty(value = "修理类型")
    private String repairTypeName;

    @ApiModelProperty(value = "修理厂类型(1:合作修理厂 2:非合作修理厂)")
    private Integer repairDepotType;

    /**
     * 修理级别
     */
    @ApiModelProperty(value = "修理级别")
    private String repairGrade;

    /**
     * 修理厂ID
     */
    @ApiModelProperty(value = "修理厂ID")
    private String repairDepotId;

    /**
     * 修理厂名称
     */
    @ApiModelProperty(value = "修理厂名称")
    private String repairDepotName;

    /**
     * 预计修理完成时间
     */
    @ApiModelProperty(value = "任务创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectedRepairComplete;

    /**
     * 任务创建时间
     */
    @ApiModelProperty(value = "任务创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskCreateTime;

    /**
     * 任务流入时间
     */
    @ApiModelProperty(value = "任务流入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskInflowTime;

    /**
     * 车辆接收时间
     */
    @ApiModelProperty(value = "车辆接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date vehicleReciveTime;

    /**
     * 车辆验收时间
     */
    @ApiModelProperty(value = "车辆验收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date vehicleCheckTime;

    /**
     * 车辆验收时间
     */
    @ApiModelProperty(value = "车辆验收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date vehicleRepairTime;

    @ApiModelProperty(value = "流程实例id")
    private Long instanceId;

    /**
     * 当前活动code
     */
    @ApiModelProperty(value = "当前活动code")
    private String currentActivityCode;

    /**
     * 当前活动名
     */
    @ApiModelProperty(value = "当前活动名")
    private String currentActivityName;

    /**
     * 当前活动code
     */
    @ApiModelProperty(value = "当前活动code")
    private String statusCode;

    /**
     * 当前状态名
     */
    @ApiModelProperty(value = "当前状态名")
    private String statusName;

    /**
     * 业务状态（0：分时租赁 1：长租 3：短租 4：公务用车）
     */
    @ApiModelProperty(value = "业务状态（0：分时租赁 1：长租 3：短租 4：公务用车）")
    private Integer renttype;

    @ApiModelProperty(value = "实际运营标签")
    private Integer factOperateTag;

    /**
     * 是否使用小程序 0-否 1-是
     */
    @ApiModelProperty(value = "是否使用小程序 0-否 1-是")
    private String isUsedApplets;

    /**
     * 资产状态
     * 0: 在建工程
     * 1: 固定资产
     * 2: 固定资产（待报废）
     * 3: 报废
     * 4: 固定资产(待处置)
     * 5: 固定资产(已处置)
     * 6: 以租代售
     * 7: 库存商品
     * 8: 已处置（未过户）
     */
    @ApiModelProperty(value = "资产状态(0:在建工程 1:固定资产 2:固定资产(待报废) 3:报废 4:固定资产(待处置) 5:固定资产(已处置) 6:以租代售 7:库存商品 8:已处置(未过户))")
    private Integer propertyStatus;

    /**
     * 产品线
     * 1: 车管中心
     * 2: 长租
     * 3: 短租
     * 4: 公务用车
     */
    @ApiModelProperty(value = "产品线(1:车管中心 2:长租 3:短租 4:公务用车)")
    private Integer productLine;

    /**
     * 子产品线
     * 1: 携程短租-短租
     * 2: 门店-短租
     * 3: 分时-短租
     * 4: 普通-长租
     * 5: 时行-长租
     * 6: 平台业务-长租
     * 7: 政企业务-长租
     * 8: 网约车业务-长租
     */
    @ApiModelProperty(value = "子产品线(1:携程短租-短租 2:门店-短租 3:分时-短租 4:普通-长租 5:时行-长租 6:平台业务-长租 7:政企业务-长租 8:网约车业务-长租)")
    private Integer subProductLine;

    @ApiModelProperty(value = "审核级别")
    private Integer advancedAuditLevel;

    @ApiModelProperty(value = "是否超时")
    private Integer isOverTime;

    @ApiModelProperty(value = "核损核价占据人id")
    private Long verificationLossTaskOperId;

    @ApiModelProperty(value = "核损核价占据人用户名")
    private String verificationLossTaskUsername;

    @ApiModelProperty(value = "核损核价占据人昵称")
    private String verificationLossTaskNickname;

    @ApiModelProperty(value = "维修总金额", example = "1000.00")
    private BigDecimal repairTotalAmount;

    @ApiModelProperty(value = "预估理赔金额", example = "500.00")
    private BigDecimal estimatedClaimAmount;

    @ApiModelProperty(value = "用户直付金额（用户承担）", example = "200.00")
    private BigDecimal userAssumedAmount;

    @ApiModelProperty(value = "自费金额", example = "1700.00")
    private BigDecimal selfFundedAmount;
}