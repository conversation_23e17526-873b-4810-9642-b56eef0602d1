package com.extracme.saas.autocare.model.dto.workflow;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动节点转换规则查询DTO
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "活动节点转换规则查询DTO")
public class ActivityTransitionQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "所属流程模板编号", example = "1")
    private Long workflowId;

    @ApiModelProperty(value = "转换起始节点编号", example = "HANDOVER")
    private String fromActivityCode;

    @ApiModelProperty(value = "目标节点编号", example = "INSPECTION")
    private String toActivityCode;

    @ApiModelProperty(value = "触发事件名称", example = "COMPLETE_HANDOVER")
    private String triggerEvent;
}
