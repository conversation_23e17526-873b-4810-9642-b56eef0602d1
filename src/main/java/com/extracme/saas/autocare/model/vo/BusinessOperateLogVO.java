package com.extracme.saas.autocare.model.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 业务操作日志VO
 */
@Data
@ApiModel(description = "业务操作日志信息")
public class BusinessOperateLogVO {

    @ApiModelProperty(value = "操作内容")
    private String opeContent;

    @ApiModelProperty(value = "当前环节编码")
    private String currentActivityCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "修改时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;
}
