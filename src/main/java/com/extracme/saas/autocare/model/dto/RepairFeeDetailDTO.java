package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "费用明细DTO")
public class RepairFeeDetailDTO {

    @ApiModelProperty(value = "维修总金额", example = "1000.00")
    private BigDecimal repairTotalAmount;

    @ApiModelProperty(value = "预估理赔金额", example = "500.00")
    private BigDecimal estimatedClaimAmount;

    @ApiModelProperty(value = "用户直付金额（用户承担）", example = "200.00")
    private BigDecimal userAssumedAmount;

    @ApiModelProperty(value = "自费金额", example = "1700.00")
    private BigDecimal selfFundedAmount;
}