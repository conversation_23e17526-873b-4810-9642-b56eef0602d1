package com.extracme.saas.autocare.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 数据同步结果VO
 */
@Data
@ApiModel(description = "数据同步结果VO")
public class DataSyncResultVO {

    @ApiModelProperty(value = "同步批次号")
    private String batchNo;

    @ApiModelProperty(value = "租户编码")
    private String tenantCode;

    @ApiModelProperty(value = "目标表名")
    private String targetTable;

    @ApiModelProperty(value = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "同步状态：SUCCESS、FAILED、PROCESSING")
    private String syncStatus;

    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    @ApiModelProperty(value = "成功记录数")
    private Integer successCount;

    @ApiModelProperty(value = "失败记录数")
    private Integer failedCount;

    @ApiModelProperty(value = "同步开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date syncStartTime;

    @ApiModelProperty(value = "同步结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date syncEndTime;

    @ApiModelProperty(value = "同步耗时（毫秒）")
    private Long syncDuration;

    @ApiModelProperty(value = "失败详情列表")
    private List<SyncFailureDetailVO> failureDetails;

    /**
     * 同步失败详情VO
     */
    @Data
    @ApiModel(description = "同步失败详情VO")
    public static class SyncFailureDetailVO {
        
        @ApiModelProperty(value = "源数据标识")
        private String sourceDataId;
        
        @ApiModelProperty(value = "失败原因")
        private String errorMessage;
        
        @ApiModelProperty(value = "失败时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date failureTime;
    }
}
