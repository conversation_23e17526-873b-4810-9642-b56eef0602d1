package com.extracme.saas.autocare.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 维修项目库创建Excel实体类
 * 用于批量创建维修项目的Excel导入
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(25)
@ColumnWidth(15)
public class RepairItemLibraryCreateExcel {

    /**
     * 项目名称（必填）
     */
    @ExcelProperty(value = "项目名称", index = 0)
    @NotBlank(message = "项目名称不能为空")
    @ColumnWidth(25)
    private String itemName;

    /**
     * 项目类型（必填）
     * 可选值：保养、终端、维修
     */
    @ExcelProperty(value = "项目类型", index = 1)
    @NotBlank(message = "项目类型不能为空")
    @ColumnWidth(15)
    private String itemType;

    /**
     * 车型信息（必填）
     */
    @ExcelProperty(value = "车型", index = 2)
    @ColumnWidth(20)
    private String vehicleModelInfo;

    /**
     * 工时费全国市场价（必填）
     * 必须为正数，最多两位小数
     */
    @ExcelProperty(value = "工时费全国市场价", index = 3)
    @NotNull(message = "工时费全国市场价不能为空")
    @ColumnWidth(20)
    private BigDecimal hourFeeNationalMarketPrice;

    /**
     * 工时费全国上限（可选）
     * 必须为正数，最多两位小数
     */
    @ExcelProperty(value = "工时费全国上限", index = 4)
    @ColumnWidth(20)
    private BigDecimal hourFeeNationalHighestPrice;

    /**
     * 材料费全国市场价（必填）
     * 必须为正数，最多两位小数
     */
    @ExcelProperty(value = "材料费全国市场价", index = 5)
    @NotNull(message = "材料费全国市场价不能为空")
    @ColumnWidth(20)
    private BigDecimal materialCostNationalMarketPrice;

    /**
     * 材料费全国上限（可选）
     * 必须为正数，最多两位小数
     */
    @ExcelProperty(value = "材料费全国上限", index = 6)
    @ColumnWidth(20)
    private BigDecimal materialCostNationalHighestPrice;
}
