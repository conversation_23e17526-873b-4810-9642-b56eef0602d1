package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table activity_status_transition
 */
public class ActivityStatusTransition implements Serializable {
    /**
     * Database Column Remarks:
     *   转换规则编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   关联的活动节点转换规则编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.activity_transition_id")
    private Long activityTransitionId;

    /**
     * Database Column Remarks:
     *   活动编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.activity_code")
    private String activityCode;

    /**
     * Database Column Remarks:
     *   起始状态编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.from_status_code")
    private String fromStatusCode;

    /**
     * Database Column Remarks:
     *   目标状态编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.to_status_code")
    private String toStatusCode;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   规则说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.description")
    private String description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.activity_transition_id")
    public Long getActivityTransitionId() {
        return activityTransitionId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.activity_transition_id")
    public void setActivityTransitionId(Long activityTransitionId) {
        this.activityTransitionId = activityTransitionId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.activity_code")
    public String getActivityCode() {
        return activityCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.activity_code")
    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.from_status_code")
    public String getFromStatusCode() {
        return fromStatusCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.from_status_code")
    public void setFromStatusCode(String fromStatusCode) {
        this.fromStatusCode = fromStatusCode == null ? null : fromStatusCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.to_status_code")
    public String getToStatusCode() {
        return toStatusCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.to_status_code")
    public void setToStatusCode(String toStatusCode) {
        this.toStatusCode = toStatusCode == null ? null : toStatusCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.description")
    public String getDescription() {
        return description;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.description")
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityTransitionId=").append(activityTransitionId);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", fromStatusCode=").append(fromStatusCode);
        sb.append(", toStatusCode=").append(toStatusCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", description=").append(description);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}