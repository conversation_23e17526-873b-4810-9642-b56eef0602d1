package com.extracme.saas.autocare.model.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车辆验收通过DTO
 * 
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Data
@ApiModel(value = "车辆验收通过信息")
public class VehicleInspectionApproveDTO {
    
    @NotBlank(message = "任务编号不能为空")
    @ApiModelProperty(value = "任务编号", required = true)
    private String taskNo;
    
    @NotNull(message = "验收图片不能为空")
    @Size(min = 1, message = "至少上传一张验收图片")
    @ApiModelProperty(value = "维修图片（验收图片）")
    private List<FileDTO> repairPicture;

    @NotNull(message = "验收视频不能为空")
    @Size(min = 1, message = "至少上传一个验收视频")
    @ApiModelProperty(value = "验收视频")
    private List<FileDTO> checkVideo;
}