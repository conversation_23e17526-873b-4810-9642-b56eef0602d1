package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 业务操作日志查询DTO
 */
@Data
@ApiModel(description = "业务操作日志查询参数")
public class BusinessOperateLogQueryDTO {

    @ApiModelProperty(value = "页码", required = true, example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum;

    @ApiModelProperty(value = "每页大小", required = true, example = "10")
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize;

    @ApiModelProperty(value = "业务类型（1：维修任务日志 2：修理厂日志 3：换件项目日志 4：修理项目日志 5：维修项目日志 6：维修项目独立信息日志 ")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @ApiModelProperty(value = "数据ID")
    @NotNull(message = "数据ID不能为空")
    private Long id;
}
