package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:27
 */
public class EvalLossInfoAssessment {
    /**
     * 定损单号（任务编号）
     */
    private String lossNo = StringUtils.EMPTY;
    /**
     * 报案号（任务编号）
     */
    private String reportCode = StringUtils.EMPTY;
    /**
     * 车牌号码
     */
    private String plateNo = StringUtils.EMPTY;
    /**
     * 发动机号
     */
    private String engineNo = StringUtils.EMPTY;
    /**
     * VIN码
     */
    private String vinNo = StringUtils.EMPTY;
    /**
     * 定损员所属分公司代码
     */
    private String comCode = StringUtils.EMPTY;
    /**
     * 定损员所属分公司名称
     */
    private String company = StringUtils.EMPTY;
    /**
     * 定损员所属中支代码
     */
    private String branchComCode = StringUtils.EMPTY;
    /**
     * 定损员所属中支名称
     */
    private String branchComName = StringUtils.EMPTY;
    /**
     * 定损员代码
     */
    private String evalHandlerCode = StringUtils.EMPTY;
    /**
     * 定损员名称
     */
    private String evalHandlerName = StringUtils.EMPTY;
    /**
     * 是否标的车（0：否 1：是）
     */
    private String isSubjectVehicle = "1";
    /**
     * 初登日期
     */
    private String enrolDate = StringUtils.EMPTY;
    /**
     * 定损方式（01：修复定损 02：推定全损 03：实际全损 04：协议定损）
     */
    private String evalTypeCode = "01";

    @XmlElement(name = "LossNo")
    public String getLossNo() {
        return lossNo;
    }

    public void setLossNo(String lossNo) {
        this.lossNo = lossNo;
    }

    @XmlElement(name = "ReportCode")
    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }

    @XmlElement(name = "PlateNo")
    public String getPlateNo() {
        return plateNo;
    }

    public void setPlateNo(String plateNo) {
        this.plateNo = plateNo;
    }

    @XmlElement(name = "EngineNo")
    public String getEngineNo() {
        return engineNo;
    }

    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo;
    }

    @XmlElement(name = "VinNo")
    public String getVinNo() {
        return vinNo;
    }

    public void setVinNo(String vinNo) {
        this.vinNo = vinNo;
    }

    @XmlElement(name = "ComCode")
    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    @XmlElement(name = "Company")
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    @XmlElement(name = "BranchComCode")
    public String getBranchComCode() {
        return branchComCode;
    }

    public void setBranchComCode(String branchComCode) {
        this.branchComCode = branchComCode;
    }

    @XmlElement(name = "BranchComName")
    public String getBranchComName() {
        return branchComName;
    }

    public void setBranchComName(String branchComName) {
        this.branchComName = branchComName;
    }

    @XmlElement(name = "EvalHandlerCode")
    public String getEvalHandlerCode() {
        return evalHandlerCode;
    }

    public void setEvalHandlerCode(String evalHandlerCode) {
        this.evalHandlerCode = evalHandlerCode;
    }

    @XmlElement(name = "EvalHandlerName")
    public String getEvalHandlerName() {
        return evalHandlerName;
    }

    public void setEvalHandlerName(String evalHandlerName) {
        this.evalHandlerName = evalHandlerName;
    }

    @XmlElement(name = "IsSubjectVehicle")
    public String getIsSubjectVehicle() {
        return isSubjectVehicle;
    }

    public void setIsSubjectVehicle(String isSubjectVehicle) {
        this.isSubjectVehicle = isSubjectVehicle;
    }

    @XmlElement(name = "EnrolDate")
    public String getEnrolDate() {
        return enrolDate;
    }

    public void setEnrolDate(String enrolDate) {
        this.enrolDate = enrolDate;
    }

    @XmlElement(name = "EvalTypeCode")
    public String getEvalTypeCode() {
        return evalTypeCode;
    }

    public void setEvalTypeCode(String evalTypeCode) {
        this.evalTypeCode = evalTypeCode;
    }
}
