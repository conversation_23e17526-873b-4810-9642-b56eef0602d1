package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "换件信息DTO")
public class LossFitInfoDTO {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "更换项目名称")
    private String itemName;

    @ApiModelProperty(value = "原厂编号")
    private String partCode;

    @ApiModelProperty(value = "价格")
    private BigDecimal auditMaterialFee;

    @ApiModelProperty(value = "数量")
    private BigDecimal auditCount;

    @ApiModelProperty(value = "价格小计")
    private BigDecimal apprPartSum;

    @ApiModelProperty(value = "残值")
    private BigDecimal apprRemainsPrice;

    @ApiModelProperty(value = "参考价格类型编码（1：4S价格 2：市场原厂价格 3：品牌价格 4：适用价格 5：再制造价格）")
    private String chgCompSetCode;
}
