package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(description = "维修信息DTO")
public class RepairInfoDTO {

    @ApiModelProperty(value = "预计修理天数")
    private Long expectedRepairDays;

    @ApiModelProperty(value = "确认车损类型", notes = "1：车辆原因 2：客户原因", example = "1")
    private Integer confirmCarDamageType;
}