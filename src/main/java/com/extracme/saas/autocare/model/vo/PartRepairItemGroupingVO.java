package com.extracme.saas.autocare.model.vo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "零件修理项目分组详情对象")
public class PartRepairItemGroupingVO {

    /** 零件分组ID/修理项目分组ID */
    @ApiModelProperty(value = "零件分组ID/修理项目分组ID", example = "1")
    private Long id;
    /** 零件分组名称/修理项目分组名称 */
    @ApiModelProperty(value = "零件分组名称/修理项目分组名称", example = "分组A")
    private String name;

    /**
     * 子节点列表
     */
    @ApiModelProperty("子节点列表")
    private List<PartRepairItemGroupingVO> children;

}