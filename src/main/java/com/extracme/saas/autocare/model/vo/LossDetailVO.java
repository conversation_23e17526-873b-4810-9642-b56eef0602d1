package com.extracme.saas.autocare.model.vo;

import java.util.List;

import com.extracme.saas.autocare.model.dto.LossAssistInfoDTO;
import com.extracme.saas.autocare.model.dto.LossFitInfoDTO;
import com.extracme.saas.autocare.model.dto.LossRepairInfoDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "定损详情VO")
public class LossDetailVO {

    @ApiModelProperty(value = "换件信息列表")
    private List<LossFitInfoDTO> lossFitInfoList;

    @ApiModelProperty(value = "维修信息列表")
    private List<LossRepairInfoDTO> lossRepairInfoList;

    @ApiModelProperty(value = "辅料信息列表")
    private List<LossAssistInfoDTO> lossAssistInfoList;
}
