package com.extracme.saas.autocare.model.vo.base;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "分页查询基础VO")
public class BasePageVO<T> {
    
    @ApiModelProperty(value = "总记录数", example = "100")
    private Long total;
    
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer pageNum;
    
    @ApiModelProperty(value = "每页记录数", example = "10")
    private Integer pageSize;
    
    @ApiModelProperty(value = "总页数", example = "10")
    private Integer pages;
    
    @ApiModelProperty(value = "数据列表")
    private List<T> list;
    
    @ApiModelProperty(value = "是否有下一页", example = "true")
    private Boolean hasNextPage;
    
    @ApiModelProperty(value = "是否有上一页", example = "false")
    private Boolean hasPreviousPage;
    
    @ApiModelProperty(value = "是否为第一页", example = "true")
    private Boolean isFirstPage;
    
    @ApiModelProperty(value = "是否为最后一页", example = "false")
    private Boolean isLastPage;
    
    public static <T> BasePageVO<T> of(List<T> list, PageInfo<?> pageInfo) {
        BasePageVO<T> result = new BasePageVO<>();
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setPages(pageInfo.getPages());
        result.setList(list);
        result.setHasNextPage(pageInfo.isHasNextPage());
        result.setHasPreviousPage(pageInfo.isHasPreviousPage());
        result.setIsFirstPage(pageInfo.isIsFirstPage());
        result.setIsLastPage(pageInfo.isIsLastPage());
        return result;
    }
} 