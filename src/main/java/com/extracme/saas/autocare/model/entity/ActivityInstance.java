package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table activity_instance
 */
public class ActivityInstance implements Serializable {
    /**
     * Database Column Remarks:
     *   活动实例记录编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   所属流程实例编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.instance_id")
    private Long instanceId;

    /**
     * Database Column Remarks:
     *   目标活动节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.to_activity_code")
    private String toActivityCode;

    /**
     * Database Column Remarks:
     *   来源节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.from_activity_code")
    private String fromActivityCode;

    /**
     * Database Column Remarks:
     *   触发转换的规则编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.transition_id")
    private Long transitionId;

    /**
     * Database Column Remarks:
     *   活动开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.start_time")
    private Date startTime;

    /**
     * Database Column Remarks:
     *   活动结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.end_time")
    private Date endTime;

    /**
     * Database Column Remarks:
     *   活动总耗时（单位：秒）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.duration")
    private Integer duration;

    /**
     * Database Column Remarks:
     *   当前状态编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.current_status_code")
    private String currentStatusCode;

    /**
     * Database Column Remarks:
     *   操作人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.operator")
    private String operator;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   备注说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.remarks")
    private String remarks;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.instance_id")
    public Long getInstanceId() {
        return instanceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.instance_id")
    public void setInstanceId(Long instanceId) {
        this.instanceId = instanceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.to_activity_code")
    public String getToActivityCode() {
        return toActivityCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.to_activity_code")
    public void setToActivityCode(String toActivityCode) {
        this.toActivityCode = toActivityCode == null ? null : toActivityCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.from_activity_code")
    public String getFromActivityCode() {
        return fromActivityCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.from_activity_code")
    public void setFromActivityCode(String fromActivityCode) {
        this.fromActivityCode = fromActivityCode == null ? null : fromActivityCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.transition_id")
    public Long getTransitionId() {
        return transitionId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.transition_id")
    public void setTransitionId(Long transitionId) {
        this.transitionId = transitionId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.start_time")
    public Date getStartTime() {
        return startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.start_time")
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.end_time")
    public Date getEndTime() {
        return endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.end_time")
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.duration")
    public Integer getDuration() {
        return duration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.duration")
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.current_status_code")
    public String getCurrentStatusCode() {
        return currentStatusCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.current_status_code")
    public void setCurrentStatusCode(String currentStatusCode) {
        this.currentStatusCode = currentStatusCode == null ? null : currentStatusCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.operator")
    public String getOperator() {
        return operator;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.operator")
    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.remarks")
    public String getRemarks() {
        return remarks;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.remarks")
    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", instanceId=").append(instanceId);
        sb.append(", toActivityCode=").append(toActivityCode);
        sb.append(", fromActivityCode=").append(fromActivityCode);
        sb.append(", transitionId=").append(transitionId);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", duration=").append(duration);
        sb.append(", currentStatusCode=").append(currentStatusCode);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", remarks=").append(remarks);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}