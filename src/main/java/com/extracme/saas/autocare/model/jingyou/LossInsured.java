package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-13 14:22
 */
public class LossInsured {
    /**
     * 主键
     */
    private String id= StringUtils.EMPTY;
    /**
     * 所属保单主键
     */
    private String policyId= StringUtils.EMPTY;
    /**
     * 险种代码
     */
    private String riskCode= StringUtils.EMPTY;
    /**
     * 险种名称
     */
    private String riskName= StringUtils.EMPTY;
    /**
     * 险别代码
     */
    private String itemCode= StringUtils.EMPTY;
    /**
     * 险别名称
     */
    private String itemName= StringUtils.EMPTY;
    /**
     * 保险金额
     */
    private String totalInsSum= StringUtils.EMPTY;

    @XmlElement(name = "Id")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @XmlElement(name = "PolicyId")
    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    @XmlElement(name = "RiskCode")
    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    @XmlElement(name = "RiskName")
    public String getRiskName() {
        return riskName;
    }

    public void setRiskName(String riskName) {
        this.riskName = riskName;
    }

    @XmlElement(name = "ItemCode")
    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    @XmlElement(name = "ItemName")
    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    @XmlElement(name = "TotalInsSum")
    public String getTotalInsSum() {
        return totalInsSum;
    }

    public void setTotalInsSum(String totalInsSum) {
        this.totalInsSum = totalInsSum;
    }
}
