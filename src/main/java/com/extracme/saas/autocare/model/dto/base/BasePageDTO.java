package com.extracme.saas.autocare.model.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "分页查询基础DTO")
public class BasePageDTO {
    @ApiModelProperty(value = "页码", example = "1")
    @NotNull(message = "页码不能为空")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页记录数", example = "10")
    @NotNull(message = "每页记录数不能为空")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "字典名称（模糊查询）", example = "性别")
    private String dictName;

    @ApiModelProperty(value = "字典编码（模糊查询）", example = "SEX")
    private String dictCode;
}