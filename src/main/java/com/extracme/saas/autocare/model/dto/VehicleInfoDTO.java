package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "车辆信息DTO")
public class VehicleInfoDTO {

    @ApiModelProperty(value = "车辆VIN码")
    private String vin;

    @ApiModelProperty(value = "车牌号", example = "沪A12345")
    private String licensePlate;

    @ApiModelProperty(value = "车型ID", example = "3001")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名称", example = "奥迪A6L 2.0T")
    private String vehicleModelName;

    @ApiModelProperty(value = "资产状态(0-在建工程 1-固定资产 2-固定资产待报废 3-报废 4-固定资产待处置 5-固定资产已处置 6-以租代售 7-库存商品 8-已处置未过户)", example = "1")
    private Integer propertyStatus;

    @ApiModelProperty(value = "产品线(1-车管中心 2-长租 3-短租 4-公务用车)", example = "2")
    private Integer productLine;

    @ApiModelProperty(value = "子产品线(1-携程短租 2-门店短租 3-分时短租 4-普通长租 5-时行长租 6-平台业务长租 7-政企业务长租 8-网约车业务长租)", example = "4")
    private Integer subProductLine;

    @ApiModelProperty(value = "车辆运营机构ID", example = "ORG001")
    private String vehicleOrgId;

    @ApiModelProperty(value = "车辆运营机构名称", example = "上海分公司")
    private String vehicleOrgName;

    @ApiModelProperty(value = "车辆所属组织机构ID", example = "ORG002")
    private String vehicleOperateOrgId;

    @ApiModelProperty(value = "车辆所属组织机构名称", example = "浦东运营中心")
    private String vehicleOperateOrgName;

    @ApiModelProperty(value = "实际运营标签(0-未投车辆 1-短租运营车辆 2-长租运营车辆 3-备库车辆 4-待退运车辆 5-已处置车辆 6-特殊车辆)", example = "2")
    private Integer factOperateTag;

    @ApiModelProperty(value = "总里程数(单位:公里)", example = "50000.00")
    private BigDecimal totalMileage;

    @ApiModelProperty(value = "终端实时里程数(核损时)", example = "50100")
    private String terminalMileage;

    @ApiModelProperty(value = "预审车辆处置残值", example = "100000.00")
    private BigDecimal preReviewVehicleScrapeValue;

    @ApiModelProperty(value = "预审车辆处置残值时间")
    private Date preReviewVehicleScrapeTime;
}