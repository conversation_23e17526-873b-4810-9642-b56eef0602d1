package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * 租户创建DTO
 */
@Data
@ApiModel(description = "租户创建DTO")
public class TenantDTO {
    
    @ApiModelProperty(value = "租户名称", required = true, example = "测试租户")
    @NotBlank(message = "租户名称不能为空")
    private String tenantName;
    
    @ApiModelProperty(value = "租户编码", required = true, example = "TEST_TENANT")
    @NotBlank(message = "租户编码不能为空")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "租户编码必须以大写字母开头，只能包含大写字母、数字和下划线")
    private String tenantCode;
    
    @ApiModelProperty(value = "联系人姓名", example = "张三")
    private String contactName;
    
    @ApiModelProperty(value = "联系人电话", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String contactPhone;
    
    @ApiModelProperty(value = "联系人邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String contactEmail;
    
    @ApiModelProperty(value = "租约过期时间")
    private Date expireTime;
    
    @ApiModelProperty(value = "最大用户数量", example = "100")
    private Integer maxUserCount;
    
    @ApiModelProperty(value = "状态：0-禁用，1-启用", example = "1")
    private Integer status = 1;
}
