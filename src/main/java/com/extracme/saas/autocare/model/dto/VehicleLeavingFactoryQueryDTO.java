package com.extracme.saas.autocare.model.dto;

import java.util.Date;
import java.util.List;

import org.hibernate.validator.constraints.Length;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车辆出厂登记查询DTO
 *
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "车辆出厂登记查询DTO")
public class VehicleLeavingFactoryQueryDTO extends BasePageDTO {
    
    /**
     * 任务编号
     */
    @Length(max = 50, message = "任务编号长度不能超过50")
    @ApiModelProperty(value = "任务编号", example = "TASK20240525001")
    private String taskNo;
    
    /**
     * 车辆所属组织机构ID
     */
    @Length(max = 50, message = "组织机构ID长度不能超过50")
    @ApiModelProperty(value = "车辆所属组织机构ID", example = "ORG001")
    private String orgId;

    @ApiModelProperty(value = "登录人机构id", example = "ORG001")
    private List<String> loginUserOrgIds;

    /**
     * 车架号
     */
    @Length(max = 20, message = "车架号长度不能超过20")
    @ApiModelProperty(value = "车架号", example = "LSVAU2183C2184235")
    private String vin;
    
    /**
     * 车牌号
     */
    @Length(max = 20, message = "车牌号长度不能超过20")
    @ApiModelProperty(value = "车牌号", example = "沪A12345")
    private String vehicleNo;
    
    /**
     * 车型ID
     */
    @ApiModelProperty(value = "车型ID", example = "1001")
    private Long vehicleModelId;
    
    /**
     * 修理类型ID 1:事故维修 2:自费维修 3:车辆保养
     */
    @ApiModelProperty(value = "修理类型ID", example = "1", notes = "1:事故维修 2:自费维修 3:车辆保养")
    private Integer repairTypeId;
    
    /**
     * 修理厂名称
     */
    @Length(max = 100, message = "修理厂名称长度不能超过100")
    @ApiModelProperty(value = "修理厂名称", example = "上海XX汽车修理厂")
    private String repairDepotName;

    /**
     * 修理厂ID
     */
    @Length(max = 30, message = "修理厂ID长度不能超过30")
    @ApiModelProperty(value = "修理厂ID", example = "REPAIR001")
    private String repairDepotId;
    
    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间", example = "2024-05-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTaskInflowTime;
    
    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间", example = "2024-05-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTaskInflowTime;
    
    /**
     * 车辆接收开始时间
     */
    @ApiModelProperty(value = "车辆接收开始时间", example = "2024-05-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startVehicleReciveTime;
    
    /**
     * 车辆接收结束时间
     */
    @ApiModelProperty(value = "车辆接收结束时间", example = "2024-05-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endVehicleReciveTime;
    
    /**
     * 是否需要复勘 0:否 1:是
     */
    @ApiModelProperty(value = "是否需要复勘", example = "0", notes = "0:否 1:是")
    private String resurveyFlag;
    
    /**
     * 超时 0:维修超时 1:未超时
     */
    @ApiModelProperty(value = "超时", example = "1", notes = "0:维修超时 1:未超时")
    private String timeOut;
    
    /**
     * sap发送状态 0未发送 1已发送 2发送失败 3需重新发送
     */
    @ApiModelProperty(value = "sap发送状态", example = "1", notes = "0:未发送 1:已发送 2:发送失败 3:需重新发送")
    private Integer sapSendStatus;
    
    /**
     * 任务类型 1 待登记 2 已登记 3 已关闭
     */
    @ApiModelProperty(value = "任务类型", example = "1", notes = "1:待登记 2:已登记 3:已关闭")
    private Integer leavingStatus;

    /**
     * 当前活动节点编码
     */
    @ApiModelProperty(value = "当前活动节点code")
    private String currentActivityCode;

    /**
     * 提车开始时间
     */
    @ApiModelProperty(value = "提车开始时间", example = "2024-05-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDeliveryTime;
    
    /**
     * 提车结束时间
     */
    @ApiModelProperty(value = "提车结束时间", example = "2024-05-31 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDeliveryTime;
    
    /**
     * 车辆业务状态（0：分时租赁 1：长租 3：短租 4：公务用车）
     */
    @ApiModelProperty(value = "车辆业务状态", example = "1", notes = "0:分时租赁 1:长租 3:短租 4:公务用车")
    private Integer renttype;
    
    /**
     * 全部车辆业务状态
     */
    @ApiModelProperty(value = "全部车辆业务状态", example = "[0,1,3,4]", notes = "0:分时租赁 1:长租 3:短租 4:公务用车")
    private List<Integer> renttypeList;
    
    /**
     * 验收完成开始时间
     */
    @ApiModelProperty(value = "验收完成开始时间", example = "2024-05-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startVehicleCheckTime;
    
    /**
     * 验收完成结束时间
     */
    @ApiModelProperty(value = "验收完成结束时间", example = "2024-05-31 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endVehicleCheckTime;
    
    /**
     * 实际运营标签(0-未投车辆 1-短租运营车辆 2-长租运营车辆 3-备库车辆 4-待退运车辆 5-已处置车辆 6-特殊车辆)
     */
    @ApiModelProperty(value = "实际运营标签", example = "2", notes = "0:未投车辆 1:短租运营车辆 2:长租运营车辆 3:备库车辆 4:待退运车辆 5:已处置车辆 6:特殊车辆")
    private Integer factOperateTag;

    /**
     * 事故编号
     */
    @Length(max = 50, message = "事故编号长度不能超过50")
    @ApiModelProperty(value = "事故编号", example = "ACC20240525001")
    private String accidentNo;
    
    /**
     * 事故转自费标识 0否 1是
     */
    @ApiModelProperty(value = "事故转自费标识", example = "0", notes = "0:否 1:是")
    private Integer reviewToSelFeeFlag;
}
