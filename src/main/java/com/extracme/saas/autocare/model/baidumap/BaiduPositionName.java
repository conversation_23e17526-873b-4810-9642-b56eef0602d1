package com.extracme.saas.autocare.model.baidumap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 百度地图逆地理编码响应结果Bean
 * 用于封装百度地图API逆地理编码接口的返回数据，将经纬度坐标转换为结构化地址信息
 * 
 * <AUTHOR>
 * @date 2018年12月5日 下午4:27:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaiduPositionName {
    
    /**
     * 状态码，0表示成功
     */
    private Integer status;
    
    /**
     * 逆地理编码结果信息
     */
    private ResultInfo result;
    
    /**
     * 逆地理编码结果信息内部类
     * 包含位置的详细地址信息和坐标数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResultInfo {
        
        /**
         * 经纬度坐标信息
         */
        private LagLongInfo location;
        
        /**
         * 结构化地址信息
         */
        private String formattedAddress;
        
        /**
         * 坐标所在商圈信息
         */
        private String business;
        
        /**
         * 地址组件详细信息
         */
        private AddressComponent addressComponent;
        
        /**
         * 地址组件详细信息内部类
         * 包含国家、省份、城市、区县等层级地址信息
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class AddressComponent {
            
            /**
             * 国家
             */
            private String country;
            
            /**
             * 省份
             */
            private String province;
            
            /**
             * 城市
             */
            private String city;
            
            /**
             * 城市级别
             */
            private Integer city_level;
            
            /**
             * 区县
             */
            private String district;
            
            /**
             * 乡镇
             */
            private String town;
            
            /**
             * 行政区划代码
             */
            private String adcode;
            
            /**
             * 街道
             */
            private String street;
            
            /**
             * 门牌号
             */
            private String street_number;
            
            /**
             * 方向
             */
            private String direction;
            
            /**
             * 距离
             */
            private String distance;
        }
        
        /**
         * 经纬度坐标信息内部类
         * 包含具体的经度和纬度数值
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class LagLongInfo {
            
            /**
             * 经度
             */
            private float lng;
            
            /**
             * 纬度
             */
            private float lat;
        }
    }
}
