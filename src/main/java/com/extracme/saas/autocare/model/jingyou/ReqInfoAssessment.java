package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:23
 */
public class ReqInfoAssessment {
    /**
     * 是否包含保单、报案信息（0：否 1：是）
     */
    private String ifNewLossFlag = "1";
    /**
     * 核损标记（0：正常定损 1：核损退回进定损）
     */
    private String auditLossFlag = "0";
    /**
     * 返回URL
     */
    private String returnURL = StringUtils.EMPTY;
    /**
     * 返回刷新URL
     */
    private String refreshURL = StringUtils.EMPTY;
    /**
     * 重开次数
     */
    private String clmTms = StringUtils.EMPTY;

    @XmlElement(name = "IfNewLossFlag")
    public String getIfNewLossFlag() {
        return ifNewLossFlag;
    }

    public void setIfNewLossFlag(String ifNewLossFlag) {
        this.ifNewLossFlag = ifNewLossFlag;
    }

    @XmlElement(name = "AuditLossFlag")
    public String getAuditLossFlag() {
        return auditLossFlag;
    }

    public void setAuditLossFlag(String auditLossFlag) {
        this.auditLossFlag = auditLossFlag;
    }

    @XmlElement(name = "ReturnURL")
    public String getReturnURL() {
        return returnURL;
    }

    public void setReturnURL(String returnURL) {
        this.returnURL = returnURL;
    }

    @XmlElement(name = "RefreshURL")
    public String getRefreshURL() {
        return refreshURL;
    }

    public void setRefreshURL(String refreshURL) {
        this.refreshURL = refreshURL;
    }

    @XmlElement(name = "ClmTms")
    public String getClmTms() {
        return clmTms;
    }

    public void setClmTms(String clmTms) {
        this.clmTms = clmTms;
    }
}
