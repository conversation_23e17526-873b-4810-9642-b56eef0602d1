package com.extracme.saas.autocare.model.jingyou;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-03-12 17:27
 */
public class EvalLossInfoAssBack {
    /**
     * 车损标的主键
     */
    private Integer dmgVhclId = 0;
    /**
     * 报案号
     */
    private String reportCode = StringUtils.EMPTY;
    /**
     * 定损单号
     */
    private String lossNo = StringUtils.EMPTY;
    /**
     * 定损车型编码
     */
    private String vehCertainCode = StringUtils.EMPTY;
    /**
     * 定损车型名称
     */
    private String vehCertainName = StringUtils.EMPTY;
    /**
     * 车组编码
     */
    private String vehGroupCode = StringUtils.EMPTY;
    /**
     * 车组名称
     */
    private String groupName = StringUtils.EMPTY;
    /**
     * 定损品牌编码
     */
    private String vehBrandCode = StringUtils.EMPTY;
    /**
     * 定损品牌名称
     */
    private String brandName = StringUtils.EMPTY;
    /**
     * 自定义车型标志
     */
    private String selfConfigFlag = StringUtils.EMPTY;
    /**
     * 定损施救费用
     */
    private BigDecimal salvageFee = BigDecimal.ZERO;
    /**
     * 定损折扣残值
     */
    private BigDecimal remnantFee = BigDecimal.ZERO;
    /**
     * 定损管理费合计
     */
    private BigDecimal manageFee = BigDecimal.ZERO;
    /**
     * 定损换件合计
     */
    private BigDecimal evalPartSum = BigDecimal.ZERO;
    /**
     * 定损工时合计
     */
    private BigDecimal evalRepairSum = BigDecimal.ZERO;
    /**
     * 定损辅料合计
     */
    private BigDecimal evalMateSum = BigDecimal.ZERO;
    /**
     * 定损自付合计
     */
    private BigDecimal selfPaySum = BigDecimal.ZERO;
    /**
     * 定损外修合计
     */
    private BigDecimal outerSum = BigDecimal.ZERO;
    /**
     * 定损外修减损合计
     */
    private BigDecimal derogationSum = BigDecimal.ZERO;
    /**
     * 定损合计
     */
    private BigDecimal sumLossAmount = BigDecimal.ZERO;
    /**
     * 定损员代码
     */
    private String handlerCode = StringUtils.EMPTY;
    /**
     * 备注
     */
    private String remark = StringUtils.EMPTY;
    /**
     * 价格类型
     */
    private String priceType = StringUtils.EMPTY;
    /**
     * 修理厂ID
     */
    private String repairFacID = StringUtils.EMPTY;
    /**
     * 修理厂代码
     */
    private String repairFacCode = StringUtils.EMPTY;
    /**
     * 修理厂类型
     */
    private String repairFacType = StringUtils.EMPTY;
    /**
     * 修理厂资质
     */
    private String factoryQualification = StringUtils.EMPTY;
    /**
     * 修理厂联系方式
     */
    private String repairFacPhone = StringUtils.EMPTY;
    /**
     * 修理厂名称
     */
    private String repairFacName = StringUtils.EMPTY;
    /**
     * VIN码
     */
    private String vinNo = StringUtils.EMPTY;
    /**
     * 发动机号
     */
    private String engineNo = StringUtils.EMPTY;
    /**
     * 车牌号
     */
    private String plateNo = StringUtils.EMPTY;
    /**
     * 初登日期
     */
    private String enrolDate = StringUtils.EMPTY;
    /**
     * 自核价标记
     */
    private String selfEstiFlag = StringUtils.EMPTY;
    /**
     * 自核损标记
     */
    private String selfApproveFlag = StringUtils.EMPTY;
    /**
     * 险别代码
     */
    private String insuranceCode = StringUtils.EMPTY;
    /**
     * 险别名称
     */
    private String insuranceName = StringUtils.EMPTY;
    /**
     * 组织机构代码
     */
    private String mixCode = StringUtils.EMPTY;
    /**
     * 定型方式
     */
    private String vehicleSettingMode = StringUtils.EMPTY;
    /**
     * 定损车型与承保车型是否匹配
     */
    private String modelMatchFlag = StringUtils.EMPTY;
    /**
     * 定损方式
     */
    private String evalTypeCode = StringUtils.EMPTY;
    /**
     * 事故类型
     */
    private String accidentCauseCode = StringUtils.EMPTY;
    /**
     * 重开次数
     */
    private String clmTms = StringUtils.EMPTY;
    /**
     * 全损整车损失金额
     */
    private BigDecimal allLoseSum = BigDecimal.ZERO;
    /**
     * 全损整车残值金额
     */
    private BigDecimal allLoseRemainsSum = BigDecimal.ZERO;
    /**
     * 全损整车施救费金额
     */
    private BigDecimal allLoseSalvSum = BigDecimal.ZERO;
    /**
     * 全损合计金额
     */
    private BigDecimal allLoseTotalSum = BigDecimal.ZERO;
    /**
     * 换件折扣
     */
    private BigDecimal partDiscountPercent = BigDecimal.ZERO;
    /**
     * 里程数
     */
    private String travelMileAges = StringUtils.EMPTY;

    @XmlElement(name = "DmgVhclId")
    public Integer getDmgVhclId() {
        return dmgVhclId;
    }

    public void setDmgVhclId(Integer dmgVhclId) {
        this.dmgVhclId = dmgVhclId;
    }

    @XmlElement(name = "ReportCode")
    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }

    @XmlElement(name = "LossNo")
    public String getLossNo() {
        return lossNo;
    }

    public void setLossNo(String lossNo) {
        this.lossNo = lossNo;
    }

    @XmlElement(name = "VehCertainCode")
    public String getVehCertainCode() {
        return vehCertainCode;
    }

    public void setVehCertainCode(String vehCertainCode) {
        this.vehCertainCode = vehCertainCode;
    }

    @XmlElement(name = "VehCertainName")
    public String getVehCertainName() {
        return vehCertainName;
    }

    public void setVehCertainName(String vehCertainName) {
        this.vehCertainName = vehCertainName;
    }

    @XmlElement(name = "VehGroupCode")
    public String getVehGroupCode() {
        return vehGroupCode;
    }

    public void setVehGroupCode(String vehGroupCode) {
        this.vehGroupCode = vehGroupCode;
    }

    @XmlElement(name = "GroupName")
    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    @XmlElement(name = "VehBrandCode")
    public String getVehBrandCode() {
        return vehBrandCode;
    }

    public void setVehBrandCode(String vehBrandCode) {
        this.vehBrandCode = vehBrandCode;
    }

    @XmlElement(name = "BrandName")
    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    @XmlElement(name = "SelfConfigFlag")
    public String getSelfConfigFlag() {
        return selfConfigFlag;
    }

    public void setSelfConfigFlag(String selfConfigFlag) {
        this.selfConfigFlag = selfConfigFlag;
    }

    @XmlElement(name = "SalvageFee")
    public BigDecimal getSalvageFee() {
        return salvageFee;
    }

    public void setSalvageFee(BigDecimal salvageFee) {
        this.salvageFee = salvageFee;
    }

    @XmlElement(name = "RemnantFee")
    public BigDecimal getRemnantFee() {
        return remnantFee;
    }

    public void setRemnantFee(BigDecimal remnantFee) {
        this.remnantFee = remnantFee;
    }

    @XmlElement(name = "ManageFee")
    public BigDecimal getManageFee() {
        return manageFee;
    }

    public void setManageFee(BigDecimal manageFee) {
        this.manageFee = manageFee;
    }

    @XmlElement(name = "EvalPartSum")
    public BigDecimal getEvalPartSum() {
        return evalPartSum;
    }

    public void setEvalPartSum(BigDecimal evalPartSum) {
        this.evalPartSum = evalPartSum;
    }

    @XmlElement(name = "EvalRepairSum")
    public BigDecimal getEvalRepairSum() {
        return evalRepairSum;
    }

    public void setEvalRepairSum(BigDecimal evalRepairSum) {
        this.evalRepairSum = evalRepairSum;
    }

    @XmlElement(name = "EvalMateSum")
    public BigDecimal getEvalMateSum() {
        return evalMateSum;
    }

    public void setEvalMateSum(BigDecimal evalMateSum) {
        this.evalMateSum = evalMateSum;
    }

    @XmlElement(name = "SelfPaySum")
    public BigDecimal getSelfPaySum() {
        return selfPaySum;
    }

    public void setSelfPaySum(BigDecimal selfPaySum) {
        this.selfPaySum = selfPaySum;
    }

    @XmlElement(name = "OuterSum")
    public BigDecimal getOuterSum() {
        return outerSum;
    }

    public void setOuterSum(BigDecimal outerSum) {
        this.outerSum = outerSum;
    }

    @XmlElement(name = "DerogationSum")
    public BigDecimal getDerogationSum() {
        return derogationSum;
    }

    public void setDerogationSum(BigDecimal derogationSum) {
        this.derogationSum = derogationSum;
    }

    @XmlElement(name = "SumLossAmount")
    public BigDecimal getSumLossAmount() {
        return sumLossAmount;
    }

    public void setSumLossAmount(BigDecimal sumLossAmount) {
        this.sumLossAmount = sumLossAmount;
    }

    @XmlElement(name = "HandlerCode")
    public String getHandlerCode() {
        return handlerCode;
    }

    public void setHandlerCode(String handlerCode) {
        this.handlerCode = handlerCode;
    }

    @XmlElement(name = "Remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @XmlElement(name = "PriceType")
    public String getPriceType() {
        return priceType;
    }

    public void setPriceType(String priceType) {
        this.priceType = priceType;
    }

    @XmlElement(name = "RepairFacID")
    public String getRepairFacID() {
        return repairFacID;
    }

    public void setRepairFacID(String repairFacID) {
        this.repairFacID = repairFacID;
    }

    @XmlElement(name = "RepairFacCode")
    public String getRepairFacCode() {
        return repairFacCode;
    }

    public void setRepairFacCode(String repairFacCode) {
        this.repairFacCode = repairFacCode;
    }

    @XmlElement(name = "RepairFacType")
    public String getRepairFacType() {
        return repairFacType;
    }

    public void setRepairFacType(String repairFacType) {
        this.repairFacType = repairFacType;
    }

    @XmlElement(name = "FactoryQualification")
    public String getFactoryQualification() {
        return factoryQualification;
    }

    public void setFactoryQualification(String factoryQualification) {
        this.factoryQualification = factoryQualification;
    }

    @XmlElement(name = "RepairFacPhone")
    public String getRepairFacPhone() {
        return repairFacPhone;
    }

    public void setRepairFacPhone(String repairFacPhone) {
        this.repairFacPhone = repairFacPhone;
    }

    @XmlElement(name = "RepairFacName")
    public String getRepairFacName() {
        return repairFacName;
    }

    public void setRepairFacName(String repairFacName) {
        this.repairFacName = repairFacName;
    }

    @XmlElement(name = "RepairFacName")
    public String getVinNo() {
        return vinNo;
    }

    public void setVinNo(String vinNo) {
        this.vinNo = vinNo;
    }

    @XmlElement(name = "EngineNo")
    public String getEngineNo() {
        return engineNo;
    }

    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo;
    }

    @XmlElement(name = "PlateNo")
    public String getPlateNo() {
        return plateNo;
    }

    public void setPlateNo(String plateNo) {
        this.plateNo = plateNo;
    }

    @XmlElement(name = "EnrolDate")
    public String getEnrolDate() {
        return enrolDate;
    }

    public void setEnrolDate(String enrolDate) {
        this.enrolDate = enrolDate;
    }

    @XmlElement(name = "SelfEstiFlag")
    public String getSelfEstiFlag() {
        return selfEstiFlag;
    }

    public void setSelfEstiFlag(String selfEstiFlag) {
        this.selfEstiFlag = selfEstiFlag;
    }

    @XmlElement(name = "SelfApproveFlag")
    public String getSelfApproveFlag() {
        return selfApproveFlag;
    }

    public void setSelfApproveFlag(String selfApproveFlag) {
        this.selfApproveFlag = selfApproveFlag;
    }

    @XmlElement(name = "InsuranceCode")
    public String getInsuranceCode() {
        return insuranceCode;
    }

    public void setInsuranceCode(String insuranceCode) {
        this.insuranceCode = insuranceCode;
    }

    @XmlElement(name = "InsuranceName")
    public String getInsuranceName() {
        return insuranceName;
    }

    public void setInsuranceName(String insuranceName) {
        this.insuranceName = insuranceName;
    }

    @XmlElement(name = "MixCode")
    public String getMixCode() {
        return mixCode;
    }

    public void setMixCode(String mixCode) {
        this.mixCode = mixCode;
    }

    @XmlElement(name = "VehicleSettingMode")
    public String getVehicleSettingMode() {
        return vehicleSettingMode;
    }

    public void setVehicleSettingMode(String vehicleSettingMode) {
        this.vehicleSettingMode = vehicleSettingMode;
    }

    @XmlElement(name = "ModelMatchFlag")
    public String getModelMatchFlag() {
        return modelMatchFlag;
    }

    public void setModelMatchFlag(String modelMatchFlag) {
        this.modelMatchFlag = modelMatchFlag;
    }

    @XmlElement(name = "EvalTypeCode")
    public String getEvalTypeCode() {
        return evalTypeCode;
    }

    public void setEvalTypeCode(String evalTypeCode) {
        this.evalTypeCode = evalTypeCode;
    }

    @XmlElement(name = "AccidentCauseCode")
    public String getAccidentCauseCode() {
        return accidentCauseCode;
    }

    public void setAccidentCauseCode(String accidentCauseCode) {
        this.accidentCauseCode = accidentCauseCode;
    }

    @XmlElement(name = "ClmTms")
    public String getClmTms() {
        return clmTms;
    }

    public void setClmTms(String clmTms) {
        this.clmTms = clmTms;
    }

    @XmlElement(name = "AllLoseSum")
    public BigDecimal getAllLoseSum() {
        return allLoseSum;
    }

    public void setAllLoseSum(BigDecimal allLoseSum) {
        this.allLoseSum = allLoseSum;
    }

    @XmlElement(name = "AllLoseRemainsSum")
    public BigDecimal getAllLoseRemainsSum() {
        return allLoseRemainsSum;
    }

    public void setAllLoseRemainsSum(BigDecimal allLoseRemainsSum) {
        this.allLoseRemainsSum = allLoseRemainsSum;
    }

    @XmlElement(name = "AllLoseSalvSum")
    public BigDecimal getAllLoseSalvSum() {
        return allLoseSalvSum;
    }

    public void setAllLoseSalvSum(BigDecimal allLoseSalvSum) {
        this.allLoseSalvSum = allLoseSalvSum;
    }

    @XmlElement(name = "AllLoseTotalSum")
    public BigDecimal getAllLoseTotalSum() {
        return allLoseTotalSum;
    }

    public void setAllLoseTotalSum(BigDecimal allLoseTotalSum) {
        this.allLoseTotalSum = allLoseTotalSum;
    }

    @XmlElement(name = "PartDiscountPercent")
    public BigDecimal getPartDiscountPercent() {
        return partDiscountPercent;
    }

    public void setPartDiscountPercent(BigDecimal partDiscountPercent) {
        this.partDiscountPercent = partDiscountPercent;
    }

    @XmlElement(name = "TravelMileAges")
    public String getTravelMileAges() {
        return travelMileAges;
    }

    public void setTravelMileAges(String travelMileAges) {
        this.travelMileAges = travelMileAges;
    }
}
