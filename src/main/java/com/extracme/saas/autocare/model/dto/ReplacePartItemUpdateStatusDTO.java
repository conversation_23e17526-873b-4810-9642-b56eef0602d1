package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "换件项目修改状态DTO")
public class ReplacePartItemUpdateStatusDTO {

    @ApiModelProperty(value = "换件项目ID不能为空", required = true)
    @NotNull(message = "换件项目ID不能为空")
    private Long id;

    @ApiModelProperty(value = "换件项目状态(0-无效 1-有效)", required = true)
    @NotNull(message = "换件项目状态不能为空")
    private Integer itemStatus;

}