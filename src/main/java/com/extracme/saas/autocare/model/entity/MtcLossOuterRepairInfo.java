package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   定损外修信息
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_loss_outer_repair_info
 */
public class MtcLossOuterRepairInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.task_no")
    private String taskNo;

    /**
     * Database Column Remarks:
     *   外修项目主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.outer_id")
    private String outerId;

    /**
     * Database Column Remarks:
     *   外修项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.outer_name")
    private String outerName;

    /**
     * Database Column Remarks:
     *   自定义标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_handadd_flag")
    private String repairHandaddFlag;

    /**
     * Database Column Remarks:
     *   外修项目定损金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.eval_outer_pirce")
    private BigDecimal evalOuterPirce;

    /**
     * Database Column Remarks:
     *   外修项目减损金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_price")
    private BigDecimal derogationPrice;

    /**
     * Database Column Remarks:
     *   配件项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_item_name")
    private String derogationItemName;

    /**
     * Database Column Remarks:
     *   配件零件号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_item_code")
    private String derogationItemCode;

    /**
     * Database Column Remarks:
     *   配件价格类型（1：4S店价 2：市场原厂价 99：其他）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_price_type")
    private String derogationPriceType;

    /**
     * Database Column Remarks:
     *   配件金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.part_price")
    private BigDecimal partPrice;

    /**
     * Database Column Remarks:
     *   外修修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_id")
    private String repairFactoryId;

    /**
     * Database Column Remarks:
     *   外修修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_name")
    private String repairFactoryName;

    /**
     * Database Column Remarks:
     *   外修修理厂代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_code")
    private String repairFactoryCode;

    /**
     * Database Column Remarks:
     *   险种代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.item_cover_code")
    private String itemCoverCode;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   外修配件数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.part_amount")
    private Integer partAmount;

    /**
     * Database Column Remarks:
     *   外修费用小计金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_outer_sum")
    private BigDecimal repairOuterSum;

    /**
     * Database Column Remarks:
     *   外修配件参考价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.reference_part_price")
    private BigDecimal referencePartPrice;

    /**
     * Database Column Remarks:
     *   外修数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.out_item_amount")
    private BigDecimal outItemAmount;

    /**
     * Database Column Remarks:
     *   重开次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.clm_tms")
    private String clmTms;

    /**
     * Database Column Remarks:
     *   参考工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.reference_price")
    private BigDecimal referencePrice;

    /**
     * Database Column Remarks:
     *   自定义标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_handadd_flag")
    private String auditRepairHandaddFlag;

    /**
     * Database Column Remarks:
     *   外修项目核损金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_eval_outer_pirce")
    private BigDecimal auditEvalOuterPirce;

    /**
     * Database Column Remarks:
     *   外修项目减损金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_price")
    private BigDecimal auditDerogationPrice;

    /**
     * Database Column Remarks:
     *   配件项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_item_name")
    private String auditDerogationItemName;

    /**
     * Database Column Remarks:
     *   配件零件号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_item_code")
    private String auditDerogationItemCode;

    /**
     * Database Column Remarks:
     *   配件价格类型（1：4S店价 2：市场原厂价 99：其他）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_price_type")
    private String auditDerogationPriceType;

    /**
     * Database Column Remarks:
     *   换件金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_part_price")
    private BigDecimal auditPartPrice;

    /**
     * Database Column Remarks:
     *   外修修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_id")
    private String auditRepairFactoryId;

    /**
     * Database Column Remarks:
     *   外修修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_name")
    private String auditRepairFactoryName;

    /**
     * Database Column Remarks:
     *   外修修理厂代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_code")
    private String auditRepairFactoryCode;

    /**
     * Database Column Remarks:
     *   险种代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_item_cover_code")
    private String auditItemCoverCode;

    /**
     * Database Column Remarks:
     *   核损状态（00：待处理 01：通过 02：价格异议 03：建议剔除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.check_state")
    private String checkState;

    /**
     * Database Column Remarks:
     *   核损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_remark")
    private String auditRemark;

    /**
     * Database Column Remarks:
     *   外修费用小计金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_outer_sum")
    private BigDecimal auditRepairOuterSum;

    /**
     * Database Column Remarks:
     *   外修配件参考价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_reference_part_price")
    private BigDecimal auditReferencePartPrice;

    /**
     * Database Column Remarks:
     *   外修数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_out_item_amount")
    private BigDecimal auditOutItemAmount;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.misc_Desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.task_no")
    public String getTaskNo() {
        return taskNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.task_no")
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo == null ? null : taskNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.outer_id")
    public String getOuterId() {
        return outerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.outer_id")
    public void setOuterId(String outerId) {
        this.outerId = outerId == null ? null : outerId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.outer_name")
    public String getOuterName() {
        return outerName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.outer_name")
    public void setOuterName(String outerName) {
        this.outerName = outerName == null ? null : outerName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_handadd_flag")
    public String getRepairHandaddFlag() {
        return repairHandaddFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_handadd_flag")
    public void setRepairHandaddFlag(String repairHandaddFlag) {
        this.repairHandaddFlag = repairHandaddFlag == null ? null : repairHandaddFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.eval_outer_pirce")
    public BigDecimal getEvalOuterPirce() {
        return evalOuterPirce;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.eval_outer_pirce")
    public void setEvalOuterPirce(BigDecimal evalOuterPirce) {
        this.evalOuterPirce = evalOuterPirce;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_price")
    public BigDecimal getDerogationPrice() {
        return derogationPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_price")
    public void setDerogationPrice(BigDecimal derogationPrice) {
        this.derogationPrice = derogationPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_item_name")
    public String getDerogationItemName() {
        return derogationItemName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_item_name")
    public void setDerogationItemName(String derogationItemName) {
        this.derogationItemName = derogationItemName == null ? null : derogationItemName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_item_code")
    public String getDerogationItemCode() {
        return derogationItemCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_item_code")
    public void setDerogationItemCode(String derogationItemCode) {
        this.derogationItemCode = derogationItemCode == null ? null : derogationItemCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_price_type")
    public String getDerogationPriceType() {
        return derogationPriceType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_price_type")
    public void setDerogationPriceType(String derogationPriceType) {
        this.derogationPriceType = derogationPriceType == null ? null : derogationPriceType.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.part_price")
    public BigDecimal getPartPrice() {
        return partPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.part_price")
    public void setPartPrice(BigDecimal partPrice) {
        this.partPrice = partPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_id")
    public String getRepairFactoryId() {
        return repairFactoryId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_id")
    public void setRepairFactoryId(String repairFactoryId) {
        this.repairFactoryId = repairFactoryId == null ? null : repairFactoryId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_name")
    public String getRepairFactoryName() {
        return repairFactoryName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_name")
    public void setRepairFactoryName(String repairFactoryName) {
        this.repairFactoryName = repairFactoryName == null ? null : repairFactoryName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_code")
    public String getRepairFactoryCode() {
        return repairFactoryCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_code")
    public void setRepairFactoryCode(String repairFactoryCode) {
        this.repairFactoryCode = repairFactoryCode == null ? null : repairFactoryCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.item_cover_code")
    public String getItemCoverCode() {
        return itemCoverCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.item_cover_code")
    public void setItemCoverCode(String itemCoverCode) {
        this.itemCoverCode = itemCoverCode == null ? null : itemCoverCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.part_amount")
    public Integer getPartAmount() {
        return partAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.part_amount")
    public void setPartAmount(Integer partAmount) {
        this.partAmount = partAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_outer_sum")
    public BigDecimal getRepairOuterSum() {
        return repairOuterSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_outer_sum")
    public void setRepairOuterSum(BigDecimal repairOuterSum) {
        this.repairOuterSum = repairOuterSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.reference_part_price")
    public BigDecimal getReferencePartPrice() {
        return referencePartPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.reference_part_price")
    public void setReferencePartPrice(BigDecimal referencePartPrice) {
        this.referencePartPrice = referencePartPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.out_item_amount")
    public BigDecimal getOutItemAmount() {
        return outItemAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.out_item_amount")
    public void setOutItemAmount(BigDecimal outItemAmount) {
        this.outItemAmount = outItemAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.clm_tms")
    public String getClmTms() {
        return clmTms;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.clm_tms")
    public void setClmTms(String clmTms) {
        this.clmTms = clmTms == null ? null : clmTms.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.reference_price")
    public BigDecimal getReferencePrice() {
        return referencePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.reference_price")
    public void setReferencePrice(BigDecimal referencePrice) {
        this.referencePrice = referencePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_handadd_flag")
    public String getAuditRepairHandaddFlag() {
        return auditRepairHandaddFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_handadd_flag")
    public void setAuditRepairHandaddFlag(String auditRepairHandaddFlag) {
        this.auditRepairHandaddFlag = auditRepairHandaddFlag == null ? null : auditRepairHandaddFlag.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_eval_outer_pirce")
    public BigDecimal getAuditEvalOuterPirce() {
        return auditEvalOuterPirce;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_eval_outer_pirce")
    public void setAuditEvalOuterPirce(BigDecimal auditEvalOuterPirce) {
        this.auditEvalOuterPirce = auditEvalOuterPirce;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_price")
    public BigDecimal getAuditDerogationPrice() {
        return auditDerogationPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_price")
    public void setAuditDerogationPrice(BigDecimal auditDerogationPrice) {
        this.auditDerogationPrice = auditDerogationPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_item_name")
    public String getAuditDerogationItemName() {
        return auditDerogationItemName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_item_name")
    public void setAuditDerogationItemName(String auditDerogationItemName) {
        this.auditDerogationItemName = auditDerogationItemName == null ? null : auditDerogationItemName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_item_code")
    public String getAuditDerogationItemCode() {
        return auditDerogationItemCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_item_code")
    public void setAuditDerogationItemCode(String auditDerogationItemCode) {
        this.auditDerogationItemCode = auditDerogationItemCode == null ? null : auditDerogationItemCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_price_type")
    public String getAuditDerogationPriceType() {
        return auditDerogationPriceType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_price_type")
    public void setAuditDerogationPriceType(String auditDerogationPriceType) {
        this.auditDerogationPriceType = auditDerogationPriceType == null ? null : auditDerogationPriceType.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_part_price")
    public BigDecimal getAuditPartPrice() {
        return auditPartPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_part_price")
    public void setAuditPartPrice(BigDecimal auditPartPrice) {
        this.auditPartPrice = auditPartPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_id")
    public String getAuditRepairFactoryId() {
        return auditRepairFactoryId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_id")
    public void setAuditRepairFactoryId(String auditRepairFactoryId) {
        this.auditRepairFactoryId = auditRepairFactoryId == null ? null : auditRepairFactoryId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_name")
    public String getAuditRepairFactoryName() {
        return auditRepairFactoryName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_name")
    public void setAuditRepairFactoryName(String auditRepairFactoryName) {
        this.auditRepairFactoryName = auditRepairFactoryName == null ? null : auditRepairFactoryName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_code")
    public String getAuditRepairFactoryCode() {
        return auditRepairFactoryCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_code")
    public void setAuditRepairFactoryCode(String auditRepairFactoryCode) {
        this.auditRepairFactoryCode = auditRepairFactoryCode == null ? null : auditRepairFactoryCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_item_cover_code")
    public String getAuditItemCoverCode() {
        return auditItemCoverCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_item_cover_code")
    public void setAuditItemCoverCode(String auditItemCoverCode) {
        this.auditItemCoverCode = auditItemCoverCode == null ? null : auditItemCoverCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.check_state")
    public String getCheckState() {
        return checkState;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.check_state")
    public void setCheckState(String checkState) {
        this.checkState = checkState == null ? null : checkState.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_remark")
    public String getAuditRemark() {
        return auditRemark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_remark")
    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark == null ? null : auditRemark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_outer_sum")
    public BigDecimal getAuditRepairOuterSum() {
        return auditRepairOuterSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_outer_sum")
    public void setAuditRepairOuterSum(BigDecimal auditRepairOuterSum) {
        this.auditRepairOuterSum = auditRepairOuterSum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_reference_part_price")
    public BigDecimal getAuditReferencePartPrice() {
        return auditReferencePartPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_reference_part_price")
    public void setAuditReferencePartPrice(BigDecimal auditReferencePartPrice) {
        this.auditReferencePartPrice = auditReferencePartPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_out_item_amount")
    public BigDecimal getAuditOutItemAmount() {
        return auditOutItemAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_out_item_amount")
    public void setAuditOutItemAmount(BigDecimal auditOutItemAmount) {
        this.auditOutItemAmount = auditOutItemAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.misc_Desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.misc_Desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", outerId=").append(outerId);
        sb.append(", outerName=").append(outerName);
        sb.append(", repairHandaddFlag=").append(repairHandaddFlag);
        sb.append(", evalOuterPirce=").append(evalOuterPirce);
        sb.append(", derogationPrice=").append(derogationPrice);
        sb.append(", derogationItemName=").append(derogationItemName);
        sb.append(", derogationItemCode=").append(derogationItemCode);
        sb.append(", derogationPriceType=").append(derogationPriceType);
        sb.append(", partPrice=").append(partPrice);
        sb.append(", repairFactoryId=").append(repairFactoryId);
        sb.append(", repairFactoryName=").append(repairFactoryName);
        sb.append(", repairFactoryCode=").append(repairFactoryCode);
        sb.append(", itemCoverCode=").append(itemCoverCode);
        sb.append(", remark=").append(remark);
        sb.append(", partAmount=").append(partAmount);
        sb.append(", repairOuterSum=").append(repairOuterSum);
        sb.append(", referencePartPrice=").append(referencePartPrice);
        sb.append(", outItemAmount=").append(outItemAmount);
        sb.append(", clmTms=").append(clmTms);
        sb.append(", referencePrice=").append(referencePrice);
        sb.append(", auditRepairHandaddFlag=").append(auditRepairHandaddFlag);
        sb.append(", auditEvalOuterPirce=").append(auditEvalOuterPirce);
        sb.append(", auditDerogationPrice=").append(auditDerogationPrice);
        sb.append(", auditDerogationItemName=").append(auditDerogationItemName);
        sb.append(", auditDerogationItemCode=").append(auditDerogationItemCode);
        sb.append(", auditDerogationPriceType=").append(auditDerogationPriceType);
        sb.append(", auditPartPrice=").append(auditPartPrice);
        sb.append(", auditRepairFactoryId=").append(auditRepairFactoryId);
        sb.append(", auditRepairFactoryName=").append(auditRepairFactoryName);
        sb.append(", auditRepairFactoryCode=").append(auditRepairFactoryCode);
        sb.append(", auditItemCoverCode=").append(auditItemCoverCode);
        sb.append(", checkState=").append(checkState);
        sb.append(", auditRemark=").append(auditRemark);
        sb.append(", auditRepairOuterSum=").append(auditRepairOuterSum);
        sb.append(", auditReferencePartPrice=").append(auditReferencePartPrice);
        sb.append(", auditOutItemAmount=").append(auditOutItemAmount);
        sb.append(", status=").append(status);
        sb.append(", miscDesc=").append(miscDesc);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}