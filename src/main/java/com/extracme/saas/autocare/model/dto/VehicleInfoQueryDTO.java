package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车辆信息查询DTO
 */
@Data
@ApiModel(description = "车辆信息查询DTO")
public class VehicleInfoQueryDTO {
    
    @ApiModelProperty(value = "车架号", example = "LSGPC52U")
    private String vin;
    
    @ApiModelProperty(value = "车牌号", example = "沪A12345")
    private String vehicleNo;
}
