package com.extracme.saas.autocare.model.bo;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 更新维修任务业务对象
 */
@Data
public class UpdateRepairTaskBO {
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务编号
     */
    private String taskNo;
    
    /**
     * 修理类型ID 1:事故维修 2:自费维修 3:车辆保养
     */
    private String repairTypeId;
    
    /**
     * 预计修理天数
     */
    private String expectedRepairDays;
    
    /**
     * 修理厂换件金额合计
     */
    private BigDecimal repairReplaceTotalAmount;
    
    /**
     * 修理厂修理金额合计
     */
    private BigDecimal repairRepairTotalAmount;
    
    /**
     * 修理厂定损总计
     */
    private BigDecimal repairInsuranceTotalAmount;
    
    /**
     * 车管换件金额合计
     */
    private BigDecimal vehicleReplaceTotalAmount;
    
    /**
     * 车管修理金额合计
     */
    private BigDecimal vehicleRepairTotalAmount;
    
    /**
     * 车管定损总计
     */
    private BigDecimal vehicleInsuranceTotalAmount;
    
    /**
     * 是否需要维修 0:否 1:是
     */
    private String repairFlag;
    
    /**
     * 保养费用
     */
    private BigDecimal maintainAmount;
    
    /**
     * 损坏部位图片
     */
    private List<String> damagedPartPicture;
    
    /**
     * 损坏部位视频
     */
    private List<String> damagedPartVideo;

    /**
     * 维修图片（验收图片）
     */
    private List<String> repairPicture;
    
    /**
     * 验收视频
     */
    private List<String> checkVideo;

    /**
     * 超时原因
     */
    private String overTimeReasons;
    
    /**
     * 备注
     */
    private String remark;
}
