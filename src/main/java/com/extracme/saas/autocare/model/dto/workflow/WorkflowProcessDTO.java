package com.extracme.saas.autocare.model.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 工作流节点处理请求对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("工作流节点处理请求")
public class WorkflowProcessDTO {

    /**
     * 活动节点编码
     */
    @NotBlank(message = "活动节点编码不能为空")
    @ApiModelProperty(value = "活动节点编码", required = true)
    private String activityCode;

    /**
     * 触发事件
     */
    @NotBlank(message = "触发事件不能为空")
    @ApiModelProperty(value = "触发事件", required = true)
    private String triggerEvent;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    private String operator;

    /**
     * 备注说明
     */
    @ApiModelProperty("备注说明")
    private String remarks;
}