package com.extracme.saas.autocare.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.StatusEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibrary;
import com.extracme.saas.autocare.model.excel.RepairItemLibraryUpdateExcel;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairItemLibraryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 维修项目库更新Excel监听器
 * 用于处理批量更新维修项目的Excel导入
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
public class RepairItemLibraryUpdateListener implements ReadListener<RepairItemLibraryUpdateExcel> {

    /**
     * 每隔100条存储数据库，然后清理list，方便内存回收
     */
    private static final int BATCH_COUNT = 100;

    /**
     * 缓存的数据
     */
    private List<RepairItemLibraryUpdateExcel> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    /**
     * 维修项目库服务
     */
    private final TableRepairItemLibraryService tableRepairItemLibraryService;

    /**
     * 操作日志服务
     */
    private final TableOperatorLogService tableOperatorLogService;

    /**
     * 成功处理的行数
     */
    private int successCount = 0;

    /**
     * 失败处理的行数
     */
    private int failedCount = 0;

    public RepairItemLibraryUpdateListener(TableRepairItemLibraryService tableRepairItemLibraryService,
                                         TableOperatorLogService tableOperatorLogService) {
        this.tableRepairItemLibraryService = tableRepairItemLibraryService;
        this.tableOperatorLogService = tableOperatorLogService;
    }

    /**
     * 每一条数据解析都会来调用
     *
     * @param data Excel行数据
     * @param context 分析上下文
     */
    @Override
    public void invoke(RepairItemLibraryUpdateExcel data, AnalysisContext context) {
        log.debug("解析到一条数据: {}", data);
        
        try {
            // 数据校验
            validateData(data, context.readRowHolder().getRowIndex());
            
            // 添加到缓存
            cachedDataList.add(data);
            
            // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
            if (cachedDataList.size() >= BATCH_COUNT) {
                saveData();
                // 存储完成清理 list
                cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            failedCount++;
            log.error("第{}行数据处理失败: {}", context.readRowHolder().getRowIndex() + 1, e.getMessage(), e);
            throw new BusinessException(StrUtil.format("第{}行数据处理失败: {}", 
                context.readRowHolder().getRowIndex() + 1, e.getMessage()));
        }
    }

    /**
     * 所有数据解析完成了都会来调用
     *
     * @param context 分析上下文
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("所有数据解析完成！成功: {}, 失败: {}", successCount, failedCount);
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("开始存储{}条数据", cachedDataList.size());
        
        for (RepairItemLibraryUpdateExcel excelData : cachedDataList) {
            try {
                // 根据项目编号查找现有记录
                MtcRepairItemLibrary existingEntity = tableRepairItemLibraryService.queryByItemNo(excelData.getItemNo());
                if (existingEntity == null) {
                    throw new BusinessException("项目编号不存在: " + excelData.getItemNo());
                }
                
                // 更新实体对象
                updateEntity(existingEntity, excelData);
                
                // 保存到数据库
                tableRepairItemLibraryService.updateSelectiveById(existingEntity);
                
                // 记录操作日志
                recordOperationLog(existingEntity.getId());
                
                successCount++;
                log.debug("成功更新维修项目: {}", existingEntity.getItemName());
                
            } catch (Exception e) {
                failedCount++;
                log.error("更新维修项目失败: {}, 错误: {}", excelData.getItemNo(), e.getMessage(), e);
                throw new BusinessException(StrUtil.format("更新维修项目失败: {}, 错误: {}", 
                    excelData.getItemNo(), e.getMessage()));
            }
        }
        
        log.info("存储数据完成");
    }

    /**
     * 数据校验
     *
     * @param data Excel行数据
     * @param rowIndex 行索引
     */
    private void validateData(RepairItemLibraryUpdateExcel data, int rowIndex) {
        // 校验项目编号
        if (StringUtils.isBlank(data.getItemNo())) {
            throw new BusinessException("项目编号不能为空");
        }

        // 校验项目名称
        if (StringUtils.isBlank(data.getItemName())) {
            throw new BusinessException("项目名称不能为空");
        }

        // 校验工时费全国市场价
        if (data.getHourFeeNationalMarketPrice() == null) {
            throw new BusinessException("工时费全国市场价不能为空");
        }
        validateAmount(data.getHourFeeNationalMarketPrice(), "工时费全国市场价");

        // 校验工时费全国上限（可选）
        if (data.getHourFeeNationalHighestPrice() != null) {
            validateAmount(data.getHourFeeNationalHighestPrice(), "工时费全国上限");
        }

        // 校验材料费全国市场价
        if (data.getMaterialCostNationalMarketPrice() == null) {
            throw new BusinessException("材料费全国市场价不能为空");
        }
        validateAmount(data.getMaterialCostNationalMarketPrice(), "材料费全国市场价");

        // 校验材料费全国上限（可选）
        if (data.getMaterialCostNationalHighestPrice() != null) {
            validateAmount(data.getMaterialCostNationalHighestPrice(), "材料费全国上限");
        }

        // 校验状态
        if (StringUtils.isBlank(data.getStatus())) {
            throw new BusinessException("状态不能为空");
        }
        
        StatusEnum statusEnum = StatusEnum.getByCode(data.getStatus());
        if (statusEnum == null) {
            throw new BusinessException("状态格式错误，请填写：启用、禁用");
        }

        // 校验项目是否存在
        MtcRepairItemLibrary existingEntity = tableRepairItemLibraryService.queryByItemNo(data.getItemNo());
        if (existingEntity == null) {
            throw new BusinessException("项目编号不存在");
        }

        // 校验唯一性（排除当前记录）
        boolean isUnique = tableRepairItemLibraryService.checkUnique(
            data.getItemNo(), data.getItemName(), existingEntity.getItemType(), existingEntity.getVehicleModelSeq());
        if (isUnique) {
            throw new BusinessException("维修项目重复");
        }
    }

    /**
     * 校验金额
     *
     * @param amount 金额
     * @param fieldName 字段名称
     */
    private void validateAmount(BigDecimal amount, String fieldName) {
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(fieldName + "不能为负数");
        }
        if (amount.scale() > 2) {
            throw new BusinessException(fieldName + "最多保留两位小数");
        }
    }

    /**
     * 更新实体对象
     *
     * @param entity 现有实体
     * @param excelData Excel数据
     */
    private void updateEntity(MtcRepairItemLibrary entity, RepairItemLibraryUpdateExcel excelData) {
        // 更新项目名称
        entity.setItemName(excelData.getItemName());
        
        // 更新价格信息
        entity.setHourFeeNationalMarketPrice(excelData.getHourFeeNationalMarketPrice());
        entity.setHourFeeNationalHighestPrice(excelData.getHourFeeNationalHighestPrice());
        entity.setMaterialCostNationalMarketPrice(excelData.getMaterialCostNationalMarketPrice());
        entity.setMaterialCostNationalHighestPrice(excelData.getMaterialCostNationalHighestPrice());
        
        // 更新状态
        StatusEnum statusEnum = StatusEnum.getByCode(excelData.getStatus());
        entity.setStatus(statusEnum.getCode());
    }

    /**
     * 记录操作日志
     *
     * @param recordId 记录ID
     */
    private void recordOperationLog(Long recordId) {
        MtcOperatorLog operatorLog = new MtcOperatorLog();
        operatorLog.setRecordId(recordId);
        operatorLog.setOpeContent("批量修改维修项目");
        operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM_LIBRARY_NATIONAL);
        tableOperatorLogService.insertSelective(operatorLog);
    }

    /**
     * 获取成功处理的行数
     */
    public int getSuccessCount() {
        return successCount;
    }

    /**
     * 获取失败处理的行数
     */
    public int getFailedCount() {
        return failedCount;
    }
}
