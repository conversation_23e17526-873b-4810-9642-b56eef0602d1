package com.extracme.saas.autocare.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.RepairItemTypeEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibrary;
import com.extracme.saas.autocare.model.excel.RepairItemLibraryCreateExcel;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairItemLibraryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * 维修项目库创建Excel监听器
 * 用于处理批量创建维修项目的Excel导入
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
public class RepairItemLibraryCreateListener implements ReadListener<RepairItemLibraryCreateExcel> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list，方便内存回收
     */
    private static final int BATCH_COUNT = 100;

    /**
     * 缓存的数据
     */
    private List<RepairItemLibraryCreateExcel> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    /**
     * 维修项目库服务
     */
    private final TableRepairItemLibraryService tableRepairItemLibraryService;

    /**
     * 操作日志服务
     */
    private final TableOperatorLogService tableOperatorLogService;

    /**
     * 成功处理的行数
     */
    private int successCount = 0;

    /**
     * 失败处理的行数
     */
    private int failedCount = 0;

    public RepairItemLibraryCreateListener(TableRepairItemLibraryService tableRepairItemLibraryService,
                                         TableOperatorLogService tableOperatorLogService) {
        this.tableRepairItemLibraryService = tableRepairItemLibraryService;
        this.tableOperatorLogService = tableOperatorLogService;
    }

    /**
     * 每一条数据解析都会来调用
     *
     * @param data Excel行数据
     * @param context 分析上下文
     */
    @Override
    public void invoke(RepairItemLibraryCreateExcel data, AnalysisContext context) {
        log.debug("解析到一条数据: {}", data);
        
        try {
            // 数据校验
            validateData(data, context.readRowHolder().getRowIndex());
            
            // 添加到缓存
            cachedDataList.add(data);
            
            // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
            if (cachedDataList.size() >= BATCH_COUNT) {
                saveData();
                // 存储完成清理 list
                cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            failedCount++;
            log.error("第{}行数据处理失败: {}", context.readRowHolder().getRowIndex() + 1, e.getMessage(), e);
            throw new BusinessException(StrUtil.format("第{}行数据处理失败: {}", 
                context.readRowHolder().getRowIndex() + 1, e.getMessage()));
        }
    }

    /**
     * 所有数据解析完成了都会来调用
     *
     * @param context 分析上下文
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("所有数据解析完成！成功: {}, 失败: {}", successCount, failedCount);
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("开始存储{}条数据", cachedDataList.size());
        
        for (RepairItemLibraryCreateExcel excelData : cachedDataList) {
            try {
                // 转换为实体对象
                MtcRepairItemLibrary entity = convertToEntity(excelData);
                
                // 保存到数据库
                tableRepairItemLibraryService.insert(entity);
                
                // 记录操作日志
                recordOperationLog(entity.getId());
                
                successCount++;
                log.debug("成功保存维修项目: {}", entity.getItemName());
                
            } catch (Exception e) {
                failedCount++;
                log.error("保存维修项目失败: {}, 错误: {}", excelData.getItemName(), e.getMessage(), e);
                throw new BusinessException(StrUtil.format("保存维修项目失败: {}, 错误: {}", 
                    excelData.getItemName(), e.getMessage()));
            }
        }
        
        log.info("存储数据完成");
    }

    /**
     * 数据校验
     *
     * @param data Excel行数据
     * @param rowIndex 行索引
     */
    private void validateData(RepairItemLibraryCreateExcel data, int rowIndex) {
        // 校验项目名称
        if (StringUtils.isBlank(data.getItemName())) {
            throw new BusinessException("项目名称不能为空");
        }

        // 校验项目类型
        if (StringUtils.isBlank(data.getItemType())) {
            throw new BusinessException("项目类型不能为空");
        }
        
        Integer itemTypeCode = RepairItemTypeEnum.getCodeByName(data.getItemType());
        if (itemTypeCode == null) {
            throw new BusinessException("项目类型格式错误，请填写：保养、终端、维修");
        }

        // 校验车型信息（必填）
        if (StringUtils.isBlank(data.getVehicleModelInfo())) {
            throw new BusinessException("维修项目必须填写车型信息");
        }

        // 校验工时费全国市场价
        if (data.getHourFeeNationalMarketPrice() == null) {
            throw new BusinessException("工时费全国市场价不能为空");
        }
        validateAmount(data.getHourFeeNationalMarketPrice(), "工时费全国市场价");

        // 校验工时费全国上限（可选）
        if (data.getHourFeeNationalHighestPrice() != null) {
            validateAmount(data.getHourFeeNationalHighestPrice(), "工时费全国上限");
        }

        // 校验材料费全国市场价
        if (data.getMaterialCostNationalMarketPrice() == null) {
            throw new BusinessException("材料费全国市场价不能为空");
        }
        validateAmount(data.getMaterialCostNationalMarketPrice(), "材料费全国市场价");

        // 校验材料费全国上限（可选）
        if (data.getMaterialCostNationalHighestPrice() != null) {
            validateAmount(data.getMaterialCostNationalHighestPrice(), "材料费全国上限");
        }

        // 校验唯一性
        Long vehicleModelSeq = StringUtils.isNotBlank(data.getVehicleModelInfo()) ? -1L : null;
        boolean isUnique = tableRepairItemLibraryService.checkUnique(
            null, data.getItemName(), itemTypeCode, vehicleModelSeq);
        if (isUnique) {
            throw new BusinessException("维修项目重复");
        }
    }

    /**
     * 校验金额
     *
     * @param amount 金额
     * @param fieldName 字段名称
     */
    private void validateAmount(BigDecimal amount, String fieldName) {
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(fieldName + "不能为负数");
        }
        if (amount.scale() > 2) {
            throw new BusinessException(fieldName + "最多保留两位小数");
        }
    }

    /**
     * 转换为实体对象
     *
     * @param excelData Excel数据
     * @return 实体对象
     */
    private MtcRepairItemLibrary convertToEntity(RepairItemLibraryCreateExcel excelData) {
        MtcRepairItemLibrary entity = new MtcRepairItemLibrary();
        
        // 基本属性复制
        BeanUtils.copyProperties(excelData, entity);
        
        // 设置项目编号
        entity.setItemNo(UUID.randomUUID().toString().substring(20));
        
        // 设置项目类型编码
        Integer itemTypeCode = RepairItemTypeEnum.getCodeByName(excelData.getItemType());
        entity.setItemType(itemTypeCode);
        
        // 设置车型序号（如果有车型信息）
        if (StringUtils.isNotBlank(excelData.getVehicleModelInfo())) {
            // 这里应该根据车型名称查找车型ID，暂时设置为-1
            entity.setVehicleModelSeq(-1L);
        }
        
        return entity;
    }

    /**
     * 记录操作日志
     *
     * @param recordId 记录ID
     */
    private void recordOperationLog(Long recordId) {
        MtcOperatorLog operatorLog = new MtcOperatorLog();
        operatorLog.setRecordId(recordId);
        operatorLog.setOpeContent("批量新增维修项目");
        operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM_LIBRARY_NATIONAL);
        tableOperatorLogService.insertSelective(operatorLog);
    }

    /**
     * 获取成功处理的行数
     */
    public int getSuccessCount() {
        return successCount;
    }

    /**
     * 获取失败处理的行数
     */
    public int getFailedCount() {
        return failedCount;
    }
}
