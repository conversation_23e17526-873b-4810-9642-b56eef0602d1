package com.extracme.saas.autocare.interceptor.tenant;

import lombok.extern.slf4j.Slf4j;

/**
 * 租户上下文持有器
 * 管理三个schema:
 * - auto_care_saas: 存储公共账号信息
 * - auto_care_extracme: 租户0的数据
 * - auto_care_dzjt: 租户1的数据
 */
@Slf4j
public class TenantContextHolder {
    private static final ThreadLocal<Long> TENANT_ID = new ThreadLocal<>();
    private static final ThreadLocal<String> TENANT_SCHEMA = new ThreadLocal<>();
    
    // 定义默认和公共schema
    private static final String DEFAULT_SCHEMA = "auto_care_saas";
    
    // 定义租户schema
    private static final String TENANT_EXTRACME = "auto_care_extracme";
    private static final String TENANT_DZJT = "auto_care_dzjt";
    
    /**
     * 设置租户上下文
     * @param tenantId 租户ID
     */
    public static void setTenant(Long tenantId) {
        if (tenantId == null) {
            log.warn("租户ID为空，使用默认schema");
            TENANT_ID.set(null);
            TENANT_SCHEMA.set(DEFAULT_SCHEMA);
            return;
        }
        
        String schema = formatSchema(tenantId);
        TENANT_ID.set(tenantId);
        TENANT_SCHEMA.set(schema);
    }
    
    /**
     * 获取当前租户ID
     * @return 租户ID
     */
    public static Long getTenantId() {
        return TENANT_ID.get();
    }
    
    /**
     * 获取当前租户的schema
     * @return 当前租户的schema名称
     */
    public static String getTenantSchema() {
        String schema = TENANT_SCHEMA.get();
        if (schema == null) {
            log.debug("未找到租户上下文，使用默认schema: {}", DEFAULT_SCHEMA);
            return DEFAULT_SCHEMA;
        }
        return schema;
    }
    
    /**
     * 清理租户上下文
     */
    public static void clear() {
        TENANT_ID.remove();
        TENANT_SCHEMA.remove();
    }
    
    /**
     * 根据租户ID格式化schema名称
     * @param tenantId 租户ID
     * @return 对应的schema名称
     */
    private static String formatSchema(Long tenantId) {
        if (tenantId == null) {
            return DEFAULT_SCHEMA;
        }
        
        switch (tenantId.intValue()) {
            case 1:
            return TENANT_DZJT;
            case 2:
                return TENANT_EXTRACME;
            default:
                log.warn("未知的租户ID: {}, 使用默认schema", tenantId);
                return DEFAULT_SCHEMA;
        }
    }
    
    /**
     * 判断是否是默认schema
     * @return 是否是默认schema
     */
    public static boolean isDefaultSchema() {
        return DEFAULT_SCHEMA.equals(getTenantSchema());
    }
} 