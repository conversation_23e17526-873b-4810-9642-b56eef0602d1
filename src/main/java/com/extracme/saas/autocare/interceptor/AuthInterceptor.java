package com.extracme.saas.autocare.interceptor;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;


import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import com.extracme.saas.autocare.annotation.RequireLogin;
import com.extracme.saas.autocare.annotation.RequirePermission;
import com.extracme.saas.autocare.exception.ForbiddenException;
import com.extracme.saas.autocare.exception.UnauthorizedException;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.PermissionService;
import com.extracme.saas.autocare.util.JwtUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class AuthInterceptor implements HandlerInterceptor {

    private static final String SESSION_USER_KEY = "LOGIN_USER";
    private static final String SESSION_PERMISSIONS_KEY = "USER_PERMISSIONS";
    private static final String SESSION_TOKEN_KEY = "USER_TOKEN";
    private static final String SESSION_TENANT_CODE_KEY = "TENANT_CODE";

    private final JwtUtil jwtUtil;
    private final PermissionService permissionService;
    private final TableUserService userService;
    private final TableTenantService tenantService;
    private final TableUserOrgService tableUserOrgService;
    private HttpServletRequest request;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        this.request = request;

        // 如果不是方法处理器，直接放行
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();

        // 获取token
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }

        // 如果有token，就验证并设置用户信息
        if (token != null) {
            if (!jwtUtil.validateToken(token)) {
                throw new UnauthorizedException("无效的token");
            }
            setSecurityContext(token);
        }

        // 检查是否需要登录
        RequireLogin requireLogin = method.getAnnotation(RequireLogin.class);
        if (requireLogin != null && token == null) {
            throw new UnauthorizedException("请先登录");
        }

        // 检查是否需要特定权限
        RequirePermission requirePermission = method.getAnnotation(RequirePermission.class);
        if (requirePermission != null) {
            if (token == null) {
                throw new UnauthorizedException("请先登录");
            }

            Long userId = Long.parseLong(jwtUtil.getUserIdFromToken(token));
            String permissionCode = requirePermission.value();

            if (!permissionService.hasPermission(userId, permissionCode)) {
                throw new ForbiddenException("没有操作权限");
            }
        }

        return true;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler, @Nullable Exception ex) {
        // 清理租户上下文
        TenantContextHolder.clear();
    }

    /**
     * 设置 Security 上下文信息
     * 优先从JWT token中解析完整用户信息，如果token中没有完整信息则回退到数据库查询（向后兼容）
     * @param token JWT token
     */
    private void setSecurityContext(String token) {
        HttpSession session = request.getSession();

        // 1. 尝试从 Session 中获取用户信息
        LoginUser loginUser = (LoginUser) session.getAttribute(SESSION_USER_KEY);
        String sessionToken = (String) session.getAttribute(SESSION_TOKEN_KEY);

        // 2. 如果 Session 中没有用户信息，或者 token 已更新，则重新获取
        if (loginUser == null || !token.equals(sessionToken)) {
            // 检查token是否包含完整用户信息
            if (jwtUtil.hasUserInfo(token)) {
                // 新版本token：直接从JWT中解析完整用户信息
                loginUser = buildLoginUserFromJwtToken(token);
                log.debug("从JWT token中解析用户信息成功，用户ID: {}", loginUser.getUser().getId());
            } else {
                // 旧版本token：回退到数据库查询方式（向后兼容）
                log.debug("JWT token中不包含完整用户信息，回退到数据库查询方式");
                loginUser = buildLoginUserFromDatabase(token);
            }

            // 将信息存储到 Session 中
            session.setAttribute(SESSION_USER_KEY, loginUser);
            session.setAttribute(SESSION_PERMISSIONS_KEY, loginUser.getPermissions());
            session.setAttribute(SESSION_TOKEN_KEY, token);
            session.setAttribute(SESSION_TENANT_CODE_KEY, loginUser.getTenantCode());

            // 设置租户上下文
            TenantContextHolder.setTenant(loginUser.getTenantId());
        } else {
            // 如果session中有用户信息，直接设置租户上下文
            TenantContextHolder.setTenant(loginUser.getTenantId());
        }

        // 创建认证信息
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(loginUser, null, Collections.emptyList());

        // 设置认证信息到上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    /**
     * 从JWT token中构建LoginUser对象（优化版本）
     * 从JWT解析基础用户信息，从Redis获取权限信息
     *
     * @param token JWT token
     * @return LoginUser对象
     */
    private LoginUser buildLoginUserFromJwtToken(String token) {
        try {
            // 从token中获取基础用户信息
            JwtUserInfoDTO userInfo = jwtUtil.getUserInfoFromToken(token);
            if (userInfo == null) {
                throw new UnauthorizedException("无法从JWT token中解析用户信息");
            }

            // 构建SysUser对象
            SysUser user = new SysUser();
            user.setId(userInfo.getUserId());
            user.setUsername(userInfo.getUsername());
            user.setNickname(userInfo.getNickname());
            user.setMobile(userInfo.getMobile());
            user.setAccountType(userInfo.getAccountType());
            user.setApprovalLevel(userInfo.getApprovalLevel());
            user.setStatus(userInfo.getStatus());
            user.setInsuranceCompanyId(userInfo.getInsuranceCompanyId());
            user.setTenantId(userInfo.getTenantId());

            // 构建LoginUser对象
            LoginUser loginUser = new LoginUser();
            loginUser.setUser(user);
            loginUser.setToken(token);
            loginUser.setTenantId(userInfo.getTenantId());
            loginUser.setTenantCode(userInfo.getTenantCode());
            loginUser.setTenantName(userInfo.getTenantName());
            loginUser.setIpaddr(request.getRemoteAddr());

            // 设置时间信息
            if (userInfo.getLoginTime() != null) {
                loginUser.setLoginTime(userInfo.getLoginTime());
            }
            if (userInfo.getExpireTime() != null) {
                loginUser.setExpireTime(userInfo.getExpireTime());
            }

            // 权限信息现在从Redis获取，这里设置为空，后续通过SessionUtils获取
            loginUser.setPermissions(Collections.emptySet());
            loginUser.setOrgIds(Collections.emptyList());
            loginUser.setAllAccessibleOrgIds(Collections.emptyList());

            log.debug("从JWT token中构建LoginUser成功，用户ID: {}（权限信息将从Redis获取）",
                     userInfo.getUserId());

            return loginUser;
        } catch (Exception e) {
            log.error("从JWT token中构建LoginUser失败", e);
            throw new UnauthorizedException("用户信息解析失败");
        }
    }

    /**
     * 从数据库构建LoginUser对象（旧版本token，向后兼容）
     *
     * @param token JWT token
     * @return LoginUser对象
     */
    private LoginUser buildLoginUserFromDatabase(String token) {
        try {
            // 从token中获取用户ID
            Long userId = Long.parseLong(jwtUtil.getUserIdFromToken(token));

            // 获取用户信息
            SysUser sysUser = userService.selectById(userId);
            if (sysUser == null) {
                throw new UnauthorizedException("用户不存在");
            }

            // 获取用户权限
            List<SysPermission> permissionList = permissionService.getUserPermissions(userId);
            Set<String> permissions = permissionList.stream()
                .map(SysPermission::getPermissionCode)
                .collect(Collectors.toSet());

            // 创建LoginUser对象
            LoginUser loginUser = new LoginUser();
            loginUser.setUser(sysUser);
            loginUser.setPermissions(permissions);
            loginUser.setToken(token);
            loginUser.setLoginTime(System.currentTimeMillis());

            // 从JWT中获取过期时间并设置到LoginUser中
            Date expirationDate = jwtUtil.getExpirationDateFromToken(token);
            if (expirationDate != null) {
                loginUser.setExpireTime(expirationDate.getTime());
            }

            loginUser.setIpaddr(request.getRemoteAddr());
            loginUser.setTenantId(sysUser.getTenantId());

            // 查询并设置租户编码
            String tenantCode = "default";
            String tenantName = "";
            if (sysUser.getTenantId() != null) {
                try {
                    SysTenant tenant = tenantService.selectById(sysUser.getTenantId());
                    if (tenant != null && tenant.getTenantCode() != null) {
                        tenantCode = tenant.getTenantCode();
                        tenantName = tenant.getTenantName();
                    }
                } catch (Exception e) {
                    log.warn("查询租户编码失败，租户ID: {}, 错误: {}", sysUser.getTenantId(), e.getMessage());
                }
            }

            loginUser.setTenantCode(tenantCode);
            loginUser.setTenantName(tenantName);

            // 查询用户关联的机构列表
            List<String> orgIds = Collections.emptyList();
            try {
                if (tableUserOrgService != null) {
                    orgIds = tableUserOrgService.findOrgIdsByUserId(userId);
                }
            } catch (Exception e) {
                log.warn("查询用户机构关联失败，用户ID: {}, 错误: {}", userId, e.getMessage());
            }
            loginUser.setOrgIds(orgIds);

            log.debug("从数据库构建LoginUser成功，用户ID: {}, 权限数量: {}, 组织数量: {}",
                     userId, permissions.size(), orgIds.size());

            return loginUser;
        } catch (Exception e) {
            log.error("从数据库构建LoginUser失败", e);
            throw new UnauthorizedException("用户信息获取失败");
        }
    }
}