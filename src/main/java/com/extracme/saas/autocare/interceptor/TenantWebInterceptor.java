package com.extracme.saas.autocare.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.security.LoginUser;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * 租户Web拦截器
 * 负责处理多租户环境下的请求拦截和租户上下文设置
 */
@Slf4j
public class TenantWebInterceptor implements HandlerInterceptor {
    
    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        try {
            // 从Session获取租户信息
            Long tenantId = SessionUtils.getTenantId();
            LoginUser loginUser = SessionUtils.getLoginUser();
            
            if (tenantId == null) {
                log.warn("未获取到租户信息，用户：{}", loginUser != null ? loginUser.getUsername() : "未登录");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return false;
            }
            
            log.debug("设置租户上下文 - 租户ID: {}, 用户: {}", tenantId, 
                loginUser != null ? loginUser.getUsername() : "未登录");
            
            // 设置租户上下文
            TenantContextHolder.setTenant(tenantId);
            return true;
        } catch (Exception e) {
            log.error("租户上下文设置失败", e);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return false;
        }
    }
    
    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, 
            @NonNull Object handler, @Nullable Exception ex) {
        try {
            // 清理租户上下文
            TenantContextHolder.clear();
        } catch (Exception e) {
            log.error("清理租户上下文失败", e);
        }
    }
} 