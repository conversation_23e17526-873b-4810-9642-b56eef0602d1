package com.extracme.saas.autocare.interceptor;

import java.sql.Connection;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;

import lombok.extern.slf4j.Slf4j;

/**
 * 多租户Schema拦截器
 * 用于自动处理多租户场景下的数据库Schema切换
 * 通过拦截MyBatis的SQL执行，动态修改SQL语句，添加租户的Schema前缀
 *
 * 使用方式：
 * 1. 在需要进行租户隔离的Mapper接口上添加@TenantSchema注解
 * 2. 可以通过@TenantSchema的excludeMethods属性排除不需要进行租户隔离的方法
 * 3. 系统会自动从TenantContextHolder获取当前租户的Schema，并添加到SQL语句中
 */
@Slf4j
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare",
              args = {Connection.class, Integer.class})
})
@Component
public class TenantSchemaInterceptor implements Interceptor {

    // 预编译的正则表达式，提高性能
    private static final Pattern INSERT_PATTERN = Pattern.compile("(?i)insert\\s+into\\s+(\\w+)");
    private static final Pattern DELETE_PATTERN = Pattern.compile("(?i)delete\\s+from\\s+(\\w+)");
    private static final Pattern UPDATE_PATTERN = Pattern.compile("(?i)update\\s+(\\w+)");
    private static final Pattern FROM_PATTERN = Pattern.compile("(?i)(\\s+)from\\s+(\\w+)");
    private static final Pattern JOIN_PATTERN = Pattern.compile("(?i)\\s+join\\s+(\\w+)");

    // SQL处理结果缓存，提高性能
    private final Map<String, String> sqlCache = new ConcurrentHashMap<>();

    // 缓存大小限制
    private static final int MAX_CACHE_SIZE = 1000;

    // 需要添加前缀的表名称-该表结构用于保险理赔用印申请
    private static final List<String> TABLE_NAME_LIST = Arrays.asList("attachment_info", "insurance_company_info");

    /**
     * 拦截SQL执行的主要方法
     * 1. 获取当前执行的Mapper方法信息
     * 2. 检查是否需要进行租户Schema处理
     * 3. 识别SQL类型并应用相应的处理逻辑
     * 4. 修改SQL语句，添加租户Schema前缀
     *
     * @param invocation MyBatis的调用信息
     * @return 处理后的执行结果
     * @throws Throwable 处理过程中的异常
     */
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();

        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");

        // 获取执行的方法信息
        String id = mappedStatement.getId();
        String className = id.substring(0, id.lastIndexOf("."));
        String methodName = id.substring(id.lastIndexOf(".") + 1);
        Class<?> clazz = Class.forName(className);

        // 检查类上的TenantSchema注解
        TenantSchema tenantSchema = clazz.getAnnotation(TenantSchema.class);
        if (tenantSchema == null || !tenantSchema.value()) {
            return invocation.proceed();
        }

        // 检查方法是否在排除列表中
        if (tenantSchema.excludeMethods().length > 0 &&
            Arrays.asList(tenantSchema.excludeMethods()).contains(methodName)) {
            return invocation.proceed();
        }

        // 获取当前租户schema
        String schema = TenantContextHolder.getTenantSchema();
        if (schema == null || "auto_care_saas".equals(schema)) {
            return invocation.proceed();
        }

        // 修改SQL添加租户Schema
        BoundSql boundSql = statementHandler.getBoundSql();
        String sql = boundSql.getSql();

        // 生成缓存键
        String cacheKey = schema + ":" + sql;

        // 尝试从缓存获取处理后的SQL
        String modifiedSql = sqlCache.get(cacheKey);

        if (modifiedSql == null) {
            // 缓存未命中，需要处理SQL

            // 识别SQL类型并应用相应的处理逻辑
            String sqlType = identifySqlType(sql);

            log.debug("SQL类型: {}, 原始SQL: {}", sqlType, sql);

            switch (sqlType) {
                case "INSERT":
                    modifiedSql = processInsertSql(sql, schema);
                    break;
                case "DELETE":
                    modifiedSql = processDeleteSql(sql, schema);
                    break;
                case "SELECT":
                    modifiedSql = processSelectSql(sql, schema);
                    break;
                case "UPDATE":
                    modifiedSql = processUpdateSql(sql, schema);
                    break;
                default:
                    // 对于未识别的SQL类型，使用原始的处理方法
                    modifiedSql = addTenantSchema(sql, schema);
                    break;
            }

            // 将处理后的SQL添加到缓存
            if (sqlCache.size() < MAX_CACHE_SIZE) {
                sqlCache.put(cacheKey, modifiedSql);
            } else {
                // 缓存已满，记录日志但不影响功能
                log.warn("SQL缓存已满，无法缓存新的SQL语句");
            }
        } else {
            log.debug("SQL缓存命中");
        }

        // 反射修改SQL
        try {
            java.lang.reflect.Field field = boundSql.getClass().getDeclaredField("sql");
            field.setAccessible(true);
            field.set(boundSql, modifiedSql);

            long endTime = System.currentTimeMillis();
            log.debug("SQL处理耗时: {}ms, 类名: {}, 方法名: {}", (endTime - startTime), className, methodName);
        } catch (Exception e) {
            log.error("SQL修改失败 - 类名: {}, 方法名: {}, 错误信息: {}", className, methodName, e.getMessage(), e);
            throw new RuntimeException("无法修改SQL语句", e);
        }

        return invocation.proceed();
    }

    /**
     * 识别SQL语句类型
     *
     * @param sql 原始SQL语句
     * @return SQL类型（INSERT、DELETE、SELECT、UPDATE或UNKNOWN）
     */
    private String identifySqlType(String sql) {
        String trimmedSql = sql.trim().toUpperCase();

        if (trimmedSql.startsWith("INSERT")) {
            return "INSERT";
        } else if (trimmedSql.startsWith("DELETE")) {
            return "DELETE";
        } else if (trimmedSql.startsWith("SELECT")) {
            return "SELECT";
        } else if (trimmedSql.startsWith("UPDATE")) {
            return "UPDATE";
        } else {
            return "UNKNOWN";
        }
    }

    /**
     * 向SQL语句中添加租户Schema
     * 处理所有类型的SQL语句
     * 此方法仅作为兜底方案，优先使用特定SQL类型的处理方法
     *
     * @param sql 原始SQL语句
     * @param tenant 租户Schema名称
     * @return 添加了租户Schema的SQL语句
     */
    private String addTenantSchema(String sql, String tenant) {
        log.debug("使用通用SQL处理方法");

        // 识别SQL类型并调用相应的处理方法
        String sqlType = identifySqlType(sql);

        switch (sqlType) {
            case "INSERT":
                return processInsertSql(sql, tenant);
            case "DELETE":
                return processDeleteSql(sql, tenant);
            case "SELECT":
                return processSelectSql(sql, tenant);
            case "UPDATE":
                return processUpdateSql(sql, tenant);
            default:
                // 对于未识别的SQL类型，尝试应用所有处理逻辑
                log.warn("未识别的SQL类型: {}", sqlType);
                String result = sql;
                result = processInsertSql(result, tenant);
                result = processDeleteSql(result, tenant);
                result = processSelectSql(result, tenant);
                result = processUpdateSql(result, tenant);
                return result;
        }
    }

    /**
     * 判断表名是否需要添加租户Schema前缀
     * 只有以"mtc_"开头的表名才需要添加租户Schema前缀
     *
     * @param tableName 表名
     * @return 是否需要添加租户Schema前缀
     */
    private boolean shouldAddTenantSchema(String tableName) {
        boolean result = tableName.toLowerCase().startsWith("mtc_") || TABLE_NAME_LIST.contains(tableName);
        if (result) {
            log.debug("表 {} 需要添加租户Schema前缀", tableName);
        } else {
            log.debug("表 {} 不需要添加租户Schema前缀", tableName);
        }
        return result;
    }

    /**
     * 处理INSERT语句
     * 在"INSERT INTO 表名"模式中为表名添加schema前缀
     * 只对以"mtc_"开头的表名添加租户Schema前缀
     *
     * @param sql 原始SQL语句
     * @param tenant 租户Schema名称
     * @return 处理后的SQL语句
     */
    private String processInsertSql(String sql, String tenant) {
        log.debug("处理INSERT语句");

        // 使用预编译的正则表达式
        Matcher insertMatcher = INSERT_PATTERN.matcher(sql);
        StringBuffer result = new StringBuffer();

        while (insertMatcher.find()) {
            String tableName = insertMatcher.group(1);
            if (shouldAddTenantSchema(tableName)) {
                insertMatcher.appendReplacement(result,
                    "INSERT INTO " + tenant + "." + tableName);
            } else {
                insertMatcher.appendReplacement(result,
                    "INSERT INTO " + tableName);
            }
        }
        insertMatcher.appendTail(result);

        return result.toString();
    }

    /**
     * 处理DELETE语句
     * 在"DELETE FROM 表名"模式中为表名添加schema前缀
     * 只对以"mtc_"开头的表名添加租户Schema前缀
     *
     * @param sql 原始SQL语句
     * @param tenant 租户Schema名称
     * @return 处理后的SQL语句
     */
    private String processDeleteSql(String sql, String tenant) {
        log.debug("处理DELETE语句");

        // 使用预编译的正则表达式
        Matcher deleteMatcher = DELETE_PATTERN.matcher(sql);
        StringBuffer result = new StringBuffer();

        while (deleteMatcher.find()) {
            String tableName = deleteMatcher.group(1);
            if (shouldAddTenantSchema(tableName)) {
                deleteMatcher.appendReplacement(result,
                    "DELETE FROM " + tenant + "." + tableName);
            } else {
                deleteMatcher.appendReplacement(result,
                    "DELETE FROM " + tableName);
            }
        }
        deleteMatcher.appendTail(result);

        return result.toString();
    }

    /**
     * 处理SELECT语句
     * 处理FROM子句和JOIN子句，为表名添加schema前缀
     * 只对以"mtc_"开头的表名添加租户Schema前缀
     *
     * @param sql 原始SQL语句
     * @param tenant 租户Schema名称
     * @return 处理后的SQL语句
     */
    private String processSelectSql(String sql, String tenant) {
        log.debug("处理SELECT语句");

        // 处理FROM子句，使用预编译的正则表达式
        Matcher fromMatcher = FROM_PATTERN.matcher(sql);
        StringBuffer fromResult = new StringBuffer();

        while (fromMatcher.find()) {
            String spaces = fromMatcher.group(1);
            String tableName = fromMatcher.group(2);
            if (shouldAddTenantSchema(tableName)) {
                fromMatcher.appendReplacement(fromResult,
                    spaces + "FROM " + tenant + "." + tableName);
            } else {
                fromMatcher.appendReplacement(fromResult,
                    spaces + "FROM " + tableName);
            }
        }
        fromMatcher.appendTail(fromResult);
        String result = fromResult.toString();

        // 处理JOIN子句，使用预编译的正则表达式
        Matcher joinMatcher = JOIN_PATTERN.matcher(result);
        StringBuffer joinResult = new StringBuffer();

        while (joinMatcher.find()) {
            String tableName = joinMatcher.group(1);
            if (shouldAddTenantSchema(tableName)) {
                joinMatcher.appendReplacement(joinResult,
                    " JOIN " + tenant + "." + tableName);
            } else {
                joinMatcher.appendReplacement(joinResult,
                    " JOIN " + tableName);
            }
        }
        joinMatcher.appendTail(joinResult);

        return joinResult.toString();
    }

    /**
     * 处理UPDATE语句
     * 在"UPDATE 表名"模式中为表名添加schema前缀
     * 只对以"mtc_"开头的表名添加租户Schema前缀
     *
     * @param sql 原始SQL语句
     * @param tenant 租户Schema名称
     * @return 处理后的SQL语句
     */
    private String processUpdateSql(String sql, String tenant) {
        log.debug("处理UPDATE语句");

        // 使用预编译的正则表达式
        Matcher updateMatcher = UPDATE_PATTERN.matcher(sql);
        StringBuffer result = new StringBuffer();

        while (updateMatcher.find()) {
            String tableName = updateMatcher.group(1);
            if (shouldAddTenantSchema(tableName)) {
                updateMatcher.appendReplacement(result,
                    "UPDATE " + tenant + "." + tableName);
            } else {
                updateMatcher.appendReplacement(result,
                    "UPDATE " + tableName);
            }
        }
        updateMatcher.appendTail(result);

        return result.toString();
    }

    /**
     * 创建代理对象
     * 用于将当前拦截器包装到目标对象上
     */
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    /**
     * 设置拦截器属性
     * 用于配置拦截器的参数（当前未使用）
     */
    @Override
    public void setProperties(Properties properties) {
    }
}