package com.extracme.saas.autocare.exception;

import com.extracme.saas.autocare.enums.ErrorCode;

/**
 * 用户相关异常
 */
public class UserException extends BusinessException {

    /**
     * 用户不存在
     */
    public static final ErrorCode USER_NOT_FOUND = ErrorCode.USER_NOT_FOUND;

    /**
     * 用户已存在
     */
    public static final ErrorCode USER_ALREADY_EXISTS = ErrorCode.USER_ALREADY_EXISTS;

    /**
     * 用户名或密码错误
     */
    public static final ErrorCode USERNAME_OR_PASSWORD_ERROR = ErrorCode.USERNAME_OR_PASSWORD_ERROR;

    /**
     * 账号已锁定
     */
    public static final ErrorCode ACCOUNT_LOCKED = ErrorCode.ACCOUNT_LOCKED;

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     */
    public UserException(ErrorCode errorCode) {
        super(errorCode);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param message 自定义错误信息
     */
    public UserException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param cause 原始异常
     */
    public UserException(ErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }

    /**
     * 构造函数
     *
     * @param code 错误码
     * @param message 错误信息
     */
    public UserException(int code, String message) {
        super(code, message);
    }

    /**
     * 构造函数
     *
     * @param code 错误码
     * @param message 错误信息
     * @param cause 原始异常
     */
    public UserException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }
}