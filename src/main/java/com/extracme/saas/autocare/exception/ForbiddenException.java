package com.extracme.saas.autocare.exception;

import com.extracme.saas.autocare.enums.ErrorCode;

/**
 * 禁止访问异常
 */
public class ForbiddenException extends BusinessException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public ForbiddenException(String message) {
        super(ErrorCode.FORBIDDEN, message);
    }

    /**
     * 构造函数
     */
    public ForbiddenException() {
        super(ErrorCode.FORBIDDEN);
    }
}