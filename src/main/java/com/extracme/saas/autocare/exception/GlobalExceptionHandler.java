package com.extracme.saas.autocare.exception;

import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.model.vo.Result;

import lombok.extern.slf4j.Slf4j;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 业务异常处理
     *
     * @param e 业务异常
     * @return 错误响应
     */
    @ExceptionHandler(BusinessException.class)
    public Result<?> handleBusinessException(BusinessException e) {
        log.error("业务异常: code={}, message={}", e.getCode(), e.getMessage(), e);
        return Result.error(e.getCode(), e.getMessage());
    }

    /**
     * 参数异常处理
     *
     * @param e 参数异常
     * @return 错误响应
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public Result<?> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("参数异常: message={}", e.getMessage(), e);
        return Result.error(ErrorCode.PARAM_TYPE_ERROR.getCode(), e.getMessage());
    }

    /**
     * 用户异常处理
     *
     * @param e 用户异常
     * @return 错误响应
     */
    @ExceptionHandler(UserException.class)
    public Result<?> handleUserException(UserException e) {
        log.error("用户异常: code={}, message={}", e.getCode(), e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }

    /**
     * 未授权异常处理
     *
     * @param e 未授权异常
     * @return 错误响应
     */
    @ExceptionHandler(UnauthorizedException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result<Void> handleUnauthorizedException(UnauthorizedException e) {
        log.error("未授权异常: message={}", e.getMessage());
        return Result.error(ErrorCode.UNAUTHORIZED.getCode(), e.getMessage());
    }

    /**
     * 禁止访问异常处理
     *
     * @param e 禁止访问异常
     * @return 错误响应
     */
    @ExceptionHandler(ForbiddenException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<Void> handleForbiddenException(ForbiddenException e) {
        log.error("禁止访问异常: message={}", e.getMessage());
        return Result.error(ErrorCode.FORBIDDEN.getCode(), e.getMessage());
    }

    /**
     * 参数校验异常处理 - @Valid注解校验失败时抛出
     *
     * @param e 参数校验异常
     * @return 错误响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        StringBuilder errorMsg = new StringBuilder();

        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            errorMsg.append(fieldError.getDefaultMessage()).append("; ");
        }

        log.error("参数校验异常: {}", errorMsg);
        return Result.error(ErrorCode.PARAM_VALID_ERROR.getCode(), errorMsg.toString());
    }

    /**
     * 参数绑定异常处理 - 表单提交时参数类型错误
     *
     * @param e 参数绑定异常
     * @return 错误响应
     */
    @ExceptionHandler(BindException.class)
    public Result<?> handleBindException(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        StringBuilder errorMsg = new StringBuilder();

        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            errorMsg.append(fieldError.getDefaultMessage()).append("; ");
        }

        log.error("参数绑定异常: {}", errorMsg);
        return Result.error(ErrorCode.PARAM_BIND_ERROR.getCode(), errorMsg.toString());
    }

    /**
     * 系统异常处理
     *
     * @param e 系统异常
     * @return 错误响应
     */
    @ExceptionHandler(Exception.class)
    public Result<?> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error(ErrorCode.INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
    }
}