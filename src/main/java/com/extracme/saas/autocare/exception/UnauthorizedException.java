package com.extracme.saas.autocare.exception;

import com.extracme.saas.autocare.enums.ErrorCode;

/**
 * 未授权异常
 */
public class UnauthorizedException extends BusinessException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public UnauthorizedException(String message) {
        super(ErrorCode.UNAUTHORIZED, message);
    }

    /**
     * 构造函数
     */
    public UnauthorizedException() {
        super(ErrorCode.UNAUTHORIZED);
    }
}