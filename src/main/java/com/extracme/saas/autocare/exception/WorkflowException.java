package com.extracme.saas.autocare.exception;

import com.extracme.saas.autocare.enums.ErrorCode;

/**
 * 工作流相关异常
 */
public class WorkflowException extends BusinessException {


    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     */
    public WorkflowException(ErrorCode errorCode) {
        super(errorCode);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param message 自定义错误信息
     */
    public WorkflowException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param cause 原始异常
     */
    public WorkflowException(ErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }

    /**
     * 构造函数
     *
     * @param code 错误码
     * @param message 错误信息
     */
    public WorkflowException(int code, String message) {
        super(code, message);
    }

    /**
     * 构造函数
     *
     * @param code 错误码
     * @param message 错误信息
     * @param cause 原始异常
     */
    public WorkflowException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }
}