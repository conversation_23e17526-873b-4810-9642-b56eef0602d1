package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 修理厂类型枚举
 *
 * <AUTHOR>
 * @date 2024/06/15
 */
@Getter
@AllArgsConstructor
public enum RepairDepotTypeEnum {
    
    /**
     * 合作修理厂
     */
    COOPERATIVE(1, "合作修理厂"),
    
    /**
     * 非合作修理厂
     */
    NON_COOPERATIVE(2, "非合作修理厂");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举值
     *
     * @param code 类型编码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static RepairDepotTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RepairDepotTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取类型描述
     *
     * @param code 类型编码
     * @return 类型描述，如果未找到则返回空字符串
     */
    public static String getDescriptionByCode(Integer code) {
        RepairDepotTypeEnum type = getByCode(code);
        return type == null ? "" : type.getDescription();
    }

    /**
     * 检查编码是否有效
     *
     * @param code 类型编码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}