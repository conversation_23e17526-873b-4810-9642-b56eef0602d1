package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维修项目类型枚举
 *
 * <AUTHOR>
 * @date 2024/06/08
 */
@Getter
@AllArgsConstructor
public enum RepairItemTypeEnum {
    
    /**
     * 保养
     */
    MAINTENANCE(1, "保养"),
    
    /**
     * 终端
     */
    TERMINAL(2, "终端"),
    
    /**
     * 维修
     */
    REPAIR(3, "维修");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据编码获取枚举值
     *
     * @param code 类型编码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static RepairItemTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RepairItemTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取类型名称
     *
     * @param code 类型编码
     * @return 类型名称，如果未找到则返回空字符串
     */
    public static String getNameByCode(Integer code) {
        RepairItemTypeEnum type = getByCode(code);
        return type == null ? "" : type.getName();
    }

    /**
     * 检查编码是否有效
     *
     * @param code 类型编码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 根据名称获取枚举值
     *
     * @param name 类型名称
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static RepairItemTypeEnum getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        for (RepairItemTypeEnum type : values()) {
            if (type.getName().equals(name.trim())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取类型编码
     *
     * @param name 类型名称
     * @return 类型编码，如果未找到则返回null
     */
    public static Integer getCodeByName(String name) {
        RepairItemTypeEnum type = getByName(name);
        return type == null ? null : type.getCode();
    }
}
