package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作日志表枚举
 */
@Getter
@AllArgsConstructor
public enum OperateLogTableEnum {

    /**
     * 维修任务表
     */
    MTC_REPAIR_TASK(1, "mtc_repair_task"),

    /**
     * 维修厂信息表
     */
    MTC_REPAIR_DEPOT_INFO(2, "mtc_repair_depot_info"),

    /**
     * 换件项目表
     */
    MTC_REPLACE_ITEM(3, "mtc_replace_item"),

    /**
     * 修理项目表
     */
    MTC_REPAIR_ITEM(4, "mtc_repair_item"),

    /**
     * 主配件库表
     */
    ITEM_LIBRARY_NATIONAL(5, "item_library_national"),

    /**
     * 本地配件库表
     */
    ITEM_LIBRARY_LOCAL(6, "item_library_local");

    /**
     * 操作类型编码
     */
    private final Integer code;

    /**
     * 操作类型名称
     */
    private final String name;

    /**
     * 根据编码获取枚举值
     *
     * @param code 操作类型编码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static OperateLogTableEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OperateLogTableEnum operateType : values()) {
            if (operateType.getCode().equals(code)) {
                return operateType;
            }
        }
        return null;
    }
}
