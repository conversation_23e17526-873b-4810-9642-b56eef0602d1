package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务类型枚举
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Getter
@AllArgsConstructor
public enum TaskTypeEnum {
    
    /**
     * 自建任务
     */
    SELF_CREATED(0, "自建任务"),

    /**
     * 事故维修
     */
    ACCIDENT_REPAIR(1, "事故维修"),

    /**
     * 自费维修
     */
    SELF_PAID_REPAIR(2, "自费维修"),

    /**
     * 车辆保养
     */
    VEHICLE_MAINTENANCE(3, "车辆保养"),

    /**
     * 轮胎任务
     */
    TIRE_TASK(4, "轮胎任务"),

    /**
     * 自费维修(原车辆保养)
     */
    SELF_PAID_MAINTENANCE(5, "自费维修(原车辆保养)"),

    /**
     * 常规保养
     */
    REGULAR_MAINTENANCE(6, "常规保养"),

    /**
     * 终端维修
     */
    TERMINAL_REPAIR(7, "终端维修");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举值
     *
     * @param code 类型编码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static TaskTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaskTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取类型描述
     *
     * @param code 类型编码
     * @return 类型描述，如果未找到则返回null
     */
    public static String getDescriptionByCode(Integer code) {
        TaskTypeEnum type = getByCode(code);
        return type == null ? null : type.getDescription();
    }
} 