package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维修任务图片类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RepairPicTypeEnum {
    /**
     * 图片类型
     */
    DAMAGED_PART_PICTURE(1, "损坏部位图片", "damagedPartPicture"),
    REPAIR_PICTURE(2, "维修图片/车辆验收图片", "repairPicture"),
    CREATE_PICTURE(3, "创建图片", "createPicture"),
    CREATE_VIDEO(4, "创建视频", "createVideo"),
    DRIVING_LICENSE_PICTURE(10, "行驶证图片", "drivingLicensePicture"),
    POLICY_PICTURE(11, "保单图片", "policyPicture"),
    ACCIDENT_PICTURE(12, "事故图片", "accidentPicture"),
    DAMAGE_A_PICTURE(13, "车损图片(标的车)", "damageAPicture"),
    DAMAGE_B_PICTURE(14, "车损图片(三者车)", "damageBPicture"),
    CLAIMS_PICTURE(15, "理赔材料", "claimsPicture"),
    OTHER_PICTURE(16, "其他图片", "otherPicture"),
    CHECK_VIDEO(17, "验收视频", "checkVideo"),
    AFTER_PIC(18, "维修后图片", "afterPic"),
    ACCIDENT_LIABILITY_CONFIRMATION_PICTURE(19, "事故责任认定书图片", "accidentLiabilityConfirmationPicture"),
    INSURANCE_COMPANY_LOSS_ORDER_PICTURE(20, "保司定损单图片", "insuranceCompanyLossOrderPicture"),
    OUR_DRIVER_LICENSE_PICTURE(21, "我方驾驶证图片", "ourDriverLicensePicture"),
    CUST_PICTURE(22, "客户直付凭证", "custPicture"),
    DAMAGED_PART_VIDEO(23, "损坏部位视频", "damagedPartVideo");

    /**
     * 图片类型ID
     */
    private final Integer typeId;

    /**
     * 图片类型名称
     */
    private final String typeName;

    /**
     * 参数名称
     */
    private final String paramName;

    /**
     * 根据图片类型ID获取图片类型名称
     * @param typeId 图片类型ID
     * @return 图片类型名称
     */
    public static String getTypeNameById(Integer typeId) {
        for (RepairPicTypeEnum item : RepairPicTypeEnum.values()) {
            if (item.getTypeId().equals(typeId)) {
                return item.getTypeName();
            }
        }
        return "";
    }

    /**
     * 根据图片类型ID获取参数名称
     * @param typeId 图片类型ID
     * @return 参数名称
     */
    public static String getParamNameById(Integer typeId) {
        for (RepairPicTypeEnum item : RepairPicTypeEnum.values()) {
            if (item.getTypeId().equals(typeId)) {
                return item.getParamName();
            }
        }
        return "";
    }

    /**
     * 根据参数名称获取图片类型ID
     * @param paramName 参数名称
     * @return 图片类型ID
     */
    public static Integer getTypeIdByParamName(String paramName) {
        for (RepairPicTypeEnum item : RepairPicTypeEnum.values()) {
            if (item.getParamName().equals(paramName)) {
                return item.getTypeId();
            }
        }
        return null;
    }
}