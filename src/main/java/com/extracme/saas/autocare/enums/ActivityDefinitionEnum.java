package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 活动定义枚举
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
@Getter
@AllArgsConstructor
public enum ActivityDefinitionEnum {
    
    /**
     * 待分配
     */
    PENDING_ASSIGNMENT("PENDING_ASSIGNMENT", "待分配", "等待任务分配", 1),

    /**
     * 车辆交接
     */
    VEHICLE_TRANSFER("VEHICLE_TRANSFER", "车辆交接", "接收客户车辆并进行初步检查", 2),

    /**
     * 进保预审
     */
    PRE_INSPECTION_REVIEW("PRE_INSPECTION_REVIEW", "进保预审", "对车辆进行详细检查和预审", 3),

    /**
     * 维修报价
     */
    REPAIR_QUOTATION("REPAIR_QUOTATION", "维修报价", "根据检查结果提供维修方案和报价", 4),

    /**
     * 核损核价
     */
    LOSS_ASSESSMENT("LOSS_ASSESSMENT", "核损核价", "对车辆损坏情况进行评估和定价", 5),

    /**
     * 车辆维修
     */
    IN_REPAIR("IN_REPAIR", "车辆维修", "按照确认的方案进行维修", 6),

    /**
     * 车辆验收
     */
    QUALITY_INSPECTION("QUALITY_INSPECTION", "车辆验收", "对维修结果进行质量检查", 7);

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 节点顺序
     */
    private final Integer sequence;

    /**
     * 根据编码获取枚举值
     *
     * @param code 状态编码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static ActivityDefinitionEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ActivityDefinitionEnum activity : values()) {
            if (activity.getCode().equals(code)) {
                return activity;
            }
        }
        return null;
    }

    /**
     * 获取状态名称
     *
     * @param code 状态编码
     * @return 状态名称，如果未找到则返回null
     */
    public static String getNameByCode(String code) {
        ActivityDefinitionEnum activity = getByCode(code);
        return activity == null ? null : activity.getName();
    }
    
    /**
     * 获取状态描述
     *
     * @param code 状态编码
     * @return 状态描述，如果未找到则返回null
     */
    public static String getDescriptionByCode(String code) {
        ActivityDefinitionEnum activity = getByCode(code);
        return activity == null ? null : activity.getDescription();
    }
    
    /**
     * 获取节点顺序
     *
     * @param code 状态编码
     * @return 节点顺序，如果未找到则返回null
     */
    public static Integer getSequenceByCode(String code) {
        ActivityDefinitionEnum activity = getByCode(code);
        return activity == null ? null : activity.getSequence();
    }
    
    /**
     * 根据节点顺序获取枚举值
     *
     * @param sequence 节点顺序
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static ActivityDefinitionEnum getBySequence(Integer sequence) {
        if (sequence == null) {
            return null;
        }
        for (ActivityDefinitionEnum activity : values()) {
            if (activity.getSequence().equals(sequence)) {
                return activity;
            }
        }
        return null;
    }
} 
