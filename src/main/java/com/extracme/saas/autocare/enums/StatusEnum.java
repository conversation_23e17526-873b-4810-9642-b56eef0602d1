package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 状态枚举
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {
    
    /**
     * 启用
     */
    ENABLE(1, "启用"),
    
    /**
     * 禁用
     */
    DISABLE(0, "禁用");

    /**
     * 状态编码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码查找枚举
     *
     * @param description 状态码（字符串）
     * @return 对应的枚举，如果未找到返回null
     */
    public static StatusEnum getByCode(String description) {
        if (description == null || description.trim().isEmpty()) {
            return null;
        }
        for (StatusEnum status : values()) {
            if (status.getDescription().toString().equals(description)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码查找枚举
     *
     * @param code 状态码（整数）
     * @return 对应的枚举，如果未找到返回null
     */
    public static StatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查状态码是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
