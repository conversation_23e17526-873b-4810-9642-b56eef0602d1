package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作日志操作类型枚举
 */
@Getter
@AllArgsConstructor
public enum OperateLogOperateTypeEnum {

    /**
     * 新增
     */
    CREATE(1, "新增"),

    /**
     * 修改
     */
    UPDATE(2, "修改"),

    /**
     * 删除
     */
    DELETE(3, "删除"),

    /**
     * 禁用
     */
    DISABLE(4, "禁用"),

    /**
     * 启用
     */
    ENABLE(5, "启用");

    /**
     * 操作类型编码
     */
    private final Integer code;

    /**
     * 操作类型名称
     */
    private final String name;

    /**
     * 根据编码获取枚举值
     *
     * @param code 操作类型编码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static OperateLogOperateTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OperateLogOperateTypeEnum operateType : values()) {
            if (operateType.getCode().equals(code)) {
                return operateType;
            }
        }
        return null;
    }

    /**
     * 获取操作类型名称
     *
     * @param code 操作类型编码
     * @return 操作类型名称，如果未找到则返回null
     */
    public static String getNameByCode(Integer code) {
        OperateLogOperateTypeEnum operateType = getByCode(code);
        return operateType == null ? null : operateType.getName();
    }
}
