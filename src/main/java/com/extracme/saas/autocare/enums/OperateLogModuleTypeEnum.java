package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作日志模块类型枚举
 */
@Getter
@AllArgsConstructor
public enum OperateLogModuleTypeEnum {

    /**
     * 用户管理
     */
    USER_MANAGEMENT(1, "用户管理"),

    /**
     * 系统管理（角色、权限）
     */
    SYSTEM_MANAGEMENT(2, "系统管理");

    /**
     * 模块类型编码
     */
    private final Integer code;

    /**
     * 模块类型名称
     */
    private final String name;

    /**
     * 根据编码获取枚举值
     *
     * @param code 模块类型编码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static OperateLogModuleTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OperateLogModuleTypeEnum moduleType : values()) {
            if (moduleType.getCode().equals(code)) {
                return moduleType;
            }
        }
        return null;
    }

    /**
     * 获取模块类型名称
     *
     * @param code 模块类型编码
     * @return 模块类型名称，如果未找到则返回null
     */
    public static String getNameByCode(Integer code) {
        OperateLogModuleTypeEnum moduleType = getByCode(code);
        return moduleType == null ? null : moduleType.getName();
    }
}
