package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维修类型枚举
 *
 * <AUTHOR>
 * @date 2024/06/10
 */
@Getter
@AllArgsConstructor
public enum RepairTypeEnum {
    
    /**
     * 事故维修
     */
    ACCIDENT_REPAIR(1, "事故维修", "SG"),
    
    /**
     * 自费维修
     */
    SELF_PAID_REPAIR(2, "自费维修", "ZF"),
    
    /**
     * 车辆保养
     */
    VEHICLE_MAINTENANCE(3, "车辆保养", "BY"),
    
    /**
     * 轮胎任务
     */
    TIRE_TASK(4, "轮胎任务", "LT"),
    /**
     * 其他维修
     */

    /**
     * 自费维修（原车辆保养）
     */
    SELF_MAINTENANCE_OLD(5, "自费维修（原车辆保养）", "LZ"),
    
    /**
     * 常规保养
     */
    REGULAR_MAINTENANCE(6, "常规保养", "CG"),
    
    /**
     * 终端维修
     */
    TERMINAL_REPAIR(7, "终端维修", "ZD"),

    /**
     * 洗车
     */
    CAR_WASH(8, "洗车", "XC");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 任务编号前缀
     */
    private final String taskNoPrefix;

    /**
     * 根据编码获取枚举值
     *
     * @param code 类型编码
     * @return 对应的枚举值，如果未找到则返回OTHER_REPAIR
     */
    public static RepairTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RepairTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取类型名称
     *
     * @param code 类型编码
     * @return 类型名称
     */
    public static String getNameByCode(Integer code) {
        return getByCode(code).getName();
    }
    
    /**
     * 获取任务编号前缀
     *
     * @param code 类型编码
     * @return 任务编号前缀
     */
    public static String getTaskNoPrefix(Integer code) {
        return getByCode(code).getTaskNoPrefix();
    }
}
