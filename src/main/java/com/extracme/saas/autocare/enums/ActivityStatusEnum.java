package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 活动状态枚举
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
@Getter
@AllArgsConstructor
public enum ActivityStatusEnum {
    
    /**
     * 未处理
     */
    UNPROCESSED("UNPROCESSED", "未处理"),
    
    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),
    
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),
    
    /**
     * 已驳回
     */
    REJECTED("REJECTED", "已驳回"),
    
    /**
     * 已暂停
     */
    SUSPENDED("SUSPENDED", "已暂停"),
    
    /**
     * 已关闭
     */
    CLOSED("CLOSED", "已关闭"),
    
    /**
     * 已跳过
     */
    SKIPPED("SKIPPED", "已跳过"),
    
    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 已删除
     */
    DELETE("DELETE", "已删除");

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码查找枚举
     *
     * @param code 状态码
     * @return 对应的枚举，如果未找到返回null
     */
    public static ActivityStatusEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ActivityStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查状态码是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
