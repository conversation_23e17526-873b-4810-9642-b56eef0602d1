package com.extracme.saas.autocare;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 车辆维修平台SaaS启动类
 *
 * <AUTHOR> Team
 */
@SpringBootApplication
@EnableTransactionManagement
@EnableCaching
@EnableScheduling
@EnableAsync
@MapperScan("com.extracme.saas.autocare.mapper")
public class AutoCareSaasApplication {

    public static void main(String[] args) {
        SpringApplication.run(AutoCareSaasApplication.class, args);
    }
}