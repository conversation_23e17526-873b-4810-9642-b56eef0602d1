package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.RepairQuoteUpdateDTO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.dto.RepairQuoteSubmitDTO;

/**
 * 维修报价服务接口
 */
public interface RepairQuoteService {

    /**
     * 保存维修报价信息
     *
     * @param repairQuoteDTO 维修报价信息
     * @return 是否保存成功
     */
    Result<Void> saveRepairQuote(RepairQuoteUpdateDTO repairQuoteDTO);

    /**
     * 提交维修报价审核
     *
     * @param submitDTO 提交审核参数
     * @return 是否提交成功
     */
    Result<Void> submitRepairQuote(RepairQuoteSubmitDTO submitDTO);
}