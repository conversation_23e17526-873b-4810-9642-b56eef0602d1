package com.extracme.saas.autocare.service;

import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.extracme.saas.autocare.model.dto.VehicleLeavingFactorySubmitDTO;
import com.extracme.saas.autocare.model.dto.VehicleLeavingFactoryQueryDTO;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryCountVO;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryDetailsVO;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryResultVO;

/**
 * 车辆出厂登记服务接口
 * 
 * <AUTHOR> Code
 * @date 2024/05/25
 */
public interface VehicleLeavingFactoryService {

    /**
     * 查询任务信息
     * 
     * @param queryDTO 查询条件
     * @return 出厂登记统计信息
     */
    VehicleLeavingFactoryCountVO queryLeavingFactoryList(VehicleLeavingFactoryQueryDTO queryDTO);

    /**
     * 导出数据
     * 
     * @param queryDTO 查询条件
     * @param response HTTP响应
     * @return 导出结果
     */
    void exportData(VehicleLeavingFactoryQueryDTO queryDTO, HttpServletResponse response);

    /**
     * 提交出厂登记
     * 
     * @param submitDTO 提交信息
     * @return 提交结果
     */
    void submit(VehicleLeavingFactorySubmitDTO submitDTO);

    /**
     * 查询明细信息
     * 
     * @param leavingId 出厂登记ID
     * @return 出厂登记详情
     */
    VehicleLeavingFactoryDetailsVO queryDetails(Long leavingId);

    /**
     * 根据任务编号查询出厂登记列表
     * 
     * @param taskNo 任务编号
     * @return 出厂登记列表
     */
    List<VehicleLeavingFactoryResultVO> queryDeliveryListByTaskNo(String taskNo);

    /**
     * 新增出厂登记
     * 
     * @param taskNo             任务编号
     * @param vin                车架号
     * @param repairDepotId      修理厂ID
     * @param repairDepotName    修理厂名称
     * @param repairDepotOrgId   修理厂组织ID
     * @param repairDepotSapCode 修理厂SAP编码
     * @param taskInflowTime     任务流入时间
     */
    void insertLeavingFactory(String taskNo, String vin, String repairDepotId, String repairDepotName,
            String repairDepotOrgId, String repairDepotSapCode, Date taskInflowTime);

    /**
     * 关闭出厂登记
     * 
     * @param taskNo 任务编号
     */
    void closeLeavingFactory(String taskNo);
}