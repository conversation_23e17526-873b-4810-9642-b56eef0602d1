package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.MerchantAdminQueryDTO;
import com.extracme.saas.autocare.model.dto.UserCreateDTO;
import com.extracme.saas.autocare.model.dto.UserQueryDTO;
import com.extracme.saas.autocare.model.dto.UserUpdateDTO;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.MerchantAdminVO;
import com.extracme.saas.autocare.model.vo.UserVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 创建用户
     *
     * @param createDTO 用户创建信息
     * @return 用户ID
     */
    Long createUser(UserCreateDTO createDTO);

    /**
     * 更新用户
     *
     * @param updateDTO 用户更新信息
     */
    void updateUser(UserUpdateDTO updateDTO);

    /**
     * 分页查询用户列表
     *
     * @param queryDTO 查询参数
     * @return 用户列表
     */
    BasePageVO<UserVO> getUserList(UserQueryDTO queryDTO);

    /**
     * 根据ID获取用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    UserVO getUserById(Long id);

    /**
     * 分页查询商户管理员列表
     *
     * @param queryDTO 查询条件
     * @return 商户管理员列表·
     */
    BasePageVO<MerchantAdminVO> getMerchantAdminList(MerchantAdminQueryDTO queryDTO);

    /**
     * 根据ID获取商户管理员详情
     *
     * @param id 商户管理员ID
     * @return 商户管理员详情
     */
    MerchantAdminVO getMerchantAdminById(Long id);

    /**
     * 启用用户
     *
     * @param userId 用户ID
     */
    void enableUser(Long userId);

    /**
     * 禁用用户
     *
     * @param userId 用户ID
     */
    void disableUser(Long userId);

    /**
     * 更新用户状态
     *
     * @param userId 用户ID
     * @param status 状态：0-禁用，1-启用
     */
    void updateUserStatus(Long userId, Integer status);

    /**
     * 更新用户的机构关联
     *
     * @param userId 用户ID
     * @param orgIds 机构ID列表
     * @return 操作是否成功
     */
    boolean updateUserOrgs(Long userId, List<String> orgIds);

    /**
     * 根据机构ID列表查询用户
     *
     * @param orgIds 机构ID列表
     * @return 用户列表
     */
    List<SysUser> getUsersByOrgIds(List<String> orgIds);

    /**
     * 获取当前登录用户信息（包含距离过期剩余天数）
     *
     * @return 当前登录用户信息，包含 daysUntilExpiry 字段
     */
    LoginUser getCurrentUserWithExpiry();

    /**
     * 根据手机号查询所有用户（包括不同状态的用户）
     *
     * @param mobile 手机号
     * @return 用户列表
     */
    List<SysUser> findAllByMobile(String mobile);
}