package com.extracme.saas.autocare.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.RepairItemCheckInfoDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairItemCheckInfo;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibrary;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibraryLocal;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableRepairItemCheckInfoService;
import com.extracme.saas.autocare.repository.TableRepairItemLibraryLocalService;
import com.extracme.saas.autocare.repository.TableRepairItemLibraryService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.RepairItemCheckInfoService;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 维修项目核损信息服务实现类
 */
@Slf4j
@Service
public class RepairItemCheckInfoServiceImpl implements RepairItemCheckInfoService {

    @Autowired
    private TableRepairItemCheckInfoService tableRepairItemCheckInfoService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairItemLibraryLocalService tableRepairItemLibraryLocalService;

    @Autowired
    private TableRepairItemLibraryService tableRepairItemLibraryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addItemCheckInfo(String taskNo, List<RepairItemCheckInfoDTO> itemCheckInfoList) {
        log.info("添加维修项目核损信息, 任务编号: {}", taskNo);
        
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + taskNo);
        }

        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + taskNo);
        }

        if (!"INSURANCE_PRE_REVIEW".equals(workflowInstance.getCurrentActivityCode())
                && !"REPAIR_QUOTATION".equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("当前工作流节点不是预审或报价节点");
        }

        // 根据当前环节决定调用哪个方法
        if ("INSURANCE_PRE_REVIEW".equals(workflowInstance.getCurrentActivityCode())) {
            return this.addItemCheckInfoPreReview(taskNo, itemCheckInfoList);
        } 
        if  ("REPAIR_QUOTATION".equals(workflowInstance.getCurrentActivityCode())){
            return this.addItemCheckInfoQuote(taskNo, itemCheckInfoList);
        }
        throw new BusinessException("无法添加维修项目核损信息");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addItemCheckInfoQuote(String taskNo, List<RepairItemCheckInfoDTO> itemCheckInfoList) {
        log.info("添加定损项目记录, 任务编号: {}", taskNo);
        
        // 获取当前登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();
        String operator = loginUser.getUsername();
        
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + taskNo);
        }
        
        // 删除该任务定损项目记录
        this.updateStatusAll(taskNo, 0);
        
        int count = 0;
        BigDecimal materialCostPrice = BigDecimal.ZERO;
        BigDecimal hourFeePrice = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        Date now = new Date();
        
        for (RepairItemCheckInfoDTO dto : itemCheckInfoList) {
            if (dto.getItemNumber() == null || dto.getItemNumber() <= 0) {
                throw new BusinessException("定损数量不能为空或者0");
            }
            
            // 保养任务判断保养周期和里程
            if (repairTask.getRepairTypeId() != null && repairTask.getRepairTypeId() == 3) {
                MtcRepairItemLibrary item = tableRepairItemLibraryService.selectById(dto.getItemId());
                if (item != null && item.getItemType() == 1) {
                    MtcRepairItemCheckInfo lastRecord = tableRepairItemCheckInfoService.selectByTaskNoAndItemId(repairTask.getVin(), dto.getItemId());
                    if (lastRecord != null) {
                        MtcRepairTask lastTask = tableRepairTaskService.selectByTaskNo(lastRecord.getTaskNo());
                        if (lastTask != null) {
                            // 检查保养周期
                            if (item.getMaintenanceCycle() > 0 && lastTask.getVehicleCheckTime() != null && 
                                    getMonthDiff(now, lastTask.getVehicleCheckTime()) < item.getMaintenanceCycle()) {
                                throw new BusinessException(repairTask.getVehicleNo() + "不满足项目:[" + item.getItemName() + "]的保养周期的限制规则");
                            }
                            
                            // 检查保养里程
                            if (item.getMaintenanceMileage() != null && item.getMaintenanceMileage().doubleValue() > 0
                                    && lastTask.getTotalMileage() != null
                                    && repairTask.getTotalMileage() != null
                                    && repairTask.getTotalMileage().subtract(lastTask.getTotalMileage()).compareTo(item.getMaintenanceMileage()) < 0) {
                                throw new BusinessException(repairTask.getVehicleNo() + "不满足项目[" + item.getItemName() + "]保养里程的限制规则");
                            }
                        }
                    }
                }
            }
            
            // 创建维修项目核损信息
            MtcRepairItemCheckInfo checkInfo = new MtcRepairItemCheckInfo();
            BeanUtils.copyProperties(dto, checkInfo);
            checkInfo.setId(null);
            checkInfo.setTaskNo(taskNo);
            
            // 验证材料费和工时费
            if (checkInfo.getInsuranceQuoteMaterialCostPrice() == null || checkInfo.getInsuranceQuoteHourFeePrice() == null) {
                throw new BusinessException("已选项目的材料费和工时费不可为空！");
            }
            
            // 计算金额
            BigDecimal insuranceQuoteAmount = (checkInfo.getInsuranceQuoteMaterialCostPrice().add(checkInfo.getInsuranceQuoteHourFeePrice()))
                    .multiply(new BigDecimal(checkInfo.getItemNumber()));
            
            checkInfo.setVin(repairTask.getVin());
            checkInfo.setInsuranceQuoteAmount(insuranceQuoteAmount);
            checkInfo.setCreateBy(operator);
            checkInfo.setUpdateBy(operator);
            checkInfo.setCreatedTime(now);
            checkInfo.setUpdatedTime(now);
            checkInfo.setInsurancePreReviewStatus(0);
            checkInfo.setStatus(1); // 有效状态
            // 初始化核损字段为定损字段
            checkInfo.setViewHourFeePrice(checkInfo.getInsuranceQuoteHourFeePrice());
            checkInfo.setViewMaterialCostPrice(checkInfo.getInsuranceQuoteMaterialCostPrice());
            checkInfo.setViewNumber(checkInfo.getItemNumber());
            checkInfo.setViewAmount(checkInfo.getInsuranceQuoteAmount());
            
            // 插入记录
            tableRepairItemCheckInfoService.insert(checkInfo, operator);
            count++;
            
            // 累加金额
            materialCostPrice = materialCostPrice.add(checkInfo.getInsuranceQuoteMaterialCostPrice().multiply(new BigDecimal(checkInfo.getItemNumber())));
            hourFeePrice = hourFeePrice.add(checkInfo.getInsuranceQuoteHourFeePrice().multiply(new BigDecimal(checkInfo.getItemNumber())));
            totalAmount = totalAmount.add(insuranceQuoteAmount);
        }
        
        // 更新维修任务金额
        MtcRepairTask updateTask = new MtcRepairTask();
        updateTask.setId(repairTask.getId());
        updateTask.setTaskNo(taskNo);
        updateTask.setUpdateBy(operator);
        updateTask.setUpdatedTime(now);
        
        // 修改定损金额
        updateTask.setRepairReplaceTotalAmount(materialCostPrice);
        updateTask.setRepairRepairTotalAmount(hourFeePrice);
        updateTask.setRepairInsuranceTotalAmount(totalAmount);
        updateTask.setRepairTotalAmount(totalAmount);
        tableRepairTaskService.updateSelectiveById(updateTask);
        
        log.info("添加定损项目记录成功, 任务编号: {}, 记录数: {}, 总金额: {}", taskNo, count, totalAmount);
        return count;
    }

    /**
     * 计算两个日期之间的月份差
     * <p>
     * 该方法计算两个日期之间的完整月份差值，考虑了日期的天数。
     * 例如：
     * - 2023-01-15 和 2023-02-10 相差不足一个月，返回0
     * - 2023-01-15 和 2023-02-15 相差整一个月，返回1
     * - 2023-01-15 和 2023-03-14 相差不足两个月，返回1
     * - 2023-01-15 和 2023-03-15 相差整两个月，返回2
     * 
     * @param d1 第一个日期，不能为null
     * @param d2 第二个日期，不能为null
     * @return 两个日期之间的月份差的绝对值
     * @throws NullPointerException 如果任一参数为null
     */
    public static int getMonthDiff(Date d1, Date d2) {
        if (d1 == null || d2 == null) {
            throw new NullPointerException("日期参数不能为null");
        }
        
        // 将Date转换为LocalDate
        LocalDate localDate1 = d1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = d2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        
        // 计算两个日期之间的周期
        Period period = Period.between(localDate1, localDate2);
        
        // 计算总月份差
        int months = period.getYears() * 12 + period.getMonths();
        
        // 考虑天数差异
        // 如果第一个日期的天数大于第二个日期的天数，则减少一个月
        if (localDate1.getDayOfMonth() > localDate2.getDayOfMonth()) {
            months = months - 1;
        }
        
        // 返回绝对值，不考虑日期先后顺序
        return Math.abs(months);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addItemCheckInfoPreReview(String taskNo, List<RepairItemCheckInfoDTO> itemCheckInfoList) {
        log.info("添加预审项目记录, 任务编号: {}", taskNo);
        
        // 获取当前登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();
        String operator = loginUser.getUsername();
        
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + taskNo);
        }
        
        // 删除该任务预审项目记录
        this.updateStatusAll(taskNo, 1);
        
        int count = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        Date now = new Date();
        
        for (RepairItemCheckInfoDTO dto : itemCheckInfoList) {
            if (dto.getItemNumber() == null || dto.getItemNumber() <= 0) {
                throw new BusinessException("预审数量不能为空或者0");
            }
            
            // 创建维修项目核损信息
            MtcRepairItemCheckInfo checkInfo = new MtcRepairItemCheckInfo();
            BeanUtils.copyProperties(dto, checkInfo);
            checkInfo.setId(null);
            checkInfo.setTaskNo(taskNo);
            
            // 验证材料费和工时费
            if (checkInfo.getInsuranceQuoteMaterialCostPrice() == null || checkInfo.getInsuranceQuoteHourFeePrice() == null) {
                throw new BusinessException("已选项目的材料费和工时费不可为空！");
            }
            
            // 计算金额
            BigDecimal insuranceQuoteAmount = (checkInfo.getInsuranceQuoteMaterialCostPrice().add(checkInfo.getInsuranceQuoteHourFeePrice()))
                    .multiply(new BigDecimal(checkInfo.getItemNumber()));
            
            checkInfo.setVin(repairTask.getVin());
            checkInfo.setInsuranceQuoteAmount(insuranceQuoteAmount);
            checkInfo.setCreateBy(operator);
            checkInfo.setUpdateBy(operator);
            checkInfo.setCreatedTime(now);
            checkInfo.setUpdatedTime(now);
            checkInfo.setInsurancePreReviewStatus(1);
            checkInfo.setStatus(1); // 有效状态
            
            // 插入记录
            tableRepairItemCheckInfoService.insert(checkInfo, operator);
            count++;
            
            // 累加金额
            totalAmount = totalAmount.add(insuranceQuoteAmount);
        }
        
        // 更新维修任务金额
        MtcRepairTask updateTask = new MtcRepairTask();
        updateTask.setId(repairTask.getId());
        updateTask.setTaskNo(taskNo);
        updateTask.setUpdateBy(operator);
        updateTask.setUpdatedTime(now);
        
        // 修改预审金额
        updateTask.setRepairReviewTotalAmount(totalAmount);
        
        tableRepairTaskService.updateSelectiveById(updateTask);
        
        log.info("添加预审项目记录成功, 任务编号: {}, 记录数: {}, 总金额: {}", taskNo, count, totalAmount);
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCheckInfoStatus(Long id) {
        log.info("更新维修项目核损状态, ID: {}", id);
        
        if (id == null) {
            throw new BusinessException("维修项目核损信息ID不能为空");
        }
        
        // 获取当前登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();
        String operator = loginUser.getUsername();
        
        // 获取维修项目核损信息
        MtcRepairItemCheckInfo checkInfo = tableRepairItemCheckInfoService.selectById(id);
        if (checkInfo == null) {
            throw new BusinessException("未找到维修项目核损信息: " + id);
        }
        
        // 更新状态为无效
        MtcRepairItemCheckInfo updateInfo = new MtcRepairItemCheckInfo();
        updateInfo.setId(id);
        updateInfo.setStatus(0); // 无效状态
        updateInfo.setUpdateBy(operator);
        
        int count = tableRepairItemCheckInfoService.updateSelectiveById(updateInfo, operator);
        
        // 更新任务价格
        if (count > 0) {
            String taskNo = checkInfo.getTaskNo();
            MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
            
            if (repairTask != null) {
                BigDecimal materialCostPrice = checkInfo.getInsuranceQuoteMaterialCostPrice().multiply(new BigDecimal(checkInfo.getItemNumber()));
                BigDecimal hourFeePrice = checkInfo.getInsuranceQuoteHourFeePrice().multiply(new BigDecimal(checkInfo.getItemNumber()));
                
                MtcRepairTask updateTask = new MtcRepairTask();
                updateTask.setId(repairTask.getId());
                updateTask.setTaskNo(taskNo);
                updateTask.setUpdateBy(operator);
                updateTask.setUpdatedTime(new Date());
                
                if (checkInfo.getInsurancePreReviewStatus() == 0) {
                    // 更新定损金额
                    updateTask.setRepairReplaceTotalAmount(repairTask.getRepairReplaceTotalAmount().subtract(materialCostPrice));
                    updateTask.setRepairRepairTotalAmount(repairTask.getRepairRepairTotalAmount().subtract(hourFeePrice));
                    updateTask.setRepairInsuranceTotalAmount(repairTask.getRepairInsuranceTotalAmount().subtract(materialCostPrice).subtract(hourFeePrice));
                    updateTask.setRepairTotalAmount(updateTask.getRepairInsuranceTotalAmount());
                } else {
                    // 更新预审金额
                    updateTask.setRepairReviewTotalAmount(repairTask.getRepairReviewTotalAmount().subtract(materialCostPrice).subtract(hourFeePrice));
                }
                
                tableRepairTaskService.updateSelectiveById(updateTask);
            }
        }
        
        log.info("更新维修项目核损状态成功, ID: {}, 影响行数: {}", id, count);
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatusAll(String taskNo, Integer insurancePreReviewStatus) {
        log.info("批量更新维修项目核损状态, 任务编号: {}, 预审状态: {}", taskNo, insurancePreReviewStatus);
        
        // 获取当前登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();
        String operator = loginUser.getUsername();
        
        // 查询符合条件的记录
        List<MtcRepairItemCheckInfo> checkInfoList = tableRepairItemCheckInfoService.selectByTaskNo(taskNo);
        if (CollectionUtils.isEmpty(checkInfoList)) {
            return 0;
        }
        
        // 过滤出符合预审状态的记录
        List<MtcRepairItemCheckInfo> filteredList = checkInfoList.stream()
                .filter(item -> item.getInsurancePreReviewStatus() != null && 
                        item.getInsurancePreReviewStatus().equals(insurancePreReviewStatus))
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(filteredList)) {
            return 0;
        }
        
        int count = 0;
        for (MtcRepairItemCheckInfo checkInfo : filteredList) {
            // 更新状态为无效
            MtcRepairItemCheckInfo updateInfo = new MtcRepairItemCheckInfo();
            updateInfo.setId(checkInfo.getId());
            updateInfo.setStatus(0); // 无效状态
            updateInfo.setUpdateBy(operator);
            updateInfo.setUpdatedTime(new Date());
            
            count += tableRepairItemCheckInfoService.updateSelectiveById(updateInfo, operator);
        }
        
        log.info("批量更新维修项目核损状态成功, 任务编号: {}, 预审状态: {}, 影响行数: {}", taskNo, insurancePreReviewStatus, count);
        return count;
    }

    @Override
    public List<RepairItemCheckInfoDTO> queryPreReviewListByTaskNo(String taskNo) {
        log.info("查询预审配件列表, 任务编号: {}", taskNo);
        return queryCheckListByTaskNoAndInsurancePreReviewStatus(taskNo, 1);
    }

    @Override
    public Integer countPreReviewListByTaskNo(String taskNo) {
        log.info("查询预审配件列表总数, 任务编号: {}", taskNo);
        List<RepairItemCheckInfoDTO> list = queryCheckListByTaskNoAndInsurancePreReviewStatus(taskNo, 1);
        return list != null ? list.size() : 0;
    }

    @Override
    public List<RepairItemCheckInfoDTO> queryCheckListByTaskNo(String taskNo) {
        log.info("查询定损配件列表, 任务编号: {}", taskNo);
        return queryCheckListByTaskNoAndInsurancePreReviewStatus(taskNo, 0);
    }

    @Override
    public List<RepairItemCheckInfoDTO> queryViewListByTaskNo(String taskNo, String orgId) {
        log.info("查询核损配件列表, 任务编号: {}, 组织ID: {}", taskNo, orgId);
        
        // 查询定损配件列表
        List<MtcRepairItemCheckInfo> checkInfoList = tableRepairItemCheckInfoService.selectByTaskNo(taskNo);
        if (CollectionUtils.isEmpty(checkInfoList)) {
            return new ArrayList<>();
        }
        
        // 过滤出有效且非预审的记录
        List<MtcRepairItemCheckInfo> filteredList = checkInfoList.stream()
                .filter(item -> item.getStatus() != null && item.getStatus() == 1 && 
                        item.getInsurancePreReviewStatus() != null && item.getInsurancePreReviewStatus() == 0)
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(filteredList)) {
            return new ArrayList<>();
        }
        
        // 转换为DTO
        List<RepairItemCheckInfoDTO> resultList = new ArrayList<>();
        for (MtcRepairItemCheckInfo checkInfo : filteredList) {
            RepairItemCheckInfoDTO dto = new RepairItemCheckInfoDTO();
            BeanUtils.copyProperties(checkInfo, dto);
            
            // 获取项目库信息，设置材料费和工时费上限
            MtcRepairItemLibrary itemLibrary = tableRepairItemLibraryService.selectById(checkInfo.getItemId());
            if (itemLibrary != null) {
                // 查询本地项目库
                MtcRepairItemLibraryLocal itemLibraryLocal = tableRepairItemLibraryLocalService.selectByItemIdAndOrgId(
                        checkInfo.getItemId(), orgId);
                
                // 设置材料费上限
                if (itemLibraryLocal != null && itemLibraryLocal.getMaterialCostLocalHighestPrice() != null && 
                        itemLibraryLocal.getMaterialCostLocalHighestPrice().doubleValue() > 0) {
                    dto.setMaterialCostHighestPrice(itemLibraryLocal.getMaterialCostLocalHighestPrice());
                } else if (itemLibrary.getMaterialCostNationalHighestPrice() != null) {
                    dto.setMaterialCostHighestPrice(itemLibrary.getMaterialCostNationalHighestPrice());
                }
                
                // 设置工时费上限
                if (itemLibraryLocal != null && itemLibraryLocal.getHourFeeLocalHighestPrice() != null && 
                        itemLibraryLocal.getHourFeeLocalHighestPrice().doubleValue() > 0) {
                    dto.setHourFeeHighestPrice(itemLibraryLocal.getHourFeeLocalHighestPrice());
                } else if (itemLibrary.getHourFeeNationalHighestPrice() != null) {
                    dto.setHourFeeHighestPrice(itemLibrary.getHourFeeNationalHighestPrice());
                }
            }
            
            resultList.add(dto);
        }
        
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateItemCheckInfo(String taskNo, List<RepairItemCheckInfoDTO> itemCheckInfoList) {
        log.info("更新定核损数据, 任务编号: {}", taskNo);
        
        if (CollectionUtils.isEmpty(itemCheckInfoList)) {
            throw new BusinessException("未经过定损维修报价，请退回后重新定损");
        }
        
        // 获取当前登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();
        String operator = loginUser.getUsername();
        
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + taskNo);
        }
        
        int count = 0;
        BigDecimal materialCostPrice = BigDecimal.ZERO;
        BigDecimal hourFeePrice = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        Date now = new Date();
        
        for (RepairItemCheckInfoDTO dto : itemCheckInfoList) {
            // 获取原记录
            MtcRepairItemCheckInfo checkInfo = tableRepairItemCheckInfoService.selectById(dto.getId());
            if (checkInfo == null) {
                continue;
            }
            
            // 更新核价信息
            MtcRepairItemCheckInfo updateInfo = new MtcRepairItemCheckInfo();
            updateInfo.setId(checkInfo.getId());
            updateInfo.setViewNumber(dto.getViewNumber());
            updateInfo.setViewMaterialCostPrice(dto.getViewMaterialCostPrice());
            updateInfo.setViewHourFeePrice(dto.getViewHourFeePrice());
            updateInfo.setViewAmount(dto.getViewAmount());
            updateInfo.setCheckStatus(dto.getCheckStatus());
            updateInfo.setRemark(dto.getRemark());
            updateInfo.setUpdateBy(operator);
            updateInfo.setUpdatedTime(now);
            
            count += tableRepairItemCheckInfoService.updateSelectiveById(updateInfo, operator);
            
            // 累加金额
            if (dto.getViewMaterialCostPrice() != null) {
                materialCostPrice = materialCostPrice.add(dto.getViewMaterialCostPrice().multiply(
                        new BigDecimal(dto.getViewNumber() != null ? dto.getViewNumber() : 0)));
            }
            
            if (dto.getViewHourFeePrice() != null) {
                hourFeePrice = hourFeePrice.add(dto.getViewHourFeePrice().multiply(
                        new BigDecimal(dto.getViewNumber() != null ? dto.getViewNumber() : 0)));
            }
            
            if (dto.getViewAmount() != null) {
                totalAmount = totalAmount.add(dto.getViewAmount());
            }
        }
        
        // 更新维修任务金额
        MtcRepairTask updateTask = new MtcRepairTask();
        updateTask.setId(repairTask.getId());
        updateTask.setTaskNo(taskNo);
        updateTask.setUpdateBy(operator);
        updateTask.setUpdatedTime(now);
        
        // 修改核损金额
        updateTask.setVehicleReplaceTotalAmount(materialCostPrice);
        updateTask.setVehicleRepairTotalAmount(hourFeePrice);
        updateTask.setVehicleInsuranceTotalAmount(totalAmount);
        updateTask.setRepairTotalAmount(totalAmount);
        
        // 如果是保养任务，更新保养金额
        if (repairTask.getRepairTypeId() != null && repairTask.getRepairTypeId() == 3) {
            updateTask.setMaintainAmount(totalAmount);
        }
        
        tableRepairTaskService.updateSelectiveById(updateTask);
        
        log.info("更新定核损数据成功, 任务编号: {}, 影响行数: {}, 总金额: {}", taskNo, count, totalAmount);
        return count;
    }

    @Override
    public List<RepairItemCheckInfoDTO> queryCheckListByTaskNoList(List<String> taskNoList) {
        log.info("查询任务列表的明细项, 任务编号列表: {}", taskNoList);
        
        if (CollectionUtils.isEmpty(taskNoList)) {
            return new ArrayList<>();
        }
        
        List<RepairItemCheckInfoDTO> resultList = new ArrayList<>();
        
        // 遍历任务编号列表，查询每个任务的明细项
        for (String taskNo : taskNoList) {
            List<MtcRepairItemCheckInfo> checkInfoList = tableRepairItemCheckInfoService.selectByTaskNo(taskNo);
            if (CollectionUtils.isEmpty(checkInfoList)) {
                continue;
            }
            
            // 过滤出有效且非预审的记录
            List<MtcRepairItemCheckInfo> filteredList = checkInfoList.stream()
                    .filter(item -> item.getStatus() != null && item.getStatus() == 1 && 
                            item.getInsurancePreReviewStatus() != null && item.getInsurancePreReviewStatus() == 0)
                    .collect(Collectors.toList());
            
            if (CollectionUtils.isEmpty(filteredList)) {
                continue;
            }
            
            // 转换为DTO
            for (MtcRepairItemCheckInfo checkInfo : filteredList) {
                RepairItemCheckInfoDTO dto = new RepairItemCheckInfoDTO();
                BeanUtils.copyProperties(checkInfo, dto);
                resultList.add(dto);
            }
        }
        
        return resultList;
    }

    @Override
    public List<RepairItemCheckInfoDTO> queryCheckListByTaskNoAndInsurancePreReviewStatus(String taskNo, Integer insurancePreReviewStatus) {
        log.info("查询配件列表, 任务编号: {}, 预审状态: {}", taskNo, insurancePreReviewStatus);
        
        // 查询维修项目核损信息列表
        List<MtcRepairItemCheckInfo> checkInfoList = tableRepairItemCheckInfoService.selectByTaskNo(taskNo);
        if (CollectionUtils.isEmpty(checkInfoList)) {
            return new ArrayList<>();
        }
        
        // 过滤出有效且符合预审状态的记录
        List<MtcRepairItemCheckInfo> filteredList = checkInfoList.stream()
                .filter(item -> item.getStatus() != null && item.getStatus() == 1 && 
                        item.getInsurancePreReviewStatus() != null && 
                        item.getInsurancePreReviewStatus().equals(insurancePreReviewStatus))
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(filteredList)) {
            return new ArrayList<>();
        }
        
        // 转换为DTO
        List<RepairItemCheckInfoDTO> resultList = new ArrayList<>();
        for (MtcRepairItemCheckInfo checkInfo : filteredList) {
            RepairItemCheckInfoDTO dto = new RepairItemCheckInfoDTO();
            BeanUtils.copyProperties(checkInfo, dto);
            resultList.add(dto);
        }
        
        return resultList;
    }
}
