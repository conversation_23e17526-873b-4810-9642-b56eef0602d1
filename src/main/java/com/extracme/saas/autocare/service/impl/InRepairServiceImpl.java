package com.extracme.saas.autocare.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.enums.RepairPicTypeEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.FileDTO;
import com.extracme.saas.autocare.model.dto.InRepairSubmitDTO;
import com.extracme.saas.autocare.model.dto.InRepairUpdateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.InRepairService;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 车辆维修活动节点服务实现类
 */
@Slf4j
@Service
public class InRepairServiceImpl implements InRepairService {

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private RepairTaskService repairTaskService;


    @Override
    public void saveVehicleInRepair(InRepairUpdateDTO updateDTO) {
        // 1. 查询维修任务
        MtcRepairTask repairTaskView = tableRepairTaskService.selectByTaskNo(updateDTO.getTaskNo());
        if (repairTaskView == null) {
            throw new BusinessException("未找到维修任务：" + updateDTO.getTaskNo());
        }

        // 2. 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(updateDTO.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例：" + updateDTO.getTaskNo());
        }

        // 3. 构建维修任务更新DTO
        RepairTaskUpdateDTO updateTaskDTO = new RepairTaskUpdateDTO();
        BeanUtils.copyProperties(updateDTO, updateTaskDTO);

        // 4. 检查任务是否超时
        String overTime = tableRepairTaskService.getOverTime(repairTaskView.getId());
        if (overTime.equals("0")) {
            if(null == updateDTO.getOverTimeReasons()) {
                throw new BusinessException("任务超时，超时原因不能为空");
            }
        }

        // 5. 保存维修任务
        repairTaskService.saveRepairTask(repairTaskView.getId(), updateTaskDTO);
        // 更新自费金额
        repairTaskService.updateSelfFundedAmount(repairTaskView.getTaskNo(), null);

        // 6. 处理图片和视频文件
        // 构建媒体类型映射
        Map<Integer, List<FileDTO>> mediaTypeMap = new HashMap<>();
        // 添加各种类型的图片到映射中
        if (updateDTO.getRepairPicture() != null) {
            mediaTypeMap.put(RepairPicTypeEnum.REPAIR_PICTURE.getTypeId(), updateDTO.getRepairPicture());
        }
        if (updateDTO.getCheckVideo() != null) {
            mediaTypeMap.put(RepairPicTypeEnum.CHECK_VIDEO.getTypeId(), updateDTO.getCheckVideo());
        }
        if (updateDTO.getCustPicture() != null) {
            mediaTypeMap.put(RepairPicTypeEnum.CUST_PICTURE.getTypeId(), updateDTO.getCustPicture());
        }
        // 如果有图片需要处理，则调用processMediaFiles方法
        if (!mediaTypeMap.isEmpty()) {
            repairTaskService.processMediaFiles(repairTaskView.getTaskNo(), mediaTypeMap, SessionUtils.getUsername());
            log.info("维修报价图片保存成功，任务编号: {}", repairTaskView.getTaskNo());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeVehicleCheck(InRepairSubmitDTO submitDTO) {
        log.info("车辆维修申请验收，参数: {}", submitDTO);
        try {
            // 1. 查询维修任务
            MtcRepairTask repairTaskView = tableRepairTaskService.selectByTaskNo(submitDTO.getTaskNo());
            if (repairTaskView == null) {
                throw new BusinessException("未找到维修任务：" + submitDTO.getTaskNo());
            }

            // 2. 获取工作流实例
            WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(submitDTO.getTaskNo());
            if (workflowInstance == null) {
                throw new BusinessException("未找到工作流实例：" + submitDTO.getTaskNo());
            }

            // 3. 保存维修任务
            InRepairUpdateDTO updateDTO = new InRepairUpdateDTO();
            BeanUtils.copyProperties(submitDTO, updateDTO);
            saveVehicleInRepair(updateDTO);

            // 4. 调用工作流服务处理节点
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("REQUEST_FOR_INSPECTION");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
            processDTO.setOperator(SessionUtils.getUsername());

            workflowService.processNode(workflowInstance.getId(), processDTO);

            log.info("车辆维修申请验收成功，任务编号: {}", submitDTO.getTaskNo());
        } catch (BusinessException e) {
            log.error("车辆维修申请验收失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("车辆维修申请验收失败: {}", e.getMessage(), e);
            throw new BusinessException("车辆维修申请验收失败: " + e.getMessage());
        }
    }
}
