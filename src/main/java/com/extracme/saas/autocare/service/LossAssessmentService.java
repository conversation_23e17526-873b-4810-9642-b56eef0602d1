package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.LossAssessmentUpdateDTO;
import com.extracme.saas.autocare.model.dto.LossAssessmentApproveDTO;
import com.extracme.saas.autocare.model.dto.LossAssessmentRejectDTO;

/**
 * 核损核价服务接口
 */
public interface LossAssessmentService {

    /**
     * 保存核损核价信息
     *
     * @param lossAssessmentDTO 核损核价信息
     */
    void saveLossAssessment(LossAssessmentUpdateDTO lossAssessmentDTO);

    /**
     * 核损核价-审核通过
     *
     * @param approveDTO 审核参数
     */
    void approveLossAssessment(LossAssessmentApproveDTO approveDTO);

    /**
     * 核损核价-平级移交
     *
     * @param taskNo 任务编号
     */
    void updateTransferTask(String taskNo);

    /**
     * 核损核价-清除任务占据人
     *
     * @param taskNo 任务编号
     */
    void clearOwner(String taskNo);

    /**
     * 核损核价-退回维修
     */
    void rejectLossAssessment(LossAssessmentRejectDTO rejectDTO);
}