package com.extracme.saas.autocare.service;

import java.util.List;

import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionCreateDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionDeleteDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionQueryDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowInstanceQueryDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowStartDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateCreateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateCopyTransitionsDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateQueryDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateUpdateDTO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowInstanceDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowInstanceVO;
import com.extracme.saas.autocare.model.vo.workflow.TaskProgressVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowProgressVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowTemplateDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowTemplateVO;

/**
 * 工作流服务接口
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
public interface WorkflowService {

    /**
     * 创建工作流模板
     *
     * @param createDTO 创建工作流模板请求对象
     * @return 创建成功的模板ID
     */
    Long createTemplate(WorkflowTemplateCreateDTO createDTO);

    /**
     * 更新工作流模板
     *
     * @param updateDTO 更新工作流模板请求对象
     */
    void updateTemplate(WorkflowTemplateUpdateDTO updateDTO);


    /**
     * 获取工作流模板详情
     *
     * @param templateId 模板ID
     * @return 工作流模板详情视图对象
     */
    WorkflowTemplateDetailVO getTemplateDetail(Long templateId);

    /**
     * 查询工作流模板列表
     *
     * @param queryDTO 查询条件对象
     * @return 工作流模板列表
     */
    List<WorkflowTemplateVO> listTemplates(WorkflowTemplateQueryDTO queryDTO);

    /**
     * 分页查询工作流模板列表
     *
     * @param queryDTO 查询条件对象
     * @return 工作流模板分页列表
     */
    BasePageVO<WorkflowTemplateVO> getTemplateList(WorkflowTemplateQueryDTO queryDTO);

    /**
     * 启动工作流实例
     *
     * @param startDTO 启动工作流实例请求对象
     * @return 创建的工作流实例ID
     */
    Long startWorkflow(WorkflowStartDTO startDTO);

    /**
     * 处理工作流节点
     *
     * @param instanceId 实例ID
     * @param processDTO 节点处理请求对象
     */
    void processNode(Long instanceId, WorkflowProcessDTO processDTO);

    /**
     * 获取工作流实例详情
     *
     * @param instanceId 实例ID
     * @return 工作流实例详情视图对象
     */
    WorkflowInstanceDetailVO getInstanceDetail(Long instanceId);

    /**
     * 查询工作流实例列表
     *
     * @param queryDTO 查询条件对象
     * @return 工作流实例列表
     */
    List<WorkflowInstanceVO> listInstances(WorkflowInstanceQueryDTO queryDTO);

    /**
     * 分页查询工作流实例列表
     *
     * @param queryDTO 查询条件对象
     * @return 工作流实例分页列表
     */
    BasePageVO<WorkflowInstanceVO> getInstanceList(WorkflowInstanceQueryDTO queryDTO);

    /**
     * 获取工作流实例进度
     *
     * @param instanceId 实例ID
     * @return 工作流进度视图对象
     */
    WorkflowProgressVO getInstanceProgress(Long instanceId);

    /**
     * 根据任务编号获取任务整体进度信息
     * 包含任务基本信息、流程模板活动节点列表、已执行活动实例详情
     *
     * @param businessId 任务编号（业务ID）
     * @return 任务进度视图对象
     */
    TaskProgressVO getTaskProgress(String businessId);

    /**
     * 更新工作流模板状态
     *
     * @param templateId 模板ID
     * @param isActive 是否启用（1:启用, 0:禁用）
     */
    void updateTemplateStatus(Long templateId, Integer isActive);

    /**
     * 查询工作流模板下拉框数据
     * 查询所有启用状态的工作流模板，返回下拉框数据
     * 根据用户权限处理租户数据访问（super admin可查看所有租户，普通用户只能查看自己租户的模板）
     *
     * @return 工作流模板下拉框数据列表
     */
    List<ComboVO<Long>> getTemplateCombo();

    /**
     * 复制工作流模板节点转换规则
     * 从指定的源模板复制所有活动节点转换规则到目标模板
     *
     * @param copyDTO 复制转换规则请求对象
     * @return 操作结果信息
     */
    String copyTemplateTransitions(WorkflowTemplateCopyTransitionsDTO copyDTO);

    /**
     * 创建活动节点转换规则
     *
     * @param createDTO 创建活动节点转换规则请求对象
     * @return 创建成功的活动节点转换规则ID
     */
    Long createActivityTransition(ActivityTransitionCreateDTO createDTO);

    /**
     * 更新活动节点转换规则
     *
     * @param updateDTO 更新活动节点转换规则请求对象
     */
    void updateActivityTransition(ActivityTransitionUpdateDTO updateDTO);

    /**
     * 删除活动节点转换规则
     *
     * @param deleteDTO 删除活动节点转换规则请求对象
     */
    void deleteActivityTransition(ActivityTransitionDeleteDTO deleteDTO);

    /**
     * 获取活动节点转换规则详情
     *
     * @param transitionId 转换规则ID
     * @return 活动节点转换规则详情视图对象
     */
    ActivityTransitionDetailVO getActivityTransitionDetail(Long transitionId);

    /**
     * 分页查询活动节点转换规则列表
     *
     * @param queryDTO 查询条件对象
     * @return 活动节点转换规则分页列表
     */
    BasePageVO<ActivityTransitionVO> getActivityTransitionList(ActivityTransitionQueryDTO queryDTO);

    /**
     * 根据工作流ID查询活动节点转换规则列表
     *
     * @param workflowId 工作流ID
     * @return 活动节点转换规则列表
     */
    List<ActivityTransitionVO> listActivityTransitionsByWorkflowId(Long workflowId);

    /**
     * 查找匹配的工作流模板
     *
     * @param taskType 任务类型
     * @param repairFactoryType 修理厂类型
     * @param subProductLine 子产品线
     * @return 匹配的工作流模板视图对象
     */
    WorkflowTemplateVO findMatchingTemplate(Integer taskType, Integer repairFactoryType, Integer subProductLine);
}