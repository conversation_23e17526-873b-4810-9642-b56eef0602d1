package com.extracme.saas.autocare.service.sync;

import com.extracme.saas.autocare.model.dto.SyncDataResultDTO;
import com.extracme.saas.autocare.model.dto.VehicleModelSyncRequestDTO;
import com.extracme.saas.autocare.model.entity.MtcVehicleModel;
import com.extracme.saas.autocare.repository.TableVehicleModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 车型信息同步策略
 */
@Slf4j
@Component
public class VehicleModelSyncStrategy implements SyncStrategy {

    private static final String TABLE_NAME = "mtc_vehicle_model";

    @Autowired
    private TableVehicleModelService tableVehicleModelService;

    @Override
    public String getSupportedTable() {
        return TABLE_NAME;
    }

    @Override
    public SyncDataResultDTO syncData(String dataIdentifier, Object dataObject, Long tenantId, String tenantCode) {

        log.debug("开始同步车型信息数据，数据标识：{}，租户：{}", dataIdentifier, tenantCode);

        try {
            // 租户上下文已在批量同步开始时设置，这里不再重复设置

            // 转换DTO为实体对象
            VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO modelDTO =
                (VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO) dataObject;

            // 强制要求传入ID
            if (modelDTO.getId() == null) {
                return SyncDataResultDTO.failure("车型ID不能为空，请提供有效的车型ID进行同步");
            }

            // 直接根据ID查询记录，简化判断逻辑
            MtcVehicleModel existingModel = tableVehicleModelService.selectById(modelDTO.getId());

            if (existingModel != null) {
                // 存在记录，执行更新操作
                log.debug("车型记录已存在，执行更新操作，ID：{}，车型名称：{}", modelDTO.getId(), modelDTO.getVehicleModelName());
                return handleUpdate(dataIdentifier, modelDTO, existingModel);
            } else {
                // 不存在记录，执行新增操作
                log.debug("车型记录不存在，执行新增操作，ID：{}，车型名称：{}", modelDTO.getId(), modelDTO.getVehicleModelName());
                return handleInsert(dataIdentifier, modelDTO);
            }

        } catch (Exception e) {
            log.error("同步车型信息数据失败：{}", e.getMessage(), e);
            return SyncDataResultDTO.failure("同步失败：" + e.getMessage());
        }
        // 移除 finally 块中的租户上下文清理，因为现在在批量同步结束时统一清理
    }

    @Override
    public SyncDataResultDTO syncData(String dataIdentifier, Object dataObject, Long tenantId, String tenantCode, String operator) {
        log.debug("开始同步车型信息数据（带操作人），数据标识：{}，租户：{}，操作人：{}", dataIdentifier, tenantCode, operator);

        try {
            // 转换DTO为实体对象
            VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO modelDTO =
                (VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO) dataObject;

            // 强制要求传入ID
            if (modelDTO.getId() == null) {
                return SyncDataResultDTO.failure("车型ID不能为空，请提供有效的车型ID进行同步");
            }

            // 直接根据ID查询记录，简化判断逻辑
            MtcVehicleModel existingModel = tableVehicleModelService.selectById(modelDTO.getId());

            if (existingModel != null) {
                // 存在记录，执行更新操作
                log.debug("车型记录已存在，执行更新操作，ID：{}，车型名称：{}，操作人：{}",
                         modelDTO.getId(), modelDTO.getVehicleModelName(), operator);
                return handleUpdateWithOperator(dataIdentifier, modelDTO, existingModel, operator);
            } else {
                // 不存在记录，执行新增操作
                log.debug("车型记录不存在，执行新增操作，ID：{}，车型名称：{}，操作人：{}",
                         modelDTO.getId(), modelDTO.getVehicleModelName(), operator);
                return handleInsertWithOperator(dataIdentifier, modelDTO, operator);
            }

        } catch (Exception e) {
            log.error("同步车型信息数据失败：{}", e.getMessage(), e);
            return SyncDataResultDTO.failure("同步失败：" + e.getMessage());
        }
    }

    /**
     * 将DTO转换为实体对象
     */
    private MtcVehicleModel convertDtoToEntity(VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO modelDTO) {
        MtcVehicleModel vehicleModel = new MtcVehicleModel();

        // 复制基本字段
        BeanUtils.copyProperties(modelDTO, vehicleModel);

        // 添加调试日志，确认ID是否正确复制
        log.debug("DTO转换为实体对象 - 原始DTO ID：{}，转换后实体ID：{}，车型名称：{}",
                 modelDTO.getId(), vehicleModel.getId(), vehicleModel.getVehicleModelName());

        return vehicleModel;
    }

    /**
     * 处理新增操作
     */
    private SyncDataResultDTO handleInsert(String dataIdentifier, VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO modelDTO) {
        try {
            MtcVehicleModel vehicleModel = convertDtoToEntity(modelDTO);

            // ID已通过BeanUtils.copyProperties复制，无需重复设置
            // 确保ID不为空
            if (vehicleModel.getId() == null) {
                log.error("车型ID为空，无法执行新增操作，数据：{}", modelDTO);
                return SyncDataResultDTO.failure("车型ID不能为空", SyncDataResultDTO.OperationType.INSERT);
            }

            // 插入数据，使用insertSelectiveWithId方法以支持ID字段
            MtcVehicleModel savedModel = tableVehicleModelService.insertSelectiveWithId(vehicleModel, "同步新增数据");

            log.debug("成功新增车型信息，ID：{}，名称：{}", savedModel.getId(), savedModel.getVehicleModelName());
            return SyncDataResultDTO.success(savedModel.getId(), SyncDataResultDTO.OperationType.INSERT);

        } catch (Exception e) {
            log.error("新增车型信息失败，ID：{}，错误：{}", modelDTO.getId(), e.getMessage());
            return SyncDataResultDTO.failure("新增失败：" + e.getMessage(), SyncDataResultDTO.OperationType.INSERT);
        }
    }

    /**
     * 处理新增操作（带操作人参数）
     */
    private SyncDataResultDTO handleInsertWithOperator(String dataIdentifier, VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO modelDTO, String operator) {
        try {
            MtcVehicleModel vehicleModel = convertDtoToEntity(modelDTO);

            // ID已通过BeanUtils.copyProperties复制，无需重复设置
            // 确保ID不为空
            if (vehicleModel.getId() == null) {
                log.error("车型ID为空，无法执行新增操作，数据：{}，操作人：{}", modelDTO, operator);
                return SyncDataResultDTO.failure("车型ID不能为空", SyncDataResultDTO.OperationType.INSERT);
            }

            // 插入数据，明确传入操作人，使用insertSelectiveWithId方法以支持ID字段
            MtcVehicleModel savedModel = tableVehicleModelService.insertSelectiveWithId(vehicleModel, operator);

            log.debug("成功新增车型信息，ID：{}，名称：{}，操作人：{}", savedModel.getId(), savedModel.getVehicleModelName(), operator);
            return SyncDataResultDTO.success(savedModel.getId(), SyncDataResultDTO.OperationType.INSERT);

        } catch (Exception e) {
            log.error("新增车型信息失败，ID：{}，操作人：{}，错误：{}", modelDTO.getId(), operator, e.getMessage());
            return SyncDataResultDTO.failure("新增失败：" + e.getMessage(), SyncDataResultDTO.OperationType.INSERT);
        }
    }

    /**
     * 处理更新操作
     */
    private SyncDataResultDTO handleUpdate(String dataIdentifier, VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO modelDTO,
                                          MtcVehicleModel existingModel) {
        try {
            // 转换DTO为实体对象
            MtcVehicleModel vehicleModel = convertDtoToEntity(modelDTO);

            // 更新操作必须使用现有记录的ID，而不是传入的ID
            // 这样可以确保更新的是正确的记录
            vehicleModel.setId(existingModel.getId());
            tableVehicleModelService.updateSelectiveById(vehicleModel);

            log.debug("成功更新车型信息，ID：{}，名称：{}", vehicleModel.getId(), vehicleModel.getVehicleModelName());
            return SyncDataResultDTO.success(vehicleModel.getId(), SyncDataResultDTO.OperationType.UPDATE);

        } catch (Exception e) {
            log.error("更新车型信息失败，ID：{}，错误：{}", modelDTO.getId(), e.getMessage());
            return SyncDataResultDTO.failure("更新失败：" + e.getMessage(), SyncDataResultDTO.OperationType.UPDATE);
        }
    }

    /**
     * 处理更新操作（带操作人参数）
     */
    private SyncDataResultDTO handleUpdateWithOperator(String dataIdentifier, VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO modelDTO,
                                                      MtcVehicleModel existingModel, String operator) {
        try {
            // 转换DTO为实体对象
            MtcVehicleModel vehicleModel = convertDtoToEntity(modelDTO);

            // 更新操作必须使用现有记录的ID，而不是传入的ID
            // 这样可以确保更新的是正确的记录
            vehicleModel.setId(existingModel.getId());
            tableVehicleModelService.updateSelectiveById(vehicleModel, operator);

            log.debug("成功更新车型信息，ID：{}，名称：{}，操作人：{}", vehicleModel.getId(), vehicleModel.getVehicleModelName(), operator);
            return SyncDataResultDTO.success(vehicleModel.getId(), SyncDataResultDTO.OperationType.UPDATE);

        } catch (Exception e) {
            log.error("更新车型信息失败，ID：{}，操作人：{}，错误：{}", modelDTO.getId(), operator, e.getMessage());
            return SyncDataResultDTO.failure("更新失败：" + e.getMessage(), SyncDataResultDTO.OperationType.UPDATE);
        }
    }


}
