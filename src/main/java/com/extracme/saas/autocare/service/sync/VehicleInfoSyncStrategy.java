package com.extracme.saas.autocare.service.sync;

import com.extracme.saas.autocare.model.dto.SyncDataResultDTO;
import com.extracme.saas.autocare.model.dto.VehicleInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;
import com.extracme.saas.autocare.repository.TableVehicleInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 车辆信息同步策略
 */
@Slf4j
@Component
public class VehicleInfoSyncStrategy implements SyncStrategy {

    private static final String TABLE_NAME = "mtc_vehicle_info";

    @Autowired
    private TableVehicleInfoService tableVehicleInfoService;

    @Override
    public String getSupportedTable() {
        return TABLE_NAME;
    }

    @Override
    public SyncDataResultDTO syncData(String dataIdentifier, Object dataObject, Long tenantId, String tenantCode) {

        log.debug("开始同步车辆信息数据，数据标识：{}，租户：{}", dataIdentifier, tenantCode);

        try {
            // 租户上下文已在批量同步开始时设置，这里不再重复设置

            // 转换DTO为实体对象
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO vehicleDTO =
                (VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO) dataObject;

            // 强制要求传入ID
            if (vehicleDTO.getId() == null) {
                return SyncDataResultDTO.failure("车辆ID不能为空，请提供有效的车辆ID进行同步");
            }

            // 直接根据ID查询记录，简化判断逻辑
            MtcVehicleInfo existingVehicle = tableVehicleInfoService.selectById(vehicleDTO.getId());

            if (existingVehicle != null) {
                // 存在记录，执行更新操作
                log.debug("车辆记录已存在，执行更新操作，ID：{}，车架号：{}", vehicleDTO.getId(), vehicleDTO.getVin());
                return handleUpdate(dataIdentifier, vehicleDTO, existingVehicle);
            } else {
                // 不存在记录，执行新增操作
                log.debug("车辆记录不存在，执行新增操作，ID：{}，车架号：{}", vehicleDTO.getId(), vehicleDTO.getVin());
                return handleInsert(dataIdentifier, vehicleDTO);
            }

        } catch (Exception e) {
            log.error("同步车辆信息数据失败：{}", e.getMessage(), e);
            return SyncDataResultDTO.failure("同步失败：" + e.getMessage());
        }
        // 移除 finally 块中的租户上下文清理，因为现在在批量同步结束时统一清理
    }

    @Override
    public SyncDataResultDTO syncData(String dataIdentifier, Object dataObject, Long tenantId, String tenantCode, String operator) {
        log.debug("开始同步车辆信息数据（带操作人），数据标识：{}，租户：{}，操作人：{}", dataIdentifier, tenantCode, operator);

        try {
            // 转换DTO为实体对象
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO vehicleDTO =
                (VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO) dataObject;

            // 强制要求传入ID
            if (vehicleDTO.getId() == null) {
                return SyncDataResultDTO.failure("车辆ID不能为空，请提供有效的车辆ID进行同步");
            }

            // 直接根据ID查询记录，简化判断逻辑
            MtcVehicleInfo existingVehicle = tableVehicleInfoService.selectById(vehicleDTO.getId());

            if (existingVehicle != null) {
                // 存在记录，执行更新操作
                log.debug("车辆记录已存在，执行更新操作，ID：{}，车架号：{}，操作人：{}",
                         vehicleDTO.getId(), vehicleDTO.getVin(), operator);
                return handleUpdateWithOperator(dataIdentifier, vehicleDTO, existingVehicle, operator);
            } else {
                // 不存在记录，执行新增操作
                log.debug("车辆记录不存在，执行新增操作，ID：{}，车架号：{}，操作人：{}",
                         vehicleDTO.getId(), vehicleDTO.getVin(), operator);
                return handleInsertWithOperator(dataIdentifier, vehicleDTO, operator);
            }

        } catch (Exception e) {
            log.error("同步车辆信息数据失败：{}", e.getMessage(), e);
            return SyncDataResultDTO.failure("同步失败：" + e.getMessage());
        }
    }

    /**
     * 将DTO转换为实体对象
     */
    private MtcVehicleInfo convertDtoToEntity(VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO vehicleDTO) {
        MtcVehicleInfo vehicleInfo = new MtcVehicleInfo();

        // 复制基本字段
        BeanUtils.copyProperties(vehicleDTO, vehicleInfo);

        // 添加调试日志，确认ID是否正确复制
        log.debug("DTO转换为实体对象 - 原始DTO ID：{}，转换后实体ID：{}，车架号：{}",
                 vehicleDTO.getId(), vehicleInfo.getId(), vehicleInfo.getVin());

        return vehicleInfo;
    }

    /**
     * 处理新增操作
     */
    private SyncDataResultDTO handleInsert(String dataIdentifier, VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO vehicleDTO) {
        try {
            MtcVehicleInfo vehicleInfo = convertDtoToEntity(vehicleDTO);

            // ID已通过BeanUtils.copyProperties复制，无需重复设置
            // 确保ID不为空
            if (vehicleInfo.getId() == null) {
                log.error("车辆ID为空，无法执行新增操作，数据：{}", vehicleDTO);
                return SyncDataResultDTO.failure("车辆ID不能为空", SyncDataResultDTO.OperationType.INSERT);
            }

            // 插入数据
            MtcVehicleInfo savedVehicle = tableVehicleInfoService.insertSelectiveWithId(vehicleInfo, "同步新增数据");

            log.debug("成功新增车辆信息，ID：{}，车架号：{}", savedVehicle.getId(), savedVehicle.getVin());
            return SyncDataResultDTO.success(savedVehicle.getId(), SyncDataResultDTO.OperationType.INSERT);

        } catch (Exception e) {
            log.error("新增车辆信息失败，ID：{}，错误：{}", vehicleDTO.getId(), e.getMessage());
            return SyncDataResultDTO.failure("新增失败：" + e.getMessage(), SyncDataResultDTO.OperationType.INSERT);
        }
    }

    /**
     * 处理新增操作（带操作人参数）
     */
    private SyncDataResultDTO handleInsertWithOperator(String dataIdentifier, VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO vehicleDTO, String operator) {
        try {
            MtcVehicleInfo vehicleInfo = convertDtoToEntity(vehicleDTO);

            // ID已通过BeanUtils.copyProperties复制，无需重复设置
            // 确保ID不为空
            if (vehicleInfo.getId() == null) {
                log.error("车辆ID为空，无法执行新增操作，数据：{}，操作人：{}", vehicleDTO, operator);
                return SyncDataResultDTO.failure("车辆ID不能为空", SyncDataResultDTO.OperationType.INSERT);
            }

            // 插入数据，明确传入操作人
            MtcVehicleInfo savedVehicle = tableVehicleInfoService.insertSelectiveWithId(vehicleInfo, operator);

            log.debug("成功新增车辆信息，ID：{}，车架号：{}，操作人：{}", savedVehicle.getId(), savedVehicle.getVin(), operator);
            return SyncDataResultDTO.success(savedVehicle.getId(), SyncDataResultDTO.OperationType.INSERT);

        } catch (Exception e) {
            log.error("新增车辆信息失败，ID：{}，操作人：{}，错误：{}", vehicleDTO.getId(), operator, e.getMessage());
            return SyncDataResultDTO.failure("新增失败：" + e.getMessage(), SyncDataResultDTO.OperationType.INSERT);
        }
    }

    /**
     * 处理更新操作
     */
    private SyncDataResultDTO handleUpdate(String dataIdentifier, VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO vehicleDTO,
                                          MtcVehicleInfo existingVehicle) {
        try {
            // 转换DTO为实体对象
            MtcVehicleInfo vehicleInfo = convertDtoToEntity(vehicleDTO);

            // 更新数据（保持原有ID）
            vehicleInfo.setId(existingVehicle.getId());
            tableVehicleInfoService.updateSelectiveById(vehicleInfo, "同步更新数据");

            log.debug("成功更新车辆信息，ID：{}，车架号：{}", vehicleInfo.getId(), vehicleInfo.getVin());
            return SyncDataResultDTO.success(vehicleInfo.getId(), SyncDataResultDTO.OperationType.UPDATE);

        } catch (Exception e) {
            log.error("更新车辆信息失败，ID：{}，错误：{}", vehicleDTO.getId(), e.getMessage());
            return SyncDataResultDTO.failure("更新失败：" + e.getMessage(), SyncDataResultDTO.OperationType.UPDATE);
        }
    }

    /**
     * 处理更新操作（带操作人参数）
     */
    private SyncDataResultDTO handleUpdateWithOperator(String dataIdentifier, VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO vehicleDTO,
                                                      MtcVehicleInfo existingVehicle, String operator) {
        try {
            // 转换DTO为实体对象
            MtcVehicleInfo vehicleInfo = convertDtoToEntity(vehicleDTO);

            // 更新数据（保持原有ID），明确传入操作人
            vehicleInfo.setId(existingVehicle.getId());
            tableVehicleInfoService.updateSelectiveById(vehicleInfo, operator);

            log.debug("成功更新车辆信息，ID：{}，车架号：{}，操作人：{}", vehicleInfo.getId(), vehicleInfo.getVin(), operator);
            return SyncDataResultDTO.success(vehicleInfo.getId(), SyncDataResultDTO.OperationType.UPDATE);

        } catch (Exception e) {
            log.error("更新车辆信息失败，ID：{}，操作人：{}，错误：{}", vehicleDTO.getId(), operator, e.getMessage());
            return SyncDataResultDTO.failure("更新失败：" + e.getMessage(), SyncDataResultDTO.OperationType.UPDATE);
        }
    }


}
