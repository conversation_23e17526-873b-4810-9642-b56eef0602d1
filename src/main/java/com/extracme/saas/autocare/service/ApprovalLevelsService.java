package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.ApprovalLevelsCreateDTO;
import com.extracme.saas.autocare.model.dto.ApprovalLevelsUpdateDTO;
import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.extracme.saas.autocare.model.vo.ApprovalLevelsVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

import java.util.List;

/**
 * 审批层级服务接口
 */
public interface ApprovalLevelsService {
    
    /**
     * 创建审批层级
     * 
     * @param createDTO 创建DTO
     */
    void createApprovalLevels(ApprovalLevelsCreateDTO createDTO);
    
    /**
     * 更新审批层级
     * 
     * @param updateDTO 更新DTO
     */
    void updateApprovalLevels(ApprovalLevelsUpdateDTO updateDTO);
    
    /**
     * 删除审批层级
     * 
     * @param id 审批层级ID
     */
    void deleteApprovalLevels(Long id);
    
    /**
     * 分页查询审批层级列表
     * 
     * @param pageDTO 分页参数
     * @return 分页结果
     */
    BasePageVO<ApprovalLevelsVO> getApprovalLevelsList(BasePageDTO pageDTO);
    
    /**
     * 根据租户ID查询审批层级下拉框选项
     * 
     * @param tenantId 租户ID
     * @return 下拉框选项列表
     */
    List<ComboVO<Integer>> getApprovalLevelsCombo(Long tenantId);
    
    /**
     * 根据审批层级级别查询审批层级信息
     * 
     * @param approvalLevel 审批层级级别
     * @return 审批层级信息
     */
    ApprovalLevelsVO getApprovalLevelsByLevel(Integer approvalLevel);
}
