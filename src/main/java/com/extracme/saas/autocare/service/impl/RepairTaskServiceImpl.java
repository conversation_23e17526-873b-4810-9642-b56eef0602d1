package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.config.OssConfig;
import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.*;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.*;
import com.extracme.saas.autocare.model.dto.repairTask.*;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowStartDTO;
import com.extracme.saas.autocare.model.entity.*;
import com.extracme.saas.autocare.model.excel.RepairDepotFirstPageExportExcel;
import com.extracme.saas.autocare.model.excel.RepairTaskProcessExportExcel;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.*;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.*;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.UserService;
import com.extracme.saas.autocare.service.VehicleInfoService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.ExcelExportUtil;
import com.extracme.saas.autocare.util.RepairPicUtil;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.util.SpringContextUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维修任务服务实现类
 */
@Slf4j
@Service
public class RepairTaskServiceImpl implements RepairTaskService {

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableVehicleRepairPicService tableVehicleRepairPicService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableWorkflowTemplateService tableWorkflowTemplateService;

    @Autowired
    private TableActivityDefinitionService tableActivityDefinitionService;

    @Autowired
    private TableActivityStatusService tableActivityStatusService;

    @Autowired
    private TableRepairDepotInfoService tableRepairDepotInfoService;

    @Autowired
    private VehicleInfoService vehicleInfoService;

    @Autowired
    private TableVehicleInfoService tableVehicleInfoService;

    @Autowired
    private TableRepairRemarkService tableRepairRemarkService;

    @Autowired
    private TableRepairTaskLeavingFactoryService tableRepairTaskLeavingFactoryService;

    @Autowired
    private TableLossFitInfoService tableLossFitInfoService;

    @Autowired
    private TableLossRepairInfoService tableLossRepairInfoService;

    @Autowired
    private TableRepairItemCheckInfoService tableRepairItemCheckInfoService;

    @Autowired
    private UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRepairTask(RepairTaskCreateDTO createDTO) {
        log.info("创建维修任务，参数: {}", createDTO);
        try {
            // 1. 参数校验
            validateRepairTaskParams(createDTO);

            // 2. 创建维修任务
            MtcRepairTask newRepairTask = new MtcRepairTask();

            // 3. 从车辆表获取车辆信息
            VehicleInfoVO vehicleInfoVo = enrichVehicleInfo(newRepairTask, createDTO.getVin(),
                    createDTO.getVehicleNo());

            // 4. 从修理厂表获取修理厂信息
            enrichRepairDepotInfo(newRepairTask, createDTO.getRepairDepotId());

            // 5. 复制DTO中的字段到实体类
            BeanUtils.copyProperties(createDTO, newRepairTask);

            // 6. 设置维修类型名称
            setRepairTypeName(newRepairTask);

            // 7. 生成任务编号 根据维修类型生成不同前缀的任务编号
            String prefix = getTaskNoPrefix(createDTO.getRepairTypeId());
            newRepairTask.setTaskNo(prefix + System.currentTimeMillis());
            // 设置任务类型为4（saas维修任务）
            newRepairTask.setTaskType((short) 4);

            // 8. 设置任务创建时间
            newRepairTask.setTaskCreateTime(new Date());
            newRepairTask.setTaskInflowTime(new Date());

            // 9. 保存维修任务
            tableRepairTaskService.insert(newRepairTask, SessionUtils.getUsername());
            Long repairTaskId = newRepairTask.getId();

            // 10. 处理维修图片信息
            Map<Integer, List<FileDTO>> mediaTypeMap = new HashMap<>();
            mediaTypeMap.put(RepairPicTypeEnum.CREATE_PICTURE.getTypeId(), createDTO.getCreatePicList()); // 创建图片
            mediaTypeMap.put(RepairPicTypeEnum.CREATE_VIDEO.getTypeId(), createDTO.getCreateVideoList()); // 创建视频
            processMediaFiles(newRepairTask.getTaskNo(), mediaTypeMap, SessionUtils.getUsername());

            // 11. 记录操作日志
            saveOperationLogs(newRepairTask);

            // 12. 更新车辆里程数
            MtcVehicleInfo updatVehicleInfo = new MtcVehicleInfo();
            updatVehicleInfo.setId(vehicleInfoVo.getId());
            updatVehicleInfo.setTotalMileage(newRepairTask.getTotalMileage().intValue());
            tableVehicleInfoService.updateSelectiveById(updatVehicleInfo);

            // 13. 启动工作流 - 自动匹配合适的工作流模板
            startWorkflowForRepairTask(newRepairTask);

            log.info("维修任务创建成功，ID: {}", repairTaskId);
            return repairTaskId;
        } catch (RuntimeException e) {
            log.error("创建维修任务失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建维修任务失败", e);
            throw new RuntimeException("创建维修任务失败: " + e.getMessage());
        }
    }

    /**
     * 从车辆表获取车辆信息并填充到维修任务中
     *
     * @param repairTask 维修任务
     * @param vin        车架号
     * @param vehicleNo  车牌号
     */
    private VehicleInfoVO enrichVehicleInfo(MtcRepairTask repairTask, String vin, String vehicleNo) {
        // 根据车架号获取车辆信息
        VehicleInfoVO vehicleInfo = vehicleInfoService.getVehicleInfoByVin(vin);
        if (vehicleInfo == null) {
            throw new BusinessException("未找到对应的车辆信息");
        }
        repairTask.setVehicleModelSeq(vehicleInfo.getVehicleModelId());
        repairTask.setVehicleModelInfo(vehicleInfo.getVehicleModelName());
        repairTask.setVehicleOperateOrgId(vehicleInfo.getOperationOrgId());
        repairTask.setVehicleOperateOrgName(vehicleInfo.getOperationOrgName());
        repairTask.setVehicleOrgId(vehicleInfo.getVehicleOrgId());
        repairTask.setVehicleOrgName(vehicleInfo.getVehicleOrgName());
        repairTask.setProductLine(vehicleInfo.getProductLine());
        repairTask.setSubProductLine(vehicleInfo.getSubProductLine());
        repairTask.setFactOperateTag(vehicleInfo.getFactOperateTag());
        repairTask.setPropertyStatus(vehicleInfo.getPropertyStatus());
        repairTask.setTotalMileage(
                null != vehicleInfo.getTotalMileage() ? BigDecimal.valueOf(vehicleInfo.getTotalMileage()) : null);
        // repairTask.setInsuranceCompanyName("不知从何而来");
        return vehicleInfo;
    }

    /**
     * 从修理厂表获取修理厂信息并填充到维修任务中
     *
     * @param repairTask    维修任务
     * @param repairDepotId 修理厂ID
     */
    private void enrichRepairDepotInfo(MtcRepairTask repairTask, String repairDepotId) {
        // 根据修理厂ID获取修理厂信息
        MtcRepairDepotInfo repairDepotInfo = tableRepairDepotInfoService.selectByRepairDepotCode(repairDepotId);
        if (repairDepotInfo == null) {
            throw new BusinessException("未找到对应的修理厂");
        }

        try {
            BeanUtils.copyProperties(repairDepotInfo, repairTask);
            repairTask.setRepairGrade(repairDepotInfo.getRepairDepotGrade());
        } catch (Exception e) {
            log.warn("获取修理厂信息失败: {}", e.getMessage());
        }
    }

    /**
     * 设置维修类型名称
     *
     * @param repairTask 维修任务
     */
    private void setRepairTypeName(MtcRepairTask repairTask) {
        // 根据维修类型ID设置维修类型名称
        Integer repairTypeId = repairTask.getRepairTypeId();
        repairTask.setRepairTypeName(RepairTypeEnum.getNameByCode(repairTypeId));
    }

    /**
     * 验证维修任务参数
     *
     * @param createDTO 创建参数
     */
    private void validateRepairTaskParams(RepairTaskCreateDTO createDTO) {
        if (createDTO == null) {
            throw new RuntimeException("创建参数不能为空");
        }

        if (StringUtils.isBlank(createDTO.getVin())) {
            throw new RuntimeException("车架号不能为空");
        }

        if (StringUtils.isBlank(createDTO.getVehicleNo())) {
            throw new RuntimeException("车牌号不能为空");
        }

        if (createDTO.getRepairTypeId() == null) {
            throw new RuntimeException("维修类型不能为空");
        }
    }

    /**
     * 根据维修类型和租户ID获取任务编号前缀
     *
     * @param repairTypeId 维修类型ID
     * @return 任务编号前缀
     */
    private String getTaskNoPrefix(Integer repairTypeId) {
        // 获取当前租户ID
        String tenantCode = SessionUtils.getTenantCode();

        // 获取基础维修类型前缀
        String basePrefix = RepairTypeEnum.getTaskNoPrefix(repairTypeId);

        // 如果租户ID为空，使用默认前缀
        if (StringUtils.isBlank(tenantCode)) {
            log.warn("当前租户ID为空，使用默认前缀");
            return basePrefix;
        }

        // 组合前缀：租户特定前缀 + 基础维修类型前缀
        return tenantCode + basePrefix;
    }

    /**
     * 根据活动编码设置活动名称
     *
     * @param item                维修任务流程列表视图对象
     * @param activityDefinitions 活动定义列表
     */
    private void setActivityNameByCode(RepairTaskProcessListVO item, List<ActivityDefinition> activityDefinitions) {
        if (item == null || item.getCurrentActivityCode() == null || activityDefinitions == null) {
            return;
        }

        // 根据活动编码查找对应的活动定义
        activityDefinitions.stream()
                .filter(definition -> item.getCurrentActivityCode().equals(definition.getActivityCode()))
                .findFirst()
                .ifPresent(definition -> item.setCurrentActivityName(definition.getActivityName()));
    }

    /**
     * 根据状态编码设置状态名称
     *
     * @param item             维修任务流程列表视图对象
     * @param activityStatuses 活动状态列表
     */
    private void setStatusNameByCode(RepairTaskProcessListVO item, List<ActivityStatus> activityStatuses) {
        if (item == null || item.getStatusCode() == null || activityStatuses == null) {
            return;
        }

        // 根据状态编码查找对应的状态定义
        activityStatuses.stream()
                .filter(status -> item.getStatusCode().equals(status.getStatusCode()))
                .findFirst()
                .ifPresent(status -> item.setStatusName(status.getStatusName()));
    }

    /**
     * 为维修任务启动工作流
     *
     * @param repairTask 维修任务
     */
    private void startWorkflowForRepairTask(MtcRepairTask repairTask) {
        try {
            // 获取当前登录用户信息
            LoginUser loginUser = SessionUtils.getLoginUser();
            String username = SessionUtils.getUsername();
            Long tenantId = SessionUtils.getTenantId();

            log.info("启动工作流 - 当前用户: {}, 租户ID: {}", username, tenantId);

            // 构建工作流启动参数
            WorkflowStartDTO startDTO = new WorkflowStartDTO();
            startDTO.setBusinessId(repairTask.getTaskNo());
            startDTO.setTaskType(repairTask.getRepairTypeId());

            // 设置修理厂类型
            // 根据修理厂ID判断是合作还是非合作修理厂
            Integer repairFactoryType = determineRepairFactoryType(repairTask.getRepairDepotId());
            startDTO.setRepairFactoryType(repairFactoryType);

            // 设置子产品线
            // 根据车辆业务状态或产品线确定子产品线
            Integer subProductLine = determineSubProductLine(repairTask.getSubProductLine());
            startDTO.setSubProductLine(subProductLine);

            // 设置操作人
            startDTO.setOperator(username);
            startDTO.setRemarks("创建维修任务自动启动工作流");

            // 如果用户有特定的配置信息，可以在这里设置
            if (loginUser != null && loginUser.getUser() != null) {
                // 可以根据用户的角色或权限设置不同的工作流参数
                // 例如：根据用户所属部门设置不同的审批流程

                // 获取用户权限信息
                Set<String> permissions = loginUser.getPermissions();
                if (permissions != null && !permissions.isEmpty()) {
                    log.info("用户权限信息: {}", permissions);
                }

                // 这里可以添加基于用户配置的自定义逻辑
                // 例如：根据用户权限设置不同的工作流参数
            }

            // 启动工作流 - WorkflowService.startWorkflow 方法会自动匹配合适的工作流模板
            Long workflowInstanceId = workflowService.startWorkflow(startDTO);
            log.info("为维修任务[{}]启动工作流成功，工作流实例ID: {}, 租户ID: {}",
                    repairTask.getTaskNo(), workflowInstanceId, tenantId);

            // 获取工作流实例信息
            WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectById(workflowInstanceId);
            // 获取工作流模板信息
            WorkflowTemplate workflowTemplate = tableWorkflowTemplateService
                    .selectById(workflowInstance.getWorkflowId());

            // 更新维修任务的配件库类型
            MtcRepairTask updateRepairTask = new MtcRepairTask();
            updateRepairTask.setId(repairTask.getId());
            updateRepairTask.setPartsLibraryType(workflowTemplate.getPartsLibraryType());
            tableRepairTaskService.updateSelectiveById(updateRepairTask);
        } catch (Exception e) {
            log.error("启动工作流失败: {}", e.getMessage(), e);
            throw new RuntimeException("启动工作流失败: " + e.getMessage());
        }
    }

    /**
     * 确定修理厂类型
     *
     * @param repairDepotId 修理厂ID
     * @return 修理厂类型（1:合作, 2:非合作）
     */
    private Integer determineRepairFactoryType(String repairDepotId) {
        // 根据修理厂ID查询修理厂信息，确定是合作还是非合作修理厂
        MtcRepairDepotInfo mtcRepairDepotInfo = tableRepairDepotInfoService.selectByRepairDepotCode(repairDepotId);
        return mtcRepairDepotInfo.getRepairDepotType();
    }

    /**
     * 确定子产品线
     *
     * @param subProductLine 子产品线
     * @return 子产品线
     */
    private Integer determineSubProductLine(Integer subProductLine) {
        return subProductLine;
    }

    /**
     * 保存操作日志
     *
     * @param repairTask 维修任务
     */
    private void saveOperationLogs(MtcRepairTask repairTask) {
        // 新增任务日志
        MtcOperatorLog taskLog = new MtcOperatorLog();
        taskLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
        taskLog.setRecordId(repairTask.getId());
        taskLog.setOpeContent("新增维修任务");
        taskLog.setRemark(StringUtils.EMPTY);
        tableOperatorLogService.insertSelective(taskLog);

        // 流程日志
        MtcOperatorLog processLog = new MtcOperatorLog();
        processLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
        processLog.setRecordId(repairTask.getId());
        processLog.setOpeContent("待处理");
        processLog.setRemark("车辆交接");
        processLog.setCurrentActivityCode(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode());
        processLog.setCreateBy(
                StringUtils.isNotBlank(repairTask.getRepairDepotName()) ? repairTask.getRepairDepotName()
                        : SessionUtils.getNickname());
        tableOperatorLogService.insertSelective(processLog);
    }

    @Override
    public BasePageVO<RepairTaskProcessListVO> queryRepairTaskProcessList(RepairTaskProcessQueryDTO queryDTO) {
        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            queryDTO.setRepairDepotId(repairDepotId);
        }
        // 登录人公司
        else {
            queryDTO.setLoginOrgIds(SessionUtils.getAllAccessibleOrgIds());
        }

        // 使用PageHelper进行分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 查询数据
        List<RepairTaskProcessListVO> list = tableRepairTaskService.queryProcessList(queryDTO);
        if (CollectionUtils.isNotEmpty(list)) {
            // 查询所有活动节点定义
            List<ActivityDefinition> activityDefinitions = tableActivityDefinitionService.findAll();
            // 查询所有活动状态定义
            List<ActivityStatus> activityStatuses = tableActivityStatusService.findAll();
            list.forEach(
                    item -> {
                        // 设置活动节点名称
                        setActivityNameByCode(item, activityDefinitions);
                        // 设置活动状态名称
                        setStatusNameByCode(item, activityStatuses);
                    });
        }
        PageInfo<RepairTaskProcessListVO> pageInfo = new PageInfo<>(list);

        // 构建返回结果
        return BasePageVO.of(list, pageInfo);
    }

    @Override
    public void exportRepairTaskProcessList(RepairTaskProcessQueryDTO queryDTO, HttpServletRequest request,
            HttpServletResponse response) {
        log.info("开始使用游标分页导出流程查询数据");

        try {
            // 登录人修理厂
            String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
            if (StringUtils.isNotBlank(repairDepotId)) {
                queryDTO.setRepairDepotId(repairDepotId);
            }
            // 登录人公司
            else {
                queryDTO.setLoginOrgIds(SessionUtils.getAllAccessibleOrgIds());
            }

            // 预先查询所有活动节点定义和活动状态定义，避免在每批次数据处理时重复查询
            List<ActivityDefinition> activityDefinitions = tableActivityDefinitionService.findAll();
            List<ActivityStatus> activityStatuses = tableActivityStatusService.findAll();

            // 创建导出配置，使用游标分页
            String fileName = "流程查询数据_" + System.currentTimeMillis() + ".xlsx";
            ExcelExportUtil.ExportConfig<RepairTaskProcessExportExcel> config = ExcelExportUtil
                    .builder(RepairTaskProcessExportExcel.class)
                    .fileName(fileName)
                    .sheetName("流程查询")
                    .cursorDataProvider(
                            lastId -> getExportDataWithCursor(queryDTO, lastId, activityDefinitions, activityStatuses))
                    .batchSize(1000)
                    .progressCallback(
                            (current, total) -> log.debug("游标分页导出进度: {}/{}", current, total > 0 ? total : "未知"))
                    .build();

            // 执行分批导出
            ExcelExportUtil.exportToResponse(config, response);

            log.info("游标分页导出维修项目库数据完成");
        } catch (Exception e) {
            log.error("导出维修项目库数据失败: {}", e.getMessage(), e);
            throw new BusinessException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取导出数据（游标分页）
     * 使用基于ID的游标分页，避免深分页性能问题
     *
     * @param queryDTO            查询条件
     * @param lastId              上一批次最后一条记录的ID（首次查询传入0）
     * @param activityDefinitions 活动定义列表（预先查询，避免重复查询）
     * @param activityStatuses    活动状态列表（预先查询，避免重复查询）
     * @return 导出数据列表
     */
    private List<RepairTaskProcessExportExcel> getExportDataWithCursor(
            RepairTaskProcessQueryDTO queryDTO,
            Long lastId,
            List<ActivityDefinition> activityDefinitions,
            List<ActivityStatus> activityStatuses) {
        log.debug("获取导出数据（游标分页），lastId: {}", lastId);

        // 使用支持动态页大小的exportListWithPageSize方法，页大小固定为1000
        List<RepairTaskProcessListVO> dataList = tableRepairTaskService.exportProcessListWithPageSize(lastId, queryDTO,
                1000);
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(
                    item -> {
                        // 设置活动节点名称
                        setActivityNameByCode(item, activityDefinitions);
                        // 设置活动状态名称
                        setStatusNameByCode(item, activityStatuses);
                    });
        }

        // 转换为导出Excel实体
        List<RepairTaskProcessExportExcel> exportList = dataList.stream()
                .map(this::convertToExportExcel)
                .collect(Collectors.toList());

        return exportList;
    }

    /**
     * 转换为导出Excel实体
     * 使用Optional简化数据转换逻辑，避免冗长的三元表达式
     *
     * @param repairTaskProcessListVO 数据库实体
     * @return 导出Excel实体
     */
    private RepairTaskProcessExportExcel convertToExportExcel(RepairTaskProcessListVO repairTaskProcessListVO) {
        RepairTaskProcessExportExcel exportExcel = new RepairTaskProcessExportExcel();

        // 使用BeanUtils进行属性拷贝
        BeanUtils.copyProperties(repairTaskProcessListVO, exportExcel);

        exportExcel.setRepairDepotTypeString(Optional.ofNullable(repairTaskProcessListVO.getRepairDepotType())
                .map(RepairDepotTypeEnum::getDescriptionByCode)
                .orElse(""));
        exportExcel.setTaskCreateTime(
                DateFormatUtils.format(repairTaskProcessListVO.getTaskCreateTime(), "yyyy-MM-dd HH:mm:ss"));

        return exportExcel;
    }

    @Override
    public RepairTaskDetailsVO getRepairTaskDetails(Long id) {
        log.info("查询维修任务详情，ID: {}", id);

        // 查询维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectById(id);
        if (repairTask == null) {
            log.error("未找到对应的维修任务，ID: {}", id);
            throw new BusinessException("未找到对应的维修任务");
        }

        // 查询工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
        if (workflowInstance == null) {
            log.error("未找到对应的工作流实例，任务编号: {}", repairTask.getTaskNo());
            throw new BusinessException("未找到对应的工作流实例");
        }

        // 转换为VO对象
        RepairTaskDetailsVO detailsVO = new RepairTaskDetailsVO();
        BeanUtils.copyProperties(repairTask, detailsVO);
        detailsVO.setInstanceId(workflowInstance.getId());
        detailsVO.setCurrentActivityCode(workflowInstance.getCurrentActivityCode());
        detailsVO.setStatusCode(workflowInstance.getStatusCode());

        // 赋值核损核价占据人
        if (null != detailsVO.getVerificationLossTaskOperId()) {
            UserVO userVO = userService.getUserById(detailsVO.getVerificationLossTaskOperId());
            if (null != userVO) {
                detailsVO.setVerificationLossTaskUsername(userVO.getNickname());
            }
        }

        // 判断任务是否超时
        Date compareDate = null != repairTask.getVehicleRepairTime() ? repairTask.getVehicleRepairTime()
                : repairTask.getExpectedRepairComplete();
        if (compareDate != null) {
            detailsVO.setIsOverTime(compareDate.compareTo(new Date()) < 0 ? 1 : 0);
        } else {
            detailsVO.setIsOverTime(0);
        }

        try {
            // 1. 填充车辆信息
            fillVehicleInfo(repairTask, detailsVO);

            // 2. 填充创建信息
            fillCreateInfo(repairTask, detailsVO);

            // 3. 填充维修信息
            fillRepairInfo(repairTask, detailsVO);

            // 4. 填充事故信息
            fillAccidentInfo(repairTask, detailsVO);

            // 5. 填充报价合计
            fillQuotationSummary(repairTask, detailsVO);

            // 6. 填充费用明细
            fillRepairFeeDetail(repairTask, detailsVO, workflowInstance);

            // 7. 填充客户付款信息
            fillCustomerPayment(repairTask, detailsVO);

            // 8. 填充验收信息
            fillAcceptanceInfo(repairTask, detailsVO);

            // 9. 填充维修图片明细
            fillRepairPictures(repairTask, detailsVO);

            log.info("维修任务详情查询成功，ID: {}, 任务编号: {}", id, repairTask.getTaskNo());
        } catch (Exception e) {
            log.error("填充维修任务详情子对象时发生错误: {}", e.getMessage(), e);
            // 不抛出异常，返回已填充的部分数据
        }

        // 返回详情
        return detailsVO;
    }

    /**
     * 填充车辆信息
     */
    private void fillVehicleInfo(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        VehicleInfoDTO vehicleInfo = new VehicleInfoDTO();
        vehicleInfo.setVin(repairTask.getVin());
        vehicleInfo.setLicensePlate(repairTask.getVehicleNo());
        vehicleInfo.setVehicleModelId(repairTask.getVehicleModelSeq());
        vehicleInfo.setVehicleModelName(repairTask.getVehicleModelInfo());
        vehicleInfo.setVehicleOrgId(repairTask.getVehicleOrgId());
        vehicleInfo.setVehicleOrgName(repairTask.getVehicleOrgName());
        vehicleInfo.setVehicleOperateOrgId(repairTask.getVehicleOperateOrgId());
        vehicleInfo.setVehicleOperateOrgName(repairTask.getVehicleOperateOrgName());
        vehicleInfo.setFactOperateTag(repairTask.getFactOperateTag());
        vehicleInfo.setProductLine(repairTask.getProductLine());
        vehicleInfo.setSubProductLine(repairTask.getSubProductLine());
        vehicleInfo.setPropertyStatus(repairTask.getPropertyStatus());
        vehicleInfo.setTotalMileage(repairTask.getTotalMileage());

        // 查询车辆当前里程数
        try {
            // 这里可以调用车辆服务查询最新里程数
            // vehicleInfo.setCurrentMileage(vehicleInfoService.getVehicleMileage(repairTask.getVin()));
        } catch (Exception e) {
            log.warn("获取车辆里程数失败: {}", e.getMessage());
        }

        detailsVO.setVehicleInfo(vehicleInfo);
    }

    /**
     * 填充创建信息
     */
    private void fillCreateInfo(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        RepairTaskCreateInfoVO createInfo = new RepairTaskCreateInfoVO();
        BeanUtils.copyProperties(repairTask, createInfo); // 复制基本属性

        detailsVO.setCreateInfo(createInfo);
    }

    /**
     * 填充维修信息
     */
    private void fillRepairInfo(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        RepairInfoDTO repairInfo = new RepairInfoDTO();
        BeanUtils.copyProperties(repairTask, repairInfo); // 复制基本属性

        detailsVO.setRepairInfo(repairInfo);
    }

    /**
     * 填充事故信息
     */
    private void fillAccidentInfo(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        // 只有事故维修类型才需要填充事故信息
        if (repairTask.getRepairTypeId() != null && repairTask.getRepairTypeId() == 1) {
            AccidentInfoDTO accidentInfo = new AccidentInfoDTO();
            BeanUtils.copyProperties(repairTask, accidentInfo); // 复制基本属性

            // 可以调用事故服务查询更多事故信息
            // 例如：事故状态、责任类型等

            detailsVO.setAccidentInfo(accidentInfo);
        }
    }

    /**
     * 填充报价合计
     */
    private void fillQuotationSummary(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        QuotationSummaryDTO quotationSummary = new QuotationSummaryDTO();
        BeanUtils.copyProperties(repairTask, quotationSummary); // 复制基本属性
        // 其他金额计算
        BigDecimal repairOtherTotalAmount = null;
        BigDecimal vehicleOtherTotalAmount = null;
        if (null != repairTask.getRepairInsuranceTotalAmount()
                && null != repairTask.getRepairReplaceTotalAmount()
                && null != repairTask.getRepairRepairTotalAmount()) {
            repairOtherTotalAmount = repairTask.getRepairInsuranceTotalAmount()
                    .subtract(repairTask.getRepairReplaceTotalAmount())
                    .subtract(repairTask.getRepairRepairTotalAmount());
        }
        if (null != repairTask.getVehicleInsuranceTotalAmount()
                && null != repairTask.getVehicleReplaceTotalAmount()
                && null != repairTask.getVehicleRepairTotalAmount()) {
            vehicleOtherTotalAmount = repairTask.getVehicleInsuranceTotalAmount()
                    .subtract(repairTask.getVehicleReplaceTotalAmount())
                    .subtract(repairTask.getVehicleRepairTotalAmount());
        }
        quotationSummary.setRepairOtherTotalAmount(repairOtherTotalAmount);
        quotationSummary.setVehicleOtherTotalAmount(vehicleOtherTotalAmount);

        detailsVO.setQuotationSummary(quotationSummary);
    }

    /**
     * 填充费用明细合计
     */
    private void fillRepairFeeDetail(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO,
            WorkflowInstance workflowInstance) {
        RepairFeeDetailDTO repairFeeDetailDTO = new RepairFeeDetailDTO();
        BeanUtils.copyProperties(repairTask, repairFeeDetailDTO); // 复制基本属性

        // 如果维修任务跳过核损核价环节
        if (repairTask.getVehicleCheckTime() == null) {
            String currentActivityCode = workflowInstance.getCurrentActivityCode();
            boolean isInRepairOrQualityInspection = ActivityDefinitionEnum.IN_REPAIR.getCode()
                    .equals(currentActivityCode)
                    || ActivityDefinitionEnum.QUALITY_INSPECTION.getCode().equals(currentActivityCode);

            if (isInRepairOrQualityInspection) {
                repairFeeDetailDTO.setRepairTotalAmount(repairTask.getRepairInsuranceTotalAmount());
            }
        }

        detailsVO.setRepairFeeDetail(repairFeeDetailDTO);
    }

    /**
     * 填充客户付款信息
     */
    private void fillCustomerPayment(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        CustomerPaymentDTO customerPayment = new CustomerPaymentDTO();
        BeanUtils.copyProperties(repairTask, customerPayment); // 复制基本属性

        detailsVO.setCustomerPayment(customerPayment);
    }

    /**
     * 填充验收信息
     */
    private void fillAcceptanceInfo(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        AcceptanceInfoDTO acceptanceInfo = new AcceptanceInfoDTO();
        BeanUtils.copyProperties(repairTask, acceptanceInfo); // 复制基本属性

        detailsVO.setAcceptanceInfo(acceptanceInfo);
    }

    /**
     * 填充维修图片明细
     */
    private void fillRepairPictures(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        List<MtcVehicleRepairPic> mtcVehicleRepairPicList = tableVehicleRepairPicService
                .selectByTaskNo(repairTask.getTaskNo());

        // 创建RepairPicturesDTO对象
        RepairPicturesDTO repairPictures = new RepairPicturesDTO();

        // 初始化所有图片列表为空列表而不是null
        repairPictures.setDrivingLicensePicture(new ArrayList<>());
        repairPictures.setPolicyPicture(new ArrayList<>());
        repairPictures.setCreatePicture(new ArrayList<>());
        repairPictures.setCreateVideo(new ArrayList<>());
        repairPictures.setAccidentPicture(new ArrayList<>());
        repairPictures.setDamageAPicture(new ArrayList<>());
        repairPictures.setDamageBPicture(new ArrayList<>());
        repairPictures.setClaimsPicture(new ArrayList<>());
        repairPictures.setOtherPicture(new ArrayList<>());
        repairPictures.setDamagedPartVideo(new ArrayList<>());
        repairPictures.setAfterPic(new ArrayList<>());
        repairPictures.setAccidentLiabilityConfirmationPicture(new ArrayList<>());
        repairPictures.setInsuranceCompanyLossOrderPicture(new ArrayList<>());
        repairPictures.setOurDriverLicensePicture(new ArrayList<>());
        repairPictures.setCustPicture(new ArrayList<>());
        repairPictures.setDamagedPartPicture(new ArrayList<>());
        repairPictures.setRepairPicture(new ArrayList<>());
        repairPictures.setCheckVideo(new ArrayList<>());

        // 如果没有图片，直接返回空列表
        if (CollectionUtils.isEmpty(mtcVehicleRepairPicList)) {
            detailsVO.setRepairPictures(repairPictures);
            return;
        }

        // 遍历所有图片，按类型分类
        for (MtcVehicleRepairPic pic : mtcVehicleRepairPicList) {
            FileDTO fileDTO = new FileDTO();
            fileDTO.setName(pic.getName());
            fileDTO.setUrl(getCompleteFileUrl(pic.getPicUrl()));
            // 根据图片类型将URL添加到对应的列表中
            if (RepairPicTypeEnum.DRIVING_LICENSE_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getDrivingLicensePicture().add(fileDTO);
            } else if (RepairPicTypeEnum.POLICY_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getPolicyPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.CREATE_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getCreatePicture().add(fileDTO);
            } else if (RepairPicTypeEnum.CREATE_VIDEO.getTypeId().equals(pic.getPicType())) {
                repairPictures.getCreateVideo().add(fileDTO);
            } else if (RepairPicTypeEnum.ACCIDENT_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getAccidentPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.DAMAGE_A_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getDamageAPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.DAMAGE_B_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getDamageBPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.CLAIMS_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getClaimsPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.OTHER_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getOtherPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.CHECK_VIDEO.getTypeId().equals(pic.getPicType())) {
                repairPictures.getCheckVideo().add(fileDTO);
            } else if (RepairPicTypeEnum.ACCIDENT_LIABILITY_CONFIRMATION_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getAccidentLiabilityConfirmationPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.INSURANCE_COMPANY_LOSS_ORDER_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getInsuranceCompanyLossOrderPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.OUR_DRIVER_LICENSE_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getOurDriverLicensePicture().add(fileDTO);
            } else if (RepairPicTypeEnum.CUST_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getCustPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.DAMAGED_PART_VIDEO.getTypeId().equals(pic.getPicType())) {
                repairPictures.getDamagedPartVideo().add(fileDTO);
            } else if (RepairPicTypeEnum.DAMAGED_PART_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getDamagedPartPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.REPAIR_PICTURE.getTypeId().equals(pic.getPicType())) {
                repairPictures.getRepairPicture().add(fileDTO);
            } else if (RepairPicTypeEnum.AFTER_PIC.getTypeId().equals(pic.getPicType())) {
                repairPictures.getAfterPic().add(fileDTO);
            }
        }

        // 设置到detailsVO中
        detailsVO.setRepairPictures(repairPictures);
    }

    @Override
    public MtcTaskListResultVO queryRepairTaskList(RepairTaskListQueryDTO queryDTO) {
        MtcTaskListResultVO resultVO = new MtcTaskListResultVO();

        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            queryDTO.setRepairDepotId(repairDepotId);
        }
        // 登录人公司
        else {
            queryDTO.setLoginOrgIds(SessionUtils.getAllAccessibleOrgIds());
        }

        // 使用PageHelper进行分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        // 1.查询数据 - 使用优化后的方法
        List<RepairTaskProcessListVO> list = tableRepairTaskService.queryRepairTaskListByActivityInstance(queryDTO,
                SessionUtils.getTenantId().intValue());
        if (CollectionUtils.isNotEmpty(list)) {
            // 查询所有活动节点定义
            List<ActivityDefinition> activityDefinitions = tableActivityDefinitionService.findAll();
            // 查询所有活动状态定义
            List<ActivityStatus> activityStatuses = tableActivityStatusService.findAll();
            for (RepairTaskProcessListVO repairTaskProcessListVO : list) {
                // 设置活动节点名称
                setActivityNameByCode(repairTaskProcessListVO, activityDefinitions);
                // 设置活动状态名称
                setStatusNameByCode(repairTaskProcessListVO, activityStatuses);
            }
        }
        PageInfo<RepairTaskProcessListVO> pageInfo = new PageInfo<>(list);
        BasePageVO<RepairTaskProcessListVO> pageVO = BasePageVO.of(list, pageInfo);
        resultVO.setPageInfo(pageVO);

        // 2.查询各环节任务数量
        if (queryDTO.getCurrentActivityCode() != null) {
            // 使用入参中的currentActivityCode查询活动实例表，按照statusCode分组统计任务数量
            Map<String, Long> statusCounts = tableRepairTaskService.countByToActivityCodeGroupByStatusCode(queryDTO);
            resultVO.setStatusCounts(statusCounts);
        }

        return resultVO;
    }

    @Override
    public void saveRepairTask(Long id, RepairTaskUpdateDTO repairTaskUpdateDTO) {
        // 1. 查询数据
        MtcRepairTask repairTask = tableRepairTaskService.selectById(id);
        if (repairTask == null) {
            throw new RuntimeException("未找到该数据");
        }

        // 2. 更新数据
        BeanUtils.copyProperties(repairTaskUpdateDTO, repairTask);
        repairTask.setUpdatedTime(new Date());
        repairTask.setUpdateBy(SessionUtils.getUsername());

        if (null != repairTask.getCustPaysDirect()) {
            if (repairTask.getCustPaysDirect() == 0) {
                repairTask.setUserAssumedAmount(BigDecimal.ZERO);
                repairTask.setNotUserAssumedAmount(BigDecimal.ZERO);
            } else {
                repairTask.setUserAssumedAmount(repairTaskUpdateDTO.getUserAssumedAmount());
                repairTask.setNotUserAssumedAmount(
                        repairTask.getCustAmount().subtract(repairTaskUpdateDTO.getUserAssumedAmount()));
            }
        }

        tableRepairTaskService.updateSelectiveById(repairTask);
    }

    /**
     * 处理维修任务相关的图片和视频文件
     * 公共方法，供多个服务共用图片处理逻辑
     *
     * @param taskNo       任务编号
     * @param mediaTypeMap 图片/视频类型映射，键为图片类型，值为图片URL列表
     * @param operatorName 操作人用户名
     * @throws BusinessException 处理过程中的业务异常
     */
    @Override
    public void processMediaFiles(String taskNo, Map<Integer, List<FileDTO>> mediaTypeMap, String operatorName) {
        log.info("开始处理维修任务图片和视频，任务编号：{}", taskNo);

        try {
            // 获取所有现有图片
            List<MtcVehicleRepairPic> allVehicleRepairPics = tableVehicleRepairPicService.selectByTaskNo(taskNo);
            log.debug("获取到现有图片/视频数量：{}", allVehicleRepairPics.size());

            // 收集需要插入和删除的图片/视频
            List<MtcVehicleRepairPic> insertPicList = new ArrayList<>();
            List<Long> deletePicIds = new ArrayList<>();

            // 处理每种类型的图片/视频
            mediaTypeMap.forEach((picType, newMediaList) -> {
                try {
                    // 获取要插入的图片/视频
                    List<FileDTO> insertMediaUrls = RepairPicUtil.getInsertPics(allVehicleRepairPics, newMediaList,
                            picType);
                    if (CollectionUtils.isNotEmpty(insertMediaUrls)) {
                        List<MtcVehicleRepairPic> typedInsertList = RepairPicUtil.transferStringToVehicleRepairPic(
                                insertMediaUrls, picType, taskNo, operatorName);
                        insertPicList.addAll(typedInsertList);
                        log.debug("类型[{}]需要新增{}个图片/视频", picType, typedInsertList.size());
                    }

                    // 获取要删除的图片/视频
                    List<MtcVehicleRepairPic> typedDeleteList = RepairPicUtil.getDeletePics(allVehicleRepairPics,
                            newMediaList, picType);
                    if (CollectionUtils.isNotEmpty(typedDeleteList)) {
                        List<Long> typedDeleteIds = typedDeleteList.stream()
                                .map(MtcVehicleRepairPic::getId)
                                .collect(Collectors.toList());
                        deletePicIds.addAll(typedDeleteIds);
                        log.debug("类型[{}]需要删除{}个图片/视频", picType, typedDeleteIds.size());
                    }
                } catch (Exception e) {
                    log.error("处理类型[{}]的图片/视频时发生错误：{}", picType, e.getMessage(), e);
                    // 这里不抛出异常，继续处理其他类型的图片/视频
                }
            });

            // 批量删除图片/视频（先删除再插入，避免主键冲突）
            if (CollectionUtils.isNotEmpty(deletePicIds)) {
                try {
                    int deleteCount = tableVehicleRepairPicService.delMaterialPic(deletePicIds);
                    log.info("成功删除{}个图片/视频", deleteCount);
                } catch (Exception e) {
                    log.error("批量删除图片/视频时发生错误：{}", e.getMessage(), e);
                    throw new BusinessException("删除图片/视频失败：" + e.getMessage());
                }
            }

            // 批量插入图片/视频
            if (CollectionUtils.isNotEmpty(insertPicList)) {
                try {
                    int insertCount = tableVehicleRepairPicService.batchInsert(insertPicList);
                    log.info("成功插入{}个图片/视频", insertCount);
                } catch (Exception e) {
                    log.error("批量插入图片/视频时发生错误：{}", e.getMessage(), e);
                    throw new BusinessException("插入图片/视频失败：" + e.getMessage());
                }
            }

            log.info("维修任务图片和视频处理完成，任务编号：{}", taskNo);
        } catch (Exception e) {
            log.error("处理维修任务图片和视频时发生未预期的错误：{}", e.getMessage(), e);
            throw new BusinessException("处理图片/视频失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void selectRepairDepot(Long taskId, String repairDepotId) {
        log.info("选择修理厂并更新维修任务，任务ID: {}, 修理厂ID: {}", taskId, repairDepotId);
        try {
            // 1. 参数校验
            if (taskId == null) {
                throw new BusinessException("维修任务ID不能为空");
            }
            if (null == repairDepotId) {
                throw new BusinessException("修理厂ID不能为空");
            }

            // 2. 查询维修任务
            MtcRepairTask repairTask = tableRepairTaskService.selectById(taskId);
            if (repairTask == null) {
                throw new BusinessException("未找到对应的维修任务");
            }

            // 3. 检查任务状态是否允许选择修理厂
            WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
            if (null == workflowInstance) {
                throw new BusinessException("未找到对应的工作流实例");
            }
            if (workflowInstance.getCurrentActivityCode().equals("") && workflowInstance.getStatusCode().equals("")) {
                throw new BusinessException("任务状态不允许选择修理厂");
            }

            // 4. 查询修理厂信息
            MtcRepairDepotInfo repairDepotInfo = tableRepairDepotInfoService.selectByRepairDepotCode(repairDepotId);
            if (repairDepotInfo == null) {
                throw new BusinessException("未找到对应的修理厂");
            }
            if (!repairDepotInfo.getRepairDepotType().equals(repairTask.getRepairDepotType())) {
                throw new BusinessException("选择的修理厂类型与当前任务不匹配");
            }

            // 5. 更新维修任务信息
            repairTask.setRepairDepotId(repairDepotInfo.getRepairDepotId());
            repairTask.setRepairDepotName(repairDepotInfo.getRepairDepotName());
            repairTask.setRepairDepotOrgId(repairDepotInfo.getRepairDepotOrgId());
            repairTask.setRepairDepotSapCode(repairDepotInfo.getRepairDepotSapCode());
            repairTask.setRepairGrade(repairDepotInfo.getRepairDepotGrade());
            repairTask.setRepairGrade(repairDepotInfo.getRepairDepotGrade());
            repairTask.setTaxRate(repairDepotInfo.getTaxRate());

            tableRepairTaskService.updateSelectiveById(repairTask, SessionUtils.getUsername());

            // 6. 启动或更新工作流
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("SELECT_REPAIR_DEPOT");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
            workflowService.processNode(workflowInstance.getId(), processDTO);
            log.info("选择修理厂成功，任务ID: {}, 修理厂: {}", taskId, repairDepotInfo.getRepairDepotName());
        } catch (BusinessException e) {
            log.error("选择修理厂失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("选择修理厂失败: {}", e.getMessage(), e);
            throw new BusinessException("选择修理厂失败: " + e.getMessage());
        }
    }

    /**
     * 添加维修备注
     *
     * @param repairRemarkDTO 维修备注信息
     * @throws BusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRepairRemark(RepairRemarkDTO repairRemarkDTO) {
        log.info("添加维修备注，参数: {}", repairRemarkDTO);

        // 参数校验
        if (repairRemarkDTO == null || StringUtils.isBlank(repairRemarkDTO.getTaskNo())) {
            throw new BusinessException("维修任务编号不能为空");
        }

        if (StringUtils.isBlank(repairRemarkDTO.getRemark())) {
            throw new BusinessException("备注内容不能为空");
        }

        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(repairRemarkDTO.getTaskNo());
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + repairRemarkDTO.getTaskNo());
        }

        // 获取当前登录用户
        String username = SessionUtils.getUsername();

        // 创建备注实体
        MtcRepairRemark repairRemark = new MtcRepairRemark();
        repairRemark.setTaskNo(repairRemarkDTO.getTaskNo());
        repairRemark.setActivityCode(repairRemarkDTO.getActivityCode());
        repairRemark.setRemark(repairRemarkDTO.getRemark());
        repairRemark.setCreatedTime(new Date());
        repairRemark.setCreateBy(username);
        repairRemark.setUpdatedTime(new Date());
        repairRemark.setUpdateBy(username);

        // 保存备注
        try {
            tableRepairRemarkService.insert(repairRemark);

            // 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setRecordId(repairTask.getId());
            operatorLog.setOpeContent("添加备注：" + repairRemarkDTO.getRemark());
            operatorLog.setRemark(ActivityDefinitionEnum.getDescriptionByCode(repairRemarkDTO.getActivityCode()));
            tableOperatorLogService.insertSelective(operatorLog);

            // // 清除缓存
            // String redisKey = "REPAIR_REMARK_" + repairRemarkDTO.getTaskNo();
            // redisTemplate.delete(redisKey);
            // redisTemplate.opsForSet().add(redisKey, username);

            log.info("添加维修备注成功，任务编号: {}, 备注ID: {}", repairRemarkDTO.getTaskNo(), repairRemark.getId());
        } catch (Exception e) {
            log.error("添加维修备注失败，任务编号: {}, 错误信息: {}", repairRemarkDTO.getTaskNo(), e.getMessage(), e);
            throw new BusinessException("添加维修备注失败: " + e.getMessage());
        }
    }

    /**
     * 删除维修备注
     *
     * @param id           备注ID
     * @param activityCode 维修阶段
     * @throws BusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRepairRemark(Long id, String activityCode) {
        log.info("删除维修备注，备注ID: {}, 维修阶段: {}", id, activityCode);

        // 参数校验
        if (id == null) {
            throw new BusinessException("备注ID不能为空");
        }

        // 查询备注信息
        MtcRepairRemark repairRemark = tableRepairRemarkService.selectById(id);
        if (repairRemark == null) {
            throw new BusinessException("未找到备注信息，备注ID: " + id);
        }

        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(repairRemark.getTaskNo());
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + repairRemark.getTaskNo());
        }

        // 获取当前登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();
        String username = loginUser.getUser().getUsername();

        // 检查是否是备注创建人
        if (!repairRemark.getCreateBy().equals(username)) {
            throw new BusinessException("该备注为其他用户创建，无法删除");
        }

        try {
            // 删除备注
            tableRepairRemarkService.deleteById(id);

            // 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setRecordId(repairTask.getId());
            operatorLog.setOpeContent("删除了备注：" + repairRemark.getRemark());
            operatorLog.setCurrentActivityCode(activityCode);
            tableOperatorLogService.insertSelective(operatorLog);

            log.info("删除维修备注成功，备注ID: {}", id);
        } catch (Exception e) {
            log.error("删除维修备注失败，备注ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new BusinessException("删除维修备注失败: " + e.getMessage());
        }
    }

    @Override
    public BasePageVO<RepairDepotFirstPageInfoVO> getFirstPageInfo(GetFirstPageInfoDTO getFirstPageInfoDTO) {
        // 构建查询条件
        RepairDepotQueryDTO queryDTO = new RepairDepotQueryDTO();

        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            queryDTO.setRepairDepotId(repairDepotId);
        }
        // 登录人公司
        else {
            queryDTO.setLoginOrgIds(SessionUtils.getAllAccessibleOrgIds());
        }

        queryDTO.setRepairDepotType(getFirstPageInfoDTO.getRepairDepotType());
        queryDTO.setRepairDepotId(getFirstPageInfoDTO.getRepairDepotId());
        queryDTO.setStatus(getFirstPageInfoDTO.getStatus());

        // 查询修理厂列表
        PageHelper.startPage(getFirstPageInfoDTO.getPageNum(), getFirstPageInfoDTO.getPageSize());
        List<MtcRepairDepotInfo> list = tableRepairDepotInfoService.queryRepairDepotList(queryDTO, null);
        PageInfo<MtcRepairDepotInfo> pageInfo = new PageInfo<>(list);

        List<RepairDepotFirstPageInfoVO> voList = list.stream().map(repairDepotInfo -> {
            RepairDepotFirstPageInfoVO vo = new RepairDepotFirstPageInfoVO();
            BeanUtils.copyProperties(repairDepotInfo, vo);
            return vo;
        }).collect(Collectors.toList());

        for (RepairDepotFirstPageInfoVO firstPageInfoVO : voList) {
            GetRepairNumDTO getRepairNumDTO = new GetRepairNumDTO(SessionUtils.getAllAccessibleOrgIds(), getFirstPageInfoDTO.getOrgId(), firstPageInfoVO.getRepairDepotId());
            // 今日新增总数
            List<RepairTaskWorkflowDTO> todayRepairList = tableRepairTaskService.getTodayRepairNum(getRepairNumDTO);
            // 今日完成总数
            List<RepairTaskWorkflowDTO> todayCompleteList = tableRepairTaskService.getTodayCompleteNum(getRepairNumDTO);
            // 在修总数
            List<RepairTaskWorkflowDTO> currentRepairList = tableRepairTaskService.getCurrentRepairNum(getRepairNumDTO);
            if (getFirstPageInfoDTO.getNumType() == 2) {
                // 根据车架号去重,保留最新的维修任务
                todayRepairList = deduplicateByVin(todayRepairList);
                // 根据车架号去重,保留最新的维修任务
                todayCompleteList = deduplicateByVin(todayCompleteList);
                // 根据车架号和当前活动节点去重
                currentRepairList = deduplicateByVinAndActivityCode(currentRepairList);
            }

            // 当前在修车辆总数
            firstPageInfoVO.setCurrentRepairNum((long) currentRepairList.size());
            // 今日维修总数
            firstPageInfoVO.setTodayAddNum((long) todayRepairList.size());
            // 今日出厂总数
            firstPageInfoVO.setTodayOutNum(todayCompleteList.stream().filter(
                    repairTaskWorkflowDTO -> ActivityDefinitionEnum.QUALITY_INSPECTION.getCode()
                            .equals(repairTaskWorkflowDTO.getCurrentActivityCode())
                            && "COMPLETED".equals(repairTaskWorkflowDTO.getStatusCode())
                            && LeavingStatusEnum.REGISTERED.getCode().equals(repairTaskWorkflowDTO.getLeavingStatus()))
                    .count());

            // 待分配数量
            firstPageInfoVO.setPendingAssignmentNum(currentRepairList.stream()
                    .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.PENDING_ASSIGNMENT.getCode()
                            .equals(repairTaskWorkflowDTO.getCurrentActivityCode()))
                    .count());

            // 车辆交接数量
            firstPageInfoVO.setVehicleTransferNum(currentRepairList.stream()
                    .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode()
                            .equals(repairTaskWorkflowDTO.getCurrentActivityCode()))
                    .count());

            // 报价中数量
            firstPageInfoVO.setRepairQuotationNum(currentRepairList.stream()
                    .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.REPAIR_QUOTATION.getCode()
                            .equals(repairTaskWorkflowDTO.getCurrentActivityCode()))
                    .count());

            // 核价中数量
            firstPageInfoVO.setLossAssessmentNum(currentRepairList.stream()
                    .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode()
                            .equals(repairTaskWorkflowDTO.getCurrentActivityCode()))
                    .count());

            // 维修中数量
            firstPageInfoVO.setInRepairNum(currentRepairList.stream()
                    .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.IN_REPAIR.getCode()
                            .equals(repairTaskWorkflowDTO.getCurrentActivityCode()))
                    .count());

            // 维修中数量
            firstPageInfoVO.setQualityInspectionNum(currentRepairList.stream()
                    .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.QUALITY_INSPECTION.getCode()
                            .equals(repairTaskWorkflowDTO.getCurrentActivityCode())
                            && !"COMPLETED".equals(repairTaskWorkflowDTO.getStatusCode()))
                    .count());
        }

        // 构建返回结果
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public BasePageVO<RepairDepotInRepairingVO> getRepairDepotInRepairingInfo(GetFirstPageInfoDTO getFirstPageInfoDTO) {
        // 构建查询条件
        RepairDepotQueryDTO queryDTO = new RepairDepotQueryDTO();

        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            queryDTO.setRepairDepotId(repairDepotId);
        } else {
            // 登录人公司
            queryDTO.setLoginOrgIds(SessionUtils.getAllAccessibleOrgIds());
            queryDTO.setRepairDepotId(getFirstPageInfoDTO.getRepairDepotId());
        }

        queryDTO.setOrgId(getFirstPageInfoDTO.getOrgId());

        // 查询修理厂列表
        PageHelper.startPage(getFirstPageInfoDTO.getPageNum(), getFirstPageInfoDTO.getPageSize());
        List<RepairDepotInRepairingVO> list = tableRepairTaskService.queryRepairDepotInRepairingInfo(queryDTO);
        PageInfo<RepairDepotInRepairingVO> pageInfo = new PageInfo<>(list);

        // 构建返回结果
        return BasePageVO.of(list, pageInfo);
    }

    @Override
    @Transactional(readOnly = true)
    public BasePageVO<VehicleRepairRecordVO> queryVehicleRepairRecord(VehicleRepairRecordQueryDTO queryDTO) {
        log.info("查询车辆历次维修记录开始，查询条件：{}", queryDTO);

        // 1. 获取当前任务信息和相关数据
        CurrentTaskInfo currentTaskInfo = fetchCurrentTaskInfo(queryDTO.getId());

        // 2. 使用PageHelper进行分页查询
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        queryDTO.setVin(currentTaskInfo.getRepairTask().getVin());
        List<VehicleRepairRecordVO> list = tableRepairTaskService.queryVehicleRepairRecord(queryDTO);

        // 3. 处理每条维修记录
        list.forEach(record -> processRepairRecord(record, currentTaskInfo));

        // 4. 构建分页结果
        PageInfo<VehicleRepairRecordVO> pageInfo = new PageInfo<>(list);
        log.info("查询车辆历次维修记录完成");
        return BasePageVO.of(list, pageInfo);
    }

    /**
     * 获取当前任务信息和相关数据
     */
    private CurrentTaskInfo fetchCurrentTaskInfo(Long taskId) {
        CurrentTaskInfo info = new CurrentTaskInfo();

        // 获取当前任务详情
        MtcRepairTask repairTask = tableRepairTaskService.selectById(taskId);
        if (repairTask != null && repairTask.getTotalMileage() != null) {
            info.setRepairTask(repairTask);
            info.setMaxTotalMileage(repairTask.getTotalMileage());
            info.setMinTotalMileage(repairTask.getTotalMileage().subtract(BigDecimal.valueOf(10000)));

            // 获取相关维修项目信息
            info.setLossFitInfoList(tableLossFitInfoService.selectByTaskNo(repairTask.getTaskNo()));
            info.setLossRepairInfoList(tableLossRepairInfoService.selectByTaskNo(repairTask.getTaskNo()));
            info.setRepairItemCheckInfoList(tableRepairItemCheckInfoService.selectByTaskNo(repairTask.getTaskNo()));
        }

        return info;
    }

    /**
     * 处理单条维修记录
     */
    private void processRepairRecord(VehicleRepairRecordVO record, CurrentTaskInfo currentTaskInfo) {
        log.debug("处理维修记录，任务编号: {}", record.getTaskNo());

        // 格式化总里程数
        formatTotalMileage(record);

        // 判断是否为重复项目（在当前任务前一万公里内）
        boolean isRepeatItem = isRepeatItem(record, currentTaskInfo);

        // 获取维修任务详情
        MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(record.getTaskNo());

        // 根据零件库类型处理不同的维修项目
        if (mtcRepairTask.getPartsLibraryType() == 1) {
            // 处理维修项目检查信息
            processRepairCheckItems(record, mtcRepairTask, isRepeatItem, currentTaskInfo);
        } else {
            // 处理换件项目
            processPartItems(record, mtcRepairTask, isRepeatItem, currentTaskInfo);
            // 处理修理项目
            processRepairItems(record, mtcRepairTask, isRepeatItem, currentTaskInfo);
        }
    }

    /**
     * 格式化总里程数，去除不必要的小数点和零
     */
    private void formatTotalMileage(VehicleRepairRecordVO record) {
        String totalMileage = record.getTotalMileage();
        if (StringUtils.isBlank(totalMileage)) {
            return;
        }
        
        try {
            BigDecimal mileage = new BigDecimal(totalMileage);
            // 如果小数部分为0，则显示为整数；否则保留小数部分
            if (mileage.scale() <= 0 || mileage.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
                record.setTotalMileage(mileage.toBigInteger().toString());
            } else {
                record.setTotalMileage(mileage.stripTrailingZeros().toPlainString());
            }
        } catch (NumberFormatException e) {
            log.warn("总里程数格式化失败，保持原值: {}", totalMileage, e);
        }
    }

    /**
     * 判断是否为重复项目（在当前任务前一万公里内）
     */
    private boolean isRepeatItem(VehicleRepairRecordVO record, CurrentTaskInfo currentTaskInfo) {
        if (StringUtils.isBlank(record.getTotalMileage()) || currentTaskInfo.getMaxTotalMileage() == null) {
            return false;
        }

        BigDecimal compareTotalMileage = new BigDecimal(record.getTotalMileage());
        return compareTotalMileage.compareTo(currentTaskInfo.getMinTotalMileage()) >= 0 &&
                compareTotalMileage.compareTo(currentTaskInfo.getMaxTotalMileage()) <= 0;
    }

    /**
     * 处理换件项目
     */
    private void processPartItems(VehicleRepairRecordVO record, MtcRepairTask mtcRepairTask,
            boolean isRepeatItem, CurrentTaskInfo currentTaskInfo) {
        List<MtcLossFitInfo> fitList = tableLossFitInfoService.selectByTaskNo(mtcRepairTask.getTaskNo());
        if (CollectionUtils.isEmpty(fitList)) {
            return;
        }

        // 设置所有零件名称
        List<String> allPartNames = fitList.stream()
                .map(MtcLossFitInfo::getItemName)
                .collect(Collectors.toList());
        record.setPartNames(String.join("  ", allPartNames));

        // 处理重复的零件
        if (isRepeatItem && CollectionUtils.isNotEmpty(currentTaskInfo.getLossFitInfoList())) {
            Set<String> repeatPartNames = findRepeatPartNames(fitList, currentTaskInfo.getLossFitInfoList());

            if (!repeatPartNames.isEmpty()) {
                String repeatPartsStr = String.join("  ", repeatPartNames);
                record.setRepeatPartNames(repeatPartsStr);

                // 从所有零件名称中移除重复的零件名称
                String partNames = record.getPartNames();
                if (StringUtils.isNotBlank(partNames)) {
                    record.setPartNames(partNames.replace(repeatPartsStr, ""));
                }

                log.debug("重复零件名称: {}", repeatPartsStr);
            }
        }
    }

    /**
     * 查找重复的零件名称
     */
    private Set<String> findRepeatPartNames(List<MtcLossFitInfo> fitList, List<MtcLossFitInfo> currentFitList) {
        Set<String> currentItemNames = currentFitList.stream()
                .map(MtcLossFitInfo::getItemName)
                .collect(Collectors.toSet());

        return fitList.stream()
                .map(MtcLossFitInfo::getItemName)
                .filter(currentItemNames::contains)
                .collect(Collectors.toSet());
    }

    /**
     * 处理修理项目
     */
    private void processRepairItems(VehicleRepairRecordVO record, MtcRepairTask mtcRepairTask,
            boolean isRepeatItem, CurrentTaskInfo currentTaskInfo) {
        List<MtcLossRepairInfo> repairList = tableLossRepairInfoService.selectByTaskNo(mtcRepairTask.getTaskNo());
        if (CollectionUtils.isEmpty(repairList)) {
            return;
        }

        // 设置所有修理项目名称
        List<String> allRepairNames = repairList.stream()
                .map(MtcLossRepairInfo::getItemName)
                .collect(Collectors.toList());
        record.setRepairNames(String.join("  ", allRepairNames));

        // 处理重复的修理项目
        if (isRepeatItem && (CollectionUtils.isNotEmpty(currentTaskInfo.getRepairItemCheckInfoList()) ||
                CollectionUtils.isNotEmpty(currentTaskInfo.getLossRepairInfoList()))) {
            Set<String> repeatRepairNames = findRepeatRepairNames(repairList, currentTaskInfo);

            if (!repeatRepairNames.isEmpty()) {
                String repeatRepairsStr = String.join("  ", repeatRepairNames);
                record.setRepeatRepairNames(repeatRepairsStr);

                // 从所有修理项目名称中移除重复的修理项目名称
                String repairNames = record.getRepairNames();
                if (StringUtils.isNotBlank(repairNames)) {
                    record.setRepairNames(repairNames.replace(repeatRepairsStr, ""));
                }

                log.debug("重复修理项目名称: {}", repeatRepairsStr);
            }
        }
    }

    /**
     * 查找重复的修理项目名称
     */
    private Set<String> findRepeatRepairNames(List<MtcLossRepairInfo> repairList, CurrentTaskInfo currentTaskInfo) {
        Set<String> result = new HashSet<>();

        // 从修理项目检查信息中查找重复项
        if (CollectionUtils.isNotEmpty(currentTaskInfo.getRepairItemCheckInfoList())) {
            Set<String> checkItemNames = currentTaskInfo.getRepairItemCheckInfoList().stream()
                    .map(MtcRepairItemCheckInfo::getItemName)
                    .collect(Collectors.toSet());

            repairList.stream()
                    .map(MtcLossRepairInfo::getItemName)
                    .filter(checkItemNames::contains)
                    .forEach(result::add);
        }

        // 从修理信息中查找重复项
        if (CollectionUtils.isNotEmpty(currentTaskInfo.getLossRepairInfoList())) {
            Set<String> repairItemNames = currentTaskInfo.getLossRepairInfoList().stream()
                    .map(MtcLossRepairInfo::getItemName)
                    .collect(Collectors.toSet());

            repairList.stream()
                    .map(MtcLossRepairInfo::getItemName)
                    .filter(repairItemNames::contains)
                    .forEach(result::add);
        }

        return result;
    }

    /**
     * 处理维修项目检查信息
     */
    private void processRepairCheckItems(VehicleRepairRecordVO record, MtcRepairTask mtcRepairTask,
            boolean isRepeatItem, CurrentTaskInfo currentTaskInfo) {
        List<MtcRepairItemCheckInfo> itemList = tableRepairItemCheckInfoService
                .selectByTaskNo(mtcRepairTask.getTaskNo());
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }

        // 设置所有维修项目检查信息名称
        List<String> allCheckNames = itemList.stream()
                .map(MtcRepairItemCheckInfo::getItemName)
                .collect(Collectors.toList());
        record.setRepairNames(String.join("  ", allCheckNames));

        // 处理重复的维修项目检查信息
        if (isRepeatItem && (CollectionUtils.isNotEmpty(currentTaskInfo.getRepairItemCheckInfoList()) ||
                CollectionUtils.isNotEmpty(currentTaskInfo.getLossRepairInfoList()))) {
            Set<String> repeatCheckNames = findRepeatCheckNames(itemList, currentTaskInfo);

            if (!repeatCheckNames.isEmpty()) {
                String repeatChecksStr = String.join("  ", repeatCheckNames);
                record.setRepeatRepairNames(repeatChecksStr);

                // 从所有维修项目检查信息名称中移除重复的维修项目检查信息名称
                String repairNames = record.getRepairNames();
                if (StringUtils.isNotBlank(repairNames)) {
                    record.setRepairNames(repairNames.replace(repeatChecksStr, ""));
                }

                log.debug("重复维修项目检查信息名称: {}", repeatChecksStr);
            }
        }
    }

    /**
     * 查找重复的维修项目检查信息名称
     */
    private Set<String> findRepeatCheckNames(List<MtcRepairItemCheckInfo> itemList, CurrentTaskInfo currentTaskInfo) {
        Set<String> result = new HashSet<>();

        // 从修理项目检查信息中查找重复项
        if (CollectionUtils.isNotEmpty(currentTaskInfo.getRepairItemCheckInfoList())) {
            Set<String> checkItemNames = currentTaskInfo.getRepairItemCheckInfoList().stream()
                    .map(MtcRepairItemCheckInfo::getItemName)
                    .collect(Collectors.toSet());

            itemList.stream()
                    .map(MtcRepairItemCheckInfo::getItemName)
                    .filter(checkItemNames::contains)
                    .forEach(result::add);
        }

        // 从修理信息中查找重复项
        if (CollectionUtils.isNotEmpty(currentTaskInfo.getLossRepairInfoList())) {
            Set<String> repairItemNames = currentTaskInfo.getLossRepairInfoList().stream()
                    .map(MtcLossRepairInfo::getItemName)
                    .collect(Collectors.toSet());

            itemList.stream()
                    .map(MtcRepairItemCheckInfo::getItemName)
                    .filter(repairItemNames::contains)
                    .forEach(result::add);
        }

        return result;
    }

    /**
     * 当前任务信息类，用于存储当前任务的相关信息
     */
    @Data
    private static class CurrentTaskInfo {
        private MtcRepairTask repairTask;
        private BigDecimal maxTotalMileage;
        private BigDecimal minTotalMileage;
        private List<MtcLossFitInfo> lossFitInfoList;
        private List<MtcLossRepairInfo> lossRepairInfoList;
        private List<MtcRepairItemCheckInfo> repairItemCheckInfoList;

        public CurrentTaskInfo() {
            this.lossFitInfoList = Collections.emptyList();
            this.lossRepairInfoList = Collections.emptyList();
            this.repairItemCheckInfoList = Collections.emptyList();
        }
    }

    public VehicleRepairStatisticsVO getVehicleRepairStatistics(GetFirstPageInfoDTO getFirstPageInfoDTO) {
        VehicleRepairStatisticsVO result = new VehicleRepairStatisticsVO();

        GetRepairNumDTO getRepairNumDTO = new GetRepairNumDTO(SessionUtils.getAllAccessibleOrgIds(),
                getFirstPageInfoDTO.getOrgId(), getFirstPageInfoDTO.getRepairDepotId());

        // 今日新增总数
        List<RepairTaskWorkflowDTO> todayRepairList = tableRepairTaskService.getTodayRepairNum(getRepairNumDTO);
        // 今日完成总数
        List<RepairTaskWorkflowDTO> todayCompleteList = tableRepairTaskService.getTodayCompleteNum(getRepairNumDTO);
        // 在修总数
        List<RepairTaskWorkflowDTO> currentRepairList = tableRepairTaskService.getCurrentRepairNum(getRepairNumDTO);
        if (getFirstPageInfoDTO.getNumType() == 2) {
            // 根据车架号去重,保留最新的维修任务
            todayRepairList = deduplicateByVin(todayRepairList);
            // 根据车架号去重,保留最新的维修任务
            todayCompleteList = deduplicateByVin(todayCompleteList);
            // 根据车架号和当前活动节点去重
            currentRepairList = deduplicateByVinAndActivityCode(currentRepairList);
        }

        // 当前在修总数量
        result.setCurrentRepairTotalNum((long) currentRepairList.size());

        // 今日进修理厂数量
        result.setTodayInFactoryNum((long) todayRepairList.size());

        // 今日出修理厂数量
        result.setTodayOutFactoryNum(todayCompleteList.stream().filter(
                repairTaskWorkflowDTO -> ActivityDefinitionEnum.QUALITY_INSPECTION.getCode()
                        .equals(repairTaskWorkflowDTO.getCurrentActivityCode())
                        && ActivityStatusEnum.COMPLETED.getCode().equals(repairTaskWorkflowDTO.getStatusCode())
                        && LeavingStatusEnum.REGISTERED.getCode().equals(repairTaskWorkflowDTO.getLeavingStatus()))
                .count());

        return result;
    }

    /**
     * 根据车架号去重，保留最新的维修任务
     *
     * @param repairTaskList 维修任务列表
     * @return 去重后的维修任务列表
     */
    private List<RepairTaskWorkflowDTO> deduplicateByVin(List<RepairTaskWorkflowDTO> repairTaskList) {
        return repairTaskList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.groupingBy(RepairTaskWorkflowDTO::getVin,
                                Collectors.reducing((first, second) -> first)),
                        map -> map.values().stream()
                                .filter(Optional::isPresent)
                                .map(Optional::get)
                                .collect(Collectors.toList())));
    }

    /**
     * 根据车架号和当前活动节点去重
     *
     * @param repairTaskList 维修任务列表
     * @return 去重后的维修任务列表
     */
    private List<RepairTaskWorkflowDTO> deduplicateByVinAndActivityCode(List<RepairTaskWorkflowDTO> repairTaskList) {
        return repairTaskList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.groupingBy(
                                task -> task.getVin() + "_" + task.getCurrentActivityCode(),
                                Collectors.toList()),
                        map -> map.values().stream()
                                .map(subList -> subList.get(0))
                                .collect(Collectors.toList())));
    }

    @Override
    public List<ViewRepairRemarkVO> viewRepairRemark(String taskNo) {
        List<MtcRepairRemark> remarkList = tableRepairRemarkService.selectByTaskNo(taskNo);

        List<ViewRepairRemarkVO> viewRepairRemarkVOs = remarkList.stream().map(remark -> {
            ViewRepairRemarkVO vo = new ViewRepairRemarkVO();
            BeanUtils.copyProperties(remark, vo);
            if (SessionUtils.getUsername().equals(remark.getCreateBy())) {
                vo.setIsOwner(1);
            } else {
                vo.setIsOwner(0);
            }
            return vo;
        }).collect(Collectors.toList());

        return viewRepairRemarkVOs;
    }

    @Override
    public List<RepairTaskCurrentListVO> queryCurrentRepairTasksByVin(String vin) {
        log.info("查询车辆当前进行中的维修任务列表开始，车架号：{}", vin);

        if (StringUtils.isBlank(vin)) {
            log.error("查询车辆当前进行中的维修任务列表失败：车架号为空");
            throw new BusinessException("车架号不能为空");
        }

        try {
            // 查询该车架号下的所有维修任务
            List<MtcRepairTask> repairTaskList = tableRepairTaskService.selectByVin(vin);

            if (CollectionUtils.isEmpty(repairTaskList)) {
                log.info("未查询到车架号为{}的维修任务", vin);
                return Collections.emptyList();
            }

            // 过滤出进行中的维修任务
            List<RepairTaskCurrentListVO> currentRepairTasks = repairTaskList.stream()
                    .filter(task -> {
                        // 获取工作流实例
                        WorkflowInstance workflowInstance = tableWorkflowInstanceService
                                .selectByBusinessId(task.getTaskNo());
                        // 判断是否为进行中状态（非CLOSED和COMPLETED状态）
                        return workflowInstance != null &&
                                !workflowInstance.getStatusCode().equals(ActivityStatusEnum.CLOSED.getCode()) &&
                                !workflowInstance.getStatusCode().equals(ActivityStatusEnum.COMPLETED.getCode());
                    })
                    .map(task -> {
                        // 转换为详情VO
                        RepairTaskCurrentListVO currentRepairTaskVO = new RepairTaskCurrentListVO();
                        BeanUtils.copyProperties(task, currentRepairTaskVO);

                        // 获取工作流实例信息
                        WorkflowInstance workflowInstance = tableWorkflowInstanceService
                                .selectByBusinessId(task.getTaskNo());
                        if (workflowInstance != null) {
                            currentRepairTaskVO.setCurrentActivityCode(workflowInstance.getCurrentActivityCode());
                            currentRepairTaskVO.setStatusCode(workflowInstance.getStatusCode());
                        }

                        MtcRepairDepotInfo repairDepotInfo = tableRepairDepotInfoService.selectByRepairDepotCode(task.getRepairDepotId());
                        if (repairDepotInfo != null) {
                            currentRepairTaskVO.setRepairDepotType(repairDepotInfo.getRepairDepotType());
                            currentRepairTaskVO.setRepairDepotGrade(repairDepotInfo.getRepairDepotGrade());
                        }

                        // 获取最新的任务出厂登记
                        MtcRepairTaskLeavingFactory mtcRepairTaskLeavingFactory = tableRepairTaskLeavingFactoryService
                                .selectLatestRecordByTaskNo(task.getTaskNo());
                        if (mtcRepairTaskLeavingFactory != null) {
                            currentRepairTaskVO.setLeavingStatus(mtcRepairTaskLeavingFactory.getLeavingStatus());
                        }

                        return currentRepairTaskVO;
                    })
                    .collect(Collectors.toList());

            log.info("查询车辆当前进行中的维修任务列表完成，车架号：{}，查询到{}条记录", vin, currentRepairTasks.size());
            return currentRepairTasks;
        } catch (Exception e) {
            log.error("查询车辆当前进行中的维修任务列表异常，车架号：{}", vin, e);
            throw new BusinessException("查询车辆当前进行中的维修任务列表失败：" + e.getMessage());
        }
    }

    /**
     * 导出维修厂首页统计信息
     * 使用游标分页导出，支持大数据量处理，避免深分页性能问题
     *
     * @param getFirstPageInfoDTO 查询参数
     * @param response            HTTP响应对象
     * @throws BusinessException 业务异常
     */
    @Override
    public void exportFirstPageInfo(GetFirstPageInfoDTO getFirstPageInfoDTO, HttpServletResponse response) {
        log.info("开始使用游标分页导出维修厂首页统计信息");

        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            getFirstPageInfoDTO.setRepairDepotId(repairDepotId);
        }
        // 登录人公司
        else {
            getFirstPageInfoDTO.setLoginOrgIds(SessionUtils.getAllAccessibleOrgIds());
        }

        try {
            // 创建导出配置，使用游标分页
            String fileName = "维修厂首页统计信息_" + System.currentTimeMillis() + ".xlsx";
            ExcelExportUtil.ExportConfig<RepairDepotFirstPageExportExcel> config = ExcelExportUtil
                    .builder(RepairDepotFirstPageExportExcel.class)
                    .fileName(fileName)
                    .sheetName("维修厂首页统计")
                    .cursorDataProvider(lastId -> getExportFirstPageInfoWithCursor(getFirstPageInfoDTO, lastId, 1000))
                    .batchSize(1000)
                    .progressCallback(
                            (current, total) -> log.debug("游标分页导出进度: {}/{}", current, total > 0 ? total : "未知"))
                    .build();

            // 执行分批导出
            ExcelExportUtil.exportToResponse(config, response);

            log.info("游标分页导出维修厂首页统计信息完成");
        } catch (Exception e) {
            log.error("导出维修厂首页统计信息失败: {}", e.getMessage(), e);
            throw new BusinessException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取导出数据（游标分页）
     * 使用基于ID的游标分页，避免深分页性能问题
     *
     * @param getFirstPageInfoDTO 查询参数
     * @param lastId              上一批次最后一条记录的ID（首次查询传入0）
     * @param pageSize            页大小
     * @return 导出数据列表
     */
    private List<RepairDepotFirstPageExportExcel> getExportFirstPageInfoWithCursor(
            GetFirstPageInfoDTO getFirstPageInfoDTO, Long lastId, int pageSize) {
        log.debug("获取导出数据（游标分页），lastId: {}", lastId);

        // 使用支持游标分页的方法查询数据
        List<MtcRepairDepotInfo> dataList = tableRepairDepotInfoService.exportFirstPageInfoWithCursor(
                lastId, getFirstPageInfoDTO, pageSize);

        // 转换为导出Excel实体
        List<RepairDepotFirstPageExportExcel> exportList = dataList.stream()
                .map(mtcRepairDepotInfo -> convertToFirstPageInfoExportExcel(getFirstPageInfoDTO, mtcRepairDepotInfo,
                        getFirstPageInfoDTO.getNumType()))
                .collect(Collectors.toList());

        log.debug("获取导出数据完成（游标分页），实际数据量: {}", exportList.size());
        return exportList;
    }

    /**
     * 转换为导出Excel实体
     *
     * @param vo 维修厂首页统计信息VO
     * @return 导出Excel实体
     */
    private RepairDepotFirstPageExportExcel convertToFirstPageInfoExportExcel(GetFirstPageInfoDTO getFirstPageInfoDTO, MtcRepairDepotInfo vo, Integer numType) {
        RepairDepotFirstPageExportExcel exportExcel = new RepairDepotFirstPageExportExcel();

        GetRepairNumDTO getRepairNumDTO = new GetRepairNumDTO(SessionUtils.getAllAccessibleOrgIds(), getFirstPageInfoDTO.getOrgId(), vo.getRepairDepotId());

        // 今日新增总数
        List<RepairTaskWorkflowDTO> todayRepairList = tableRepairTaskService.getTodayRepairNum(getRepairNumDTO);
        // 今日完成总数
        List<RepairTaskWorkflowDTO> todayCompleteList = tableRepairTaskService.getTodayCompleteNum(getRepairNumDTO);
        // 在修总数
        List<RepairTaskWorkflowDTO> currentRepairList = tableRepairTaskService.getCurrentRepairNum(getRepairNumDTO);

        if (numType == 2) {
            // 根据车架号去重,保留最新的维修任务
            todayRepairList = deduplicateByVin(todayRepairList);
            // 根据车架号去重,保留最新的维修任务
            todayCompleteList = deduplicateByVin(todayCompleteList);
            // 根据车架号和当前活动节点去重
            currentRepairList = deduplicateByVinAndActivityCode(currentRepairList);
        }

        // 复制基本属性
        BeanUtils.copyProperties(vo, exportExcel);

        // 转换修理厂类型
        exportExcel.setRepairDepotTypeString(Optional.ofNullable(vo.getRepairDepotType())
                .map(RepairDepotTypeEnum::getDescriptionByCode)
                .orElse(""));

        // 当前在修车辆总数
        exportExcel.setCurrentRepairNum((long) currentRepairList.size());
        // 今日维修总数
        exportExcel.setTodayAddNum((long) todayRepairList.size());
        // 今日出厂总数
        exportExcel.setTodayOutNum(todayCompleteList.stream().filter(
                repairTaskWorkflowDTO -> ActivityDefinitionEnum.QUALITY_INSPECTION.getCode()
                        .equals(repairTaskWorkflowDTO.getCurrentActivityCode())
                        && ActivityStatusEnum.COMPLETED.getCode().equals(repairTaskWorkflowDTO.getStatusCode())
                        && LeavingStatusEnum.REGISTERED.getCode().equals(repairTaskWorkflowDTO.getLeavingStatus()))
                .count());

        // 车辆交接数量
        exportExcel.setVehicleTransferNum(currentRepairList.stream()
                .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode()
                        .equals(repairTaskWorkflowDTO.getCurrentActivityCode()))
                .count());

        // 报价中数量
        exportExcel.setRepairQuotationNum(currentRepairList.stream()
                .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.REPAIR_QUOTATION.getCode()
                        .equals(repairTaskWorkflowDTO.getCurrentActivityCode()))
                .count());

        // 核价中数量
        exportExcel.setLossAssessmentNum(currentRepairList.stream()
                .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode()
                        .equals(repairTaskWorkflowDTO.getCurrentActivityCode()))
                .count());

        // 维修中数量
        exportExcel.setInRepairNum(currentRepairList.stream()
                .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.IN_REPAIR.getCode()
                        .equals(repairTaskWorkflowDTO.getCurrentActivityCode()))
                .count());

        // 待验收数量
        exportExcel.setQualityInspectionNum(currentRepairList.stream()
                .filter(repairTaskWorkflowDTO -> ActivityDefinitionEnum.QUALITY_INSPECTION.getCode()
                        .equals(repairTaskWorkflowDTO.getCurrentActivityCode())
                        && !ActivityStatusEnum.COMPLETED.getCode().equals(repairTaskWorkflowDTO.getStatusCode()))
                .count());

        return exportExcel;
    }

    /**
     * 获取完整的文件URL
     * 如果URL已经是完整URL（以http://或https://开头），则直接返回
     * 否则，拼接baseUrl和env前缀
     *
     * @param fileUrl 文件URL（可能是相对路径）
     * @return 完整的文件URL
     */
    private String getCompleteFileUrl(String fileUrl) {
        if (StringUtils.isEmpty(fileUrl)) {
            return fileUrl;
        }

        // 如果已经是完整URL，直接返回
        if (fileUrl.startsWith("http://") || fileUrl.startsWith("https://")) {
            return fileUrl;
        }

        try {
            // 获取OssConfig中的配置
            OssConfig ossConfig = SpringContextUtil.getBean(OssConfig.class);
            String baseUrl = ossConfig.getUpload().getBaseUrl();
            String env = ossConfig.getUpload().getEnv();

            if (StringUtils.isEmpty(baseUrl)) {
                return fileUrl;
            }

            // 构建完整URL
            StringBuilder fullUrl = new StringBuilder(baseUrl);

            // 处理baseUrl末尾的斜杠
            if (!baseUrl.endsWith("/")) {
                fullUrl.append("/");
            }

            // 添加环境目录
            if (StringUtils.isNotEmpty(env)) {
                fullUrl.append(env);
                if (!env.endsWith("/")) {
                    fullUrl.append("/");
                }
            }

            // 处理fileUrl开头的斜杠，避免重复
            if (fileUrl.startsWith("/")) {
                fileUrl = fileUrl.substring(1);
            }

            // 拼接文件路径
            fullUrl.append(fileUrl);

            return fullUrl.toString();
        } catch (Exception e) {
            log.warn("构建完整文件URL失败: {}", e.getMessage());
            return fileUrl;
        }
    }

    @Override
    public Map<String, Long> countActivityCode(RepairTaskListQueryDTO queryDTO) {
        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            queryDTO.setRepairDepotId(repairDepotId);
        } else {
            // 登录人公司
            queryDTO.setLoginOrgIds(SessionUtils.getAllAccessibleOrgIds());
        }

        return tableRepairTaskService.countByActivityCode(queryDTO);
    }

    @Override
    public void adjustCustAmount(RepairCustInfoDTO repairCustInfoDTO) {
        if (null == repairCustInfoDTO) {
            throw new BusinessException("入参不满足要求！");
        }

        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(repairCustInfoDTO.getTaskNo());
        if (null == repairTask) {
            throw new BusinessException("维修任务不存在！");
        }
        repairCustInfoDTO.setId(repairTask.getId());

        if (repairCustInfoDTO.getCustAmount().compareTo(repairCustInfoDTO.getUserAssumedAmount()) < 0) {
            throw new BusinessException("用户承担金额不可大于用户直付金额！");
        }

        // 是否客户直付 = 否 -> 清空数据
        if (repairCustInfoDTO.getCustPaysDirect() == 0) {
            repairCustInfoDTO.setCustAmount(BigDecimal.ZERO);
            repairCustInfoDTO.setUserAssumedAmount(BigDecimal.ZERO);
            repairCustInfoDTO.setNotUserAssumedAmount(BigDecimal.ZERO);
        }
        repairCustInfoDTO.setNotUserAssumedAmount(
                repairCustInfoDTO.getCustAmount().subtract(repairCustInfoDTO.getUserAssumedAmount()));
        tableRepairTaskService.adjustCustAmount(repairCustInfoDTO);

        // 处理客户直付凭证
        try {
            // 构建媒体类型映射
            Map<Integer, List<FileDTO>> mediaTypeMap = new HashMap<>();
            if (repairCustInfoDTO.getCustPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.CUST_PICTURE.getTypeId(), repairCustInfoDTO.getCustPicture());
            }

            // 如果有图片需要处理，则调用processMediaFiles方法
            if (!mediaTypeMap.isEmpty()) {
                processMediaFiles(repairTask.getTaskNo(), mediaTypeMap, SessionUtils.getUsername());
            }
        } catch (Exception e) {
            log.error("保存客户直付凭证失败: {}", e.getMessage(), e);
            throw new BusinessException("保存客户直付凭证失败: " + e.getMessage());
        }

        // 更新自费金额
        // TODO 老维修系统使用manualUtils.getAccidentDamage获取accidentDamageDTO
        updateSelfFundedAmount(repairTask.getTaskNo(), null);
    }

    @Override
    public void updateSelfFundedAmount(String taskNo, AccidentDamageDTO accidentDamageDTO) {
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (null == repairTask) {
            throw new BusinessException("任务不存在，更新自费金额失败！");
        }
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(taskNo);
        if (null == workflowInstance) {
            throw new BusinessException("流程实例不存在，更新自费金额失败！");
        }

        if (workflowInstance.getCurrentActivityCode().equals(ActivityDefinitionEnum.PENDING_ASSIGNMENT.getCode())
                || workflowInstance.getCurrentActivityCode().equals(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode())
                || workflowInstance.getCurrentActivityCode()
                        .equals(ActivityDefinitionEnum.PRE_INSPECTION_REVIEW.getCode())
                || workflowInstance.getCurrentActivityCode()
                        .equals(ActivityDefinitionEnum.REPAIR_QUOTATION.getCode())) {
            return;
        }

        // 重新计算自费金额
        RepairAmountDTO repairAmountDTO = new RepairAmountDTO(
                repairTask.getTaskNo(),
                repairTask.getRepairTotalAmount(),
                repairTask.getUserAssumedAmount(),
                repairTask.getEstimatedClaimAmount());
        BigDecimal selfFundedAmount = getSelfFundedAmount(repairAmountDTO, accidentDamageDTO);
        tableRepairTaskService.updateSelfFundedAmount(repairTask.getId(), selfFundedAmount);
        log.info("任务【{}】更新自费金额为：{}", repairTask.getTaskNo(), selfFundedAmount);
    }

    @Override
    public BigDecimal getSelfFundedAmount(RepairAmountDTO repairAmountDTO, AccidentDamageDTO accidentDamageDTO) {
        if (null == repairAmountDTO) {
            throw new BusinessException("数据错误，更新自费金额失败！");
        }
        if (null == repairAmountDTO.getRepairTotalAmount()
                || repairAmountDTO.getRepairTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        if (null != accidentDamageDTO && null != accidentDamageDTO.getVehicleTotalLossDisposalFlag()
                && accidentDamageDTO.getVehicleTotalLossDisposalFlag() == 1) {
            // 【本车全损处置】=是，则【自费金额】=0元
            log.info("重新计算任务【{}】自费金额为：{}", repairAmountDTO.getTaskNo(), "0");
            return BigDecimal.ZERO;
        } else {
            // 【自费金额】=【维修总金额】-【预估保险理赔金额】-【非用户承担金额】
            BigDecimal selfFundedAmount = repairAmountDTO.getRepairTotalAmount()
                    .subtract(repairAmountDTO.getEstimatedClaimAmount())
                    .subtract(repairAmountDTO.getUserAssumedAmount());
            if (selfFundedAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BusinessException("自费金额不能小于0！请确认后重新提交！");
            }
            log.info("任务【{}】更新自费金额为：{}", repairAmountDTO.getTaskNo(), selfFundedAmount);
            return selfFundedAmount;
        }
    }

    @Override
    public BasePageVO<RepairTaskProcessListVO> queryCurrentActivityCodeRepairTaskList(RepairTaskListQueryDTO queryDTO) {
        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            queryDTO.setRepairDepotId(repairDepotId);
        } else {
            // 登录人公司
            queryDTO.setLoginOrgIds(SessionUtils.getAllAccessibleOrgIds());
        }

        // 使用PageHelper进行分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        // 1.查询数据 - 使用优化后的方法
        List<RepairTaskProcessListVO> list = tableRepairTaskService.queryRepairTaskListByWorkflowInstance(queryDTO,
                SessionUtils.getTenantId().intValue());
        if (CollectionUtils.isNotEmpty(list)) {
            // 查询所有活动节点定义
            List<ActivityDefinition> activityDefinitions = tableActivityDefinitionService.findAll();
            // 查询所有活动状态定义
            List<ActivityStatus> activityStatuses = tableActivityStatusService.findAll();
            for (RepairTaskProcessListVO repairTaskProcessListVO : list) {
                // 设置活动节点名称
                setActivityNameByCode(repairTaskProcessListVO, activityDefinitions);
                // 设置活动状态名称
                setStatusNameByCode(repairTaskProcessListVO, activityStatuses);
            }
        }
        PageInfo<RepairTaskProcessListVO> pageInfo = new PageInfo<>(list);
        return BasePageVO.of(list, pageInfo);
    }
}
