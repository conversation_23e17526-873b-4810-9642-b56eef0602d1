package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.vo.base.ComboVO;

import java.util.List;

/**
 * 地区数据服务接口
 * 
 * <AUTHOR>
 * @date 2024/06/03
 */
public interface RegionService {

    /**
     * 获取所有省份下拉框数据
     * 
     * @return 省份下拉框列表，key为省份ID，value为省份名称
     */
    List<ComboVO<Long>> getProvinceCombo();

    /**
     * 根据省份ID获取城市下拉框数据
     * 
     * @param provinceId 省份ID
     * @return 城市下拉框列表，key为城市ID，value为城市名称
     */
    List<ComboVO<Long>> getCityCombo(Long provinceId);

    /**
     * 根据城市ID获取区域下拉框数据
     * 
     * @param cityId 城市ID
     * @return 区域下拉框列表，key为区域ID，value为区域名称
     */
    List<ComboVO<Long>> getAreaCombo(Long cityId);
}
