package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.TenantDTO;
import com.extracme.saas.autocare.model.dto.TenantQueryDTO;
import com.extracme.saas.autocare.model.dto.TenantUpdateDTO;
import com.extracme.saas.autocare.model.vo.TenantVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

import java.util.List;

/**
 * 租户服务接口
 */
public interface TenantService {

    /**
     * 创建租户
     *
     * @param tenantDTO 租户创建DTO
     * @return 租户ID
     */
    Long createTenant(TenantDTO tenantDTO);

    /**
     * 更新租户
     *
     * @param tenantUpdateDTO 租户更新DTO
     */
    void updateTenant(TenantUpdateDTO tenantUpdateDTO);

    /**
     * 获取租户详情
     *
     * @param id 租户ID
     * @return 租户详情
     */
    TenantVO getTenant(Long id);

    /**
     * 分页查询租户列表
     *
     * @param queryDTO 查询条件
     * @return 租户列表
     */
    BasePageVO<TenantVO> getTenantList(TenantQueryDTO queryDTO);

    /**
     * 获取租户下拉列表
     * 返回所有有效的租户，用于下拉框选择
     *
     * @return 租户下拉列表数据，ID类型为Long，value为租户名称，remark为租户编码
     */
    List<ComboVO<Long>> getTenantCombo();
}
