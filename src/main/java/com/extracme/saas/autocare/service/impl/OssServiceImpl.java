package com.extracme.saas.autocare.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.model.*;
import com.extracme.saas.autocare.config.OssConfig;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.MultipartUploadCompleteResult;
import com.extracme.saas.autocare.model.dto.MultipartUploadInitResult;
import com.extracme.saas.autocare.model.dto.PartETagInfo;
import com.extracme.saas.autocare.model.dto.ResumableUploadInitDTO;
import com.extracme.saas.autocare.model.dto.ResumableUploadSession;
import com.extracme.saas.autocare.model.dto.StsCredentials;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.FileUploadResultVO;
import com.extracme.saas.autocare.service.OssService;
import com.extracme.saas.autocare.service.StsCredentialService;
import com.extracme.saas.autocare.util.OssDebugUtil;
import com.extracme.saas.autocare.util.RedisUtils;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.util.SpringContextUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * OSS 断点续传服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OssServiceImpl implements OssService {

    private final OssConfig ossConfig;
    private final StsCredentialService stsCredentialService;
    private final RedisUtils redisUtils;

    // Redis 缓存键前缀和过期时间常量
    private static final String UPLOAD_SESSION_KEY_PREFIX = "upload:session:";
    private static final long UPLOAD_SESSION_EXPIRE_SECONDS = 24 * 60 * 60; // 24小时

    // OSS客户端缓存（根据STS凭证动态创建）
    private volatile OSS cachedOssClient;
    private volatile StsCredentials cachedCredentials;

    @Override
    public FileUploadResultVO uploadSimpleFile(MultipartFile file, String category) {
        try {
            log.info("开始普通文件上传: fileName={}, fileSize={}", file.getOriginalFilename(), file.getSize());

            // 验证文件
            validateSimpleFile(file);

            // 生成文件路径
            String relativePath = generateFilePath(file.getOriginalFilename(), category);

            // 直接上传到OSS
            try (java.io.InputStream inputStream = file.getInputStream()) {
                PutObjectRequest putRequest = new PutObjectRequest(ossConfig.getBucketName(), relativePath, inputStream);

                // 设置文件元数据
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentLength(file.getSize());
                if (StringUtils.hasText(file.getContentType())) {
                    metadata.setContentType(file.getContentType());
                }
                putRequest.setMetadata(metadata);

                PutObjectResult putResult = getOssClient().putObject(putRequest);

                log.info("普通文件上传成功: relativePath={}, eTag={}", relativePath, putResult.getETag());
            }

            // 构建返回结果
            FileUploadResultVO result = buildUploadResult(file, relativePath);

            log.info("普通文件上传完成: fileName={}, fullUrl={}", file.getOriginalFilename(), result.getFullUrl());

            return result;
        } catch (Exception e) {
            log.error("普通文件上传失败: fileName={}", file.getOriginalFilename(), e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public FileUploadResultVO uploadLargeSimpleFile(MultipartFile file, String category) {
        try {
            log.info("开始普通文件上传: fileName={}, fileSize={}", file.getOriginalFilename(), file.getSize());

            // 验证文件
            validateLargeSimpleFile(file);

            // 生成文件路径
            String relativePath = generateFilePath(file.getOriginalFilename(), category);

            // 直接上传到OSS
            try (java.io.InputStream inputStream = file.getInputStream()) {
                PutObjectRequest putRequest = new PutObjectRequest(ossConfig.getBucketName(), relativePath, inputStream);

                // 设置文件元数据
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentLength(file.getSize());
                if (StringUtils.hasText(file.getContentType())) {
                    metadata.setContentType(file.getContentType());
                }
                putRequest.setMetadata(metadata);

                PutObjectResult putResult = getOssClient().putObject(putRequest);

                log.info("普通文件上传成功: relativePath={}, eTag={}", relativePath, putResult.getETag());
            }

            // 构建返回结果
            FileUploadResultVO result = buildUploadResult(file, relativePath);

            log.info("普通文件上传完成: fileName={}, fullUrl={}", file.getOriginalFilename(), result.getFullUrl());

            return result;
        } catch (Exception e) {
            log.error("普通文件上传失败: fileName={}", file.getOriginalFilename(), e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public List<FileUploadResultVO> uploadSimpleFiles(List<MultipartFile> files, String category) {
        if (files == null || files.isEmpty()) {
            throw new BusinessException("上传文件列表不能为空");
        }

        log.info("开始批量文件上传: 文件数量={}", files.size());
        
        List<FileUploadResultVO> results = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();
        
        for (MultipartFile file : files) {
            try {
                // 对每个文件调用单文件上传方法
                FileUploadResultVO result = uploadSimpleFile(file, category);
                results.add(result);
            } catch (Exception e) {
                // 记录失败的文件
                String fileName = file.getOriginalFilename() != null ? file.getOriginalFilename() : "未知文件";
                failedFiles.add(fileName);
                log.error("批量上传中的文件上传失败: fileName={}", fileName, e);
            }
        }
        
        // 检查是否有文件上传失败
        if (!failedFiles.isEmpty()) {
            log.warn("批量上传部分文件失败: 成功={}, 失败={}, 失败文件={}", 
                    results.size(), failedFiles.size(), String.join(", ", failedFiles));
            
            // 如果全部失败，则抛出异常
            if (results.isEmpty()) {
                throw new BusinessException("所有文件上传失败");
            }
        }
        
        log.info("批量文件上传完成: 总数={}, 成功={}, 失败={}", 
                files.size(), results.size(), failedFiles.size());
        
        return results;
    }

    @Override
    public MultipartUploadInitResult initializeResumableUpload(ResumableUploadInitDTO initDTO) {
        try {
            log.info("初始化断点续传上传: fileName={}, fileSize={}",
                initDTO.getOriginalFileName(), initDTO.getFileSize());

            // 验证参数
            validateInitDTO(initDTO);

            // 生成文件路径
            String relativePath = generateFilePath(initDTO.getOriginalFileName(), initDTO.getCategory());

            // 初始化OSS分片上传
            InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(
                ossConfig.getBucketName(), relativePath);
            InitiateMultipartUploadResult initResult = getOssClient().initiateMultipartUpload(initRequest);

            // 获取OSS返回的真实uploadId
            String ossUploadId = initResult.getUploadId();
            log.debug("OSS分片上传初始化成功: ossUploadId={}, relativePath={}", ossUploadId, relativePath);

            // 生成内部会话ID
            String sessionId = generateUploadId();

            // 创建上传会话 - 关键修复：分别保存会话ID和OSS uploadId
            ResumableUploadSession session = new ResumableUploadSession(sessionId, ossUploadId, relativePath, ossConfig.getBucketName());
            session.setOriginalFileName(initDTO.getOriginalFileName());
            session.setFileSize(initDTO.getFileSize());
            session.setContentType(initDTO.getContentType());
            session.setCategory(initDTO.getCategory());
            session.setMd5(initDTO.getMd5());
            session.setChunkSize(initDTO.getChunkSize());
            session.setTenantId(getCurrentTenantId());
            session.setTenantCode(getTenantCode());

            // 保存会话信息到 Redis - 使用内部会话ID作为key
            saveUploadSession(sessionId, session);

            // 构建返回结果 - 返回内部会话ID给前端
            MultipartUploadInitResult result = new MultipartUploadInitResult(
                sessionId, relativePath, ossConfig.getBucketName());

            log.info("断点续传初始化成功: sessionId={}, ossUploadId={}, relativePath={}",
                sessionId, ossUploadId, relativePath);

            return result;
        } catch (Exception e) {
            log.error("初始化断点续传上传失败: {}", initDTO.getOriginalFileName(), e);
            throw new BusinessException("初始化断点续传上传失败: " + e.getMessage());
        }
    }

    @Override
    public String generatePresignedPartUploadUrl(String uploadId, int partNumber, long expiration) {
        try {
            log.debug("生成分片上传预签名URL: uploadId={}, partNumber={}, expiration={}s",
                uploadId, partNumber, expiration);

            // 验证参数
            if (!StringUtils.hasText(uploadId)) {
                throw new BusinessException("uploadId不能为空");
            }
            if (partNumber < 1 || partNumber > 10000) {
                throw new BusinessException("分片编号必须在1-10000之间");
            }

            // 从 Redis 获取上传会话信息
            ResumableUploadSession session = getUploadSession(uploadId);
            if (session == null) {
                throw new BusinessException("上传会话不存在或已过期: " + uploadId);
            }

            // 验证OSS配置
            validateOssConfig();

            // 验证OSS Upload ID
            if (!OssDebugUtil.validateUploadId(session.getOssUploadId())) {
                throw new BusinessException("无效的OSS Upload ID: " + session.getOssUploadId());
            }

            // 获取OSS客户端（支持STS凭证）
            OSS ossClient = getOssClient();

            // 在开发环境启用详细调试
            if (log.isDebugEnabled()) {
                OssDebugUtil.validateOssConfig(ossConfig, ossClient);
                OssDebugUtil.debugPresignedUrl(ossClient, session.getBucketName(),
                    session.getRelativePath(), session.getOssUploadId(), partNumber, expiration);
            }

            // 计算过期时间
            Date expirationDate = new Date(System.currentTimeMillis() + expiration * 1000);

            // 构造预签名URL请求 - 关键修复：使用OSS的真实uploadId
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                session.getBucketName(), session.getRelativePath(), HttpMethod.PUT);
            request.setExpiration(expirationDate);

            // 重要：使用OSS返回的真实uploadId，而不是我们的会话ID
            request.addQueryParameter("partNumber", String.valueOf(partNumber));
            request.addQueryParameter("uploadId", session.getOssUploadId()); // 修复：使用OSS的uploadId

            // 🔧 关键修复：处理前端Content-Type自动添加问题
            // 前端XMLHttpRequest会根据Blob类型自动添加Content-Type请求头
            // 为了确保签名一致，我们需要在生成预签名URL时考虑这个请求头

            // 根据文件类型确定Content-Type
            String contentType = determineContentTypeForPresignedUrl(session);
            if (contentType != null) {
                Map<String, String> headers = new HashMap<>();
                headers.put(OSSHeaders.CONTENT_TYPE, contentType);
                request.setHeaders(headers);

                log.debug("为预签名URL设置Content-Type: {}", contentType);
            } else {
                log.debug("未设置Content-Type，让前端浏览器自动处理");
            }


            log.debug("构造预签名URL请求: bucket={}, key={}, method={}, partNumber={}, ossUploadId={}",
                session.getBucketName(), session.getRelativePath(), HttpMethod.PUT,
                partNumber, session.getOssUploadId());

            // 使用STS凭证生成预签名URL
            URL url = ossClient.generatePresignedUrl(request);
            String presignedUrl = url.toString();

            // 记录STS凭证信息（用于调试）
            if (ossConfig.isStsEnabled() && log.isDebugEnabled()) {
                StsCredentials credentials = stsCredentialService.getValidCredentials();
                log.debug("使用STS凭证生成预签名URL: accessKeyId={}, remainingMinutes={}",
                    credentials.getAccessKeyId().substring(0, 8) + "***",
                    credentials.getRemainingMinutes());
            }

            log.debug("分片上传预签名URL生成成功: partNumber={}, url={}", partNumber, presignedUrl);

            return presignedUrl;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            // 记录详细的错误信息
            OssDebugUtil.logDetailedError("生成分片上传预签名URL", e);

            log.error("生成分片上传预签名URL失败: uploadId={}, partNumber={}, error={}",
                uploadId, partNumber, e.getMessage(), e);
            throw new BusinessException("生成分片上传预签名URL失败: " + e.getMessage());
        }
    }

    @Override
    public MultipartUploadCompleteResult completeMultipartUpload(String uploadId, List<PartETagInfo> partETags) {
        try {
            log.info("完成分片上传: uploadId={}, partCount={}", uploadId, partETags.size());

            // 从 Redis 获取上传会话信息
            ResumableUploadSession session = getUploadSession(uploadId);
            if (session == null) {
                throw new BusinessException("上传会话不存在或已过期: " + uploadId);
            }

            // 转换PartETagInfo为OSS的PartETag
            List<PartETag> ossPartETags = new ArrayList<>();
            for (PartETagInfo partETagInfo : partETags) {
                ossPartETags.add(new PartETag(partETagInfo.getPartNumber(), partETagInfo.getETag()));
            }

            // 使用OSS的真实uploadId完成分片上传
            CompleteMultipartUploadRequest completeRequest = new CompleteMultipartUploadRequest(
                session.getBucketName(), session.getRelativePath(), session.getOssUploadId(), ossPartETags);
            CompleteMultipartUploadResult completeResult = getOssClient().completeMultipartUpload(completeRequest);

            // 构建返回结果
            MultipartUploadCompleteResult result = new MultipartUploadCompleteResult(
                session.getRelativePath(),
                getFileUrl(session.getRelativePath()),
                session.getBucketName(),
                completeResult.getETag()
            );

            // 计算总大小和分片数
            long totalSize = partETags.stream()
                .mapToLong(part -> part.getPartSize() != null ? part.getPartSize() : 0L)
                .sum();
            result.setTotalSize(totalSize);
            result.setTotalParts(partETags.size());
            result.setTenantCode(session.getTenantCode());

            // 从 Redis 清理会话信息
            removeUploadSession(uploadId);

            log.info("分片上传完成成功: relativePath={}, totalSize={}, totalParts={}",
                session.getRelativePath(), totalSize, partETags.size());

            return result;
        } catch (Exception e) {
            log.error("完成分片上传失败: uploadId={}", uploadId, e);
            throw new BusinessException("完成分片上传失败: " + e.getMessage());
        }
    }

    @Override
    public void abortMultipartUpload(String uploadId) {
        try {
            log.info("取消分片上传: uploadId={}", uploadId);

            // 从 Redis 获取上传会话信息
            ResumableUploadSession session = getUploadSession(uploadId);
            if (session == null) {
                log.warn("上传会话不存在，可能已被清理: {}", uploadId);
                return;
            }

            // 使用OSS的真实uploadId取消分片上传
            AbortMultipartUploadRequest abortRequest = new AbortMultipartUploadRequest(
                session.getBucketName(), session.getRelativePath(), session.getOssUploadId());
            getOssClient().abortMultipartUpload(abortRequest);

            // 从 Redis 清理会话信息
            removeUploadSession(uploadId);

            log.info("分片上传取消成功: uploadId={}", uploadId);
        } catch (Exception e) {
            log.error("取消分片上传失败: uploadId={}", uploadId, e);
            throw new BusinessException("取消分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取OSS客户端（支持STS临时凭证）
     */
    private OSS getOssClient() {
        if (!ossConfig.isStsEnabled()) {
            // 如果未启用STS，使用传统方式创建客户端
            return new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret()
            );
        }

        // 使用STS临时凭证
        StsCredentials currentCredentials = stsCredentialService.getValidCredentials();

        // 如果凭证发生变化，重新创建客户端
        if (cachedOssClient == null || !isSameCredentials(cachedCredentials, currentCredentials)) {
            synchronized (this) {
                // 双重检查锁定
                if (cachedOssClient == null || !isSameCredentials(cachedCredentials, currentCredentials)) {
                    // 关闭旧客户端
                    if (cachedOssClient != null) {
                        try {
                            cachedOssClient.shutdown();
                        } catch (Exception e) {
                            log.warn("关闭旧OSS客户端失败", e);
                        }
                    }

                    // 创建新的OSS客户端
                    cachedOssClient = new OSSClientBuilder().build(
                        ossConfig.getEndpoint(),
                        currentCredentials.getAccessKeyId(),
                        currentCredentials.getAccessKeySecret(),
                        currentCredentials.getSecurityToken()
                    );

                    cachedCredentials = currentCredentials;

                    log.debug("创建新的OSS客户端: accessKeyId={}, expiration={}",
                        currentCredentials.getAccessKeyId().substring(0, 8) + "***",
                        currentCredentials.getExpiration());
                }
            }
        }

        return cachedOssClient;
    }

    /**
     * 检查STS凭证是否相同
     */
    private boolean isSameCredentials(StsCredentials cached, StsCredentials current) {
        if (cached == null || current == null) {
            return false;
        }
        return Objects.equals(cached.getAccessKeyId(), current.getAccessKeyId()) &&
               Objects.equals(cached.getSecurityToken(), current.getSecurityToken());
    }

    /**
     * 验证普通文件上传参数
     */
    private void validateSimpleFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }

        if (!StringUtils.hasText(file.getOriginalFilename())) {
            throw new BusinessException("文件名不能为空");
        }

        // 检查文件大小限制（10MB）
        long maxSize = 10 * 1024 * 1024L;
        if (file.getSize() > maxSize) {
            throw new BusinessException("文件大小不能超过10MB，大文件请使用断点续传接口");
        }

        // 检查文件类型
        String extension = getFileExtension(file.getOriginalFilename()).toLowerCase();
        List<String> allowedExtensions = Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "pdf", "doc", "docx",
            "xls", "xlsx", "ppt", "pptx", "txt", "zip", "rar"
        );
        if (!allowedExtensions.contains(extension)) {
            throw new BusinessException("不支持的文件类型: " + extension);
        }
    }
    
    /**
     * 验证普通文件上传参数
     */
    private void validateLargeSimpleFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }

        if (!StringUtils.hasText(file.getOriginalFilename())) {
            throw new BusinessException("文件名不能为空");
        }

        // 检查文件大小限制（50MB）
        long maxSize = 50 * 1024 * 1024L;
        if (file.getSize() > maxSize) {
            throw new BusinessException("文件大小不能超过10MB，大文件请使用断点续传接口");
        }

        // 检查文件类型
        String extension = getFileExtension(file.getOriginalFilename()).toLowerCase();
        List<String> allowedExtensions = Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "pdf", "doc", "docx",
            "xls", "xlsx", "ppt", "pptx", "txt", "zip", "rar", "mp4", "mov", "mkv"
        );
        if (!allowedExtensions.contains(extension)) {
            throw new BusinessException("不支持的文件类型: " + extension);
        }
    }

    /**
     * 构建文件上传结果
     */
    private FileUploadResultVO buildUploadResult(MultipartFile file, String relativePath) {
        FileUploadResultVO result = new FileUploadResultVO();
        result.setFileName(getFileNameFromPath(relativePath));
        result.setOriginalFileName(file.getOriginalFilename());
        result.setRelativePath(relativePath);
        result.setFullUrl(getFileUrl(relativePath));
        result.setFileSize(file.getSize());
        result.setContentType(file.getContentType());
        result.setFileExtension(getFileExtension(file.getOriginalFilename()));
        result.setUploadTime(LocalDateTime.now());
        result.setTenantCode(getTenantCode());
        result.setIsResumable(false);
        result.setProgress(100);

        // 设置上传用户信息
        SysUser currentUser = getCurrentUser();
        if (currentUser != null) {
            result.setUploadUserId(currentUser.getId());
            result.setUploadUserName(currentUser.getNickname() != null ?
                currentUser.getNickname() : currentUser.getUsername());
        }

        return result;
    }

    /**
     * 验证初始化参数
     */
    private void validateInitDTO(ResumableUploadInitDTO initDTO) {
        if (!StringUtils.hasText(initDTO.getOriginalFileName())) {
            throw new BusinessException("文件名不能为空");
        }

        if (initDTO.getFileSize() == null || initDTO.getFileSize() <= 0) {
            throw new BusinessException("文件大小必须大于0");
        }

        // 检查文件大小限制（100MB）
        long maxSize = 100 * 1024 * 1024L;
        if (initDTO.getFileSize() > maxSize) {
            throw new BusinessException("文件大小不能超过100MB");
        }

        // 检查文件类型
        String extension = getFileExtension(initDTO.getOriginalFileName()).toLowerCase();
        List<String> allowedExtensions = Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "pdf", "doc", "docx",
            "xls", "xlsx", "ppt", "pptx", "txt", "zip", "rar", "mp4", "mp3", "avi", "mov", "wmv", "flv", "mkv"
        );
        if (!allowedExtensions.contains(extension)) {
            throw new BusinessException("不支持的文件类型: " + extension);
        }
    }

    /**
     * 生成文件路径
     */
    private String generateFilePath(String originalFilename, String category) {
        // 获取租户编码
        String tenantCode = getTenantCode();

        // 生成日期目录
        String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));

        // 生成唯一文件名
        String extension = getFileExtension(originalFilename);
        String uniqueFileName = UUID.randomUUID().toString().replace("-", "") + "." + extension;

        // 构建完整路径
        StringBuilder pathBuilder = new StringBuilder();
        OssConfig ossConfig = SpringContextUtil.getBean(OssConfig.class);
        pathBuilder.append(ossConfig.getUpload().getEnv()).append("/auto-care-saas/").append(tenantCode).append("/uploads/").append(dateDir).append("/");

        if (StringUtils.hasText(category)) {
            pathBuilder.append(category).append("/");
        }

        pathBuilder.append(uniqueFileName);

        return pathBuilder.toString();
    }

    /**
     * 验证OSS配置
     */
    private void validateOssConfig() {
        if (ossConfig == null) {
            throw new BusinessException("OSS配置未初始化");
        }
        if (!StringUtils.hasText(ossConfig.getBucketName())) {
            throw new BusinessException("OSS Bucket名称未配置");
        }

        // 验证STS配置（如果启用）
        if (ossConfig.isStsEnabled()) {
            if (!StringUtils.hasText(ossConfig.getRoleArn())) {
                throw new BusinessException("STS配置错误: roleArn不能为空");
            }
            if (stsCredentialService == null) {
                throw new BusinessException("STS凭证服务未初始化");
            }
        } else {
            // 验证传统配置
            if (!StringUtils.hasText(ossConfig.getAccessKeyId())) {
                throw new BusinessException("OSS配置错误: accessKeyId不能为空");
            }
            if (!StringUtils.hasText(ossConfig.getAccessKeySecret())) {
                throw new BusinessException("OSS配置错误: accessKeySecret不能为空");
            }
        }
    }

    /**
     * 获取文件访问URL
     */
    private String getFileUrl(String relativePath) {
        return ossConfig.getUpload().getBaseUrl() + "/" + relativePath;
    }

    /**
     * 获取当前租户ID
     */
    private Long getCurrentTenantId() {
        try {
            SysUser currentUser = getCurrentUser();
            return currentUser != null ? currentUser.getTenantId() : null;
        } catch (Exception e) {
            log.warn("获取当前租户ID失败", e);
            return null;
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    /**
     * 从路径中获取文件名
     */
    private String getFileNameFromPath(String path) {
        if (!StringUtils.hasText(path)) {
            return "";
        }
        int lastSlashIndex = path.lastIndexOf('/');
        return lastSlashIndex >= 0 ? path.substring(lastSlashIndex + 1) : path;
    }

    /**
     * 获取租户编码
     * 直接从SessionUtils中获取租户编码
     */
    private String getTenantCode() {
        return SessionUtils.getTenantCode();
    }

    /**
     * 生成内部上传ID
     */
    private String generateUploadId() {
        return "upload-" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 获取当前用户
     */
    private SysUser getCurrentUser() {
        try {
            return SessionUtils.getLoginUser() != null ? SessionUtils.getLoginUser().getUser() : null;
        } catch (Exception e) {
            log.warn("获取当前用户失败", e);
            return null;
        }
    }

    /**
     * 为预签名URL确定Content-Type
     * 解决前端XMLHttpRequest自动添加Content-Type导致的签名不匹配问题
     */
    private String determineContentTypeForPresignedUrl(ResumableUploadSession session) {
        // 策略1：如果会话中有明确的Content-Type，使用它
        if (StringUtils.hasText(session.getContentType())) {
            log.debug("使用会话中的Content-Type: {}", session.getContentType());
            return session.getContentType();
        }

        // 策略2：根据文件扩展名推断Content-Type
        // 这样可以确保与前端浏览器的行为一致
        String fileName = session.getOriginalFileName();
        if (StringUtils.hasText(fileName)) {
            String extension = getFileExtension(fileName).toLowerCase();
            String inferredContentType = inferContentTypeFromExtension(extension);
            if (inferredContentType != null) {
                log.debug("根据文件扩展名推断Content-Type: {} -> {}", extension, inferredContentType);
                return inferredContentType;
            }
        }

        // 策略3：对于未知类型，返回null让浏览器自动处理
        // 这样可以避免Content-Type不匹配的问题
        log.debug("无法确定Content-Type，让浏览器自动处理");
        return null;
    }

    /**
     * 根据文件扩展名推断Content-Type
     * 与前端浏览器的行为保持一致
     */
    private String inferContentTypeFromExtension(String extension) {
        switch (extension) {
            // 图片类型
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";

            // 文档类型
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";

            // 文本类型
            case "txt":
                return "text/plain";

            // 压缩文件
            case "zip":
                return "application/zip";
            case "rar":
                return "application/x-rar-compressed";

            // 默认情况：返回null让浏览器自动处理
            default:
                return null;
        }
    }

    // ============================Redis 会话管理方法=============================

    /**
     * 生成 Redis 缓存键
     */
    private String generateSessionKey(String uploadId) {
        return UPLOAD_SESSION_KEY_PREFIX + uploadId;
    }

    /**
     * 保存上传会话到 Redis
     */
    private void saveUploadSession(String uploadId, ResumableUploadSession session) {
        try {
            String key = generateSessionKey(uploadId);
            boolean success = redisUtils.set(key, session, UPLOAD_SESSION_EXPIRE_SECONDS);
            if (!success) {
                log.warn("保存上传会话到Redis失败，尝试使用内存缓存作为降级方案: uploadId={}", uploadId);
                // 这里可以考虑添加内存缓存作为降级方案
                throw new BusinessException("保存上传会话失败");
            }
            log.debug("上传会话已保存到Redis: uploadId={}, key={}", uploadId, key);
        } catch (Exception e) {
            log.error("保存上传会话到Redis异常: uploadId={}", uploadId, e);
            throw new BusinessException("保存上传会话失败: " + e.getMessage());
        }
    }

    /**
     * 从 Redis 获取上传会话
     */
    private ResumableUploadSession getUploadSession(String uploadId) {
        try {
            String key = generateSessionKey(uploadId);
            Object sessionObj = redisUtils.get(key);
            if (sessionObj == null) {
                log.debug("Redis中未找到上传会话: uploadId={}, key={}", uploadId, key);
                return null;
            }

            if (sessionObj instanceof ResumableUploadSession) {
                ResumableUploadSession session = (ResumableUploadSession) sessionObj;
                log.debug("从Redis获取上传会话成功: uploadId={}, ossUploadId={}", uploadId, session.getOssUploadId());
                return session;
            } else {
                log.warn("Redis中的会话数据类型不正确: uploadId={}, type={}", uploadId, sessionObj.getClass().getName());
                return null;
            }
        } catch (Exception e) {
            log.error("从Redis获取上传会话异常: uploadId={}", uploadId, e);
            // Redis 异常时返回 null，让上层处理
            return null;
        }
    }

    /**
     * 从 Redis 删除上传会话
     */
    private void removeUploadSession(String uploadId) {
        try {
            String key = generateSessionKey(uploadId);
            redisUtils.del(key);
            log.debug("已从Redis删除上传会话: uploadId={}, key={}", uploadId, key);
        } catch (Exception e) {
            log.error("从Redis删除上传会话异常: uploadId={}", uploadId, e);
            // 删除失败不影响主流程，只记录日志
        }
    }

    @Override
    public boolean deleteFile(String relativePath) {
        try {
            log.info("开始删除OSS文件: relativePath={}", relativePath);
            
            // 验证参数
            if (!StringUtils.hasText(relativePath)) {
                log.error("删除OSS文件失败: 文件路径不能为空");
                throw new BusinessException("文件路径不能为空");
            }

            // 验证OSS配置
            validateOssConfig();
            
            // 获取OSS客户端Client = getOssClient();
            OSS ossClient = getOssClient();

            // 检查文件是否存在
            boolean exists = ossClient.doesObjectExist(ossConfig.getBucketName(), relativePath);
            if (!exists) {
                log.warn("要删除的OSS文件不存在: bucket={}, relativePath={}", ossConfig.getBucketName(), relativePath);
                return false;
            }
            
            // 执行删除操作
            ossClient.deleteObject(ossConfig.getBucketName(), relativePath);
            
            log.info("OSS文件删除成功: bucket={}, relativePath={}", ossConfig.getBucketName(), relativePath);
            return true;
        } catch (Exception e) {
            log.error("删除OSS文件失败: relativePath={}", relativePath, e);
            return false;
        }
    }

}
