package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.ReplacePartItemCreateDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemQueryDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemUpdateDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemUpdateStatusDTO;
import com.extracme.saas.autocare.model.vo.PartRepairItemGroupingVO;
import com.extracme.saas.autocare.model.vo.ReplacePartItemDetailsVO;
import com.extracme.saas.autocare.model.vo.ReplacePartItemListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

import java.util.List;

/**
 * 修理厂服务接口
 */
public interface ReplacePartItemService {

    /**
     * 分页查询列表
     *
     * @param queryDTO 查询参数
     * @return 换件项目列表
     */
    BasePageVO<ReplacePartItemListVO> queryReplacePartItemList(ReplacePartItemQueryDTO queryDTO);

    /**
     * 获取换件项目详情
     *
     * @param id 换件项目ID
     * @return 换件项目详情
     */
    ReplacePartItemDetailsVO getReplacePartItemDetails(Long id);

    /**
     * 创建非合作换件项目
     *
     * @param createDTO 创建参数
     * @return 换件项目ID
     */
    void createReplacePartItem(ReplacePartItemCreateDTO createDTO);

    /**
     * 修改换件项目
     *
     * @param updateDTO 修改参数
     */
    void updateReplacePartItem(ReplacePartItemUpdateDTO updateDTO);

    /**
     * 删除换件项目
     *
     * @param id 换件项目ID
     */
    void deleteReplacePartItem(Long id);

    /**
     * 更新换件项目状态
     *
     * @param updateStatusDTO 更新参数
     */
    void updateReplacePartItemStatus(ReplacePartItemUpdateStatusDTO updateStatusDTO);


    /**
     * 获取零件修理项目分组数据
     *
     * @param groupingType 分组类型 1:零件分组 2:修理项目分组
     * @return 零件修理项目分组数据
     */
    List<PartRepairItemGroupingVO> queryItemGroupingTree(Integer groupingType);
}