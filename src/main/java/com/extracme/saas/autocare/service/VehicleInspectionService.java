package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.VehicleInspectionApproveDTO;
import com.extracme.saas.autocare.model.dto.VehicleInspectionRejectDTO;

/**
 * 车辆验收服务接口
 * 提供车辆维修完成后的验收相关功能
 * 
 * <AUTHOR> Code
 * @date 2024/05/25
 */
public interface VehicleInspectionService {
    
    /**
     * 验收通过
     * 处理车辆维修任务的验收通过流程
     *
     * @param approveDTO 验收通过信息DTO
     */
    void approveInspection(VehicleInspectionApproveDTO approveDTO);
    
    /**
     * 验收不通过
     * 处理车辆维修任务的验收不通过流程
     *
     * @param rejectDTO 验收不通过信息DTO
     */
    void rejectInspection(VehicleInspectionRejectDTO rejectDTO);
}