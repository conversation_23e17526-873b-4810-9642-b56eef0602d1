package com.extracme.saas.autocare.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.extracme.saas.autocare.model.dto.FileDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.enums.RepairPicTypeEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.VehicleInRepairInspectionDTO;
import com.extracme.saas.autocare.model.dto.VehicleInRepairUpdateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.VehicleInRepairService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 车辆维修服务实现类
 */
@Slf4j
@Service
public class VehicleInRepairServiceImpl implements VehicleInRepairService {

    @Autowired
    private RepairTaskService repairTaskService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private WorkflowService workflowService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveVehicleRepair(VehicleInRepairUpdateDTO vehicleRepairDTO) {
        log.info("保存车辆维修信息: {}", vehicleRepairDTO);

        // 获取维修任务
        MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(vehicleRepairDTO.getTaskNo());
        if (mtcRepairTask == null) {
            throw new BusinessException("未找到维修任务: " + vehicleRepairDTO.getTaskNo());
        }

        // 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(vehicleRepairDTO.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + vehicleRepairDTO.getTaskNo());
        }
        
        // 验证当前节点是否为维修施工节点
        if (!workflowInstance.getCurrentActivityCode().equals("IN_REPAIR")
                && !workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new BusinessException("维修任务节点状态不是维修施工中, 无法保存维修信息: " + vehicleRepairDTO.getTaskNo());
        }

        // 更新维修任务信息
        RepairTaskUpdateDTO updateDTO = new RepairTaskUpdateDTO();
        BeanUtils.copyProperties(vehicleRepairDTO, updateDTO);
        
        // 保存维修任务
        repairTaskService.saveRepairTask(mtcRepairTask.getId(), updateDTO);

        // 保存图片逻辑
        saveVehicleRepairImages(vehicleRepairDTO);
    }

    /**
     * 保存车辆维修相关图片
     * 
     * @param vehicleRepairDTO 车辆维修DTO
     * @throws BusinessException 业务异常
     */
    private void saveVehicleRepairImages(VehicleInRepairUpdateDTO vehicleRepairDTO) {
        try {
            // 构建媒体类型映射
            Map<Integer, List<FileDTO>> mediaTypeMap = new HashMap<>();
            
            // 添加验收申请图片到映射中
            if (vehicleRepairDTO.getRepairPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.REPAIR_PICTURE.getTypeId(), vehicleRepairDTO.getRepairPicture());
            }
            
            // 添加验收申请视频到映射中
            if (vehicleRepairDTO.getCheckVideo() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.CHECK_VIDEO.getTypeId(), vehicleRepairDTO.getCheckVideo());
            }
            
            // 如果有图片需要处理，则调用processMediaFiles方法
            if (!mediaTypeMap.isEmpty()) {
                String operatorName = SessionUtils.getUsername();
                repairTaskService.processMediaFiles(vehicleRepairDTO.getTaskNo(), mediaTypeMap, operatorName);
                log.info("车辆维修图片保存成功，任务编号: {}", vehicleRepairDTO.getTaskNo());
            }
        } catch (Exception e) {
            log.error("保存车辆维修图片失败: {}", e.getMessage(), e);
            throw new BusinessException("保存车辆维修图片失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitVehicleRepairInspection(VehicleInRepairInspectionDTO inspectionDTO) {
        log.info("申请车辆维修验收: {}", inspectionDTO);

        try {
            // 1. 保存最新的维修信息
            saveVehicleRepair(inspectionDTO);
            
            // 2. 获取工作流实例
            WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(inspectionDTO.getTaskNo());
            if (workflowInstance == null) {
                throw new BusinessException("未找到工作流实例: " + inspectionDTO.getTaskNo());
            }
            
            // 4. 调用工作流服务处理节点
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("REQUEST_FOR_INSPECTION");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
            processDTO.setOperator(SessionUtils.getUsername());
            
            workflowService.processNode(workflowInstance.getId(), processDTO);
            
            log.info("申请车辆维修验收成功，任务编号: {}", inspectionDTO.getTaskNo());
        } catch (BusinessException e) {
            log.error("申请车辆维修验收失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("申请车辆维修验收失败: {}", e.getMessage(), e);
            throw new BusinessException("申请车辆维修验收失败: " + e.getMessage());
        }
    }
}