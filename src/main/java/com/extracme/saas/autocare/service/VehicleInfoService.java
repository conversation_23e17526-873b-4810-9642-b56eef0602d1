package com.extracme.saas.autocare.service;

import java.util.List;

import com.extracme.saas.autocare.model.vo.VehicleInfoVO;

/**
 * 车辆信息服务接口
 */
public interface VehicleInfoService {

    /**
     * 根据车架号查询车辆详情
     *
     * @param vin 车架号
     * @return 车辆详情
     */
    VehicleInfoVO getVehicleInfoByVin(String vin);

    /**
     * 根据车架号或车牌号模糊查询车辆信息列表
     *
     * @param vin 车架号
     * @param vehicleNo 车牌号
     * @return 车辆信息列表，最多返回20条数据
     */
    List<VehicleInfoVO> getVehicleInfoList(String vin, String vehicleNo);
}
