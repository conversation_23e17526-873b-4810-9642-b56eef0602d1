package com.extracme.saas.autocare.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.exception.UserException;
import com.extracme.saas.autocare.model.dto.MerchantAdminQueryDTO;
import com.extracme.saas.autocare.model.dto.UserCreateDTO;
import com.extracme.saas.autocare.model.dto.UserQueryDTO;
import com.extracme.saas.autocare.model.dto.UserUpdateDTO;
import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.entity.SysUserOrg;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotInfo;
import com.extracme.saas.autocare.model.vo.MerchantAdminVO;
import com.extracme.saas.autocare.model.vo.UserVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.repository.TableRepairDepotInfoService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.UserService;
import com.extracme.saas.autocare.util.OperateLogUtil;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final TableUserService tableUserService;
    private final TableRoleService tableRoleService;
    private final TableUserRoleService tableUserRoleService;
    private final TableUserOrgService tableUserOrgService;
    private final TableOrgInfoService tableOrgInfoService;
    private final TableTenantService tableTenantService;
    private final TableRepairDepotInfoService tableRepairDepotInfoService;
    private final UserPermissionCacheUtils userPermissionCacheUtils;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(UserCreateDTO createDTO) {
        // 验证保险公司ID字段
        validateInsuranceCompanyId(createDTO.getAccountType(), createDTO.getInsuranceCompanyId());

        // 检查手机号是否已存在
        if (tableUserService.countByMobile(createDTO.getMobile()) > 0) {
            throw new UserException(UserException.USER_ALREADY_EXISTS, "手机号已存在");
        }

        // 创建用户实体
        SysUser user = new SysUser();
        user.setUsername(createDTO.getMobile());
        user.setNickname(createDTO.getNickname());
        user.setMobile(createDTO.getMobile());
        user.setEmail(createDTO.getEmail());
        user.setStatus(createDTO.getStatus());
        user.setApprovalLevel(createDTO.getApprovalLevel());
        user.setRepairDepotId(createDTO.getRepairDepotId());
        user.setAccountType(createDTO.getAccountType());
        user.setInsuranceCompanyId(createDTO.getInsuranceCompanyId());
        user.setTenantId(createDTO.getTenantId());

        // 保存用户
        SysUser savedUser = tableUserService.insert(user);
        Long userId = savedUser.getId();

        // 如果包含角色ID列表，创建用户角色关联
        if (createDTO.getRoleIds() != null && !createDTO.getRoleIds().isEmpty()) {
            tableUserRoleService.batchCreate(userId, createDTO.getRoleIds());
        }

        // 处理机构关联
        List<String> orgIds = createDTO.getOrgIds();
        if (orgIds != null && !orgIds.isEmpty()) {
            tableUserOrgService.batchInsert(userId, orgIds);
            log.info("创建用户机构关联: userId={}, orgIds={}", userId, orgIds);
        }

        // 记录操作日志（包含机构关联信息）
        OperateLogUtil.recordUserCreateDetailed(createDTO.getMobile(), createDTO.getNickname(), orgIds);

        return userId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UserUpdateDTO updateDTO) {
        // 验证保险公司ID字段
        validateInsuranceCompanyId(updateDTO.getAccountType(), updateDTO.getInsuranceCompanyId());

        // 检查用户是否存在
        SysUser existingUser = tableUserService.selectById(updateDTO.getId());
        if (existingUser == null) {
            throw new UserException(UserException.USER_NOT_FOUND, "用户不存在");
        }

        // 如果更新手机号，检查新手机号是否已被其他用户使用
        if (updateDTO.getMobile() != null && !updateDTO.getMobile().equals(existingUser.getMobile())) {
            if (tableUserService.countByMobile(updateDTO.getMobile()) > 0) {
                throw new UserException(UserException.USER_ALREADY_EXISTS, "手机号已被其他用户使用");
            }
        }

        // 更新用户信息（不包括状态字段，状态通过专门的启用/禁用接口管理）
        SysUser user = new SysUser();
        user.setId(updateDTO.getId());
        user.setUsername(updateDTO.getMobile());
        user.setNickname(updateDTO.getNickname());
        user.setMobile(updateDTO.getMobile());
        user.setTenantId(updateDTO.getTenantId());
        user.setEmail(updateDTO.getEmail());
        user.setApprovalLevel(updateDTO.getApprovalLevel());
        user.setRepairDepotId(updateDTO.getRepairDepotId());
        user.setAccountType(updateDTO.getAccountType());
        user.setInsuranceCompanyId(updateDTO.getInsuranceCompanyId());

        // 保留原有的审计字段，避免被覆盖为null
        user.setStatus(existingUser.getStatus());
        user.setCreateBy(existingUser.getCreateBy());
        user.setCreatedTime(existingUser.getCreatedTime());

        // 使用完整更新方法，使前端传递的null值能够覆盖数据库中的对应字段
        String operator = SessionUtils.getUsername();
        tableUserService.updateById(user, operator);

        // 如果包含角色ID列表，更新用户角色
        if (updateDTO.getRoleIds() != null) {
            // 更新用户角色关联
            tableUserRoleService.updateUserRoles(updateDTO.getId(), updateDTO.getRoleIds());
            log.info("更新用户角色: userId={}, roleIds={}", updateDTO.getId(), updateDTO.getRoleIds());
        }

        // 处理机构关联更新
        List<String> orgIds = updateDTO.getOrgIds();

        // 处理机构关联更新和日志记录
        List<String> oldOrgIds = null;
        if (orgIds != null) {
            // 获取更新前的机构关联列表
            oldOrgIds = tableUserOrgService.findOrgIdsByUserId(updateDTO.getId());

            // 更新用户机构关联（先删除再插入）
            tableUserOrgService.updateUserOrgs(updateDTO.getId(), orgIds);
            log.info("更新用户机构关联: userId={}, orgIds={}", updateDTO.getId(), orgIds);
        }

        // 记录操作日志 - 使用详细的变更对比记录（包含机构关联变更）
        SysUser updatedUser = buildUpdatedUserForComparison(existingUser, updateDTO);
        OperateLogUtil.recordUserUpdateDetailed(existingUser, updatedUser, oldOrgIds, orgIds);

        // 刷新用户权限缓存（用户信息或组织关联发生变更时）
        try {
            if (userPermissionCacheUtils != null) {
                boolean refreshSuccess = userPermissionCacheUtils.refreshUserPermissions(
                    existingUser.getTenantId(), updateDTO.getId());

                if (refreshSuccess) {
                    log.debug("用户权限缓存刷新成功，用户ID: {}", updateDTO.getId());
                } else {
                    log.warn("用户权限缓存刷新失败，用户ID: {}", updateDTO.getId());
                }
            }
        } catch (Exception e) {
            log.error("刷新用户权限缓存异常，用户ID: {}", updateDTO.getId(), e);
        }
    }

    /**
     * 构建用于日志对比的更新后用户对象
     * @param existingUser 原始用户信息
     * @param updateDTO 更新参数
     * @return 构建后的用户对象
     */
    private SysUser buildUpdatedUserForComparison(SysUser existingUser, UserUpdateDTO updateDTO) {
        SysUser updatedUser = new SysUser();
        BeanUtils.copyProperties(existingUser, updatedUser);
        updatedUser.setNickname(updateDTO.getNickname() != null ? updateDTO.getNickname() : existingUser.getNickname());
        updatedUser.setMobile(updateDTO.getMobile() != null ? updateDTO.getMobile() : existingUser.getMobile());
        updatedUser.setEmail(updateDTO.getEmail() != null ? updateDTO.getEmail() : existingUser.getEmail());
        updatedUser.setApprovalLevel(updateDTO.getApprovalLevel() != null ? updateDTO.getApprovalLevel() : existingUser.getApprovalLevel());
        updatedUser.setRepairDepotId(updateDTO.getRepairDepotId() != null ? updateDTO.getRepairDepotId() : existingUser.getRepairDepotId());
        updatedUser.setAccountType(updateDTO.getAccountType() != null ? updateDTO.getAccountType() : existingUser.getAccountType());
        updatedUser.setInsuranceCompanyId(updateDTO.getInsuranceCompanyId() != null ? updateDTO.getInsuranceCompanyId() : existingUser.getInsuranceCompanyId());
        updatedUser.setTenantId(updateDTO.getTenantId() != null ? updateDTO.getTenantId() : existingUser.getTenantId());
        return updatedUser;
    }

    @Override
    public BasePageVO<UserVO> getUserList(UserQueryDTO queryDTO) {
        // 判断当前用户是否为超级管理员
        boolean isSuperAdmin = SessionUtils.isSuperAdmin();

        // 根据用户类型决定租户ID过滤条件
        Long tenantIdFilter = null;
        if (!isSuperAdmin) {
            // 普通用户只能查询自己所属租户下的用户数据
            LoginUser currentLoginUser = SessionUtils.getLoginUser();
            if (currentLoginUser != null && currentLoginUser.getUser() != null) {
                tenantIdFilter = currentLoginUser.getUser().getTenantId();
            }
        }
        // 超级管理员不设置租户ID过滤条件，可以查询所有租户的用户数据

        // 开启分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 查询用户列表
        List<SysUser> users = tableUserService.findByCondition(
            queryDTO.getNickname(),
            queryDTO.getMobile(),
            queryDTO.getRoleId(),
            tenantIdFilter
        );

        // 获取分页信息
        PageInfo<SysUser> pageInfo = new PageInfo<>(users);

        // 如果没有查询到用户，直接返回空结果
        if (users.isEmpty()) {
            return BasePageVO.of(Collections.emptyList(), pageInfo);
        }

        // 一次性查询所有租户信息，构建租户ID与租户名称的映射关系
        List<SysTenant> allTenants = tableTenantService.findByCondition(null, null, null);
        Map<Long, String> tenantIdToNameMap = allTenants.stream()
            .collect(Collectors.toMap(SysTenant::getId, SysTenant::getTenantName, (existing, replacement) -> existing));

        // 预加载修理厂信息，构建修理厂ID与修理厂名称的映射关系
        Map<String, String> repairDepotNameMap = buildRepairDepotNameMap();

        // 提取所有用户ID
        List<Long> userIds = users.stream()
            .map(SysUser::getId)
            .collect(Collectors.toList());

        // 批量查询所有用户的角色
        Map<Long, List<SysRole>> userRolesMap = batchGetUserRoles(userIds);

        // 批量查询所有用户的机构关联
        Map<Long, List<String>> userOrgsMap = batchGetUserOrgs(userIds);

        // 批量查询机构名称信息
        Map<String, String> orgIdToNameMap = batchGetOrgNames(userOrgsMap);

        // 在内存中组装数据
        List<UserVO> voList = users.stream().map(user -> {
            UserVO vo = new UserVO();
            BeanUtils.copyProperties(user, vo);

            // 从Map中获取租户名称并设置
            if (user.getTenantId() != null) {
                String tenantName = tenantIdToNameMap.get(user.getTenantId());
                vo.setTenantName(tenantName);
            }

            // 从Map中获取修理厂名称并设置
            if (user.getRepairDepotId() != null && !user.getRepairDepotId().isEmpty()) {
                String repairDepotName = getRepairDepotNameFromMap(user, repairDepotNameMap, isSuperAdmin);
                vo.setRepairDepotName(repairDepotName != null ? repairDepotName : "");
            } else {
                vo.setRepairDepotName("");
            }

            // 从Map中获取用户角色
            List<SysRole> roles = userRolesMap.getOrDefault(user.getId(), Collections.emptyList());

            // 提取角色ID列表
            List<Long> roleIds = roles.stream()
                .map(SysRole::getId)
                .collect(Collectors.toList());
            vo.setRoleIds(roleIds);

            // 提取角色名称列表
            List<String> roleNames = roles.stream()
                .map(SysRole::getRoleName)
                .collect(Collectors.toList());
            vo.setRoleNames(roleNames);

            // 从Map中获取用户机构关联
            List<String> userOrgIds = userOrgsMap.getOrDefault(user.getId(), Collections.emptyList());
            vo.setOrgIds(userOrgIds);

            // 设置机构名称信息
            if (userOrgIds != null && !userOrgIds.isEmpty()) {
                // 设置完整的机构名称列表
                List<String> orgNames = userOrgIds.stream()
                    .map(orgId -> orgIdToNameMap.getOrDefault(orgId, orgId))
                    .collect(Collectors.toList());
                vo.setOrgNames(orgNames);
            } else {
                // 清空机构相关字段
                vo.setOrgNames(Collections.emptyList());
            }

            return vo;
        }).collect(Collectors.toList());

        // 构建返回结果
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public UserVO getUserById(Long id) {
        // 查询用户基本信息
        SysUser user = tableUserService.selectById(id);
        if (user == null) {
            throw new UserException(UserException.USER_NOT_FOUND, "用户不存在");
        }

        // 查询用户角色
        List<SysRole> roles = tableRoleService.findByUserId(id);

        // 查询用户关联的机构
        List<String> orgIds = tableUserOrgService.findOrgIdsByUserId(id);

        // 组装用户详情
        UserVO vo = new UserVO();
        BeanUtils.copyProperties(user, vo);

        // 设置租户名称
        if (user.getTenantId() != null) {
            SysTenant tenant = tableTenantService.selectById(user.getTenantId());
            if (tenant != null) {
                vo.setTenantName(tenant.getTenantName());
            }
        }

        // 设置修理厂名称
        if (user.getTenantId() != null && user.getRepairDepotId() != null && !user.getRepairDepotId().isEmpty()) {
            String repairDepotName = getRepairDepotNameByTenantAndDepotId(user.getTenantId(), user.getRepairDepotId());
            vo.setRepairDepotName(repairDepotName != null ? repairDepotName : "");
        } else {
            vo.setRepairDepotName("");
        }



        // 提取角色ID列表
        List<Long> roleIds = roles.stream()
            .map(SysRole::getId)
            .collect(Collectors.toList());
        vo.setRoleIds(roleIds);

        // 提取角色名称列表
        List<String> roleNames = roles.stream()
            .map(SysRole::getRoleName)
            .collect(Collectors.toList());
        vo.setRoleNames(roleNames);

        // 设置机构信息
        vo.setOrgIds(orgIds);

        // 设置机构名称信息
        if (orgIds != null && !orgIds.isEmpty()) {
            // 批量查询机构名称
            Map<String, String> orgIdToNameMap = getOrgIdToNameMap(orgIds);

            // 设置完整的机构名称列表
            List<String> orgNames = orgIds.stream()
                .map(orgId -> orgIdToNameMap.getOrDefault(orgId, orgId))
                .collect(Collectors.toList());
            vo.setOrgNames(orgNames);
        } else {
            // 清空机构相关字段
            vo.setOrgNames(Collections.emptyList());
        }

        return vo;
    }



    /**
     * 批量获取用户角色
     * 一次性查询所有用户的角色，减少数据库访问次数
     *
     * @param userIds 用户ID列表
     * @return 用户ID到角色列表的映射
     */
    private Map<Long, List<SysRole>> batchGetUserRoles(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 创建结果Map
        Map<Long, List<SysRole>> result = new HashMap<>();

        // 初始化每个用户的角色列表为空列表
        userIds.forEach(userId -> result.put(userId, new ArrayList<>()));

        // 使用现有的方法查询每个用户的角色，并将结果放入Map中
        for (Long userId : userIds) {
            List<SysRole> roles = tableRoleService.findByUserId(userId);
            result.put(userId, roles);
        }

        return result;
    }

    /**
     * 批量获取用户机构关联
     * 一次性查询所有用户的机构关联，减少数据库访问次数
     *
     * @param userIds 用户ID列表
     * @return 用户ID到机构ID列表的映射
     */
    private Map<Long, List<String>> batchGetUserOrgs(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            // 批量查询用户机构关联
            List<SysUserOrg> userOrgs = tableUserOrgService.findByUserIds(userIds);

            // 按用户ID分组
            Map<Long, List<String>> result = new HashMap<>();

            // 初始化每个用户的机构列表为空列表
            userIds.forEach(userId -> result.put(userId, new ArrayList<>()));

            // 填充机构数据
            for (SysUserOrg userOrg : userOrgs) {
                Long userId = userOrg.getUserId();
                String orgId = userOrg.getOrgId();

                if (userId != null && orgId != null) {
                    result.computeIfAbsent(userId, k -> new ArrayList<>()).add(orgId);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("批量查询用户机构关联失败，用户IDs: {}, 错误: {}", userIds, e.getMessage(), e);
            // 返回空映射，每个用户都有空的机构列表
            Map<Long, List<String>> emptyResult = new HashMap<>();
            userIds.forEach(userId -> emptyResult.put(userId, new ArrayList<>()));
            return emptyResult;
        }
    }

    /**
     * 构建修理厂名称映射表
     * 预加载修理厂信息，避免N+1查询问题
     *
     * <p>多租户数据隔离说明：</p>
     * <ul>
     *   <li>普通用户：通过 @TenantSchema 注解，自动应用租户数据隔离，只查询当前租户的修理厂</li>
     *   <li>超级管理员：绕过多租户拦截器，查询所有租户的修理厂信息</li>
     *   <li>映射键值策略：普通用户使用 repairDepotId，超级管理员使用 tenantId_repairDepotId</li>
     * </ul>
     *
     * @return 修理厂ID与修理厂名称的映射关系
     */
    private Map<String, String> buildRepairDepotNameMap() {
        Long currentTenantId = SessionUtils.getTenantId();
        boolean isSuperAdmin = determineIfSuperAdmin();

        logRepairDepotLoadStart(currentTenantId, isSuperAdmin);

        try {
            List<MtcRepairDepotInfo> repairDepots = queryRepairDepots(isSuperAdmin, currentTenantId);
            return buildRepairDepotMapping(repairDepots, isSuperAdmin, currentTenantId);
        } catch (Exception e) {
            logRepairDepotLoadError(currentTenantId, isSuperAdmin, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 判断当前用户是否为超级管理员
     *
     * @return true-超级管理员，false-普通用户
     */
    private boolean determineIfSuperAdmin() {
        LoginUser loginUser = SessionUtils.getLoginUser();

        if (loginUser == null || loginUser.getUser() == null) {
            return false;
        }

        Integer accountType = loginUser.getUser().getAccountType();
        return accountType != null && accountType == 0;
    }

    /**
     * 查询修理厂数据
     *
     * @param isSuperAdmin 是否为超级管理员
     * @param currentTenantId 当前租户ID
     * @return 修理厂列表
     */
    private List<MtcRepairDepotInfo> queryRepairDepots(boolean isSuperAdmin, Long currentTenantId) {
        if (isSuperAdmin) {
            return queryRepairDepotsForSuperAdmin();
        }
        return queryRepairDepotsForRegularUser(currentTenantId);
    }

    /**
     * 查询超级管理员的修理厂数据
     *
     * @return 所有租户的修理厂列表
     */
    private List<MtcRepairDepotInfo> queryRepairDepotsForSuperAdmin() {
        List<MtcRepairDepotInfo> repairDepots = tableRepairDepotInfoService.findAllValidDepotsForSuperAdmin();
        log.debug("超级管理员查询所有租户修理厂信息，共找到: {} 条", repairDepots.size());
        return repairDepots;
    }

    /**
     * 查询普通用户的修理厂数据
     *
     * @param currentTenantId 当前租户ID
     * @return 当前租户的修理厂列表
     */
    private List<MtcRepairDepotInfo> queryRepairDepotsForRegularUser(Long currentTenantId) {
        List<MtcRepairDepotInfo> repairDepots = tableRepairDepotInfoService.findValidDepots();
        log.debug("普通用户查询当前租户修理厂信息，租户ID: {}, 共找到: {} 条", currentTenantId, repairDepots.size());
        return repairDepots;
    }

    /**
     * 记录修理厂加载开始日志
     *
     * @param currentTenantId 当前租户ID
     * @param isSuperAdmin 是否为超级管理员
     */
    private void logRepairDepotLoadStart(Long currentTenantId, boolean isSuperAdmin) {
        String userType = isSuperAdmin ? "超级管理员" : "普通用户";
        log.debug("开始预加载修理厂信息，当前租户ID: {}, 用户类型: {}", currentTenantId, userType);
    }

    /**
     * 记录修理厂加载错误日志
     *
     * @param currentTenantId 当前租户ID
     * @param isSuperAdmin 是否为超级管理员
     * @param e 异常信息
     */
    private void logRepairDepotLoadError(Long currentTenantId, boolean isSuperAdmin, Exception e) {
        String userType = isSuperAdmin ? "超级管理员" : "普通用户";
        log.error("预加载修理厂信息失败，租户ID: {}, 用户类型: {}", currentTenantId, userType, e);
    }

    /**
     * 构建修理厂名称映射
     *
     * @param repairDepots 修理厂列表
     * @param isSuperAdmin 是否为超级管理员
     * @param currentTenantId 当前租户ID
     * @return 修理厂映射表
     */
    private Map<String, String> buildRepairDepotMapping(List<MtcRepairDepotInfo> repairDepots,
                                                       boolean isSuperAdmin, Long currentTenantId) {
        if (repairDepots.isEmpty()) {
            log.debug("修理厂列表为空，返回空映射表");
            return Collections.emptyMap();
        }

        Map<String, String> repairDepotNameMap = new HashMap<>();

        for (MtcRepairDepotInfo depot : repairDepots) {
            if (!isValidRepairDepot(depot)) {
                continue;
            }

            String mappingKey = buildMappingKey(depot, isSuperAdmin);
            String displayName = buildDisplayName(depot, isSuperAdmin);

            repairDepotNameMap.put(mappingKey, displayName);
        }

        logMappingResult(isSuperAdmin, currentTenantId, repairDepotNameMap.size());
        return repairDepotNameMap;
    }

    /**
     * 验证修理厂信息是否有效
     *
     * @param depot 修理厂信息
     * @return true-有效，false-无效
     */
    private boolean isValidRepairDepot(MtcRepairDepotInfo depot) {
        if (depot.getRepairDepotId() == null || depot.getRepairDepotName() == null) {
            log.warn("修理厂信息不完整，跳过处理，ID: {}, 名称: {}",
                    depot.getRepairDepotId(), depot.getRepairDepotName());
            return false;
        }
        return true;
    }

    /**
     * 构建映射键
     *
     * @param depot 修理厂信息
     * @param isSuperAdmin 是否为超级管理员
     * @return 映射键
     */
    private String buildMappingKey(MtcRepairDepotInfo depot, boolean isSuperAdmin) {
        String repairDepotId = depot.getRepairDepotId();

        if (isSuperAdmin) {
            String tenantInfo = extractTenantFromRepairDepot(depot);
            return tenantInfo + "_" + repairDepotId;
        }

        return repairDepotId;
    }

    /**
     * 构建显示名称
     *
     * @param depot 修理厂信息
     * @param isSuperAdmin 是否为超级管理员
     * @return 显示名称
     */
    private String buildDisplayName(MtcRepairDepotInfo depot, boolean isSuperAdmin) {
        String repairDepotName = depot.getRepairDepotName();

        if (isSuperAdmin) {
            String tenantName = extractTenantNameFromRepairDepot(depot);
            return repairDepotName + " [租户:" + tenantName + "]";
        }

        return repairDepotName;
    }

    /**
     * 记录映射构建结果日志
     *
     * @param isSuperAdmin 是否为超级管理员
     * @param currentTenantId 当前租户ID
     * @param mappingCount 映射数量
     */
    private void logMappingResult(boolean isSuperAdmin, Long currentTenantId, int mappingCount) {
        String userType = isSuperAdmin ? "超级管理员" : "普通用户";
        log.info("成功构建修理厂映射，用户类型: {}, 租户ID: {}, 映射数量: {}",
                userType, currentTenantId, mappingCount);
    }

    /**
     * 从修理厂信息中提取租户信息
     *
     * @param depot 修理厂信息对象
     * @return 租户标识
     */
    private String extractTenantFromRepairDepot(MtcRepairDepotInfo depot) {
        // 优先从备注字段提取租户信息
        String tenantFromRemark = extractTenantFromRemark(depot.getRemark(), depot.getRepairDepotId());
        if (tenantFromRemark != null) {
            return tenantFromRemark;
        }

        // 备用方案：从修理厂ID中提取
        String tenantFromId = extractTenantFromRepairDepotId(depot.getRepairDepotId());
        if (tenantFromId != null) {
            return tenantFromId;
        }

        // 默认值
        return "unknown";
    }

    /**
     * 从备注字段中提取租户信息
     *
     * @param remark 备注信息
     * @param repairDepotId 修理厂ID（用于日志记录）
     * @return 租户标识，如果提取失败返回null
     */
    private String extractTenantFromRemark(String remark, String repairDepotId) {
        if (remark == null || !remark.contains("TENANT_ID:")) {
            return null;
        }

        try {
            String[] parts = remark.split(";");
            for (String part : parts) {
                if (part.startsWith("TENANT_ID:")) {
                    return part.substring("TENANT_ID:".length());
                }
            }
        } catch (Exception e) {
            log.warn("解析修理厂租户信息失败，修理厂ID: {}, 备注: {}", repairDepotId, remark, e);
        }

        return null;
    }

    /**
     * 从修理厂信息中提取租户名称
     *
     * @param depot 修理厂信息对象
     * @return 租户名称
     */
    private String extractTenantNameFromRepairDepot(MtcRepairDepotInfo depot) {
        String tenantNameFromRemark = extractTenantNameFromRemark(depot.getRemark(), depot.getRepairDepotId());
        if (tenantNameFromRemark != null) {
            return tenantNameFromRemark;
        }

        // 备用方案：返回租户ID作为名称
        return extractTenantFromRepairDepot(depot);
    }

    /**
     * 从备注字段中提取租户名称
     *
     * @param remark 备注信息
     * @param repairDepotId 修理厂ID（用于日志记录）
     * @return 租户名称，如果提取失败返回null
     */
    private String extractTenantNameFromRemark(String remark, String repairDepotId) {
        if (remark == null || !remark.contains("TENANT_NAME:")) {
            return null;
        }

        try {
            String[] parts = remark.split(";");
            for (String part : parts) {
                if (part.startsWith("TENANT_NAME:")) {
                    return part.substring("TENANT_NAME:".length());
                }
            }
        } catch (Exception e) {
            log.warn("解析修理厂租户名称失败，修理厂ID: {}, 备注: {}", repairDepotId, remark, e);
        }

        return null;
    }

    /**
     * 从修理厂ID中提取租户信息（兼容旧方法）
     *
     * @param repairDepotId 修理厂ID
     * @return 租户标识，如果提取失败返回null
     */
    private String extractTenantFromRepairDepotId(String repairDepotId) {
        if (repairDepotId == null || !repairDepotId.contains("_")) {
            return null;
        }

        // 假设格式为 tenantId_timestamp 或类似格式
        String[] parts = repairDepotId.split("_");
        if (parts.length > 0) {
            return parts[0];
        }

        return null;
    }

    /**
     * 从映射表中获取修理厂名称
     * 根据用户类型采用不同的查找策略
     *
     * @param user 用户信息
     * @param repairDepotNameMap 修理厂名称映射表
     * @param isSuperAdmin 是否为超级管理员
     * @return 修理厂名称
     */
    private String getRepairDepotNameFromMap(SysUser user, Map<String, String> repairDepotNameMap, boolean isSuperAdmin) {
        String repairDepotId = user.getRepairDepotId();

        if (isSuperAdmin) {
            return getRepairDepotNameForSuperAdmin(user, repairDepotNameMap, repairDepotId);
        }

        return getRepairDepotNameForRegularUser(repairDepotNameMap, repairDepotId);
    }

    /**
     * 为超级管理员获取修理厂名称
     *
     * @param user 用户信息
     * @param repairDepotNameMap 修理厂名称映射表
     * @param repairDepotId 修理厂ID
     * @return 修理厂名称
     */
    private String getRepairDepotNameForSuperAdmin(SysUser user, Map<String, String> repairDepotNameMap, String repairDepotId) {
        String tenantInfo = extractTenantFromRepairDepotId(repairDepotId);
        if (tenantInfo == null) {
            tenantInfo = "unknown";
        }

        String mappingKey = tenantInfo + "_" + repairDepotId;
        String repairDepotName = repairDepotNameMap.get(mappingKey);

        log.debug("超级管理员查找修理厂名称，用户租户: {}, 修理厂ID: {}, 映射键: {}, 结果: {}",
                user.getTenantId(), repairDepotId, mappingKey, repairDepotName);

        return repairDepotName;
    }

    /**
     * 为普通用户获取修理厂名称
     *
     * @param repairDepotNameMap 修理厂名称映射表
     * @param repairDepotId 修理厂ID
     * @return 修理厂名称
     */
    private String getRepairDepotNameForRegularUser(Map<String, String> repairDepotNameMap, String repairDepotId) {
        String repairDepotName = repairDepotNameMap.get(repairDepotId);
        log.debug("普通用户查找修理厂名称，修理厂ID: {}, 结果: {}", repairDepotId, repairDepotName);
        return repairDepotName;
    }

    /**
     * 根据修理厂ID获取修理厂名称
     * 用于单个用户查询时获取修理厂名称
     *
     * <p>多租户数据隔离说明：</p>
     * <ul>
     *   <li>通过多租户拦截器自动应用租户隔离，只查询当前租户的修理厂</li>
     *   <li>直接使用 repairDepotId 作为查询条件，简化查询逻辑</li>
     * </ul>
     *
     * @param tenantId 租户ID（用于日志记录）
     * @param repairDepotId 修理厂ID
     * @return 修理厂名称，如果未找到则返回null
     */
    private String getRepairDepotNameByTenantAndDepotId(Long tenantId, String repairDepotId) {
        try {
            // 优先从缓存获取
            String nameFromCache = getRepairDepotNameFromCache(repairDepotId);
            if (nameFromCache != null) {
                return nameFromCache;
            }

            // 备用方案：直接查询数据库
            return getRepairDepotNameFromDatabase(tenantId, repairDepotId);

        } catch (Exception e) {
            log.error("查询修理厂名称失败，租户ID: {}, 修理厂ID: {}", tenantId, repairDepotId, e);
            return null;
        }
    }

    /**
     * 从缓存中获取修理厂名称
     *
     * @param repairDepotId 修理厂ID
     * @return 修理厂名称，如果未找到返回null
     */
    private String getRepairDepotNameFromCache(String repairDepotId) {
        Map<String, String> repairDepotNameMap = buildRepairDepotNameMap();
        String repairDepotName = repairDepotNameMap.get(repairDepotId);

        if (repairDepotName != null) {
            log.debug("从缓存获取修理厂名称，修理厂ID: {}, 名称: {}", repairDepotId, repairDepotName);
        }

        return repairDepotName;
    }

    /**
     * 从数据库中获取修理厂名称
     *
     * @param tenantId 租户ID
     * @param repairDepotId 修理厂ID
     * @return 修理厂名称，如果未找到返回null
     */
    private String getRepairDepotNameFromDatabase(Long tenantId, String repairDepotId) {
        // 注意：由于多租户拦截器的存在，这里只会查询当前租户的修理厂
        MtcRepairDepotInfo depot = tableRepairDepotInfoService.selectByRepairDepotCode(repairDepotId);

        if (depot != null && depot.getRepairDepotName() != null) {
            log.debug("从数据库获取修理厂名称，修理厂ID: {}, 名称: {}", repairDepotId, depot.getRepairDepotName());
            return depot.getRepairDepotName();
        }

        log.debug("未找到修理厂信息，租户ID: {}, 修理厂ID: {}", tenantId, repairDepotId);
        return null;
    }

    @Override
    public BasePageVO<MerchantAdminVO> getMerchantAdminList(MerchantAdminQueryDTO queryDTO) {
        // 开启分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 查询商户管理员用户列表
        // 这里假设商户管理员的账号类型为1
        List<SysUser> users = tableUserService.findMerchantAdmins(
                queryDTO.getMerchantName(),
                queryDTO.getAdminName(),
                queryDTO.getMobile(),
                queryDTO.getStatus()
        );

        System.out.println(JSON.toJSONString(users));

        // 转换为VO
        List<MerchantAdminVO> adminVOs = users.stream().map(user -> {
            MerchantAdminVO adminVO = new MerchantAdminVO();
            BeanUtils.copyProperties(user, adminVO);
            return adminVO;
        }).collect(Collectors.toList());

        // 构建分页结果
        PageInfo<SysUser> pageInfo = new PageInfo<>(users);
        return BasePageVO.of(adminVOs, pageInfo);
    }

    @Override
    public MerchantAdminVO getMerchantAdminById(Long id) {
        // 查询用户基本信息
        SysUser user = tableUserService.selectById(id);
        if (user == null) {
            throw new BusinessException(ErrorCode.MERCHANT_ADMIN_NOT_FOUND);
        }

        // 检查是否为商户管理员
        if (user.getAccountType() == null || user.getAccountType() != 1) {
            throw new BusinessException(ErrorCode.MERCHANT_ADMIN_NOT_FOUND);
        }

        // 组装商户管理员详情
        MerchantAdminVO adminVO = new MerchantAdminVO();
        BeanUtils.copyProperties(user, adminVO);

        // 查询商户名称
        if (user.getTenantId() != null) {
            SysTenant tenant = tableTenantService.selectById(user.getTenantId());
            if (tenant != null) {
                adminVO.setTenantName(tenant.getTenantName());
            }
        }

        // 查询角色名称列表
        List<SysRole> roles = tableRoleService.findByUserId(id);
        List<String> roleNames = roles.stream()
                .map(SysRole::getRoleName)
                .collect(Collectors.toList());
        adminVO.setRoleNames(roleNames);

        return adminVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableUser(Long userId) {
        updateUserStatus(userId, 1, "启用");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableUser(Long userId) {
        updateUserStatus(userId, 0, "禁用");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long userId, Integer status) {
        // 参数校验
        if (status == null || (status != 0 && status != 1)) {
            throw new BusinessException("状态值只能是0（禁用）或1（启用）");
        }

        // 根据状态值调用对应的方法
        if (status == 1) {
            enableUser(userId);
        } else {
            disableUser(userId);
        }
    }

    /**
     * 更新用户状态的通用方法
     *
     * @param userId 用户ID
     * @param newStatus 新状态（0-禁用，1-启用）
     * @param operation 操作描述（用于日志）
     */
    private void updateUserStatus(Long userId, Integer newStatus, String operation) {
        // 参数校验
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 查询用户信息
        SysUser existingUser = tableUserService.selectById(userId);
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }

        // 多租户访问控制
        validateTenantAccess(existingUser);

        // 检查状态是否需要更新
        if (Objects.equals(existingUser.getStatus(), newStatus)) {
            log.info("用户状态无需更新，用户ID: {}, 当前状态: {}", userId, newStatus);
            return;
        }

        // 记录操作前的状态（用于日志）
        Integer oldStatus = existingUser.getStatus();

        // 更新用户状态
        SysUser updateUser = new SysUser();
        updateUser.setId(userId);
        updateUser.setStatus(newStatus);

        // 获取当前操作人
        String operator = getCurrentOperator();
        int updateCount = tableUserService.updateSelectiveById(updateUser, operator);

        if (updateCount <= 0) {
            throw new BusinessException(operation + "用户失败");
        }

        // 记录操作日志
        recordUserStatusChangeLog(existingUser, oldStatus, newStatus, operation);

        // 刷新用户权限缓存（用户状态发生变更时）
        try {
            if (userPermissionCacheUtils != null) {
                boolean refreshSuccess = userPermissionCacheUtils.refreshUserPermissions(
                    existingUser.getTenantId(), userId);

                if (refreshSuccess) {
                    log.debug("用户权限缓存刷新成功，用户ID: {}", userId);
                } else {
                    log.warn("用户权限缓存刷新失败，用户ID: {}", userId);
                }
            }
        } catch (Exception e) {
            log.error("刷新用户权限缓存异常，用户ID: {}", userId, e);
        }

        log.info("{}用户成功，用户ID: {}, 手机号: {}, 姓名: {}, 状态: {} -> {}",
                operation, userId, existingUser.getMobile(), existingUser.getNickname(), oldStatus, newStatus);
    }

    /**
     * 验证多租户访问权限
     *
     * @param targetUser 目标用户
     */
    private void validateTenantAccess(SysUser targetUser) {
        // 判断当前用户是否为超级管理员
        boolean isSuperAdmin = SessionUtils.isSuperAdmin();

        if (isSuperAdmin) {
            // 超级管理员可以操作所有租户的用户
            log.debug("超级管理员操作用户，用户ID: {}, 目标租户: {}", targetUser.getId(), targetUser.getTenantId());
            return;
        }

        // 普通用户只能操作本租户的用户
        LoginUser currentLoginUser = SessionUtils.getLoginUser();
        if (currentLoginUser == null || currentLoginUser.getUser() == null) {
            throw new BusinessException("未登录或登录信息无效");
        }

        Long currentTenantId = currentLoginUser.getUser().getTenantId();
        Long targetTenantId = targetUser.getTenantId();

        if (!Objects.equals(currentTenantId, targetTenantId)) {
            throw new BusinessException("无权限操作其他租户的用户");
        }

        log.debug("普通用户操作本租户用户，当前租户: {}, 目标用户租户: {}", currentTenantId, targetTenantId);
    }

    /**
     * 获取当前操作人
     *
     * @return 操作人标识
     */
    private String getCurrentOperator() {
        try {
            LoginUser loginUser = SessionUtils.getLoginUser();
            if (loginUser != null && loginUser.getUser() != null) {
                return loginUser.getUser().getUsername();
            }
        } catch (Exception e) {
            log.warn("获取当前操作人失败，使用默认值", e);
        }
        return "system";
    }

    /**
     * 记录用户状态变更操作日志
     *
     * @param user 用户信息
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     * @param operation 操作描述
     */
    private void recordUserStatusChangeLog(SysUser user, Integer oldStatus, Integer newStatus, String operation) {
        try {
            // 根据新状态值调用对应的专门日志记录方法
            if (newStatus != null && newStatus == 1) {
                // 启用用户
                OperateLogUtil.recordUserEnable(user.getMobile(), user.getNickname());
            } else if (newStatus != null && newStatus == 0) {
                // 禁用用户
                OperateLogUtil.recordUserDisable(user.getMobile(), user.getNickname());
            } else {
                // 状态值异常时，使用通用的状态变更日志记录方法作为降级处理
                log.warn("用户状态值异常，使用通用状态变更日志记录，用户ID: {}, 新状态: {}", user.getId(), newStatus);
                OperateLogUtil.recordUserStatusChange(user.getMobile(), user.getNickname(), oldStatus, newStatus);
            }
        } catch (Exception e) {
            log.error("记录用户状态变更日志失败，用户ID: {}, 操作: {}", user.getId(), operation, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserOrgs(Long userId, List<String> orgIds) {
        if (userId == null) {
            log.warn("更新用户机构关联失败：用户ID为空");
            return false;
        }

        try {
            // 获取用户信息以获取租户ID
            SysUser user = tableUserService.selectById(userId);
            if (user == null) {
                log.warn("更新用户机构关联失败：用户不存在，用户ID: {}", userId);
                return false;
            }

            boolean result = tableUserOrgService.updateUserOrgs(userId, orgIds);
            if (result) {
                log.info("更新用户机构关联成功，用户ID: {}, 机构数量: {}",
                        userId, orgIds != null ? orgIds.size() : 0);

                // 刷新用户权限缓存（组织关联发生变更时）
                try {
                    if (userPermissionCacheUtils != null) {
                        boolean refreshSuccess = userPermissionCacheUtils.refreshUserPermissions(
                            user.getTenantId(), userId);

                        if (refreshSuccess) {
                            log.debug("用户权限缓存刷新成功，用户ID: {}", userId);
                        } else {
                            log.warn("用户权限缓存刷新失败，用户ID: {}", userId);
                        }
                    }
                } catch (Exception e) {
                    log.error("刷新用户权限缓存异常，用户ID: {}", userId, e);
                }
            } else {
                log.warn("更新用户机构关联失败，用户ID: {}", userId);
            }
            return result;
        } catch (Exception e) {
            log.error("更新用户机构关联异常，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<SysUser> getUsersByOrgIds(List<String> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return Collections.emptyList();
        }

        try {
            // 查询机构关联的用户ID列表
            List<SysUserOrg> userOrgs = tableUserOrgService.findByOrgIds(orgIds);
            if (userOrgs.isEmpty()) {
                return Collections.emptyList();
            }

            // 提取用户ID列表
            List<Long> userIds = userOrgs.stream()
                .map(SysUserOrg::getUserId)
                .distinct()
                .collect(Collectors.toList());

            // 批量查询用户信息
            List<SysUser> users = new ArrayList<>();
            for (Long userId : userIds) {
                SysUser user = tableUserService.selectById(userId);
                if (user != null) {
                    users.add(user);
                }
            }
            return users;
        } catch (Exception e) {
            log.error("根据机构ID查询用户失败，机构IDs: {}, 错误: {}", orgIds, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量获取机构名称信息
     * 从用户机构关联映射中提取所有机构ID，然后批量查询机构名称
     *
     * @param userOrgsMap 用户ID到机构ID列表的映射
     * @return 机构ID到机构名称的映射
     */
    private Map<String, String> batchGetOrgNames(Map<Long, List<String>> userOrgsMap) {
        if (userOrgsMap == null || userOrgsMap.isEmpty()) {
            return Collections.emptyMap();
        }

        // 提取所有唯一的机构ID
        List<String> allOrgIds = userOrgsMap.values().stream()
            .flatMap(List::stream)
            .distinct()
            .collect(Collectors.toList());

        return getOrgIdToNameMap(allOrgIds);
    }

    /**
     * 根据机构ID列表获取机构ID到名称的映射
     * 参考OperateLogUtil中的getOrgIdToNameMap方法实现
     *
     * @param orgIds 机构ID列表
     * @return 机构ID到名称的映射
     */
    private Map<String, String> getOrgIdToNameMap(List<String> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            List<MtcOrgInfo> orgInfos = tableOrgInfoService.findByOrgIds(orgIds);
            return orgInfos.stream()
                .collect(Collectors.toMap(
                    MtcOrgInfo::getOrgId,
                    org -> org.getOrgName() != null ? org.getOrgName() : org.getOrgId(),
                    (existing, replacement) -> existing // 处理重复key的情况
                ));
        } catch (Exception e) {
            log.error("批量查询机构信息失败，机构ID列表: {}", orgIds, e);
            // 降级处理：返回ID作为名称的映射
            return orgIds.stream()
                .collect(Collectors.toMap(orgId -> orgId, orgId -> orgId));
        }
    }

    @Override
    public LoginUser getCurrentUserWithExpiry() {
        // 从Session获取登录用户信息
        LoginUser loginUser = SessionUtils.getLoginUser();
        if (loginUser == null) {
            return null;
        }

        // 计算距离过期剩余天数
        Integer daysUntilExpiry = calculateDaysUntilExpiry(loginUser.getTenantId());
        loginUser.setDaysUntilExpiry(daysUntilExpiry);

        return loginUser;
    }

    /**
     * 计算距离过期剩余天数
     *
     * @param tenantId 租户ID
     * @return 剩余天数，如果已过期则返回负数或0，如果未设置过期时间则返回null
     */
    private Integer calculateDaysUntilExpiry(Long tenantId) {
        if (tenantId == null) {
            return null;
        }

        try {
            // 查询租户信息
            SysTenant tenant = tableTenantService.selectById(tenantId);
            if (tenant == null || tenant.getExpireTime() == null) {
                // 租户不存在或未设置过期时间，返回null表示无限期
                return null;
            }

            // 获取当前日期（GMT+8时区）
            LocalDate currentDate = LocalDate.now(ZoneId.of("GMT+8"));

            // 将租户过期时间转换为LocalDate（GMT+8时区）
            LocalDate expireDate = tenant.getExpireTime()
                .toInstant()
                .atZone(ZoneId.of("GMT+8"))
                .toLocalDate();

            // 计算剩余天数
            long daysBetween = ChronoUnit.DAYS.between(currentDate, expireDate);

            return (int) daysBetween;
        } catch (Exception e) {
            log.error("计算租户过期剩余天数失败，租户ID: {}", tenantId, e);
            // 发生异常时返回null，避免影响正常的用户信息返回
            return null;
        }
    }

    /**
     * 验证保险公司ID字段
     * 当accountType=3（保险公司用户）时，insuranceCompanyId字段为必填项
     * 当accountType为其他值时，insuranceCompanyId字段应为空或null
     *
     * @param accountType 账号类型
     * @param insuranceCompanyId 保险公司ID
     */
    private void validateInsuranceCompanyId(Integer accountType, Long insuranceCompanyId) {
        if (accountType != null && accountType == 3) {
            // 保险公司用户，insuranceCompanyId必填
            if (insuranceCompanyId == null) {
                throw new BusinessException("保险公司用户必须指定保险公司ID");
            }
        } else if (accountType != null && accountType != 3) {
            // 非保险公司用户，insuranceCompanyId应为空
            if (insuranceCompanyId != null) {
                throw new BusinessException("非保险公司用户不能指定保险公司ID");
            }
        }
    }

    @Override
    public List<SysUser> findAllByMobile(String mobile) {
        return tableUserService.findAllByMobile(mobile);
    }
}