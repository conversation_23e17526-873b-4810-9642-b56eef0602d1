package com.extracme.saas.autocare.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.PermissionTreeVO;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.PermissionService;
import com.extracme.saas.autocare.util.OperateLogUtil;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 权限服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {

    private final TablePermissionService permissionRepository;
    private final TableUserService userRepository;
    private final UserPermissionCacheUtils userPermissionCacheUtils;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPermission(SysPermission permission) {
        // 检查权限编码是否已存在
        if (permissionRepository.existsByCode(permission.getPermissionCode())) {
            throw new BusinessException(ErrorCode.PERMISSION_CODE_EXISTS);
        }
        // 使用当前登录用户作为操作人
        String operator = SessionUtils.getUsername();
        // 保存权限
        return permissionRepository.insert(permission, operator).getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePermission(SysPermission permission) {
        // 检查权限是否存在
        SysPermission existingPermission = getPermission(permission.getId());
        if (existingPermission == null) {
            throw new BusinessException(ErrorCode.PERMISSION_NOT_FOUND);
        }

        // 如果修改了权限编码，需要检查是否重复
        if (!existingPermission.getPermissionCode().equals(permission.getPermissionCode())
            && permissionRepository.existsByCode(permission.getPermissionCode())) {
            throw new BusinessException(ErrorCode.PERMISSION_CODE_EXISTS);
        }

        // 使用当前登录用户作为操作人
        String operator = SessionUtils.getUsername();
        // 更新权限
        permissionRepository.updateSelectiveById(permission, operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePermission(Long id) {
        // 默认不强制删除
        deletePermission(id, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePermission(Long id, boolean forceDelete) {
        // 检查权限是否存在
        SysPermission permission = getPermission(id);
        if (permission == null) {
            throw new BusinessException(ErrorCode.PERMISSION_NOT_FOUND);
        }

        // 获取当前租户ID
        Long currentTenantId = SessionUtils.getTenantId();

        // 查询使用该权限的角色ID列表
        List<Long> roleIds = permissionRepository.findRoleIdsByPermissionId(id);

        if (!roleIds.isEmpty()) {
            if (!forceDelete) {
                // 非强制删除模式：权限被使用时抛出异常
                throw new BusinessException(ErrorCode.PERMISSION_IN_USE);
            }

            // 强制删除模式：删除角色权限关联并清除相关用户缓存
            log.info("强制删除权限，权限ID: {}, 权限编码: {}, 影响角色数量: {}",
                    id, permission.getPermissionCode(), roleIds.size());

            // 删除角色权限关联
            permissionRepository.deleteByPermissionId(id);

            // 清除相关用户的权限缓存
            try {
                if (userPermissionCacheUtils != null && currentTenantId != null) {
                    int totalRefreshCount = 0;
                    for (Long roleId : roleIds) {
                        int refreshCount = userPermissionCacheUtils.refreshUserPermissionsByRole(currentTenantId, roleId);
                        totalRefreshCount += refreshCount;
                    }

                    if (totalRefreshCount > 0) {
                        log.info("强制删除权限，成功刷新 {} 个用户的权限缓存，权限ID: {}", totalRefreshCount, id);
                    } else {
                        log.debug("强制删除权限，未找到需要刷新的用户缓存，权限ID: {}", id);
                    }
                }
            } catch (Exception e) {
                log.error("强制删除权限时刷新用户权限缓存异常，权限ID: {}", id, e);
            }

            // 记录操作日志
            OperateLogUtil.recordPermissionForceDelete(permission.getPermissionName(),
                    permission.getPermissionCode(), roleIds.size());
        }

        // 物理删除权限
        permissionRepository.deleteById(id);

        // 如果是普通删除（非强制删除），也记录日志
        if (!forceDelete) {
            OperateLogUtil.recordPermissionDelete(permission.getPermissionName(),
                    permission.getPermissionCode());
        }

        log.info("权限删除成功，权限ID: {}, 权限编码: {}, 强制删除: {}",
                id, permission.getPermissionCode(), forceDelete);
    }

    @Override
    public SysPermission getPermission(Long id) {
        return permissionRepository.selectById(id);
    }

    @Override
    public List<SysPermission> getPermissionList(Integer type) {
        return permissionRepository.findByType(type);
    }

    @Override
    public List<SysPermission> getUserPermissions(Long userId) {
        // 查询用户信息以判断账户类型
        SysUser user = userRepository.selectById(userId);
        if (user == null) {
            return new ArrayList<>();
        }

        // 根据账户类型返回相应权限
        if (user.getAccountType() != null && user.getAccountType() == 0) {
            // 超级管理员：返回系统中所有可用权限
            return permissionRepository.findAll();
        } else {
            // 普通用户：通过角色关联查询权限
            return permissionRepository.findByUserId(userId);
        }
    }

    @Override
    public List<SysPermission> getRolePermissions(Long roleId) {
        return permissionRepository.findByRoleId(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignRolePermissions(Long roleId, List<Long> permissionIds) {
        permissionRepository.assignRolePermissions(roleId, permissionIds);
    }

    @Override
    public boolean hasPermission(Long userId, String permissionCode) {
        // 查询用户信息以判断账户类型
        SysUser user = userRepository.selectById(userId);
        if (user == null) {
            return false;
        }

        // 超级管理员对所有权限检查都返回true
        if (user.getAccountType() != null && user.getAccountType() == 0) {
            return true;
        }

        // 普通用户：通过角色关联检查权限
        return permissionRepository.hasPermission(userId, permissionCode);
    }

    @Override
    public List<PermissionTreeVO> getPermissionTree() {
        List<SysPermission> permissions;

        // 判断当前用户是否为超级管理员
        if (SessionUtils.isSuperAdmin()) {
            // 超级管理员：获取所有权限
            permissions = permissionRepository.findAll();
        } else {
            // 普通用户：获取用户拥有的权限
            Long currentUserId = SessionUtils.getUserId();
            if (currentUserId == null) {
                // 用户未登录，返回空树
                return new ArrayList<>();
            }

            // 获取用户拥有的权限列表
            List<SysPermission> userPermissions = permissionRepository.findByUserId(currentUserId);
            if (userPermissions.isEmpty()) {
                // 用户没有任何权限，返回空树
                return new ArrayList<>();
            }

            // 获取用户权限ID集合
            Set<Long> userPermissionIds = userPermissions.stream()
                    .map(SysPermission::getId)
                    .collect(Collectors.toSet());

            // 补充父权限ID，确保权限树结构完整
            Set<Long> completePermissionIds = getCompletePermissionIds(userPermissionIds);

            // 获取所有权限并过滤
            List<SysPermission> allPermissions = permissionRepository.findAll();
            permissions = allPermissions.stream()
                    .filter(p -> completePermissionIds.contains(p.getId()))
                    .collect(Collectors.toList());
        }

        // 按父ID分组
        Map<Long, List<SysPermission>> permissionMap = permissions.stream()
                .collect(Collectors.groupingBy(p -> p.getParentId() == null ? 0L : p.getParentId()));

        // 构建树形结构（从根节点开始）
        return buildPermissionTree(permissionMap, 0L);
    }

    /**
     * 递归构建权限树
     *
     * @param permissionMap 按父ID分组的权限Map
     * @param parentId 父ID
     * @return 权限树节点列表
     */
    private List<PermissionTreeVO> buildPermissionTree(Map<Long, List<SysPermission>> permissionMap, Long parentId) {
        List<PermissionTreeVO> result = new ArrayList<>();

        // 获取当前父ID下的所有权限
        List<SysPermission> permissions = permissionMap.get(parentId);
        if (permissions == null || permissions.isEmpty()) {
            return result;
        }

        // 排序
        permissions.sort(Comparator.comparing(SysPermission::getSort, Comparator.nullsLast(Comparator.naturalOrder())));

        // 转换为VO并递归构建子节点
        for (SysPermission permission : permissions) {
            PermissionTreeVO node = new PermissionTreeVO();

            // 复制基本属性
            node.setId(permission.getId());
            node.setPermissionName(permission.getPermissionName());
            node.setPermissionCode(permission.getPermissionCode());
            node.setPermissionType(permission.getPermissionType());
            node.setParentId(permission.getParentId());
            node.setPath(permission.getPath());
            node.setComponent(permission.getComponent());
            node.setIcon(permission.getIcon());
            node.setSort(permission.getSort());
            node.setStatus(permission.getStatus());

            // 递归构建子节点
            node.setChildren(buildPermissionTree(permissionMap, permission.getId()));

            result.add(node);
        }

        return result;
    }

    /**
     * 获取完整的权限ID集合（包含父权限）
     * 确保权限树的层级关系完整
     *
     * @param userPermissionIds 用户拥有的权限ID集合
     * @return 包含父权限的完整权限ID集合
     */
    private Set<Long> getCompletePermissionIds(Set<Long> userPermissionIds) {
        if (userPermissionIds == null || userPermissionIds.isEmpty()) {
            return new HashSet<>();
        }

        // 获取所有权限，用于查找父权限
        List<SysPermission> allPermissions = permissionRepository.findAll();
        Map<Long, SysPermission> permissionMap = allPermissions.stream()
                .collect(Collectors.toMap(SysPermission::getId, p -> p));

        // 结果集合，包含用户权限和所有父权限
        Set<Long> completePermissionIds = new HashSet<>(userPermissionIds);

        // 为每个用户权限递归添加父权限
        for (Long permissionId : userPermissionIds) {
            addParentPermissions(permissionId, permissionMap, completePermissionIds);
        }

        return completePermissionIds;
    }

    /**
     * 递归添加父权限
     *
     * @param permissionId 权限ID
     * @param permissionMap 权限映射表
     * @param completePermissionIds 完整权限ID集合
     */
    private void addParentPermissions(Long permissionId, Map<Long, SysPermission> permissionMap, Set<Long> completePermissionIds) {
        SysPermission permission = permissionMap.get(permissionId);
        if (permission == null || permission.getParentId() == null) {
            return;
        }

        Long parentId = permission.getParentId();
        // 如果父权限还没有被添加，则添加并继续递归
        if (!completePermissionIds.contains(parentId)) {
            completePermissionIds.add(parentId);
            addParentPermissions(parentId, permissionMap, completePermissionIds);
        }
    }
}