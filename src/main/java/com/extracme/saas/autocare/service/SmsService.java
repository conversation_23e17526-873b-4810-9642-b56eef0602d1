package com.extracme.saas.autocare.service;

/**
 * 短信服务接口
 */
public interface SmsService {
    
    /**
     * 发送登录验证码
     *
     * @param mobile 手机号
     * @param ipAddress 请求IP地址
     * @return 发送结果：true-成功，false-失败
     * @throws IllegalArgumentException 手机号格式不正确、发送频率限制等异常
     */
    boolean sendLoginVerificationCode(String mobile, String ipAddress);
    
    /**
     * 验证验证码是否有效
     *
     * @param mobile 手机号
     * @param code 验证码
     * @param type 验证码类型：LOGIN-登录
     * @return 验证结果：true-验证通过，false-验证失败
     */
    boolean verifyCode(String mobile, String code, String type);
    
    /**
     * 标记验证码为已使用
     *
     * @param mobile 手机号
     * @param code 验证码
     * @param type 验证码类型：LOGIN-登录
     * @return 操作结果：true-成功，false-失败
     */
    boolean markCodeAsUsed(String mobile, String code, String type);
    
    /**
     * 清理过期验证码
     * 
     * @return 清理数量
     */
    int cleanExpiredCodes();
} 