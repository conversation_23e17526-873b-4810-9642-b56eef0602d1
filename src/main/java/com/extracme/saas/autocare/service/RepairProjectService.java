package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.RepairProjectCreateDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectUpdateStatusDTO;
import com.extracme.saas.autocare.model.vo.RepairProjectDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairProjectListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

/**
 * 修理项目服务接口
 */
public interface RepairProjectService {

    /**
     * 分页查询列表
     *
     * @param queryDTO 查询参数
     * @return 修理项目列表
     */
    BasePageVO<RepairProjectListVO> queryRepairProjectList(RepairProjectQueryDTO queryDTO);

    /**
     * 获取修理项目详情
     *
     * @param id 修理项目ID
     * @return 修理项目详情
     */
    RepairProjectDetailsVO getRepairProjectDetails(Long id);

    /**
     * 创建非合作修理项目
     *
     * @param createDTO 创建参数
     * @return 修理项目ID
     */
    void createRepairProject(RepairProjectCreateDTO createDTO);

    /**
     * 修改修理项目
     *
     * @param updateDTO 修改参数
     */
    void updateRepairProject(RepairProjectUpdateDTO updateDTO);

    /**
     * 删除修理项目
     *
     * @param id 修理项目ID
     */
    void deleteRepairProject(Long id);

    /**
     * 更新修理项目状态
     *
     * @param updateStatusDTO 更新参数
     */
    void updateRepairProjectStatus(RepairProjectUpdateStatusDTO updateStatusDTO);

}