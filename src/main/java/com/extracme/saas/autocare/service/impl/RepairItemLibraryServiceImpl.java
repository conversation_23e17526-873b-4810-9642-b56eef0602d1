package com.extracme.saas.autocare.service.impl;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.RepairItemTypeEnum;
import com.extracme.saas.autocare.enums.StatusEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryCheckQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryCreateDTO;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryLocalCreateDTO;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryLocalQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryLocalUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryUpdateStatusDTO;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibrary;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibraryLocal;
import com.extracme.saas.autocare.model.entity.MtcVehicleModel;
import com.extracme.saas.autocare.model.excel.RepairItemLibraryCreateExcel;
import com.extracme.saas.autocare.model.excel.RepairItemLibraryExportExcel;
import com.extracme.saas.autocare.model.excel.RepairItemLibraryUpdateExcel;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryCheckListVO;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryListVO;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryLocalVO;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairItemLibraryLocalService;
import com.extracme.saas.autocare.repository.TableRepairItemLibraryService;
import com.extracme.saas.autocare.repository.TableVehicleModelService;
import com.extracme.saas.autocare.service.RepairItemLibraryService;
import com.extracme.saas.autocare.util.BeanComparator;
import com.extracme.saas.autocare.util.ExcelExportUtil;
import com.extracme.saas.autocare.util.ExcelImportUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 维修项目服务实现类
 */
@Slf4j
@Service
public class RepairItemLibraryServiceImpl implements RepairItemLibraryService {

    @Autowired
    private TableRepairItemLibraryService tableRepairItemLibraryService;

    @Autowired
    private TableRepairItemLibraryLocalService tableRepairItemLibraryLocalService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private TableVehicleModelService tableVehicleModelService;

    @Override
    public BasePageVO<RepairItemLibraryListVO> queryItemLibraryList(RepairItemLibraryQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<RepairItemLibraryListVO> list = tableRepairItemLibraryService.queryList(queryDTO);
        PageInfo<RepairItemLibraryListVO> pageInfo = new PageInfo<>(list);
        return BasePageVO.of(list, pageInfo);
    }

    @Override
    public RepairItemLibraryVO getItemLibraryDetails(Long id) {
        MtcRepairItemLibrary repairItemLibrary = tableRepairItemLibraryService.selectById(id);
        if (repairItemLibrary == null) {
            return null;
        }
        RepairItemLibraryVO repairItemLibraryVO = new RepairItemLibraryVO();
        BeanUtils.copyProperties(repairItemLibrary, repairItemLibraryVO);
        if (repairItemLibrary.getVehicleModelSeq() != null){
            MtcVehicleModel vehicleModel = tableVehicleModelService.selectById(repairItemLibrary.getVehicleModelSeq());
            repairItemLibraryVO.setVehicleModelInfo(vehicleModel == null ? "" : vehicleModel.getVehicleModelName());
        }
        return repairItemLibraryVO;
    }

    @Override
    public void createItemLibrary(RepairItemLibraryCreateDTO createDTO) {
        MtcRepairItemLibrary repairItemLibrary = new MtcRepairItemLibrary();
        BeanUtils.copyProperties(createDTO, repairItemLibrary);
        // 获取项目编号
        int itemType = createDTO.getItemType();
        int maxSerialNumber = 1;
        MtcRepairItemLibrary lastData = tableRepairItemLibraryService.queryLastDataByItemType(itemType);
        if (lastData != null) {
            maxSerialNumber = lastData.getSerialNumber().intValue() + 1;
        }
        repairItemLibrary.setSerialNumber(maxSerialNumber);
        repairItemLibrary.setItemNo(getRepairItemNo(maxSerialNumber, itemType));
        tableRepairItemLibraryService.insert(repairItemLibrary);
        // 添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(repairItemLibrary.getId());
        mtcProcessLog.setOpeContent("新增主配件库信息");
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM_LIBRARY_NATIONAL);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    public void updateItemLibrary(RepairItemLibraryUpdateDTO updateDTO) {
        MtcRepairItemLibrary repairItemLibrary = tableRepairItemLibraryService.selectById(updateDTO.getId());
        if (repairItemLibrary == null) {
            throw new BusinessException("未找到该维修项目");
        }
        BeanUtils.copyProperties(updateDTO, repairItemLibrary);
        tableRepairItemLibraryService.updateSelectiveById(repairItemLibrary);

        // 添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(repairItemLibrary.getId());
        mtcProcessLog.setOpeContent("修改主配件库信息");
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM_LIBRARY_NATIONAL);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    public void updateItemLibraryStatus(RepairItemLibraryUpdateStatusDTO updateStatusDTO) {
        MtcRepairItemLibrary repairItemLibrary = tableRepairItemLibraryService.selectById(updateStatusDTO.getId());
        if (repairItemLibrary == null) {
            throw new BusinessException("未找到该维修项目");
        }
        repairItemLibrary.setStatus(updateStatusDTO.getStatus());
        tableRepairItemLibraryService.updateSelectiveById(repairItemLibrary);

        // 记录操作日志，使用Optional简化状态描述
        String statusDescription = Optional.ofNullable(repairItemLibrary.getStatus())
                .map(status -> status == 1 ? "启用" : "禁用")
                .orElse("未知状态");

        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(repairItemLibrary.getId());
        mtcProcessLog.setOpeContent(StrUtil.format("修改主配件库信息状态为{}", statusDescription));
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM_LIBRARY_NATIONAL);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    public BasePageVO<RepairItemLibraryLocalVO> queryLocalList(RepairItemLibraryLocalQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<MtcRepairItemLibraryLocal> list = tableRepairItemLibraryLocalService.queryList(queryDTO);
        PageInfo<MtcRepairItemLibraryLocal> pageInfo = new PageInfo<>(list);
        List<RepairItemLibraryLocalVO> voList = list.stream().map(repairDepotInfo -> {
            RepairItemLibraryLocalVO vo = new RepairItemLibraryLocalVO();
            BeanUtils.copyProperties(repairDepotInfo, vo);
            return vo;
        }).collect(Collectors.toList());
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public RepairItemLibraryLocalVO getItemLibraryLocalDetails(Long id) {
        MtcRepairItemLibraryLocal repairItemLibraryLocal = tableRepairItemLibraryLocalService.selectById(id);
        if (repairItemLibraryLocal == null) {
            return null;
        }
        RepairItemLibraryLocalVO repairItemLibraryLocalVO = new RepairItemLibraryLocalVO();
        BeanUtils.copyProperties(repairItemLibraryLocal, repairItemLibraryLocalVO);
        return repairItemLibraryLocalVO;

    }

    @Override
    public void createItemLibraryLocal(RepairItemLibraryLocalCreateDTO createDTO) {
        MtcRepairItemLibrary repairItemLibrary = tableRepairItemLibraryService.selectById(createDTO.getItemId());
        if (repairItemLibrary == null) {
            throw new BusinessException("未找到该维修项目");
        }
        MtcRepairItemLibraryLocal repairItemLibraryLocal = tableRepairItemLibraryLocalService.selectByItemIdAndOrgId(
                createDTO.getItemId(), createDTO.getOrgId());
        if (repairItemLibraryLocal != null) {
            throw new BusinessException("该机构配件信息已存在");
        }
        repairItemLibraryLocal = new MtcRepairItemLibraryLocal();
        BeanUtils.copyProperties(createDTO, repairItemLibraryLocal);
        // 使用系统操作人创建记录
        tableRepairItemLibraryLocalService.insert(repairItemLibraryLocal);
        // 添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(repairItemLibraryLocal.getId());
        mtcProcessLog.setOpeContent("新增本地配件库信息");
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM_LIBRARY_LOCAL);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateItemLibraryLocal(RepairItemLibraryLocalUpdateDTO updateDTO) {
        MtcRepairItemLibraryLocal repairItemLibraryLocal = tableRepairItemLibraryLocalService.selectById(updateDTO.getId());
        if (repairItemLibraryLocal == null) {
            throw new BusinessException("未找到该维修项目");
        }
        // 比较修改的内容 添加日志
        MtcRepairItemLibraryLocal compareLibraryLocal = new MtcRepairItemLibraryLocal();
        BeanUtils.copyProperties(repairItemLibraryLocal, compareLibraryLocal);
        BeanUtils.copyProperties(updateDTO, compareLibraryLocal);
        List<Map<String, String>> differences = BeanComparator.compareObjects(repairItemLibraryLocal, compareLibraryLocal);
        if (CollectionUtils.isNotEmpty(differences)) {
            StringBuilder sb = new StringBuilder();
            differences.forEach(difference -> {
                sb.append(StrUtil.format("{}：{} 修改为 {}\n", difference.get("fieldName"), difference.get("oldValue"), difference.get("newValue")));
            });
            //添加日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setRecordId(repairItemLibraryLocal.getId());
            operatorLog.setOpeContent(sb.toString());
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM_LIBRARY_LOCAL);
            tableOperatorLogService.insertSelective(operatorLog);
        }
        BeanUtils.copyProperties(updateDTO, repairItemLibraryLocal);
        tableRepairItemLibraryLocalService.updateSelectiveById(repairItemLibraryLocal);
    }

    @Override
    public void updateItemLibraryLocalStatus(RepairItemLibraryUpdateStatusDTO updateStatusDTO) {
        MtcRepairItemLibraryLocal repairItemLibraryLocal = tableRepairItemLibraryLocalService.selectById(updateStatusDTO.getId());
        if (repairItemLibraryLocal == null) {
            throw new BusinessException("未找到该维修项目");
        }
        repairItemLibraryLocal.setStatus(updateStatusDTO.getStatus());
        tableRepairItemLibraryLocalService.updateSelectiveById(repairItemLibraryLocal);
        // 记录操作日志，使用Optional简化状态描述
        String statusDescription = Optional.ofNullable(repairItemLibraryLocal.getStatus())
                .map(status -> status == 1 ? "启用" : "禁用")
                .orElse("未知状态");

        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(repairItemLibraryLocal.getId());
        mtcProcessLog.setOpeContent(StrUtil.format("修改本地配件库信息状态为{}", statusDescription));
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM_LIBRARY_LOCAL);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    public BasePageVO<RepairItemLibraryCheckListVO> queryLibraryCheckList(RepairItemLibraryCheckQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<RepairItemLibraryCheckListVO> list = tableRepairItemLibraryLocalService.queryLibraryCheckList(queryDTO);
        PageInfo<RepairItemLibraryCheckListVO> pageInfo = new PageInfo<>(list);
        List<RepairItemLibraryCheckListVO> voList = list.stream().map(info -> {
            // 工时费价格优先使用本地价格，如果本地价格为空则使用全国价格
            info.setHourFeeMarketPrice(
                    Optional.ofNullable(info.getHourFeeLocalMarketPrice())
                            .orElse(info.getHourFeeNationalMarketPrice())
            );

            // 材料费价格优先使用本地价格，如果本地价格为空则使用全国价格
            info.setMaterialCostMarketPrice(
                    Optional.ofNullable(info.getMaterialCostLocalMarketPrice())
                            .orElse(info.getMaterialCostNationalMarketPrice())
            );

            return info;
        }).collect(Collectors.toList());
        return BasePageVO.of(voList, pageInfo);
    }

    /**
     * 批量创建维修项目
     * 使用两阶段处理：第一阶段数据校验，第二阶段批量保存
     *
     * @param inputStream Excel文件输入流
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importRepairItemLibraryCreate(InputStream inputStream) {
        log.info("开始两阶段批量创建维修项目");

        try {
            // 创建导入配置
            ExcelImportUtil.ImportConfig<RepairItemLibraryCreateExcel> config =
                    ExcelImportUtil.builder(RepairItemLibraryCreateExcel.class)
                            .dataValidator(this::validateCreateData)
                            .dataSaver(this::saveCreateData)
                            .maxRows(10000)
                            .errorMessageBuilder(rowIndex -> String.format("第%d行创建失败", rowIndex))
                            .build();

            // 执行两阶段导入
            ExcelImportUtil.ImportResult<RepairItemLibraryCreateExcel> result =
                    ExcelImportUtil.importFromStream(inputStream, config);

            // 检查导入结果
            if (result.hasErrors()) {
                String errorMessage = String.join("; ", result.getErrorMessages());
                throw new BusinessException(errorMessage);
            }

            log.info("批量创建维修项目完成，总计: {}, 成功: {}, 失败: {}",
                    result.getTotalRows(), result.getSuccessRows(), result.getFailedRows());

        } catch (Exception e) {
            log.error("批量创建维修项目失败: {}", e.getMessage(), e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException(e.getMessage());
        }
    }


    /**
     * 批量修改维修项目
     * 使用两阶段处理：第一阶段数据校验，第二阶段批量保存
     *
     * @param inputStream Excel文件输入流
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importRepairItemLibraryUpdate(InputStream inputStream) {
        log.info("开始两阶段批量修改维修项目");

        try {
            // 创建导入配置
            ExcelImportUtil.ImportConfig<RepairItemLibraryUpdateExcel> config =
                    ExcelImportUtil.builder(RepairItemLibraryUpdateExcel.class)
                            .dataValidator(this::validateUpdateData)
                            .dataSaver(this::saveUpdateData)
                            .maxRows(10000)
                            .errorMessageBuilder(rowIndex -> String.format("第%d行修改失败", rowIndex))
                            .build();

            // 执行两阶段导入
            ExcelImportUtil.ImportResult<RepairItemLibraryUpdateExcel> result =
                    ExcelImportUtil.importFromStream(inputStream, config);

            // 检查导入结果
            if (result.hasErrors()) {
                String errorMessage = String.join("; ", result.getErrorMessages());
                throw new BusinessException(errorMessage);
            }

            log.info("批量修改维修项目完成，总计: {}, 成功: {}, 失败: {}",
                    result.getTotalRows(), result.getSuccessRows(), result.getFailedRows());

        } catch (Exception e) {
            log.error("批量修改维修项目失败: {}", e.getMessage(), e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 导出维修项目库数据
     * 使用游标分页导出，支持大数据量处理，避免深分页性能问题
     *
     * @param queryDTO 查询条件
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     */
    @Override
    public void exportRepairLibrary(RepairItemLibraryQueryDTO queryDTO, HttpServletRequest request, HttpServletResponse response) {
        log.info("开始使用游标分页导出维修项目库数据");

        try {
            // 创建导出配置，使用游标分页
            String fileName = "维修项目库数据_" + System.currentTimeMillis() + ".xlsx";
            ExcelExportUtil.ExportConfig<RepairItemLibraryExportExcel> config =
                    ExcelExportUtil.builder(RepairItemLibraryExportExcel.class)
                            .fileName(fileName)
                            .sheetName("维修项目库")
                            .cursorDataProvider(lastId -> getExportDataWithCursor(queryDTO, lastId))
                            .batchSize(1000)
                            .progressCallback((current, total) ->
                                    log.debug("游标分页导出进度: {}/{}", current, total > 0 ? total : "未知"))
                            .build();

            // 执行分批导出
            ExcelExportUtil.exportToResponse(config, response);

            log.info("游标分页导出维修项目库数据完成");

        } catch (Exception e) {
            log.error("导出维修项目库数据失败: {}", e.getMessage(), e);
            throw new BusinessException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取导出数据（游标分页）
     * 使用基于ID的游标分页，避免深分页性能问题
     *
     * @param queryDTO 查询条件
     * @param lastId 上一批次最后一条记录的ID（首次查询传入0）
     * @return 导出数据列表
     */
    private List<RepairItemLibraryExportExcel> getExportDataWithCursor(RepairItemLibraryQueryDTO queryDTO, Long lastId) {
        log.debug("获取导出数据（游标分页），lastId: {}", lastId);

        // 使用支持动态页大小的exportListWithPageSize方法，页大小固定为1000
        List<RepairItemLibraryListVO> dataList = tableRepairItemLibraryService.exportListWithPageSize(lastId, queryDTO, 1000);

        // 转换为导出Excel实体
        List<RepairItemLibraryExportExcel> exportList = dataList.stream()
                .map(this::convertToExportExcel)
                .collect(Collectors.toList());

        log.debug("获取导出数据完成（游标分页），实际数据量: {}", exportList.size());
        return exportList;
    }

    /**
     * 转换为导出Excel实体
     * 使用Optional简化数据转换逻辑，避免冗长的三元表达式
     *
     * @param repairItemLibrary 数据库实体
     * @return 导出Excel实体
     */
    private RepairItemLibraryExportExcel convertToExportExcel(RepairItemLibraryListVO repairItemLibrary) {
        RepairItemLibraryExportExcel exportExcel = new RepairItemLibraryExportExcel();

        // 基本信息
        exportExcel.setItemNo(repairItemLibrary.getItemNo());
        exportExcel.setItemName(repairItemLibrary.getItemName());
        exportExcel.setVehicleModelInfo(Optional.ofNullable(repairItemLibrary.getVehicleModelInfo()).orElse(""));

        // 项目类型转换
        exportExcel.setItemTypeName(Optional.ofNullable(repairItemLibrary.getItemType())
                .map(RepairItemTypeEnum::getNameByCode)
                .orElse(""));

        // 价格信息（使用Optional简化空值处理）
        exportExcel.setHourFeeNationalMarketPrice(repairItemLibrary.getHourFeeNationalMarketPrice());
        exportExcel.setHourFeeNationalHighestPrice(repairItemLibrary.getHourFeeNationalHighestPrice());
        exportExcel.setMaterialCostNationalMarketPrice(repairItemLibrary.getMaterialCostNationalMarketPrice());
        exportExcel.setMaterialCostNationalHighestPrice(repairItemLibrary.getMaterialCostNationalHighestPrice());

        // 状态转换（现在StatusEnum支持Integer参数）
        exportExcel.setStatusName(Optional.ofNullable(repairItemLibrary.getStatus())
                .map(StatusEnum::getByCode)
                .map(StatusEnum::getDescription)
                .orElse(""));

        return exportExcel;
    }

    /**
     * 数据校验方法 - 创建数据
     *
     * @param data Excel行数据
     * @param rowIndex 行索引
     * @throws Exception 校验异常
     */
    private void validateCreateData(RepairItemLibraryCreateExcel data, int rowIndex) throws Exception {
        // 校验项目名称
        if (data.getItemName() == null || data.getItemName().trim().isEmpty()) {
            throw new Exception("项目名称不能为空");
        }

        // 校验项目类型
        if (data.getItemType() == null || data.getItemType().trim().isEmpty()) {
            throw new Exception("项目类型不能为空");
        }

        Integer itemTypeCode = RepairItemTypeEnum.getCodeByName(data.getItemType());
        if (itemTypeCode == null) {
            throw new Exception("项目类型格式错误，请填写：保养、终端、维修");
        }

        // 校验车型信息
        if (data.getVehicleModelInfo() == null || data.getVehicleModelInfo().trim().isEmpty()) {
            throw new Exception("维修项目必须填写车型信息");
        }

        // 校验工时费全国市场价
        if (data.getHourFeeNationalMarketPrice() == null) {
            throw new Exception("工时费全国市场价不能为空");
        }
        validateAmount(data.getHourFeeNationalMarketPrice(), "工时费全国市场价");

        // 校验工时费全国上限（可选）
        if (data.getHourFeeNationalHighestPrice() != null) {
            validateAmount(data.getHourFeeNationalHighestPrice(), "工时费全国上限");
        }

        // 校验材料费全国市场价
        if (data.getMaterialCostNationalMarketPrice() == null) {
            throw new Exception("材料费全国市场价不能为空");
        }
        validateAmount(data.getMaterialCostNationalMarketPrice(), "材料费全国市场价");

        // 校验材料费全国上限（可选）
        if (data.getMaterialCostNationalHighestPrice() != null) {
            validateAmount(data.getMaterialCostNationalHighestPrice(), "材料费全国上限");
        }

        // 校验唯一性
        Long vehicleModelSeq = (data.getVehicleModelInfo() != null && !data.getVehicleModelInfo().trim().isEmpty()) ? -1L : null;
        boolean isUnique = tableRepairItemLibraryService.checkUnique(
                null, data.getItemName(), itemTypeCode, vehicleModelSeq);
        if (isUnique) {
            throw new Exception("维修项目重复");
        }
    }

    /**
     * 数据保存方法 - 创建数据
     *
     * @param dataList 验证通过的数据列表
     * @throws Exception 保存异常
     */
    private void saveCreateData(List<RepairItemLibraryCreateExcel> dataList) throws Exception {
        log.info("开始批量保存创建数据，数据量: {}", dataList.size());

        // 校验车型信息
        List<MtcVehicleModel> vehicleModels = tableVehicleModelService.findAll();
        Map<String, MtcVehicleModel> vehicleModelMap = vehicleModels.stream().collect(Collectors.toMap(MtcVehicleModel::getVehicleModelName, data -> data));
        for (RepairItemLibraryCreateExcel excelData : dataList) {
            try {
                // 转换为实体对象
                MtcRepairItemLibrary entity = convertCreateExcelToEntity(excelData);
                if (StringUtils.isNotBlank(excelData.getVehicleModelInfo())){
                    MtcVehicleModel vehicleModel = vehicleModelMap.get(excelData.getVehicleModelInfo());
                    if (vehicleModel == null) {
                        throw new Exception(StrUtil.format("车型[{}]不存在", excelData.getVehicleModelInfo()));
                    }
                    entity.setVehicleModelSeq(vehicleModel.getId());
                    entity.setVehicleModelInfo(vehicleModel.getVehicleModelName());
                }
                // 保存到数据库
                tableRepairItemLibraryService.insert(entity);

                // 记录操作日志
                recordCreateOperationLog(entity.getId());

                log.debug("成功保存维修项目: {}", entity.getItemName());

            } catch (Exception e) {
                log.error("保存维修项目失败: {}, 错误: {}", excelData.getItemName(), e.getMessage(), e);
                throw new Exception("保存维修项目失败: " + excelData.getItemName() + ", 错误: " + e.getMessage());
            }
        }

        log.info("批量保存创建数据完成，成功保存: {}条", dataList.size());
    }

    /**
     * 转换Excel数据为实体对象 - 创建
     */
    private MtcRepairItemLibrary convertCreateExcelToEntity(RepairItemLibraryCreateExcel excelData) {
        MtcRepairItemLibrary entity = new MtcRepairItemLibrary();

        // 基本属性复制
        BeanUtils.copyProperties(excelData, entity);
        // 获取项目编号
        Integer itemTypeCode = RepairItemTypeEnum.getCodeByName(excelData.getItemType());
        int maxSerialNumber = 1;
        MtcRepairItemLibrary lastData = tableRepairItemLibraryService.queryLastDataByItemType(itemTypeCode);
        if (lastData != null) {
            maxSerialNumber = lastData.getSerialNumber().intValue() + 1;
        }
        entity.setSerialNumber(maxSerialNumber);

        // 设置项目编号
        entity.setItemNo(getRepairItemNo(maxSerialNumber, itemTypeCode));
        // 设置项目类型编码
        entity.setItemType(itemTypeCode);

        // 设置车型序号（如果有车型信息）
        if (excelData.getVehicleModelInfo() != null && !excelData.getVehicleModelInfo().trim().isEmpty()) {
            // 这里应该根据车型名称查找车型ID，暂时设置为-1
            entity.setVehicleModelSeq(-1L);
        }

        return entity;
    }

    /**
     * 记录创建操作日志
     */
    private void recordCreateOperationLog(Long recordId) {
        MtcOperatorLog operatorLog = new MtcOperatorLog();
        operatorLog.setRecordId(recordId);
        operatorLog.setOpeContent("批量新增维修项目");
        operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM_LIBRARY_NATIONAL);
        tableOperatorLogService.insertSelective(operatorLog);
    }

    /**
     * 数据校验方法 - 更新数据
     *
     * @param data Excel行数据
     * @param rowIndex 行索引
     * @throws Exception 校验异常
     */
    private void validateUpdateData(RepairItemLibraryUpdateExcel data, int rowIndex) throws Exception {
        // 校验项目编号
        if (data.getItemNo() == null || data.getItemNo().trim().isEmpty()) {
            throw new Exception("项目编号不能为空");
        }

        // 校验项目名称
        if (data.getItemName() == null || data.getItemName().trim().isEmpty()) {
            throw new Exception("项目名称不能为空");
        }

        // 校验工时费全国市场价
        if (data.getHourFeeNationalMarketPrice() == null) {
            throw new Exception("工时费全国市场价不能为空");
        }
        validateAmount(data.getHourFeeNationalMarketPrice(), "工时费全国市场价");

        // 校验工时费全国上限（可选）
        if (data.getHourFeeNationalHighestPrice() != null) {
            validateAmount(data.getHourFeeNationalHighestPrice(), "工时费全国上限");
        }

        // 校验材料费全国市场价
        if (data.getMaterialCostNationalMarketPrice() == null) {
            throw new Exception("材料费全国市场价不能为空");
        }
        validateAmount(data.getMaterialCostNationalMarketPrice(), "材料费全国市场价");

        // 校验材料费全国上限（可选）
        if (data.getMaterialCostNationalHighestPrice() != null) {
            validateAmount(data.getMaterialCostNationalHighestPrice(), "材料费全国上限");
        }

        // 校验状态
        if (data.getStatus() == null || data.getStatus().trim().isEmpty()) {
            throw new Exception("状态不能为空");
        }

        StatusEnum statusEnum = StatusEnum.getByCode(data.getStatus());
        if (statusEnum == null) {
            throw new Exception("状态格式错误，请填写：启用、禁用");
        }

        // 校验项目是否存在
        MtcRepairItemLibrary existingEntity = tableRepairItemLibraryService.queryByItemNo(data.getItemNo());
        if (existingEntity == null) {
            throw new Exception("项目编号不存在");
        }

        // 校验唯一性（排除当前记录）
        boolean isUnique = tableRepairItemLibraryService.checkUnique(
                data.getItemNo(), data.getItemName(), existingEntity.getItemType(), existingEntity.getVehicleModelSeq());
        if (isUnique) {
            throw new Exception("维修项目重复");
        }
    }

    /**
     * 数据保存方法 - 更新数据
     *
     * @param dataList 验证通过的数据列表
     * @throws Exception 保存异常
     */
    private void saveUpdateData(List<RepairItemLibraryUpdateExcel> dataList) throws Exception {
        log.info("开始批量保存更新数据，数据量: {}", dataList.size());

        for (RepairItemLibraryUpdateExcel excelData : dataList) {
            try {
                // 根据项目编号查找现有记录
                MtcRepairItemLibrary existingEntity = tableRepairItemLibraryService.queryByItemNo(excelData.getItemNo());
                if (existingEntity == null) {
                    throw new Exception("项目编号不存在: " + excelData.getItemNo());
                }

                // 更新实体对象
                updateEntityFromExcel(existingEntity, excelData);

                // 保存到数据库
                tableRepairItemLibraryService.updateSelectiveById(existingEntity);

                // 记录操作日志
                recordUpdateOperationLog(existingEntity.getId());

                log.debug("成功更新维修项目: {}", existingEntity.getItemName());

            } catch (Exception e) {
                log.error("更新维修项目失败: {}, 错误: {}", excelData.getItemNo(), e.getMessage(), e);
                throw new Exception("更新维修项目失败: " + excelData.getItemNo() + ", 错误: " + e.getMessage());
            }
        }

        log.info("批量保存更新数据完成，成功更新: {}条", dataList.size());
    }

    /**
     * 更新实体对象
     */
    private void updateEntityFromExcel(MtcRepairItemLibrary entity, RepairItemLibraryUpdateExcel excelData) {
        // 更新项目名称
        entity.setItemName(excelData.getItemName());

        // 更新价格信息
        entity.setHourFeeNationalMarketPrice(excelData.getHourFeeNationalMarketPrice());
        entity.setHourFeeNationalHighestPrice(excelData.getHourFeeNationalHighestPrice());
        entity.setMaterialCostNationalMarketPrice(excelData.getMaterialCostNationalMarketPrice());
        entity.setMaterialCostNationalHighestPrice(excelData.getMaterialCostNationalHighestPrice());

        // 更新状态
        StatusEnum statusEnum = StatusEnum.getByCode(excelData.getStatus());
        entity.setStatus(statusEnum.getCode());
    }

    /**
     * 记录更新操作日志
     */
    private void recordUpdateOperationLog(Long recordId) {
        MtcOperatorLog operatorLog = new MtcOperatorLog();
        operatorLog.setRecordId(recordId);
        operatorLog.setOpeContent("批量修改维修项目");
        operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM_LIBRARY_NATIONAL);
        tableOperatorLogService.insertSelective(operatorLog);
    }

    /**
     * 校验金额
     */
    private void validateAmount(java.math.BigDecimal amount, String fieldName) throws Exception {
        if (amount.compareTo(java.math.BigDecimal.ZERO) < 0) {
            throw new Exception(fieldName + "不能为负数");
        }
        if (amount.scale() > 2) {
            throw new Exception(fieldName + "最多保留两位小数");
        }
    }

    public static String getRepairItemNo(int num, int type) {
        String pre = "";
        String no = "";
        if (type == 1) {
            pre = "XMBY";
        } else if (type == 2) {
            pre = "XMZD";
        } else if (type == 3) {
            pre = "XMWX";
        }
        if (num <= 99999) {
            no = String.format("%05d", num);
        } else {
            no = String.valueOf(num);
        }
        return pre + no;
    }
}