package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.InRepairSubmitDTO;
import com.extracme.saas.autocare.model.dto.InRepairUpdateDTO;

/**
 * 车辆维修活动节点服务接口
 * <AUTHOR>
 * @date 2025/05/21
 */
public interface InRepairService {

    /**
     * 车辆维修-保存信息
     *
     * @param updateDTO 更新维修任务的DTO
     */
    void saveVehicleInRepair(InRepairUpdateDTO updateDTO);

    /**
     * 车辆维修-申请验收
     *
     * @param submitDTO 更新维修任务的DTO
     */
    void changeVehicleCheck(InRepairSubmitDTO submitDTO);
}
