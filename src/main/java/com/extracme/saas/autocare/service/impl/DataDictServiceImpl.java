package com.extracme.saas.autocare.service.impl;


import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;

import com.extracme.saas.autocare.model.dto.DataDictAddDTO;
import com.extracme.saas.autocare.model.dto.DataDictDTO;
import com.extracme.saas.autocare.model.dto.DataDictUpdateDTO;
import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.extracme.saas.autocare.model.entity.DataDictInfo;
import com.extracme.saas.autocare.model.vo.DataDictDetailVO;
import com.extracme.saas.autocare.model.vo.DataDictSimpleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableDataDictInfoService;
import com.extracme.saas.autocare.service.DataDictService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * 数据字典服务实现类
 */
@Service
public class DataDictServiceImpl implements DataDictService {

    @Autowired
    private TableDataDictInfoService dataDictInfoService;

    @Override
    public BasePageVO<DataDictSimpleVO> pageList(BasePageDTO pageDTO) {
        Assert.notNull(pageDTO, "分页参数不能为空");
        Assert.isTrue(pageDTO.getPageNum() > 0, "页码必须大于0");
        Assert.isTrue(pageDTO.getPageSize() > 0, "每页显示记录数必须大于0");

        PageHelper.startPage(pageDTO.getPageNum(), pageDTO.getPageSize());

        // 使用条件查询，支持字典名称和字典编码的模糊查询
        List<DataDictInfo> originalList = dataDictInfoService.findByCondition(
            pageDTO.getDictName(),
            pageDTO.getDictCode()
        );

        PageInfo<DataDictInfo> pageInfo = new PageInfo<>(originalList);

        // 转换为简化VO，只包含需要的字段
        List<DataDictSimpleVO> simplifiedList = originalList.stream()
                .map(this::convertToSimpleVO)
                .collect(Collectors.toList());

        return BasePageVO.of(simplifiedList, pageInfo);
    }

    /**
     * 将DataDictInfo实体转换为DataDictSimpleVO
     *
     * @param entity 数据字典实体
     * @return 简化VO
     */
    private DataDictSimpleVO convertToSimpleVO(DataDictInfo entity) {
        if (entity == null) {
            return null;
        }

        DataDictSimpleVO vo = new DataDictSimpleVO();
        vo.setId(entity.getId());
        vo.setDataName(entity.getDataName());
        vo.setDataCode(entity.getDataCode());
        vo.setCodeType(entity.getCodeType());
        return vo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> DataDictInfo add(DataDictAddDTO<T> addDTO) {
        Assert.notNull(addDTO, "数据字典信息不能为空");
        Assert.hasText(addDTO.getDataCode(), "字典编码不能为空");
        Assert.hasText(addDTO.getDataName(), "字典名称不能为空");
        Assert.notNull(addDTO.getCodeType(), "字段类型不能为空");


        // 验证字典编码是否已存在
        DataDictInfo existDict = dataDictInfoService.selectByDataCode(addDTO.getDataCode());
        Assert.isNull(existDict, "字典编码已存在");


        // 将DTO转换为实体
        DataDictInfo dataDict = new DataDictInfo();
        dataDict.setDataName(addDTO.getDataName());
        dataDict.setDataCode(addDTO.getDataCode());
        dataDict.setCodeType(addDTO.getCodeType());

        // 将字典项列表转换为JSON字符串
        if (addDTO.getDictItems() != null && !addDTO.getDictItems().isEmpty()) {
            dataDict.setDataValue(JSON.toJSONString(addDTO.getDictItems()));
        }

        // 使用系统操作人创建记录
        return dataDictInfoService.insert(dataDict, "system");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> DataDictInfo update(DataDictUpdateDTO<T> updateDTO) {
        Assert.notNull(updateDTO, "数据字典信息不能为空");
        Assert.notNull(updateDTO.getId(), "数据字典ID不能为空");
        Assert.hasText(updateDTO.getDataCode(), "字典编码不能为空");
        Assert.hasText(updateDTO.getDataName(), "字典名称不能为空");
        Assert.notNull(updateDTO.getCodeType(), "字段类型不能为空");

        // 验证数据字典是否存在
        DataDictInfo existDict = dataDictInfoService.selectById(updateDTO.getId());
        Assert.notNull(existDict, "数据字典不存在");

        // 将DTO转换为实体
        DataDictInfo dataDict = new DataDictInfo();
        dataDict.setId(updateDTO.getId());
        dataDict.setDataName(updateDTO.getDataName());
        dataDict.setDataCode(updateDTO.getDataCode());
        dataDict.setCodeType(updateDTO.getCodeType());

        // 将字典项列表转换为JSON字符串
        if (updateDTO.getDictItems() != null && !updateDTO.getDictItems().isEmpty()) {
            dataDict.setDataValue(JSON.toJSONString(updateDTO.getDictItems()));
        }

        // 使用系统操作人更新记录
        dataDictInfoService.updateSelectiveById(dataDict, "system");
        return dataDictInfoService.selectById(dataDict.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        Assert.notNull(id, "数据字典ID不能为空");

        // 验证数据字典是否存在
        DataDictInfo existDict = dataDictInfoService.selectById(id);
        Assert.notNull(existDict, "数据字典不存在");

        // 执行删除操作
        int result = dataDictInfoService.deleteById(id);
        return result > 0;
    }

    @Override
    public <T> DataDictDetailVO<T> getById(Long id) {
        Assert.notNull(id, "数据字典ID不能为空");

        // 查询数据字典
        DataDictInfo dataDictInfo = dataDictInfoService.selectById(id);
        if (dataDictInfo == null) {
            return null;
        }

        // 转换为详情VO
        return convertToDetailVO(dataDictInfo);
    }

    @Override
    public <T> DataDictDetailVO<T> getByCode(String dataCode) {
        Assert.hasText(dataCode, "数据字典编码不能为空");

        // 查询数据字典
        DataDictInfo dataDictInfo = dataDictInfoService.selectByDataCode(dataCode);
        if (dataDictInfo == null) {
            return null;
        }

        // 转换为详情VO
        return convertToDetailVO(dataDictInfo);
    }

    /**
     * 将DataDictInfo实体转换为DataDictDetailVO
     *
     * @param <T> 字典值的类型，可以是String、Integer等
     * @param dataDictInfo 数据字典实体
     * @return 数据字典详情VO
     */
    @SuppressWarnings("unchecked")
    private <T> DataDictDetailVO<T> convertToDetailVO(DataDictInfo dataDictInfo) {
        if (dataDictInfo == null) {
            return null;
        }

        DataDictDetailVO<T> detailVO = new DataDictDetailVO<>();
        detailVO.setId(dataDictInfo.getId());
        detailVO.setDataName(dataDictInfo.getDataName());
        detailVO.setDataCode(dataDictInfo.getDataCode());
        detailVO.setCodeType(dataDictInfo.getCodeType());
        detailVO.setCreateTime(dataDictInfo.getCreateTime());
        detailVO.setCreateBy(dataDictInfo.getCreateBy());
        detailVO.setUpdateTime(dataDictInfo.getUpdateTime());
        detailVO.setUpdateBy(dataDictInfo.getUpdateBy());

        // 解析字典项JSON
        if (StringUtils.hasText(dataDictInfo.getDataValue())) {
            try {
                // 使用泛型安全的方式解析JSON
                List<?> rawList = JSON.parseArray(dataDictInfo.getDataValue(), DataDictDTO.class);
                // 由于泛型擦除，这里的强制转换在运行时是安全的
                List<DataDictDTO<T>> typedDictItems = (List<DataDictDTO<T>>) rawList;
                detailVO.setDictItems(typedDictItems);
            } catch (Exception e) {
                // 解析失败，设置为空列表
                detailVO.setDictItems(null);
            }
        }

        return detailVO;
    }
}