package com.extracme.saas.autocare.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.OrgInfoTreeVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.service.OrgService;
import com.extracme.saas.autocare.util.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 组织机构服务实现类
 */
@Slf4j
@Service
public class OrgServiceImpl implements OrgService {

    @Autowired
    private TableOrgInfoService orgInfoRepository;

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<String>> getOrgCombo() {
        List<String> userOrgIds = SessionUtils.getDirectOrgIds();
        if (CollectionUtils.isEmpty(userOrgIds)) {
            return Collections.emptyList();
        }
        List<ComboVO<String>> result = new ArrayList<>();
        List<MtcOrgInfo> orgInfos = orgInfoRepository.findByOrgIds(userOrgIds);
        for (MtcOrgInfo orgInfo : orgInfos) {
            ComboVO<String> comboVO = new ComboVO<>();
            comboVO.setId(orgInfo.getOrgId());
            comboVO.setValue(orgInfo.getOrgName());
            result.add(comboVO);
        }
        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<String>> getOrgComboByTenantId(Long tenantId) {
        try {
            // 保存当前租户上下文
            Long currentTenantId = TenantContextHolder.getTenantId();

            try {
                // 如果传入的租户ID不为空，则设置租户上下文
                if (tenantId != null) {
                    TenantContextHolder.setTenant(tenantId);
                }

                // 查询组织机构
                List<MtcOrgInfo> orgs = orgInfoRepository.findValidOrgs();
                List<ComboVO<String>> result = new ArrayList<>();

                for (MtcOrgInfo org : orgs) {
                    ComboVO<String> vo = new ComboVO<>();
                    vo.setId(org.getOrgId());
                    vo.setValue(org.getOrgName());
                    result.add(vo);
                }
                return result;
            } finally {
                // 恢复原始租户上下文
                if (tenantId != null && !tenantId.equals(currentTenantId)) {
                    if (currentTenantId != null) {
                        TenantContextHolder.setTenant(currentTenantId);
                    } else {
                        TenantContextHolder.clear();
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取组织机构下拉列表失败", e);
            throw new RuntimeException("获取组织机构下拉列表失败");
        }
    }

    @Override
    public List<OrgInfoTreeVO> getOrgTree() {
        SysUser user = SessionUtils.getLoginUser().getUser();
        int accountType = user.getAccountType();

        List<MtcOrgInfo> orgs;
        if (accountType == 0) {
            // 超级管理员：查询所有组织机构
            orgs = orgInfoRepository.findValidOrgs();
        } else {
            // 普通用户：根据用户关联的机构查询
            List<String> userOrgIds = SessionUtils.getDirectOrgIds();
            if (userOrgIds.isEmpty()) {
                // 如果用户没有关联任何机构，返回空列表
                return Collections.emptyList();
            }

            // 查询用户关联的所有机构的组织机构信息
            orgs = new ArrayList<>();
            for (String orgId : userOrgIds) {
                List<MtcOrgInfo> orgsByOrgId = orgInfoRepository.findValidOrgsByOrgId(orgId);
                orgs.addAll(orgsByOrgId);
            }
        }

        if (CollectionUtils.isEmpty(orgs)){
            return Collections.emptyList();
        }
        return getOrgTree(orgs);
    }

    @Override
    public List<OrgInfoTreeVO> getOrgTreeByTenant(Long tenantId) {
        try {
            // 保存当前租户上下文
            Long currentTenantId = TenantContextHolder.getTenantId();
            try {
                // 如果传入的租户ID不为空，则设置租户上下文
                if (tenantId != null) {
                    TenantContextHolder.setTenant(tenantId);
                }
                // 查询组织机构
                List<MtcOrgInfo> orgs = orgInfoRepository.findValidOrgs();
                if (CollectionUtils.isEmpty(orgs)){
                    return Collections.emptyList();
                }
                return getOrgTree(orgs);
            } finally {
                // 恢复原始租户上下文
                if (tenantId != null && !tenantId.equals(currentTenantId)) {
                    if (currentTenantId != null) {
                        TenantContextHolder.setTenant(currentTenantId);
                    } else {
                        TenantContextHolder.clear();
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取组织机构下拉列表失败", e);
            throw new RuntimeException("获取组织机构下拉列表失败");
        }
    }

    /**
     * 获取资源树
     * @param list
     * @return
     */
    public static List<OrgInfoTreeVO> getOrgTree(List<MtcOrgInfo> list){
        String rootId = list.get(0).getParentId();
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        list.forEach(info -> {
            Map<String, Object> map = BeanUtil.beanToMap(info);
            TreeNode<String> node = new TreeNode<>(map.get("uniqueId").toString(), map.get("parentId").toString(), map.get("orgName").toString(), 0L);
            node.setExtra(map);
            nodeList.add(node);
        });
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setWeightKey("weight");
        treeNodeConfig.setIdKey("id");
        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(nodeList, rootId, treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    tree.putExtra("orgName", treeNode.getExtra().get("orgName"));
                    tree.putExtra("orgId", treeNode.getExtra().get("orgId"));
                    Long sort = Convert.toLong(treeNode.getExtra().get("position"), 0L);
                    Date createTime = (Date) treeNode.getExtra().get("createTime");
                    if (sort == 0 && treeNode.getExtra().get("createTime") != null) {
                        sort = createTime.getTime();
                    }
                    tree.putExtra("weight", sort);
                });
        if (ObjectUtil.length(treeNodes) == 0) {
            throw new RuntimeException("组织机构结构错误");
        }
        List<OrgInfoTreeVO> treeListResponse = BeanUtil.copyToList(treeNodes, OrgInfoTreeVO.class);// JSONUtil.toBean(JSONUtil.toJsonStr(tree), OrgTreeResponse.class);
        return treeListResponse;
    }
}
