package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.TenantIdentificationResultDTO;

/**
 * 租户识别服务接口
 */
public interface TenantIdentificationService {

    /**
     * 根据同步密钥识别租户
     * 
     * @param syncKey 同步密钥
     * @return 识别结果，包含租户信息
     */
    TenantIdentificationResultDTO identifyTenantBySyncKey(String syncKey);

    /**
     * 为租户生成新的同步密钥
     * 
     * @param tenantCode 租户编码
     * @return 生成的同步密钥
     */
    String generateSyncKey(String tenantCode);

    /**
     * 验证同步密钥是否有效
     * 
     * @param syncKey 同步密钥
     * @return 是否有效
     */
    boolean validateSyncKey(String syncKey);

    /**
     * 更新同步密钥使用统计
     * 
     * @param syncKey 同步密钥
     */
    void updateSyncKeyUsageStats(String syncKey);
}
