package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.LeavingStatusEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.mapper.extend.MtcRepairTaskExtendMapper;
import com.extracme.saas.autocare.model.dto.VehicleLeavingFactoryQueryDTO;
import com.extracme.saas.autocare.model.dto.VehicleLeavingFactorySubmitDTO;
import com.extracme.saas.autocare.model.entity.*;
import com.extracme.saas.autocare.model.excel.VehicleLeavingFactoryExportExcel;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryCountVO;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryDetailsVO;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryResultVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.*;
import com.extracme.saas.autocare.service.VehicleLeavingFactoryService;
import com.extracme.saas.autocare.util.ExcelExportUtil;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 车辆出厂登记服务实现类
 *
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Slf4j
@Service
public class VehicleLeavingFactoryServiceImpl implements VehicleLeavingFactoryService {

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private MtcRepairTaskExtendMapper mtcRepairTaskMapper;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private TableRepairTaskLeavingFactoryService tableRepairTaskLeavingFactoryService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableActivityDefinitionService tableActivityDefinitionService;

    @Autowired
    private TableActivityStatusService tableActivityStatusService;

    /**
     * 查询任务信息
     *
     * @param queryDTO 查询条件
     * @return 出厂登记统计信息
     */
    @Override
    public VehicleLeavingFactoryCountVO queryLeavingFactoryList(VehicleLeavingFactoryQueryDTO queryDTO) {        
        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            queryDTO.setRepairDepotId(repairDepotId);
        }
        // 登录人公司
        else {
            queryDTO.setLoginUserOrgIds(SessionUtils.getAllAccessibleOrgIds());
        }

        // 统计各状态数量
        VehicleLeavingFactoryCountVO countVO = new VehicleLeavingFactoryCountVO();

        // 分页查询
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<VehicleLeavingFactoryResultVO> vehicleLeavingFactoryResultVOList = tableRepairTaskLeavingFactoryService
                .selectLeavingFactoryList(queryDTO);
        PageInfo<VehicleLeavingFactoryResultVO> pageInfo = new PageInfo<>(vehicleLeavingFactoryResultVOList);

        countVO.setPageInfo(BasePageVO.of(vehicleLeavingFactoryResultVOList, pageInfo));

        // 待登记数量
        long waitCount = tableRepairTaskLeavingFactoryService.selectLeavingFactoryCount(queryDTO, LeavingStatusEnum.NOT_REGISTERED.getCode());

        // 已登记数量
        long finishCount = tableRepairTaskLeavingFactoryService.selectLeavingFactoryCount(queryDTO, LeavingStatusEnum.REGISTERED.getCode());

        // 已关闭数量
        long closeCount = tableRepairTaskLeavingFactoryService.selectLeavingFactoryCount(queryDTO, LeavingStatusEnum.CLOSED.getCode());

        countVO.setWaitCount(waitCount);
        countVO.setFinishCount(finishCount);
        countVO.setCloseCount(closeCount);

        return countVO;
    }

    /**
     * 导出数据
     *
     * @param queryDTO 查询条件
     * @param response HTTP响应
     */
    @Override
    public void exportData(VehicleLeavingFactoryQueryDTO queryDTO, HttpServletResponse response) {
        log.info("开始使用游标分页导出车辆出厂登记数据");
        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            queryDTO.setRepairDepotId(repairDepotId);
        }
        // 登录人公司
        else {
            queryDTO.setLoginUserOrgIds(SessionUtils.getAllAccessibleOrgIds());
        }

        try {
            // 预先查询所有活动节点定义和活动状态定义，避免在每批次数据处理时重复查询
            List<ActivityDefinition> activityDefinitions = tableActivityDefinitionService.findAll();
            List<ActivityStatus> activityStatuses = tableActivityStatusService.findAll();

            // 创建导出配置，使用游标分页
            String fileName = "车辆出厂登记数据_" + System.currentTimeMillis() + ".xlsx";
            ExcelExportUtil.ExportConfig<VehicleLeavingFactoryExportExcel> config =
                    ExcelExportUtil.builder(VehicleLeavingFactoryExportExcel.class)
                            .fileName(fileName)
                            .sheetName("车辆出厂登记")
                            .cursorDataProvider(lastId -> getExportDataWithCursor(queryDTO, lastId, activityDefinitions, activityStatuses))
                            .batchSize(1000)
                            .progressCallback((current, total) ->
                                    log.debug("游标分页导出进度: {}/{}", current, total > 0 ? total : "未知"))
                            .build();

            // 执行分批导出
            ExcelExportUtil.exportToResponse(config, response);

            log.info("游标分页导出车辆出厂登记数据完成");
        } catch (Exception e) {
            log.error("导出车辆出厂登记数据失败: {}", e.getMessage(), e);
            throw new BusinessException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取导出数据（游标分页）
     * 使用基于ID的游标分页，避免深分页性能问题
     *
     * @param queryDTO            查询条件
     * @param lastId              上一批次最后一条记录的ID（首次查询传入0）
     * @param activityDefinitions 活动定义列表（预先查询，避免重复查询）
     * @param activityStatuses    活动状态列表（预先查询，避免重复查询）
     * @return 导出数据列表
     */
    private List<VehicleLeavingFactoryExportExcel> getExportDataWithCursor(
            VehicleLeavingFactoryQueryDTO queryDTO, Long lastId,
            List<ActivityDefinition> activityDefinitions,
            List<ActivityStatus> activityStatuses) {
        log.debug("获取导出数据（游标分页），lastId: {}", lastId);

        // 查询数据
        List<VehicleLeavingFactoryResultVO> dataList = tableRepairTaskLeavingFactoryService.exportLeavingFactoryList(queryDTO, lastId, 1000);

        // 设置活动名称和状态名称
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(item -> {
                // 设置活动节点名称
                setActivityNameByCode(item, activityDefinitions);
                // 设置活动状态名称
                setStatusNameByCode(item, activityStatuses);
            });
        }

        // 转换为导出Excel实体
        List<VehicleLeavingFactoryExportExcel> exportList = dataList.stream()
                .map(this::convertToExportExcel)
                .collect(Collectors.toList());

        log.debug("获取导出数据完成（游标分页），实际数据量: {}", exportList.size());
        return exportList;
    }

    /**
     * 转换为导出Excel实体
     *
     * @param resultVO 查询结果VO
     * @return 导出Excel实体
     */
    private VehicleLeavingFactoryExportExcel convertToExportExcel(VehicleLeavingFactoryResultVO resultVO) {
        VehicleLeavingFactoryExportExcel exportExcel = new VehicleLeavingFactoryExportExcel();

        // 使用BeanUtils进行属性拷贝
        BeanUtils.copyProperties(resultVO, exportExcel);

        // 格式化日期时间
        if (resultVO.getTaskInflowTime() != null) {
            exportExcel.setTaskInflowTimeString(DateFormatUtils.format(resultVO.getTaskInflowTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (resultVO.getVehicleCheckTime() != null) {
            exportExcel.setVehicleCheckTimeString(DateFormatUtils.format(resultVO.getVehicleCheckTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (resultVO.getDeliveryTime() != null) {
            exportExcel.setDeliveryTimeString(DateFormatUtils.format(resultVO.getDeliveryTime(), "yyyy-MM-dd HH:mm:ss"));
        }

        return exportExcel;
    }

    /**
     * 提交出厂登记
     *
     * @param submitDTO 提交信息
     * @return 提交结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submit(VehicleLeavingFactorySubmitDTO submitDTO) {
        // 获取当前用户信息
        String username = SessionUtils.getLoginUser().getUsername();
        Date currentDate = new Date();

        String taskNo = submitDTO.getTaskNo();
        if (StringUtils.isBlank(taskNo)) {
            throw new BusinessException("参数不能为空");
        }

        // 查询出厂登记信息
        Long leavingFactoryId = submitDTO.getLeavingFactoryId();
        MtcRepairTaskLeavingFactory leavingFactory = tableRepairTaskLeavingFactoryService.selectById(leavingFactoryId);
        if (leavingFactory == null) {
            throw new BusinessException("当前出厂登记不存在");
        }

        // 检查状态
        if (leavingFactory.getLeavingStatus().equals(LeavingStatusEnum.REGISTERED.getCode())) {
            throw new BusinessException("当前任务已完成出厂登记操作");
        }

        // 查询任务信息
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            throw new BusinessException("任务信息不存在");
        }

        // 验证出厂时间
        Date now = new Date();
        if (submitDTO.getDeliveryTime().compareTo(now) > 0
                || submitDTO.getDeliveryTime().compareTo(repairTask.getCreatedTime()) < 0) {
            throw new BusinessException("出厂时间有误，请确认");
        }

        // 验证备注长度
        if (StringUtils.isNotBlank(submitDTO.getRemark()) && submitDTO.getRemark().length() > 1000) {
            throw new BusinessException("出厂备注长度不能超过1000");
        }

        // 处理图片
        List<String> deliveryPictureList = new ArrayList<>();
        List<String> takeVoucherList = new ArrayList<>();
        if (submitDTO.getDeliveryPictures() != null) {
            deliveryPictureList = submitDTO.getDeliveryPictures();
            takeVoucherList.addAll(submitDTO.getDeliveryPictures());
        }

        if (submitDTO.getTakeVouchers() != null) {
            takeVoucherList.addAll(submitDTO.getTakeVouchers());
        }

        // 判断当前出厂登记是否为最后一条出厂登记记录
        boolean isLastLeavingFactory = judgeLastLeavingFactory(taskNo, leavingFactoryId);

        if (isLastLeavingFactory) {
            // 更新任务提车信息
            MtcRepairTask updateTaskInfo = new MtcRepairTask();
            updateTaskInfo.setId(repairTask.getId());
            updateTaskInfo.setTakeUserName(submitDTO.getName());
            updateTaskInfo.setTakeUserPhone(submitDTO.getPhoneNumber());
            updateTaskInfo.setSynTakeTime(submitDTO.getDeliveryTime());
            updateTaskInfo.setTakeVoucher(String.join(",", takeVoucherList));
            mtcRepairTaskMapper.updateByPrimaryKeySelective(updateTaskInfo);
        }

        // 更新出厂登记信息
        leavingFactory.setName(submitDTO.getName());
        leavingFactory.setPhoneNumber(submitDTO.getPhoneNumber());
        leavingFactory.setDeliveryTime(submitDTO.getDeliveryTime());
        leavingFactory.setDeliveryPictures(String.join(",", deliveryPictureList));
        leavingFactory.setRemark(submitDTO.getRemark());
        leavingFactory.setLeavingStatus(LeavingStatusEnum.REGISTERED.getCode());
        leavingFactory.setUpdateBy(username);
        leavingFactory.setUpdatedTime(currentDate);

        tableRepairTaskLeavingFactoryService.updateSelectiveById(leavingFactory);

        // 添加操作日志
        MtcOperatorLog operatorLog = new MtcOperatorLog();
        operatorLog.setTableName("mtc_repair_task");
        operatorLog.setRecordId(repairTask.getId());
        operatorLog.setOpeContent("出厂登记");
        operatorLog.setRemark("出厂登记");

        tableOperatorLogService.insertSelective(operatorLog);
    }

    /**
     * 判断当前出厂登记是否为最后一条出厂登记记录
     *
     * @param taskNo           任务编号
     * @param leavingFactoryId 出厂登记ID
     * @return 是否为最后一条记录
     */
    private boolean judgeLastLeavingFactory(String taskNo, Long leavingFactoryId) {
        // 根据任务编号查询所有的出厂登记信息
        List<VehicleLeavingFactoryResultVO> mtcRepairTaskLeavingFactories = tableRepairTaskLeavingFactoryService
                .selectByTaskNo(taskNo);
        if (CollectionUtils.isEmpty(mtcRepairTaskLeavingFactories)) {
            return true;
        }
        Long id = mtcRepairTaskLeavingFactories.get(0).getLeavingFactoryId();
        return id.equals(leavingFactoryId);
    }

    /**
     * 查询明细信息
     *
     * @param leavingId 出厂登记ID
     * @return 出厂登记详情
     */
    @Override
    public VehicleLeavingFactoryDetailsVO queryDetails(Long leavingId) {
        MtcRepairTaskLeavingFactory leavingFactory = tableRepairTaskLeavingFactoryService.selectById(leavingId);
        if (leavingFactory == null) {
            throw new BusinessException("出厂登记信息不存在");
        }
        VehicleLeavingFactoryDetailsVO detailsVO = new VehicleLeavingFactoryDetailsVO();
        detailsVO.setName(leavingFactory.getName());
        detailsVO.setPhoneNumber(leavingFactory.getPhoneNumber());
        detailsVO.setDeliveryTime(leavingFactory.getDeliveryTime());

        // 处理图片
        String deliveryPictures = leavingFactory.getDeliveryPictures();
        if (StringUtils.isNotBlank(deliveryPictures)) {
            detailsVO.setDeliveryPictures(Lists.newArrayList(deliveryPictures.split(",")));
        } else {
            detailsVO.setDeliveryPictures(new ArrayList<>());
        }

//         String takeVouchers = leavingFactory.getTakeVouchers();
//         if (StringUtils.isNotBlank(takeVouchers)) {
//         detailsVO.setTakeVouchers(Lists.newArrayList(takeVouchers.split(",")));
//         } else {
//         detailsVO.setTakeVouchers(new ArrayList<>());
//         }

        detailsVO.setRemark(leavingFactory.getRemark());
        detailsVO.setLeavingStatus(leavingFactory.getLeavingStatus());
        detailsVO.setLeavingStatusDesc(LeavingStatusEnum.getEnumDesc(leavingFactory.getLeavingStatus()));
        detailsVO.setLeavingFactoryId(leavingFactory.getId());
        detailsVO.setTaskNo(leavingFactory.getTaskNo());
        return detailsVO;
    }

    /**
     * 根据任务编号查询出厂登记列表
     *
     * @param taskNo 任务编号
     * @return 出厂登记列表
     */
    @Override
    public List<VehicleLeavingFactoryResultVO> queryDeliveryListByTaskNo(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            throw new BusinessException("任务编号不能为空");
        }

        // 查询出厂登记列表
        return tableRepairTaskLeavingFactoryService.selectByTaskNo(taskNo);
    }

    /**
     * 新增出厂登记
     *
     * @param taskNo             任务编号
     * @param vin                车架号
     * @param repairDepotId      修理厂ID
     * @param repairDepotName    修理厂名称
     * @param repairDepotOrgId   修理厂组织ID
     * @param repairDepotSapCode 修理厂SAP编码
     * @param taskInflowTime     任务流入时间
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertLeavingFactory(String taskNo, String vin, String repairDepotId, String repairDepotName,
                                     String repairDepotOrgId, String repairDepotSapCode, Date taskInflowTime) {
        log.info("新增出厂登记，任务编号：{}", taskNo);

        // 获取当前用户信息
        String username = SessionUtils.getLoginUser().getUsername();
        Date currentDate = new Date();

        // 查询任务信息
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            log.error("任务信息不存在，任务编号：{}", taskNo);
            throw new BusinessException("任务信息不存在");
        }

        // 查询流程实例
        WorkflowInstance workflow = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
        if (null == workflow) {
            throw new BusinessException("流程实例不存在");
        }

        // 查询是否已存在出厂登记
        List<VehicleLeavingFactoryResultVO> existingLeavingFactories = tableRepairTaskLeavingFactoryService
                .selectByTaskNo(taskNo);
        if (CollectionUtils.isNotEmpty(existingLeavingFactories)) {
            // 检查是否有未登记的记录
            boolean hasUnregistered = existingLeavingFactories.stream()
                    .anyMatch(factory -> LeavingStatusEnum.NOT_REGISTERED.getCode().equals(factory.getLeavingStatus()));

            if (hasUnregistered) {
                log.info("任务已存在未登记的出厂记录，任务编号：{}", taskNo);
                throw new BusinessException("任务已存在未登记的出厂记录");
            }
        }

        // 创建新的出厂登记记录
        MtcRepairTaskLeavingFactory leavingFactory = new MtcRepairTaskLeavingFactory();
        leavingFactory.setTaskNo(taskNo);
        leavingFactory.setVin(vin);
        leavingFactory.setRepairDepotId(repairDepotId);
        leavingFactory.setRepairDepotName(repairDepotName);
        leavingFactory.setRepairDepotOrgId(repairDepotOrgId);
        leavingFactory.setRepairDepotSapCode(repairDepotSapCode);
        leavingFactory.setRepairTaskInflowTime(taskInflowTime);
        leavingFactory.setLeavingStatus(LeavingStatusEnum.NOT_REGISTERED.getCode());
        leavingFactory.setCreateBy(username);
        leavingFactory.setCreatedTime(currentDate);
        leavingFactory.setUpdateBy(username);
        leavingFactory.setUpdatedTime(currentDate);

        tableRepairTaskLeavingFactoryService.insert(leavingFactory);

        // 添加操作日志
        MtcOperatorLog operatorLog = new MtcOperatorLog();
        operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
        operatorLog.setCurrentActivityCode(workflow.getCurrentActivityCode()); // 当前环节编码
        operatorLog.setRecordId(repairTask.getId());
        operatorLog.setOpeContent("新增出厂登记");
        operatorLog.setRemark("新增出厂登记");
        operatorLog.setCreateBy(username);
        operatorLog.setCreatedTime(currentDate);
        operatorLog.setUpdateBy(username);
        operatorLog.setUpdatedTime(currentDate);

        tableOperatorLogService.insertSelective(operatorLog);

        log.info("新增出厂登记成功，任务编号：{}", taskNo);
    }

    /**
     * 关闭出厂登记
     *
     * @param taskNo 任务编号
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void closeLeavingFactory(String taskNo) {
        log.info("关闭出厂登记，任务编号：{}", taskNo);

        // 获取当前用户信息
        String username = SessionUtils.getUsername();
        Date currentDate = new Date();

        // 查询任务信息
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            log.error("任务信息不存在，任务编号：{}", taskNo);
            throw new BusinessException("任务信息不存在");
        }

        // 查询流程实例
        WorkflowInstance workflow = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
        if (null == workflow) {
            throw new BusinessException("流程实例不存在");
        }

        // 查询出厂登记列表
        List<VehicleLeavingFactoryResultVO> leavingFactories = tableRepairTaskLeavingFactoryService
                .selectByTaskNo(taskNo);
        if (CollectionUtils.isEmpty(leavingFactories)) {
            log.info("任务没有出厂登记记录，任务编号：{}", taskNo);
            return;
        }

        // 更新所有未登记的出厂登记状态为已关闭
        for (VehicleLeavingFactoryResultVO leavingFactory : leavingFactories) {
            if (LeavingStatusEnum.NOT_REGISTERED.getCode().equals(leavingFactory.getLeavingStatus())) {
                MtcRepairTaskLeavingFactory updateLeavingFactory = new MtcRepairTaskLeavingFactory();
                updateLeavingFactory.setId(leavingFactory.getLeavingFactoryId());
                updateLeavingFactory.setLeavingStatus(LeavingStatusEnum.CLOSED.getCode());
                updateLeavingFactory.setUpdateBy(username);
                updateLeavingFactory.setUpdatedTime(currentDate);

                tableRepairTaskLeavingFactoryService.updateSelectiveById(updateLeavingFactory);
            }
        }

        // 添加操作日志
        MtcOperatorLog operatorLog = new MtcOperatorLog();
        operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
        operatorLog.setCurrentActivityCode(workflow.getCurrentActivityCode());
        operatorLog.setRecordId(repairTask.getId());
        operatorLog.setOpeContent("关闭出厂登记");
        operatorLog.setRemark("关闭出厂登记");
        tableOperatorLogService.insertSelective(operatorLog);

        log.info("关闭出厂登记成功，任务编号：{}", taskNo);
    }

    /**
     * 根据活动编码设置活动名称
     *
     * @param item                车辆出厂登记结果视图对象
     * @param activityDefinitions 活动定义列表
     */
    private void setActivityNameByCode(VehicleLeavingFactoryResultVO item, List<ActivityDefinition> activityDefinitions) {
        if (item == null || StringUtils.isEmpty(item.getCurrentActivityCode()) || CollectionUtils.isEmpty(activityDefinitions)) {
            // 如果参数为空，设置默认值
            if (item != null) {
                item.setCurrentActivityName("");
            }
            return;
        }

        try {
            // 根据活动编码查找对应的活动定义
            Optional<ActivityDefinition> definitionOpt = activityDefinitions.stream()
                    .filter(definition -> item.getCurrentActivityCode().equals(definition.getActivityCode()))
                    .findFirst();

            if (definitionOpt.isPresent()) {
                // 找到匹配的活动定义，设置活动名称
                item.setCurrentActivityName(definitionOpt.get().getActivityName());
            } else {
                // 未找到匹配的活动定义，设置默认值
                item.setCurrentActivityName("");
            }
        } catch (Exception e) {
            // 处理可能的异常，确保不影响整体导出流程
            log.warn("设置活动名称时发生异常，活动编码: {}, 异常信息: {}", item.getCurrentActivityCode(), e.getMessage());
            item.setCurrentActivityName("");
        }
    }

    /**
     * 根据状态编码设置状态名称
     *
     * @param item             车辆出厂登记结果视图对象
     * @param activityStatuses 活动状态列表
     */
    private void setStatusNameByCode(VehicleLeavingFactoryResultVO item, List<ActivityStatus> activityStatuses) {
        if (item == null || StringUtils.isEmpty(item.getStatusCode()) || CollectionUtils.isEmpty(activityStatuses)) {
            // 如果参数为空，设置默认值
            if (item != null) {
                item.setStatusName("");
            }
            return;
        }

        try {
            // 根据状态编码查找对应的状态定义
            Optional<ActivityStatus> statusOptional = activityStatuses.stream()
                    .filter(status -> item.getStatusCode().equals(status.getStatusCode()))
                    .findFirst();

            if (statusOptional.isPresent()) {
                // 找到匹配的状态定义，设置状态名称
                item.setStatusName(statusOptional.get().getStatusName());
            } else {
                // 未找到匹配的状态定义，设置默认值
                item.setStatusName("");
            }
        } catch (Exception e) {
            // 处理可能的异常，确保不影响整体导出流程
            log.warn("设置状态名称时发生异常，状态编码: {}, 异常信息: {}", item.getStatusCode(), e.getMessage());
            item.setStatusName("");
        }
    }
}
