package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.TenantDecryptionResultDTO;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysTenantSyncKey;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableTenantSyncKeyService;
import com.extracme.saas.autocare.service.EncryptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Date;
import java.util.List;

/**
 * 加密服务实现类
 */
@Slf4j
@Service
public class EncryptionServiceImpl implements EncryptionService {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final int KEY_LENGTH = 256;

    @Autowired
    private TableTenantSyncKeyService tableTenantSyncKeyService;

    @Autowired
    private TableTenantService tableTenantService;

    @Override
    public TenantDecryptionResultDTO decryptAndIdentifyTenant(String encryptedData) {
        if (!StringUtils.hasText(encryptedData)) {
            return TenantDecryptionResultDTO.failure("加密数据不能为空");
        }

        log.debug("开始解密数据并识别租户");

        try {
            // 获取所有有效的租户密钥
            List<SysTenantSyncKey> activeTenantKeys = tableTenantSyncKeyService.findAllActiveKeys();
            
            if (activeTenantKeys.isEmpty()) {
                log.warn("未找到任何有效的租户密钥");
                return TenantDecryptionResultDTO.failure("未找到有效的租户密钥");
            }

            // 尝试使用每个租户的密钥进行解密
            for (SysTenantSyncKey tenantKey : activeTenantKeys) {
                try {
                    String decryptedData = decryptData(encryptedData, tenantKey.getEncryptionKey());
                    
                    // 解密成功，返回结果
                    log.info("成功识别租户：{}", tenantKey.getTenantCode());
                    
                    // 更新密钥使用统计
                    updateKeyUsageStats(tenantKey.getTenantCode());
                    
                    return TenantDecryptionResultDTO.success(
                        tenantKey.getTenantId(),
                        tenantKey.getTenantCode(),
                        decryptedData
                    );
                    
                } catch (Exception e) {
                    // 当前密钥解密失败，尝试下一个
                    log.debug("租户 {} 的密钥解密失败，尝试下一个", tenantKey.getTenantCode());
                    continue;
                }
            }

            log.warn("所有租户密钥都无法解密数据");
            return TenantDecryptionResultDTO.failure("无法识别租户，解密失败");

        } catch (Exception e) {
            log.error("解密数据时发生异常", e);
            return TenantDecryptionResultDTO.failure("解密过程发生异常：" + e.getMessage());
        }
    }

    @Override
    public String encryptWithTenantKey(String data, String tenantCode) {
        if (!StringUtils.hasText(data) || !StringUtils.hasText(tenantCode)) {
            throw new BusinessException("数据和租户编码不能为空");
        }

        try {
            SysTenantSyncKey tenantKey = tableTenantSyncKeyService.findByTenantCode(tenantCode);
            if (tenantKey == null || tenantKey.getKeyStatus() != 1) {
                throw new BusinessException("租户密钥不存在或已禁用");
            }

            return encryptData(data, tenantKey.getEncryptionKey());

        } catch (Exception e) {
            log.error("使用租户密钥加密数据失败：{}", e.getMessage(), e);
            throw new BusinessException("加密失败：" + e.getMessage());
        }
    }

    @Override
    public String generateTenantKey(String tenantCode) {
        if (!StringUtils.hasText(tenantCode)) {
            throw new BusinessException("租户编码不能为空");
        }

        try {
            // 验证租户是否存在
            SysTenant tenant = tableTenantService.findByTenantCode(tenantCode)
                .orElseThrow(() -> new BusinessException("租户不存在"));

            // SysDataSyncLog
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(KEY_LENGTH, new SecureRandom());
            SecretKey secretKey = keyGenerator.generateKey();
            String encodedKey = Base64.getEncoder().encodeToString(secretKey.getEncoded());

            // 保存或更新租户密钥
            SysTenantSyncKey existingKey = tableTenantSyncKeyService.findByTenantCode(tenantCode);
            if (existingKey != null) {
                // 更新现有密钥
                existingKey.setEncryptionKey(encodedKey);
                existingKey.setKeyGenerateTime(new Date());
                existingKey.setKeyStatus(1);
                existingKey.setUsageCount(0L);
                tableTenantSyncKeyService.updateSelectiveById(existingKey);
            } else {
                // 创建新密钥记录
                SysTenantSyncKey newKey = new SysTenantSyncKey();
                newKey.setTenantId(tenant.getId());
                newKey.setTenantCode(tenantCode);
                newKey.setEncryptionKey(encodedKey);
                newKey.setKeyStatus(1);
                newKey.setKeyGenerateTime(new Date());
                newKey.setUsageCount(0L);
                tableTenantSyncKeyService.insert(newKey);
            }

            log.info("为租户 {} 生成新的同步密钥", tenantCode);
            return encodedKey;

        } catch (Exception e) {
            log.error("生成租户密钥失败：{}", e.getMessage(), e);
            throw new BusinessException("生成密钥失败：" + e.getMessage());
        }
    }

    @Override
    public boolean validateTenantKey(String tenantCode) {
        if (!StringUtils.hasText(tenantCode)) {
            return false;
        }

        try {
            SysTenantSyncKey tenantKey = tableTenantSyncKeyService.findByTenantCode(tenantCode);
            if (tenantKey == null) {
                return false;
            }

            // 检查密钥状态
            if (tenantKey.getKeyStatus() != 1) {
                return false;
            }

            // 检查密钥是否过期（如果设置了过期时间）
            if (tenantKey.getKeyExpireTime() != null && 
                tenantKey.getKeyExpireTime().before(new Date())) {
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("验证租户密钥失败：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void updateKeyUsageStats(String tenantCode) {
        if (!StringUtils.hasText(tenantCode)) {
            return;
        }

        try {
            SysTenantSyncKey tenantKey = tableTenantSyncKeyService.findByTenantCode(tenantCode);
            if (tenantKey != null) {
                tenantKey.setLastUsedTime(new Date());
                tenantKey.setUsageCount(
                    tenantKey.getUsageCount() != null ? tenantKey.getUsageCount() + 1 : 1L
                );
                tableTenantSyncKeyService.updateSelectiveById(tenantKey);
            }
        } catch (Exception e) {
            log.error("更新密钥使用统计失败：{}", e.getMessage(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 加密数据
     */
    private String encryptData(String data, String key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(
            Base64.getDecoder().decode(key), ALGORITHM);
        
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        
        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 解密数据
     */
    private String decryptData(String encryptedData, String key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(
            Base64.getDecoder().decode(key), ALGORITHM);
        
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }
}
