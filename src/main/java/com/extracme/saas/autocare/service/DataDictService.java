package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.DataDictAddDTO;
import com.extracme.saas.autocare.model.dto.DataDictUpdateDTO;
import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.extracme.saas.autocare.model.entity.DataDictInfo;
import com.extracme.saas.autocare.model.vo.DataDictDetailVO;
import com.extracme.saas.autocare.model.vo.DataDictSimpleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

/**
 * 数据字典服务接口
 */
public interface DataDictService {

    /**
     * 分页查询数据字典列表
     *
     * @param pageDTO 分页查询参数
     * @return 分页数据，只包含dataName、dataCode、codeType字段
     */
    BasePageVO<DataDictSimpleVO> pageList(BasePageDTO pageDTO);

    /**
     * 新增数据字典
     *
     * @param <T> 字典值的类型，可以是String、Integer等
     * @param addDTO 数据字典新增DTO
     * @return 新增成功的数据字典
     */
    <T> DataDictInfo add(DataDictAddDTO<T> addDTO);

    /**
     * 修改数据字典
     *
     * @param <T> 字典值的类型，可以是String、Integer等
     * @param updateDTO 数据字典更新DTO
     * @return 修改后的数据字典
     */
    <T> DataDictInfo update(DataDictUpdateDTO<T> updateDTO);

    /**
     * 根据ID删除数据字典
     *
     * @param id 数据字典ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询数据字典详情
     *
     * @param <T> 字典值的类型，可以是String、Integer等
     * @param id 数据字典ID
     * @return 数据字典详情，若不存在返回null
     */
    <T> DataDictDetailVO<T> getById(Long id);

    /**
     * 根据编码查询数据字典详情
     *
     * @param <T> 字典值的类型，可以是String、Integer等
     * @param dataCode 数据字典编码
     * @return 数据字典详情，若不存在返回null
     */
    <T> DataDictDetailVO<T> getByCode(String dataCode);
}