package com.extracme.saas.autocare.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableVehicleModelService;
import com.extracme.saas.autocare.service.VehicleModelService;

import lombok.extern.slf4j.Slf4j;

/**
 * 车型服务实现类
 */
@Slf4j
@Service
public class VehicleModelServiceImpl implements VehicleModelService {

    @Autowired
    private TableVehicleModelService tableVehicleModelService;

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<Long>> getVehicleModelCombo(String vehicleModelName) {
        try {
            // 最多返回20条数据
            return tableVehicleModelService.getVehicleModelCombo(vehicleModelName, 20);
        } catch (Exception e) {
            log.error("获取车型下拉列表数据失败", e);
            throw new RuntimeException("获取车型下拉列表数据失败", e);
        }
    }
}
