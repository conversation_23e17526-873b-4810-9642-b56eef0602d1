package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableRegionService;
import com.extracme.saas.autocare.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 地区数据服务实现类
 * 
 * <AUTHOR>
 * @date 2024/06/03
 */
@Slf4j
@Service
public class RegionServiceImpl implements RegionService {

    @Autowired
    private TableRegionService tableRegionService;

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<Long>> getProvinceCombo() {
        try {
            log.debug("开始获取省份下拉框数据");
            List<ComboVO<Long>> result = tableRegionService.getProvinceCombo();
            log.debug("获取省份下拉框数据成功，共{}条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取省份下拉框数据失败", e);
            throw new BusinessException("获取省份数据失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<Long>> getCityCombo(Long provinceId) {
        if (provinceId == null) {
            throw new BusinessException("省份ID不能为空");
        }

        try {
            log.debug("开始获取城市下拉框数据，provinceId: {}", provinceId);
            List<ComboVO<Long>> result = tableRegionService.getCityCombo(provinceId);
            log.debug("获取城市下拉框数据成功，provinceId: {}，共{}条记录", provinceId, result.size());
            return result;
        } catch (Exception e) {
            log.error("获取城市下拉框数据失败，provinceId: {}", provinceId, e);
            throw new BusinessException("获取城市数据失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<Long>> getAreaCombo(Long cityId) {
        if (cityId == null) {
            throw new BusinessException("城市ID不能为空");
        }

        try {
            log.debug("开始获取区域下拉框数据，cityId: {}", cityId);
            List<ComboVO<Long>> result = tableRegionService.getAreaCombo(cityId);
            log.debug("获取区域下拉框数据成功，cityId: {}，共{}条记录", cityId, result.size());
            return result;
        } catch (Exception e) {
            log.error("获取区域下拉框数据失败，cityId: {}", cityId, e);
            throw new BusinessException("获取区域数据失败");
        }
    }
}
