package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.TenantDecryptionResultDTO;

/**
 * 加密服务接口
 */
public interface EncryptionService {

    /**
     * 解密数据并识别租户
     * 
     * @param encryptedData 加密的数据
     * @return 解密结果，包含租户信息和原始数据
     */
    TenantDecryptionResultDTO decryptAndIdentifyTenant(String encryptedData);

    /**
     * 使用指定租户密钥加密数据
     * 
     * @param data 原始数据
     * @param tenantCode 租户编码
     * @return 加密后的数据
     */
    String encryptWithTenantKey(String data, String tenantCode);

    /**
     * 为租户生成新的加密密钥
     * 
     * @param tenantCode 租户编码
     * @return 生成的密钥
     */
    String generateTenantKey(String tenantCode);

    /**
     * 验证租户密钥是否有效
     * 
     * @param tenantCode 租户编码
     * @return 是否有效
     */
    boolean validateTenantKey(String tenantCode);

    /**
     * 更新租户密钥使用统计
     * 
     * @param tenantCode 租户编码
     */
    void updateKeyUsageStats(String tenantCode);
}
