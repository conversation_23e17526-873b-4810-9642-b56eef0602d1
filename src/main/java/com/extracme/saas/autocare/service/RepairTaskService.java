package com.extracme.saas.autocare.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.AccidentDamageDTO;
import com.extracme.saas.autocare.model.dto.FileDTO;
import com.extracme.saas.autocare.model.dto.RepairAmountDTO;
import com.extracme.saas.autocare.model.dto.RepairCustInfoDTO;
import com.extracme.saas.autocare.model.dto.RepairRemarkDTO;
import com.extracme.saas.autocare.model.dto.VehicleRepairRecordQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.GetFirstPageInfoDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskCreateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskProcessQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.vo.MtcTaskListResultVO;
import com.extracme.saas.autocare.model.vo.RepairDepotFirstPageInfoVO;
import com.extracme.saas.autocare.model.vo.RepairDepotInRepairingVO;
import com.extracme.saas.autocare.model.vo.RepairTaskCurrentListVO;
import com.extracme.saas.autocare.model.vo.RepairTaskDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO;
import com.extracme.saas.autocare.model.vo.VehicleRepairRecordVO;
import com.extracme.saas.autocare.model.vo.VehicleRepairStatisticsVO;
import com.extracme.saas.autocare.model.vo.ViewRepairRemarkVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

/**
 * 维修任务服务接口
 */
public interface RepairTaskService {

    /**
     * 创建维修任务
     *
     * @param createDTO 创建参数
     * @return 维修任务ID
     */
    Long createRepairTask(RepairTaskCreateDTO createDTO);

    /**
     * 首页看板三个数据统计
     *
     * @param getFirstPageInfoDTO
     * @return
     */
    VehicleRepairStatisticsVO getVehicleRepairStatistics(GetFirstPageInfoDTO getFirstPageInfoDTO);

    /**
     * 获取维修厂首页统计信息
     * 
     * @param getFirstPageInfoDTO
     * @return 首页统计信息
     */
    BasePageVO<RepairDepotFirstPageInfoVO> getFirstPageInfo(GetFirstPageInfoDTO getFirstPageInfoDTO);

    /**
     * 导出维修厂首页统计信息
     * 
     * @param getFirstPageInfoDTO 查询参数
     * @param response HTTP响应对象
     * @throws BusinessException 业务异常
     */
    void exportFirstPageInfo(GetFirstPageInfoDTO getFirstPageInfoDTO, HttpServletResponse response);

    /**
     * 获取维修厂首页统计信息
     * 
     * @param getFirstPageInfoDTO
     * @return 首页统计信息
     */
    BasePageVO<RepairDepotInRepairingVO> getRepairDepotInRepairingInfo(GetFirstPageInfoDTO getFirstPageInfoDTO);

    /**
     * 流程查询-查询维修任务流程列表
     *
     * @param queryDTO 维修任务流程查询DTO
     * @return 维修任务列表
     */
    BasePageVO<RepairTaskProcessListVO> queryRepairTaskProcessList(RepairTaskProcessQueryDTO queryDTO);

    /**
     * 流程查询-导出
     *
     * @param queryDTO 维修任务流程查询DTO
     * @param request
     * @param response
     * @return 维修任务列表
     */
    void exportRepairTaskProcessList(RepairTaskProcessQueryDTO queryDTO, HttpServletRequest request, HttpServletResponse response);

    /**
     * 各环节-所有维修任务列表
     *
     * @param queryDTO 维修任务流程查询DTO
     * @return 维修任务列表
     */
    MtcTaskListResultVO queryRepairTaskList(RepairTaskListQueryDTO queryDTO);

    /**
     * 各环节-查询维修任务列表
     *
     * @param queryDTO 维修任务流程查询DTO
     * @return 维修任务列表
     */
    Map<String, Long> countActivityCode(RepairTaskListQueryDTO queryDTO);

    /**
     * 各环节-当前环节任务列表
     *
     * @param queryDTO 维修任务流程查询DTO
     * @return 维修任务列表
     */
    BasePageVO<RepairTaskProcessListVO> queryCurrentActivityCodeRepairTaskList(RepairTaskListQueryDTO queryDTO);

    /**
     * 获取维修任务详情
     *
     * @param id 维修任务ID
     * @return 维修任务详情
     */
    RepairTaskDetailsVO getRepairTaskDetails(Long id);

    /**
     * 维修任务保存
     *
     * @param id                  任务id
     * @param repairTaskUpdateDTO 任务修改信息bo
     */
    void saveRepairTask(Long id, RepairTaskUpdateDTO repairTaskUpdateDTO);

    /**
     * 处理维修任务相关的图片和视频文件
     * 公共方法，供多个服务共用图片处理逻辑
     *
     * @param taskNo 任务编号
     * @param mediaTypeMap 图片/视频类型映射，键为图片类型，值为图片URL列表
     * @param operatorName 操作人用户名
     * @throws BusinessException 处理过程中的业务异常
     */
    void processMediaFiles(String taskNo, Map<Integer, List<FileDTO>> mediaTypeMap, String operatorName);

    /**
     * 待分配-选择修理厂并更新维修任务信息
     *
     * @param taskId 维修任务ID
     * @param repairDepotId 修理厂ID
     * @return 是否更新成功
     * @throws BusinessException 业务异常
     */
    void selectRepairDepot(Long taskId, String repairDepotId);

    /**
	 * 查看备注
	 * @param taskNo
	 * @return 维修备注列表
	 */
	List<ViewRepairRemarkVO> viewRepairRemark(String taskNo);

    /**
     * 添加维修备注
     *
     * @param repairRemarkDTO 维修备注信息
     * @throws BusinessException 业务异常
     */
    void addRepairRemark(RepairRemarkDTO repairRemarkDTO);

    /**
     * 删除维修备注
     *
     * @param id 备注ID
     * @param repairStage 维修阶段
     * @throws BusinessException 业务异常
     */
    void deleteRepairRemark(Long id, String repairStage);

    /**
     * 查询指定车辆当前正在进行中的维修任务列表
     *
     * @param vin 车架号
     * @return 正在进行中的维修任务列表
     * @throws BusinessException 业务异常
     */
    List<RepairTaskCurrentListVO> queryCurrentRepairTasksByVin(String vin);

    /**
     * 查询车辆历次维修记录
     *
     * @param queryDTO 查询条件DTO
     * @return 分页的车辆维修记录
     */
    BasePageVO<VehicleRepairRecordVO> queryVehicleRepairRecord(VehicleRepairRecordQueryDTO queryDTO);

    /**
     * 调整维修任务客户直付金额
     *
     * @param repairCustInfoDTO 调整用户承担金额dto
     */
    void adjustCustAmount(RepairCustInfoDTO repairCustInfoDTO);

    /**
     * 更新自费金额
     *
     * @param taskNo 维修任务编号
     * @param accidentDamageDTO 维修任务事故损失金额
     */
    void updateSelfFundedAmount(String taskNo, AccidentDamageDTO accidentDamageDTO);

    /**
     * 获取自费金额
     *
     * @param repairAmountDTO
     * @param accidentDamageDTO 维修任务编号
     */
    BigDecimal getSelfFundedAmount(RepairAmountDTO repairAmountDTO, AccidentDamageDTO accidentDamageDTO);
}
