package com.extracme.saas.autocare.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;
import com.extracme.saas.autocare.model.entity.MtcVehicleModel;
import com.extracme.saas.autocare.model.vo.VehicleInfoVO;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.repository.TableVehicleInfoService;
import com.extracme.saas.autocare.repository.TableVehicleModelService;
import com.extracme.saas.autocare.service.VehicleInfoService;

import lombok.extern.slf4j.Slf4j;

/**
 * 车辆信息服务实现类
 */
@Slf4j
@Service
public class VehicleInfoServiceImpl implements VehicleInfoService {

    @Autowired
    private TableVehicleInfoService tableVehicleInfoService;

    @Autowired
    private TableVehicleModelService tableVehicleModelService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Override
    @Transactional(readOnly = true)
    public VehicleInfoVO getVehicleInfoByVin(String vin) {
        try {
            VehicleInfoVO vehicleInfoVO = new VehicleInfoVO();
            MtcVehicleInfo vehicleInfo = tableVehicleInfoService.findByVin(vin);
            BeanUtils.copyProperties(vehicleInfo, vehicleInfoVO);

            // 根据车型ID获取车型信息
            MtcVehicleModel vehicleModel = tableVehicleModelService.selectById(vehicleInfo.getVehicleModelId());
            if (vehicleModel != null) {
                vehicleInfoVO.setVehicleModelName(vehicleModel.getVehicleModelName());
            }

            // 根据组织机构ID获取组织机构信息
            MtcOrgInfo orgInfo = tableOrgInfoService.selectByOrgId(vehicleInfo.getVehicleOrgId());
            if (orgInfo != null) {
                vehicleInfoVO.setVehicleOrgName(orgInfo.getOrgName());
            }

            // 根据组织机构ID获取组织机构信息
            MtcOrgInfo operateOrgInfo = tableOrgInfoService.selectByOrgId(vehicleInfo.getOperationOrgId());
            if (operateOrgInfo != null) {
                vehicleInfoVO.setOperationOrgName(operateOrgInfo.getOrgName());
            }

            return vehicleInfoVO;
        } catch (Exception e) {
            log.error("根据车架号查询车辆详情失败", e);
            throw new RuntimeException("查询车辆详情失败", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<VehicleInfoVO> getVehicleInfoList(String vin, String vehicleNo) {
        try {
            // 限制最多返回20条数据
            List<MtcVehicleInfo> mtcVehicleInfoList = tableVehicleInfoService.findByCondition(vin, vehicleNo, 20);


            List<VehicleInfoVO> vehicleInfoVOList = new ArrayList<>();
            mtcVehicleInfoList.forEach(mtcVehicleInfo -> {
                VehicleInfoVO vehicleInfoVO = new VehicleInfoVO();
                BeanUtils.copyProperties(mtcVehicleInfo, vehicleInfoVO);
                
                // 根据组织机构ID获取组织机构信息
                MtcOrgInfo orgInfo = tableOrgInfoService.selectByOrgId(mtcVehicleInfo.getVehicleOrgId());
                if (orgInfo != null) {
                    vehicleInfoVO.setVehicleOrgName(orgInfo.getOrgName());
                }

                // 根据组织机构ID获取组织机构信息
                MtcOrgInfo operateOrgInfo = tableOrgInfoService.selectByOrgId(mtcVehicleInfo.getOperationOrgId());
                if (operateOrgInfo != null) {
                    vehicleInfoVO.setOperationOrgName(operateOrgInfo.getOrgName());
                }

                // 根据车型ID获取车型信息
                MtcVehicleModel vehicleModel = tableVehicleModelService.selectById(mtcVehicleInfo.getVehicleModelId());
                if (vehicleModel != null) {
                    vehicleInfoVO.setVehicleModelName(vehicleModel.getVehicleModelName());
                }

                vehicleInfoVOList.add(vehicleInfoVO);
            });

            return vehicleInfoVOList;
        } catch (Exception e) {
            log.error("根据车架号或车牌号查询车辆列表失败", e);
            throw new RuntimeException("查询车辆列表失败", e);
        }
    }
}
