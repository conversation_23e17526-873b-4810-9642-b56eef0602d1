package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.BusinessOperateLogQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.vo.BusinessOperateLogVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

import java.util.List;

public interface OperatorLogService {

    /**
     * 查看操作日志
     *
     */
    BasePageVO<BusinessOperateLogVO> queryOperatorLog(BusinessOperateLogQueryDTO queryDTO);

    /**
     * 记录维修流程操作日志
     *
     * @param tableName           关联表名
     * @param recordId            关联记录ID
     * @param content             操作内容
     * @param currentActivityCode 当前环节编码
     * @param operator            操作人
     */
    void recordOperationLog(String tableName, Long recordId, String content, String currentActivityCode, String operator);

    /**
     * 根据记录ID查询操作日志列表
     */
    List<MtcOperatorLog> queryLogsByRecordId(Long recordId);

    /**
     * 根据记录ID和环节查询操作日志列表
     */
    List<MtcOperatorLog> queryLogsByRecordIdAndTache(Long recordId, String currentTache);
}