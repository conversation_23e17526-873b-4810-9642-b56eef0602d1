package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.VehicleInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.VehicleModelSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.OrgInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.vo.DataSyncResultVO;

/**
 * 数据同步服务接口
 */
public interface DataSyncService {



    /**
     * 查询同步状态
     * 
     * @param batchNo 批次号
     * @return 同步结果
     */
    DataSyncResultVO querySyncStatus(String batchNo);



    /**
     * 批量同步车辆信息数据
     *
     * @param requestDTO 车辆信息同步请求
     * @param sourceIp 请求来源IP
     * @return 同步结果
     */
    DataSyncResultVO syncVehicleInfoBatch(VehicleInfoSyncRequestDTO requestDTO, String sourceIp);

    /**
     * 批量同步车型信息数据
     *
     * @param requestDTO 车型信息同步请求
     * @param sourceIp 请求来源IP
     * @return 同步结果
     */
    DataSyncResultVO syncVehicleModelBatch(VehicleModelSyncRequestDTO requestDTO, String sourceIp);

    /**
     * 批量同步机构信息数据
     *
     * @param requestDTO 机构信息同步请求
     * @param sourceIp 请求来源IP
     * @return 同步结果
     */
    DataSyncResultVO syncOrgInfoBatch(OrgInfoSyncRequestDTO requestDTO, String sourceIp);
}
