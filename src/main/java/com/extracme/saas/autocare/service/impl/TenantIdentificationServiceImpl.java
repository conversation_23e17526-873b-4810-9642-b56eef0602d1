package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.TenantIdentificationResultDTO;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysTenantSyncKey;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableTenantSyncKeyService;
import com.extracme.saas.autocare.service.TenantIdentificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.UUID;

/**
 * 租户识别服务实现类
 */
@Slf4j
@Service
public class TenantIdentificationServiceImpl implements TenantIdentificationService {

    @Autowired
    private TableTenantSyncKeyService tableTenantSyncKeyService;

    @Autowired
    private TableTenantService tableTenantService;

    @Override
    public TenantIdentificationResultDTO identifyTenantBySyncKey(String syncKey) {
        if (!StringUtils.hasText(syncKey)) {
            return TenantIdentificationResultDTO.failure("同步密钥不能为空");
        }

        log.debug("开始根据同步密钥识别租户：{}", syncKey);

        try {
            // 根据同步密钥查询租户信息
            SysTenantSyncKey tenantSyncKey = tableTenantSyncKeyService.findBySyncKey(syncKey);
            
            if (tenantSyncKey == null) {
                log.warn("未找到对应的租户同步密钥：{}", syncKey);
                return TenantIdentificationResultDTO.failure("无效的同步密钥");
            }

            // 检查密钥状态
            if (tenantSyncKey.getKeyStatus() != 1) {
                log.warn("租户同步密钥已禁用：{}", syncKey);
                return TenantIdentificationResultDTO.failure("同步密钥已禁用");
            }

            // 检查密钥是否过期
            if (tenantSyncKey.getKeyExpireTime() != null && 
                tenantSyncKey.getKeyExpireTime().before(new Date())) {
                log.warn("租户同步密钥已过期：{}", syncKey);
                return TenantIdentificationResultDTO.failure("同步密钥已过期");
            }

            log.info("成功识别租户：{}", tenantSyncKey.getTenantCode());
            
            // 更新密钥使用统计
            updateSyncKeyUsageStats(syncKey);
            
            return TenantIdentificationResultDTO.success(
                tenantSyncKey.getTenantId(),
                tenantSyncKey.getTenantCode()
            );

        } catch (Exception e) {
            log.error("根据同步密钥识别租户失败：{}", e.getMessage(), e);
            return TenantIdentificationResultDTO.failure("识别租户失败：" + e.getMessage());
        }
    }

    @Override
    public String generateSyncKey(String tenantCode) {
        if (!StringUtils.hasText(tenantCode)) {
            throw new BusinessException("租户编码不能为空");
        }

        try {
            // 验证租户是否存在
            SysTenant tenant = tableTenantService.findByTenantCode(tenantCode)
                .orElseThrow(() -> new BusinessException("租户不存在"));

            // 生成新的同步密钥
            String newSyncKey = generateUniqueKey(tenantCode);

            // 保存或更新租户同步密钥
            SysTenantSyncKey existingKey = tableTenantSyncKeyService.findByTenantCode(tenantCode);
            if (existingKey != null) {
                // 更新现有密钥
                existingKey.setSyncKey(newSyncKey);
                existingKey.setKeyGenerateTime(new Date());
                existingKey.setKeyStatus(1);
                existingKey.setUsageCount(0L);
                tableTenantSyncKeyService.updateSelectiveById(existingKey);
            } else {
                // 创建新密钥记录
                SysTenantSyncKey newKey = new SysTenantSyncKey();
                newKey.setTenantId(tenant.getId());
                newKey.setTenantCode(tenantCode);
                newKey.setSyncKey(newSyncKey);
                newKey.setKeyStatus(1);
                newKey.setKeyGenerateTime(new Date());
                newKey.setUsageCount(0L);
                tableTenantSyncKeyService.insert(newKey);
            }

            log.info("为租户 {} 生成新的同步密钥", tenantCode);
            return newSyncKey;

        } catch (Exception e) {
            log.error("生成租户同步密钥失败：{}", e.getMessage(), e);
            throw new BusinessException("生成同步密钥失败：" + e.getMessage());
        }
    }

    @Override
    public boolean validateSyncKey(String syncKey) {
        if (!StringUtils.hasText(syncKey)) {
            return false;
        }

        try {
            SysTenantSyncKey tenantSyncKey = tableTenantSyncKeyService.findBySyncKey(syncKey);
            if (tenantSyncKey == null) {
                return false;
            }

            // 检查密钥状态
            if (tenantSyncKey.getKeyStatus() != 1) {
                return false;
            }

            // 检查密钥是否过期（如果设置了过期时间）
            if (tenantSyncKey.getKeyExpireTime() != null && 
                tenantSyncKey.getKeyExpireTime().before(new Date())) {
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("验证同步密钥失败：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void updateSyncKeyUsageStats(String syncKey) {
        if (!StringUtils.hasText(syncKey)) {
            return;
        }

        try {
            SysTenantSyncKey tenantSyncKey = tableTenantSyncKeyService.findBySyncKey(syncKey);
            if (tenantSyncKey != null) {
                tenantSyncKey.setLastUsedTime(new Date());
                tenantSyncKey.setUsageCount(tenantSyncKey.getUsageCount() + 1);
                tableTenantSyncKeyService.updateSelectiveById(tenantSyncKey);
            }
        } catch (Exception e) {
            log.error("更新同步密钥使用统计失败：{}", e.getMessage(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 生成唯一的同步密钥
     */
    private String generateUniqueKey(String tenantCode) {
        // 生成格式：SYNC_KEY_{租户编码}_{时间戳}_{UUID前8位}
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("SYNC_KEY_%s_%s_%s", tenantCode, timestamp, uuid);
    }
}
