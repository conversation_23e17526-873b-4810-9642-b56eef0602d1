package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.dto.VehicleInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.VehicleModelSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.OrgInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.SyncDataResultDTO;
import com.extracme.saas.autocare.model.dto.TenantIdentificationResultDTO;
import com.extracme.saas.autocare.model.entity.SysDataSyncLog;
import com.extracme.saas.autocare.model.vo.DataSyncResultVO;
import com.extracme.saas.autocare.repository.TableDataSyncLogService;
import com.extracme.saas.autocare.service.DataSyncService;
import com.extracme.saas.autocare.service.TenantIdentificationService;
import com.extracme.saas.autocare.service.sync.SyncStrategy;
import com.extracme.saas.autocare.util.OperatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 数据同步服务实现类
 */
@Slf4j
@Service
public class DataSyncServiceImpl implements DataSyncService {

    @Autowired
    private TenantIdentificationService tenantIdentificationService;

    @Autowired
    private TableDataSyncLogService tableDataSyncLogService;

    @Autowired
    private List<SyncStrategy> syncStrategies;

    @Autowired
    @Qualifier("taskExecutor")
    private Executor taskExecutor;

    // 策略映射缓存
    private Map<String, SyncStrategy> strategyMap;

    // 并行处理配置
    private static final int BATCH_SIZE = 50; // 每个线程处理的数据量
    private static final int MIN_PARALLEL_SIZE = 100; // 启用并行处理的最小数据量

    /**
     * 初始化策略映射
     */
    private void initStrategyMap() {
        if (strategyMap == null) {
            strategyMap = syncStrategies.stream()
                .collect(Collectors.toMap(
                    SyncStrategy::getSupportedTable,
                    strategy -> strategy,
                    (existing, replacement) -> existing
                ));
            log.info("初始化同步策略映射，支持的表：{}", strategyMap.keySet());
        }
    }



    @Override
    public DataSyncResultVO querySyncStatus(String batchNo) {
        if (!StringUtils.hasText(batchNo)) {
            throw new BusinessException("批次号不能为空");
        }

        try {
            List<SysDataSyncLog> syncLogs = tableDataSyncLogService.findByBatchNo(batchNo);
            if (CollectionUtils.isEmpty(syncLogs)) {
                throw new BusinessException("未找到批次号为 " + batchNo + " 的同步记录");
            }

            // 统计同步结果
            SysDataSyncLog firstLog = syncLogs.get(0);
            int totalCount = syncLogs.size();
            int successCount = (int) syncLogs.stream()
                .filter(log -> "SUCCESS".equals(log.getSyncStatus()))
                .count();
            int failedCount = totalCount - successCount;

            // 构建失败详情 - 移除对已删除字段的引用
            List<DataSyncResultVO.SyncFailureDetailVO> failureDetails = syncLogs.stream()
                .filter(log -> !"SUCCESS".equals(log.getSyncStatus()))
                .map(log -> {
                    DataSyncResultVO.SyncFailureDetailVO detail = new DataSyncResultVO.SyncFailureDetailVO();
                    detail.setSourceDataId("batch_" + log.getId()); // 使用日志ID作为标识
                    detail.setErrorMessage(log.getErrorMessage());
                    detail.setFailureTime(log.getSyncEndTime());
                    return detail;
                })
                .collect(Collectors.toList());

            // 构建结果
            DataSyncResultVO result = new DataSyncResultVO();
            result.setBatchNo(batchNo);
            result.setTenantCode(firstLog.getTenantCode());
            result.setTargetTable(firstLog.getTargetTable());
            result.setOperationType("AUTO"); // 固定为自动判断类型
            result.setSyncStatus(failedCount == 0 ? "SUCCESS" :
                               (successCount > 0 ? "PARTIAL_SUCCESS" : "FAILED"));
            result.setTotalCount(totalCount);
            result.setSuccessCount(successCount);
            result.setFailedCount(failedCount);
            result.setSyncStartTime(firstLog.getSyncStartTime());
            result.setSyncEndTime(firstLog.getSyncEndTime());
            result.setSyncDuration(firstLog.getSyncDuration());
            result.setFailureDetails(failureDetails);

            return result;

        } catch (Exception e) {
            log.error("查询同步状态失败：{}", e.getMessage(), e);
            throw new BusinessException("查询同步状态失败：" + e.getMessage());
        }
    }



    /**
     * 获取同步策略
     */
    private SyncStrategy getSyncStrategy(String tableName) {
        initStrategyMap();
        return strategyMap.get(tableName);
    }

    /**
     * 生成批次号
     */
    private String generateBatchNo() {
        return "SYNC_" + System.currentTimeMillis() + "_" + 
               String.format("%04d", new Random().nextInt(10000));
    }







    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataSyncResultVO syncVehicleInfoBatch(VehicleInfoSyncRequestDTO requestDTO, String sourceIp) {
        log.info("开始批量同步车辆信息数据，数据量：{}，来源IP：{}",
                requestDTO.getBatchData() != null ? requestDTO.getBatchData().size() : 0, sourceIp);

        // 参数校验
        validateVehicleInfoRequest(requestDTO);

        // 根据同步密钥识别租户
        TenantIdentificationResultDTO tenantResult =
            tenantIdentificationService.identifyTenantBySyncKey(requestDTO.getSyncKey());

        if (!tenantResult.isSuccess()) {
            throw new BusinessException("租户识别失败：" + tenantResult.getErrorMessage());
        }

        // 调用批量同步处理
        DataSyncResultVO result = syncBatchDataWithTenant(
            "mtc_vehicle_info", requestDTO.getBatchData(),
            tenantResult, sourceIp);

        log.info("车辆信息批量同步完成，批次号：{}，状态：{}，总数：{}，成功：{}，失败：{}",
                result.getBatchNo(), result.getSyncStatus(), result.getTotalCount(),
                result.getSuccessCount(), result.getFailedCount());

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataSyncResultVO syncVehicleModelBatch(VehicleModelSyncRequestDTO requestDTO, String sourceIp) {
        log.info("开始批量同步车型信息数据，数据量：{}，来源IP：{}",
                requestDTO.getBatchData() != null ? requestDTO.getBatchData().size() : 0, sourceIp);

        // 参数校验
        validateVehicleModelRequest(requestDTO);

        // 根据同步密钥识别租户
        TenantIdentificationResultDTO tenantResult =
            tenantIdentificationService.identifyTenantBySyncKey(requestDTO.getSyncKey());

        if (!tenantResult.isSuccess()) {
            throw new BusinessException("租户识别失败：" + tenantResult.getErrorMessage());
        }

        // 调用批量同步处理
        DataSyncResultVO result = syncBatchDataWithTenant(
            "mtc_vehicle_model", requestDTO.getBatchData(),
            tenantResult, sourceIp);

        log.info("车型信息批量同步完成，批次号：{}，状态：{}，总数：{}，成功：{}，失败：{}",
                result.getBatchNo(), result.getSyncStatus(), result.getTotalCount(),
                result.getSuccessCount(), result.getFailedCount());

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataSyncResultVO syncOrgInfoBatch(OrgInfoSyncRequestDTO requestDTO, String sourceIp) {
        log.info("开始批量同步机构信息数据，数据量：{}，来源IP：{}",
                requestDTO.getBatchData() != null ? requestDTO.getBatchData().size() : 0, sourceIp);

        // 参数校验
        validateOrgInfoRequest(requestDTO);

        // 根据同步密钥识别租户
        TenantIdentificationResultDTO tenantResult =
            tenantIdentificationService.identifyTenantBySyncKey(requestDTO.getSyncKey());

        if (!tenantResult.isSuccess()) {
            throw new BusinessException("租户识别失败：" + tenantResult.getErrorMessage());
        }

        // 调用批量同步处理
        DataSyncResultVO result = syncBatchDataWithTenant(
            "mtc_org_info", requestDTO.getBatchData(),
            tenantResult, sourceIp);

        log.info("机构信息批量同步完成，批次号：{}，状态：{}，总数：{}，成功：{}，失败：{}",
                result.getBatchNo(), result.getSyncStatus(), result.getTotalCount(),
                result.getSuccessCount(), result.getFailedCount());

        return result;
    }

    /**
     * 校验车辆信息同步请求参数
     */
    private void validateVehicleInfoRequest(VehicleInfoSyncRequestDTO requestDTO) {
        if (requestDTO == null) {
            throw new BusinessException("同步请求不能为空");
        }

        if (CollectionUtils.isEmpty(requestDTO.getBatchData())) {
            throw new BusinessException("车辆信息同步数据不能为空");
        }

        // 校验每条数据
        for (int i = 0; i < requestDTO.getBatchData().size(); i++) {
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO dataItem = requestDTO.getBatchData().get(i);
            if (dataItem == null) {
                throw new BusinessException("第" + (i + 1) + "条车辆信息数据不能为空");
            }
            if (!StringUtils.hasText(dataItem.getVin())) {
                throw new BusinessException("第" + (i + 1) + "条车辆信息数据的车架号不能为空");
            }
            if (!StringUtils.hasText(dataItem.getVehicleNo())) {
                throw new BusinessException("第" + (i + 1) + "条车辆信息数据的车牌号不能为空");
            }
        }

        log.info("车辆信息同步请求参数校验通过，数据量：{}", requestDTO.getBatchData().size());
    }

    /**
     * 校验车型信息同步请求参数
     */
    private void validateVehicleModelRequest(VehicleModelSyncRequestDTO requestDTO) {
        if (requestDTO == null) {
            throw new BusinessException("同步请求不能为空");
        }

        if (CollectionUtils.isEmpty(requestDTO.getBatchData())) {
            throw new BusinessException("车型信息同步数据不能为空");
        }

        // 校验每条数据
        for (int i = 0; i < requestDTO.getBatchData().size(); i++) {
            VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO dataItem = requestDTO.getBatchData().get(i);
            if (dataItem == null) {
                throw new BusinessException("第" + (i + 1) + "条车型信息数据不能为空");
            }
            if (!StringUtils.hasText(dataItem.getVehicleModelName())) {
                throw new BusinessException("第" + (i + 1) + "条车型信息数据的车型名称不能为空");
            }
        }

        log.info("车型信息同步请求参数校验通过，数据量：{}", requestDTO.getBatchData().size());
    }

    /**
     * 校验机构信息同步请求参数
     */
    private void validateOrgInfoRequest(OrgInfoSyncRequestDTO requestDTO) {
        if (requestDTO == null) {
            throw new BusinessException("同步请求不能为空");
        }

        if (CollectionUtils.isEmpty(requestDTO.getBatchData())) {
            throw new BusinessException("机构信息同步数据不能为空");
        }

        // 校验每条数据
        for (int i = 0; i < requestDTO.getBatchData().size(); i++) {
            OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO dataItem = requestDTO.getBatchData().get(i);
            if (dataItem == null) {
                throw new BusinessException("第" + (i + 1) + "条机构信息数据不能为空");
            }
            if (!StringUtils.hasText(dataItem.getOrgId())) {
                throw new BusinessException("第" + (i + 1) + "条机构信息数据的机构ID不能为空");
            }
            if (!StringUtils.hasText(dataItem.getOrgName())) {
                throw new BusinessException("第" + (i + 1) + "条机构信息数据的机构名称不能为空");
            }
        }

        log.info("机构信息同步请求参数校验通过，数据量：{}", requestDTO.getBatchData().size());
    }



    /**
     * 通用批量同步方法（基于租户识别）- 支持并行处理
     */
    private <T> DataSyncResultVO syncBatchDataWithTenant(String targetTable, List<T> batchDataList,
                                                        TenantIdentificationResultDTO tenantResult,
                                                        String sourceIp) {
        String batchNo = generateBatchNo();
        Date syncStartTime = new Date();
        int totalCount = batchDataList.size();

        try {
            // 设置租户上下文（整个批量同步过程只设置一次）
            TenantContextHolder.setTenant(tenantResult.getTenantId());
            log.info("设置租户上下文，租户ID：{}，租户编码：{}", tenantResult.getTenantId(), tenantResult.getTenantCode());

            // 获取同步策略
            SyncStrategy strategy = getSyncStrategy(targetTable);
            if (strategy == null) {
                throw new BusinessException("不支持的目标表：" + targetTable);
            }

            log.info("开始批量同步处理，批次号：{}，数据量：{}，是否启用并行：{}",
                    batchNo, totalCount, totalCount >= MIN_PARALLEL_SIZE);

            // 根据数据量决定是否使用并行处理
            SyncResult syncResult;
            if (totalCount >= MIN_PARALLEL_SIZE) {
                syncResult = processDataInParallel(batchDataList, strategy, tenantResult, batchNo, targetTable, syncStartTime, sourceIp);
            } else {
                syncResult = processDataSequentially(batchDataList, strategy, tenantResult, batchNo, targetTable, syncStartTime, sourceIp);
            }

            Date syncEndTime = new Date();

            // 记录批量同步汇总日志（每次批量同步只记录一条日志）
            recordBatchSyncSummaryLog(batchNo, targetTable, tenantResult, syncResult,
                                    totalCount, syncStartTime, syncEndTime, sourceIp);

            // 构建返回结果
            DataSyncResultVO result = new DataSyncResultVO();
            result.setBatchNo(batchNo);
            result.setTenantCode(tenantResult.getTenantCode());
            result.setTargetTable(targetTable);
            result.setOperationType("AUTO"); // 自动判断INSERT或UPDATE
            result.setSyncStatus(syncResult.failureDetails.isEmpty() ? "SUCCESS" :
                               (syncResult.successCount > 0 ? "PARTIAL_SUCCESS" : "FAILED"));
            result.setTotalCount(totalCount);
            result.setSuccessCount(syncResult.successCount);
            result.setFailedCount(totalCount - syncResult.successCount);
            result.setSyncStartTime(syncStartTime);
            result.setSyncEndTime(syncEndTime);
            result.setSyncDuration(syncEndTime.getTime() - syncStartTime.getTime());
            result.setFailureDetails(syncResult.failureDetails);

            log.info("批量数据同步完成，批次号：{}，总数：{}，成功：{}，失败：{}",
                    batchNo, totalCount, syncResult.successCount, totalCount - syncResult.successCount);

            return result;

        } catch (Exception e) {
            log.error("批量数据同步失败：{}", e.getMessage(), e);
            throw new BusinessException("批量同步过程发生异常：" + e.getMessage());
        } finally {
            // 清理租户上下文（整个批量同步过程结束后清理一次）
            TenantContextHolder.clear();
            log.debug("清理租户上下文");
        }
    }

    /**
     * 同步结果内部类
     */
    private static class SyncResult {
        int successCount;
        int insertSuccessCount;  // 新增成功数量
        int updateSuccessCount;  // 更新成功数量
        int insertFailureCount;  // 新增失败数量
        int updateFailureCount;  // 更新失败数量
        List<DataSyncResultVO.SyncFailureDetailVO> failureDetails;

        SyncResult() {
            this.successCount = 0;
            this.insertSuccessCount = 0;
            this.updateSuccessCount = 0;
            this.insertFailureCount = 0;
            this.updateFailureCount = 0;
            this.failureDetails = new ArrayList<>();
        }

        /**
         * 获取总失败数量
         */
        int getTotalFailureCount() {
            return this.insertFailureCount + this.updateFailureCount;
        }
    }

    /**
     * 并行处理数据
     */
    private <T> SyncResult processDataInParallel(List<T> batchDataList, SyncStrategy strategy,
                                               TenantIdentificationResultDTO tenantResult,
                                               String batchNo, String targetTable,
                                               Date syncStartTime, String sourceIp) {
        log.info("启用并行处理模式，数据量：{}，分片大小：{}", batchDataList.size(), BATCH_SIZE);

        // 获取当前线程的租户上下文，用于传递到子线程
        Long currentTenantId = tenantResult.getTenantId();
        log.debug("主线程租户上下文 - 租户ID: {}, 租户编码: {}", currentTenantId, tenantResult.getTenantCode());

        // 分片处理
        List<List<T>> chunks = partitionList(batchDataList, BATCH_SIZE);

        // 使用线程安全的统计计数器
        AtomicInteger totalSuccessCount = new AtomicInteger(0);
        AtomicInteger insertSuccessCount = new AtomicInteger(0);
        AtomicInteger updateSuccessCount = new AtomicInteger(0);
        AtomicInteger insertFailureCount = new AtomicInteger(0);
        AtomicInteger updateFailureCount = new AtomicInteger(0);
        List<DataSyncResultVO.SyncFailureDetailVO> allFailureDetails =
            Collections.synchronizedList(new ArrayList<>());

        // 创建并行任务，确保每个任务都正确设置租户上下文
        List<CompletableFuture<Void>> futures = chunks.stream()
            .map(chunk -> CompletableFuture.runAsync(() -> {
                try {
                    // 在子线程中设置租户上下文
                    TenantContextHolder.setTenant(currentTenantId);
                    log.debug("子线程设置租户上下文 - 租户ID: {}, 线程: {}",
                             currentTenantId, Thread.currentThread().getName());

                    // 处理数据分片
                    processChunk(chunk, strategy, tenantResult, batchNo, targetTable,
                               syncStartTime, sourceIp, totalSuccessCount, insertSuccessCount,
                               updateSuccessCount, insertFailureCount, updateFailureCount, allFailureDetails);
                } finally {
                    // 清理子线程的租户上下文
                    TenantContextHolder.clear();
                    log.debug("子线程清理租户上下文 - 线程: {}", Thread.currentThread().getName());
                }
            }, taskExecutor))
            .collect(Collectors.toList());

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        SyncResult result = new SyncResult();
        result.successCount = totalSuccessCount.get();
        result.insertSuccessCount = insertSuccessCount.get();
        result.updateSuccessCount = updateSuccessCount.get();
        result.insertFailureCount = insertFailureCount.get();
        result.updateFailureCount = updateFailureCount.get();
        result.failureDetails = allFailureDetails;
        return result;
    }

    /**
     * 顺序处理数据
     */
    private <T> SyncResult processDataSequentially(List<T> batchDataList, SyncStrategy strategy,
                                                 TenantIdentificationResultDTO tenantResult,
                                                 String batchNo, String targetTable,
                                                 Date syncStartTime, String sourceIp) {
        log.debug("使用顺序处理模式，数据量：{}", batchDataList.size());

        SyncResult result = new SyncResult();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger insertSuccessCount = new AtomicInteger(0);
        AtomicInteger updateSuccessCount = new AtomicInteger(0);
        AtomicInteger insertFailureCount = new AtomicInteger(0);
        AtomicInteger updateFailureCount = new AtomicInteger(0);

        processChunk(batchDataList, strategy, tenantResult, batchNo, targetTable,
                   syncStartTime, sourceIp, successCount, insertSuccessCount,
                   updateSuccessCount, insertFailureCount, updateFailureCount, result.failureDetails);

        result.successCount = successCount.get();
        result.insertSuccessCount = insertSuccessCount.get();
        result.updateSuccessCount = updateSuccessCount.get();
        result.insertFailureCount = insertFailureCount.get();
        result.updateFailureCount = updateFailureCount.get();
        return result;
    }

    /**
     * 处理数据分片
     */
    private <T> void processChunk(List<T> chunk, SyncStrategy strategy,
                                TenantIdentificationResultDTO tenantResult,
                                String batchNo, String targetTable,
                                Date syncStartTime, String sourceIp,
                                AtomicInteger successCount,
                                AtomicInteger insertSuccessCount,
                                AtomicInteger updateSuccessCount,
                                AtomicInteger insertFailureCount,
                                AtomicInteger updateFailureCount,
                                List<DataSyncResultVO.SyncFailureDetailVO> failureDetails) {
        for (T dataItem : chunk) {
            try {
                String dataIdentifier = getDataIdentifier(dataItem);

                // 执行同步（明确传入系统操作人，不依赖Repository层获取）
                SyncDataResultDTO syncResult = strategy.syncData(
                    dataIdentifier,
                    dataItem,
                    tenantResult.getTenantId(),
                    tenantResult.getTenantCode(),
                    OperatorUtils.SYSTEM_OPERATOR  // 明确传入系统操作人
                );

                if (syncResult.isSuccess()) {
                    successCount.incrementAndGet();
                    // 根据操作类型统计成功数量
                    if (syncResult.getOperationType() == SyncDataResultDTO.OperationType.INSERT) {
                        insertSuccessCount.incrementAndGet();
                    } else if (syncResult.getOperationType() == SyncDataResultDTO.OperationType.UPDATE) {
                        updateSuccessCount.incrementAndGet();
                    }
                } else {
                    // 根据操作类型统计失败数量
                    if (syncResult.getOperationType() == SyncDataResultDTO.OperationType.INSERT) {
                        insertFailureCount.incrementAndGet();
                    } else if (syncResult.getOperationType() == SyncDataResultDTO.OperationType.UPDATE) {
                        updateFailureCount.incrementAndGet();
                    }
                    addFailureDetail(failureDetails, dataIdentifier, syncResult.getErrorMessage());
                }

                // 移除单条数据的日志记录，改为批量汇总日志

            } catch (Exception e) {
                String dataIdentifier = getDataIdentifier(dataItem);
                log.error("处理数据项失败，数据标识：{}，错误：{}", dataIdentifier, e.getMessage());
                // 异常情况无法确定操作类型，不统计到具体操作类型中
                addFailureDetail(failureDetails, dataIdentifier, "处理异常：" + e.getMessage());
            }
        }
    }

    /**
     * 将列表分片
     */
    private <T> List<List<T>> partitionList(List<T> list, int chunkSize) {
        List<List<T>> chunks = new ArrayList<>();
        for (int i = 0; i < list.size(); i += chunkSize) {
            chunks.add(list.subList(i, Math.min(i + chunkSize, list.size())));
        }
        return chunks;
    }

    /**
     * 记录批量同步汇总日志
     */
    private void recordBatchSyncSummaryLog(String batchNo, String targetTable,
                                         TenantIdentificationResultDTO tenantResult,
                                         SyncResult syncResult, int totalCount,
                                         Date syncStartTime, Date syncEndTime, String sourceIp) {
        try {
            SysDataSyncLog syncLog = new SysDataSyncLog();
            syncLog.setBatchNo(batchNo);
            syncLog.setTargetTable(targetTable);
            syncLog.setTenantId(tenantResult.getTenantId());
            syncLog.setTenantCode(tenantResult.getTenantCode());

            // 根据同步结果设置状态
            String syncStatus;
            int totalFailureCount = syncResult.getTotalFailureCount();
            if (totalFailureCount == 0) {
                syncStatus = "SUCCESS";
            } else if (syncResult.successCount > 0) {
                syncStatus = "PARTIAL_SUCCESS";
            } else {
                syncStatus = "FAILED";
            }
            syncLog.setSyncStatus(syncStatus);

            // 构建增强的汇总信息，包含操作类型统计和总耗时
            long syncDuration = syncEndTime.getTime() - syncStartTime.getTime();
            StringBuilder summaryBuilder = new StringBuilder();
            summaryBuilder.append(String.format("批量同步汇总 - 总数:%d, 成功:%d(新增:%d,更新:%d), 失败:%d(新增:%d,更新:%d), 总耗时:%dms",
                                               totalCount,
                                               syncResult.successCount,
                                               syncResult.insertSuccessCount,
                                               syncResult.updateSuccessCount,
                                               totalFailureCount,
                                               syncResult.insertFailureCount,
                                               syncResult.updateFailureCount,
                                               syncDuration));

            // 如果有失败记录，添加失败示例
            if (!syncResult.failureDetails.isEmpty()) {
                summaryBuilder.append("。失败示例: ");
                int errorCount = Math.min(3, syncResult.failureDetails.size()); // 最多记录3条错误示例
                for (int i = 0; i < errorCount; i++) {
                    DataSyncResultVO.SyncFailureDetailVO failure = syncResult.failureDetails.get(i);
                    String errorMessage = failure.getErrorMessage();

                    // 限制单个错误信息的长度，避免过长
                    if (errorMessage != null && errorMessage.length() > 150) {
                        errorMessage = errorMessage.substring(0, 150) + "...";
                    }

                    summaryBuilder.append(String.format("[%s: %s]", failure.getSourceDataId(), errorMessage));
                    if (i < errorCount - 1) summaryBuilder.append("; ");
                }
                if (syncResult.failureDetails.size() > 3) {
                    summaryBuilder.append(String.format(" 等%d条错误", syncResult.failureDetails.size()));
                }
            }

            // 确保整个错误信息不超过数据库字段长度限制（1000字符）
            String finalErrorMessage = summaryBuilder.toString();
            if (finalErrorMessage.length() > 950) { // 留50字符余量
                finalErrorMessage = finalErrorMessage.substring(0, 950) + "...";
            }

            // 设置错误信息（成功时也设置汇总信息，便于查看统计）
            syncLog.setErrorMessage(finalErrorMessage);

            syncLog.setSourceIp(sourceIp);
            syncLog.setSyncStartTime(syncStartTime);
            syncLog.setSyncEndTime(syncEndTime);
            syncLog.setSyncDuration(syncEndTime.getTime() - syncStartTime.getTime());

            // 异步记录日志，不影响主流程
            // 注意：日志记录使用系统级表(sys_data_sync_log)，不需要租户上下文
            CompletableFuture.runAsync(() -> {
                try {
                    tableDataSyncLogService.insert(syncLog);
                    log.debug("批量同步汇总日志记录成功，批次号：{}，总数：{}，成功：{}，失败：{}",
                             batchNo, totalCount, syncResult.successCount, totalCount - syncResult.successCount);
                } catch (Exception e) {
                    log.warn("记录批量同步汇总日志失败，批次号：{}，错误：{}", batchNo, e.getMessage());
                }
            }, taskExecutor);

        } catch (Exception e) {
            log.warn("构建批量同步汇总日志失败，批次号：{}，错误：{}", batchNo, e.getMessage());
        }
    }

    /**
     * 添加失败详情（线程安全）
     */
    private void addFailureDetail(List<DataSyncResultVO.SyncFailureDetailVO> failureDetails,
                                String sourceDataId, String errorMessage) {
        DataSyncResultVO.SyncFailureDetailVO detail = new DataSyncResultVO.SyncFailureDetailVO();
        detail.setSourceDataId(sourceDataId);
        detail.setErrorMessage(errorMessage);
        detail.setFailureTime(new Date());
        failureDetails.add(detail);
    }

    /**
     * 获取数据标识（用于日志记录）
     */
    private <T> String getDataIdentifier(T dataItem) {
        if (dataItem instanceof VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO) {
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO vehicle =
                (VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO) dataItem;
            return "车辆[" + vehicle.getVin() + "/" + vehicle.getVehicleNo() + "]";
        } else if (dataItem instanceof VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO) {
            VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO model =
                (VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO) dataItem;
            return "车型[" + model.getVehicleModelName() + "]";
        } else if (dataItem instanceof OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO) {
            OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO org =
                (OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO) dataItem;
            return "机构[" + org.getOrgId() + "/" + org.getOrgName() + "]";
        }
        return "unknown";
    }

}
