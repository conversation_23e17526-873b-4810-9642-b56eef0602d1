package com.extracme.saas.autocare.service;

import java.util.List;

import com.extracme.saas.autocare.model.dto.repairTask.AccidentTaskDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;

public interface InsurancePreReviewService {

    /**
     * 进保预审-保存
     *
     * @param id                  任务id
     * @param repairTaskUpdateDTO 任务修改信息bo
     */
    void saveRepairTask(String id, RepairTaskUpdateDTO repairTaskUpdateDTO);

    /**
     * 进保预审-提交审核
     *
     * @param id                  任务id
     * @param repairTaskUpdateDTO 任务修改信息bo
     */
    void submit(String id, RepairTaskUpdateDTO repairTaskUpdateDTO);

    /**
     * 进保预审-驳回
     *
     * @param id                  任务id
     * @param repairTaskUpdateDTO 任务修改信息bo
     */
    void reject(String id, RepairTaskUpdateDTO repairTaskUpdateDTO);

    /**
     * 进保预审-通过
     *
     * @param id                  任务id
     * @param repairTaskUpdateDTO 任务修改信息bo
     */
    void pass(String id, RepairTaskUpdateDTO repairTaskUpdateDTO);

    /**
     * 进保预审-转自费
     *
     * @param id                  任务id
     * @param repairTaskUpdateDTO 任务修改信息bo
     */
    void toSelfFee(String id, RepairTaskUpdateDTO repairTaskUpdateDTO);

    /**
     * 获取事故任务信息列表
     *
     * @param vin 车架号
     * @return 处理结果
     */
    List<AccidentTaskDTO> getAccidentTaskList(String vin);

    /**
     * 关联事故编号
     *
     * @param taskId     维修任务id
     * @param accidentNo 事故编号
     * @return 处理结果
     */
    void relateAccidentTask(Long taskId, String accidentNo);

    /**
     * 验证事故编号
     *
     * @param repairTask 维修任务信息
     * @param accidentNo 事故任务编号
     * @return 验证结果
     */
    void validateAccidentNo(MtcRepairTask repairTask, String accidentNo);
}
