package com.extracme.saas.autocare.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.extracme.saas.autocare.model.dto.repairTask.AccidentTaskDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.service.InsurancePreReviewService;

/**
 * 保险预审服务实现类
 */
@Service
public class InsurancePreReviewServiceImpl implements InsurancePreReviewService {

    @Override
    public void saveRepairTask(String id, RepairTaskUpdateDTO repairTaskUpdateDTO) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'saveRepairTask'");
    }

    @Override
    public void submit(String id, RepairTaskUpdateDTO repairTaskUpdateDTO) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'submit'");
    }

    @Override
    public void reject(String id, RepairTaskUpdateDTO repairTaskUpdateDTO) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'reject'");
    }

    @Override
    public void pass(String id, RepairTaskUpdateDTO repairTaskUpdateDTO) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'pass'");
    }

    @Override
    public void toSelfFee(String id, RepairTaskUpdateDTO repairTaskUpdateDTO) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'toSelfFee'");
    }

    @Override
    public List<AccidentTaskDTO> getAccidentTaskList(String vin) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAccidentTaskList'");
    }

    @Override
    public void relateAccidentTask(Long taskId, String accidentNo) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'relateAccidentTask'");
    }

    @Override
    public void validateAccidentNo(MtcRepairTask repairTask, String accidentNo) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'validateAccidentNo'");
    }

    
}
