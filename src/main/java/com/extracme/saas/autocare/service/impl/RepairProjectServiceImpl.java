package com.extracme.saas.autocare.service.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.model.dto.RepairProjectCreateDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectUpdateStatusDTO;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairItem;
import com.extracme.saas.autocare.model.vo.RepairProjectDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairProjectListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairProjectService;
import com.extracme.saas.autocare.service.RepairProjectService;
import com.extracme.saas.autocare.util.BeanComparator;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 修理项目服务实现类
 */
@Slf4j
@Service
public class RepairProjectServiceImpl implements RepairProjectService {

    @Autowired
    private TableRepairProjectService tableRepairProjectService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Override
    public BasePageVO<RepairProjectListVO> queryRepairProjectList(RepairProjectQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<RepairProjectListVO> list = tableRepairProjectService.queryRepairItemList(queryDTO);
        PageInfo<RepairProjectListVO> pageInfo = new PageInfo<>(list);
        return BasePageVO.of(list, pageInfo);
    }

    @Override
    public RepairProjectDetailsVO getRepairProjectDetails(Long id) {
        RepairProjectDetailsVO replacePartItemDetailsVO = tableRepairProjectService.getRepairProjectDetails(id);
        if (replacePartItemDetailsVO == null) {
            return null;
        }
        return replacePartItemDetailsVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRepairProject(RepairProjectCreateDTO createDTO) {
        MtcRepairItem repairItems = tableRepairProjectService.selectByRepairName(createDTO.getRepairName());
        if (repairItems != null) {
            throw new RuntimeException("该修理名称已存在");
        }
        MtcRepairItem mtcRepairItem = new MtcRepairItem();
        BeanUtils.copyProperties(createDTO, mtcRepairItem);
        tableRepairProjectService.insert(mtcRepairItem);
        //添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(mtcRepairItem.getId());
        mtcProcessLog.setOpeContent("新增修理项目信息");
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepairProject(RepairProjectUpdateDTO updateDTO) {
        MtcRepairItem repairItem = tableRepairProjectService.selectById(updateDTO.getId());
        if (repairItem == null) {
            throw new RuntimeException("该记录不存在");
        }
        if (!StringUtils.equals(updateDTO.getRepairName(), repairItem.getRepairName())) {
            MtcRepairItem repairItems = tableRepairProjectService.selectByRepairName(updateDTO.getRepairName());
            if (repairItems != null) {
                throw new RuntimeException("该修理名称已存在");
            }
        }

        // 比较修改的内容 添加日志
        MtcRepairItem compareRepairItem = new MtcRepairItem();
        BeanUtils.copyProperties(updateDTO, compareRepairItem);
        List<Map<String, String>> differences = BeanComparator.compareObjects(repairItem, compareRepairItem);
        if (CollectionUtils.isNotEmpty(differences)) {
            StringBuilder sb = new StringBuilder();
            differences.forEach(difference -> {
                sb.append(StrUtil.format("{}：{} 修改为 {}\n", difference.get("fieldName"), difference.get("oldValue"), difference.get("newValue")));
            });
            //添加日志
            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setRecordId(repairItem.getId());
            mtcProcessLog.setOpeContent(sb.toString());
            mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM);
            tableOperatorLogService.insertSelective(mtcProcessLog);
        }
        BeanUtils.copyProperties(updateDTO, repairItem);
        tableRepairProjectService.updateSelectiveById(repairItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRepairProject(Long id) {
        MtcRepairItem replaceItem = tableRepairProjectService.selectById(id);
        if (replaceItem == null) {
            return;
        }
        tableRepairProjectService.deleteById(id);
        //添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(id);
        mtcProcessLog.setOpeContent(StrUtil.format("删除了修理项目-{}", replaceItem.getRepairName()));
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepairProjectStatus(RepairProjectUpdateStatusDTO updateStatusDTO) {
        MtcRepairItem repairItem = tableRepairProjectService.selectById(updateStatusDTO.getId());
        if (repairItem == null) {
            throw new RuntimeException("该记录不存在");
        }
        repairItem.setStatus(updateStatusDTO.getItemStatus());
        tableRepairProjectService.updateSelectiveById(repairItem);
        //添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(repairItem.getId());
        mtcProcessLog.setOpeContent(StrUtil.format("{}修理项目", updateStatusDTO.getItemStatus() == 1 ? "启用" : "禁用"));
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_ITEM);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }
}