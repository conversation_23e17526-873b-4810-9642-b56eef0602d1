package com.extracme.saas.autocare.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.ReplacePartItemCreateDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemQueryDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemUpdateDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemUpdateStatusDTO;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcPartRepairItemGrouping;
import com.extracme.saas.autocare.model.entity.MtcReplaceItem;
import com.extracme.saas.autocare.model.vo.PartRepairItemGroupingVO;
import com.extracme.saas.autocare.model.vo.ReplacePartItemDetailsVO;
import com.extracme.saas.autocare.model.vo.ReplacePartItemListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TablePartRepairItemGroupingService;
import com.extracme.saas.autocare.repository.TableReplacePartItemService;
import com.extracme.saas.autocare.service.ReplacePartItemService;
import com.extracme.saas.autocare.util.BeanComparator;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 修理厂服务实现类
 */
@Slf4j
@Service
public class ReplacePartItemServiceImpl implements ReplacePartItemService {

    @Autowired
    private TableReplacePartItemService tableReplacePartItemService;

    @Autowired
    private TablePartRepairItemGroupingService tablePartRepairItemGroupingService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Override
    public BasePageVO<ReplacePartItemListVO> queryReplacePartItemList(ReplacePartItemQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<ReplacePartItemListVO> list = tableReplacePartItemService.queryReplaceItemList(queryDTO);
        PageInfo<ReplacePartItemListVO> pageInfo = new PageInfo<>(list);
        return BasePageVO.of(list, pageInfo);
    }

    @Override
    public ReplacePartItemDetailsVO getReplacePartItemDetails(Long id) {
        MtcReplaceItem replaceItem = tableReplacePartItemService.selectById(id);
        if (replaceItem == null) {
            return null;
        }
        ReplacePartItemDetailsVO replacePartItemDetailsVO = new ReplacePartItemDetailsVO();
        BeanUtils.copyProperties(replaceItem, replacePartItemDetailsVO);
        replacePartItemDetailsVO.setGroupingName("零件分组名称");
        return replacePartItemDetailsVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createReplacePartItem(ReplacePartItemCreateDTO createDTO) {
        // 同一个公司 同一种车型的零件名称不可重复
        boolean isBool = tableReplacePartItemService.checkExistPartItemName(createDTO.getOrgId(), createDTO.getVehicleModelSeq(), createDTO.getPartName());
        if (isBool){
            throw new RuntimeException("同一个公司同一种车型的零件名称不可重复");
        }
        
        MtcReplaceItem replaceItem = new MtcReplaceItem();
        BeanUtils.copyProperties(createDTO, replaceItem);
        tableReplacePartItemService.insert(replaceItem);
        //添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(replaceItem.getId());
        mtcProcessLog.setOpeContent("新增换件项目信息");
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPLACE_ITEM);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReplacePartItem(ReplacePartItemUpdateDTO updateDTO) {
        MtcReplaceItem replaceItem = tableReplacePartItemService.selectById(updateDTO.getId());
        if (replaceItem == null) {
            throw new RuntimeException("该记录不存在");
        }
        if (!StringUtils.equals(updateDTO.getPartName(), replaceItem.getPartName())) {
            // 同一个公司 同一种车型的零件名称不可重复
            boolean isBool = tableReplacePartItemService.checkExistPartItemName(replaceItem.getOrgId(), updateDTO.getVehicleModelSeq(), updateDTO.getPartName());
            if (isBool){
                throw new RuntimeException("同一个公司同一种车型的零件名称不可重复");
            }
        }
        // 比较修改的内容 添加日志
        MtcReplaceItem compareReplaceItem = new MtcReplaceItem();
        BeanUtils.copyProperties(updateDTO, compareReplaceItem);
        List<Map<String, String>> differences = BeanComparator.compareObjects(replaceItem, compareReplaceItem);
        if (CollectionUtils.isNotEmpty(differences)) {
            StringBuilder sb = new StringBuilder();
            differences.forEach(difference -> {
                sb.append(StrUtil.format("{}：{} 修改为 {}\n", difference.get("fieldName"), difference.get("oldValue"), difference.get("newValue")));
            });
            //添加日志
            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setRecordId(replaceItem.getId());
            mtcProcessLog.setOpeContent(sb.toString());
            mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPLACE_ITEM);
            tableOperatorLogService.insertSelective(mtcProcessLog);
        }
        BeanUtils.copyProperties(updateDTO, replaceItem);
        tableReplacePartItemService.updateSelectiveById(replaceItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReplacePartItem(Long id) {
        MtcReplaceItem replaceItem = tableReplacePartItemService.selectById(id);
        if (replaceItem == null) {
            return;
        }
        tableReplacePartItemService.deleteById(id);
        //添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(replaceItem.getId());
        mtcProcessLog.setOpeContent(StrUtil.format("删除了换件项目-{}", replaceItem.getPartName()));
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPLACE_ITEM);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    public void updateReplacePartItemStatus(ReplacePartItemUpdateStatusDTO updateStatusDTO) {
        MtcReplaceItem replaceItem = tableReplacePartItemService.selectById(updateStatusDTO.getId());
        if (replaceItem == null) {
            throw new RuntimeException("该记录不存在");
        }
        replaceItem.setStatus(updateStatusDTO.getItemStatus());
        tableReplacePartItemService.updateSelectiveById(replaceItem);
        //添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(replaceItem.getId());
        mtcProcessLog.setOpeContent(StrUtil.format("{}换件项目", updateStatusDTO.getItemStatus() == 1 ? "启用" : "禁用"));
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPLACE_ITEM);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    public List<PartRepairItemGroupingVO> queryItemGroupingTree(Integer groupingType) {
        List<MtcPartRepairItemGrouping> groupingList = tablePartRepairItemGroupingService.queryPartRepairItemGroupingList(groupingType);
        if (CollectionUtils.isEmpty(groupingList)) {
            return Collections.emptyList();
        }
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        groupingList.forEach(info -> {
            Map<String, Object> map = BeanUtil.beanToMap(info);
            TreeNode<String> node = new TreeNode<>(map.get("groupingId").toString(), map.get("fatherId").toString(), map.get("groupingName").toString(), 0L);
            node.setExtra(map);
            nodeList.add(node);
        });
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setWeightKey("weight");
        treeNodeConfig.setIdKey("id");
        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(nodeList, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Long sort = Convert.toLong(treeNode.getExtra().get("levelNo"), 0L);
                    Date createTime = (Date) treeNode.getExtra().get("createTime");
                    if (sort == 0 && treeNode.getExtra().get("createTime") != null) {
                        sort = createTime.getTime();
                    }
                    tree.putExtra("sort", treeNode.getExtra().get("position"));
                    tree.putExtra("weight", sort);
                });
        if (ObjectUtil.length(treeNodes) == 0) {
            throw new BusinessException("零件修理项目结构错误");
        }
        List<PartRepairItemGroupingVO> treeListResponse = BeanUtil.copyToList(treeNodes, PartRepairItemGroupingVO.class);// JSONUtil.toBean(JSONUtil.toJsonStr(tree), OrgTreeResponse.class);
        return treeListResponse;

    }
}