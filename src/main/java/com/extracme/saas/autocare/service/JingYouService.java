package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.JingYouLossNotify;
import com.extracme.saas.autocare.model.jingyou.PacketBeanAssBack;
import com.extracme.saas.autocare.model.jingyou.PacketBeanEvaBack;
import com.extracme.saas.autocare.model.jingyou.PacketResponse;
import com.extracme.saas.autocare.model.vo.LossDetailVO;

public interface JingYouService {

    /**
     * 定损报价（请求定损系统）
     * 
     * @param id 任务id
     * @returns
     */
    String lossAssessment(Long id);

    /**
     * 定损报价（请求定损系统）
     *
     * @param id 任务id
     * @return .
     */
    String lossAssessment(Long id, String parameterString);

    /**
     * 核损核价（请求定损系统）
     * 
     * @param id 任务id
     * @return .
     */
    String evaluateLoss(Long id);

    /**
     * 核损核价（请求定损系统）
     *
     * @param id              任务id
     * @param parameterString 回调参数
     * @return .
     */
    String evaluateLoss(Long id, String parameterString);

    /**
     * 核损状态变更通知精友
     *
     * @param taskNo            任务编号
     * @param jingYouLossNotify 通知数据
     * @return .
     */
    void lossNotify(String taskNo, JingYouLossNotify jingYouLossNotify);

    /**
     * 定损查看（请求定损系统）
     * 
     * @param id 任务id
     * @return .
     */
    String viewLoss(Long id);

    /**
     * 定损完成返回
     * 
     * @param packetBeanAssBack 定损系统返回数据
     * @return .
     */
    PacketResponse lossAssessmentBack(PacketBeanAssBack packetBeanAssBack);

    /**
     * 核损完成返回
     * 
     * @param packetBeanEvaBack 定损系统返回数据
     * @return .
     */
    PacketResponse evaluateLossBack(PacketBeanEvaBack packetBeanEvaBack);

    /**
     * 获取定损详情
     * 
     * @param taskNo 维修任务编号
     * @return .
     */
    LossDetailVO getLossDetail(String taskNo);
}
