package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.StsCredentials;

/**
 * STS临时凭证服务接口
 */
public interface StsCredentialService {

    /**
     * 获取STS临时凭证
     * 
     * @return STS临时凭证
     */
    StsCredentials getCredentials();

    /**
     * 刷新STS临时凭证
     * 
     * @return 新的STS临时凭证
     */
    StsCredentials refreshCredentials();

    /**
     * 检查凭证是否即将过期（剩余时间少于5分钟）
     * 
     * @return true如果即将过期，false否则
     */
    boolean isCredentialsExpiringSoon();

    /**
     * 获取有效的STS临时凭证（自动刷新）
     * 
     * @return 有效的STS临时凭证
     */
    StsCredentials getValidCredentials();
}
