package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityDefinitionEnum;
import com.extracme.saas.autocare.enums.RepairPicTypeEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.FileDTO;
import com.extracme.saas.autocare.model.dto.LossAssessmentApproveDTO;
import com.extracme.saas.autocare.model.dto.LossAssessmentRejectDTO;
import com.extracme.saas.autocare.model.dto.LossAssessmentUpdateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.entity.*;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.ApprovalLevelsVO;
import com.extracme.saas.autocare.repository.*;
import com.extracme.saas.autocare.service.ApprovalLevelsService;
import com.extracme.saas.autocare.service.LossAssessmentService;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 核损核价服务实现类
 */
@Slf4j
@Service
public class LossAssessmentServiceImpl implements LossAssessmentService {

    @Autowired
    private RepairTaskService repairTaskService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private ApprovalLevelsService approvalLevelsService;

    @Autowired
    private TableRepairItemCheckInfoService tableRepairItemCheckInfoService;

    @Autowired
    private TableLossInfoService tableLossInfoService;

    @Autowired
    private TableLossFitInfoService tableLossFitInfoService;

    @Autowired
    private TableLossRepairInfoService tableLossRepairInfoService;

    @Autowired
    private TableLossOuterRepairInfoService tableLossOuterRepairInfoService;

    @Autowired
    private TableLossAssistInfoService tableLossAssistInfoService;

    @Autowired
    private TableVehicleInfoService tableVehicleInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLossAssessment(LossAssessmentUpdateDTO lossAssessmentDTO) {
        log.info("保存核损核价信息: {}", lossAssessmentDTO);

        // 获取维修任务
        MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(lossAssessmentDTO.getTaskNo());
        if (mtcRepairTask == null) {
            throw new BusinessException("未找到维修任务: " + lossAssessmentDTO.getTaskNo());
        }

        // 1.更新维修任务核损核价信息
        RepairTaskUpdateDTO updateDTO = new RepairTaskUpdateDTO();
        BeanUtils.copyProperties(lossAssessmentDTO, updateDTO);
        // 保存维修任务
        repairTaskService.saveRepairTask(mtcRepairTask.getId(), updateDTO);
        // 更新自费金额
        repairTaskService.updateSelfFundedAmount(mtcRepairTask.getTaskNo(), null);

        // 2.保存图片逻辑
        saveLossAssessmentImages(lossAssessmentDTO);

        // 3. 更新车辆里程数
        if (null != lossAssessmentDTO.getTotalMileage()) {
            MtcVehicleInfo mtcVehicleInfo = tableVehicleInfoService.findByVin(mtcRepairTask.getVin());
            if (null == mtcVehicleInfo) {
                throw new BusinessException("未找到车辆信息");
            }
            MtcVehicleInfo updateVehicleInfo = new MtcVehicleInfo();
            updateVehicleInfo.setId(mtcVehicleInfo.getId());
            updateVehicleInfo.setTotalMileage(lossAssessmentDTO.getTotalMileage().intValue());
            tableVehicleInfoService.updateSelectiveById(updateVehicleInfo);
        }
    }

    /**
     * 保存核损核价相关图片
     *
     * @param lossAssessmentDTO 维修报价DTO
     * @throws BusinessException 业务异常
     */
    private void saveLossAssessmentImages(LossAssessmentUpdateDTO lossAssessmentDTO) {
        try {
            // 构建媒体类型映射
            Map<Integer, List<FileDTO>> mediaTypeMap = new HashMap<>();

            // 添加各种类型的图片到映射中
            if (lossAssessmentDTO.getDamagedPartPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.DAMAGED_PART_PICTURE.getTypeId(),
                        lossAssessmentDTO.getDamagedPartPicture());
            }

            if (lossAssessmentDTO.getDamagedPartVideo() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.DAMAGED_PART_VIDEO.getTypeId(),
                        lossAssessmentDTO.getDamagedPartVideo());
            }

            if (lossAssessmentDTO.getCustPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.CUST_PICTURE.getTypeId(),
                        lossAssessmentDTO.getCustPicture());
            }

            // 如果有图片需要处理，则调用processMediaFiles方法
            if (!mediaTypeMap.isEmpty()) {
                String operatorName = SessionUtils.getUsername();
                repairTaskService.processMediaFiles(lossAssessmentDTO.getTaskNo(), mediaTypeMap, operatorName);
                log.info("核损核价图片保存成功，任务编号: {}", lossAssessmentDTO.getTaskNo());
            }
        } catch (Exception e) {
            log.error("保存核损核价图片失败: {}", e.getMessage(), e);
            throw new BusinessException("保存核损核价图片失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveLossAssessment(LossAssessmentApproveDTO approveDTO) {
        log.info("审核完成核损核价: {}", approveDTO);
        // 获取登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();

        // 1. 获取维修任务
        MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(approveDTO.getTaskNo());
        if (mtcRepairTask == null) {
            throw new BusinessException("未找到维修任务: " + approveDTO.getTaskNo());
        }

        // 2. 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(approveDTO.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + approveDTO.getTaskNo());
        }

        // 3. 验证当前工作流状态是否允许核损核价审核通过
        validateWorkflowStatus(workflowInstance);

        // 4. 验证用户核损核价级别
        Integer lossAssessmentLevel = validateUserLossAssessmentLevel(loginUser, mtcRepairTask);

        // 5. 验证定损数据 & 验证是否全部通过
        validateLossData(mtcRepairTask);

        // 6. 保存核损核价信息
        LossAssessmentUpdateDTO lossAssessmentDTO = new LossAssessmentUpdateDTO();
        BeanUtils.copyProperties(approveDTO, lossAssessmentDTO);
        saveLossAssessment(lossAssessmentDTO);

        // 7. 验证复勘部位
        validateResurveyPart(approveDTO);

        // 8. 验证价格异议状态
        validatePriceObjection(approveDTO);

        // 9. 验证核损金额
        validateLossAmount(mtcRepairTask, approveDTO, lossAssessmentLevel);

        // 获取下一审核级别配置
        ApprovalLevelsVO approvalLevelsVO = approvalLevelsService.getApprovalLevelsByLevel(lossAssessmentLevel);
        if (null == approvalLevelsVO || approvalLevelsVO.getSelfApprovalAmount() == null
                || approvalLevelsVO.getHasNextLevel() == null) {
            throw new BusinessException("未找到核损核价级别配置");
        }

        // 10. 存在下一级配置且核损金额大于等于配置金额 需进入下一级审核
        boolean intoNextStageAudit = approvalLevelsVO.getHasNextLevel()
                && mtcRepairTask.getVehicleInsuranceTotalAmount()
                        .compareTo(approvalLevelsVO.getSelfApprovalAmount()) > 0;
        if (intoNextStageAudit) {
            // 更新维修任务核损核价级别
            RepairTaskUpdateDTO repairTaskUpdateDTO = new RepairTaskUpdateDTO();
            repairTaskUpdateDTO.setAdvancedAuditLevel(lossAssessmentLevel + 1);
            repairTaskService.saveRepairTask(mtcRepairTask.getId(), repairTaskUpdateDTO);

            // 调用工作流服务处理节点
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("CLEAR_LOSS_ASSESSMENT_OWNER");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
            processDTO.setOperator(loginUser.getUsername());
            workflowService.processNode(workflowInstance.getId(), processDTO);
        } else {
            // 调用工作流服务处理节点
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("APPROVE_QUOTE");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
            processDTO.setOperator(loginUser.getUsername());

            try {
                workflowService.processNode(workflowInstance.getId(), processDTO);
                log.info("审核完成核损核价成功, 任务编号: {}", approveDTO.getTaskNo());

                // 记录操作日志
                MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
                mtcProcessLog.setRecordId(mtcRepairTask.getId());
                mtcProcessLog.setRemark(approveDTO.getApprovalRemark());
                mtcProcessLog.setOpeContent("审核通过核损核价，金额：" + mtcRepairTask.getVehicleInsuranceTotalAmount());
                mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
                tableOperatorLogService.insertSelective(mtcProcessLog);
            } catch (Exception e) {
                log.error("审核完成核损核价失败: {}", e.getMessage(), e);
                throw new BusinessException("审核完成核损核价失败: " + e.getMessage());
            }
        }
    }

    /**
     * 验证工作流状态
     * 
     * @param workflowInstance 工作流实例
     */
    private void validateWorkflowStatus(WorkflowInstance workflowInstance) {
        // 验证当前节点是否为核损核价节点
        if (!"LOSS_ASSESSMENT".equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("当前工作流节点不是核损核价节点");
        }

        // 验证当前状态是否允许审核通过
        if (!"PROCESSING".equals(workflowInstance.getStatusCode())) {
            throw new BusinessException("当前状态不允许审核通过");
        }
    }

    /**
     * 验证核损金额
     * 
     * @param mtcRepairTask       维修任务
     * @param approveDTO          审核参数
     * @param lossAssessmentLevel 用户核损核价级别
     */
    private void validateLossAmount(MtcRepairTask mtcRepairTask, LossAssessmentApproveDTO approveDTO,
            Integer lossAssessmentLevel) {
        // 如果是事故维修任务且不是转自费的情况
        if (mtcRepairTask.getRepairTypeId() != null &&
                mtcRepairTask.getRepairTypeId() == 1 &&
                (mtcRepairTask.getReviewToSelFeeFlag() == null || mtcRepairTask.getReviewToSelFeeFlag() == 0)) {

            // 检查是否有预审金额
            if (mtcRepairTask.getRepairReviewTotalAmount() != null) {
                // 核损金额超过预审金额130%的检查
                BigDecimal maxAllowedAmount = mtcRepairTask.getRepairReviewTotalAmount()
                        .multiply(new BigDecimal("1.3"));

                if (mtcRepairTask.getRepairInsuranceTotalAmount() != null &&
                        maxAllowedAmount.compareTo(mtcRepairTask.getRepairInsuranceTotalAmount()) < 0) {

                    // 根据核损核价级别判断是否允许超额
                    // 一级核损核价人员不允许超额
                    if (lossAssessmentLevel == 1) {
                        throw new BusinessException("核损金额超过预审金额130%，不可审核通过");
                    }

                    // 检查是否允许超额审核通过（针对高级别核损核价人员）
                    if (approveDTO.getCheckFlag() == null || approveDTO.getCheckFlag() == 0) {
                        throw new BusinessException("核损金额超过预审金额130%，请确认是否继续审核通过");
                    }
                }
            }
        }
    }

    /**
     * 核损核价-平级移交
     * 将当前处理中的核损核价任务移交给其他人处理
     *
     * @param taskNo 任务编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransferTask(String taskNo) {
        log.info("核损核价-平级移交: {}", taskNo);

        // 获取登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();

        // 1. 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + taskNo);
        }

        // 2. 校验任务状态
        // 取得任务状态详情
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(taskNo);
        if (workflowInstance == null || !ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode()
                .equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("任务已经不处于核损核价环节，请刷新页面重试");
        }

        // 检查当前环节是否处于核损核价环节
        if (!ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode().equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("任务已经不处于核损核价环节，请刷新页面重试");
        }

        // 3. 校验任务占有人
        if (!loginUser.getUser().getId().equals(repairTask.getVerificationLossTaskOperId())) {
            throw new BusinessException("你没有占据此任务，不可移交");
        }

        // 4. 调用工作流服务处理节点
        WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
        processDTO.setTriggerEvent("CLEAR_LOSS_ASSESSMENT_OWNER");
        processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
        processDTO.setOperator(loginUser.getUsername());
        workflowService.processNode(workflowInstance.getId(), processDTO);

        // 5. 记录操作日志
        MtcOperatorLog operatorLog = new MtcOperatorLog();
        operatorLog.setRecordId(repairTask.getId());
        operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
        operatorLog.setRemark("核损核价");
        operatorLog.setOpeContent("平级移交");
        operatorLog.setCurrentActivityCode(workflowInstance.getCurrentActivityCode());
        tableOperatorLogService.insertSelective(operatorLog);
        log.info("核损核价-平级移交成功: {}", taskNo);
    }

    /**
     * 核损核价-清除任务占据人
     * 管理员可以清除任务占据人，使任务可以被其他人处理
     *
     * @param taskNo 任务编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearOwner(String taskNo) {
        log.info("核损核价-清除任务占据人: {}", taskNo);

        // 获取登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();

        // 1. 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + taskNo);
        }

        // 2. 校验任务状态
        // 取得任务状态详情
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(taskNo);
        if (workflowInstance == null || !ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode()
                .equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("任务已经不处于核损核价环节，请刷新页面重试");
        }

        // 检查当前环节是否处于核损核价环节
        if (!ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode().equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("任务已经不处于核损核价环节，请刷新页面重试");
        }

        // 3. 调用工作流服务处理节点
        WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
        processDTO.setTriggerEvent("CLEAR_LOSS_ASSESSMENT_OWNER");
        processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
        processDTO.setOperator(loginUser.getUsername());
        workflowService.processNode(workflowInstance.getId(), processDTO);

        // 4. 记录操作日志
        MtcOperatorLog operatorLog = new MtcOperatorLog();
        operatorLog.setRecordId(repairTask.getId());
        operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
        operatorLog.setRemark("核损核价");
        operatorLog.setOpeContent("清除任务占据人");
        operatorLog.setCurrentActivityCode(workflowInstance.getCurrentActivityCode());
        tableOperatorLogService.insertSelective(operatorLog);

        log.info("核损核价-清除任务占据人成功: {}", taskNo);
    }

    /**
     * 验证定损数据
     *
     * @param mtcRepairTask 任务
     */
    private void validateLossData(MtcRepairTask mtcRepairTask) {
        if (mtcRepairTask.getPartsLibraryType() == 1) {
            validateSelfLossData(mtcRepairTask);
        } else if (mtcRepairTask.getPartsLibraryType() == 2) {
            validateJingYouLossData(mtcRepairTask);
        }
    }

    /**
     * 验证自有配件库定损数据
     *
     * @param mtcRepairTask 维修任务
     */
    private void validateSelfLossData(MtcRepairTask mtcRepairTask) {
        // 检查是否存在定损数据
        List<MtcRepairItemCheckInfo> checkInfoList = tableRepairItemCheckInfoService
                .selectByTaskNo(mtcRepairTask.getTaskNo());
        if (CollectionUtils.isEmpty(checkInfoList)) {
            throw new BusinessException("未经过定损系统维修报价，请退回后重新定损");
        }

        // 检查是否存在未通过核价的项目
        boolean hasUnapprovedItems = checkInfoList.stream()
                .anyMatch(item -> item.getCheckStatus() != null && item.getCheckStatus() != 1);
        if (hasUnapprovedItems) {
            throw new BusinessException("核价项目未全部通过，不可审核通过");
        }

        BigDecimal vehicleReplaceTotalAmount = BigDecimal.ZERO;
        BigDecimal vehicleRepairTotalAmount = BigDecimal.ZERO;
        BigDecimal vehicleInsuranceTotalAmount = BigDecimal.ZERO;
        for (MtcRepairItemCheckInfo itemCheckInfo : checkInfoList) {
            // 计算金额
            BigDecimal insuranceQuoteAmount = (itemCheckInfo.getInsuranceQuoteMaterialCostPrice()
                    .add(itemCheckInfo.getInsuranceQuoteHourFeePrice()))
                    .multiply(new BigDecimal(itemCheckInfo.getItemNumber()));

            // 累加金额
            vehicleReplaceTotalAmount = vehicleReplaceTotalAmount.add(itemCheckInfo.getInsuranceQuoteMaterialCostPrice()
                    .multiply(new BigDecimal(itemCheckInfo.getItemNumber())));
            vehicleRepairTotalAmount = vehicleRepairTotalAmount.add(itemCheckInfo.getInsuranceQuoteHourFeePrice()
                    .multiply(new BigDecimal(itemCheckInfo.getItemNumber())));
            vehicleInsuranceTotalAmount = vehicleInsuranceTotalAmount.add(insuranceQuoteAmount);
        }
    }

    /**
     * 验证精友配件库定损数据
     *
     * @param mtcRepairTask 维修任务
     */
    private void validateJingYouLossData(MtcRepairTask mtcRepairTask) {
        List<MtcLossInfo> mtcLossInfoList = tableLossInfoService.selectByTaskNo(mtcRepairTask.getTaskNo());
        if (CollectionUtils.isEmpty(mtcLossInfoList)) {
            throw new BusinessException("未经过定损系统维修报价，请退回后重新定损");
        }

        MtcLossInfo mtcLossInfo = mtcLossInfoList.get(0);
        if (StringUtils.isBlank(mtcLossInfo.getAuditHandlerCode())) {
            throw new BusinessException("请先进行核损核价");
        }

        List<MtcLossFitInfo> fitInfoList = tableLossFitInfoService.selectByTaskNo(mtcRepairTask.getTaskNo());
        long fitCount = fitInfoList.stream().filter(item -> !item.getCheckState().equals("01")).count();
        if (fitCount > 0) {
            throw new BusinessException("核价项目未全部通过，不可审核通过");
        }

        List<MtcLossRepairInfo> repairInfoList = tableLossRepairInfoService.selectByTaskNo(mtcRepairTask.getTaskNo());
        long repairCount = repairInfoList.stream().filter(item -> !item.getCheckState().equals("01")).count();
        if (repairCount > 0) {
            throw new BusinessException("核价项目未全部通过，不可审核通过");
        }

        List<MtcLossOuterRepairInfo> outerRepairInfoList = tableLossOuterRepairInfoService.selectByTaskNo(mtcRepairTask.getTaskNo());
        long outerRepairCount = outerRepairInfoList.stream().filter(item -> !item.getCheckState().equals("01")).count();
        if (outerRepairCount > 0) {
            throw new BusinessException("核价项目未全部通过，不可审核通过");
        }

        List<MtcLossAssistInfo> assistInfoList = tableLossAssistInfoService.selectByTaskNo(mtcRepairTask.getTaskNo());
        long assistCount = assistInfoList.stream().filter(item -> !item.getCheckState().equals("01")).count();
        if (assistCount > 0) {
            throw new BusinessException("核价项目未全部通过，不可审核通过");
        }
    }

    /**
     * 验证复勘部位
     * 
     * @param approveDTO 审核参数
     */
    private void validateResurveyPart(LossAssessmentApproveDTO approveDTO) {
        // // 选择"是"，则复勘部位必填
        // if (approveDTO.getResurveyFlag() != null &&
        // "1".equals(approveDTO.getResurveyFlag())) {
        // if (approveDTO.getResurveyPart() == null ||
        // approveDTO.getResurveyPart().trim().isEmpty()) {
        // throw new BusinessException("复勘部位不能为空");
        // }
        // }
    }

    /**
     * 验证价格异议状态
     * 
     * @param approveDTO 审核参数
     */
    private void  validatePriceObjection(LossAssessmentApproveDTO approveDTO) {
        // // 事故维修 价格异议
        // if (approveDTO.getVehicleManageViewFlag() != null &&
        // approveDTO.getVehicleManageViewFlag() == 1) {
        // throw new BusinessException("核价异议,不可审核通过");
        // }
    }

    /**
     * 验证用户核损核价级别
     * 
     * @param loginUser     登录用户
     * @param mtcRepairTask 维修任务
     * @return 用户核损核价级别
     */
    private Integer validateUserLossAssessmentLevel(LoginUser loginUser, MtcRepairTask mtcRepairTask) {
        // 获取用户信息
        String username = loginUser.getUsername();

        // 获取用户核损核价级别
        Integer lossAssessmentLevel = loginUser.getApprovalLevel();

        // 如果用户没有核损核价权限
        if (lossAssessmentLevel == null) {
            throw new BusinessException("您没有核损核价权限，不可进行核损核价审核");
        }

        // 验证用户核损核价级别与任务要求的级别是否匹配
        Integer requiredLevel = mtcRepairTask.getAdvancedAuditLevel();
        if (requiredLevel != null && !requiredLevel.equals(lossAssessmentLevel)) {
            throw new BusinessException("当前用户级别：" + lossAssessmentLevel + "。该任务在" + requiredLevel + "级审核级别，请更换相应权限账号");
        }

        log.info("用户[{}]核损核价级别: {}, 任务要求级别: {}", username, lossAssessmentLevel, requiredLevel);

        return lossAssessmentLevel;
    }

    @Override
    public void rejectLossAssessment(LossAssessmentRejectDTO rejectDTO) {
        log.info("核损核价-任务退回: {}", rejectDTO.getTaskNo());

        // 获取登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();

        // 1. 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(rejectDTO.getTaskNo());
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + rejectDTO.getTaskNo());
        }

        // 2. 校验任务状态
        // 取得任务状态详情
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(rejectDTO.getTaskNo());
        if (workflowInstance == null || !ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode()
                .equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("任务已经不处于核损核价环节，请刷新页面重试");
        }

        // 检查当前环节是否处于核损核价环节
        if (!ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode().equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("任务已经不处于核损核价环节，请刷新页面重试");
        }

        log.info("被驳回时等级advancedAuditLevel:" + repairTask.getAdvancedAuditLevel());
        if (repairTask.getPartsLibraryType() == 2) {
            // jingyou 核价项目全部通过，不可退回
            List<MtcLossInfo> mtcLossInfoList = tableLossInfoService.selectByTaskNo(repairTask.getTaskNo());
            if (CollectionUtils.isNotEmpty(mtcLossInfoList)) {
                List<MtcLossFitInfo> fitInfoList = tableLossFitInfoService.selectByTaskNo(repairTask.getTaskNo());
                long fitCount = fitInfoList.stream().filter(item -> !item.getCheckState().equals("01")).count();

                List<MtcLossRepairInfo> repairInfoList = tableLossRepairInfoService
                        .selectByTaskNo(repairTask.getTaskNo());
                long repairCount = repairInfoList.stream().filter(item -> !item.getCheckState().equals("01")).count();

                List<MtcLossOuterRepairInfo> outerRepairInfoList = tableLossOuterRepairInfoService
                        .selectByTaskNo(repairTask.getTaskNo());
                long outerRepairCount = outerRepairInfoList.stream().filter(item -> !item.getCheckState().equals("01"))
                        .count();

                List<MtcLossAssistInfo> assistInfoList = tableLossAssistInfoService
                        .selectByTaskNo(repairTask.getTaskNo());
                long assistCount = assistInfoList.stream().filter(item -> !item.getCheckState().equals("01")).count();

                if (fitCount == 0
                        && repairCount == 0
                        && outerRepairCount == 0
                        && assistCount == 0) {
                    throw new BusinessException("精友核价项目全部通过，不可退回");
                }
            }
        }

        // 3. 分级别处理逻辑
        MtcRepairTask updateRepairTask = new MtcRepairTask();
        updateRepairTask.setId(repairTask.getId());
        updateRepairTask.setUpdateBy(SessionUtils.getUsername());
        updateRepairTask.setUpdatedTime(new Date());
        updateRepairTask.setVerificationRejectReasons(rejectDTO.getVerificationRejectReasons());
        updateRepairTask.setVerificationRejectReasonsDetail(rejectDTO.getVerificationRejectReasonsDetail());
        if (repairTask.getAdvancedAuditLevel() != 1) {
            // 退回至1级核价
            updateRepairTask.setAdvancedAuditLevel(1);
            // 记录操作日志
            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setRecordId(repairTask.getId());
            mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcProcessLog.setRemark("核损核价");
            mtcProcessLog.setOpeContent(repairTask.getAdvancedAuditLevel() + "级退回至1级审核，金额为："
                    + repairTask.getVehicleInsuranceTotalAmount() + "元");
            tableOperatorLogService.insertSelective(mtcProcessLog);
        } else {
            // 退回至维修报价
            // 调用工作流服务处理节点
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("REJECT_LOSS_ASSESSMENT");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
            processDTO.setOperator(loginUser.getUsername());
            workflowService.processNode(workflowInstance.getId(), processDTO);
        }
        // 更新维修任务
        tableRepairTaskService.updateSelectiveById(updateRepairTask);
        // 清楚占据人
        tableRepairTaskService.clearOwner(repairTask.getId(), SessionUtils.getUsername());
    }
}
