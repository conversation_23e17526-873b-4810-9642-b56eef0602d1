package com.extracme.saas.autocare.service.impl;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import com.extracme.saas.autocare.config.OssConfig;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.StsCredentials;
import com.extracme.saas.autocare.service.StsCredentialService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.locks.ReentrantLock;

/**
 * STS临时凭证服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StsCredentialServiceImpl implements StsCredentialService {

    private final OssConfig ossConfig;

    // 当前有效的STS凭证
    private volatile StsCredentials currentCredentials;

    // 凭证刷新锁，防止并发刷新
    private final ReentrantLock refreshLock = new ReentrantLock();

    @Override
    public StsCredentials getCredentials() {
        if (currentCredentials == null) {
            return refreshCredentials();
        }
        return currentCredentials;
    }

    @Override
    public StsCredentials refreshCredentials() {
        refreshLock.lock();
        try {
            log.info("开始刷新STS临时凭证...");
            
            // 验证配置
            validateConfig();
            
            // 创建STS客户端
            IAcsClient stsClient = createStsClient();
            
            // 构建AssumeRole请求
            AssumeRoleRequest request = new AssumeRoleRequest();
            request.setRoleArn(ossConfig.getRoleArn());
            request.setRoleSessionName(ossConfig.getRoleSessionName());
            request.setDurationSeconds(ossConfig.getDurationSeconds());
            
            // 设置权限策略（可选，限制临时凭证的权限）
            request.setPolicy(buildOssPolicy());
            
            log.debug("STS请求参数: roleArn={}, sessionName={}, duration={}s", 
                ossConfig.getRoleArn(), ossConfig.getRoleSessionName(), ossConfig.getDurationSeconds());
            
            // 调用STS服务
            AssumeRoleResponse response = stsClient.getAcsResponse(request);
            AssumeRoleResponse.Credentials credentials = response.getCredentials();
            
            // 构建STS凭证对象
            StsCredentials stsCredentials = new StsCredentials();
            stsCredentials.setAccessKeyId(credentials.getAccessKeyId());
            stsCredentials.setAccessKeySecret(credentials.getAccessKeySecret());
            stsCredentials.setSecurityToken(credentials.getSecurityToken());
            stsCredentials.setRequestTime(LocalDateTime.now());
            
            // 解析过期时间
            LocalDateTime expiration = parseExpirationTime(credentials.getExpiration());
            stsCredentials.setExpiration(expiration);
            
            // 更新当前凭证
            currentCredentials = stsCredentials;
            
            log.info("STS临时凭证刷新成功: accessKeyId={}, expiration={}, remainingMinutes={}", 
                credentials.getAccessKeyId().substring(0, 8) + "***", 
                expiration.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                stsCredentials.getRemainingMinutes());
            
            return stsCredentials;
            
        } catch (Exception e) {
            log.error("刷新STS临时凭证失败", e);
            throw new BusinessException("获取STS临时凭证失败: " + e.getMessage());
        } finally {
            refreshLock.unlock();
        }
    }

    @Override
    public boolean isCredentialsExpiringSoon() {
        if (currentCredentials == null) {
            return true;
        }
        return currentCredentials.isExpiringSoon(5); // 5分钟内过期
    }

    @Override
    public StsCredentials getValidCredentials() {
        // 如果凭证不存在或即将过期，则刷新
        if (currentCredentials == null || isCredentialsExpiringSoon()) {
            return refreshCredentials();
        }
        return currentCredentials;
    }

    /**
     * 验证STS配置
     */
    private void validateConfig() {
        if (!StringUtils.hasText(ossConfig.getAccessKeyId())) {
            throw new BusinessException("STS配置错误: accessKeyId不能为空");
        }
        if (!StringUtils.hasText(ossConfig.getAccessKeySecret())) {
            throw new BusinessException("STS配置错误: accessKeySecret不能为空");
        }
        if (!StringUtils.hasText(ossConfig.getRoleArn())) {
            throw new BusinessException("STS配置错误: roleArn不能为空");
        }
        if (!StringUtils.hasText(ossConfig.getRoleSessionName())) {
            throw new BusinessException("STS配置错误: roleSessionName不能为空");
        }
    }

    /**
     * 创建STS客户端
     */
    private IAcsClient createStsClient() {
        // 使用默认的STS endpoint
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", 
            ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret());
        return new DefaultAcsClient(profile);
    }

    /**
     * 构建OSS权限策略
     */
    private String buildOssPolicy() {
        // 构建限制性的OSS权限策略
        return "{\n" +
               "  \"Version\": \"1\",\n" +
               "  \"Statement\": [\n" +
               "    {\n" +
               "      \"Effect\": \"Allow\",\n" +
               "      \"Action\": [\n" +
               "        \"oss:PutObject\",\n" +
               "        \"oss:GetObject\",\n" +
               "        \"oss:DeleteObject\",\n" +
               "        \"oss:InitiateMultipartUpload\",\n" +
               "        \"oss:UploadPart\",\n" +
               "        \"oss:CompleteMultipartUpload\",\n" +
               "        \"oss:AbortMultipartUpload\",\n" +
               "        \"oss:ListParts\"\n" +
               "      ],\n" +
               "      \"Resource\": [\n" +
               "        \"acs:oss:*:*:" + ossConfig.getBucketName() + "/*\"\n" +
               "      ]\n" +
               "    }\n" +
               "  ]\n" +
               "}";
    }

    /**
     * 解析过期时间字符串
     */
    private LocalDateTime parseExpirationTime(String expirationStr) {
        try {
            // STS返回的时间格式通常是ISO 8601格式
            // 例如: 2024-06-05T10:30:00Z
            if (expirationStr.endsWith("Z")) {
                expirationStr = expirationStr.substring(0, expirationStr.length() - 1);
            }
            return LocalDateTime.parse(expirationStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
        } catch (Exception e) {
            log.warn("解析STS过期时间失败: {}, 使用默认过期时间", expirationStr, e);
            // 如果解析失败，使用当前时间加上配置的有效期
            return LocalDateTime.now().plusSeconds(ossConfig.getDurationSeconds() - 300); // 减去5分钟作为缓冲
        }
    }
}
