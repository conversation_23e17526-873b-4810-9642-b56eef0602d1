package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.MultipartUploadCompleteResult;
import com.extracme.saas.autocare.model.dto.MultipartUploadInitResult;
import com.extracme.saas.autocare.model.dto.PartETagInfo;
import com.extracme.saas.autocare.model.dto.ResumableUploadInitDTO;
import com.extracme.saas.autocare.model.vo.FileUploadResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * OSS 文件上传服务接口
 */
public interface OssService {

    /**
     * 普通文件上传（小文件）
     * 适用于小于10MB的文件，直接上传到OSS
     *
     * @param file 上传的文件
     * @param category 文件分类目录（可选）
     * @return 文件上传结果
     */
    FileUploadResultVO uploadSimpleFile(MultipartFile file, String category);

    /**
     * 普通文件上传（大文件）
     * 适用于小于50MB的文件，直接上传到OSS
     *
     * @param file 上传的文件
     * @param category 文件分类目录（可选）
     * @return 文件上传结果
     */
    FileUploadResultVO uploadLargeSimpleFile(MultipartFile file, String category);

    /**
     * 批量文件上传（小文件）
     * 适用于多个小于10MB的文件，直接上传到OSS
     *
     * @param files 上传的文件列表
     * @param category 文件分类目录（可选）
     * @return 文件上传结果列表
     */
    List<FileUploadResultVO> uploadSimpleFiles(List<MultipartFile> files, String category);

    /**
     * 初始化断点续传上传
     * 后端自动生成文件路径并返回uploadId
     *
     * @param initDTO 初始化参数
     * @return 分片上传初始化结果
     */
    MultipartUploadInitResult initializeResumableUpload(ResumableUploadInitDTO initDTO);

    /**
     * 生成分片上传的预签名 URL
     *
     * @param uploadId 分片上传ID
     * @param partNumber 分片编号（从1开始）
     * @param expiration 过期时间（秒）
     * @return 预签名 URL
     */
    String generatePresignedPartUploadUrl(String uploadId, int partNumber, long expiration);

    /**
     * 完成分片上传
     *
     * @param uploadId 分片上传ID
     * @param partETags 分片ETag列表
     * @return 完成结果
     */
    MultipartUploadCompleteResult completeMultipartUpload(String uploadId, List<PartETagInfo> partETags);

    /**
     * 取消分片上传
     *
     * @param uploadId 分片上传ID
     */
    void abortMultipartUpload(String uploadId);

    /**
     * 删除OSS文件
     * 根据文件的相对路径从阿里云OSS中删除文件
     *
     * @param relativePath 文件的相对路径
     * @return 删除是否成功
     */
    boolean deleteFile(String relativePath);
}
