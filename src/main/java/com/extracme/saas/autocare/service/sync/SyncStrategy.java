package com.extracme.saas.autocare.service.sync;

import com.extracme.saas.autocare.model.dto.SyncDataResultDTO;

/**
 * 数据同步策略接口
 */
public interface SyncStrategy {

    /**
     * 获取支持的表名
     * 
     * @return 表名
     */
    String getSupportedTable();

    /**
     * 同步数据（自动判断INSERT或UPDATE操作）
     *
     * @param dataIdentifier 数据标识（用于日志记录）
     * @param dataObject 数据对象（DTO）
     * @param tenantId 租户ID
     * @param tenantCode 租户编码
     * @return 同步结果
     */
    SyncDataResultDTO syncData(String dataIdentifier, Object dataObject, Long tenantId, String tenantCode);

    /**
     * 同步数据（自动判断INSERT或UPDATE操作）- 带操作人参数
     *
     * @param dataIdentifier 数据标识（用于日志记录）
     * @param dataObject 数据对象（DTO）
     * @param tenantId 租户ID
     * @param tenantCode 租户编码
     * @param operator 操作人
     * @return 同步结果
     */
    default SyncDataResultDTO syncData(String dataIdentifier, Object dataObject, Long tenantId, String tenantCode, String operator) {
        // 默认实现：调用原有方法，保持向后兼容
        return syncData(dataIdentifier, dataObject, tenantId, tenantCode);
    }
}
