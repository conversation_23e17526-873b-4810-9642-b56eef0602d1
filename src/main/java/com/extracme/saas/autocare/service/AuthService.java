package com.extracme.saas.autocare.service;

import java.util.List;

import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

/**
 * 身份认证服务接口
 */
public interface AuthService {

    /**
     * 通过短信验证码登录
     *
     * @param mobile 手机号
     * @param code 验证码
     * @return 登录成功返回token信息，失败返回null
     */
    TokenDTO loginByMobile(String mobile, String code);

    /**
     * 通过短信验证码登录（支持多租户）
     *
     * @param mobile 手机号
     * @param code 验证码
     * @param tenantId 租户ID（可选）
     * @return 登录成功返回token信息，失败抛出异常
     */
    TokenDTO loginByMobileWithTenant(String mobile, String code, Long tenantId);

    /**
     * 退出登录
     *
     * @param token 用户令牌
     * @return 是否成功退出
     */
    boolean logout(String token);

    /**
     * 发送登录验证码
     *
     * @param mobile 手机号
     * @param ipAddress 请求IP地址
     * @return 是否发送成功
     */
    boolean sendLoginCode(String mobile, String ipAddress);

    /**
     * 检查用户是否登录
     *
     * @param token 用户令牌
     * @return 是否已登录
     */
    boolean isLoggedIn(String token);

    /**
     * 验证token
     * @param token JWT token
     * @return 是否有效
     */
    boolean validateToken(String token);

    /**
     * 从token中获取用户ID
     * @param token JWT token
     * @return 用户ID
     */
    String getUserIdFromToken(String token);

    /**
     * 检查用户是否有指定权限
     *
     * @param token 用户令牌
     * @param permissionCode 权限编码
     * @return 是否有权限
     */
    boolean hasPermission(String token, String permissionCode);

    /**
     * 获取用户角色列表
     *
     * @param token 用户令牌
     * @return 角色列表
     */
    List<SysRole> getUserRoles(String token);

    /**
     * 获取用户权限列表
     *
     * @param token 用户令牌
     * @return 权限列表
     */
    List<SysPermission> getUserPermissions(String token);

    /**
     * 检查用户是否有指定角色
     *
     * @param token 用户令牌
     * @param roleCode 角色编码
     * @return 是否有角色
     */
    boolean hasRole(String token, String roleCode);

    /**
     * 根据手机号查询用户名下所有商户账号
     *
     * @param mobile 手机号
     * @return 商户账号列表，ID为租户ID，value为租户名称
     */
    List<ComboVO<String>> getUserTenantAccounts(String mobile);
}