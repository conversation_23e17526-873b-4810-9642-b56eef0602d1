package com.extracme.saas.autocare.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;
import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.service.AuthService;
import com.extracme.saas.autocare.service.SmsService;
import com.extracme.saas.autocare.util.JwtUtil;
import com.extracme.saas.autocare.util.OrgHierarchyUtils;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;

import lombok.RequiredArgsConstructor;

/**
 * 身份认证服务实现类
 */
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private static final Logger logger = LoggerFactory.getLogger(AuthServiceImpl.class);
    private static final String LOGIN_TYPE = "LOGIN";

    @Value("${jwt.expiration:86400}") // 默认24小时
    private int jwtExpiration;

    private final SmsService smsService;
    private final TableUserService userService;
    private final TableRoleService roleService;
    private final TablePermissionService permissionService;
    private final TableTenantService tenantService;
    private final TableUserOrgService userOrgService;
    private final OrgHierarchyUtils orgHierarchyUtils;
    private final JwtUtil jwtUtil;
    private final UserPermissionCacheUtils userPermissionCacheUtils;

    /**
     * 通过短信验证码登录
     *
     * @param mobile 手机号
     * @param code 验证码
     * @return 登录成功返回token信息，失败返回null
     */
    @Override
    @Transactional
    public TokenDTO loginByMobile(String mobile, String code) {
        // 1. 查询该手机号下的所有用户账号（包括不同租户下的账号）
        List<SysUser> allUsers = userService.findAllByMobile(mobile);
        if (allUsers.isEmpty()) {
            logger.warn("用户不存在, 手机号: {}", mobile);
            return null;
        }

        // 2. 检查账号状态：如果存在至少一个状态为正常的账号，则允许继续登录流程
        boolean hasActiveAccount = allUsers.stream()
            .anyMatch(user -> user.getStatus() != null && user.getStatus() == 1);

        if (!hasActiveAccount) {
            logger.warn("该手机号下所有账号都已禁用, 手机号: {}, 账号数量: {}", mobile, allUsers.size());
            return null;
        }

        // 3. 获取第一个启用的用户进行后续登录流程（保持原有逻辑兼容性）
        Optional<SysUser> userOpt = userService.findByMobile(mobile);
        if (!userOpt.isPresent()) {
            logger.warn("未找到启用的用户账号, 手机号: {}", mobile);
            return null;
        }

        SysUser user = userOpt.get();

        // 3. 验证验证码
        if (!smsService.verifyCode(mobile, code, LOGIN_TYPE)) {
            logger.warn("验证码校验失败, 手机号: {}", mobile);
            return null;
        }

        // 4. 校验租户状态和有效期
        try {
            validateTenantStatus(user.getTenantId());
        } catch (BusinessException e) {
            logger.warn("租户校验失败, 用户ID: {}, 手机号: {}, 错误: {}", user.getId(), mobile, e.getMessage());
            return null;
        }

        // 5. 标记验证码为已使用
        smsService.markCodeAsUsed(mobile, code, LOGIN_TYPE);

        // 6. 查询并设置租户编码
        String tenantCode = "default";
        String tenantName = "";
        if (user.getTenantId() != null) {
            try {
                SysTenant tenant = tenantService.selectById(user.getTenantId());
                if (tenant != null && tenant.getTenantCode() != null) {
                    tenantCode = tenant.getTenantCode();
                    tenantName = tenant.getTenantName();
                }
            } catch (Exception e) {
                logger.warn("查询租户编码失败，租户ID: {}, 错误: {}", user.getTenantId(), e.getMessage());
            }
        }

        // 7. 设置过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, jwtExpiration);
        Date expireTime = calendar.getTime();

        // 8. 构建完整用户信息并生成JWT Token
        JwtUserInfoDTO userInfo = buildJwtUserInfo(user, tenantCode, tenantName, expireTime);
        String token = jwtUtil.generateTokenWithUserInfo(userInfo);

        // 9. 构建返回结果
        TokenDTO tokenDTO = new TokenDTO();
        tokenDTO.setAccessToken(token);
        tokenDTO.setUserId(user.getId());
        tokenDTO.setUsername(user.getUsername());
        tokenDTO.setMobile(user.getMobile());
        tokenDTO.setTenantId(user.getTenantId());
        tokenDTO.setTenantCode(tenantCode);
        tokenDTO.setExpireTime(expireTime);

        logger.info("用户登录成功, ID: {}, 手机号: {}, 租户ID: {}, 租户编码: {}",
                   user.getId(), mobile, user.getTenantId(), tenantCode);
        return tokenDTO;
    }

    /**
     * 退出登录
     *
     * @param token 用户令牌
     * @return 是否成功退出
     */
    @Override
    public boolean logout(String token) {
        // 实际应用中，可以将token加入黑名单或从缓存中删除
        logger.info("用户退出登录, token: {}", token);
        return true;
    }

    /**
     * 发送登录验证码
     *
     * @param mobile 手机号
     * @param ipAddress 请求IP地址
     * @return 是否发送成功
     */
    @Override
    public boolean sendLoginCode(String mobile, String ipAddress) {
        // 1. 验证手机号是否为SaaS平台的注册账户
        List<SysUser> allUsers = userService.findAllByMobile(mobile);
        if (allUsers.isEmpty()) {
            logger.warn("手机号不是平台注册账户, 手机号: {}", mobile);
            throw new BusinessException("非平台账号！");
        }

        // 2. 发送验证码
        return smsService.sendLoginVerificationCode(mobile, ipAddress);
    }

    /**
     * 检查用户是否登录
     *
     * @param token 用户令牌
     * @return 是否已登录
     */
    @Override
    public boolean isLoggedIn(String token) {
        return token != null && !token.isEmpty() && jwtUtil.validateToken(token);
    }

    @Override
    public boolean validateToken(String token) {
        return jwtUtil.validateToken(token);
    }

    @Override
    public String getUserIdFromToken(String token) {
        return jwtUtil.getUserIdFromToken(token);
    }

    @Override
    public List<SysRole> getUserRoles(String token) {
        String userId = jwtUtil.getUserIdFromToken(token);
        return roleService.findByUserId(Long.parseLong(userId));
    }

    @Override
    public boolean hasRole(String token, String roleCode) {
        String userId = jwtUtil.getUserIdFromToken(token);
        return roleService.hasRole(Long.parseLong(userId), roleCode);
    }

    @Override
    public List<SysPermission> getUserPermissions(String token) {
        String userId = jwtUtil.getUserIdFromToken(token);
        Long userIdLong = Long.parseLong(userId);

        // 查询用户信息以判断账户类型
        SysUser user = userService.selectById(userIdLong);
        if (user == null) {
            logger.warn("根据用户ID未找到用户信息，userId: {}", userIdLong);
            return Collections.emptyList();
        }

        // 根据账户类型返回相应权限
        if (user.getAccountType() != null && user.getAccountType() == 0) {
            // 超级管理员：返回系统中所有可用权限
            List<SysPermission> allPermissions = permissionService.findAll();
            logger.debug("超级管理员用户 {} 获得所有权限，权限数量: {}", userIdLong, allPermissions.size());
            return allPermissions;
        } else {
            // 普通用户：通过角色关联查询权限
            List<SysPermission> userPermissions = permissionService.findByUserId(userIdLong);
            logger.debug("普通用户 {} 通过角色关联获得权限，权限数量: {}", userIdLong, userPermissions.size());
            return userPermissions;
        }
    }

    @Override
    public boolean hasPermission(String token, String permissionCode) {
        if (!validateToken(token)) {
            return false;
        }
        String userId = jwtUtil.getUserIdFromToken(token);
        Long userIdLong = Long.parseLong(userId);

        // 查询用户信息以判断账户类型
        SysUser user = userService.selectById(userIdLong);
        if (user == null) {
            logger.warn("根据用户ID未找到用户信息，userId: {}", userIdLong);
            return false;
        }

        // 超级管理员对所有权限检查都返回true
        if (user.getAccountType() != null && user.getAccountType() == 0) {
            logger.debug("超级管理员用户 {} 拥有权限: {}", userIdLong, permissionCode);
            return true;
        }

        // 普通用户：通过角色关联检查权限
        boolean hasPermission = permissionService.hasPermission(userIdLong, permissionCode);
        logger.debug("普通用户 {} 权限检查结果: {} -> {}", userIdLong, permissionCode, hasPermission);
        return hasPermission;
    }

    /**
     * 通过短信验证码登录（支持多租户）
     *
     * @param mobile 手机号
     * @param code 验证码
     * @param tenantId 租户ID（可选）
     * @return 登录成功返回token信息，失败抛出异常
     */
    @Override
    @Transactional
    public TokenDTO loginByMobileWithTenant(String mobile, String code, Long tenantId) {
        logger.debug("开始多租户登录验证, 手机号: {}, 租户ID: {}", mobile, tenantId);

        // 1. 验证验证码
        if (!smsService.verifyCode(mobile, code, LOGIN_TYPE)) {
            logger.warn("验证码校验失败, 手机号: {}", mobile);
            throw new BusinessException(ErrorCode.USERNAME_OR_PASSWORD_ERROR);
        }

        // 2. 先进行用户基础验证：查询该手机号下的所有用户账号（包括不同租户下的账号）
        List<SysUser> allUsers = userService.findAllByMobile(mobile);
        if (allUsers.isEmpty()) {
            logger.warn("用户不存在, 手机号: {}", mobile);
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }

        // 3. 验证账号状态：如果存在至少一个状态为正常的账号，则允许继续登录流程
        boolean hasActiveAccount = allUsers.stream()
            .anyMatch(user -> user.getStatus() != null && user.getStatus() == 1);

        if (!hasActiveAccount) {
            logger.warn("该手机号下所有账号都已禁用, 手机号: {}, 账号数量: {}", mobile, allUsers.size());
            throw new BusinessException(ErrorCode.ACCOUNT_LOCKED);
        }

        // 4. 再进行租户权限验证：查询用户关联的所有租户ID（只查询启用的用户）
        List<Long> tenantIds = userService.findTenantIdsByMobile(mobile);
        logger.debug("用户[{}]关联的租户ID列表: {}", mobile, tenantIds);

        if (tenantIds.isEmpty()) {
            logger.warn("用户未关联任何商户账号, 手机号: {}", mobile);
            throw new BusinessException(ErrorCode.NO_TENANT_ACCOUNTS);
        }

        // 5. 处理多租户账号的选择逻辑
        SysUser finalUser;
        if (tenantId != null) {
            // 5a. 如果指定了租户ID，验证用户是否有权限访问该租户
            if (!tenantIds.contains(tenantId)) {
                logger.warn("用户无权限访问指定租户, 手机号: {}, 租户ID: {}", mobile, tenantId);
                throw new BusinessException(ErrorCode.TENANT_ACCESS_DENIED);
            }

            // 查询指定租户下的用户
            Optional<SysUser> tenantUserOpt = userService.findByMobileAndTenantId(mobile, tenantId);
            if (!tenantUserOpt.isPresent()) {
                logger.warn("指定租户下用户不存在, 手机号: {}, 租户ID: {}", mobile, tenantId);
                throw new BusinessException(ErrorCode.USER_NOT_FOUND);
            }
            finalUser = tenantUserOpt.get();
        } else {
            // 5b. 如果未指定租户ID，检查用户是否有多个商户账号
            if (tenantIds.size() > 1) {
                logger.warn("用户存在多个商户账号，需要选择具体租户, 手机号: {}, 租户数量: {}", mobile, tenantIds.size());
                throw new BusinessException(ErrorCode.MULTIPLE_TENANT_ACCOUNTS);
            }

            // 只有一个商户账号，自动登录
            Long singleTenantId = tenantIds.get(0);
            Optional<SysUser> tenantUserOpt = userService.findByMobileAndTenantId(mobile, singleTenantId);
            if (!tenantUserOpt.isPresent()) {
                logger.warn("用户不存在, 手机号: {}, 租户ID: {}", mobile, singleTenantId);
                throw new BusinessException(ErrorCode.USER_NOT_FOUND);
            }
            finalUser = tenantUserOpt.get();
        }

        // 6. 校验租户状态和有效期
        validateTenantStatus(finalUser.getTenantId());

        // 7. 标记验证码为已使用
        smsService.markCodeAsUsed(mobile, code, LOGIN_TYPE);

        // 8. 查询并设置租户编码
        String tenantCode = "default";
        String tenantName = "";
        if (finalUser.getTenantId() != null) {
            try {
                SysTenant tenant = tenantService.selectById(finalUser.getTenantId());
                if (tenant != null && tenant.getTenantCode() != null) {
                    tenantCode = tenant.getTenantCode();
                    tenantName = tenant.getTenantName();
                }
            } catch (Exception e) {
                logger.warn("查询租户编码失败，租户ID: {}, 错误: {}", finalUser.getTenantId(), e.getMessage());
            }
        }

        // 9. 设置过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, jwtExpiration);
        Date expireTime = calendar.getTime();

        // 10. 构建完整用户信息并生成JWT Token
        JwtUserInfoDTO userInfo = buildJwtUserInfo(finalUser, tenantCode, tenantName, expireTime);
        String token = jwtUtil.generateTokenWithUserInfo(userInfo);

        // 11. 构建返回结果
        TokenDTO tokenDTO = new TokenDTO();
        tokenDTO.setAccessToken(token);
        tokenDTO.setUserId(finalUser.getId());
        tokenDTO.setUsername(finalUser.getUsername());
        tokenDTO.setMobile(finalUser.getMobile());
        tokenDTO.setTenantId(finalUser.getTenantId());
        tokenDTO.setTenantCode(tenantCode);
        tokenDTO.setExpireTime(expireTime);

        logger.info("用户多租户登录成功, ID: {}, 手机号: {}, 租户ID: {}, 租户编码: {}",
                   finalUser.getId(), mobile, finalUser.getTenantId(), tenantCode);
        return tokenDTO;
    }

    /**
     * 根据手机号查询用户名下所有商户账号
     *
     * @param mobile 手机号
     * @return 商户账号列表，ID为租户ID，value为租户名称
     */
    @Override
    public List<ComboVO<String>> getUserTenantAccounts(String mobile) {
        logger.debug("查询用户商户账号列表, 手机号: {}", mobile);

        // 1. 查询用户关联的所有租户ID
        List<Long> tenantIds = userService.findTenantIdsByMobile(mobile);
        if (tenantIds.isEmpty()) {
            logger.debug("用户未关联任何商户账号, 手机号: {}", mobile);
            return new ArrayList<>();
        }

        // 2. 查询租户信息并构建下拉列表
        List<ComboVO<String>> result = new ArrayList<>();
        Date currentTime = new Date();
        for (Long tenantId : tenantIds) {
            SysTenant tenant = tenantService.selectById(tenantId);
            if (tenant != null && tenant.getStatus() == 1) { // 只返回启用的租户
                // 检查租户有效期
                boolean isExpired = tenant.getExpireTime() != null && currentTime.after(tenant.getExpireTime());
                if (!isExpired) { // 只返回未过期的租户
                    ComboVO<String> combo = new ComboVO<>();
                    combo.setId(String.valueOf(tenant.getId()));
                    combo.setValue(tenant.getTenantName());
                    result.add(combo);
                } else {
                    logger.debug("租户已过期，不返回给用户选择，租户ID: {}, 租户名称: {}, 过期时间: {}",
                               tenantId, tenant.getTenantName(), tenant.getExpireTime());
                }
            }
        }

        logger.debug("用户[{}]的商户账号列表查询完成, 共{}个账号", mobile, result.size());
        return result;
    }

    /**
     * 校验租户状态和有效期
     *
     * @param tenantId 租户ID
     * @throws BusinessException 如果租户状态异常或已过期
     */
    private void validateTenantStatus(Long tenantId) {
        if (tenantId == null) {
            logger.warn("租户ID为空，跳过租户校验");
            return;
        }

        try {
            SysTenant tenant = tenantService.selectById(tenantId);
            if (tenant == null) {
                logger.warn("租户不存在，租户ID: {}", tenantId);
                throw new BusinessException(ErrorCode.TENANT_NOT_FOUND);
            }

            // 检查租户状态
            if (tenant.getStatus() == null || tenant.getStatus() != 1) {
                logger.warn("租户状态异常，租户ID: {}, 状态: {}", tenantId, tenant.getStatus());
                throw new BusinessException(ErrorCode.TENANT_DISABLED);
            }

            // 检查租户有效期
            if (tenant.getExpireTime() != null) {
                Date currentTime = new Date();
                if (currentTime.after(tenant.getExpireTime())) {
                    logger.warn("租户已过期，租户ID: {}, 过期时间: {}, 当前时间: {}",
                               tenantId, tenant.getExpireTime(), currentTime);
                    throw new BusinessException(ErrorCode.TENANT_EXPIRED);
                }
            }

            logger.debug("租户校验通过，租户ID: {}, 租户名称: {}", tenantId, tenant.getTenantName());
        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            logger.error("租户校验过程中发生异常，租户ID: {}", tenantId, e);
            throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR, "租户校验失败");
        }
    }

    /**
     * 构建完整的用户信息DTO，用于存储到JWT token中
     *
     * @param user 用户实体
     * @param tenantCode 租户编码
     * @param tenantName 租户名称
     * @param expireTime 过期时间
     * @return 用户信息DTO
     */
    private JwtUserInfoDTO buildJwtUserInfo(SysUser user, String tenantCode, String tenantName, Date expireTime) {
        try {
            logger.debug("开始构建用户JWT信息，用户ID: {}", user.getId());

            JwtUserInfoDTO userInfo = new JwtUserInfoDTO();

            // 设置用户基本信息
            userInfo.setUserId(user.getId());
            userInfo.setUsername(user.getUsername());
            userInfo.setNickname(user.getNickname());
            // 直接存储完整手机号，支持业务功能正常使用
            userInfo.setMobile(user.getMobile());
            // 移除邮箱字段以减少token大小和提高安全性
            userInfo.setAccountType(user.getAccountType());
            userInfo.setApprovalLevel(user.getApprovalLevel());
            userInfo.setStatus(user.getStatus());
            // 设置关联修理厂ID
            userInfo.setRepairDepotId(user.getRepairDepotId());
            // 设置保险公司ID
            userInfo.setInsuranceCompanyId(user.getInsuranceCompanyId());
            // 移除创建时间和更新时间以减少token大小

            // 设置租户信息
            userInfo.setTenantId(user.getTenantId());
            userInfo.setTenantCode(tenantCode);
            userInfo.setTenantName(tenantName);

            // 设置时间信息（使用时间戳）
            userInfo.setLoginTime(System.currentTimeMillis());
            userInfo.setExpireTime(expireTime != null ? expireTime.getTime() : null);

            // 查询用户权限信息并缓存到Redis
            Set<String> permissions;
            if (user.getAccountType() != null && user.getAccountType() == 0) {
                // 超级管理员：自动获得系统中所有可用权限
                List<SysPermission> allPermissions = permissionService.findAll();
                permissions = allPermissions.stream()
                    .map(SysPermission::getPermissionCode)
                    .collect(Collectors.toSet());
                logger.debug("超级管理员用户 {} 自动获得所有权限，权限数量: {}", user.getId(), permissions.size());
            } else {
                // 普通用户：通过角色关联查询权限
                List<SysPermission> permissionList = permissionService.findByUserId(user.getId());
                permissions = permissionList.stream()
                    .map(SysPermission::getPermissionCode)
                    .collect(Collectors.toSet());
                logger.debug("普通用户 {} 通过角色关联获得权限，权限数量: {}", user.getId(), permissions.size());
            }

            // 查询用户关联的组织列表
            List<String> orgIds = Collections.emptyList();
            try {
                orgIds = userOrgService.findOrgIdsByUserId(user.getId());
            } catch (Exception e) {
                logger.warn("查询用户组织关联失败，用户ID: {}", user.getId(), e);
            }

            // 计算用户可访问的所有组织ID列表（包括子组织）
            List<String> allAccessibleOrgIds = Collections.emptyList();
            try {
                if (!orgIds.isEmpty()) {
                    // 临时设置租户上下文，确保组织层级查询限制在当前租户范围内
                    Long currentTenantId = TenantContextHolder.getTenantId();
                    boolean needSetTenantContext = (currentTenantId == null || !currentTenantId.equals(user.getTenantId()));

                    if (needSetTenantContext) {
                        logger.debug("临时设置租户上下文，租户ID: {}", user.getTenantId());
                        TenantContextHolder.setTenant(user.getTenantId());
                    }

                    try {
                        allAccessibleOrgIds = orgHierarchyUtils.calculateAllAccessibleOrgIds(orgIds);
                        logger.debug("用户 {} 的组织权限计算完成，直接关联组织: {}, 可访问组织总数: {}",
                                   user.getId(), orgIds.size(), allAccessibleOrgIds.size());
                    } finally {
                        // 如果是临时设置的租户上下文，需要恢复原状态
                        if (needSetTenantContext) {
                            if (currentTenantId != null) {
                                TenantContextHolder.setTenant(currentTenantId);
                                logger.debug("恢复租户上下文，租户ID: {}", currentTenantId);
                            } else {
                                TenantContextHolder.clear();
                                logger.debug("清理租户上下文");
                            }
                        }
                    }
                } else {
                    allAccessibleOrgIds = new ArrayList<>(orgIds);
                }
            } catch (Exception e) {
                logger.warn("计算用户可访问组织列表失败，用户ID: {}, 直接关联组织: {}",
                           user.getId(), orgIds, e);
                allAccessibleOrgIds = new ArrayList<>(orgIds);
            }

            // 将权限信息缓存到Redis，而不是存储到JWT中
            try {
                boolean cacheSuccess = userPermissionCacheUtils.cacheUserPermissions(
                    user.getTenantId(), user.getId(), permissions, orgIds, allAccessibleOrgIds);

                if (cacheSuccess) {
                    logger.debug("用户权限信息缓存成功，用户ID: {}, 权限数量: {}, 直接关联组织数: {}, 可访问组织总数: {}",
                                user.getId(), permissions.size(), orgIds.size(), allAccessibleOrgIds.size());
                } else {
                    logger.warn("用户权限信息缓存失败，用户ID: {}", user.getId());
                }
            } catch (Exception e) {
                logger.error("缓存用户权限信息异常，用户ID: {}", user.getId(), e);
            }

            logger.debug("用户JWT信息构建完成（权限信息已缓存到Redis），用户ID: {}, 权限数量: {}, 直接关联组织数: {}, 可访问组织总数: {}",
                        user.getId(), permissions.size(), orgIds.size(), allAccessibleOrgIds.size());

            return userInfo;
        } catch (Exception e) {
            logger.error("构建用户JWT信息失败，用户ID: {}", user.getId(), e);
            throw new RuntimeException("构建用户信息失败", e);
        }
    }
}