package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.RepairDepotCreateDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotUpdateStatusDTO;
import com.extracme.saas.autocare.model.vo.RepairDepotDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairDepotListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

import java.util.List;

/**
 * 修理厂服务接口
 */
public interface RepairDepotService {

    /**
     * 分页查询列表
     *
     * @param queryDTO 查询参数
     * @return 维修厂列表
     */
    BasePageVO<RepairDepotListVO> queryRepairDepotList(RepairDepotQueryDTO queryDTO);

    /**
     * 创建任务-查询维修厂列表
     *
     * @param queryDTO 查询参数
     * @return 维修厂列表
     */
    BasePageVO<RepairDepotListVO> queryCheckRepairDepotList(RepairDepotQueryDTO queryDTO);

    /**
     * 获取修理厂详情
     *
     * @param id 维修厂ID
     * @return 维修厂详情
     */
    RepairDepotDetailsVO getRepairDepotDetails(Long id);

    /**
     * 获取修理厂详情
     *
     * @param repairDepotId 维修厂编码
     * @return 维修厂详情
     */
    RepairDepotDetailsVO getRepairDepotDetailsByRepairDepotId(String repairDepotId);

    /**
     * 创建维修厂
     *
     * @param createDTO 创建参数
     * @return 维修厂ID
     */
    void createRepairDepot(RepairDepotCreateDTO createDTO);

    /**
     * 修改修理厂
     *
     * @param updateDTO 修改参数
     */
    void updateRepairDepot(RepairDepotUpdateDTO updateDTO);

    /**
     * 删除修理厂
     *
     * @param id 维修厂ID
     */
    void deleteRepairDepot(Long id);

    /**
     * 更新修理厂状态
     *
     * @param updateStatusDTO 更新参数
     */
    void updateRepairDepotStatus(RepairDepotUpdateStatusDTO updateStatusDTO);


    /**
     * 获取修理厂下拉列表
     * @return 修理厂下拉数据列表，ID类型为String
     */
    List<ComboVO<String>> getRepairDepotCombo();

    /**
     * 获取修理厂下拉列表
     * @return 修理厂下拉数据列表，ID类型为String
     */
    List<ComboVO<String>> getAllRepairDepotCombo();

    /**
     * 根据租户ID获取修理厂下拉列表
     * 如果租户ID为空，则使用当前登录用户的租户ID
     *
     * @param tenantId 租户ID，可为空
     * @return 修理厂下拉数据列表，ID类型为String
     */
    List<ComboVO<String>> getRepairDepotComboByTenantId(Long tenantId);
}