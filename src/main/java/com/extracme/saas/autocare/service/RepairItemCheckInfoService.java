package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.RepairItemCheckInfoDTO;

import java.util.List;

/**
 * 维修自有配件库服务接口
 *
 * <AUTHOR>
 * @date 2025/05/21
 */
public interface RepairItemCheckInfoService {

    /**
     * 添加维修项目核损信息
     *
     * @param taskNo 任务编号
     * @param itemCheckInfoList 维修项目核损信息列表
     * @return 添加的记录数
     */
    int addItemCheckInfo(String taskNo, List<RepairItemCheckInfoDTO> itemCheckInfoList);

    /**
     * 添加定损项目记录
     *
     * @param taskNo 任务编号
     * @param itemCheckInfoList 维修项目核损信息列表
     * @return 添加的记录数
     */
    int addItemCheckInfoQuote(String taskNo, List<RepairItemCheckInfoDTO> itemCheckInfoList);

    /**
     * 添加预审项目记录
     *
     * @param taskNo 任务编号
     * @param itemCheckInfoList 维修项目核损信息列表
     * @return 添加的记录数
     */
    int addItemCheckInfoPreReview(String taskNo, List<RepairItemCheckInfoDTO> itemCheckInfoList);

    /**
     * 更新维修项目核损状态（删除项目）
     *
     * @param id 维修项目核损信息ID
     * @return 更新的记录数
     */
    int updateCheckInfoStatus(Long id);

    /**
     * 批量更新维修项目核损状态
     *
     * @param taskNo 任务编号
     * @param insurancePreReviewStatus 预审状态（0非进保预审 1进保预审）
     * @return 更新的记录数
     */
    int updateStatusAll(String taskNo, Integer insurancePreReviewStatus);

    /**
     * 查询预审配件列表
     *
     * @param taskNo 任务编号
     * @return 预审配件列表
     */
    List<RepairItemCheckInfoDTO> queryPreReviewListByTaskNo(String taskNo);

    /**
     * 查询预审配件列表总数
     *
     * @param taskNo 任务编号
     * @return 预审配件列表总数
     */
    Integer countPreReviewListByTaskNo(String taskNo);

    /**
     * 查询定损配件列表
     *
     * @param taskNo 任务编号
     * @return 定损配件列表
     */
    List<RepairItemCheckInfoDTO> queryCheckListByTaskNo(String taskNo);

    /**
     * 查询核损配件列表
     *
     * @param taskNo 任务编号
     * @param orgId 组织ID
     * @return 核损配件列表
     */
    List<RepairItemCheckInfoDTO> queryViewListByTaskNo(String taskNo, String orgId);

    /**
     * 更新定核损数据
     *
     * @param taskNo 任务编号
     * @param itemCheckInfoList 维修项目核损信息列表
     * @return 更新的记录数
     */
    int updateItemCheckInfo(String taskNo, List<RepairItemCheckInfoDTO> itemCheckInfoList);

    /**
     * 查询任务列表的明细项
     *
     * @param taskNoList 任务编号列表
     * @return 维修项目核损信息列表
     */
    List<RepairItemCheckInfoDTO> queryCheckListByTaskNoList(List<String> taskNoList);

    /**
     * 查询配件列表
     *
     * @param taskNo 任务编号
     * @param insurancePreReviewStatus 预审状态（0非进保预审 1进保预审）
     * @return 配件列表
     */
    List<RepairItemCheckInfoDTO> queryCheckListByTaskNoAndInsurancePreReviewStatus(String taskNo, Integer insurancePreReviewStatus);
}