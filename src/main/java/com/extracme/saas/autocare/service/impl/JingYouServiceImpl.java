package com.extracme.saas.autocare.service.impl;

import com.alibaba.fastjson.JSON;
import com.extracme.saas.autocare.config.SystemConfig;
import com.extracme.saas.autocare.enums.ActivityDefinitionEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.dto.JingYouLossNotify;
import com.extracme.saas.autocare.model.dto.LossAssistInfoDTO;
import com.extracme.saas.autocare.model.dto.LossFitInfoDTO;
import com.extracme.saas.autocare.model.dto.LossRepairInfoDTO;
import com.extracme.saas.autocare.model.entity.*;
import com.extracme.saas.autocare.model.jingyou.*;
import com.extracme.saas.autocare.model.vo.LossDetailVO;
import com.extracme.saas.autocare.repository.*;
import com.extracme.saas.autocare.service.JingYouService;
import com.extracme.saas.autocare.util.HttpUtils;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.util.XmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.bind.JAXB;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 精友报价服务实现类
 */
@Slf4j
@Service
public class JingYouServiceImpl implements JingYouService {

    @Autowired
    private SystemConfig systemConfig;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableLossInfoService tableLossInfoService;

    @Autowired
    private TableLossFitInfoService tableLossFitInfoService;

    @Autowired
    private TableLossOuterRepairInfoService tableLossOuterRepairInfoService;

    @Autowired
    private TableLossRepairSumInfoService tableLossRepairSumInfoService;

    @Autowired
    private TableLossRepairInfoService tableLossRepairInfoService;

    @Autowired
    private TableCollisionPartsService tableCollisionPartsService;

    @Autowired
    private TableLossAssistInfoService tableLossAssistInfoService;

    @Autowired
    private CarDamageRequestService carDamageRequestService;

    /**
     * 安全地将Object转换为Map<String, String>
     * 
     * @param obj       要转换的对象
     * @param fieldName 字段名称，用于错误信息
     * @return 转换后的Map
     * @throws BusinessException 如果转换失败
     */
    @SuppressWarnings("unchecked")
    private Map<String, String> safeMapCast(Object obj, String fieldName) {
        if (obj instanceof Map) {
            return (Map<String, String>) obj;
        }
        throw new BusinessException("调用精友系统失败：" + fieldName + "数据格式错误");
    }

    /**
     * 是否进行精友报价
     * 
     * @param taskNo
     * @return
     */
    public boolean hasLossAssessmentData(String taskNo) {
        // 获取损失信息列表
        List<MtcLossInfo> mtcLossInfoList = tableLossInfoService.selectByTaskNo(taskNo);
        if (mtcLossInfoList == null || mtcLossInfoList.isEmpty()) {
            return false;
        }
        return true;
    }

    @Override
    public String lossAssessment(Long id) {
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectById(id);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务");
        }

        // 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + repairTask.getTaskNo());
        }
        if (!workflowInstance.getCurrentActivityCode().equals("REPAIR_QUOTATION")
                && !workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new BusinessException("维修任务节点状态不是处理中, 无法保存报价信息: " + repairTask.getTaskNo());
        }

        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("001");
        headBean.setOperatingTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // PACKET > BODY >ReqInfo
        ReqInfoAssessment reqInfoAssessment = new ReqInfoAssessment();
        // 判断初次定损还是核损退回定损
        List<MtcLossInfo> mtcLossInfoList = tableLossInfoService.selectByTaskNo(repairTask.getTaskNo());
        for (MtcLossInfo mtcLossInfo : mtcLossInfoList) {
            if (StringUtils.isNotBlank(mtcLossInfo.getAuditHandlerCode())) {
                reqInfoAssessment.setAuditLossFlag("1");
                break;
            }
        }
        reqInfoAssessment.setReturnURL(systemConfig.getAssessmentBackURL());
        String refreshUrl = StringUtils.replace(systemConfig.getAssessmentRefreshURL(), "idReplace", id.toString());
        reqInfoAssessment.setRefreshURL(refreshUrl);

        // PACKET > BODY >EvalLossInfoAssessment
        EvalLossInfoAssessment evalLossInfoAssessment = carDamageRequestService.queryEvalLossInfoAssessment(id);
        
        String orgId = "00";
        String orgName = "环球";
        evalLossInfoAssessment.setComCode(orgId);
        evalLossInfoAssessment.setCompany(orgName);
        evalLossInfoAssessment.setBranchComCode(orgId);
        evalLossInfoAssessment.setBranchComName(orgName);
        // PACKET >BODY>FactoryInfo
        FactoryInfo factoryInfo = carDamageRequestService.queryFactoryInfo(id);
        factoryInfo.setComName("上海挚极");
        if (StringUtils.equals("A", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("1");
            factoryInfo.setFactoryQualification("1");
        } else if (StringUtils.equals("B", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("0");
            factoryInfo.setFactoryQualification("1");
            factoryInfo.setFitsDiscountRate("0.9");
        } else if (StringUtils.equals("C", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("0");
            factoryInfo.setFactoryQualification("2");
            factoryInfo.setFitsDiscountRate("0.9");
        } else if (StringUtils.equals("D", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("0");
            factoryInfo.setFactoryQualification("2");
            factoryInfo.setFitsDiscountRate("0.85");
        } else {
            factoryInfo.setFactoryType("");
            factoryInfo.setFactoryQualification("");
        }
        // PACKET >BODY>FactoryBrand
        FactoryBrand factoryBrand = new FactoryBrand();
        List<FactoryBrand> factoryBrandList = new ArrayList<>();
        factoryBrandList.add(factoryBrand);
        // PACKET > BODY >LossPolicy
        LossPolicy lossPolicy = carDamageRequestService.queryLossPolicy(id);
        List<LossPolicy> lossPolicyList = new ArrayList<>();
        lossPolicyList.add(lossPolicy);
        // PACKET > BODY >LossCoverVehicle
        LossCoverVehicle lossCoverVehicle = carDamageRequestService.queryLossCoverVehicle(id);
        // PACKET > BODY >LossInsured
        LossInsured lossInsured = carDamageRequestService.queryLossInsured(id);
        List<LossInsured> lossInsuredList = new ArrayList<>();
        lossInsuredList.add(lossInsured);
        // PACKET > BODY >LossReporting
        LossReporting lossReporting = carDamageRequestService.queryLossReporting(id);
        // PACKET > BODY
        BodyBeanAssessment bodyBeanAssessment = new BodyBeanAssessment();
        bodyBeanAssessment.setReqInfoAssessment(reqInfoAssessment);
        bodyBeanAssessment.setEvalLossInfoAssessment(evalLossInfoAssessment);
        bodyBeanAssessment.setFactoryInfo(factoryInfo);
        bodyBeanAssessment.setFactoryBrandList(factoryBrandList);
        bodyBeanAssessment.setLossPolicyList(lossPolicyList);
        bodyBeanAssessment.setLossCoverVehicle(lossCoverVehicle);
        bodyBeanAssessment.setLossInsuredList(lossInsuredList);
        bodyBeanAssessment.setLossReporting(lossReporting);
        // PACKET
        PacketBeanAssessment packetBean = new PacketBeanAssessment();
        packetBean.setHeadBean(headBean);
        packetBean.setBodyBeanAssessment(bodyBeanAssessment);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBean, stringWriter);
        log.info("精友定损报价请求报文: {}", stringWriter);
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), systemConfig.getJyInterfaceUrl() + "001");

        // 解析响应
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error("{}定损报价返回为空", repairTask.getTaskNo());
            throw new BusinessException("调用精友系统失败");
        }

        Map<String, String> headMap = safeMapCast(resultMap.get("HEAD"), "HEAD");
        Map<String, String> bodyMap = safeMapCast(resultMap.get("BODY"), "BODY");

        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error("{}定损报价返回错误: {}", repairTask.getTaskNo(), JSON.toJSONString(resultMap));
            throw new BusinessException("调用精友系统失败: " + headMap.get("ResponseMessage"));
        }

        return bodyMap.get("URL");
    }

    @Override
    public String lossAssessment(Long id, String parameterString) {
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectById(id);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务");
        }

        // 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + repairTask.getTaskNo());
        }
        if (!workflowInstance.getCurrentActivityCode().equals("REPAIR_QUOTATION")
                && !workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new BusinessException("维修任务节点状态不是处理中, 无法保存报价信息: " + repairTask.getTaskNo());
        }
        
        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("001");
        headBean.setOperatingTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // PACKET > BODY >ReqInfo
        ReqInfoAssessment reqInfoAssessment = new ReqInfoAssessment();
        // 判断初次定损还是核损退回定损
        List<MtcLossInfo> mtcLossInfoList = tableLossInfoService.selectByTaskNo(repairTask.getTaskNo());
        for (MtcLossInfo mtcLossInfo : mtcLossInfoList) {
            if (StringUtils.isNotBlank(mtcLossInfo.getAuditHandlerCode())) {
                reqInfoAssessment.setAuditLossFlag("1");
                break;
            }
        }
        reqInfoAssessment.setReturnURL(systemConfig.getAssessmentBackURL());
        String refreshUrl = StringUtils.replace(systemConfig.getAssessmentRefreshURL(), "idReplace", id.toString());
        reqInfoAssessment.setRefreshURL(refreshUrl);
        // PACKET > BODY >EvalLossInfoAssessment
        EvalLossInfoAssessment evalLossInfoAssessment = carDamageRequestService.queryEvalLossInfoAssessment(id);
        String orgId = "00";
        String orgName = "环球";
        evalLossInfoAssessment.setComCode(orgId);
        evalLossInfoAssessment.setCompany(orgName);
        evalLossInfoAssessment.setBranchComCode(orgId);
        evalLossInfoAssessment.setBranchComName(orgName);
        // PACKET >BODY>FactoryInfo
        FactoryInfo factoryInfo = carDamageRequestService.queryFactoryInfo(id);
        factoryInfo.setComName("上海挚极");
        if (StringUtils.equals("A", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("1");
            factoryInfo.setFactoryQualification("1");
        } else if (StringUtils.equals("B", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("0");
            factoryInfo.setFactoryQualification("1");
            factoryInfo.setFitsDiscountRate("0.9");
        } else if (StringUtils.equals("C", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("0");
            factoryInfo.setFactoryQualification("2");
            factoryInfo.setFitsDiscountRate("0.9");
        } else if (StringUtils.equals("D", factoryInfo.getFactoryQualification())) {
            factoryInfo.setFactoryType("0");
            factoryInfo.setFactoryQualification("2");
            factoryInfo.setFitsDiscountRate("0.85");
        } else {
            factoryInfo.setFactoryType("");
            factoryInfo.setFactoryQualification("");
        }
        // PACKET >BODY>FactoryBrand
        FactoryBrand factoryBrand = new FactoryBrand();
        List<FactoryBrand> factoryBrandList = new ArrayList<>();
        factoryBrandList.add(factoryBrand);
        // PACKET > BODY >LossPolicy
        LossPolicy lossPolicy = carDamageRequestService.queryLossPolicy(id);
        List<LossPolicy> lossPolicyList = new ArrayList<>();
        lossPolicyList.add(lossPolicy);
        // PACKET > BODY >LossCoverVehicle
        LossCoverVehicle lossCoverVehicle = carDamageRequestService.queryLossCoverVehicle(id);
        // PACKET > BODY >LossInsured
        LossInsured lossInsured = carDamageRequestService.queryLossInsured(id);
        List<LossInsured> lossInsuredList = new ArrayList<>();
        lossInsuredList.add(lossInsured);
        // PACKET > BODY >LossReporting
        LossReporting lossReporting = carDamageRequestService.queryLossReporting(id);
        // PACKET > BODY
        BodyBeanAssessment bodyBeanAssessment = new BodyBeanAssessment();
        bodyBeanAssessment.setReqInfoAssessment(reqInfoAssessment);
        bodyBeanAssessment.setEvalLossInfoAssessment(evalLossInfoAssessment);
        bodyBeanAssessment.setFactoryInfo(factoryInfo);
        bodyBeanAssessment.setFactoryBrandList(factoryBrandList);
        bodyBeanAssessment.setLossPolicyList(lossPolicyList);
        bodyBeanAssessment.setLossCoverVehicle(lossCoverVehicle);
        bodyBeanAssessment.setLossInsuredList(lossInsuredList);
        bodyBeanAssessment.setLossReporting(lossReporting);
        // PACKET
        PacketBeanAssessment packetBean = new PacketBeanAssessment();
        packetBean.setHeadBean(headBean);
        packetBean.setBodyBeanAssessment(bodyBeanAssessment);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBean, stringWriter);
        log.info("精友核损报价请求报文: {}", stringWriter);
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), systemConfig.getJyInterfaceUrl() + "001");

        // 解析响应
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error("{}定损报价返回为空", repairTask.getTaskNo());
            throw new BusinessException("调用精友系统失败");
        }

        Map<String, String> headMap = safeMapCast(resultMap.get("HEAD"), "HEAD");
        Map<String, String> bodyMap = safeMapCast(resultMap.get("BODY"), "BODY");

        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error("{}定损报价返回错误: {}", repairTask.getTaskNo(), JSON.toJSONString(resultMap));
            throw new BusinessException("调用精友系统失败: " + headMap.get("ResponseMessage"));
        }

        return bodyMap.get("URL");
    }

    @Override
    public String evaluateLoss(Long id) {
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectById(id);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务");
        }

        // 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + repairTask.getTaskNo());
        }
        if (!workflowInstance.getCurrentActivityCode().equals("LOSS_ASSESSMENT")
                && !workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new BusinessException("维修任务节点状态不是处理中, 无法进行精友核价信息: " + repairTask.getTaskNo());
        }

        // 检查是否存在定损数据
        if (!hasLossAssessmentData(repairTask.getTaskNo())) {
            throw new BusinessException("未经过定损系统维修报价，请退回后重新定损");
        }

        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("005");
        headBean.setOperatingTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // PACKET > BODY >ReqInfo
        ReqInfoEvaluate reqInfoEvaluate = new ReqInfoEvaluate();
        reqInfoEvaluate.setReturnURL(systemConfig.getEvaluateBackURL());
        String refreshUrl = systemConfig.getEvaluateRefreshURL().replace("idReplace", id.toString());
        reqInfoEvaluate.setRefreshURL(refreshUrl);
        // PACKET > BODY >EvalLossInfo
        EvalLossInfoEvaluate evalLossInfoEvaluate = carDamageRequestService.queryEvalLossInfoEvaluate(id);
        
        String orgId = "00";
        String orgName = "环球";
        evalLossInfoEvaluate.setApprComCode(orgId);
        evalLossInfoEvaluate.setApprCompany(orgName);
        evalLossInfoEvaluate.setApprBranchComCode(orgId);
        evalLossInfoEvaluate.setApprBranchComName(orgName);
        evalLossInfoEvaluate.setApprHandlerCode(orgId);
        evalLossInfoEvaluate.setApprHandlerName(orgName);
        // PACKET > BODY
        BodyBeanEvaluate bodyBeanEvaluate = new BodyBeanEvaluate();
        bodyBeanEvaluate.setReqInfoEvaluate(reqInfoEvaluate);
        bodyBeanEvaluate.setEvalLossInfoEvaluate(evalLossInfoEvaluate);
        // PACKET
        PacketBeanEvaluate packetBeanEvaluate = new PacketBeanEvaluate();
        packetBeanEvaluate.setHeadBean(headBean);
        packetBeanEvaluate.setBodyBeanEvaluate(bodyBeanEvaluate);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBeanEvaluate, stringWriter);
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), systemConfig.getJyInterfaceUrl() + "005");

        // 解析响应
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error("{}核损核价返回为空", repairTask.getTaskNo());
            throw new BusinessException("调用精友系统失败");
        }

        Map<String, String> headMap = safeMapCast(resultMap.get("HEAD"), "HEAD");
        Map<String, String> bodyMap = safeMapCast(resultMap.get("BODY"), "BODY");

        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error("{}核损核价返回错误: {}", repairTask.getTaskNo(), JSON.toJSONString(resultMap));
            throw new BusinessException("调用精友系统失败: " + headMap.get("ResponseMessage"));
        }

        return bodyMap.get("URL");
    }

    @Override
    public String evaluateLoss(Long id, String parameterString) {
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectById(id);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务");
        }

        // 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + repairTask.getTaskNo());
        }
        if (!workflowInstance.getCurrentActivityCode().equals("LOSS_ASSESSMENT")
                && !workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new BusinessException("维修任务节点状态不是处理中, 无法进行精友核价信息: " + repairTask.getTaskNo());
        }

        // 检查是否存在定损数据
        if (!hasLossAssessmentData(repairTask.getTaskNo())) {
            throw new BusinessException("未经过定损系统维修报价，请退回后重新定损");
        }
        
        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("005");
        headBean.setOperatingTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // PACKET > BODY >ReqInfo
        ReqInfoEvaluate reqInfoEvaluate = new ReqInfoEvaluate();
        reqInfoEvaluate.setReturnURL(systemConfig.getEvaluateBackURL());
        String refreshUrl = systemConfig.getEvaluateRefreshURL().replace("idReplace", id.toString());
        reqInfoEvaluate.setRefreshURL(refreshUrl);
        // PACKET > BODY >EvalLossInfo
        EvalLossInfoEvaluate evalLossInfoEvaluate = carDamageRequestService.queryEvalLossInfoEvaluate(id);
        
        String orgId = "00";
        String orgName = "环球";
        evalLossInfoEvaluate.setApprComCode(orgId);
        evalLossInfoEvaluate.setApprCompany(orgName);
        evalLossInfoEvaluate.setApprBranchComCode(orgId);
        evalLossInfoEvaluate.setApprBranchComName(orgName);
        evalLossInfoEvaluate.setApprHandlerCode(orgId);
        evalLossInfoEvaluate.setApprHandlerName(orgName);
        // PACKET > BODY
        BodyBeanEvaluate bodyBeanEvaluate = new BodyBeanEvaluate();
        bodyBeanEvaluate.setReqInfoEvaluate(reqInfoEvaluate);
        bodyBeanEvaluate.setEvalLossInfoEvaluate(evalLossInfoEvaluate);
        // PACKET
        PacketBeanEvaluate packetBeanEvaluate = new PacketBeanEvaluate();
        packetBeanEvaluate.setHeadBean(headBean);
        packetBeanEvaluate.setBodyBeanEvaluate(bodyBeanEvaluate);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBeanEvaluate, stringWriter);
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), systemConfig.getJyInterfaceUrl() + "005");

        // 解析响应
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error("{}核损核价返回为空", repairTask.getTaskNo());
            throw new BusinessException("调用精友系统失败");
        }

        Map<String, String> headMap = safeMapCast(resultMap.get("HEAD"), "HEAD");
        Map<String, String> bodyMap = safeMapCast(resultMap.get("BODY"), "BODY");

        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error("{}核损核价返回错误: {}", repairTask.getTaskNo(), JSON.toJSONString(resultMap));
            throw new BusinessException("调用精友系统失败: " + headMap.get("ResponseMessage"));
        }

        return bodyMap.get("URL");
    }

    @Override
    public String viewLoss(Long id) {
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectById(id);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务");
        }

        // 检查是否存在定损数据
        if (!hasLossAssessmentData(repairTask.getTaskNo())) {
            throw new BusinessException("未经过定损系统维修报价，请退回后重新定损");
        }

        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("014");
        headBean.setOperatingTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // PACKET > BODY >EvalLossInfo
        EvalLossInfoView evalLossInfoView = new EvalLossInfoView();
        evalLossInfoView.setDmgVhclId(repairTask.getTaskNo());
        evalLossInfoView.setLossNo(repairTask.getTaskNo());
        evalLossInfoView.setReportCode(repairTask.getTaskNo());
        // PACKET > BODY
        BodyBeanView bodyBeanView = new BodyBeanView();
        bodyBeanView.setEvalLossInfoView(evalLossInfoView);
        // PACKET
        PacketBeanView packetBeanView = new PacketBeanView();
        packetBeanView.setHeadBean(headBean);
        packetBeanView.setBodyBeanView(bodyBeanView);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBeanView, stringWriter);
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), systemConfig.getJyInterfaceUrl() + "014");

        // 解析响应
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error("{}查看定损返回为空", repairTask.getTaskNo());
            throw new BusinessException("调用精友系统失败");
        }

        Map<String, String> headMap = safeMapCast(resultMap.get("HEAD"), "HEAD");
        Map<String, String> bodyMap = safeMapCast(resultMap.get("BODY"), "BODY");

        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error("{}查看定损返回错误: {}", repairTask.getTaskNo(), JSON.toJSONString(resultMap));
            throw new BusinessException("调用精友系统失败: " + headMap.get("ResponseMessage"));
        }

        return bodyMap.get("URL");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PacketResponse lossAssessmentBack(PacketBeanAssBack packetBeanAssBack) {
        // 返回数据
        PacketResponse packetResponse = new PacketResponse();
        HeadBeanResponse headBeanResponse = new HeadBeanResponse();
        packetResponse.setHeadBeanResponse(headBeanResponse);
        // 回调数据
        HeadBeanAssBack headBeanAssBack = packetBeanAssBack.getHeadBeanAssBack();
        BodyBeanAssBack bodyBeanAssBack = packetBeanAssBack.getBodyBeanAssBack();
        EvalLossInfoAssBack evalLossInfoAssBack = bodyBeanAssBack.getEvalLossInfoAssBack();
        // 接口校验
        if (!StringUtils.equals("jy", headBeanAssBack.getUserCode())
                || !StringUtils.equals("jy", headBeanAssBack.getPassword())) {
            headBeanResponse.setResponseCode("400");
            headBeanResponse.setErrorMessage("系统认证错误");
            return packetResponse;
        }
        if (StringUtils.isBlank(evalLossInfoAssBack.getLossNo())) {
            headBeanResponse.setResponseCode("402");
            headBeanResponse.setErrorMessage("字段：LossNo 是必传字段，不可为空;");
            return packetResponse;
        }
        if (!StringUtils.equals("009", headBeanAssBack.getRequestType())) {
            headBeanResponse.setResponseCode("403");
            headBeanResponse.setErrorMessage("请求类型错误");
            return packetResponse;
        }
        try {
            WorkflowInstance workflowInstance = tableWorkflowInstanceService
                    .selectByBusinessId(evalLossInfoAssBack.getLossNo());
            // 临时设置当前租户上下文
            TenantContextHolder.setTenant(Long.valueOf(workflowInstance.getTenantId()));
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(workflowInstance.getBusinessId());

            List<MtcLossInfo> mtcLossInfoList = tableLossInfoService.selectByTaskNo(evalLossInfoAssBack.getLossNo());
            if (CollectionUtils.isEmpty(mtcLossInfoList)) {
                // 第一次定损，新增
                saveLossAssBackData(packetBeanAssBack);
            } else if (mtcLossInfoList.size() == 1) {
                MtcLossInfo mtcLossInfo = mtcLossInfoList.get(0);
                if (StringUtils.isNotBlank(mtcLossInfo.getAuditHandlerCode())) {
                    // 核损退回定损，修改第一次为无效，新增
                    updateLossAssBackData(evalLossInfoAssBack.getLossNo(), 1);
                } else {
                    // 第一次定损，删除后新增
                    updateLossAssBackData(evalLossInfoAssBack.getLossNo(), 2);
                }
                saveLossAssBackData(packetBeanAssBack);
            } else {
                // 第三次或三次以上定损，删除最新一次，新增
                updateLossAssBackData(evalLossInfoAssBack.getLossNo(), 2);
                saveLossAssBackData(packetBeanAssBack);
            }
            // 修改定损金额
            MtcRepairTask updateRepairTask = new MtcRepairTask();
            updateRepairTask.setId(mtcRepairTask.getId());
            updateRepairTask.setTaskNo(evalLossInfoAssBack.getLossNo());
            updateRepairTask.setUpdateBy("定损系统");
            updateRepairTask.setUpdatedTime(new Date());
            updateRepairTask.setRepairReplaceTotalAmount(evalLossInfoAssBack.getEvalPartSum());
            updateRepairTask.setRepairRepairTotalAmount(evalLossInfoAssBack.getEvalRepairSum());
            updateRepairTask.setRepairInsuranceTotalAmount(evalLossInfoAssBack.getSumLossAmount());
            if (workflowInstance.getCurrentActivityCode().equals(ActivityDefinitionEnum.REPAIR_QUOTATION.getCode())) {
                updateRepairTask.setRepairTotalAmount(evalLossInfoAssBack.getSumLossAmount());
            }
            MtcRepairTask repairTaskBO = tableRepairTaskService.selectByTaskNo(evalLossInfoAssBack.getLossNo());
            if (repairTaskBO != null && repairTaskBO.getOrigin() == 1
                    && StringUtils.isNotBlank(evalLossInfoAssBack.getTravelMileAges())) {
                if (!new BigDecimal(evalLossInfoAssBack.getTravelMileAges()).equals(BigDecimal.ZERO)) {
                    updateRepairTask.setTotalMileage(new BigDecimal(evalLossInfoAssBack.getTravelMileAges()));
                }
            }
            tableRepairTaskService.updateSelectiveById(updateRepairTask, "精友回调");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("lossAssessmentBack error", e);
            headBeanResponse.setResponseCode("499");
            headBeanResponse.setErrorMessage("其它异常错误");
            return packetResponse;
        } finally {
            // 恢复原始租户上下文
            TenantContextHolder.clear();
        }
        log.warn("lossAssessmentBack return--------------------->" + JSON.toJSONString(packetResponse));
        return packetResponse;
    }

    /**
     * 保存定损回调数据
     * 
     * @param packetBeanAssBack 回调数据
     */
    private void saveLossAssBackData(PacketBeanAssBack packetBeanAssBack) {
        // 回调数据
        BodyBeanAssBack bodyBeanAssBack = packetBeanAssBack.getBodyBeanAssBack();
        EvalLossInfoAssBack evalLossInfoAssBack = bodyBeanAssBack.getEvalLossInfoAssBack();
        VehicleInfoAssBack vehicleInfoAssBack = bodyBeanAssBack.getVehicleInfoAssBack();
        List<CollisionPartsAssBack> collisionPartsAssBackList = bodyBeanAssBack.getCollisionPartsAssBackList();
        List<LossFitInfoAssBack> lossFitInfoAssBackList = bodyBeanAssBack.getLossFitInfoAssBackList();
        List<LossRepairInfoAssBack> lossRepairInfoAssBackList = bodyBeanAssBack.getLossRepairInfoAssBackList();
        List<LossOuterRepairInfoAssBack> lossOuterRepairInfoAssBackList = bodyBeanAssBack
                .getLossOuterRepairInfoAssBackList();
        List<LossRepairSumInfoAssBack> lossRepairSumInfoAssBackList = bodyBeanAssBack.getLossRepairSumInfoAssBackList();
        List<LossAssistInfoAssBack> lossAssistInfoAssBackList = bodyBeanAssBack.getLossAssistInfoAssBackList();
        MtcLossInfo mtcLossInfo = new MtcLossInfo();
        BeanUtils.copyProperties(evalLossInfoAssBack, mtcLossInfo);
        BeanUtils.copyProperties(vehicleInfoAssBack, mtcLossInfo);
        mtcLossInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
        tableLossInfoService.insert(mtcLossInfo);
        List<MtcCollisionParts> mtcCollisionPartsList = new ArrayList<>();
        for (CollisionPartsAssBack collisionPartsAssBack : collisionPartsAssBackList) {
            MtcCollisionParts mtcCollisionParts = new MtcCollisionParts();
            BeanUtils.copyProperties(collisionPartsAssBack, mtcCollisionParts);
            mtcCollisionParts.setTaskNo(evalLossInfoAssBack.getLossNo());
            mtcCollisionPartsList.add(mtcCollisionParts);
        }
        if (CollectionUtils.isNotEmpty(mtcCollisionPartsList)) {
            tableCollisionPartsService.batchInsert(mtcCollisionPartsList);
        }
        List<MtcLossFitInfo> mtcLossFitInfoList = new ArrayList<>();
        for (LossFitInfoAssBack lossFitInfoAssBack : lossFitInfoAssBackList) {
            MtcLossFitInfo mtcLossFitInfo = new MtcLossFitInfo();
            BeanUtils.copyProperties(lossFitInfoAssBack, mtcLossFitInfo);
            mtcLossFitInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
            mtcLossFitInfoList.add(mtcLossFitInfo);
        }
        if (CollectionUtils.isNotEmpty(mtcLossFitInfoList)) {
            tableLossFitInfoService.batchInsert(mtcLossFitInfoList);
        }
        List<MtcLossRepairInfo> mtcLossRepairInfoList = new ArrayList<>();
        for (LossRepairInfoAssBack lossRepairInfoAssBack : lossRepairInfoAssBackList) {
            MtcLossRepairInfo mtcLossRepairInfo = new MtcLossRepairInfo();
            BeanUtils.copyProperties(lossRepairInfoAssBack, mtcLossRepairInfo);
            mtcLossRepairInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
            mtcLossRepairInfoList.add(mtcLossRepairInfo);
        }
        if (CollectionUtils.isNotEmpty(mtcLossRepairInfoList)) {
            tableLossRepairInfoService.batchInsert(mtcLossRepairInfoList);
        }
        List<MtcLossOuterRepairInfo> mtcLossOuterRepairInfoList = new ArrayList<>();
        for (LossOuterRepairInfoAssBack lossOuterRepairInfoAssBack : lossOuterRepairInfoAssBackList) {
            MtcLossOuterRepairInfo mtcLossOuterRepairInfo = new MtcLossOuterRepairInfo();
            BeanUtils.copyProperties(lossOuterRepairInfoAssBack, mtcLossOuterRepairInfo);
            mtcLossOuterRepairInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
            mtcLossOuterRepairInfoList.add(mtcLossOuterRepairInfo);
        }
        if (CollectionUtils.isNotEmpty(mtcLossOuterRepairInfoList)) {
            tableLossOuterRepairInfoService.batchInsert(mtcLossOuterRepairInfoList);
        }
        List<MtcLossRepairSumInfo> mtcLossRepairSumInfoList = new ArrayList<>();
        for (LossRepairSumInfoAssBack lossRepairSumInfoAssBack : lossRepairSumInfoAssBackList) {
            MtcLossRepairSumInfo mtcLossRepairSumInfo = new MtcLossRepairSumInfo();
            BeanUtils.copyProperties(lossRepairSumInfoAssBack, mtcLossRepairSumInfo);
            mtcLossRepairSumInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
            mtcLossRepairSumInfoList.add(mtcLossRepairSumInfo);
        }
        if (CollectionUtils.isNotEmpty(mtcLossRepairSumInfoList)) {
            tableLossRepairSumInfoService.batchInsert(mtcLossRepairSumInfoList);
        }
        List<MtcLossAssistInfo> mtcLossAssistInfoList = new ArrayList<>();
        for (LossAssistInfoAssBack lossAssistInfoAssBack : lossAssistInfoAssBackList) {
            MtcLossAssistInfo mtcLossAssistInfo = new MtcLossAssistInfo();
            BeanUtils.copyProperties(lossAssistInfoAssBack, mtcLossAssistInfo);
            mtcLossAssistInfo.setTaskNo(evalLossInfoAssBack.getLossNo());
            mtcLossAssistInfoList.add(mtcLossAssistInfo);
        }
        if (CollectionUtils.isNotEmpty(mtcLossAssistInfoList)) {
            tableLossAssistInfoService.batchInsert(mtcLossAssistInfoList);
        }
    }

    /**
     * 修改定损回调数据
     * 
     * @param taskNo 任务编号
     * @param type   操作类型（1：修改 2：删除）
     */
    private void updateLossAssBackData(String taskNo, Integer type) {
        if (type == 1) {
            tableLossInfoService.updateStatus(taskNo);
            tableCollisionPartsService.updateStatus(taskNo);
            tableLossFitInfoService.updateStatus(taskNo);
            tableLossRepairInfoService.updateStatus(taskNo);
            tableLossOuterRepairInfoService.updateStatus(taskNo);
            tableLossRepairSumInfoService.updateStatus(taskNo);
            tableLossAssistInfoService.updateStatus(taskNo);
        } else {
            tableLossInfoService.deleteByTaskNo(taskNo);
            tableCollisionPartsService.deleteByTaskNo(taskNo);
            tableLossFitInfoService.deleteByTaskNo(taskNo);
            tableLossRepairInfoService.deleteByTaskNo(taskNo);
            tableLossOuterRepairInfoService.deleteByTaskNo(taskNo);
            tableLossRepairSumInfoService.deleteByTaskNo(taskNo);
            tableLossAssistInfoService.deleteByTaskNo(taskNo);
        }
    }

    @Override
    public PacketResponse evaluateLossBack(PacketBeanEvaBack packetBeanEvaBack) {
        // 返回数据
        PacketResponse packetResponse = new PacketResponse();
        HeadBeanResponse headBeanResponse = new HeadBeanResponse();
        packetResponse.setHeadBeanResponse(headBeanResponse);
        // 回调数据
        HeadBeanEvaBack headBeanEvaBack = packetBeanEvaBack.getHeadBeanEvaBack();
        BodyBeanEvaBack bodyBeanEvaBack = packetBeanEvaBack.getBodyBeanEvaBack();
        EvalLossInfoEvaBack evalLossInfoEvaBack = bodyBeanEvaBack.getEvalLossInfoEvaBack();
        List<LossFitInfoEvaBack> lossFitInfoEvaBackList = bodyBeanEvaBack.getLossFitInfoEvaBackList();
        List<LossRepairInfoEvaBack> lossRepairInfoEvaBackList = bodyBeanEvaBack.getLossRepairInfoEvaBackList();
        List<LossOuterRepairInfoEvaBack> lossOuterRepairInfoEvaBackList = bodyBeanEvaBack
                .getLossOuterRepairInfoEvaBackList();
        List<LossRepairSumInfoEvaBack> lossRepairSumInfoEvaBackList = bodyBeanEvaBack.getLossRepairSumInfoEvaBackList();
        List<LossAssistInfoEvaBack> lossAssistInfoEvaBackList = bodyBeanEvaBack.getLossAssistInfoEvaBackList();
        // 接口校验
        if (!StringUtils.equals("jy", headBeanEvaBack.getUserCode())
                || !StringUtils.equals("jy", headBeanEvaBack.getPassword())) {
            headBeanResponse.setResponseCode("400");
            headBeanResponse.setErrorMessage("系统认证错误");
            return packetResponse;
        }
        if (StringUtils.isBlank(evalLossInfoEvaBack.getLossNo())) {
            headBeanResponse.setResponseCode("402");
            headBeanResponse.setErrorMessage("字段：LossNo 是必传字段，不可为空;");
            return packetResponse;
        }
        if (!StringUtils.equals("006", headBeanEvaBack.getRequestType())) {
            headBeanResponse.setResponseCode("403");
            headBeanResponse.setErrorMessage("请求类型错误");
            return packetResponse;
        }
        try {
            WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(evalLossInfoEvaBack.getLossNo());
            // 临时设置当前租户上下文
            TenantContextHolder.setTenant(Long.valueOf(workflowInstance.getTenantId()));
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(workflowInstance.getBusinessId());
            // 更新数据
            MtcLossInfo mtcLossInfo = new MtcLossInfo();
            BeanUtils.copyProperties(evalLossInfoEvaBack, mtcLossInfo);
            mtcLossInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
            mtcLossInfo.setAuditSelfPaySum(evalLossInfoEvaBack.getSelfPaySum());
            mtcLossInfo.setAuditOuterSum(evalLossInfoEvaBack.getOuterSum());
            mtcLossInfo.setAuditDerogationSum(evalLossInfoEvaBack.getDerogationSum());
            mtcLossInfo.setAuditHandlerCode(evalLossInfoEvaBack.getHandlerCode());
            mtcLossInfo.setAuditRemark(evalLossInfoEvaBack.getRemark());
            mtcLossInfo.setAuditAllLoseSum(evalLossInfoEvaBack.getAllLoseSum());
            mtcLossInfo.setAuditAllLoseRemainsSum(evalLossInfoEvaBack.getAllLoseRemainsSum());
            mtcLossInfo.setAuditAllLoseSalvSum(evalLossInfoEvaBack.getAllLoseSalvSum());
            mtcLossInfo.setAuditAllLoseTotalSum(evalLossInfoEvaBack.getAllLoseTotalSum());
            tableLossInfoService.updateSelectiveByTaskNoAndStatus(mtcLossInfo, evalLossInfoEvaBack.getLossNo(), "定损系统");
            for (LossFitInfoEvaBack lossFitInfoEvaBack : lossFitInfoEvaBackList) {
                MtcLossFitInfo mtcLossFitInfo = new MtcLossFitInfo();
                BeanUtils.copyProperties(lossFitInfoEvaBack, mtcLossFitInfo);
                mtcLossFitInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
                mtcLossFitInfo.setAuditRemark(lossFitInfoEvaBack.getRemark());
                mtcLossFitInfo.setAuditRecheckFlag(lossFitInfoEvaBack.getRecheckFlag());
                tableLossFitInfoService.updateSelectiveByTaskNoAndStatus(mtcLossFitInfo, evalLossInfoEvaBack.getLossNo(), "定损系统");
            }
            for (LossRepairInfoEvaBack lossRepairInfoEvaBack : lossRepairInfoEvaBackList) {
                MtcLossRepairInfo mtcLossRepairInfo = new MtcLossRepairInfo();
                BeanUtils.copyProperties(lossRepairInfoEvaBack, mtcLossRepairInfo);
                mtcLossRepairInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
                mtcLossRepairInfo.setAuditRemark(lossRepairInfoEvaBack.getRemark());
                tableLossRepairInfoService.updateSelectiveByTaskNoAndStatus(mtcLossRepairInfo, evalLossInfoEvaBack.getLossNo(), "定损系统");
            }
            for (LossOuterRepairInfoEvaBack lossOuterRepairInfoEvaBack : lossOuterRepairInfoEvaBackList) {
                MtcLossOuterRepairInfo mtcLossOuterRepairInfo = new MtcLossOuterRepairInfo();
                BeanUtils.copyProperties(lossOuterRepairInfoEvaBack, mtcLossOuterRepairInfo);
                mtcLossOuterRepairInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
                mtcLossOuterRepairInfo.setAuditRepairHandaddFlag(lossOuterRepairInfoEvaBack.getRepairHandaddFlag());
                mtcLossOuterRepairInfo.setAuditEvalOuterPirce(lossOuterRepairInfoEvaBack.getEvalOuterPirce());
                mtcLossOuterRepairInfo.setAuditDerogationPrice(lossOuterRepairInfoEvaBack.getDerogationPrice());
                mtcLossOuterRepairInfo.setAuditDerogationItemName(lossOuterRepairInfoEvaBack.getDerogationItemName());
                mtcLossOuterRepairInfo.setAuditDerogationItemCode(lossOuterRepairInfoEvaBack.getDerogationItemCode());
                mtcLossOuterRepairInfo.setAuditDerogationPriceType(lossOuterRepairInfoEvaBack.getDerogationPriceType());
                mtcLossOuterRepairInfo.setAuditPartPrice(lossOuterRepairInfoEvaBack.getPartPrice());
                mtcLossOuterRepairInfo.setAuditRepairFactoryId(lossOuterRepairInfoEvaBack.getRepairFactoryId());
                mtcLossOuterRepairInfo.setAuditRepairFactoryName(lossOuterRepairInfoEvaBack.getRepairFactoryName());
                mtcLossOuterRepairInfo.setAuditRepairFactoryCode(lossOuterRepairInfoEvaBack.getRepairFactoryCode());
                mtcLossOuterRepairInfo.setAuditItemCoverCode(lossOuterRepairInfoEvaBack.getItemCoverCode());
                mtcLossOuterRepairInfo.setAuditRemark(lossOuterRepairInfoEvaBack.getRemark());
                mtcLossOuterRepairInfo.setAuditRepairOuterSum(lossOuterRepairInfoEvaBack.getRepairOuterSum());
                mtcLossOuterRepairInfo.setAuditReferencePartPrice(lossOuterRepairInfoEvaBack.getReferencePartPrice());
                mtcLossOuterRepairInfo.setAuditOutItemAmount(lossOuterRepairInfoEvaBack.getOutItemAmount());
                tableLossOuterRepairInfoService.updateSelectiveByTaskNoAndStatus(mtcLossOuterRepairInfo, evalLossInfoEvaBack.getLossNo(),"定损系统");
            }
            for (LossRepairSumInfoEvaBack lossRepairSumInfoEvaBack : lossRepairSumInfoEvaBackList) {
                MtcLossRepairSumInfo mtcLossRepairSumInfo = new MtcLossRepairSumInfo();
                BeanUtils.copyProperties(lossRepairSumInfoEvaBack, mtcLossRepairSumInfo);
                mtcLossRepairSumInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
                mtcLossRepairSumInfo.setAuditItemCount(lossRepairSumInfoEvaBack.getItemCount());
                tableLossRepairSumInfoService.updateSelectiveByTaskNoAndStatus(mtcLossRepairSumInfo, evalLossInfoEvaBack.getLossNo(), "定损系统");
            }
            for (LossAssistInfoEvaBack lossAssistInfoEvaBack : lossAssistInfoEvaBackList) {
                MtcLossAssistInfo mtcLossAssistInfo = new MtcLossAssistInfo();
                BeanUtils.copyProperties(lossAssistInfoEvaBack, mtcLossAssistInfo);
                mtcLossAssistInfo.setTaskNo(evalLossInfoEvaBack.getLossNo());
                mtcLossAssistInfo.setAuditRemark(lossAssistInfoEvaBack.getRemark());
                tableLossAssistInfoService.updateSelectiveByTaskNoAndStatus(mtcLossAssistInfo, evalLossInfoEvaBack.getLossNo(), "定损系统");
            }
            // 修改核损金额
            MtcRepairTask updateRepairTask = new MtcRepairTask();
            updateRepairTask.setId(mtcRepairTask.getId());
            updateRepairTask.setTaskNo(evalLossInfoEvaBack.getLossNo());
            updateRepairTask.setVehicleReplaceTotalAmount(evalLossInfoEvaBack.getAuditPartSum());
            updateRepairTask.setVehicleRepairTotalAmount(evalLossInfoEvaBack.getAuditRepiarSum());
            updateRepairTask.setVehicleInsuranceTotalAmount(evalLossInfoEvaBack.getTotalSum());
            if (workflowInstance.getCurrentActivityCode().equals(ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode())) {
                updateRepairTask.setRepairTotalAmount(updateRepairTask.getVehicleInsuranceTotalAmount());
            }
            tableRepairTaskService.updateSelectiveById(updateRepairTask, "定损系统");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("evaluateLossBack error", e);
            headBeanResponse.setResponseCode("499");
            headBeanResponse.setErrorMessage("其它异常错误");
            return packetResponse;
        } finally {
            // 恢复原始租户上下文
            TenantContextHolder.clear();
        }
        log.warn("evaluateLossBack return--------------------->" + JSON.toJSONString(packetResponse));
        return packetResponse;
    }

    @Override
    public LossDetailVO getLossDetail(String taskNo) {
        LossDetailVO lossDetailVO = new LossDetailVO();

        // 获取配件信息并设置
        List<MtcLossFitInfo> mtcLossFitInfos = tableLossFitInfoService.selectByTaskNo(taskNo);
        if (CollectionUtils.isNotEmpty(mtcLossFitInfos)) {
            List<LossFitInfoDTO> lossFitInfoList = new ArrayList<>();
            for (MtcLossFitInfo mtcLossFitInfo : mtcLossFitInfos) {
                LossFitInfoDTO lossFitInfoDTO = new LossFitInfoDTO();
                BeanUtils.copyProperties(mtcLossFitInfo, lossFitInfoDTO);
                lossFitInfoList.add(lossFitInfoDTO);
            }
            lossDetailVO.setLossFitInfoList(lossFitInfoList);
        }

        // 获取维修信息并设置
        List<MtcLossRepairInfo> mtcLossRepairInfos = tableLossRepairInfoService.selectByTaskNo(taskNo);
        if (CollectionUtils.isNotEmpty(mtcLossRepairInfos)) {
            List<LossRepairInfoDTO> lossRepairInfoList = new ArrayList<>();
            for (MtcLossRepairInfo mtcLossRepairInfo : mtcLossRepairInfos) {
                LossRepairInfoDTO LossRepairInfo = new LossRepairInfoDTO();
                BeanUtils.copyProperties(mtcLossRepairInfo, LossRepairInfo);
                lossRepairInfoList.add(LossRepairInfo);
            }
            lossDetailVO.setLossRepairInfoList(lossRepairInfoList);
        }

        // 获取辅料信息并设置
        List<MtcLossAssistInfo> mtcLossAssistInfos = tableLossAssistInfoService.selectByTaskNo(taskNo);
        if (CollectionUtils.isNotEmpty(mtcLossAssistInfos)) {
            List<LossAssistInfoDTO> lossAssistInfoList = new ArrayList<>();
            for (MtcLossAssistInfo mtcLossAssistInfo : mtcLossAssistInfos) {
                LossAssistInfoDTO lossAssistInfo = new LossAssistInfoDTO();
                BeanUtils.copyProperties(mtcLossAssistInfo, lossAssistInfo);
                lossAssistInfoList.add(lossAssistInfo);
            }
            lossDetailVO.setLossAssistInfoList(null);
        }

        return lossDetailVO;
    }

    @Override
    public void lossNotify(String taskNo, JingYouLossNotify jingYouLossNotify) {
        // 获取登录用户公司信息
        List<String> userOrgIds = SessionUtils.getDirectOrgIds();
        String orgId = !userOrgIds.isEmpty() ? userOrgIds.get(0) : "";
        
        // 任务状态同步（请求定损系统）
        // PACKET > HEAD
        HeadBean headBean = new HeadBean();
        headBean.setRequestType("007");
        headBean.setOperatingTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // PACKET > BODY >EvalLossInfo
        EvalLossInfoSynchronize evalLossInfoSynchronize = new EvalLossInfoSynchronize();
        evalLossInfoSynchronize.setLossNo(taskNo);
        evalLossInfoSynchronize.setReportCode(taskNo);
        evalLossInfoSynchronize.setDmgVhclId(taskNo);
        evalLossInfoSynchronize.setStatusCode(jingYouLossNotify.getStatusCode());
        evalLossInfoSynchronize.setStatusName(jingYouLossNotify.getStatusName());
        String orgName = "上海挚极";
        evalLossInfoSynchronize.setComCode(orgId);
        evalLossInfoSynchronize.setCompany(orgName);
        evalLossInfoSynchronize.setBranchComCode(orgId);
        evalLossInfoSynchronize.setBranchComName(orgName);
        evalLossInfoSynchronize.setHandlerCode(SessionUtils.getUserId().toString());
        evalLossInfoSynchronize.setHandlerName(SessionUtils.getNickname());
        evalLossInfoSynchronize.setOperationLink(jingYouLossNotify.getOperationLink());
        evalLossInfoSynchronize.setOperationResults(jingYouLossNotify.getOperationResults());
        // PACKET > BODY
        BodyBeanSynchronize bodyBeanSynchronize = new BodyBeanSynchronize();
        bodyBeanSynchronize.setEvalLossInfoSynchronize(evalLossInfoSynchronize);
        // PACKET
        PacketBeanSynchronize packetBeanSynchronize = new PacketBeanSynchronize();
        packetBeanSynchronize.setHeadBean(headBean);
        packetBeanSynchronize.setBodyBeanSynchronize(bodyBeanSynchronize);
        StringWriter stringWriter = new StringWriter();
        JAXB.marshal(packetBeanSynchronize, stringWriter);
        String result = HttpUtils.sendXmlPost(stringWriter.toString(), systemConfig.getJyInterfaceUrl() + "007");
        Map<String, Object> resultMap = XmlUtils.xmlToMap(result);
        if (MapUtils.isEmpty(resultMap)) {
            log.error(" ：-------------->" + JSON.toJSONString(resultMap));
            throw new BusinessException("任务状态同步失败");
        }
        Map<String, String> headMap = safeMapCast(resultMap.get("HEAD"), "HEAD");
        if (!StringUtils.equals("000", headMap.get("ResponseCode"))) {
            log.error("任务状态同步失败：-------------->" + JSON.toJSONString(resultMap));
            throw new BusinessException("任务状态同步失败");
        }
    }
}
