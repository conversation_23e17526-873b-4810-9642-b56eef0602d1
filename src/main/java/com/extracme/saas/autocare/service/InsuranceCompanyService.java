package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.InsuranceCompanyCreateDTO;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyQueryDTO;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyUpdateDTO;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyUpdateStatusDTO;
import com.extracme.saas.autocare.model.entity.AttachmentInfo;
import com.extracme.saas.autocare.model.vo.InsuranceCompanyVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

import java.util.List;

/**
 * 保司信息服务接口
 */
public interface InsuranceCompanyService {

    /**
     * 分页查询列表
     *
     * @param queryDTO 查询参数
     * @return 保司信息列表
     */
    BasePageVO<InsuranceCompanyVO> queryInsuranceCompanyList(InsuranceCompanyQueryDTO queryDTO);


    /**
     * 获取保司信息详情
     *
     * @param id 保司信息ID
     * @return 保司信息详情
     */
    InsuranceCompanyVO getInsuranceCompanyDetails(Long id);

    /**
     * 创建保司信息
     *
     * @param createDTO 创建参数
     * @return 保司信息ID
     */
    void createInsuranceCompany(InsuranceCompanyCreateDTO createDTO);

    /**
     * 修改保司信息
     *
     * @param updateDTO 修改参数
     */
    void updateInsuranceCompany(InsuranceCompanyUpdateDTO updateDTO);

    /**
     * 删除保司信息
     *
     * @param id 保司信息ID
     */
    void deleteInsuranceCompany(Long id);

    /**
     * 更新保司信息状态
     *
     * @param updateStatusDTO 更新参数
     */
    void updateInsuranceCompanyStatus(InsuranceCompanyUpdateStatusDTO updateStatusDTO);


    /**
     * 获取保司信息下拉列表
     * @return 保司信息下拉数据列表，ID类型为String
     */
    List<ComboVO<Integer>> getAllInsuranceCompanyCombo();


    /**
     * 批量插入附件信息
     * @param attachmentInfoList 附件信息列表
     * @return 插入数量
     */
    int insertAttachment(List<AttachmentInfo> attachmentInfoList);


    /**
     * 查询附件信息列表
     * @param foreignKey 外键
     * @return 附件信息列表
     */
    List<AttachmentInfo> queryAttachmentList(String foreignKey);
}