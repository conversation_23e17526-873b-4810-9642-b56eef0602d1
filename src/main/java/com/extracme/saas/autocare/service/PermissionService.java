package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.vo.PermissionTreeVO;
import java.util.List;

/**
 * 权限服务接口
 */
public interface PermissionService {

    /**
     * 创建权限
     *
     * @param permission 权限信息
     * @return 权限ID
     */
    Long createPermission(SysPermission permission);

    /**
     * 更新权限
     *
     * @param permission 权限信息
     */
    void updatePermission(SysPermission permission);

    /**
     * 删除权限
     *
     * @param id 权限ID
     */
    void deletePermission(Long id);

    /**
     * 删除权限（支持强制删除）
     *
     * @param id 权限ID
     * @param forceDelete 是否强制删除（true：即使权限被角色使用也删除；false：权限被使用时抛出异常）
     */
    void deletePermission(Long id, boolean forceDelete);

    /**
     * 获取权限详情
     *
     * @param id 权限ID
     * @return 权限信息
     */
    SysPermission getPermission(Long id);

    /**
     * 获取权限列表
     *
     * @param type 权限类型
     * @return 权限列表
     */
    List<SysPermission> getPermissionList(Integer type);

    /**
     * 获取用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<SysPermission> getUserPermissions(Long userId);

    /**
     * 获取角色权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<SysPermission> getRolePermissions(Long roleId);

    /**
     * 分配角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    void assignRolePermissions(Long roleId, List<Long> permissionIds);

    /**
     * 检查用户是否有指定权限
     *
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String permissionCode);

    /**
     * 获取权限树
     *
     * @return 权限树列表
     */
    List<PermissionTreeVO> getPermissionTree();
}