package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.VehicleInRepairUpdateDTO;
import com.extracme.saas.autocare.model.dto.VehicleInRepairInspectionDTO;

/**
 * 车辆维修服务接口
 */
public interface VehicleInRepairService {

    /**
     * 保存车辆维修信息
     *
     * @param vehicleRepairDTO 车辆维修信息
     */
    void saveVehicleRepair(VehicleInRepairUpdateDTO vehicleRepairDTO);

    /**
     * 申请车辆维修验收
     * 
     * @param inspectionDTO 申请验收参数
     */
    void submitVehicleRepairInspection(VehicleInRepairInspectionDTO inspectionDTO);
}