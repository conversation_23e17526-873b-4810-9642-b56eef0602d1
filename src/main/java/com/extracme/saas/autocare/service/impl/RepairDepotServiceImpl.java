package com.extracme.saas.autocare.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.extracme.saas.autocare.model.entity.*;
import com.extracme.saas.autocare.repository.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.dto.GetRepairNumDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotCreateDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotUpdateStatusDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskWorkflowDTO;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.RepairDepotDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairDepotListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.RepairDepotService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 修理厂服务实现类
 */
@Slf4j
@Service
public class RepairDepotServiceImpl implements RepairDepotService {

    @Autowired
    private TableRepairDepotInfoService repairDepotRepository;

    @Autowired
    private TableRepairDepotCooperativeBranchService cooperativeBranchRepository;

    @Autowired
    private TableRepairDepotVehicleModelService vehicleModelRepository;

    @Autowired
    private TableRepairDepotWarrantyVehicleModelService warrantyVehicleModelRepository;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private TableOrgInfoService orgInfoRepository;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableUserService tableUserService;

    @Override
    public BasePageVO<RepairDepotListVO> queryRepairDepotList(RepairDepotQueryDTO queryDTO) {
        String otherOrgId = queryDTO.getOtherOrgId();
        List<String> repairDepotIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(otherOrgId)){
            List<MtcRepairDepotCooperativeBranch> cooperativeBranchList = cooperativeBranchRepository.queryCooperativeBranchListByOrgId(otherOrgId);
            repairDepotIdList = cooperativeBranchList.stream().map(MtcRepairDepotCooperativeBranch::getRepairDepotId).collect(Collectors.toList());
        }
        List<MtcOrgInfo> orgs = orgInfoRepository.findValidOrgs();
        Map<String, MtcOrgInfo> orgMap = CollStreamUtil.toMap(orgs, MtcOrgInfo::getOrgId, s -> s);
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        queryDTO.setLoginOrgIds(SessionUtils.getDirectOrgIds());
        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            queryDTO.setRepairDepotId(repairDepotId);
        }
        List<MtcRepairDepotInfo> list = repairDepotRepository.queryRepairDepotList(queryDTO,repairDepotIdList);
        PageInfo<MtcRepairDepotInfo> pageInfo = new PageInfo<>(list);
        List<RepairDepotListVO> voList = list.stream().map(repairDepotInfo -> {
            RepairDepotListVO vo = new RepairDepotListVO();
            BeanUtils.copyProperties(repairDepotInfo, vo);
            MtcOrgInfo mainOrg = orgMap.get(repairDepotInfo.getRepairDepotOrgId());
            vo.setMainOrgName(mainOrg != null ? mainOrg.getOrgName() : null);
            return vo;
        }).collect(Collectors.toList());
        // 构建返回结果
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public BasePageVO<RepairDepotListVO> queryCheckRepairDepotList(RepairDepotQueryDTO queryDTO) {
        queryDTO.setStatus(1);
        List<MtcOrgInfo> orgs = orgInfoRepository.findValidOrgs();
        Map<String, MtcOrgInfo> orgMap = CollStreamUtil.toMap(orgs, MtcOrgInfo::getOrgId, s -> s);
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        // 登录人公司
        queryDTO.setLoginOrgIds(SessionUtils.getDirectOrgIds());
        List<MtcRepairDepotCooperativeBranch> cooperativeBranchList = cooperativeBranchRepository.queryCooperativeBranchList(SessionUtils.getDirectOrgIds());
        List<String> repairDepotIdList = cooperativeBranchList.stream().map(MtcRepairDepotCooperativeBranch::getRepairDepotId).collect(Collectors.toList());

        // 登录人修理厂
        String repairDepotId = SessionUtils.getLoginUser().getUser().getRepairDepotId();
        if (StringUtils.isNotBlank(repairDepotId)) {
            queryDTO.setRepairDepotId(repairDepotId);
        }
        List<MtcRepairDepotInfo> list = repairDepotRepository.queryRepairDepotList(queryDTO,repairDepotIdList);
        PageInfo<MtcRepairDepotInfo> pageInfo = new PageInfo<>(list);
        List<RepairDepotListVO> voList = list.stream().map(repairDepotInfo -> {
            RepairDepotListVO vo = new RepairDepotListVO();
            BeanUtils.copyProperties(repairDepotInfo, vo);
            MtcOrgInfo mainOrg = orgMap.get(repairDepotInfo.getRepairDepotOrgId());
            vo.setMainOrgName(mainOrg != null ? mainOrg.getOrgName() : null);
            // 查询维修厂的再修任务
            List<RepairTaskWorkflowDTO> taskList = tableRepairTaskService.getCurrentRepairNum(new GetRepairNumDTO(null, repairDepotInfo.getRepairDepotOrgId(), repairDepotInfo.getRepairDepotId()));
            taskList = taskList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.groupingBy(
                                    task -> task.getVin() + "_" + task.getCurrentActivityCode(),
                                    Collectors.toList()),
                            map -> map.values().stream()
                                    .map(subList -> subList.get(0))
                                    .collect(Collectors.toList())));
            vo.setTaskCount(taskList.size());
            return vo;
        }).collect(Collectors.toList());
        // 构建返回结果
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public RepairDepotDetailsVO getRepairDepotDetails(Long id) {
        MtcRepairDepotInfo repairDepot = repairDepotRepository.selectById(id);
        if (repairDepot == null) {
            return null;
        }
        RepairDepotDetailsVO vo = new RepairDepotDetailsVO();
        BeanUtils.copyProperties(repairDepot, vo);
        vo.setMainOrgId(repairDepot.getRepairDepotOrgId());
        // 获取合作分公司信息
        List<MtcRepairDepotCooperativeBranch> cooperativeBranchList = cooperativeBranchRepository.queryCooperativeBranchList(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(cooperativeBranchList)) {
            vo.setOtherOrgIdList(cooperativeBranchList.stream().map(MtcRepairDepotCooperativeBranch::getOrgId).collect(Collectors.toList()));
        }
        // 获取可保修车型信息
        List<MtcRepairDepotVehicleModelInfo> vehicleModelList = vehicleModelRepository.queryVehicleModelList(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(vehicleModelList)) {
            vo.setVehicleModelList(vehicleModelList.stream().map(MtcRepairDepotVehicleModelInfo::getVehicleModelSeq).collect(Collectors.toList()));
        }
        // 获取可保修车型信息
        List<MtcRepairDepotWarrantyVehicleModelInfo> warrantyVehicleModelList = warrantyVehicleModelRepository.queryVehicleModelList(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(warrantyVehicleModelList)) {
            vo.setWarrantyVehicleModelList(warrantyVehicleModelList.stream().map(MtcRepairDepotWarrantyVehicleModelInfo::getVehicleModelSeq).collect(Collectors.toList()));
        }
        // 查询维修厂关联的用户信息
        List<SysUser> userList = tableUserService.findAllByRepairDepotId(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(userList)){
            List<String> repairDepotAccountList = userList.stream().map(SysUser::getMobile).collect(Collectors.toList());
            vo.setRepairDepotAccountList(repairDepotAccountList);
        }
        return vo;
    }

    @Override
    public RepairDepotDetailsVO getRepairDepotDetailsByRepairDepotId(String repairDepotId) {
        MtcRepairDepotInfo repairDepot = repairDepotRepository.selectByRepairDepotCode(repairDepotId);
        if (repairDepot == null) {
            return null;
        }
        RepairDepotDetailsVO vo = new RepairDepotDetailsVO();
        BeanUtils.copyProperties(repairDepot, vo);
        vo.setMainOrgId(repairDepot.getRepairDepotOrgId());
        // 获取合作分公司信息
        List<MtcRepairDepotCooperativeBranch> cooperativeBranchList = cooperativeBranchRepository.queryCooperativeBranchList(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(cooperativeBranchList)) {
            vo.setOtherOrgIdList(cooperativeBranchList.stream().map(MtcRepairDepotCooperativeBranch::getOrgId).collect(Collectors.toList()));
        }
        // 获取可保修车型信息
        List<MtcRepairDepotVehicleModelInfo> vehicleModelList = vehicleModelRepository.queryVehicleModelList(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(vehicleModelList)) {
            vo.setVehicleModelList(vehicleModelList.stream().map(MtcRepairDepotVehicleModelInfo::getVehicleModelSeq).collect(Collectors.toList()));
        }
        // 获取可保修车型信息
        List<MtcRepairDepotWarrantyVehicleModelInfo> warrantyVehicleModelList = warrantyVehicleModelRepository.queryVehicleModelList(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(warrantyVehicleModelList)) {
            vo.setWarrantyVehicleModelList(warrantyVehicleModelList.stream().map(MtcRepairDepotWarrantyVehicleModelInfo::getVehicleModelSeq).collect(Collectors.toList()));
        }
        // 查询维修厂关联的用户信息
        List<SysUser> userList = tableUserService.findAllByRepairDepotId(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(userList)){
            List<String> repairDepotAccountList = userList.stream().map(SysUser::getMobile).collect(Collectors.toList());
            vo.setRepairDepotAccountList(repairDepotAccountList);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRepairDepot(RepairDepotCreateDTO createDTO) {
        // 判断姓名是否存在
        MtcRepairDepotInfo repairDepot = repairDepotRepository.selectByRepairDepotName(createDTO.getRepairDepotName());
        if (repairDepot != null) {
            throw new BusinessException("修理厂名称已存在");
        }

        LoginUser loginUser = SessionUtils.getLoginUser();
        MtcRepairDepotInfo repairDepotInfo = new MtcRepairDepotInfo();
        BeanUtils.copyProperties(createDTO, repairDepotInfo);
        //设置唯一ID
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dateStr = dateFormat.format(System.currentTimeMillis());
        repairDepotInfo.setRepairDepotId(dateStr);
        repairDepotInfo.setSsoUserId(loginUser.getUser().getId());
        repairDepotInfo.setRepairDepotOrgId(createDTO.getMainOrgId());
        // 是否保修点(0-否 1-是)
        int warrantyPoint = createDTO.getWarrantyPoint();
        if (warrantyPoint == 1) {
            List<Long> warrantyVehicleModelList = createDTO.getWarrantyVehicleModelList();
            if (CollectionUtils.isEmpty(warrantyVehicleModelList)) {
                throw new BusinessException("请选择可保修车型");
            }
            // 新增可保修车型信息
            List<MtcRepairDepotWarrantyVehicleModelInfo> repairDepotWarrantyVehicleModelList = warrantyVehicleModelList.stream().map(otherOrgId -> {
                MtcRepairDepotWarrantyVehicleModelInfo warrantyVehicleModelInfo = new MtcRepairDepotWarrantyVehicleModelInfo();
                warrantyVehicleModelInfo.setRepairDepotId(repairDepotInfo.getRepairDepotId());
                warrantyVehicleModelInfo.setVehicleModelSeq(otherOrgId);
                return warrantyVehicleModelInfo;
            }).collect(Collectors.toList());
            warrantyVehicleModelRepository.batchInsert(repairDepotWarrantyVehicleModelList);
        }

        // 添加其他组织机构信息
        List<String> otherOrgIdList = createDTO.getOtherOrgIdList();
        if (!CollectionUtils.isEmpty(otherOrgIdList)) {
            List<MtcRepairDepotCooperativeBranch> cooperativeBranchList = otherOrgIdList.stream().map(otherOrgId -> {
                MtcRepairDepotCooperativeBranch cooperativeBranch = new MtcRepairDepotCooperativeBranch();
                cooperativeBranch.setRepairDepotId(repairDepotInfo.getRepairDepotId());
                cooperativeBranch.setOrgId(otherOrgId);
                return cooperativeBranch;
            }).collect(Collectors.toList());
            cooperativeBranchRepository.batchInsert(cooperativeBranchList);
        }


        // 添加可修理车型
        List<Long> repairVehicleModelList = createDTO.getRepairVehicleModelList();
        if (!CollectionUtils.isEmpty(repairVehicleModelList)) {
            List<MtcRepairDepotVehicleModelInfo> vehicleModelList = repairVehicleModelList.stream().map(vehicleModelId -> {
                MtcRepairDepotVehicleModelInfo vehicleModel = new MtcRepairDepotVehicleModelInfo();
                vehicleModel.setRepairDepotId(repairDepotInfo.getRepairDepotId());
                vehicleModel.setVehicleModelSeq(vehicleModelId);
                return vehicleModel;
            }).collect(Collectors.toList());
            vehicleModelRepository.batchInsert(vehicleModelList);
        }

        repairDepotRepository.insert(repairDepotInfo);

        // 添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(repairDepotInfo.getId());
        mtcProcessLog.setOpeContent("新增维修厂信息");
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_DEPOT_INFO);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepairDepot(RepairDepotUpdateDTO updateDTO) {
        MtcRepairDepotInfo repairDepotInfo = repairDepotRepository.selectById(updateDTO.getId());
        if (repairDepotInfo == null) {
            throw new BusinessException("修理厂信息不存在");
        }
        // 判断名称是否已存在
        if (!StringUtils.equals(updateDTO.getRepairDepotName(), repairDepotInfo.getRepairDepotName())) {
            MtcRepairDepotInfo repairDepot = repairDepotRepository.selectByRepairDepotName(updateDTO.getRepairDepotName());
            if (repairDepot != null) {
                throw new BusinessException("修理厂名称已存在");
            }
        }
        if (updateDTO.getStatus() == 0 && repairDepotInfo.getStatus() == 1) {
            // 判断该维修厂是否有未完成的维修任务，无法关闭
            List<RepairTaskWorkflowDTO> repairTaskList = tableRepairTaskService.getCurrentRepairNum(new GetRepairNumDTO(null, repairDepotInfo.getRepairDepotOrgId(), repairDepotInfo.getRepairDepotId()));
            if (CollectionUtils.isNotEmpty(repairTaskList)) {
                throw new BusinessException("此维修厂还有维修任务没完成,不能修改为禁用状态");
            }
        }
        BeanUtils.copyProperties(updateDTO, repairDepotInfo);
        repairDepotInfo.setRepairDepotOrgId(updateDTO.getMainOrgId());

        warrantyVehicleModelRepository.deleteByRepairDepotId(repairDepotInfo.getRepairDepotId());
        // 是否保修点(0-否 1-是)
        int warrantyPoint = updateDTO.getWarrantyPoint();
        if (warrantyPoint == 1) {
            List<Long> warrantyVehicleModelList = updateDTO.getWarrantyVehicleModelList();
            if (CollectionUtils.isEmpty(warrantyVehicleModelList)) {
                throw new BusinessException("请选择可保修车型");
            }
            // 新增可保修车型信息
            List<MtcRepairDepotWarrantyVehicleModelInfo> repairDepotWarrantyVehicleModelList = warrantyVehicleModelList.stream().map(otherOrgId -> {
                MtcRepairDepotWarrantyVehicleModelInfo warrantyVehicleModelInfo = new MtcRepairDepotWarrantyVehicleModelInfo();
                warrantyVehicleModelInfo.setRepairDepotId(repairDepotInfo.getRepairDepotId());
                warrantyVehicleModelInfo.setVehicleModelSeq(otherOrgId);
                return warrantyVehicleModelInfo;
            }).collect(Collectors.toList());
            warrantyVehicleModelRepository.batchInsert(repairDepotWarrantyVehicleModelList);
        }

        cooperativeBranchRepository.deleteByRepairDepotId(repairDepotInfo.getRepairDepotId());
        // 添加其他组织机构信息
        List<String> otherOrgIdList = updateDTO.getOtherOrgIdList();
        List<MtcRepairDepotCooperativeBranch> cooperativeBranchList = otherOrgIdList.stream().map(otherOrgId -> {
            MtcRepairDepotCooperativeBranch cooperativeBranch = new MtcRepairDepotCooperativeBranch();
            cooperativeBranch.setRepairDepotId(repairDepotInfo.getRepairDepotId());
            cooperativeBranch.setOrgId(otherOrgId);
            return cooperativeBranch;
        }).collect(Collectors.toList());
        cooperativeBranchRepository.batchInsert(cooperativeBranchList);

        vehicleModelRepository.deleteByRepairDepotId(repairDepotInfo.getRepairDepotId());
        // 添加可修理车型
        List<Long> repairVehicleModelList = updateDTO.getRepairVehicleModelList();
        if (CollectionUtils.isNotEmpty(repairVehicleModelList)) {
            List<MtcRepairDepotVehicleModelInfo> vehicleModelList = repairVehicleModelList.stream()
                    .map(vehicleModelId -> {
                        MtcRepairDepotVehicleModelInfo vehicleModel = new MtcRepairDepotVehicleModelInfo();
                        vehicleModel.setRepairDepotId(repairDepotInfo.getRepairDepotId());
                        vehicleModel.setVehicleModelSeq(vehicleModelId);
                        return vehicleModel;
                    }).collect(Collectors.toList());
            vehicleModelRepository.batchInsert(vehicleModelList);
        }

        repairDepotRepository.updateSelectiveById(repairDepotInfo);
        //添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(repairDepotInfo.getId());
        mtcProcessLog.setOpeContent("修改维修厂信息");
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_DEPOT_INFO);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRepairDepot(Long id) {
        MtcRepairDepotInfo repairDepot = repairDepotRepository.selectById(id);
        if (repairDepot == null) {
            return;
        }
        //  判断是否可以删除
        List<RepairTaskWorkflowDTO> repairTaskList = tableRepairTaskService.getCurrentRepairNum(new GetRepairNumDTO(null, repairDepot.getRepairDepotOrgId(), repairDepot.getRepairDepotId()));
        if (CollectionUtils.isNotEmpty(repairTaskList)) {
            throw new BusinessException("无法删除，此维修厂还有维修任务没完成");
        }

        List<RepairTaskWorkflowDTO> repairNumList = tableRepairTaskService.getRepairNum(new GetRepairNumDTO(null, repairDepot.getRepairDepotOrgId(), repairDepot.getRepairDepotId()));
        if (CollectionUtils.isNotEmpty(repairNumList)) {
            throw new BusinessException("无法删除，此维修厂已关联维修任务");
        }
        repairDepot.setDelFlag(1);
        repairDepotRepository.updateSelectiveById(repairDepot);
        // 添加日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(repairDepot.getId());
        mtcProcessLog.setOpeContent("删除维修厂信息");
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_DEPOT_INFO);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepairDepotStatus(RepairDepotUpdateStatusDTO updateStatusDTO) {
        MtcRepairDepotInfo repairDepot = repairDepotRepository.selectById(updateStatusDTO.getId());
        if (repairDepot == null) {
            throw new BusinessException(-1, "修理厂信息不存在");
        }
        // 维修厂状态(0-无效 1-有效)
        int repairDepotStatus = updateStatusDTO.getRepairDepotStatus();
        if (repairDepotStatus == 0) {
            // 判断该维修厂是否有未完成的维修任务，无法关闭
            List<RepairTaskWorkflowDTO> repairTaskList = tableRepairTaskService.getCurrentRepairNum(new GetRepairNumDTO(null, repairDepot.getRepairDepotOrgId(), repairDepot.getRepairDepotId()));
            if (CollectionUtils.isNotEmpty(repairTaskList)) {
                throw new BusinessException("不能禁用，此维修厂还有维修任务没完成");
            }
        }

        repairDepot.setStatus(repairDepotStatus);
        repairDepotRepository.updateSelectiveById(repairDepot);
        // 记录日志
        MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
        mtcProcessLog.setRecordId(repairDepot.getId());
        mtcProcessLog.setOpeContent(StrUtil.format("修改了维修厂状态为{}", repairDepotStatus == 1 ? "启用" : "禁用"));
        mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_DEPOT_INFO);
        tableOperatorLogService.insertSelective(mtcProcessLog);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<String>> getRepairDepotCombo() {
        // 调用根据租户ID查询的方法，传入null表示使用当前登录用户的租户ID
        return getRepairDepotComboByTenantId(null);
    }

    @Override
    public List<ComboVO<String>> getAllRepairDepotCombo() {
        List<MtcRepairDepotInfo> depots = repairDepotRepository.findAllDepots();
        List<ComboVO<String>> result = new ArrayList<>();
        for (MtcRepairDepotInfo depot : depots) {
            ComboVO<String> vo = new ComboVO<>();
            vo.setId(depot.getRepairDepotId());
            vo.setValue(depot.getRepairDepotName());
            result.add(vo);
        }
        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<String>> getRepairDepotComboByTenantId(Long tenantId) {
        try {
            // 保存当前租户上下文
            Long currentTenantId = TenantContextHolder.getTenantId();
            String currentSchema = TenantContextHolder.getTenantSchema();

            try {
                // 如果传入的租户ID不为空，则设置租户上下文
                if (tenantId != null) {
                    log.debug("使用指定的租户ID查询修理厂: {}", tenantId);
                    TenantContextHolder.setTenant(tenantId);
                } else {
                    // 如果未传入租户ID，则使用当前登录用户的租户ID
                    log.debug("使用当前登录用户的租户ID查询修理厂");
                }

                String schema = TenantContextHolder.getTenantSchema();

                // 如果是默认租户（auto_care_saas），返回空列表，因为公共架构不包含修理厂数据
                if ("auto_care_saas".equals(schema)) {
                    log.debug("当前使用默认租户[{}]，不包含修理厂数据，返回空列表", schema);
                    return new ArrayList<>();
                }

                // 查询修理厂
                log.debug("正在获取租户[{}]的修理厂列表", schema);
                List<MtcRepairDepotInfo> depots = repairDepotRepository.findValidDepots();
                List<ComboVO<String>> result = new ArrayList<>();

                for (MtcRepairDepotInfo depot : depots) {
                    ComboVO<String> vo = new ComboVO<>();
                    vo.setId(depot.getRepairDepotId());
                    vo.setValue(depot.getRepairDepotName());
                    result.add(vo);
                }

                log.debug("成功获取租户[{}]的修理厂列表，共{}条数据", schema, result.size());
                return result;
            } finally {
                // 恢复原始租户上下文
                if (tenantId != null && !tenantId.equals(currentTenantId)) {
                    if (currentTenantId != null) {
                        TenantContextHolder.setTenant(currentTenantId);
                    } else {
                        TenantContextHolder.clear();
                    }
                    log.debug("已恢复原始租户上下文: {}", currentSchema);
                }
            }
        } catch (Exception e) {
            log.error("获取修理厂下拉列表失败", e);
            throw new RuntimeException("获取修理厂下拉列表失败");
        }
    }
}