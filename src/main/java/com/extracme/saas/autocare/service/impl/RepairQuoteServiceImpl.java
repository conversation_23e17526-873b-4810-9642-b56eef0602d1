package com.extracme.saas.autocare.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.extracme.saas.autocare.model.dto.FileDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.enums.RepairPicTypeEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.RepairQuoteSubmitDTO;
import com.extracme.saas.autocare.model.dto.RepairQuoteUpdateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.entity.MtcLossInfo;
import com.extracme.saas.autocare.model.entity.MtcRepairItemCheckInfo;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.repository.TableLossInfoService;
import com.extracme.saas.autocare.repository.TableRepairItemCheckInfoService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.RepairQuoteService;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 维修报价服务实现类
 */
@Slf4j
@Service
public class RepairQuoteServiceImpl implements RepairQuoteService {

    @Autowired
    private RepairTaskService repairTaskService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableLossInfoService tableLossInfoService;

    @Autowired
    private TableRepairItemCheckInfoService tableRepairItemCheckInfoService;

    @Autowired
    private WorkflowService workflowService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> saveRepairQuote(RepairQuoteUpdateDTO repairQuoteDTO) {
        log.info("保存维修报价信息: {}", repairQuoteDTO);

        // 获取维修任务
        MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(repairQuoteDTO.getTaskNo());
        if (mtcRepairTask == null) {
            throw new BusinessException("未找到维修任务: " + repairQuoteDTO.getTaskNo());
        }

        // 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairQuoteDTO.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + repairQuoteDTO.getTaskNo());
        }
        if (!workflowInstance.getCurrentActivityCode().equals("REPAIR_QUOTATION")
                && !workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new BusinessException("维修任务节点状态不是处理中, 无法保存报价信息: " + repairQuoteDTO.getTaskNo());
        }

        // 检查图片数量是否超出限制
        if (repairQuoteDTO.pictureSizeOutOfRange()) {
            throw new BusinessException("图片数量超出限制！");
        }

        // 处理预计修理天数和预计完成时间
        Date expectedRepairComplete = null;
        if (repairQuoteDTO.getExpectedRepairDays() != null && repairQuoteDTO.getExpectedRepairDays() > 0) {
            expectedRepairComplete = calculateExpectedRepairCompleteTime(mtcRepairTask, repairQuoteDTO.getExpectedRepairDays());
        }

        // 更新维修任务报价信息
        RepairTaskUpdateDTO updateDTO = new RepairTaskUpdateDTO();
        BeanUtils.copyProperties(repairQuoteDTO, updateDTO);
        updateDTO.setExpectedRepairComplete(expectedRepairComplete);
        // 保存维修任务
        repairTaskService.saveRepairTask(mtcRepairTask.getId(), updateDTO);

        // 保存图片逻辑
        saveRepairQuoteImages(repairQuoteDTO);

        return Result.success();
    }

    /**
     * 保存维修报价相关图片
     * 
     * @param repairQuoteDTO 维修报价DTO
     * @throws BusinessException 业务异常
     */
    private void saveRepairQuoteImages(RepairQuoteUpdateDTO repairQuoteDTO) {
        try {
            // 构建媒体类型映射
            Map<Integer, List<FileDTO>> mediaTypeMap = new HashMap<>();

            // 添加各种类型的图片到映射中
            if (repairQuoteDTO.getDamagedPartPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.DAMAGED_PART_PICTURE.getTypeId(),
                        repairQuoteDTO.getDamagedPartPicture());
            }

            if (repairQuoteDTO.getDamagedPartVideo() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.DAMAGED_PART_VIDEO.getTypeId(),
                        repairQuoteDTO.getDamagedPartVideo());
            }

            if (repairQuoteDTO.getAccidentLiabilityConfirmationPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.ACCIDENT_LIABILITY_CONFIRMATION_PICTURE.getTypeId(),
                        repairQuoteDTO.getAccidentLiabilityConfirmationPicture());
            }

            if (repairQuoteDTO.getInsuranceCompanyLossOrderPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.INSURANCE_COMPANY_LOSS_ORDER_PICTURE.getTypeId(),
                        repairQuoteDTO.getInsuranceCompanyLossOrderPicture());
            }

            if (repairQuoteDTO.getOurDriverLicensePicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.OUR_DRIVER_LICENSE_PICTURE.getTypeId(),
                        repairQuoteDTO.getOurDriverLicensePicture());
            }

            if (repairQuoteDTO.getCustPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.CUST_PICTURE.getTypeId(),
                        repairQuoteDTO.getCustPicture());
            }

            // 如果有图片需要处理，则调用processMediaFiles方法
            if (!mediaTypeMap.isEmpty()) {
                String operatorName = SessionUtils.getUsername();
                repairTaskService.processMediaFiles(repairQuoteDTO.getTaskNo(), mediaTypeMap, operatorName);
                log.info("维修报价图片保存成功，任务编号: {}", repairQuoteDTO.getTaskNo());
            }
        } catch (Exception e) {
            log.error("保存维修报价图片失败: {}", e.getMessage(), e);
            throw new BusinessException("保存维修报价图片失败: " + e.getMessage());
        }
    }

    /**
     * 计算预计维修完成时间
     * 
     * @param mtcRepairTask      维修任务对象
     * @param expectedRepairDays 预计修理天数
     */
    private Date calculateExpectedRepairCompleteTime(MtcRepairTask mtcRepairTask, Long expectedRepairDays) {
        Date vehicleReceiveTime = mtcRepairTask.getVehicleReciveTime();
        if (vehicleReceiveTime == null) {
            log.warn("维修任务车辆接收时间为空, 任务编号: {}", mtcRepairTask.getTaskNo());
            return null;
        }
        try {
            // 使用Java 8日期时间API计算预计完成时间
            LocalDateTime receiveDateTime = vehicleReceiveTime.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            LocalDateTime expectedCompleteDateTime = receiveDateTime
                    .plusDays(expectedRepairDays);
            Date expectedRepairComplete = Date.from(expectedCompleteDateTime
                    .atZone(ZoneId.systemDefault())
                    .toInstant());

            log.info("设置维修任务预计完成时间: {}, 任务编号: {}",
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(expectedCompleteDateTime),
                    mtcRepairTask.getTaskNo());
            return expectedRepairComplete;
        } catch (Exception e) {
            log.error("计算预计完成时间失败, 任务编号: {}, 错误: {}", mtcRepairTask.getTaskNo(), e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> submitRepairQuote(RepairQuoteSubmitDTO submitDTO) {
        log.info("提交维修报价审核: {}", submitDTO);

        // 1. 保存维修报价信息
        RepairQuoteUpdateDTO updateDTO = new RepairQuoteUpdateDTO();
        BeanUtils.copyProperties(submitDTO, updateDTO);
        saveRepairQuote(updateDTO);

        // 2. 获取维修任务 & 获取工作流实例
        MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(submitDTO.getTaskNo());
        if (null == mtcRepairTask) {
            throw new BusinessException("未找到维修任务: " + submitDTO.getTaskNo());
        }
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(submitDTO.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + submitDTO.getTaskNo());
        }

        // 3. 验证定损数据
        Result<Void> validateLossDataResult = validateLossData(submitDTO.getTaskNo(), mtcRepairTask.getPartsLibraryType());
        if (!validateLossDataResult.getCode().equals(200)) {
            return validateLossDataResult;
        }

        // 4. 调用工作流服务处理节点
        WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
        processDTO.setTriggerEvent("SUBMIT_QUOTE");
        processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
        processDTO.setOperator(SessionUtils.getUsername());

        try {
            workflowService.processNode(workflowInstance.getId(), processDTO);
            log.info("提交维修报价审核成功, 任务编号: {}", submitDTO.getTaskNo());
        } catch (Exception e) {
            log.error("提交维修报价审核失败: {}", e.getMessage(), e);
            throw new BusinessException("提交维修报价审核失败: " + e.getMessage());
        }

        // 5.更新自费金额
        repairTaskService.updateSelfFundedAmount(submitDTO.getTaskNo(), null);

        return Result.success();
    }

    /**
     * 验证定损数据
     *
     * @param taskNo           任务编号
     * @param partsLibraryType 配件库类型
     */
    private Result<Void> validateLossData(String taskNo, Integer partsLibraryType) {
        if (null == partsLibraryType) {
            throw new BusinessException("配件库类型数据缺失！任务编号: " + taskNo);
        }
        // 检查是否存在定损数据
        if (partsLibraryType == 1) {
            // 自有配件库
            List<MtcRepairItemCheckInfo> checkInfoList = tableRepairItemCheckInfoService.selectByTaskNo(taskNo);
            if (CollectionUtils.isEmpty(checkInfoList)) {
                return Result.error(-2372428, "未经过定损系统维修报价，请进行定损");
            }
        } else if (partsLibraryType == 2) {
            // 精友配件库
            List<MtcLossInfo> mtcLossInfoList = tableLossInfoService.selectByTaskNo(taskNo);
            if (CollectionUtils.isEmpty(mtcLossInfoList)) {
                return Result.error(-2372428, "未经过定损系统维修报价，请进行定损");
            }
        }

        return Result.success();
    }
}
