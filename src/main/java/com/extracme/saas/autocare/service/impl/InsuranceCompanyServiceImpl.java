package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyCreateDTO;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyQueryDTO;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyUpdateDTO;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyUpdateStatusDTO;
import com.extracme.saas.autocare.model.entity.AttachmentInfo;
import com.extracme.saas.autocare.model.entity.InsuranceCompanyInfo;
import com.extracme.saas.autocare.model.vo.InsuranceCompanyVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableAttachmentInfoService;
import com.extracme.saas.autocare.repository.TableInsuranceCompanyService;
import com.extracme.saas.autocare.service.InsuranceCompanyService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 保司信息服务实现类
 */
@Slf4j
@Service
public class InsuranceCompanyServiceImpl implements InsuranceCompanyService {

    @Autowired
    private TableInsuranceCompanyService tableInsuranceCompanyService;

    @Autowired
    private TableAttachmentInfoService tableAttachmentInfoService;

    @Override
    public BasePageVO<InsuranceCompanyVO> queryInsuranceCompanyList(InsuranceCompanyQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<InsuranceCompanyInfo> list = tableInsuranceCompanyService.queryInsuranceCompanyList(queryDTO);
        PageInfo<InsuranceCompanyInfo> pageInfo = new PageInfo<>(list);
        List<InsuranceCompanyVO> voList = list.stream().map(item -> {
            InsuranceCompanyVO vo = new InsuranceCompanyVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public InsuranceCompanyVO getInsuranceCompanyDetails(Long id) {
        if (id == null){
            return null;
        }
        InsuranceCompanyInfo insuranceCompanyInfo = tableInsuranceCompanyService.selectById(id);
        if (insuranceCompanyInfo != null) {
            InsuranceCompanyVO vo = new InsuranceCompanyVO();
            BeanUtils.copyProperties(insuranceCompanyInfo, vo);
            return vo;
        }
        return null;
    }

    @Override
    public void createInsuranceCompany(InsuranceCompanyCreateDTO createDTO) {
        // 判断名称是否存在
        InsuranceCompanyInfo insuranceCompanyInfo = tableInsuranceCompanyService.selectByCompanyName(createDTO.getCompanyName());
        if (insuranceCompanyInfo != null) {
            throw new BusinessException("保司名称已存在");
        }
        insuranceCompanyInfo = new InsuranceCompanyInfo();
        BeanUtils.copyProperties(createDTO, insuranceCompanyInfo);
        tableInsuranceCompanyService.insert(insuranceCompanyInfo);
    }

    @Override
    public void updateInsuranceCompany(InsuranceCompanyUpdateDTO updateDTO) {
        InsuranceCompanyInfo insuranceCompanyInfo = tableInsuranceCompanyService.selectById(updateDTO.getId());
        if (insuranceCompanyInfo == null) {
            throw new BusinessException("保司信息不存在");
        }
        if (!insuranceCompanyInfo.getCompanyName().equals(updateDTO.getCompanyName())){
            InsuranceCompanyInfo companyNameInfo = tableInsuranceCompanyService.selectByCompanyName(updateDTO.getCompanyName());
            if (companyNameInfo != null) {
                throw new BusinessException("保司名称已存在");
            }
        }
        insuranceCompanyInfo.setStatus(updateDTO.getStatus());
        insuranceCompanyInfo.setCompanyName(updateDTO.getCompanyName());
        insuranceCompanyInfo.setContactNumber(updateDTO.getContactNumber());
        insuranceCompanyInfo.setCompanyAbbreviation(updateDTO.getCompanyAbbreviation());
        tableInsuranceCompanyService.updateSelectiveById(insuranceCompanyInfo, SessionUtils.getLoginUser().getUsername());
    }

    @Override
    public void deleteInsuranceCompany(Long id) {
        tableInsuranceCompanyService.deleteById(id);
    }

    @Override
    public void updateInsuranceCompanyStatus(InsuranceCompanyUpdateStatusDTO updateStatusDTO) {
        InsuranceCompanyInfo insuranceCompanyInfo = tableInsuranceCompanyService.selectById(updateStatusDTO.getId());
        if (insuranceCompanyInfo == null) {
            throw new BusinessException("保司信息不存在");
        }
        insuranceCompanyInfo.setStatus(updateStatusDTO.getStatus());
        tableInsuranceCompanyService.updateSelectiveById(insuranceCompanyInfo, SessionUtils.getLoginUser().getUsername());
    }

    @Override
    public List<ComboVO<Integer>> getAllInsuranceCompanyCombo() {
        List<InsuranceCompanyInfo> list = tableInsuranceCompanyService.findAllInsuranceCompany();
        if (list != null && list.size() > 0) {
            return list.stream().map(item -> {
                ComboVO<Integer> vo = new ComboVO<>();
                vo.setId(item.getId().intValue());
                vo.setValue(item.getCompanyName());
                return vo;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public int insertAttachment(List<AttachmentInfo> attachmentInfoList) {
        for (AttachmentInfo info : attachmentInfoList) {
            tableAttachmentInfoService.insert(info);
        }
        return 0;
    }

    @Override
    public List<AttachmentInfo> queryAttachmentList(String foreignKey) {
        List<AttachmentInfo> attachmentInfoList = tableAttachmentInfoService.findAttachmentByForeignKey(foreignKey);
        return attachmentInfoList;
    }
}