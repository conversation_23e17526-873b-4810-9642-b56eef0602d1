package com.extracme.saas.autocare.service.sync;

import com.extracme.saas.autocare.model.dto.OrgInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.SyncDataResultDTO;
import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 机构信息同步策略
 */
@Slf4j
@Component
public class OrgInfoSyncStrategy implements SyncStrategy {

    private static final String TABLE_NAME = "mtc_org_info";

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Override
    public String getSupportedTable() {
        return TABLE_NAME;
    }

    @Override
    public SyncDataResultDTO syncData(String dataIdentifier, Object dataObject, Long tenantId, String tenantCode) {

        log.debug("开始同步机构信息数据，数据标识：{}，租户：{}", dataIdentifier, tenantCode);

        try {
            // 租户上下文已在批量同步开始时设置，这里不再重复设置

            // 转换DTO为实体对象
            OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO orgDTO =
                (OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO) dataObject;

            MtcOrgInfo orgInfo = convertDtoToEntity(orgDTO);

            // 根据ID或机构ID查询是否存在记录，自动判断操作类型
            MtcOrgInfo existingOrg = tableOrgInfoService.selectByUniqueId(orgDTO.getUniqueId());
            if (existingOrg == null) {
                // 如果没找到，再根据机构ID查询
                existingOrg = tableOrgInfoService.findByOrgId(orgInfo.getOrgId());
            }

            if (existingOrg != null) {
                // 存在记录，执行更新操作
                log.debug("机构记录已存在，执行更新操作，机构ID：{}", orgInfo.getOrgId());
                return handleUpdate(dataIdentifier, orgDTO, existingOrg);
            } else {
                // 不存在记录，执行新增操作
                log.debug("机构记录不存在，执行新增操作，机构ID：{}", orgInfo.getOrgId());
                return handleInsert(dataIdentifier, orgDTO);
            }

        } catch (Exception e) {
            log.error("同步机构信息数据失败：{}", e.getMessage(), e);
            return SyncDataResultDTO.failure("同步失败：" + e.getMessage());
        }
        // 移除 finally 块中的租户上下文清理，因为现在在批量同步结束时统一清理
    }

    @Override
    public SyncDataResultDTO syncData(String dataIdentifier, Object dataObject, Long tenantId, String tenantCode, String operator) {
        log.debug("开始同步机构信息数据（带操作人），数据标识：{}，租户：{}，操作人：{}", dataIdentifier, tenantCode, operator);

        try {
            // 转换DTO为实体对象
            OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO orgDTO =
                (OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO) dataObject;

            // 根据机构ID查询是否已存在记录
            MtcOrgInfo existingOrg = tableOrgInfoService.findByOrgId(orgDTO.getOrgId());

            if (existingOrg != null) {
                // 存在记录，执行更新操作
                log.debug("机构记录已存在，执行更新操作，机构ID：{}，名称：{}，操作人：{}",
                         orgDTO.getOrgId(), orgDTO.getOrgName(), operator);
                return handleUpdateWithOperator(dataIdentifier, orgDTO, existingOrg, operator);
            } else {
                // 不存在记录，执行新增操作
                log.debug("机构记录不存在，执行新增操作，机构ID：{}，名称：{}，操作人：{}",
                         orgDTO.getOrgId(), orgDTO.getOrgName(), operator);
                return handleInsertWithOperator(dataIdentifier, orgDTO, operator);
            }

        } catch (Exception e) {
            log.error("同步机构信息数据失败：{}", e.getMessage(), e);
            return SyncDataResultDTO.failure("同步失败：" + e.getMessage());
        }
    }

    /**
     * 将DTO转换为实体对象
     */
    private MtcOrgInfo convertDtoToEntity(OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO orgDTO) {
        MtcOrgInfo orgInfo = new MtcOrgInfo();

        // 复制基本字段
        BeanUtils.copyProperties(orgDTO, orgInfo);

        return orgInfo;
    }

    /**
     * 处理新增操作
     */
    private SyncDataResultDTO handleInsert(String dataIdentifier, OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO orgDTO) {
        try {
            MtcOrgInfo orgInfo = convertDtoToEntity(orgDTO);

            // 检查是否已存在相同机构ID的记录
            MtcOrgInfo existingOrg = tableOrgInfoService.findByOrgId(orgInfo.getOrgId());
            if (existingOrg != null) {
                return SyncDataResultDTO.failure("机构ID已存在：" + orgInfo.getOrgId(), SyncDataResultDTO.OperationType.INSERT);
            }

            // 清空ID，让数据库自动生成

            // 插入数据
            MtcOrgInfo savedOrg = tableOrgInfoService.insert(orgInfo);

            log.debug("成功新增机构信息，ID：{}，机构ID：{}，名称：{}",
                    savedOrg.getId(), savedOrg.getOrgId(), savedOrg.getOrgName());
            return SyncDataResultDTO.success(savedOrg.getId(), SyncDataResultDTO.OperationType.INSERT);

        } catch (Exception e) {
            log.error("新增机构信息失败：{}", e.getMessage(), e);
            return SyncDataResultDTO.failure("新增失败：" + e.getMessage(), SyncDataResultDTO.OperationType.INSERT);
        }
    }

    /**
     * 处理新增操作（带操作人参数）
     */
    private SyncDataResultDTO handleInsertWithOperator(String dataIdentifier, OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO orgDTO, String operator) {
        try {
            MtcOrgInfo orgInfo = convertDtoToEntity(orgDTO);

            // 检查是否已存在相同机构ID的记录
            MtcOrgInfo existingOrg = tableOrgInfoService.findByOrgId(orgInfo.getOrgId());
            if (existingOrg != null) {
                return SyncDataResultDTO.failure("机构ID已存在：" + orgInfo.getOrgId(), SyncDataResultDTO.OperationType.INSERT);
            }

            // 清空ID，让数据库自动生成

            // 插入数据，明确传入操作人
            MtcOrgInfo savedOrg = tableOrgInfoService.insert(orgInfo, operator);

            log.debug("成功新增机构信息，ID：{}，机构ID：{}，名称：{}，操作人：{}",
                    savedOrg.getId(), savedOrg.getOrgId(), savedOrg.getOrgName(), operator);
            return SyncDataResultDTO.success(savedOrg.getId(), SyncDataResultDTO.OperationType.INSERT);

        } catch (Exception e) {
            log.error("新增机构信息失败，操作人：{}，错误：{}", operator, e.getMessage(), e);
            return SyncDataResultDTO.failure("新增失败：" + e.getMessage(), SyncDataResultDTO.OperationType.INSERT);
        }
    }

    /**
     * 处理更新操作
     */
    private SyncDataResultDTO handleUpdate(String dataIdentifier, OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO orgDTO,
                                          MtcOrgInfo existingOrg) {
        try {
            // 转换DTO为实体对象
            MtcOrgInfo orgInfo = convertDtoToEntity(orgDTO);

            // 更新数据（保持原有ID）
            orgInfo.setId(existingOrg.getId());
            tableOrgInfoService.updateSelectiveById(orgInfo);

            log.debug("成功更新机构信息，ID：{}，机构ID：{}，名称：{}",
                    orgInfo.getId(), orgInfo.getOrgId(), orgInfo.getOrgName());
            return SyncDataResultDTO.success(orgInfo.getId(), SyncDataResultDTO.OperationType.UPDATE);

        } catch (Exception e) {
            log.error("更新机构信息失败：{}", e.getMessage(), e);
            return SyncDataResultDTO.failure("更新失败：" + e.getMessage(), SyncDataResultDTO.OperationType.UPDATE);
        }
    }

    /**
     * 处理更新操作（带操作人参数）
     */
    private SyncDataResultDTO handleUpdateWithOperator(String dataIdentifier, OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO orgDTO,
                                                      MtcOrgInfo existingOrg, String operator) {
        try {
            // 转换DTO为实体对象
            MtcOrgInfo orgInfo = convertDtoToEntity(orgDTO);

            // 更新数据（保持原有ID），明确传入操作人
            orgInfo.setId(existingOrg.getId());
            tableOrgInfoService.updateSelectiveById(orgInfo, operator);

            log.debug("成功更新机构信息，ID：{}，机构ID：{}，名称：{}，操作人：{}",
                    orgInfo.getId(), orgInfo.getOrgId(), orgInfo.getOrgName(), operator);
            return SyncDataResultDTO.success(orgInfo.getId(), SyncDataResultDTO.OperationType.UPDATE);

        } catch (Exception e) {
            log.error("更新机构信息失败，操作人：{}，错误：{}", operator, e.getMessage(), e);
            return SyncDataResultDTO.failure("更新失败：" + e.getMessage(), SyncDataResultDTO.OperationType.UPDATE);
        }
    }


}
