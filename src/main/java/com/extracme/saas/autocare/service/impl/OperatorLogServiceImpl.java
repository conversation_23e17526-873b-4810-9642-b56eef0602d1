package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.enums.OperateLogTableEnum;
import com.extracme.saas.autocare.model.dto.BusinessOperateLogQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.vo.BusinessOperateLogVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.service.OperatorLogService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class OperatorLogServiceImpl implements OperatorLogService {

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Override
    public BasePageVO<BusinessOperateLogVO> queryOperatorLog(BusinessOperateLogQueryDTO queryDTO) {
        OperateLogTableEnum tableEnum = OperateLogTableEnum.getByCode(queryDTO.getBusinessType());
        if (tableEnum == null) {
            return BasePageVO.of(null, new PageInfo<>());
        }
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<MtcOperatorLog> list = tableOperatorLogService.queryOperatorLog(queryDTO.getId(), tableEnum.getName());
        PageInfo<MtcOperatorLog> pageInfo = new PageInfo<>(list);
        List<BusinessOperateLogVO> voList = list.stream().map(item -> {
            BusinessOperateLogVO vo = new BusinessOperateLogVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordOperationLog(String tableName, Long recordId, String content, String currentActivityCode, String operator) {
        MtcOperatorLog log = new MtcOperatorLog();
        log.setTableName(tableName);
        log.setRecordId(recordId);
        log.setOpeContent(content);
        log.setCurrentActivityCode(currentActivityCode);
        tableOperatorLogService.insertSelective(log);
    }

    @Override
    public List<MtcOperatorLog> queryLogsByRecordId(Long recordId) {
        return tableOperatorLogService.selectByRecordId(recordId);
    }

    @Override
    public List<MtcOperatorLog> queryLogsByRecordIdAndTache(Long recordId, String currentTache) {
        return tableOperatorLogService.selectByRecordIdAndTache(recordId, currentTache);
    }
}