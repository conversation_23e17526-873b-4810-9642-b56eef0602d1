package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.SysUserDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.SysUser;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysUserMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    BasicColumn[] selectList = BasicColumn.columnList(id, tenantId, username, mobile, nickname, avatar, email, status, accountType, repairDepotId, approvalLevel, insuranceCompanyId, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<SysUser> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysUserResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.BIGINT),
        @Result(column="username", property="username", jdbcType=JdbcType.VARCHAR),
        @Result(column="mobile", property="mobile", jdbcType=JdbcType.VARCHAR),
        @Result(column="nickname", property="nickname", jdbcType=JdbcType.VARCHAR),
        @Result(column="avatar", property="avatar", jdbcType=JdbcType.VARCHAR),
        @Result(column="email", property="email", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="account_type", property="accountType", jdbcType=JdbcType.INTEGER),
        @Result(column="repair_depot_id", property="repairDepotId", jdbcType=JdbcType.VARCHAR),
        @Result(column="approval_level", property="approvalLevel", jdbcType=JdbcType.INTEGER),
        @Result(column="insurance_company_id", property="insuranceCompanyId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<SysUser> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysUserResult")
    Optional<SysUser> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default int insert(SysUser row) {
        return MyBatis3Utils.insert(this::insert, row, sysUser, c ->
            c.map(tenantId).toProperty("tenantId")
            .map(username).toProperty("username")
            .map(mobile).toProperty("mobile")
            .map(nickname).toProperty("nickname")
            .map(avatar).toProperty("avatar")
            .map(email).toProperty("email")
            .map(status).toProperty("status")
            .map(accountType).toProperty("accountType")
            .map(repairDepotId).toProperty("repairDepotId")
            .map(approvalLevel).toProperty("approvalLevel")
            .map(insuranceCompanyId).toProperty("insuranceCompanyId")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default int insertSelective(SysUser row) {
        return MyBatis3Utils.insert(this::insert, row, sysUser, c ->
            c.map(tenantId).toPropertyWhenPresent("tenantId", row::getTenantId)
            .map(username).toPropertyWhenPresent("username", row::getUsername)
            .map(mobile).toPropertyWhenPresent("mobile", row::getMobile)
            .map(nickname).toPropertyWhenPresent("nickname", row::getNickname)
            .map(avatar).toPropertyWhenPresent("avatar", row::getAvatar)
            .map(email).toPropertyWhenPresent("email", row::getEmail)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(accountType).toPropertyWhenPresent("accountType", row::getAccountType)
            .map(repairDepotId).toPropertyWhenPresent("repairDepotId", row::getRepairDepotId)
            .map(approvalLevel).toPropertyWhenPresent("approvalLevel", row::getApprovalLevel)
            .map(insuranceCompanyId).toPropertyWhenPresent("insuranceCompanyId", row::getInsuranceCompanyId)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default Optional<SysUser> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default List<SysUser> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default List<SysUser> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default Optional<SysUser> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysUser, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    static UpdateDSL<UpdateModel> updateAllColumns(SysUser row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(tenantId).equalTo(row::getTenantId)
                .set(username).equalTo(row::getUsername)
                .set(mobile).equalTo(row::getMobile)
                .set(nickname).equalTo(row::getNickname)
                .set(avatar).equalTo(row::getAvatar)
                .set(email).equalTo(row::getEmail)
                .set(status).equalTo(row::getStatus)
                .set(accountType).equalTo(row::getAccountType)
                .set(repairDepotId).equalTo(row::getRepairDepotId)
                .set(approvalLevel).equalTo(row::getApprovalLevel)
                .set(insuranceCompanyId).equalTo(row::getInsuranceCompanyId)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysUser row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(tenantId).equalToWhenPresent(row::getTenantId)
                .set(username).equalToWhenPresent(row::getUsername)
                .set(mobile).equalToWhenPresent(row::getMobile)
                .set(nickname).equalToWhenPresent(row::getNickname)
                .set(avatar).equalToWhenPresent(row::getAvatar)
                .set(email).equalToWhenPresent(row::getEmail)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(accountType).equalToWhenPresent(row::getAccountType)
                .set(repairDepotId).equalToWhenPresent(row::getRepairDepotId)
                .set(approvalLevel).equalToWhenPresent(row::getApprovalLevel)
                .set(insuranceCompanyId).equalToWhenPresent(row::getInsuranceCompanyId)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default int updateByPrimaryKey(SysUser row) {
        return update(c ->
            c.set(tenantId).equalTo(row::getTenantId)
            .set(username).equalTo(row::getUsername)
            .set(mobile).equalTo(row::getMobile)
            .set(nickname).equalTo(row::getNickname)
            .set(avatar).equalTo(row::getAvatar)
            .set(email).equalTo(row::getEmail)
            .set(status).equalTo(row::getStatus)
            .set(accountType).equalTo(row::getAccountType)
            .set(repairDepotId).equalTo(row::getRepairDepotId)
            .set(approvalLevel).equalTo(row::getApprovalLevel)
            .set(insuranceCompanyId).equalTo(row::getInsuranceCompanyId)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    default int updateByPrimaryKeySelective(SysUser row) {
        return update(c ->
            c.set(tenantId).equalToWhenPresent(row::getTenantId)
            .set(username).equalToWhenPresent(row::getUsername)
            .set(mobile).equalToWhenPresent(row::getMobile)
            .set(nickname).equalToWhenPresent(row::getNickname)
            .set(avatar).equalToWhenPresent(row::getAvatar)
            .set(email).equalToWhenPresent(row::getEmail)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(accountType).equalToWhenPresent(row::getAccountType)
            .set(repairDepotId).equalToWhenPresent(row::getRepairDepotId)
            .set(approvalLevel).equalToWhenPresent(row::getApprovalLevel)
            .set(insuranceCompanyId).equalToWhenPresent(row::getInsuranceCompanyId)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}