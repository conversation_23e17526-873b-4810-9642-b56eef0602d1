package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class WorkflowInstantProgressDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    public static final WorkflowInstantProgress workflowInstantProgress = new WorkflowInstantProgress();

    /**
     * Database Column Remarks:
     *   进度记录编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.id")
    public static final SqlColumn<Long> id = workflowInstantProgress.id;

    /**
     * Database Column Remarks:
     *   所属流程实例编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.instance_id")
    public static final SqlColumn<Long> instanceId = workflowInstantProgress.instanceId;

    /**
     * Database Column Remarks:
     *   活动节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.activity_code")
    public static final SqlColumn<String> activityCode = workflowInstantProgress.activityCode;

    /**
     * Database Column Remarks:
     *   节点状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.status_code")
    public static final SqlColumn<String> statusCode = workflowInstantProgress.statusCode;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.create_time")
    public static final SqlColumn<Date> createTime = workflowInstantProgress.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.create_by")
    public static final SqlColumn<String> createBy = workflowInstantProgress.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.update_time")
    public static final SqlColumn<Date> updateTime = workflowInstantProgress.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.update_by")
    public static final SqlColumn<String> updateBy = workflowInstantProgress.updateBy;

    /**
     * Database Column Remarks:
     *   备注说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instant_progress.remarks")
    public static final SqlColumn<String> remarks = workflowInstantProgress.remarks;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    public static final class WorkflowInstantProgress extends AliasableSqlTable<WorkflowInstantProgress> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> instanceId = column("instance_id", JDBCType.BIGINT);

        public final SqlColumn<String> activityCode = column("activity_code", JDBCType.VARCHAR);

        public final SqlColumn<String> statusCode = column("status_code", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<String> remarks = column("remarks", JDBCType.LONGVARCHAR);

        public WorkflowInstantProgress() {
            super("workflow_instant_progress", WorkflowInstantProgress::new);
        }
    }
}