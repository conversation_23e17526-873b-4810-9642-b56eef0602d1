package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SysOperateLogDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operate_log")
    public static final SysOperateLog sysOperateLog = new SysOperateLog();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.id")
    public static final SqlColumn<Long> id = sysOperateLog.id;

    /**
     * Database Column Remarks:
     *   租户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.tenant_id")
    public static final SqlColumn<Long> tenantId = sysOperateLog.tenantId;

    /**
     * Database Column Remarks:
     *   模块类型（1：用户管理 2：系统管理（角色、权限）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.module_type")
    public static final SqlColumn<Integer> moduleType = sysOperateLog.moduleType;

    /**
     * Database Column Remarks:
     *   操作类型（1：新增 2：修改 3：删除 4：禁用 5：启用 ）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.operate_type")
    public static final SqlColumn<Integer> operateType = sysOperateLog.operateType;

    /**
     * Database Column Remarks:
     *   操作内容描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.content")
    public static final SqlColumn<String> content = sysOperateLog.content;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.misc_desc")
    public static final SqlColumn<String> miscDesc = sysOperateLog.miscDesc;

    /**
     * Database Column Remarks:
     *   是否有效（0 无效 1 有效）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.status")
    public static final SqlColumn<Integer> status = sysOperateLog.status;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.create_time")
    public static final SqlColumn<Date> createTime = sysOperateLog.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.create_by")
    public static final SqlColumn<String> createBy = sysOperateLog.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.update_time")
    public static final SqlColumn<Date> updateTime = sysOperateLog.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operate_log.update_by")
    public static final SqlColumn<String> updateBy = sysOperateLog.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operate_log")
    public static final class SysOperateLog extends AliasableSqlTable<SysOperateLog> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> tenantId = column("tenant_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> moduleType = column("module_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> operateType = column("operate_type", JDBCType.INTEGER);

        public final SqlColumn<String> content = column("content", JDBCType.VARCHAR);

        public final SqlColumn<String> miscDesc = column("misc_desc", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public SysOperateLog() {
            super("sys_operate_log", SysOperateLog::new);
        }
    }
}