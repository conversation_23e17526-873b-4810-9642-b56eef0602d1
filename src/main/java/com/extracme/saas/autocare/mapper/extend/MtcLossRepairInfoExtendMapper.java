package com.extracme.saas.autocare.mapper.extend;

import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.apprHour;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.auditManpowerFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.auditRemark;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.checkState;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.clmTms;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.createBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.createdTime;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.evalHour;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.itemCoverCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.itemName;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.manpowerFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.manpowerRefFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.miscDesc;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.remark;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.repairId;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.repairLevelCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.repairLevelName;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.repairModeCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.repairUnitPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.selfConfigFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.status;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.taskNo;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.updateBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import org.apache.ibatis.annotations.Mapper;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoMapper;
import com.extracme.saas.autocare.model.entity.MtcLossRepairInfo;

@Mapper
@TenantSchema
public interface MtcLossRepairInfoExtendMapper extends MtcLossRepairInfoMapper {

    /**
     * 根据任务编号更新维修配件信息
     * @param row
     * @return
     */
    default int updateSelectiveByTaskNoAndStatus(MtcLossRepairInfo row) {
        return update(c -> c.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(repairId).equalToWhenPresent(row::getRepairId)
                .set(repairModeCode).equalToWhenPresent(row::getRepairModeCode)
                .set(itemName).equalToWhenPresent(row::getItemName)
                .set(manpowerFee).equalToWhenPresent(row::getManpowerFee)
                .set(manpowerRefFee).equalToWhenPresent(row::getManpowerRefFee)
                .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
                .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(clmTms).equalToWhenPresent(row::getClmTms)
                .set(evalHour).equalToWhenPresent(row::getEvalHour)
                .set(repairUnitPrice).equalToWhenPresent(row::getRepairUnitPrice)
                .set(repairLevelCode).equalToWhenPresent(row::getRepairLevelCode)
                .set(repairLevelName).equalToWhenPresent(row::getRepairLevelName)
                .set(auditManpowerFee).equalToWhenPresent(row::getAuditManpowerFee)
                .set(apprHour).equalToWhenPresent(row::getApprHour)
                .set(checkState).equalToWhenPresent(row::getCheckState)
                .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .where(taskNo, isEqualTo(row::getTaskNo))
                .and(status, isEqualTo(1))
                .and(repairId, isEqualTo(row::getRepairId)));
    }
}
