package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcRepairItemLibraryLocalDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcRepairItemLibraryLocal;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcRepairItemLibraryLocalMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    BasicColumn[] selectList = BasicColumn.columnList(id, itemId, itemNo, orgId, orgName, hourFeeLocalMarketPrice, hourFeeLocalHighestPrice, materialCostLocalMarketPrice, materialCostLocalHighestPrice, status, miscDesc, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcRepairItemLibraryLocal> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcRepairItemLibraryLocalResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="item_id", property="itemId", jdbcType=JdbcType.BIGINT),
        @Result(column="item_no", property="itemNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
        @Result(column="hour_fee_local_market_price", property="hourFeeLocalMarketPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="hour_fee_local_highest_price", property="hourFeeLocalHighestPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="material_cost_local_market_price", property="materialCostLocalMarketPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="material_cost_local_highest_price", property="materialCostLocalHighestPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_Desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcRepairItemLibraryLocal> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcRepairItemLibraryLocalResult")
    Optional<MtcRepairItemLibraryLocal> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcRepairItemLibraryLocal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcRepairItemLibraryLocal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default int insert(MtcRepairItemLibraryLocal row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairItemLibraryLocal, c ->
            c.map(itemId).toProperty("itemId")
            .map(itemNo).toProperty("itemNo")
            .map(orgId).toProperty("orgId")
            .map(orgName).toProperty("orgName")
            .map(hourFeeLocalMarketPrice).toProperty("hourFeeLocalMarketPrice")
            .map(hourFeeLocalHighestPrice).toProperty("hourFeeLocalHighestPrice")
            .map(materialCostLocalMarketPrice).toProperty("materialCostLocalMarketPrice")
            .map(materialCostLocalHighestPrice).toProperty("materialCostLocalHighestPrice")
            .map(status).toProperty("status")
            .map(miscDesc).toProperty("miscDesc")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default int insertSelective(MtcRepairItemLibraryLocal row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairItemLibraryLocal, c ->
            c.map(itemId).toPropertyWhenPresent("itemId", row::getItemId)
            .map(itemNo).toPropertyWhenPresent("itemNo", row::getItemNo)
            .map(orgId).toPropertyWhenPresent("orgId", row::getOrgId)
            .map(orgName).toPropertyWhenPresent("orgName", row::getOrgName)
            .map(hourFeeLocalMarketPrice).toPropertyWhenPresent("hourFeeLocalMarketPrice", row::getHourFeeLocalMarketPrice)
            .map(hourFeeLocalHighestPrice).toPropertyWhenPresent("hourFeeLocalHighestPrice", row::getHourFeeLocalHighestPrice)
            .map(materialCostLocalMarketPrice).toPropertyWhenPresent("materialCostLocalMarketPrice", row::getMaterialCostLocalMarketPrice)
            .map(materialCostLocalHighestPrice).toPropertyWhenPresent("materialCostLocalHighestPrice", row::getMaterialCostLocalHighestPrice)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default Optional<MtcRepairItemLibraryLocal> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcRepairItemLibraryLocal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default List<MtcRepairItemLibraryLocal> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcRepairItemLibraryLocal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default List<MtcRepairItemLibraryLocal> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcRepairItemLibraryLocal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default Optional<MtcRepairItemLibraryLocal> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcRepairItemLibraryLocal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcRepairItemLibraryLocal row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(itemId).equalTo(row::getItemId)
                .set(itemNo).equalTo(row::getItemNo)
                .set(orgId).equalTo(row::getOrgId)
                .set(orgName).equalTo(row::getOrgName)
                .set(hourFeeLocalMarketPrice).equalTo(row::getHourFeeLocalMarketPrice)
                .set(hourFeeLocalHighestPrice).equalTo(row::getHourFeeLocalHighestPrice)
                .set(materialCostLocalMarketPrice).equalTo(row::getMaterialCostLocalMarketPrice)
                .set(materialCostLocalHighestPrice).equalTo(row::getMaterialCostLocalHighestPrice)
                .set(status).equalTo(row::getStatus)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcRepairItemLibraryLocal row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(itemId).equalToWhenPresent(row::getItemId)
                .set(itemNo).equalToWhenPresent(row::getItemNo)
                .set(orgId).equalToWhenPresent(row::getOrgId)
                .set(orgName).equalToWhenPresent(row::getOrgName)
                .set(hourFeeLocalMarketPrice).equalToWhenPresent(row::getHourFeeLocalMarketPrice)
                .set(hourFeeLocalHighestPrice).equalToWhenPresent(row::getHourFeeLocalHighestPrice)
                .set(materialCostLocalMarketPrice).equalToWhenPresent(row::getMaterialCostLocalMarketPrice)
                .set(materialCostLocalHighestPrice).equalToWhenPresent(row::getMaterialCostLocalHighestPrice)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default int updateByPrimaryKey(MtcRepairItemLibraryLocal row) {
        return update(c ->
            c.set(itemId).equalTo(row::getItemId)
            .set(itemNo).equalTo(row::getItemNo)
            .set(orgId).equalTo(row::getOrgId)
            .set(orgName).equalTo(row::getOrgName)
            .set(hourFeeLocalMarketPrice).equalTo(row::getHourFeeLocalMarketPrice)
            .set(hourFeeLocalHighestPrice).equalTo(row::getHourFeeLocalHighestPrice)
            .set(materialCostLocalMarketPrice).equalTo(row::getMaterialCostLocalMarketPrice)
            .set(materialCostLocalHighestPrice).equalTo(row::getMaterialCostLocalHighestPrice)
            .set(status).equalTo(row::getStatus)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    default int updateByPrimaryKeySelective(MtcRepairItemLibraryLocal row) {
        return update(c ->
            c.set(itemId).equalToWhenPresent(row::getItemId)
            .set(itemNo).equalToWhenPresent(row::getItemNo)
            .set(orgId).equalToWhenPresent(row::getOrgId)
            .set(orgName).equalToWhenPresent(row::getOrgName)
            .set(hourFeeLocalMarketPrice).equalToWhenPresent(row::getHourFeeLocalMarketPrice)
            .set(hourFeeLocalHighestPrice).equalToWhenPresent(row::getHourFeeLocalHighestPrice)
            .set(materialCostLocalMarketPrice).equalToWhenPresent(row::getMaterialCostLocalMarketPrice)
            .set(materialCostLocalHighestPrice).equalToWhenPresent(row::getMaterialCostLocalHighestPrice)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}