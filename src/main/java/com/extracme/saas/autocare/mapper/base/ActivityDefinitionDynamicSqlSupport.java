package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ActivityDefinitionDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    public static final ActivityDefinition activityDefinition = new ActivityDefinition();

    /**
     * Database Column Remarks:
     *   活动节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.id")
    public static final SqlColumn<Long> id = activityDefinition.id;

    /**
     * Database Column Remarks:
     *   活动编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.activity_code")
    public static final SqlColumn<String> activityCode = activityDefinition.activityCode;

    /**
     * Database Column Remarks:
     *   活动名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.activity_name")
    public static final SqlColumn<String> activityName = activityDefinition.activityName;

    /**
     * Database Column Remarks:
     *   节点顺序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.sequence")
    public static final SqlColumn<Integer> sequence = activityDefinition.sequence;

    /**
     * Database Column Remarks:
     *   是否启用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.is_enabled")
    public static final SqlColumn<Integer> isEnabled = activityDefinition.isEnabled;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.create_time")
    public static final SqlColumn<Date> createTime = activityDefinition.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.create_by")
    public static final SqlColumn<String> createBy = activityDefinition.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.update_time")
    public static final SqlColumn<Date> updateTime = activityDefinition.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.update_by")
    public static final SqlColumn<String> updateBy = activityDefinition.updateBy;

    /**
     * Database Column Remarks:
     *   活动描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_definition.description")
    public static final SqlColumn<String> description = activityDefinition.description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    public static final class ActivityDefinition extends AliasableSqlTable<ActivityDefinition> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> activityCode = column("activity_code", JDBCType.VARCHAR);

        public final SqlColumn<String> activityName = column("activity_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> sequence = column("sequence", JDBCType.INTEGER);

        public final SqlColumn<Integer> isEnabled = column("is_enabled", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<String> description = column("description", JDBCType.LONGVARCHAR);

        public ActivityDefinition() {
            super("activity_definition", ActivityDefinition::new);
        }
    }
}