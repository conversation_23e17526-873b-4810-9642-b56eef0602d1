package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcApprovalLevelsDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    public static final MtcApprovalLevels mtcApprovalLevels = new MtcApprovalLevels();

    /**
     * Database Column Remarks:
     *   自增ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_approval_levels.id")
    public static final SqlColumn<Long> id = mtcApprovalLevels.id;

    /**
     * Database Column Remarks:
     *   审批层级（0：无，1：一级，2：二级，3：三级，4：四级）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_approval_levels.approval_level")
    public static final SqlColumn<Integer> approvalLevel = mtcApprovalLevels.approvalLevel;

    /**
     * Database Column Remarks:
     *   自审批金额（单位：元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_approval_levels.self_approval_amount")
    public static final SqlColumn<BigDecimal> selfApprovalAmount = mtcApprovalLevels.selfApprovalAmount;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_approval_levels.create_time")
    public static final SqlColumn<Date> createTime = mtcApprovalLevels.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_approval_levels.create_by")
    public static final SqlColumn<String> createBy = mtcApprovalLevels.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_approval_levels.update_time")
    public static final SqlColumn<Date> updateTime = mtcApprovalLevels.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_approval_levels.update_by")
    public static final SqlColumn<String> updateBy = mtcApprovalLevels.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    public static final class MtcApprovalLevels extends AliasableSqlTable<MtcApprovalLevels> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> approvalLevel = column("approval_level", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> selfApprovalAmount = column("self_approval_amount", JDBCType.DECIMAL);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public MtcApprovalLevels() {
            super("mtc_approval_levels", MtcApprovalLevels::new);
        }
    }
}