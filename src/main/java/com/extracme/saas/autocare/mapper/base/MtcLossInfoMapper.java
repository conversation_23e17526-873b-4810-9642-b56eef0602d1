package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcLossInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcLossInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNo, vehCertainCode, vehCertainName, vehGroupCode, groupName, vehBrandCode, brandName, selfConfigFlag, salvageFee, remnantFee, manageFee, evalPartSum, evalRepairSum, evalMateSum, selfPaySum, outerSum, derogationSum, sumLossAmount, handlerCode, remark, priceType, repairFacId, repairFacCode, repairFacType, factoryQualification, repairFacPhone, repairFacName, vinNo, engineNo, plateNo, enrolDate, selfEstiFlag, selfApproveFlag, insuranceCode, insuranceName, mixCode, vehicleSettingMode, modelMatchFlag, evalTypeCode, accidentCauseCode, clmTms, allLoseSum, allLoseRemainsSum, allLoseSalvSum, allLoseTotalSum, partDiscountPercent, engineType, fuelType, vehicleOrigin, vehicleType, auditSalvageFee, auditRemnantFee, auditPartSum, auditRepiarSum, auditMateSum, totalManageSum, auditSelfPaySum, auditOuterSum, auditDerogationSum, auditHandlerCode, auditRemark, totalSum, auditAllLoseSum, auditAllLoseRemainsSum, auditAllLoseSalvSum, auditAllLoseTotalSum, status, miscDesc, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcLossInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcLossInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_no", property="taskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="veh_certain_code", property="vehCertainCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="veh_certain_name", property="vehCertainName", jdbcType=JdbcType.VARCHAR),
        @Result(column="veh_group_code", property="vehGroupCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="group_name", property="groupName", jdbcType=JdbcType.VARCHAR),
        @Result(column="veh_brand_code", property="vehBrandCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="brand_name", property="brandName", jdbcType=JdbcType.VARCHAR),
        @Result(column="self_config_flag", property="selfConfigFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="salvage_fee", property="salvageFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="remnant_fee", property="remnantFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="manage_fee", property="manageFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="eval_part_sum", property="evalPartSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="eval_repair_sum", property="evalRepairSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="eval_mate_sum", property="evalMateSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="self_pay_sum", property="selfPaySum", jdbcType=JdbcType.DECIMAL),
        @Result(column="outer_sum", property="outerSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="derogation_sum", property="derogationSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="sum_loss_amount", property="sumLossAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="handler_code", property="handlerCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="price_type", property="priceType", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_fac_id", property="repairFacId", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_fac_code", property="repairFacCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_fac_type", property="repairFacType", jdbcType=JdbcType.VARCHAR),
        @Result(column="factory_qualification", property="factoryQualification", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_fac_phone", property="repairFacPhone", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_fac_name", property="repairFacName", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin_no", property="vinNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="engine_no", property="engineNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="plate_no", property="plateNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="enrol_date", property="enrolDate", jdbcType=JdbcType.VARCHAR),
        @Result(column="self_esti_flag", property="selfEstiFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="self_approve_flag", property="selfApproveFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="insurance_code", property="insuranceCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="insurance_name", property="insuranceName", jdbcType=JdbcType.VARCHAR),
        @Result(column="mix_code", property="mixCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_setting_mode", property="vehicleSettingMode", jdbcType=JdbcType.VARCHAR),
        @Result(column="model_match_flag", property="modelMatchFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="eval_type_code", property="evalTypeCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="accident_cause_code", property="accidentCauseCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="clm_tms", property="clmTms", jdbcType=JdbcType.VARCHAR),
        @Result(column="all_lose_sum", property="allLoseSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="all_lose_remains_sum", property="allLoseRemainsSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="all_lose_salv_sum", property="allLoseSalvSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="all_lose_total_sum", property="allLoseTotalSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="part_discount_percent", property="partDiscountPercent", jdbcType=JdbcType.DECIMAL),
        @Result(column="engine_type", property="engineType", jdbcType=JdbcType.VARCHAR),
        @Result(column="fuel_type", property="fuelType", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_origin", property="vehicleOrigin", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_type", property="vehicleType", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_salvage_fee", property="auditSalvageFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_remnant_fee", property="auditRemnantFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_part_sum", property="auditPartSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_repiar_sum", property="auditRepiarSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_mate_sum", property="auditMateSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="total_manage_sum", property="totalManageSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_self_pay_sum", property="auditSelfPaySum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_outer_sum", property="auditOuterSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_derogation_sum", property="auditDerogationSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_handler_code", property="auditHandlerCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_remark", property="auditRemark", jdbcType=JdbcType.VARCHAR),
        @Result(column="total_sum", property="totalSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_all_lose_sum", property="auditAllLoseSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_all_lose_remains_sum", property="auditAllLoseRemainsSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_all_lose_salv_sum", property="auditAllLoseSalvSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_all_lose_total_sum", property="auditAllLoseTotalSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_Desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcLossInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcLossInfoResult")
    Optional<MtcLossInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcLossInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcLossInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default int insert(MtcLossInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossInfo, c ->
            c.map(taskNo).toProperty("taskNo")
            .map(vehCertainCode).toProperty("vehCertainCode")
            .map(vehCertainName).toProperty("vehCertainName")
            .map(vehGroupCode).toProperty("vehGroupCode")
            .map(groupName).toProperty("groupName")
            .map(vehBrandCode).toProperty("vehBrandCode")
            .map(brandName).toProperty("brandName")
            .map(selfConfigFlag).toProperty("selfConfigFlag")
            .map(salvageFee).toProperty("salvageFee")
            .map(remnantFee).toProperty("remnantFee")
            .map(manageFee).toProperty("manageFee")
            .map(evalPartSum).toProperty("evalPartSum")
            .map(evalRepairSum).toProperty("evalRepairSum")
            .map(evalMateSum).toProperty("evalMateSum")
            .map(selfPaySum).toProperty("selfPaySum")
            .map(outerSum).toProperty("outerSum")
            .map(derogationSum).toProperty("derogationSum")
            .map(sumLossAmount).toProperty("sumLossAmount")
            .map(handlerCode).toProperty("handlerCode")
            .map(remark).toProperty("remark")
            .map(priceType).toProperty("priceType")
            .map(repairFacId).toProperty("repairFacId")
            .map(repairFacCode).toProperty("repairFacCode")
            .map(repairFacType).toProperty("repairFacType")
            .map(factoryQualification).toProperty("factoryQualification")
            .map(repairFacPhone).toProperty("repairFacPhone")
            .map(repairFacName).toProperty("repairFacName")
            .map(vinNo).toProperty("vinNo")
            .map(engineNo).toProperty("engineNo")
            .map(plateNo).toProperty("plateNo")
            .map(enrolDate).toProperty("enrolDate")
            .map(selfEstiFlag).toProperty("selfEstiFlag")
            .map(selfApproveFlag).toProperty("selfApproveFlag")
            .map(insuranceCode).toProperty("insuranceCode")
            .map(insuranceName).toProperty("insuranceName")
            .map(mixCode).toProperty("mixCode")
            .map(vehicleSettingMode).toProperty("vehicleSettingMode")
            .map(modelMatchFlag).toProperty("modelMatchFlag")
            .map(evalTypeCode).toProperty("evalTypeCode")
            .map(accidentCauseCode).toProperty("accidentCauseCode")
            .map(clmTms).toProperty("clmTms")
            .map(allLoseSum).toProperty("allLoseSum")
            .map(allLoseRemainsSum).toProperty("allLoseRemainsSum")
            .map(allLoseSalvSum).toProperty("allLoseSalvSum")
            .map(allLoseTotalSum).toProperty("allLoseTotalSum")
            .map(partDiscountPercent).toProperty("partDiscountPercent")
            .map(engineType).toProperty("engineType")
            .map(fuelType).toProperty("fuelType")
            .map(vehicleOrigin).toProperty("vehicleOrigin")
            .map(vehicleType).toProperty("vehicleType")
            .map(auditSalvageFee).toProperty("auditSalvageFee")
            .map(auditRemnantFee).toProperty("auditRemnantFee")
            .map(auditPartSum).toProperty("auditPartSum")
            .map(auditRepiarSum).toProperty("auditRepiarSum")
            .map(auditMateSum).toProperty("auditMateSum")
            .map(totalManageSum).toProperty("totalManageSum")
            .map(auditSelfPaySum).toProperty("auditSelfPaySum")
            .map(auditOuterSum).toProperty("auditOuterSum")
            .map(auditDerogationSum).toProperty("auditDerogationSum")
            .map(auditHandlerCode).toProperty("auditHandlerCode")
            .map(auditRemark).toProperty("auditRemark")
            .map(totalSum).toProperty("totalSum")
            .map(auditAllLoseSum).toProperty("auditAllLoseSum")
            .map(auditAllLoseRemainsSum).toProperty("auditAllLoseRemainsSum")
            .map(auditAllLoseSalvSum).toProperty("auditAllLoseSalvSum")
            .map(auditAllLoseTotalSum).toProperty("auditAllLoseTotalSum")
            .map(status).toProperty("status")
            .map(miscDesc).toProperty("miscDesc")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default int insertSelective(MtcLossInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossInfo, c ->
            c.map(taskNo).toPropertyWhenPresent("taskNo", row::getTaskNo)
            .map(vehCertainCode).toPropertyWhenPresent("vehCertainCode", row::getVehCertainCode)
            .map(vehCertainName).toPropertyWhenPresent("vehCertainName", row::getVehCertainName)
            .map(vehGroupCode).toPropertyWhenPresent("vehGroupCode", row::getVehGroupCode)
            .map(groupName).toPropertyWhenPresent("groupName", row::getGroupName)
            .map(vehBrandCode).toPropertyWhenPresent("vehBrandCode", row::getVehBrandCode)
            .map(brandName).toPropertyWhenPresent("brandName", row::getBrandName)
            .map(selfConfigFlag).toPropertyWhenPresent("selfConfigFlag", row::getSelfConfigFlag)
            .map(salvageFee).toPropertyWhenPresent("salvageFee", row::getSalvageFee)
            .map(remnantFee).toPropertyWhenPresent("remnantFee", row::getRemnantFee)
            .map(manageFee).toPropertyWhenPresent("manageFee", row::getManageFee)
            .map(evalPartSum).toPropertyWhenPresent("evalPartSum", row::getEvalPartSum)
            .map(evalRepairSum).toPropertyWhenPresent("evalRepairSum", row::getEvalRepairSum)
            .map(evalMateSum).toPropertyWhenPresent("evalMateSum", row::getEvalMateSum)
            .map(selfPaySum).toPropertyWhenPresent("selfPaySum", row::getSelfPaySum)
            .map(outerSum).toPropertyWhenPresent("outerSum", row::getOuterSum)
            .map(derogationSum).toPropertyWhenPresent("derogationSum", row::getDerogationSum)
            .map(sumLossAmount).toPropertyWhenPresent("sumLossAmount", row::getSumLossAmount)
            .map(handlerCode).toPropertyWhenPresent("handlerCode", row::getHandlerCode)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(priceType).toPropertyWhenPresent("priceType", row::getPriceType)
            .map(repairFacId).toPropertyWhenPresent("repairFacId", row::getRepairFacId)
            .map(repairFacCode).toPropertyWhenPresent("repairFacCode", row::getRepairFacCode)
            .map(repairFacType).toPropertyWhenPresent("repairFacType", row::getRepairFacType)
            .map(factoryQualification).toPropertyWhenPresent("factoryQualification", row::getFactoryQualification)
            .map(repairFacPhone).toPropertyWhenPresent("repairFacPhone", row::getRepairFacPhone)
            .map(repairFacName).toPropertyWhenPresent("repairFacName", row::getRepairFacName)
            .map(vinNo).toPropertyWhenPresent("vinNo", row::getVinNo)
            .map(engineNo).toPropertyWhenPresent("engineNo", row::getEngineNo)
            .map(plateNo).toPropertyWhenPresent("plateNo", row::getPlateNo)
            .map(enrolDate).toPropertyWhenPresent("enrolDate", row::getEnrolDate)
            .map(selfEstiFlag).toPropertyWhenPresent("selfEstiFlag", row::getSelfEstiFlag)
            .map(selfApproveFlag).toPropertyWhenPresent("selfApproveFlag", row::getSelfApproveFlag)
            .map(insuranceCode).toPropertyWhenPresent("insuranceCode", row::getInsuranceCode)
            .map(insuranceName).toPropertyWhenPresent("insuranceName", row::getInsuranceName)
            .map(mixCode).toPropertyWhenPresent("mixCode", row::getMixCode)
            .map(vehicleSettingMode).toPropertyWhenPresent("vehicleSettingMode", row::getVehicleSettingMode)
            .map(modelMatchFlag).toPropertyWhenPresent("modelMatchFlag", row::getModelMatchFlag)
            .map(evalTypeCode).toPropertyWhenPresent("evalTypeCode", row::getEvalTypeCode)
            .map(accidentCauseCode).toPropertyWhenPresent("accidentCauseCode", row::getAccidentCauseCode)
            .map(clmTms).toPropertyWhenPresent("clmTms", row::getClmTms)
            .map(allLoseSum).toPropertyWhenPresent("allLoseSum", row::getAllLoseSum)
            .map(allLoseRemainsSum).toPropertyWhenPresent("allLoseRemainsSum", row::getAllLoseRemainsSum)
            .map(allLoseSalvSum).toPropertyWhenPresent("allLoseSalvSum", row::getAllLoseSalvSum)
            .map(allLoseTotalSum).toPropertyWhenPresent("allLoseTotalSum", row::getAllLoseTotalSum)
            .map(partDiscountPercent).toPropertyWhenPresent("partDiscountPercent", row::getPartDiscountPercent)
            .map(engineType).toPropertyWhenPresent("engineType", row::getEngineType)
            .map(fuelType).toPropertyWhenPresent("fuelType", row::getFuelType)
            .map(vehicleOrigin).toPropertyWhenPresent("vehicleOrigin", row::getVehicleOrigin)
            .map(vehicleType).toPropertyWhenPresent("vehicleType", row::getVehicleType)
            .map(auditSalvageFee).toPropertyWhenPresent("auditSalvageFee", row::getAuditSalvageFee)
            .map(auditRemnantFee).toPropertyWhenPresent("auditRemnantFee", row::getAuditRemnantFee)
            .map(auditPartSum).toPropertyWhenPresent("auditPartSum", row::getAuditPartSum)
            .map(auditRepiarSum).toPropertyWhenPresent("auditRepiarSum", row::getAuditRepiarSum)
            .map(auditMateSum).toPropertyWhenPresent("auditMateSum", row::getAuditMateSum)
            .map(totalManageSum).toPropertyWhenPresent("totalManageSum", row::getTotalManageSum)
            .map(auditSelfPaySum).toPropertyWhenPresent("auditSelfPaySum", row::getAuditSelfPaySum)
            .map(auditOuterSum).toPropertyWhenPresent("auditOuterSum", row::getAuditOuterSum)
            .map(auditDerogationSum).toPropertyWhenPresent("auditDerogationSum", row::getAuditDerogationSum)
            .map(auditHandlerCode).toPropertyWhenPresent("auditHandlerCode", row::getAuditHandlerCode)
            .map(auditRemark).toPropertyWhenPresent("auditRemark", row::getAuditRemark)
            .map(totalSum).toPropertyWhenPresent("totalSum", row::getTotalSum)
            .map(auditAllLoseSum).toPropertyWhenPresent("auditAllLoseSum", row::getAuditAllLoseSum)
            .map(auditAllLoseRemainsSum).toPropertyWhenPresent("auditAllLoseRemainsSum", row::getAuditAllLoseRemainsSum)
            .map(auditAllLoseSalvSum).toPropertyWhenPresent("auditAllLoseSalvSum", row::getAuditAllLoseSalvSum)
            .map(auditAllLoseTotalSum).toPropertyWhenPresent("auditAllLoseTotalSum", row::getAuditAllLoseTotalSum)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default Optional<MtcLossInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcLossInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default List<MtcLossInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcLossInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default List<MtcLossInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcLossInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default Optional<MtcLossInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcLossInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcLossInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalTo(row::getTaskNo)
                .set(vehCertainCode).equalTo(row::getVehCertainCode)
                .set(vehCertainName).equalTo(row::getVehCertainName)
                .set(vehGroupCode).equalTo(row::getVehGroupCode)
                .set(groupName).equalTo(row::getGroupName)
                .set(vehBrandCode).equalTo(row::getVehBrandCode)
                .set(brandName).equalTo(row::getBrandName)
                .set(selfConfigFlag).equalTo(row::getSelfConfigFlag)
                .set(salvageFee).equalTo(row::getSalvageFee)
                .set(remnantFee).equalTo(row::getRemnantFee)
                .set(manageFee).equalTo(row::getManageFee)
                .set(evalPartSum).equalTo(row::getEvalPartSum)
                .set(evalRepairSum).equalTo(row::getEvalRepairSum)
                .set(evalMateSum).equalTo(row::getEvalMateSum)
                .set(selfPaySum).equalTo(row::getSelfPaySum)
                .set(outerSum).equalTo(row::getOuterSum)
                .set(derogationSum).equalTo(row::getDerogationSum)
                .set(sumLossAmount).equalTo(row::getSumLossAmount)
                .set(handlerCode).equalTo(row::getHandlerCode)
                .set(remark).equalTo(row::getRemark)
                .set(priceType).equalTo(row::getPriceType)
                .set(repairFacId).equalTo(row::getRepairFacId)
                .set(repairFacCode).equalTo(row::getRepairFacCode)
                .set(repairFacType).equalTo(row::getRepairFacType)
                .set(factoryQualification).equalTo(row::getFactoryQualification)
                .set(repairFacPhone).equalTo(row::getRepairFacPhone)
                .set(repairFacName).equalTo(row::getRepairFacName)
                .set(vinNo).equalTo(row::getVinNo)
                .set(engineNo).equalTo(row::getEngineNo)
                .set(plateNo).equalTo(row::getPlateNo)
                .set(enrolDate).equalTo(row::getEnrolDate)
                .set(selfEstiFlag).equalTo(row::getSelfEstiFlag)
                .set(selfApproveFlag).equalTo(row::getSelfApproveFlag)
                .set(insuranceCode).equalTo(row::getInsuranceCode)
                .set(insuranceName).equalTo(row::getInsuranceName)
                .set(mixCode).equalTo(row::getMixCode)
                .set(vehicleSettingMode).equalTo(row::getVehicleSettingMode)
                .set(modelMatchFlag).equalTo(row::getModelMatchFlag)
                .set(evalTypeCode).equalTo(row::getEvalTypeCode)
                .set(accidentCauseCode).equalTo(row::getAccidentCauseCode)
                .set(clmTms).equalTo(row::getClmTms)
                .set(allLoseSum).equalTo(row::getAllLoseSum)
                .set(allLoseRemainsSum).equalTo(row::getAllLoseRemainsSum)
                .set(allLoseSalvSum).equalTo(row::getAllLoseSalvSum)
                .set(allLoseTotalSum).equalTo(row::getAllLoseTotalSum)
                .set(partDiscountPercent).equalTo(row::getPartDiscountPercent)
                .set(engineType).equalTo(row::getEngineType)
                .set(fuelType).equalTo(row::getFuelType)
                .set(vehicleOrigin).equalTo(row::getVehicleOrigin)
                .set(vehicleType).equalTo(row::getVehicleType)
                .set(auditSalvageFee).equalTo(row::getAuditSalvageFee)
                .set(auditRemnantFee).equalTo(row::getAuditRemnantFee)
                .set(auditPartSum).equalTo(row::getAuditPartSum)
                .set(auditRepiarSum).equalTo(row::getAuditRepiarSum)
                .set(auditMateSum).equalTo(row::getAuditMateSum)
                .set(totalManageSum).equalTo(row::getTotalManageSum)
                .set(auditSelfPaySum).equalTo(row::getAuditSelfPaySum)
                .set(auditOuterSum).equalTo(row::getAuditOuterSum)
                .set(auditDerogationSum).equalTo(row::getAuditDerogationSum)
                .set(auditHandlerCode).equalTo(row::getAuditHandlerCode)
                .set(auditRemark).equalTo(row::getAuditRemark)
                .set(totalSum).equalTo(row::getTotalSum)
                .set(auditAllLoseSum).equalTo(row::getAuditAllLoseSum)
                .set(auditAllLoseRemainsSum).equalTo(row::getAuditAllLoseRemainsSum)
                .set(auditAllLoseSalvSum).equalTo(row::getAuditAllLoseSalvSum)
                .set(auditAllLoseTotalSum).equalTo(row::getAuditAllLoseTotalSum)
                .set(status).equalTo(row::getStatus)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcLossInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(vehCertainCode).equalToWhenPresent(row::getVehCertainCode)
                .set(vehCertainName).equalToWhenPresent(row::getVehCertainName)
                .set(vehGroupCode).equalToWhenPresent(row::getVehGroupCode)
                .set(groupName).equalToWhenPresent(row::getGroupName)
                .set(vehBrandCode).equalToWhenPresent(row::getVehBrandCode)
                .set(brandName).equalToWhenPresent(row::getBrandName)
                .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
                .set(salvageFee).equalToWhenPresent(row::getSalvageFee)
                .set(remnantFee).equalToWhenPresent(row::getRemnantFee)
                .set(manageFee).equalToWhenPresent(row::getManageFee)
                .set(evalPartSum).equalToWhenPresent(row::getEvalPartSum)
                .set(evalRepairSum).equalToWhenPresent(row::getEvalRepairSum)
                .set(evalMateSum).equalToWhenPresent(row::getEvalMateSum)
                .set(selfPaySum).equalToWhenPresent(row::getSelfPaySum)
                .set(outerSum).equalToWhenPresent(row::getOuterSum)
                .set(derogationSum).equalToWhenPresent(row::getDerogationSum)
                .set(sumLossAmount).equalToWhenPresent(row::getSumLossAmount)
                .set(handlerCode).equalToWhenPresent(row::getHandlerCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(priceType).equalToWhenPresent(row::getPriceType)
                .set(repairFacId).equalToWhenPresent(row::getRepairFacId)
                .set(repairFacCode).equalToWhenPresent(row::getRepairFacCode)
                .set(repairFacType).equalToWhenPresent(row::getRepairFacType)
                .set(factoryQualification).equalToWhenPresent(row::getFactoryQualification)
                .set(repairFacPhone).equalToWhenPresent(row::getRepairFacPhone)
                .set(repairFacName).equalToWhenPresent(row::getRepairFacName)
                .set(vinNo).equalToWhenPresent(row::getVinNo)
                .set(engineNo).equalToWhenPresent(row::getEngineNo)
                .set(plateNo).equalToWhenPresent(row::getPlateNo)
                .set(enrolDate).equalToWhenPresent(row::getEnrolDate)
                .set(selfEstiFlag).equalToWhenPresent(row::getSelfEstiFlag)
                .set(selfApproveFlag).equalToWhenPresent(row::getSelfApproveFlag)
                .set(insuranceCode).equalToWhenPresent(row::getInsuranceCode)
                .set(insuranceName).equalToWhenPresent(row::getInsuranceName)
                .set(mixCode).equalToWhenPresent(row::getMixCode)
                .set(vehicleSettingMode).equalToWhenPresent(row::getVehicleSettingMode)
                .set(modelMatchFlag).equalToWhenPresent(row::getModelMatchFlag)
                .set(evalTypeCode).equalToWhenPresent(row::getEvalTypeCode)
                .set(accidentCauseCode).equalToWhenPresent(row::getAccidentCauseCode)
                .set(clmTms).equalToWhenPresent(row::getClmTms)
                .set(allLoseSum).equalToWhenPresent(row::getAllLoseSum)
                .set(allLoseRemainsSum).equalToWhenPresent(row::getAllLoseRemainsSum)
                .set(allLoseSalvSum).equalToWhenPresent(row::getAllLoseSalvSum)
                .set(allLoseTotalSum).equalToWhenPresent(row::getAllLoseTotalSum)
                .set(partDiscountPercent).equalToWhenPresent(row::getPartDiscountPercent)
                .set(engineType).equalToWhenPresent(row::getEngineType)
                .set(fuelType).equalToWhenPresent(row::getFuelType)
                .set(vehicleOrigin).equalToWhenPresent(row::getVehicleOrigin)
                .set(vehicleType).equalToWhenPresent(row::getVehicleType)
                .set(auditSalvageFee).equalToWhenPresent(row::getAuditSalvageFee)
                .set(auditRemnantFee).equalToWhenPresent(row::getAuditRemnantFee)
                .set(auditPartSum).equalToWhenPresent(row::getAuditPartSum)
                .set(auditRepiarSum).equalToWhenPresent(row::getAuditRepiarSum)
                .set(auditMateSum).equalToWhenPresent(row::getAuditMateSum)
                .set(totalManageSum).equalToWhenPresent(row::getTotalManageSum)
                .set(auditSelfPaySum).equalToWhenPresent(row::getAuditSelfPaySum)
                .set(auditOuterSum).equalToWhenPresent(row::getAuditOuterSum)
                .set(auditDerogationSum).equalToWhenPresent(row::getAuditDerogationSum)
                .set(auditHandlerCode).equalToWhenPresent(row::getAuditHandlerCode)
                .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
                .set(totalSum).equalToWhenPresent(row::getTotalSum)
                .set(auditAllLoseSum).equalToWhenPresent(row::getAuditAllLoseSum)
                .set(auditAllLoseRemainsSum).equalToWhenPresent(row::getAuditAllLoseRemainsSum)
                .set(auditAllLoseSalvSum).equalToWhenPresent(row::getAuditAllLoseSalvSum)
                .set(auditAllLoseTotalSum).equalToWhenPresent(row::getAuditAllLoseTotalSum)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default int updateByPrimaryKey(MtcLossInfo row) {
        return update(c ->
            c.set(taskNo).equalTo(row::getTaskNo)
            .set(vehCertainCode).equalTo(row::getVehCertainCode)
            .set(vehCertainName).equalTo(row::getVehCertainName)
            .set(vehGroupCode).equalTo(row::getVehGroupCode)
            .set(groupName).equalTo(row::getGroupName)
            .set(vehBrandCode).equalTo(row::getVehBrandCode)
            .set(brandName).equalTo(row::getBrandName)
            .set(selfConfigFlag).equalTo(row::getSelfConfigFlag)
            .set(salvageFee).equalTo(row::getSalvageFee)
            .set(remnantFee).equalTo(row::getRemnantFee)
            .set(manageFee).equalTo(row::getManageFee)
            .set(evalPartSum).equalTo(row::getEvalPartSum)
            .set(evalRepairSum).equalTo(row::getEvalRepairSum)
            .set(evalMateSum).equalTo(row::getEvalMateSum)
            .set(selfPaySum).equalTo(row::getSelfPaySum)
            .set(outerSum).equalTo(row::getOuterSum)
            .set(derogationSum).equalTo(row::getDerogationSum)
            .set(sumLossAmount).equalTo(row::getSumLossAmount)
            .set(handlerCode).equalTo(row::getHandlerCode)
            .set(remark).equalTo(row::getRemark)
            .set(priceType).equalTo(row::getPriceType)
            .set(repairFacId).equalTo(row::getRepairFacId)
            .set(repairFacCode).equalTo(row::getRepairFacCode)
            .set(repairFacType).equalTo(row::getRepairFacType)
            .set(factoryQualification).equalTo(row::getFactoryQualification)
            .set(repairFacPhone).equalTo(row::getRepairFacPhone)
            .set(repairFacName).equalTo(row::getRepairFacName)
            .set(vinNo).equalTo(row::getVinNo)
            .set(engineNo).equalTo(row::getEngineNo)
            .set(plateNo).equalTo(row::getPlateNo)
            .set(enrolDate).equalTo(row::getEnrolDate)
            .set(selfEstiFlag).equalTo(row::getSelfEstiFlag)
            .set(selfApproveFlag).equalTo(row::getSelfApproveFlag)
            .set(insuranceCode).equalTo(row::getInsuranceCode)
            .set(insuranceName).equalTo(row::getInsuranceName)
            .set(mixCode).equalTo(row::getMixCode)
            .set(vehicleSettingMode).equalTo(row::getVehicleSettingMode)
            .set(modelMatchFlag).equalTo(row::getModelMatchFlag)
            .set(evalTypeCode).equalTo(row::getEvalTypeCode)
            .set(accidentCauseCode).equalTo(row::getAccidentCauseCode)
            .set(clmTms).equalTo(row::getClmTms)
            .set(allLoseSum).equalTo(row::getAllLoseSum)
            .set(allLoseRemainsSum).equalTo(row::getAllLoseRemainsSum)
            .set(allLoseSalvSum).equalTo(row::getAllLoseSalvSum)
            .set(allLoseTotalSum).equalTo(row::getAllLoseTotalSum)
            .set(partDiscountPercent).equalTo(row::getPartDiscountPercent)
            .set(engineType).equalTo(row::getEngineType)
            .set(fuelType).equalTo(row::getFuelType)
            .set(vehicleOrigin).equalTo(row::getVehicleOrigin)
            .set(vehicleType).equalTo(row::getVehicleType)
            .set(auditSalvageFee).equalTo(row::getAuditSalvageFee)
            .set(auditRemnantFee).equalTo(row::getAuditRemnantFee)
            .set(auditPartSum).equalTo(row::getAuditPartSum)
            .set(auditRepiarSum).equalTo(row::getAuditRepiarSum)
            .set(auditMateSum).equalTo(row::getAuditMateSum)
            .set(totalManageSum).equalTo(row::getTotalManageSum)
            .set(auditSelfPaySum).equalTo(row::getAuditSelfPaySum)
            .set(auditOuterSum).equalTo(row::getAuditOuterSum)
            .set(auditDerogationSum).equalTo(row::getAuditDerogationSum)
            .set(auditHandlerCode).equalTo(row::getAuditHandlerCode)
            .set(auditRemark).equalTo(row::getAuditRemark)
            .set(totalSum).equalTo(row::getTotalSum)
            .set(auditAllLoseSum).equalTo(row::getAuditAllLoseSum)
            .set(auditAllLoseRemainsSum).equalTo(row::getAuditAllLoseRemainsSum)
            .set(auditAllLoseSalvSum).equalTo(row::getAuditAllLoseSalvSum)
            .set(auditAllLoseTotalSum).equalTo(row::getAuditAllLoseTotalSum)
            .set(status).equalTo(row::getStatus)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    default int updateByPrimaryKeySelective(MtcLossInfo row) {
        return update(c ->
            c.set(taskNo).equalToWhenPresent(row::getTaskNo)
            .set(vehCertainCode).equalToWhenPresent(row::getVehCertainCode)
            .set(vehCertainName).equalToWhenPresent(row::getVehCertainName)
            .set(vehGroupCode).equalToWhenPresent(row::getVehGroupCode)
            .set(groupName).equalToWhenPresent(row::getGroupName)
            .set(vehBrandCode).equalToWhenPresent(row::getVehBrandCode)
            .set(brandName).equalToWhenPresent(row::getBrandName)
            .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
            .set(salvageFee).equalToWhenPresent(row::getSalvageFee)
            .set(remnantFee).equalToWhenPresent(row::getRemnantFee)
            .set(manageFee).equalToWhenPresent(row::getManageFee)
            .set(evalPartSum).equalToWhenPresent(row::getEvalPartSum)
            .set(evalRepairSum).equalToWhenPresent(row::getEvalRepairSum)
            .set(evalMateSum).equalToWhenPresent(row::getEvalMateSum)
            .set(selfPaySum).equalToWhenPresent(row::getSelfPaySum)
            .set(outerSum).equalToWhenPresent(row::getOuterSum)
            .set(derogationSum).equalToWhenPresent(row::getDerogationSum)
            .set(sumLossAmount).equalToWhenPresent(row::getSumLossAmount)
            .set(handlerCode).equalToWhenPresent(row::getHandlerCode)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(priceType).equalToWhenPresent(row::getPriceType)
            .set(repairFacId).equalToWhenPresent(row::getRepairFacId)
            .set(repairFacCode).equalToWhenPresent(row::getRepairFacCode)
            .set(repairFacType).equalToWhenPresent(row::getRepairFacType)
            .set(factoryQualification).equalToWhenPresent(row::getFactoryQualification)
            .set(repairFacPhone).equalToWhenPresent(row::getRepairFacPhone)
            .set(repairFacName).equalToWhenPresent(row::getRepairFacName)
            .set(vinNo).equalToWhenPresent(row::getVinNo)
            .set(engineNo).equalToWhenPresent(row::getEngineNo)
            .set(plateNo).equalToWhenPresent(row::getPlateNo)
            .set(enrolDate).equalToWhenPresent(row::getEnrolDate)
            .set(selfEstiFlag).equalToWhenPresent(row::getSelfEstiFlag)
            .set(selfApproveFlag).equalToWhenPresent(row::getSelfApproveFlag)
            .set(insuranceCode).equalToWhenPresent(row::getInsuranceCode)
            .set(insuranceName).equalToWhenPresent(row::getInsuranceName)
            .set(mixCode).equalToWhenPresent(row::getMixCode)
            .set(vehicleSettingMode).equalToWhenPresent(row::getVehicleSettingMode)
            .set(modelMatchFlag).equalToWhenPresent(row::getModelMatchFlag)
            .set(evalTypeCode).equalToWhenPresent(row::getEvalTypeCode)
            .set(accidentCauseCode).equalToWhenPresent(row::getAccidentCauseCode)
            .set(clmTms).equalToWhenPresent(row::getClmTms)
            .set(allLoseSum).equalToWhenPresent(row::getAllLoseSum)
            .set(allLoseRemainsSum).equalToWhenPresent(row::getAllLoseRemainsSum)
            .set(allLoseSalvSum).equalToWhenPresent(row::getAllLoseSalvSum)
            .set(allLoseTotalSum).equalToWhenPresent(row::getAllLoseTotalSum)
            .set(partDiscountPercent).equalToWhenPresent(row::getPartDiscountPercent)
            .set(engineType).equalToWhenPresent(row::getEngineType)
            .set(fuelType).equalToWhenPresent(row::getFuelType)
            .set(vehicleOrigin).equalToWhenPresent(row::getVehicleOrigin)
            .set(vehicleType).equalToWhenPresent(row::getVehicleType)
            .set(auditSalvageFee).equalToWhenPresent(row::getAuditSalvageFee)
            .set(auditRemnantFee).equalToWhenPresent(row::getAuditRemnantFee)
            .set(auditPartSum).equalToWhenPresent(row::getAuditPartSum)
            .set(auditRepiarSum).equalToWhenPresent(row::getAuditRepiarSum)
            .set(auditMateSum).equalToWhenPresent(row::getAuditMateSum)
            .set(totalManageSum).equalToWhenPresent(row::getTotalManageSum)
            .set(auditSelfPaySum).equalToWhenPresent(row::getAuditSelfPaySum)
            .set(auditOuterSum).equalToWhenPresent(row::getAuditOuterSum)
            .set(auditDerogationSum).equalToWhenPresent(row::getAuditDerogationSum)
            .set(auditHandlerCode).equalToWhenPresent(row::getAuditHandlerCode)
            .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
            .set(totalSum).equalToWhenPresent(row::getTotalSum)
            .set(auditAllLoseSum).equalToWhenPresent(row::getAuditAllLoseSum)
            .set(auditAllLoseRemainsSum).equalToWhenPresent(row::getAuditAllLoseRemainsSum)
            .set(auditAllLoseSalvSum).equalToWhenPresent(row::getAuditAllLoseSalvSum)
            .set(auditAllLoseTotalSum).equalToWhenPresent(row::getAuditAllLoseTotalSum)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}