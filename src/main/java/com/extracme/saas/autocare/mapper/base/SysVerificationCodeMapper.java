package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.SysVerificationCodeDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.SysVerificationCode;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysVerificationCodeMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    BasicColumn[] selectList = BasicColumn.columnList(id, mobile, code, type, failCount, status, expireTime, createdTime, updateTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<SysVerificationCode> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysVerificationCodeResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="mobile", property="mobile", jdbcType=JdbcType.VARCHAR),
        @Result(column="code", property="code", jdbcType=JdbcType.VARCHAR),
        @Result(column="type", property="type", jdbcType=JdbcType.VARCHAR),
        @Result(column="fail_count", property="failCount", jdbcType=JdbcType.INTEGER),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="expire_time", property="expireTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<SysVerificationCode> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysVerificationCodeResult")
    Optional<SysVerificationCode> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysVerificationCode, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysVerificationCode, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default int insert(SysVerificationCode row) {
        return MyBatis3Utils.insert(this::insert, row, sysVerificationCode, c ->
            c.map(mobile).toProperty("mobile")
            .map(code).toProperty("code")
            .map(type).toProperty("type")
            .map(failCount).toProperty("failCount")
            .map(status).toProperty("status")
            .map(expireTime).toProperty("expireTime")
            .map(createdTime).toProperty("createdTime")
            .map(updateTime).toProperty("updateTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default int insertSelective(SysVerificationCode row) {
        return MyBatis3Utils.insert(this::insert, row, sysVerificationCode, c ->
            c.map(mobile).toPropertyWhenPresent("mobile", row::getMobile)
            .map(code).toPropertyWhenPresent("code", row::getCode)
            .map(type).toPropertyWhenPresent("type", row::getType)
            .map(failCount).toPropertyWhenPresent("failCount", row::getFailCount)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(expireTime).toPropertyWhenPresent("expireTime", row::getExpireTime)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default Optional<SysVerificationCode> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysVerificationCode, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default List<SysVerificationCode> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysVerificationCode, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default List<SysVerificationCode> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysVerificationCode, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default Optional<SysVerificationCode> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysVerificationCode, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    static UpdateDSL<UpdateModel> updateAllColumns(SysVerificationCode row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(mobile).equalTo(row::getMobile)
                .set(code).equalTo(row::getCode)
                .set(type).equalTo(row::getType)
                .set(failCount).equalTo(row::getFailCount)
                .set(status).equalTo(row::getStatus)
                .set(expireTime).equalTo(row::getExpireTime)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateTime).equalTo(row::getUpdateTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysVerificationCode row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(mobile).equalToWhenPresent(row::getMobile)
                .set(code).equalToWhenPresent(row::getCode)
                .set(type).equalToWhenPresent(row::getType)
                .set(failCount).equalToWhenPresent(row::getFailCount)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(expireTime).equalToWhenPresent(row::getExpireTime)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default int updateByPrimaryKey(SysVerificationCode row) {
        return update(c ->
            c.set(mobile).equalTo(row::getMobile)
            .set(code).equalTo(row::getCode)
            .set(type).equalTo(row::getType)
            .set(failCount).equalTo(row::getFailCount)
            .set(status).equalTo(row::getStatus)
            .set(expireTime).equalTo(row::getExpireTime)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateTime).equalTo(row::getUpdateTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    default int updateByPrimaryKeySelective(SysVerificationCode row) {
        return update(c ->
            c.set(mobile).equalToWhenPresent(row::getMobile)
            .set(code).equalToWhenPresent(row::getCode)
            .set(type).equalToWhenPresent(row::getType)
            .set(failCount).equalToWhenPresent(row::getFailCount)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(expireTime).equalToWhenPresent(row::getExpireTime)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}