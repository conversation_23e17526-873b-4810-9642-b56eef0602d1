package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.WorkflowInstanceDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface WorkflowInstanceMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    BasicColumn[] selectList = BasicColumn.columnList(id, workflowId, businessId, tenantId, currentActivityCode, statusCode, createTime, createBy, updateTime, updateBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<WorkflowInstance> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="WorkflowInstanceResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="workflow_id", property="workflowId", jdbcType=JdbcType.BIGINT),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.VARCHAR),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.INTEGER),
        @Result(column="current_activity_code", property="currentActivityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="status_code", property="statusCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR)
    })
    List<WorkflowInstance> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("WorkflowInstanceResult")
    Optional<WorkflowInstance> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, workflowInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, workflowInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default int insert(WorkflowInstance row) {
        return MyBatis3Utils.insert(this::insert, row, workflowInstance, c ->
            c.map(workflowId).toProperty("workflowId")
            .map(businessId).toProperty("businessId")
            .map(tenantId).toProperty("tenantId")
            .map(currentActivityCode).toProperty("currentActivityCode")
            .map(statusCode).toProperty("statusCode")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default int insertSelective(WorkflowInstance row) {
        return MyBatis3Utils.insert(this::insert, row, workflowInstance, c ->
            c.map(workflowId).toPropertyWhenPresent("workflowId", row::getWorkflowId)
            .map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(tenantId).toPropertyWhenPresent("tenantId", row::getTenantId)
            .map(currentActivityCode).toPropertyWhenPresent("currentActivityCode", row::getCurrentActivityCode)
            .map(statusCode).toPropertyWhenPresent("statusCode", row::getStatusCode)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default Optional<WorkflowInstance> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, workflowInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default List<WorkflowInstance> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, workflowInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default List<WorkflowInstance> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, workflowInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default Optional<WorkflowInstance> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, workflowInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    static UpdateDSL<UpdateModel> updateAllColumns(WorkflowInstance row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(workflowId).equalTo(row::getWorkflowId)
                .set(businessId).equalTo(row::getBusinessId)
                .set(tenantId).equalTo(row::getTenantId)
                .set(currentActivityCode).equalTo(row::getCurrentActivityCode)
                .set(statusCode).equalTo(row::getStatusCode)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(WorkflowInstance row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(workflowId).equalToWhenPresent(row::getWorkflowId)
                .set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(tenantId).equalToWhenPresent(row::getTenantId)
                .set(currentActivityCode).equalToWhenPresent(row::getCurrentActivityCode)
                .set(statusCode).equalToWhenPresent(row::getStatusCode)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default int updateByPrimaryKey(WorkflowInstance row) {
        return update(c ->
            c.set(workflowId).equalTo(row::getWorkflowId)
            .set(businessId).equalTo(row::getBusinessId)
            .set(tenantId).equalTo(row::getTenantId)
            .set(currentActivityCode).equalTo(row::getCurrentActivityCode)
            .set(statusCode).equalTo(row::getStatusCode)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    default int updateByPrimaryKeySelective(WorkflowInstance row) {
        return update(c ->
            c.set(workflowId).equalToWhenPresent(row::getWorkflowId)
            .set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(tenantId).equalToWhenPresent(row::getTenantId)
            .set(currentActivityCode).equalToWhenPresent(row::getCurrentActivityCode)
            .set(statusCode).equalToWhenPresent(row::getStatusCode)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }
}