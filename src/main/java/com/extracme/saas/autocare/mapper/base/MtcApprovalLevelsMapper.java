package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcApprovalLevelsDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcApprovalLevels;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcApprovalLevelsMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    BasicColumn[] selectList = BasicColumn.columnList(id, approvalLevel, selfApprovalAmount, createTime, createBy, updateTime, updateBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcApprovalLevels> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcApprovalLevelsResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="approval_level", property="approvalLevel", jdbcType=JdbcType.INTEGER),
        @Result(column="self_approval_amount", property="selfApprovalAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR)
    })
    List<MtcApprovalLevels> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcApprovalLevelsResult")
    Optional<MtcApprovalLevels> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcApprovalLevels, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcApprovalLevels, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default int insert(MtcApprovalLevels row) {
        return MyBatis3Utils.insert(this::insert, row, mtcApprovalLevels, c ->
            c.map(approvalLevel).toProperty("approvalLevel")
            .map(selfApprovalAmount).toProperty("selfApprovalAmount")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default int insertSelective(MtcApprovalLevels row) {
        return MyBatis3Utils.insert(this::insert, row, mtcApprovalLevels, c ->
            c.map(approvalLevel).toPropertyWhenPresent("approvalLevel", row::getApprovalLevel)
            .map(selfApprovalAmount).toPropertyWhenPresent("selfApprovalAmount", row::getSelfApprovalAmount)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default Optional<MtcApprovalLevels> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcApprovalLevels, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default List<MtcApprovalLevels> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcApprovalLevels, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default List<MtcApprovalLevels> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcApprovalLevels, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default Optional<MtcApprovalLevels> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcApprovalLevels, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcApprovalLevels row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(approvalLevel).equalTo(row::getApprovalLevel)
                .set(selfApprovalAmount).equalTo(row::getSelfApprovalAmount)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcApprovalLevels row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(approvalLevel).equalToWhenPresent(row::getApprovalLevel)
                .set(selfApprovalAmount).equalToWhenPresent(row::getSelfApprovalAmount)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default int updateByPrimaryKey(MtcApprovalLevels row) {
        return update(c ->
            c.set(approvalLevel).equalTo(row::getApprovalLevel)
            .set(selfApprovalAmount).equalTo(row::getSelfApprovalAmount)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_approval_levels")
    default int updateByPrimaryKeySelective(MtcApprovalLevels row) {
        return update(c ->
            c.set(approvalLevel).equalToWhenPresent(row::getApprovalLevel)
            .set(selfApprovalAmount).equalToWhenPresent(row::getSelfApprovalAmount)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }
}