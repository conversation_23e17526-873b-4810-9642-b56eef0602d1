package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcLossOuterRepairInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    public static final MtcLossOuterRepairInfo mtcLossOuterRepairInfo = new MtcLossOuterRepairInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.id")
    public static final SqlColumn<Long> id = mtcLossOuterRepairInfo.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.task_no")
    public static final SqlColumn<String> taskNo = mtcLossOuterRepairInfo.taskNo;

    /**
     * Database Column Remarks:
     *   外修项目主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.outer_id")
    public static final SqlColumn<String> outerId = mtcLossOuterRepairInfo.outerId;

    /**
     * Database Column Remarks:
     *   外修项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.outer_name")
    public static final SqlColumn<String> outerName = mtcLossOuterRepairInfo.outerName;

    /**
     * Database Column Remarks:
     *   自定义标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_handadd_flag")
    public static final SqlColumn<String> repairHandaddFlag = mtcLossOuterRepairInfo.repairHandaddFlag;

    /**
     * Database Column Remarks:
     *   外修项目定损金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.eval_outer_pirce")
    public static final SqlColumn<BigDecimal> evalOuterPirce = mtcLossOuterRepairInfo.evalOuterPirce;

    /**
     * Database Column Remarks:
     *   外修项目减损金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_price")
    public static final SqlColumn<BigDecimal> derogationPrice = mtcLossOuterRepairInfo.derogationPrice;

    /**
     * Database Column Remarks:
     *   配件项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_item_name")
    public static final SqlColumn<String> derogationItemName = mtcLossOuterRepairInfo.derogationItemName;

    /**
     * Database Column Remarks:
     *   配件零件号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_item_code")
    public static final SqlColumn<String> derogationItemCode = mtcLossOuterRepairInfo.derogationItemCode;

    /**
     * Database Column Remarks:
     *   配件价格类型（1：4S店价 2：市场原厂价 99：其他）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.derogation_price_type")
    public static final SqlColumn<String> derogationPriceType = mtcLossOuterRepairInfo.derogationPriceType;

    /**
     * Database Column Remarks:
     *   配件金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.part_price")
    public static final SqlColumn<BigDecimal> partPrice = mtcLossOuterRepairInfo.partPrice;

    /**
     * Database Column Remarks:
     *   外修修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_id")
    public static final SqlColumn<String> repairFactoryId = mtcLossOuterRepairInfo.repairFactoryId;

    /**
     * Database Column Remarks:
     *   外修修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_name")
    public static final SqlColumn<String> repairFactoryName = mtcLossOuterRepairInfo.repairFactoryName;

    /**
     * Database Column Remarks:
     *   外修修理厂代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_factory_code")
    public static final SqlColumn<String> repairFactoryCode = mtcLossOuterRepairInfo.repairFactoryCode;

    /**
     * Database Column Remarks:
     *   险种代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.item_cover_code")
    public static final SqlColumn<String> itemCoverCode = mtcLossOuterRepairInfo.itemCoverCode;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.remark")
    public static final SqlColumn<String> remark = mtcLossOuterRepairInfo.remark;

    /**
     * Database Column Remarks:
     *   外修配件数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.part_amount")
    public static final SqlColumn<Integer> partAmount = mtcLossOuterRepairInfo.partAmount;

    /**
     * Database Column Remarks:
     *   外修费用小计金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.repair_outer_sum")
    public static final SqlColumn<BigDecimal> repairOuterSum = mtcLossOuterRepairInfo.repairOuterSum;

    /**
     * Database Column Remarks:
     *   外修配件参考价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.reference_part_price")
    public static final SqlColumn<BigDecimal> referencePartPrice = mtcLossOuterRepairInfo.referencePartPrice;

    /**
     * Database Column Remarks:
     *   外修数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.out_item_amount")
    public static final SqlColumn<BigDecimal> outItemAmount = mtcLossOuterRepairInfo.outItemAmount;

    /**
     * Database Column Remarks:
     *   重开次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.clm_tms")
    public static final SqlColumn<String> clmTms = mtcLossOuterRepairInfo.clmTms;

    /**
     * Database Column Remarks:
     *   参考工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.reference_price")
    public static final SqlColumn<BigDecimal> referencePrice = mtcLossOuterRepairInfo.referencePrice;

    /**
     * Database Column Remarks:
     *   自定义标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_handadd_flag")
    public static final SqlColumn<String> auditRepairHandaddFlag = mtcLossOuterRepairInfo.auditRepairHandaddFlag;

    /**
     * Database Column Remarks:
     *   外修项目核损金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_eval_outer_pirce")
    public static final SqlColumn<BigDecimal> auditEvalOuterPirce = mtcLossOuterRepairInfo.auditEvalOuterPirce;

    /**
     * Database Column Remarks:
     *   外修项目减损金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_price")
    public static final SqlColumn<BigDecimal> auditDerogationPrice = mtcLossOuterRepairInfo.auditDerogationPrice;

    /**
     * Database Column Remarks:
     *   配件项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_item_name")
    public static final SqlColumn<String> auditDerogationItemName = mtcLossOuterRepairInfo.auditDerogationItemName;

    /**
     * Database Column Remarks:
     *   配件零件号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_item_code")
    public static final SqlColumn<String> auditDerogationItemCode = mtcLossOuterRepairInfo.auditDerogationItemCode;

    /**
     * Database Column Remarks:
     *   配件价格类型（1：4S店价 2：市场原厂价 99：其他）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_derogation_price_type")
    public static final SqlColumn<String> auditDerogationPriceType = mtcLossOuterRepairInfo.auditDerogationPriceType;

    /**
     * Database Column Remarks:
     *   换件金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_part_price")
    public static final SqlColumn<BigDecimal> auditPartPrice = mtcLossOuterRepairInfo.auditPartPrice;

    /**
     * Database Column Remarks:
     *   外修修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_id")
    public static final SqlColumn<String> auditRepairFactoryId = mtcLossOuterRepairInfo.auditRepairFactoryId;

    /**
     * Database Column Remarks:
     *   外修修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_name")
    public static final SqlColumn<String> auditRepairFactoryName = mtcLossOuterRepairInfo.auditRepairFactoryName;

    /**
     * Database Column Remarks:
     *   外修修理厂代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_factory_code")
    public static final SqlColumn<String> auditRepairFactoryCode = mtcLossOuterRepairInfo.auditRepairFactoryCode;

    /**
     * Database Column Remarks:
     *   险种代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_item_cover_code")
    public static final SqlColumn<String> auditItemCoverCode = mtcLossOuterRepairInfo.auditItemCoverCode;

    /**
     * Database Column Remarks:
     *   核损状态（00：待处理 01：通过 02：价格异议 03：建议剔除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.check_state")
    public static final SqlColumn<String> checkState = mtcLossOuterRepairInfo.checkState;

    /**
     * Database Column Remarks:
     *   核损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_remark")
    public static final SqlColumn<String> auditRemark = mtcLossOuterRepairInfo.auditRemark;

    /**
     * Database Column Remarks:
     *   外修费用小计金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_repair_outer_sum")
    public static final SqlColumn<BigDecimal> auditRepairOuterSum = mtcLossOuterRepairInfo.auditRepairOuterSum;

    /**
     * Database Column Remarks:
     *   外修配件参考价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_reference_part_price")
    public static final SqlColumn<BigDecimal> auditReferencePartPrice = mtcLossOuterRepairInfo.auditReferencePartPrice;

    /**
     * Database Column Remarks:
     *   外修数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.audit_out_item_amount")
    public static final SqlColumn<BigDecimal> auditOutItemAmount = mtcLossOuterRepairInfo.auditOutItemAmount;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.status")
    public static final SqlColumn<Integer> status = mtcLossOuterRepairInfo.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.misc_Desc")
    public static final SqlColumn<String> miscDesc = mtcLossOuterRepairInfo.miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.create_by")
    public static final SqlColumn<String> createBy = mtcLossOuterRepairInfo.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.created_time")
    public static final SqlColumn<Date> createdTime = mtcLossOuterRepairInfo.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.update_by")
    public static final SqlColumn<String> updateBy = mtcLossOuterRepairInfo.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_outer_repair_info.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcLossOuterRepairInfo.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    public static final class MtcLossOuterRepairInfo extends AliasableSqlTable<MtcLossOuterRepairInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<String> outerId = column("outer_id", JDBCType.VARCHAR);

        public final SqlColumn<String> outerName = column("outer_name", JDBCType.VARCHAR);

        public final SqlColumn<String> repairHandaddFlag = column("repair_handadd_flag", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> evalOuterPirce = column("eval_outer_pirce", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> derogationPrice = column("derogation_price", JDBCType.DECIMAL);

        public final SqlColumn<String> derogationItemName = column("derogation_item_name", JDBCType.VARCHAR);

        public final SqlColumn<String> derogationItemCode = column("derogation_item_code", JDBCType.VARCHAR);

        public final SqlColumn<String> derogationPriceType = column("derogation_price_type", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> partPrice = column("part_price", JDBCType.DECIMAL);

        public final SqlColumn<String> repairFactoryId = column("repair_factory_id", JDBCType.VARCHAR);

        public final SqlColumn<String> repairFactoryName = column("repair_factory_name", JDBCType.VARCHAR);

        public final SqlColumn<String> repairFactoryCode = column("repair_factory_code", JDBCType.VARCHAR);

        public final SqlColumn<String> itemCoverCode = column("item_cover_code", JDBCType.VARCHAR);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> partAmount = column("part_amount", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> repairOuterSum = column("repair_outer_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> referencePartPrice = column("reference_part_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> outItemAmount = column("out_item_amount", JDBCType.DECIMAL);

        public final SqlColumn<String> clmTms = column("clm_tms", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> referencePrice = column("reference_price", JDBCType.DECIMAL);

        public final SqlColumn<String> auditRepairHandaddFlag = column("audit_repair_handadd_flag", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> auditEvalOuterPirce = column("audit_eval_outer_pirce", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditDerogationPrice = column("audit_derogation_price", JDBCType.DECIMAL);

        public final SqlColumn<String> auditDerogationItemName = column("audit_derogation_item_name", JDBCType.VARCHAR);

        public final SqlColumn<String> auditDerogationItemCode = column("audit_derogation_item_code", JDBCType.VARCHAR);

        public final SqlColumn<String> auditDerogationPriceType = column("audit_derogation_price_type", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> auditPartPrice = column("audit_part_price", JDBCType.DECIMAL);

        public final SqlColumn<String> auditRepairFactoryId = column("audit_repair_factory_id", JDBCType.VARCHAR);

        public final SqlColumn<String> auditRepairFactoryName = column("audit_repair_factory_name", JDBCType.VARCHAR);

        public final SqlColumn<String> auditRepairFactoryCode = column("audit_repair_factory_code", JDBCType.VARCHAR);

        public final SqlColumn<String> auditItemCoverCode = column("audit_item_cover_code", JDBCType.VARCHAR);

        public final SqlColumn<String> checkState = column("check_state", JDBCType.VARCHAR);

        public final SqlColumn<String> auditRemark = column("audit_remark", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> auditRepairOuterSum = column("audit_repair_outer_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditReferencePartPrice = column("audit_reference_part_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditOutItemAmount = column("audit_out_item_amount", JDBCType.DECIMAL);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_Desc", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcLossOuterRepairInfo() {
            super("mtc_loss_outer_repair_info", MtcLossOuterRepairInfo::new);
        }
    }
}