package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcLossOuterRepairInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcLossOuterRepairInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNo, outerId, outerName, repairHandaddFlag, evalOuterPirce, derogationPrice, derogationItemName, derogationItemCode, derogationPriceType, partPrice, repairFactoryId, repairFactoryName, repairFactoryCode, itemCoverCode, remark, partAmount, repairOuterSum, referencePartPrice, outItemAmount, clmTms, referencePrice, auditRepairHandaddFlag, auditEvalOuterPirce, auditDerogationPrice, auditDerogationItemName, auditDerogationItemCode, auditDerogationPriceType, auditPartPrice, auditRepairFactoryId, auditRepairFactoryName, auditRepairFactoryCode, auditItemCoverCode, checkState, auditRemark, auditRepairOuterSum, auditReferencePartPrice, auditOutItemAmount, status, miscDesc, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcLossOuterRepairInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcLossOuterRepairInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_no", property="taskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="outer_id", property="outerId", jdbcType=JdbcType.VARCHAR),
        @Result(column="outer_name", property="outerName", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_handadd_flag", property="repairHandaddFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="eval_outer_pirce", property="evalOuterPirce", jdbcType=JdbcType.DECIMAL),
        @Result(column="derogation_price", property="derogationPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="derogation_item_name", property="derogationItemName", jdbcType=JdbcType.VARCHAR),
        @Result(column="derogation_item_code", property="derogationItemCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="derogation_price_type", property="derogationPriceType", jdbcType=JdbcType.VARCHAR),
        @Result(column="part_price", property="partPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="repair_factory_id", property="repairFactoryId", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_factory_name", property="repairFactoryName", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_factory_code", property="repairFactoryCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_cover_code", property="itemCoverCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="part_amount", property="partAmount", jdbcType=JdbcType.INTEGER),
        @Result(column="repair_outer_sum", property="repairOuterSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="reference_part_price", property="referencePartPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="out_item_amount", property="outItemAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="clm_tms", property="clmTms", jdbcType=JdbcType.VARCHAR),
        @Result(column="reference_price", property="referencePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_repair_handadd_flag", property="auditRepairHandaddFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_eval_outer_pirce", property="auditEvalOuterPirce", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_derogation_price", property="auditDerogationPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_derogation_item_name", property="auditDerogationItemName", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_derogation_item_code", property="auditDerogationItemCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_derogation_price_type", property="auditDerogationPriceType", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_part_price", property="auditPartPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_repair_factory_id", property="auditRepairFactoryId", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_repair_factory_name", property="auditRepairFactoryName", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_repair_factory_code", property="auditRepairFactoryCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_item_cover_code", property="auditItemCoverCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="check_state", property="checkState", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_remark", property="auditRemark", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_repair_outer_sum", property="auditRepairOuterSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_reference_part_price", property="auditReferencePartPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_out_item_amount", property="auditOutItemAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_Desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcLossOuterRepairInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcLossOuterRepairInfoResult")
    Optional<MtcLossOuterRepairInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcLossOuterRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcLossOuterRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default int insert(MtcLossOuterRepairInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossOuterRepairInfo, c ->
            c.map(taskNo).toProperty("taskNo")
            .map(outerId).toProperty("outerId")
            .map(outerName).toProperty("outerName")
            .map(repairHandaddFlag).toProperty("repairHandaddFlag")
            .map(evalOuterPirce).toProperty("evalOuterPirce")
            .map(derogationPrice).toProperty("derogationPrice")
            .map(derogationItemName).toProperty("derogationItemName")
            .map(derogationItemCode).toProperty("derogationItemCode")
            .map(derogationPriceType).toProperty("derogationPriceType")
            .map(partPrice).toProperty("partPrice")
            .map(repairFactoryId).toProperty("repairFactoryId")
            .map(repairFactoryName).toProperty("repairFactoryName")
            .map(repairFactoryCode).toProperty("repairFactoryCode")
            .map(itemCoverCode).toProperty("itemCoverCode")
            .map(remark).toProperty("remark")
            .map(partAmount).toProperty("partAmount")
            .map(repairOuterSum).toProperty("repairOuterSum")
            .map(referencePartPrice).toProperty("referencePartPrice")
            .map(outItemAmount).toProperty("outItemAmount")
            .map(clmTms).toProperty("clmTms")
            .map(referencePrice).toProperty("referencePrice")
            .map(auditRepairHandaddFlag).toProperty("auditRepairHandaddFlag")
            .map(auditEvalOuterPirce).toProperty("auditEvalOuterPirce")
            .map(auditDerogationPrice).toProperty("auditDerogationPrice")
            .map(auditDerogationItemName).toProperty("auditDerogationItemName")
            .map(auditDerogationItemCode).toProperty("auditDerogationItemCode")
            .map(auditDerogationPriceType).toProperty("auditDerogationPriceType")
            .map(auditPartPrice).toProperty("auditPartPrice")
            .map(auditRepairFactoryId).toProperty("auditRepairFactoryId")
            .map(auditRepairFactoryName).toProperty("auditRepairFactoryName")
            .map(auditRepairFactoryCode).toProperty("auditRepairFactoryCode")
            .map(auditItemCoverCode).toProperty("auditItemCoverCode")
            .map(checkState).toProperty("checkState")
            .map(auditRemark).toProperty("auditRemark")
            .map(auditRepairOuterSum).toProperty("auditRepairOuterSum")
            .map(auditReferencePartPrice).toProperty("auditReferencePartPrice")
            .map(auditOutItemAmount).toProperty("auditOutItemAmount")
            .map(status).toProperty("status")
            .map(miscDesc).toProperty("miscDesc")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default int insertSelective(MtcLossOuterRepairInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossOuterRepairInfo, c ->
            c.map(taskNo).toPropertyWhenPresent("taskNo", row::getTaskNo)
            .map(outerId).toPropertyWhenPresent("outerId", row::getOuterId)
            .map(outerName).toPropertyWhenPresent("outerName", row::getOuterName)
            .map(repairHandaddFlag).toPropertyWhenPresent("repairHandaddFlag", row::getRepairHandaddFlag)
            .map(evalOuterPirce).toPropertyWhenPresent("evalOuterPirce", row::getEvalOuterPirce)
            .map(derogationPrice).toPropertyWhenPresent("derogationPrice", row::getDerogationPrice)
            .map(derogationItemName).toPropertyWhenPresent("derogationItemName", row::getDerogationItemName)
            .map(derogationItemCode).toPropertyWhenPresent("derogationItemCode", row::getDerogationItemCode)
            .map(derogationPriceType).toPropertyWhenPresent("derogationPriceType", row::getDerogationPriceType)
            .map(partPrice).toPropertyWhenPresent("partPrice", row::getPartPrice)
            .map(repairFactoryId).toPropertyWhenPresent("repairFactoryId", row::getRepairFactoryId)
            .map(repairFactoryName).toPropertyWhenPresent("repairFactoryName", row::getRepairFactoryName)
            .map(repairFactoryCode).toPropertyWhenPresent("repairFactoryCode", row::getRepairFactoryCode)
            .map(itemCoverCode).toPropertyWhenPresent("itemCoverCode", row::getItemCoverCode)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(partAmount).toPropertyWhenPresent("partAmount", row::getPartAmount)
            .map(repairOuterSum).toPropertyWhenPresent("repairOuterSum", row::getRepairOuterSum)
            .map(referencePartPrice).toPropertyWhenPresent("referencePartPrice", row::getReferencePartPrice)
            .map(outItemAmount).toPropertyWhenPresent("outItemAmount", row::getOutItemAmount)
            .map(clmTms).toPropertyWhenPresent("clmTms", row::getClmTms)
            .map(referencePrice).toPropertyWhenPresent("referencePrice", row::getReferencePrice)
            .map(auditRepairHandaddFlag).toPropertyWhenPresent("auditRepairHandaddFlag", row::getAuditRepairHandaddFlag)
            .map(auditEvalOuterPirce).toPropertyWhenPresent("auditEvalOuterPirce", row::getAuditEvalOuterPirce)
            .map(auditDerogationPrice).toPropertyWhenPresent("auditDerogationPrice", row::getAuditDerogationPrice)
            .map(auditDerogationItemName).toPropertyWhenPresent("auditDerogationItemName", row::getAuditDerogationItemName)
            .map(auditDerogationItemCode).toPropertyWhenPresent("auditDerogationItemCode", row::getAuditDerogationItemCode)
            .map(auditDerogationPriceType).toPropertyWhenPresent("auditDerogationPriceType", row::getAuditDerogationPriceType)
            .map(auditPartPrice).toPropertyWhenPresent("auditPartPrice", row::getAuditPartPrice)
            .map(auditRepairFactoryId).toPropertyWhenPresent("auditRepairFactoryId", row::getAuditRepairFactoryId)
            .map(auditRepairFactoryName).toPropertyWhenPresent("auditRepairFactoryName", row::getAuditRepairFactoryName)
            .map(auditRepairFactoryCode).toPropertyWhenPresent("auditRepairFactoryCode", row::getAuditRepairFactoryCode)
            .map(auditItemCoverCode).toPropertyWhenPresent("auditItemCoverCode", row::getAuditItemCoverCode)
            .map(checkState).toPropertyWhenPresent("checkState", row::getCheckState)
            .map(auditRemark).toPropertyWhenPresent("auditRemark", row::getAuditRemark)
            .map(auditRepairOuterSum).toPropertyWhenPresent("auditRepairOuterSum", row::getAuditRepairOuterSum)
            .map(auditReferencePartPrice).toPropertyWhenPresent("auditReferencePartPrice", row::getAuditReferencePartPrice)
            .map(auditOutItemAmount).toPropertyWhenPresent("auditOutItemAmount", row::getAuditOutItemAmount)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default Optional<MtcLossOuterRepairInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcLossOuterRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default List<MtcLossOuterRepairInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcLossOuterRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default List<MtcLossOuterRepairInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcLossOuterRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default Optional<MtcLossOuterRepairInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcLossOuterRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcLossOuterRepairInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalTo(row::getTaskNo)
                .set(outerId).equalTo(row::getOuterId)
                .set(outerName).equalTo(row::getOuterName)
                .set(repairHandaddFlag).equalTo(row::getRepairHandaddFlag)
                .set(evalOuterPirce).equalTo(row::getEvalOuterPirce)
                .set(derogationPrice).equalTo(row::getDerogationPrice)
                .set(derogationItemName).equalTo(row::getDerogationItemName)
                .set(derogationItemCode).equalTo(row::getDerogationItemCode)
                .set(derogationPriceType).equalTo(row::getDerogationPriceType)
                .set(partPrice).equalTo(row::getPartPrice)
                .set(repairFactoryId).equalTo(row::getRepairFactoryId)
                .set(repairFactoryName).equalTo(row::getRepairFactoryName)
                .set(repairFactoryCode).equalTo(row::getRepairFactoryCode)
                .set(itemCoverCode).equalTo(row::getItemCoverCode)
                .set(remark).equalTo(row::getRemark)
                .set(partAmount).equalTo(row::getPartAmount)
                .set(repairOuterSum).equalTo(row::getRepairOuterSum)
                .set(referencePartPrice).equalTo(row::getReferencePartPrice)
                .set(outItemAmount).equalTo(row::getOutItemAmount)
                .set(clmTms).equalTo(row::getClmTms)
                .set(referencePrice).equalTo(row::getReferencePrice)
                .set(auditRepairHandaddFlag).equalTo(row::getAuditRepairHandaddFlag)
                .set(auditEvalOuterPirce).equalTo(row::getAuditEvalOuterPirce)
                .set(auditDerogationPrice).equalTo(row::getAuditDerogationPrice)
                .set(auditDerogationItemName).equalTo(row::getAuditDerogationItemName)
                .set(auditDerogationItemCode).equalTo(row::getAuditDerogationItemCode)
                .set(auditDerogationPriceType).equalTo(row::getAuditDerogationPriceType)
                .set(auditPartPrice).equalTo(row::getAuditPartPrice)
                .set(auditRepairFactoryId).equalTo(row::getAuditRepairFactoryId)
                .set(auditRepairFactoryName).equalTo(row::getAuditRepairFactoryName)
                .set(auditRepairFactoryCode).equalTo(row::getAuditRepairFactoryCode)
                .set(auditItemCoverCode).equalTo(row::getAuditItemCoverCode)
                .set(checkState).equalTo(row::getCheckState)
                .set(auditRemark).equalTo(row::getAuditRemark)
                .set(auditRepairOuterSum).equalTo(row::getAuditRepairOuterSum)
                .set(auditReferencePartPrice).equalTo(row::getAuditReferencePartPrice)
                .set(auditOutItemAmount).equalTo(row::getAuditOutItemAmount)
                .set(status).equalTo(row::getStatus)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcLossOuterRepairInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(outerId).equalToWhenPresent(row::getOuterId)
                .set(outerName).equalToWhenPresent(row::getOuterName)
                .set(repairHandaddFlag).equalToWhenPresent(row::getRepairHandaddFlag)
                .set(evalOuterPirce).equalToWhenPresent(row::getEvalOuterPirce)
                .set(derogationPrice).equalToWhenPresent(row::getDerogationPrice)
                .set(derogationItemName).equalToWhenPresent(row::getDerogationItemName)
                .set(derogationItemCode).equalToWhenPresent(row::getDerogationItemCode)
                .set(derogationPriceType).equalToWhenPresent(row::getDerogationPriceType)
                .set(partPrice).equalToWhenPresent(row::getPartPrice)
                .set(repairFactoryId).equalToWhenPresent(row::getRepairFactoryId)
                .set(repairFactoryName).equalToWhenPresent(row::getRepairFactoryName)
                .set(repairFactoryCode).equalToWhenPresent(row::getRepairFactoryCode)
                .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(partAmount).equalToWhenPresent(row::getPartAmount)
                .set(repairOuterSum).equalToWhenPresent(row::getRepairOuterSum)
                .set(referencePartPrice).equalToWhenPresent(row::getReferencePartPrice)
                .set(outItemAmount).equalToWhenPresent(row::getOutItemAmount)
                .set(clmTms).equalToWhenPresent(row::getClmTms)
                .set(referencePrice).equalToWhenPresent(row::getReferencePrice)
                .set(auditRepairHandaddFlag).equalToWhenPresent(row::getAuditRepairHandaddFlag)
                .set(auditEvalOuterPirce).equalToWhenPresent(row::getAuditEvalOuterPirce)
                .set(auditDerogationPrice).equalToWhenPresent(row::getAuditDerogationPrice)
                .set(auditDerogationItemName).equalToWhenPresent(row::getAuditDerogationItemName)
                .set(auditDerogationItemCode).equalToWhenPresent(row::getAuditDerogationItemCode)
                .set(auditDerogationPriceType).equalToWhenPresent(row::getAuditDerogationPriceType)
                .set(auditPartPrice).equalToWhenPresent(row::getAuditPartPrice)
                .set(auditRepairFactoryId).equalToWhenPresent(row::getAuditRepairFactoryId)
                .set(auditRepairFactoryName).equalToWhenPresent(row::getAuditRepairFactoryName)
                .set(auditRepairFactoryCode).equalToWhenPresent(row::getAuditRepairFactoryCode)
                .set(auditItemCoverCode).equalToWhenPresent(row::getAuditItemCoverCode)
                .set(checkState).equalToWhenPresent(row::getCheckState)
                .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
                .set(auditRepairOuterSum).equalToWhenPresent(row::getAuditRepairOuterSum)
                .set(auditReferencePartPrice).equalToWhenPresent(row::getAuditReferencePartPrice)
                .set(auditOutItemAmount).equalToWhenPresent(row::getAuditOutItemAmount)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default int updateByPrimaryKey(MtcLossOuterRepairInfo row) {
        return update(c ->
            c.set(taskNo).equalTo(row::getTaskNo)
            .set(outerId).equalTo(row::getOuterId)
            .set(outerName).equalTo(row::getOuterName)
            .set(repairHandaddFlag).equalTo(row::getRepairHandaddFlag)
            .set(evalOuterPirce).equalTo(row::getEvalOuterPirce)
            .set(derogationPrice).equalTo(row::getDerogationPrice)
            .set(derogationItemName).equalTo(row::getDerogationItemName)
            .set(derogationItemCode).equalTo(row::getDerogationItemCode)
            .set(derogationPriceType).equalTo(row::getDerogationPriceType)
            .set(partPrice).equalTo(row::getPartPrice)
            .set(repairFactoryId).equalTo(row::getRepairFactoryId)
            .set(repairFactoryName).equalTo(row::getRepairFactoryName)
            .set(repairFactoryCode).equalTo(row::getRepairFactoryCode)
            .set(itemCoverCode).equalTo(row::getItemCoverCode)
            .set(remark).equalTo(row::getRemark)
            .set(partAmount).equalTo(row::getPartAmount)
            .set(repairOuterSum).equalTo(row::getRepairOuterSum)
            .set(referencePartPrice).equalTo(row::getReferencePartPrice)
            .set(outItemAmount).equalTo(row::getOutItemAmount)
            .set(clmTms).equalTo(row::getClmTms)
            .set(referencePrice).equalTo(row::getReferencePrice)
            .set(auditRepairHandaddFlag).equalTo(row::getAuditRepairHandaddFlag)
            .set(auditEvalOuterPirce).equalTo(row::getAuditEvalOuterPirce)
            .set(auditDerogationPrice).equalTo(row::getAuditDerogationPrice)
            .set(auditDerogationItemName).equalTo(row::getAuditDerogationItemName)
            .set(auditDerogationItemCode).equalTo(row::getAuditDerogationItemCode)
            .set(auditDerogationPriceType).equalTo(row::getAuditDerogationPriceType)
            .set(auditPartPrice).equalTo(row::getAuditPartPrice)
            .set(auditRepairFactoryId).equalTo(row::getAuditRepairFactoryId)
            .set(auditRepairFactoryName).equalTo(row::getAuditRepairFactoryName)
            .set(auditRepairFactoryCode).equalTo(row::getAuditRepairFactoryCode)
            .set(auditItemCoverCode).equalTo(row::getAuditItemCoverCode)
            .set(checkState).equalTo(row::getCheckState)
            .set(auditRemark).equalTo(row::getAuditRemark)
            .set(auditRepairOuterSum).equalTo(row::getAuditRepairOuterSum)
            .set(auditReferencePartPrice).equalTo(row::getAuditReferencePartPrice)
            .set(auditOutItemAmount).equalTo(row::getAuditOutItemAmount)
            .set(status).equalTo(row::getStatus)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_outer_repair_info")
    default int updateByPrimaryKeySelective(MtcLossOuterRepairInfo row) {
        return update(c ->
            c.set(taskNo).equalToWhenPresent(row::getTaskNo)
            .set(outerId).equalToWhenPresent(row::getOuterId)
            .set(outerName).equalToWhenPresent(row::getOuterName)
            .set(repairHandaddFlag).equalToWhenPresent(row::getRepairHandaddFlag)
            .set(evalOuterPirce).equalToWhenPresent(row::getEvalOuterPirce)
            .set(derogationPrice).equalToWhenPresent(row::getDerogationPrice)
            .set(derogationItemName).equalToWhenPresent(row::getDerogationItemName)
            .set(derogationItemCode).equalToWhenPresent(row::getDerogationItemCode)
            .set(derogationPriceType).equalToWhenPresent(row::getDerogationPriceType)
            .set(partPrice).equalToWhenPresent(row::getPartPrice)
            .set(repairFactoryId).equalToWhenPresent(row::getRepairFactoryId)
            .set(repairFactoryName).equalToWhenPresent(row::getRepairFactoryName)
            .set(repairFactoryCode).equalToWhenPresent(row::getRepairFactoryCode)
            .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(partAmount).equalToWhenPresent(row::getPartAmount)
            .set(repairOuterSum).equalToWhenPresent(row::getRepairOuterSum)
            .set(referencePartPrice).equalToWhenPresent(row::getReferencePartPrice)
            .set(outItemAmount).equalToWhenPresent(row::getOutItemAmount)
            .set(clmTms).equalToWhenPresent(row::getClmTms)
            .set(referencePrice).equalToWhenPresent(row::getReferencePrice)
            .set(auditRepairHandaddFlag).equalToWhenPresent(row::getAuditRepairHandaddFlag)
            .set(auditEvalOuterPirce).equalToWhenPresent(row::getAuditEvalOuterPirce)
            .set(auditDerogationPrice).equalToWhenPresent(row::getAuditDerogationPrice)
            .set(auditDerogationItemName).equalToWhenPresent(row::getAuditDerogationItemName)
            .set(auditDerogationItemCode).equalToWhenPresent(row::getAuditDerogationItemCode)
            .set(auditDerogationPriceType).equalToWhenPresent(row::getAuditDerogationPriceType)
            .set(auditPartPrice).equalToWhenPresent(row::getAuditPartPrice)
            .set(auditRepairFactoryId).equalToWhenPresent(row::getAuditRepairFactoryId)
            .set(auditRepairFactoryName).equalToWhenPresent(row::getAuditRepairFactoryName)
            .set(auditRepairFactoryCode).equalToWhenPresent(row::getAuditRepairFactoryCode)
            .set(auditItemCoverCode).equalToWhenPresent(row::getAuditItemCoverCode)
            .set(checkState).equalToWhenPresent(row::getCheckState)
            .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
            .set(auditRepairOuterSum).equalToWhenPresent(row::getAuditRepairOuterSum)
            .set(auditReferencePartPrice).equalToWhenPresent(row::getAuditReferencePartPrice)
            .set(auditOutItemAmount).equalToWhenPresent(row::getAuditOutItemAmount)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}