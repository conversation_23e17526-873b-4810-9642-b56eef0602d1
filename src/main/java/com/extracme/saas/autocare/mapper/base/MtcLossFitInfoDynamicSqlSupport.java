package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcLossFitInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    public static final MtcLossFitInfo mtcLossFitInfo = new MtcLossFitInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.id")
    public static final SqlColumn<Long> id = mtcLossFitInfo.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.task_no")
    public static final SqlColumn<String> taskNo = mtcLossFitInfo.taskNo;

    /**
     * Database Column Remarks:
     *   定损明细主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.part_id")
    public static final SqlColumn<String> partId = mtcLossFitInfo.partId;

    /**
     * Database Column Remarks:
     *   零配件原厂编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.part_code")
    public static final SqlColumn<String> partCode = mtcLossFitInfo.partCode;

    /**
     * Database Column Remarks:
     *   项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.item_name")
    public static final SqlColumn<String> itemName = mtcLossFitInfo.itemName;

    /**
     * Database Column Remarks:
     *   系统4S店价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.sys_guide_price")
    public static final SqlColumn<BigDecimal> sysGuidePrice = mtcLossFitInfo.sysGuidePrice;

    /**
     * Database Column Remarks:
     *   系统市场价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.sys_market_price")
    public static final SqlColumn<BigDecimal> sysMarketPrice = mtcLossFitInfo.sysMarketPrice;

    /**
     * Database Column Remarks:
     *   本地4S店价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_guide_price")
    public static final SqlColumn<BigDecimal> localGuidePrice = mtcLossFitInfo.localGuidePrice;

    /**
     * Database Column Remarks:
     *   本地市场原厂价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_market_price")
    public static final SqlColumn<BigDecimal> localMarketPrice = mtcLossFitInfo.localMarketPrice;

    /**
     * Database Column Remarks:
     *   本地品牌价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_brand_price")
    public static final SqlColumn<BigDecimal> localBrandPrice = mtcLossFitInfo.localBrandPrice;

    /**
     * Database Column Remarks:
     *   本地适用价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_applicable_price")
    public static final SqlColumn<BigDecimal> localApplicablePrice = mtcLossFitInfo.localApplicablePrice;

    /**
     * Database Column Remarks:
     *   本地再制造价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_remanufacture_price")
    public static final SqlColumn<BigDecimal> localRemanufacturePrice = mtcLossFitInfo.localRemanufacturePrice;

    /**
     * Database Column Remarks:
     *   定损参考价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.local_price")
    public static final SqlColumn<BigDecimal> localPrice = mtcLossFitInfo.localPrice;

    /**
     * Database Column Remarks:
     *   定损配件数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.count")
    public static final SqlColumn<Integer> count = mtcLossFitInfo.count;

    /**
     * Database Column Remarks:
     *   定损配件单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.material_fee")
    public static final SqlColumn<BigDecimal> materialFee = mtcLossFitInfo.materialFee;

    /**
     * Database Column Remarks:
     *   自定义配件标记（0：系统点选 1：手工自定义 2：标准点选 ）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_config_flag")
    public static final SqlColumn<Integer> selfConfigFlag = mtcLossFitInfo.selfConfigFlag;

    /**
     * Database Column Remarks:
     *   险种代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.item_cover_code")
    public static final SqlColumn<Integer> itemCoverCode = mtcLossFitInfo.itemCoverCode;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.remark")
    public static final SqlColumn<String> remark = mtcLossFitInfo.remark;

    /**
     * Database Column Remarks:
     *   参考价格类型编码（1：4S价格 2：市场原厂价格 3：品牌价格 4：适用价格 5：再制造价格）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.chg_comp_set_code")
    public static final SqlColumn<String> chgCompSetCode = mtcLossFitInfo.chgCompSetCode;

    /**
     * Database Column Remarks:
     *   回收标志（0：不回收 1：回收）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.fit_back_flag")
    public static final SqlColumn<String> fitBackFlag = mtcLossFitInfo.fitBackFlag;

    /**
     * Database Column Remarks:
     *   残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.remains_price")
    public static final SqlColumn<BigDecimal> remainsPrice = mtcLossFitInfo.remainsPrice;

    /**
     * Database Column Remarks:
     *   待检测标志（0：无需检测 1：待检测）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.detected_flag")
    public static final SqlColumn<String> detectedFlag = mtcLossFitInfo.detectedFlag;

    /**
     * Database Column Remarks:
     *   直供测标志（0：非直供 1：直供）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.direct_supply_flag")
    public static final SqlColumn<String> directSupplyFlag = mtcLossFitInfo.directSupplyFlag;

    /**
     * Database Column Remarks:
     *   直供商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.direct_supplier")
    public static final SqlColumn<String> directSupplier = mtcLossFitInfo.directSupplier;

    /**
     * Database Column Remarks:
     *   管理费率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_single_rate")
    public static final SqlColumn<BigDecimal> manageSingleRate = mtcLossFitInfo.manageSingleRate;

    /**
     * Database Column Remarks:
     *   管理费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_single_fee")
    public static final SqlColumn<BigDecimal> manageSingleFee = mtcLossFitInfo.manageSingleFee;

    /**
     * Database Column Remarks:
     *   换件合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.eval_part_sum")
    public static final SqlColumn<BigDecimal> evalPartSum = mtcLossFitInfo.evalPartSum;

    /**
     * Database Column Remarks:
     *   自付比例
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_pay_rate")
    public static final SqlColumn<BigDecimal> selfPayRate = mtcLossFitInfo.selfPayRate;

    /**
     * Database Column Remarks:
     *   回收方式（1：需回收）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.recycle_part_flag")
    public static final SqlColumn<String> recyclePartFlag = mtcLossFitInfo.recyclePartFlag;

    /**
     * Database Column Remarks:
     *   首次定损金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.eval_part_sum_first")
    public static final SqlColumn<BigDecimal> evalPartSumFirst = mtcLossFitInfo.evalPartSumFirst;

    /**
     * Database Column Remarks:
     *   配件角标（第一位：是否待检测 第二位：是否改装配件 第三位：是否高价值配件 第四位：是否退回新增项目 第五位：是否风险项目 第六位：是否询价配件 第七位：是否直供配件）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.corner_mark")
    public static final SqlColumn<String> cornerMark = mtcLossFitInfo.cornerMark;

    /**
     * Database Column Remarks:
     *   重开次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.clm_tms")
    public static final SqlColumn<String> clmTms = mtcLossFitInfo.clmTms;

    /**
     * Database Column Remarks:
     *   是否涉水（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.if_wading")
    public static final SqlColumn<String> ifWading = mtcLossFitInfo.ifWading;

    /**
     * Database Column Remarks:
     *   复检标志
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.recheck_flag")
    public static final SqlColumn<String> recheckFlag = mtcLossFitInfo.recheckFlag;

    /**
     * Database Column Remarks:
     *   核损换件单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_material_fee")
    public static final SqlColumn<BigDecimal> auditMaterialFee = mtcLossFitInfo.auditMaterialFee;

    /**
     * Database Column Remarks:
     *   核损数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_count")
    public static final SqlColumn<BigDecimal> auditCount = mtcLossFitInfo.auditCount;

    /**
     * Database Column Remarks:
     *   核损换件小计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_part_sum")
    public static final SqlColumn<BigDecimal> apprPartSum = mtcLossFitInfo.apprPartSum;

    /**
     * Database Column Remarks:
     *   自付比例
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.self_pay_price")
    public static final SqlColumn<BigDecimal> selfPayPrice = mtcLossFitInfo.selfPayPrice;

    /**
     * Database Column Remarks:
     *   核损残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_remains_price")
    public static final SqlColumn<BigDecimal> apprRemainsPrice = mtcLossFitInfo.apprRemainsPrice;

    /**
     * Database Column Remarks:
     *   核损管理费率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.manage_rate")
    public static final SqlColumn<BigDecimal> manageRate = mtcLossFitInfo.manageRate;

    /**
     * Database Column Remarks:
     *   核损管理费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_manage_fee")
    public static final SqlColumn<BigDecimal> apprManageFee = mtcLossFitInfo.apprManageFee;

    /**
     * Database Column Remarks:
     *   核损状态（00：待处理 01：通过 02：价格异议 03：建议剔除 04：建议修复）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.check_state")
    public static final SqlColumn<String> checkState = mtcLossFitInfo.checkState;

    /**
     * Database Column Remarks:
     *   核损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_remark")
    public static final SqlColumn<String> auditRemark = mtcLossFitInfo.auditRemark;

    /**
     * Database Column Remarks:
     *   核损回收标记（0：不回收 1：回收）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.appr_back_flag")
    public static final SqlColumn<String> apprBackFlag = mtcLossFitInfo.apprBackFlag;

    /**
     * Database Column Remarks:
     *   复勘标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.audit_recheck_flag")
    public static final SqlColumn<String> auditRecheckFlag = mtcLossFitInfo.auditRecheckFlag;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.status")
    public static final SqlColumn<Integer> status = mtcLossFitInfo.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.misc_Desc")
    public static final SqlColumn<String> miscDesc = mtcLossFitInfo.miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.create_by")
    public static final SqlColumn<String> createBy = mtcLossFitInfo.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.created_time")
    public static final SqlColumn<Date> createdTime = mtcLossFitInfo.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.update_by")
    public static final SqlColumn<String> updateBy = mtcLossFitInfo.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_fit_info.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcLossFitInfo.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    public static final class MtcLossFitInfo extends AliasableSqlTable<MtcLossFitInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<String> partId = column("part_id", JDBCType.VARCHAR);

        public final SqlColumn<String> partCode = column("part_code", JDBCType.VARCHAR);

        public final SqlColumn<String> itemName = column("item_name", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> sysGuidePrice = column("sys_guide_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> sysMarketPrice = column("sys_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> localGuidePrice = column("local_guide_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> localMarketPrice = column("local_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> localBrandPrice = column("local_brand_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> localApplicablePrice = column("local_applicable_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> localRemanufacturePrice = column("local_remanufacture_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> localPrice = column("local_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> count = column("count", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> materialFee = column("material_fee", JDBCType.DECIMAL);

        public final SqlColumn<Integer> selfConfigFlag = column("self_config_flag", JDBCType.INTEGER);

        public final SqlColumn<Integer> itemCoverCode = column("item_cover_code", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> chgCompSetCode = column("chg_comp_set_code", JDBCType.VARCHAR);

        public final SqlColumn<String> fitBackFlag = column("fit_back_flag", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> remainsPrice = column("remains_price", JDBCType.DECIMAL);

        public final SqlColumn<String> detectedFlag = column("detected_flag", JDBCType.VARCHAR);

        public final SqlColumn<String> directSupplyFlag = column("direct_supply_flag", JDBCType.VARCHAR);

        public final SqlColumn<String> directSupplier = column("direct_supplier", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> manageSingleRate = column("manage_single_rate", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> manageSingleFee = column("manage_single_fee", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> evalPartSum = column("eval_part_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> selfPayRate = column("self_pay_rate", JDBCType.DECIMAL);

        public final SqlColumn<String> recyclePartFlag = column("recycle_part_flag", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> evalPartSumFirst = column("eval_part_sum_first", JDBCType.DECIMAL);

        public final SqlColumn<String> cornerMark = column("corner_mark", JDBCType.VARCHAR);

        public final SqlColumn<String> clmTms = column("clm_tms", JDBCType.VARCHAR);

        public final SqlColumn<String> ifWading = column("if_wading", JDBCType.VARCHAR);

        public final SqlColumn<String> recheckFlag = column("recheck_flag", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> auditMaterialFee = column("audit_material_fee", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditCount = column("audit_count", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> apprPartSum = column("appr_part_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> selfPayPrice = column("self_pay_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> apprRemainsPrice = column("appr_remains_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> manageRate = column("manage_rate", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> apprManageFee = column("appr_manage_fee", JDBCType.DECIMAL);

        public final SqlColumn<String> checkState = column("check_state", JDBCType.VARCHAR);

        public final SqlColumn<String> auditRemark = column("audit_remark", JDBCType.VARCHAR);

        public final SqlColumn<String> apprBackFlag = column("appr_back_flag", JDBCType.VARCHAR);

        public final SqlColumn<String> auditRecheckFlag = column("audit_recheck_flag", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_Desc", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcLossFitInfo() {
            super("mtc_loss_fit_info", MtcLossFitInfo::new);
        }
    }
}