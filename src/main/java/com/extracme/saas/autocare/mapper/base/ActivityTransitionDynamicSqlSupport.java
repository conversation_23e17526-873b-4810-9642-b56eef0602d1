package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ActivityTransitionDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    public static final ActivityTransition activityTransition = new ActivityTransition();

    /**
     * Database Column Remarks:
     *   转换规则编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.id")
    public static final SqlColumn<Long> id = activityTransition.id;

    /**
     * Database Column Remarks:
     *   所属流程模板编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.workflow_id")
    public static final SqlColumn<Long> workflowId = activityTransition.workflowId;

    /**
     * Database Column Remarks:
     *   转换起始节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.from_activity_code")
    public static final SqlColumn<String> fromActivityCode = activityTransition.fromActivityCode;

    /**
     * Database Column Remarks:
     *   目标节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.to_activity_code")
    public static final SqlColumn<String> toActivityCode = activityTransition.toActivityCode;

    /**
     * Database Column Remarks:
     *   触发事件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.trigger_event")
    public static final SqlColumn<String> triggerEvent = activityTransition.triggerEvent;

    /**
     * Database Column Remarks:
     *   条件判断处理器类名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.condition_handler")
    public static final SqlColumn<String> conditionHandler = activityTransition.conditionHandler;

    /**
     * Database Column Remarks:
     *   处理逻辑类名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.handler_class")
    public static final SqlColumn<String> handlerClass = activityTransition.handlerClass;

    /**
     * Database Column Remarks:
     *   租户编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.tenant_id")
    public static final SqlColumn<Integer> tenantId = activityTransition.tenantId;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.create_time")
    public static final SqlColumn<Date> createTime = activityTransition.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.create_by")
    public static final SqlColumn<String> createBy = activityTransition.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.update_time")
    public static final SqlColumn<Date> updateTime = activityTransition.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.update_by")
    public static final SqlColumn<String> updateBy = activityTransition.updateBy;

    /**
     * Database Column Remarks:
     *   规则说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_transition.description")
    public static final SqlColumn<String> description = activityTransition.description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    public static final class ActivityTransition extends AliasableSqlTable<ActivityTransition> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> workflowId = column("workflow_id", JDBCType.BIGINT);

        public final SqlColumn<String> fromActivityCode = column("from_activity_code", JDBCType.VARCHAR);

        public final SqlColumn<String> toActivityCode = column("to_activity_code", JDBCType.VARCHAR);

        public final SqlColumn<String> triggerEvent = column("trigger_event", JDBCType.VARCHAR);

        public final SqlColumn<String> conditionHandler = column("condition_handler", JDBCType.VARCHAR);

        public final SqlColumn<String> handlerClass = column("handler_class", JDBCType.VARCHAR);

        public final SqlColumn<Integer> tenantId = column("tenant_id", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<String> description = column("description", JDBCType.LONGVARCHAR);

        public ActivityTransition() {
            super("activity_transition", ActivityTransition::new);
        }
    }
}