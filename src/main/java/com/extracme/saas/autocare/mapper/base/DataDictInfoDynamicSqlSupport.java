package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DataDictInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    public static final DataDictInfo dataDictInfo = new DataDictInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.id")
    public static final SqlColumn<Long> id = dataDictInfo.id;

    /**
     * Database Column Remarks:
     *   数据字典名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_name")
    public static final SqlColumn<String> dataName = dataDictInfo.dataName;

    /**
     * Database Column Remarks:
     *   数据字典编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_code")
    public static final SqlColumn<String> dataCode = dataDictInfo.dataCode;

    /**
     * Database Column Remarks:
     *   数据字段value值类型 1-数字 2-字符串
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.code_type")
    public static final SqlColumn<Integer> codeType = dataDictInfo.codeType;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.create_time")
    public static final SqlColumn<Date> createTime = dataDictInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.create_by")
    public static final SqlColumn<String> createBy = dataDictInfo.createBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.update_time")
    public static final SqlColumn<Date> updateTime = dataDictInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.update_by")
    public static final SqlColumn<String> updateBy = dataDictInfo.updateBy;

    /**
     * Database Column Remarks:
     *   字典值 JSON文本
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_dict_info.data_value")
    public static final SqlColumn<String> dataValue = dataDictInfo.dataValue;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    public static final class DataDictInfo extends AliasableSqlTable<DataDictInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> dataName = column("data_name", JDBCType.VARCHAR);

        public final SqlColumn<String> dataCode = column("data_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> codeType = column("code_type", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<String> dataValue = column("data_value", JDBCType.LONGVARCHAR);

        public DataDictInfo() {
            super("data_dict_info", DataDictInfo::new);
        }
    }
}