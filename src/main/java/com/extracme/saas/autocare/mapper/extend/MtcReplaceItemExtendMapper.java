package com.extracme.saas.autocare.mapper.extend;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcReplaceItemMapper;
import com.extracme.saas.autocare.model.vo.ReplacePartItemListVO;

/**
 * 换件项目信息扩展Mapper
 */
@Mapper
@TenantSchema // 可以指定某些方法不使用租户Schema
public interface MtcReplaceItemExtendMapper extends MtcReplaceItemMapper {

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="QueryReplaceItemListResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.BIGINT, id=true),
            @Result(column="org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="part_name", property="partName", jdbcType=JdbcType.VARCHAR),
            @Result(column="grouping_id", property="groupingId", jdbcType=JdbcType.BIGINT),
            @Result(column="original_factory_part_no", property="originalFactoryPartNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="original_factory_part_name", property="originalFactoryPartName", jdbcType=JdbcType.VARCHAR),
            @Result(column="sys_market_price", property="sysMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="sys_major_in_price", property="sysMajorInPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="local_market_price", property="localMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="local_major_in_price", property="localMajorInPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="vehicle_model_seq", property="vehicleModelSeq", jdbcType=JdbcType.BIGINT),
            @Result(column="separate_quotation", property="separateQuotation", jdbcType=JdbcType.INTEGER),
            @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
            @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="grouping_name", property="groupingName", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR)
    })
    List<ReplacePartItemListVO> queryReplaceItemList(SelectStatementProvider selectStatement);

} 