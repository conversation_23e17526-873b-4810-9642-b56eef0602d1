package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.ActivityTransitionDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.ActivityTransition;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ActivityTransitionMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    BasicColumn[] selectList = BasicColumn.columnList(id, workflowId, fromActivityCode, toActivityCode, triggerEvent, conditionHandler, handlerClass, tenantId, createTime, createBy, updateTime, updateBy, description);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<ActivityTransition> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ActivityTransitionResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="workflow_id", property="workflowId", jdbcType=JdbcType.BIGINT),
        @Result(column="from_activity_code", property="fromActivityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="to_activity_code", property="toActivityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="trigger_event", property="triggerEvent", jdbcType=JdbcType.VARCHAR),
        @Result(column="condition_handler", property="conditionHandler", jdbcType=JdbcType.VARCHAR),
        @Result(column="handler_class", property="handlerClass", jdbcType=JdbcType.VARCHAR),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="description", property="description", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<ActivityTransition> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ActivityTransitionResult")
    Optional<ActivityTransition> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, activityTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, activityTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default int insert(ActivityTransition row) {
        return MyBatis3Utils.insert(this::insert, row, activityTransition, c ->
            c.map(workflowId).toProperty("workflowId")
            .map(fromActivityCode).toProperty("fromActivityCode")
            .map(toActivityCode).toProperty("toActivityCode")
            .map(triggerEvent).toProperty("triggerEvent")
            .map(conditionHandler).toProperty("conditionHandler")
            .map(handlerClass).toProperty("handlerClass")
            .map(tenantId).toProperty("tenantId")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
            .map(description).toProperty("description")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default int insertSelective(ActivityTransition row) {
        return MyBatis3Utils.insert(this::insert, row, activityTransition, c ->
            c.map(workflowId).toPropertyWhenPresent("workflowId", row::getWorkflowId)
            .map(fromActivityCode).toPropertyWhenPresent("fromActivityCode", row::getFromActivityCode)
            .map(toActivityCode).toPropertyWhenPresent("toActivityCode", row::getToActivityCode)
            .map(triggerEvent).toPropertyWhenPresent("triggerEvent", row::getTriggerEvent)
            .map(conditionHandler).toPropertyWhenPresent("conditionHandler", row::getConditionHandler)
            .map(handlerClass).toPropertyWhenPresent("handlerClass", row::getHandlerClass)
            .map(tenantId).toPropertyWhenPresent("tenantId", row::getTenantId)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(description).toPropertyWhenPresent("description", row::getDescription)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default Optional<ActivityTransition> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, activityTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default List<ActivityTransition> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, activityTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default List<ActivityTransition> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, activityTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default Optional<ActivityTransition> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, activityTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    static UpdateDSL<UpdateModel> updateAllColumns(ActivityTransition row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(workflowId).equalTo(row::getWorkflowId)
                .set(fromActivityCode).equalTo(row::getFromActivityCode)
                .set(toActivityCode).equalTo(row::getToActivityCode)
                .set(triggerEvent).equalTo(row::getTriggerEvent)
                .set(conditionHandler).equalTo(row::getConditionHandler)
                .set(handlerClass).equalTo(row::getHandlerClass)
                .set(tenantId).equalTo(row::getTenantId)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(description).equalTo(row::getDescription);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ActivityTransition row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(workflowId).equalToWhenPresent(row::getWorkflowId)
                .set(fromActivityCode).equalToWhenPresent(row::getFromActivityCode)
                .set(toActivityCode).equalToWhenPresent(row::getToActivityCode)
                .set(triggerEvent).equalToWhenPresent(row::getTriggerEvent)
                .set(conditionHandler).equalToWhenPresent(row::getConditionHandler)
                .set(handlerClass).equalToWhenPresent(row::getHandlerClass)
                .set(tenantId).equalToWhenPresent(row::getTenantId)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(description).equalToWhenPresent(row::getDescription);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default int updateByPrimaryKey(ActivityTransition row) {
        return update(c ->
            c.set(workflowId).equalTo(row::getWorkflowId)
            .set(fromActivityCode).equalTo(row::getFromActivityCode)
            .set(toActivityCode).equalTo(row::getToActivityCode)
            .set(triggerEvent).equalTo(row::getTriggerEvent)
            .set(conditionHandler).equalTo(row::getConditionHandler)
            .set(handlerClass).equalTo(row::getHandlerClass)
            .set(tenantId).equalTo(row::getTenantId)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(description).equalTo(row::getDescription)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_transition")
    default int updateByPrimaryKeySelective(ActivityTransition row) {
        return update(c ->
            c.set(workflowId).equalToWhenPresent(row::getWorkflowId)
            .set(fromActivityCode).equalToWhenPresent(row::getFromActivityCode)
            .set(toActivityCode).equalToWhenPresent(row::getToActivityCode)
            .set(triggerEvent).equalToWhenPresent(row::getTriggerEvent)
            .set(conditionHandler).equalToWhenPresent(row::getConditionHandler)
            .set(handlerClass).equalToWhenPresent(row::getHandlerClass)
            .set(tenantId).equalToWhenPresent(row::getTenantId)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(description).equalToWhenPresent(row::getDescription)
            .where(id, isEqualTo(row::getId))
        );
    }
}