package com.extracme.saas.autocare.mapper.extend;

import org.apache.ibatis.annotations.Mapper;
import com.extracme.saas.autocare.mapper.base.SysUserMapper;

/**
 * 用户表扩展Mapper接口
 * 
 * 主要功能：
 * 1. 扩展基础Mapper的功能
 * 2. 仅用于无法通过基础Mapper实现的复杂查询
 * 3. 禁止包含可以通过Dynamic SQL实现的简单查询
 * 4. 所有查询必须使用Dynamic SQL，确保类型安全
 */
@Mapper
public interface SysUserExtendMapper extends SysUserMapper {
    // 移除 default 方法到 Repository 层实现
    // 如需添加新方法，必须是无法通过 Dynamic SQL 实现的复杂查询
} 