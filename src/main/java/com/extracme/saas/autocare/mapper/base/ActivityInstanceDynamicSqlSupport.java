package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ActivityInstanceDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    public static final ActivityInstance activityInstance = new ActivityInstance();

    /**
     * Database Column Remarks:
     *   活动实例记录编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.id")
    public static final SqlColumn<Long> id = activityInstance.id;

    /**
     * Database Column Remarks:
     *   所属流程实例编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.instance_id")
    public static final SqlColumn<Long> instanceId = activityInstance.instanceId;

    /**
     * Database Column Remarks:
     *   目标活动节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.to_activity_code")
    public static final SqlColumn<String> toActivityCode = activityInstance.toActivityCode;

    /**
     * Database Column Remarks:
     *   来源节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.from_activity_code")
    public static final SqlColumn<String> fromActivityCode = activityInstance.fromActivityCode;

    /**
     * Database Column Remarks:
     *   触发转换的规则编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.transition_id")
    public static final SqlColumn<Long> transitionId = activityInstance.transitionId;

    /**
     * Database Column Remarks:
     *   活动开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.start_time")
    public static final SqlColumn<Date> startTime = activityInstance.startTime;

    /**
     * Database Column Remarks:
     *   活动结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.end_time")
    public static final SqlColumn<Date> endTime = activityInstance.endTime;

    /**
     * Database Column Remarks:
     *   活动总耗时（单位：秒）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.duration")
    public static final SqlColumn<Integer> duration = activityInstance.duration;

    /**
     * Database Column Remarks:
     *   当前状态编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.current_status_code")
    public static final SqlColumn<String> currentStatusCode = activityInstance.currentStatusCode;

    /**
     * Database Column Remarks:
     *   操作人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.operator")
    public static final SqlColumn<String> operator = activityInstance.operator;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.create_time")
    public static final SqlColumn<Date> createTime = activityInstance.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.create_by")
    public static final SqlColumn<String> createBy = activityInstance.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.update_time")
    public static final SqlColumn<Date> updateTime = activityInstance.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.update_by")
    public static final SqlColumn<String> updateBy = activityInstance.updateBy;

    /**
     * Database Column Remarks:
     *   备注说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_instance.remarks")
    public static final SqlColumn<String> remarks = activityInstance.remarks;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    public static final class ActivityInstance extends AliasableSqlTable<ActivityInstance> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> instanceId = column("instance_id", JDBCType.BIGINT);

        public final SqlColumn<String> toActivityCode = column("to_activity_code", JDBCType.VARCHAR);

        public final SqlColumn<String> fromActivityCode = column("from_activity_code", JDBCType.VARCHAR);

        public final SqlColumn<Long> transitionId = column("transition_id", JDBCType.BIGINT);

        public final SqlColumn<Date> startTime = column("start_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> endTime = column("end_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> duration = column("duration", JDBCType.INTEGER);

        public final SqlColumn<String> currentStatusCode = column("current_status_code", JDBCType.VARCHAR);

        public final SqlColumn<String> operator = column("operator", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<String> remarks = column("remarks", JDBCType.LONGVARCHAR);

        public ActivityInstance() {
            super("activity_instance", ActivityInstance::new);
        }
    }
}