package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.SysPermissionDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.SysPermission;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysPermissionMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    BasicColumn[] selectList = BasicColumn.columnList(id, permissionName, permissionCode, permissionType, parentId, path, component, icon, sort, status, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<SysPermission> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysPermissionResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="permission_name", property="permissionName", jdbcType=JdbcType.VARCHAR),
        @Result(column="permission_code", property="permissionCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="permission_type", property="permissionType", jdbcType=JdbcType.VARCHAR),
        @Result(column="parent_id", property="parentId", jdbcType=JdbcType.BIGINT),
        @Result(column="path", property="path", jdbcType=JdbcType.VARCHAR),
        @Result(column="component", property="component", jdbcType=JdbcType.VARCHAR),
        @Result(column="icon", property="icon", jdbcType=JdbcType.VARCHAR),
        @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<SysPermission> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysPermissionResult")
    Optional<SysPermission> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysPermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysPermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default int insert(SysPermission row) {
        return MyBatis3Utils.insert(this::insert, row, sysPermission, c ->
            c.map(permissionName).toProperty("permissionName")
            .map(permissionCode).toProperty("permissionCode")
            .map(permissionType).toProperty("permissionType")
            .map(parentId).toProperty("parentId")
            .map(path).toProperty("path")
            .map(component).toProperty("component")
            .map(icon).toProperty("icon")
            .map(sort).toProperty("sort")
            .map(status).toProperty("status")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default int insertSelective(SysPermission row) {
        return MyBatis3Utils.insert(this::insert, row, sysPermission, c ->
            c.map(permissionName).toPropertyWhenPresent("permissionName", row::getPermissionName)
            .map(permissionCode).toPropertyWhenPresent("permissionCode", row::getPermissionCode)
            .map(permissionType).toPropertyWhenPresent("permissionType", row::getPermissionType)
            .map(parentId).toPropertyWhenPresent("parentId", row::getParentId)
            .map(path).toPropertyWhenPresent("path", row::getPath)
            .map(component).toPropertyWhenPresent("component", row::getComponent)
            .map(icon).toPropertyWhenPresent("icon", row::getIcon)
            .map(sort).toPropertyWhenPresent("sort", row::getSort)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default Optional<SysPermission> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysPermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default List<SysPermission> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysPermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default List<SysPermission> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysPermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default Optional<SysPermission> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysPermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    static UpdateDSL<UpdateModel> updateAllColumns(SysPermission row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(permissionName).equalTo(row::getPermissionName)
                .set(permissionCode).equalTo(row::getPermissionCode)
                .set(permissionType).equalTo(row::getPermissionType)
                .set(parentId).equalTo(row::getParentId)
                .set(path).equalTo(row::getPath)
                .set(component).equalTo(row::getComponent)
                .set(icon).equalTo(row::getIcon)
                .set(sort).equalTo(row::getSort)
                .set(status).equalTo(row::getStatus)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysPermission row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(permissionName).equalToWhenPresent(row::getPermissionName)
                .set(permissionCode).equalToWhenPresent(row::getPermissionCode)
                .set(permissionType).equalToWhenPresent(row::getPermissionType)
                .set(parentId).equalToWhenPresent(row::getParentId)
                .set(path).equalToWhenPresent(row::getPath)
                .set(component).equalToWhenPresent(row::getComponent)
                .set(icon).equalToWhenPresent(row::getIcon)
                .set(sort).equalToWhenPresent(row::getSort)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default int updateByPrimaryKey(SysPermission row) {
        return update(c ->
            c.set(permissionName).equalTo(row::getPermissionName)
            .set(permissionCode).equalTo(row::getPermissionCode)
            .set(permissionType).equalTo(row::getPermissionType)
            .set(parentId).equalTo(row::getParentId)
            .set(path).equalTo(row::getPath)
            .set(component).equalTo(row::getComponent)
            .set(icon).equalTo(row::getIcon)
            .set(sort).equalTo(row::getSort)
            .set(status).equalTo(row::getStatus)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    default int updateByPrimaryKeySelective(SysPermission row) {
        return update(c ->
            c.set(permissionName).equalToWhenPresent(row::getPermissionName)
            .set(permissionCode).equalToWhenPresent(row::getPermissionCode)
            .set(permissionType).equalToWhenPresent(row::getPermissionType)
            .set(parentId).equalToWhenPresent(row::getParentId)
            .set(path).equalToWhenPresent(row::getPath)
            .set(component).equalToWhenPresent(row::getComponent)
            .set(icon).equalToWhenPresent(row::getIcon)
            .set(sort).equalToWhenPresent(row::getSort)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}