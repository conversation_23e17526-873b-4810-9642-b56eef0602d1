package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcLossInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    public static final MtcLossInfo mtcLossInfo = new MtcLossInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.id")
    public static final SqlColumn<Long> id = mtcLossInfo.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.task_no")
    public static final SqlColumn<String> taskNo = mtcLossInfo.taskNo;

    /**
     * Database Column Remarks:
     *   定损车型编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_certain_code")
    public static final SqlColumn<String> vehCertainCode = mtcLossInfo.vehCertainCode;

    /**
     * Database Column Remarks:
     *   定损车型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_certain_name")
    public static final SqlColumn<String> vehCertainName = mtcLossInfo.vehCertainName;

    /**
     * Database Column Remarks:
     *   车组编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_group_code")
    public static final SqlColumn<String> vehGroupCode = mtcLossInfo.vehGroupCode;

    /**
     * Database Column Remarks:
     *   车组名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.group_name")
    public static final SqlColumn<String> groupName = mtcLossInfo.groupName;

    /**
     * Database Column Remarks:
     *   定损品牌编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.veh_brand_code")
    public static final SqlColumn<String> vehBrandCode = mtcLossInfo.vehBrandCode;

    /**
     * Database Column Remarks:
     *   定损品牌名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.brand_name")
    public static final SqlColumn<String> brandName = mtcLossInfo.brandName;

    /**
     * Database Column Remarks:
     *   自定义车型标志（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_config_flag")
    public static final SqlColumn<String> selfConfigFlag = mtcLossInfo.selfConfigFlag;

    /**
     * Database Column Remarks:
     *   定损施救费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.salvage_fee")
    public static final SqlColumn<BigDecimal> salvageFee = mtcLossInfo.salvageFee;

    /**
     * Database Column Remarks:
     *   定损折扣残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.remnant_fee")
    public static final SqlColumn<BigDecimal> remnantFee = mtcLossInfo.remnantFee;

    /**
     * Database Column Remarks:
     *   定损管理费合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.manage_fee")
    public static final SqlColumn<BigDecimal> manageFee = mtcLossInfo.manageFee;

    /**
     * Database Column Remarks:
     *   定损换件合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_part_sum")
    public static final SqlColumn<BigDecimal> evalPartSum = mtcLossInfo.evalPartSum;

    /**
     * Database Column Remarks:
     *   定损工时合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_repair_sum")
    public static final SqlColumn<BigDecimal> evalRepairSum = mtcLossInfo.evalRepairSum;

    /**
     * Database Column Remarks:
     *   定损辅料合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_mate_sum")
    public static final SqlColumn<BigDecimal> evalMateSum = mtcLossInfo.evalMateSum;

    /**
     * Database Column Remarks:
     *   定损自付合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_pay_sum")
    public static final SqlColumn<BigDecimal> selfPaySum = mtcLossInfo.selfPaySum;

    /**
     * Database Column Remarks:
     *   定损外修合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.outer_sum")
    public static final SqlColumn<BigDecimal> outerSum = mtcLossInfo.outerSum;

    /**
     * Database Column Remarks:
     *   定损外修减损合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.derogation_sum")
    public static final SqlColumn<BigDecimal> derogationSum = mtcLossInfo.derogationSum;

    /**
     * Database Column Remarks:
     *   定损合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.sum_loss_amount")
    public static final SqlColumn<BigDecimal> sumLossAmount = mtcLossInfo.sumLossAmount;

    /**
     * Database Column Remarks:
     *   定损员代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.handler_code")
    public static final SqlColumn<String> handlerCode = mtcLossInfo.handlerCode;

    /**
     * Database Column Remarks:
     *   定损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.remark")
    public static final SqlColumn<String> remark = mtcLossInfo.remark;

    /**
     * Database Column Remarks:
     *   价格类型（1：4S价格 2：市场价格 99：其他）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.price_type")
    public static final SqlColumn<String> priceType = mtcLossInfo.priceType;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_id")
    public static final SqlColumn<String> repairFacId = mtcLossInfo.repairFacId;

    /**
     * Database Column Remarks:
     *   修理厂代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_code")
    public static final SqlColumn<String> repairFacCode = mtcLossInfo.repairFacCode;

    /**
     * Database Column Remarks:
     *   修理厂类型（0：综合修理厂 1：4s店修理厂）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_type")
    public static final SqlColumn<String> repairFacType = mtcLossInfo.repairFacType;

    /**
     * Database Column Remarks:
     *   修理厂资质（1：一类厂 2：二类厂 3：三类厂）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.factory_qualification")
    public static final SqlColumn<String> factoryQualification = mtcLossInfo.factoryQualification;

    /**
     * Database Column Remarks:
     *   修理厂联系方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_phone")
    public static final SqlColumn<String> repairFacPhone = mtcLossInfo.repairFacPhone;

    /**
     * Database Column Remarks:
     *   修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.repair_fac_name")
    public static final SqlColumn<String> repairFacName = mtcLossInfo.repairFacName;

    /**
     * Database Column Remarks:
     *   VIN码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vin_no")
    public static final SqlColumn<String> vinNo = mtcLossInfo.vinNo;

    /**
     * Database Column Remarks:
     *   发动机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.engine_no")
    public static final SqlColumn<String> engineNo = mtcLossInfo.engineNo;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.plate_no")
    public static final SqlColumn<String> plateNo = mtcLossInfo.plateNo;

    /**
     * Database Column Remarks:
     *   初登日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.enrol_date")
    public static final SqlColumn<String> enrolDate = mtcLossInfo.enrolDate;

    /**
     * Database Column Remarks:
     *   自核价标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_esti_flag")
    public static final SqlColumn<String> selfEstiFlag = mtcLossInfo.selfEstiFlag;

    /**
     * Database Column Remarks:
     *   自核损标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.self_approve_flag")
    public static final SqlColumn<String> selfApproveFlag = mtcLossInfo.selfApproveFlag;

    /**
     * Database Column Remarks:
     *   险别代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.insurance_code")
    public static final SqlColumn<String> insuranceCode = mtcLossInfo.insuranceCode;

    /**
     * Database Column Remarks:
     *   险别名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.insurance_name")
    public static final SqlColumn<String> insuranceName = mtcLossInfo.insuranceName;

    /**
     * Database Column Remarks:
     *   组织机构代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.mix_code")
    public static final SqlColumn<String> mixCode = mtcLossInfo.mixCode;

    /**
     * Database Column Remarks:
     *   定型方式（1：承保车型匹配方式 2：VIN码定型方式 3：智能定型方式 4：模糊定型方式 5：VIN码定型出车型，但无理赔车型 6：自定义车型）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_setting_mode")
    public static final SqlColumn<String> vehicleSettingMode = mtcLossInfo.vehicleSettingMode;

    /**
     * Database Column Remarks:
     *   定损车型与承保车型是否匹配（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.model_match_flag")
    public static final SqlColumn<String> modelMatchFlag = mtcLossInfo.modelMatchFlag;

    /**
     * Database Column Remarks:
     *   定损方式（01：修复定损 02：推定全损 03：实际全损 04：协议定损）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.eval_type_code")
    public static final SqlColumn<String> evalTypeCode = mtcLossInfo.evalTypeCode;

    /**
     * Database Column Remarks:
     *   事故类型（1：碰撞 2：涉水 3：火自爆 4：盗抢）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.accident_cause_code")
    public static final SqlColumn<String> accidentCauseCode = mtcLossInfo.accidentCauseCode;

    /**
     * Database Column Remarks:
     *   重开次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.clm_tms")
    public static final SqlColumn<String> clmTms = mtcLossInfo.clmTms;

    /**
     * Database Column Remarks:
     *   定损全损整车损失金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_sum")
    public static final SqlColumn<BigDecimal> allLoseSum = mtcLossInfo.allLoseSum;

    /**
     * Database Column Remarks:
     *   定损全损整车残值金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_remains_sum")
    public static final SqlColumn<BigDecimal> allLoseRemainsSum = mtcLossInfo.allLoseRemainsSum;

    /**
     * Database Column Remarks:
     *   定损全损整车施救费金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_salv_sum")
    public static final SqlColumn<BigDecimal> allLoseSalvSum = mtcLossInfo.allLoseSalvSum;

    /**
     * Database Column Remarks:
     *   定损全损合计金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.all_lose_total_sum")
    public static final SqlColumn<BigDecimal> allLoseTotalSum = mtcLossInfo.allLoseTotalSum;

    /**
     * Database Column Remarks:
     *   换件折扣
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.part_discount_percent")
    public static final SqlColumn<BigDecimal> partDiscountPercent = mtcLossInfo.partDiscountPercent;

    /**
     * Database Column Remarks:
     *   发动机型号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.engine_type")
    public static final SqlColumn<String> engineType = mtcLossInfo.engineType;

    /**
     * Database Column Remarks:
     *   燃料类型（01：汽油 02：柴油）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.fuel_type")
    public static final SqlColumn<String> fuelType = mtcLossInfo.fuelType;

    /**
     * Database Column Remarks:
     *   车型产地（01：国产 02：进口）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_origin")
    public static final SqlColumn<String> vehicleOrigin = mtcLossInfo.vehicleOrigin;

    /**
     * Database Column Remarks:
     *   定损车种
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.vehicle_type")
    public static final SqlColumn<String> vehicleType = mtcLossInfo.vehicleType;

    /**
     * Database Column Remarks:
     *   核损施救费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_salvage_fee")
    public static final SqlColumn<BigDecimal> auditSalvageFee = mtcLossInfo.auditSalvageFee;

    /**
     * Database Column Remarks:
     *   核损折扣残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_remnant_fee")
    public static final SqlColumn<BigDecimal> auditRemnantFee = mtcLossInfo.auditRemnantFee;

    /**
     * Database Column Remarks:
     *   核损换件合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_part_sum")
    public static final SqlColumn<BigDecimal> auditPartSum = mtcLossInfo.auditPartSum;

    /**
     * Database Column Remarks:
     *   核损工时合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_repiar_sum")
    public static final SqlColumn<BigDecimal> auditRepiarSum = mtcLossInfo.auditRepiarSum;

    /**
     * Database Column Remarks:
     *   核损辅料合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_mate_sum")
    public static final SqlColumn<BigDecimal> auditMateSum = mtcLossInfo.auditMateSum;

    /**
     * Database Column Remarks:
     *   核损管理费合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.total_manage_sum")
    public static final SqlColumn<BigDecimal> totalManageSum = mtcLossInfo.totalManageSum;

    /**
     * Database Column Remarks:
     *   核损自付合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_self_pay_sum")
    public static final SqlColumn<BigDecimal> auditSelfPaySum = mtcLossInfo.auditSelfPaySum;

    /**
     * Database Column Remarks:
     *   核损外修合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_outer_sum")
    public static final SqlColumn<BigDecimal> auditOuterSum = mtcLossInfo.auditOuterSum;

    /**
     * Database Column Remarks:
     *   核损减损合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_derogation_sum")
    public static final SqlColumn<BigDecimal> auditDerogationSum = mtcLossInfo.auditDerogationSum;

    /**
     * Database Column Remarks:
     *   核损员代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_handler_code")
    public static final SqlColumn<String> auditHandlerCode = mtcLossInfo.auditHandlerCode;

    /**
     * Database Column Remarks:
     *   核损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_remark")
    public static final SqlColumn<String> auditRemark = mtcLossInfo.auditRemark;

    /**
     * Database Column Remarks:
     *   核损整单合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.total_sum")
    public static final SqlColumn<BigDecimal> totalSum = mtcLossInfo.totalSum;

    /**
     * Database Column Remarks:
     *   核损全损整车损失金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_sum")
    public static final SqlColumn<BigDecimal> auditAllLoseSum = mtcLossInfo.auditAllLoseSum;

    /**
     * Database Column Remarks:
     *   核损全损整车残值金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_remains_sum")
    public static final SqlColumn<BigDecimal> auditAllLoseRemainsSum = mtcLossInfo.auditAllLoseRemainsSum;

    /**
     * Database Column Remarks:
     *   核损全损整车施救费金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_salv_sum")
    public static final SqlColumn<BigDecimal> auditAllLoseSalvSum = mtcLossInfo.auditAllLoseSalvSum;

    /**
     * Database Column Remarks:
     *   核损全损合计金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.audit_all_lose_total_sum")
    public static final SqlColumn<BigDecimal> auditAllLoseTotalSum = mtcLossInfo.auditAllLoseTotalSum;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.status")
    public static final SqlColumn<Integer> status = mtcLossInfo.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.misc_Desc")
    public static final SqlColumn<String> miscDesc = mtcLossInfo.miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.create_by")
    public static final SqlColumn<String> createBy = mtcLossInfo.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.created_time")
    public static final SqlColumn<Date> createdTime = mtcLossInfo.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.update_by")
    public static final SqlColumn<String> updateBy = mtcLossInfo.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_info.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcLossInfo.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_info")
    public static final class MtcLossInfo extends AliasableSqlTable<MtcLossInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<String> vehCertainCode = column("veh_certain_code", JDBCType.VARCHAR);

        public final SqlColumn<String> vehCertainName = column("veh_certain_name", JDBCType.VARCHAR);

        public final SqlColumn<String> vehGroupCode = column("veh_group_code", JDBCType.VARCHAR);

        public final SqlColumn<String> groupName = column("group_name", JDBCType.VARCHAR);

        public final SqlColumn<String> vehBrandCode = column("veh_brand_code", JDBCType.VARCHAR);

        public final SqlColumn<String> brandName = column("brand_name", JDBCType.VARCHAR);

        public final SqlColumn<String> selfConfigFlag = column("self_config_flag", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> salvageFee = column("salvage_fee", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> remnantFee = column("remnant_fee", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> manageFee = column("manage_fee", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> evalPartSum = column("eval_part_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> evalRepairSum = column("eval_repair_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> evalMateSum = column("eval_mate_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> selfPaySum = column("self_pay_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> outerSum = column("outer_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> derogationSum = column("derogation_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> sumLossAmount = column("sum_loss_amount", JDBCType.DECIMAL);

        public final SqlColumn<String> handlerCode = column("handler_code", JDBCType.VARCHAR);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> priceType = column("price_type", JDBCType.VARCHAR);

        public final SqlColumn<String> repairFacId = column("repair_fac_id", JDBCType.VARCHAR);

        public final SqlColumn<String> repairFacCode = column("repair_fac_code", JDBCType.VARCHAR);

        public final SqlColumn<String> repairFacType = column("repair_fac_type", JDBCType.VARCHAR);

        public final SqlColumn<String> factoryQualification = column("factory_qualification", JDBCType.VARCHAR);

        public final SqlColumn<String> repairFacPhone = column("repair_fac_phone", JDBCType.VARCHAR);

        public final SqlColumn<String> repairFacName = column("repair_fac_name", JDBCType.VARCHAR);

        public final SqlColumn<String> vinNo = column("vin_no", JDBCType.VARCHAR);

        public final SqlColumn<String> engineNo = column("engine_no", JDBCType.VARCHAR);

        public final SqlColumn<String> plateNo = column("plate_no", JDBCType.VARCHAR);

        public final SqlColumn<String> enrolDate = column("enrol_date", JDBCType.VARCHAR);

        public final SqlColumn<String> selfEstiFlag = column("self_esti_flag", JDBCType.VARCHAR);

        public final SqlColumn<String> selfApproveFlag = column("self_approve_flag", JDBCType.VARCHAR);

        public final SqlColumn<String> insuranceCode = column("insurance_code", JDBCType.VARCHAR);

        public final SqlColumn<String> insuranceName = column("insurance_name", JDBCType.VARCHAR);

        public final SqlColumn<String> mixCode = column("mix_code", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleSettingMode = column("vehicle_setting_mode", JDBCType.VARCHAR);

        public final SqlColumn<String> modelMatchFlag = column("model_match_flag", JDBCType.VARCHAR);

        public final SqlColumn<String> evalTypeCode = column("eval_type_code", JDBCType.VARCHAR);

        public final SqlColumn<String> accidentCauseCode = column("accident_cause_code", JDBCType.VARCHAR);

        public final SqlColumn<String> clmTms = column("clm_tms", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> allLoseSum = column("all_lose_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> allLoseRemainsSum = column("all_lose_remains_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> allLoseSalvSum = column("all_lose_salv_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> allLoseTotalSum = column("all_lose_total_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> partDiscountPercent = column("part_discount_percent", JDBCType.DECIMAL);

        public final SqlColumn<String> engineType = column("engine_type", JDBCType.VARCHAR);

        public final SqlColumn<String> fuelType = column("fuel_type", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleOrigin = column("vehicle_origin", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleType = column("vehicle_type", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> auditSalvageFee = column("audit_salvage_fee", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditRemnantFee = column("audit_remnant_fee", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditPartSum = column("audit_part_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditRepiarSum = column("audit_repiar_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditMateSum = column("audit_mate_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> totalManageSum = column("total_manage_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditSelfPaySum = column("audit_self_pay_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditOuterSum = column("audit_outer_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditDerogationSum = column("audit_derogation_sum", JDBCType.DECIMAL);

        public final SqlColumn<String> auditHandlerCode = column("audit_handler_code", JDBCType.VARCHAR);

        public final SqlColumn<String> auditRemark = column("audit_remark", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> totalSum = column("total_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditAllLoseSum = column("audit_all_lose_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditAllLoseRemainsSum = column("audit_all_lose_remains_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditAllLoseSalvSum = column("audit_all_lose_salv_sum", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditAllLoseTotalSum = column("audit_all_lose_total_sum", JDBCType.DECIMAL);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_Desc", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcLossInfo() {
            super("mtc_loss_info", MtcLossInfo::new);
        }
    }
}