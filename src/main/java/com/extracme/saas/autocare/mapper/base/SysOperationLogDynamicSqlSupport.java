package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SysOperationLogDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    public static final SysOperationLog sysOperationLog = new SysOperationLog();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.id")
    public static final SqlColumn<Long> id = sysOperationLog.id;

    /**
     * Database Column Remarks:
     *   操作人ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.user_id")
    public static final SqlColumn<Long> userId = sysOperationLog.userId;

    /**
     * Database Column Remarks:
     *   操作人用户名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.username")
    public static final SqlColumn<String> username = sysOperationLog.username;

    /**
     * Database Column Remarks:
     *   操作人所属机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.org_id")
    public static final SqlColumn<Long> orgId = sysOperationLog.orgId;

    /**
     * Database Column Remarks:
     *   租户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.tenant_id")
    public static final SqlColumn<Long> tenantId = sysOperationLog.tenantId;

    /**
     * Database Column Remarks:
     *   操作模块
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.module")
    public static final SqlColumn<String> module = sysOperationLog.module;

    /**
     * Database Column Remarks:
     *   操作类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.operation")
    public static final SqlColumn<String> operation = sysOperationLog.operation;

    /**
     * Database Column Remarks:
     *   请求方法
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.method")
    public static final SqlColumn<String> method = sysOperationLog.method;

    /**
     * Database Column Remarks:
     *   操作IP
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.ip")
    public static final SqlColumn<String> ip = sysOperationLog.ip;

    /**
     * Database Column Remarks:
     *   操作地点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.location")
    public static final SqlColumn<String> location = sysOperationLog.location;

    /**
     * Database Column Remarks:
     *   用户代理
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.user_agent")
    public static final SqlColumn<String> userAgent = sysOperationLog.userAgent;

    /**
     * Database Column Remarks:
     *   操作状态：0-失败，1-成功
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.status")
    public static final SqlColumn<Boolean> status = sysOperationLog.status;

    /**
     * Database Column Remarks:
     *   操作时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.operation_time")
    public static final SqlColumn<Date> operationTime = sysOperationLog.operationTime;

    /**
     * Database Column Remarks:
     *   请求参数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.params")
    public static final SqlColumn<String> params = sysOperationLog.params;

    /**
     * Database Column Remarks:
     *   错误消息
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_operation_log.error_msg")
    public static final SqlColumn<String> errorMsg = sysOperationLog.errorMsg;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    public static final class SysOperationLog extends AliasableSqlTable<SysOperationLog> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> userId = column("user_id", JDBCType.BIGINT);

        public final SqlColumn<String> username = column("username", JDBCType.VARCHAR);

        public final SqlColumn<Long> orgId = column("org_id", JDBCType.BIGINT);

        public final SqlColumn<Long> tenantId = column("tenant_id", JDBCType.BIGINT);

        public final SqlColumn<String> module = column("module", JDBCType.VARCHAR);

        public final SqlColumn<String> operation = column("operation", JDBCType.VARCHAR);

        public final SqlColumn<String> method = column("method", JDBCType.VARCHAR);

        public final SqlColumn<String> ip = column("ip", JDBCType.VARCHAR);

        public final SqlColumn<String> location = column("location", JDBCType.VARCHAR);

        public final SqlColumn<String> userAgent = column("user_agent", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> status = column("status", JDBCType.BIT);

        public final SqlColumn<Date> operationTime = column("operation_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> params = column("params", JDBCType.LONGVARCHAR);

        public final SqlColumn<String> errorMsg = column("error_msg", JDBCType.LONGVARCHAR);

        public SysOperationLog() {
            super("sys_operation_log", SysOperationLog::new);
        }
    }
}