package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SysTenantDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    public static final SysTenant sysTenant = new SysTenant();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.id")
    public static final SqlColumn<Long> id = sysTenant.id;

    /**
     * Database Column Remarks:
     *   租户名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.tenant_name")
    public static final SqlColumn<String> tenantName = sysTenant.tenantName;

    /**
     * Database Column Remarks:
     *   租户编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.tenant_code")
    public static final SqlColumn<String> tenantCode = sysTenant.tenantCode;

    /**
     * Database Column Remarks:
     *   联系人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.contact_name")
    public static final SqlColumn<String> contactName = sysTenant.contactName;

    /**
     * Database Column Remarks:
     *   联系人电话
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.contact_phone")
    public static final SqlColumn<String> contactPhone = sysTenant.contactPhone;

    /**
     * Database Column Remarks:
     *   联系人邮箱
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.contact_email")
    public static final SqlColumn<String> contactEmail = sysTenant.contactEmail;

    /**
     * Database Column Remarks:
     *   租约过期时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.expire_time")
    public static final SqlColumn<Date> expireTime = sysTenant.expireTime;

    /**
     * Database Column Remarks:
     *   最大用户数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.max_user_count")
    public static final SqlColumn<Integer> maxUserCount = sysTenant.maxUserCount;

    /**
     * Database Column Remarks:
     *   状态：0-禁用，1-启用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.status")
    public static final SqlColumn<Integer> status = sysTenant.status;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.create_by")
    public static final SqlColumn<String> createBy = sysTenant.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.created_time")
    public static final SqlColumn<Date> createdTime = sysTenant.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.update_by")
    public static final SqlColumn<String> updateBy = sysTenant.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant.updated_time")
    public static final SqlColumn<Date> updatedTime = sysTenant.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    public static final class SysTenant extends AliasableSqlTable<SysTenant> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> tenantName = column("tenant_name", JDBCType.VARCHAR);

        public final SqlColumn<String> tenantCode = column("tenant_code", JDBCType.VARCHAR);

        public final SqlColumn<String> contactName = column("contact_name", JDBCType.VARCHAR);

        public final SqlColumn<String> contactPhone = column("contact_phone", JDBCType.VARCHAR);

        public final SqlColumn<String> contactEmail = column("contact_email", JDBCType.VARCHAR);

        public final SqlColumn<Date> expireTime = column("expire_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> maxUserCount = column("max_user_count", JDBCType.INTEGER);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public SysTenant() {
            super("sys_tenant", SysTenant::new);
        }
    }
}