package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcRepairItemCheckInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    public static final MtcRepairItemCheckInfo mtcRepairItemCheckInfo = new MtcRepairItemCheckInfo();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.id")
    public static final SqlColumn<Long> id = mtcRepairItemCheckInfo.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.task_no")
    public static final SqlColumn<String> taskNo = mtcRepairItemCheckInfo.taskNo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.vin")
    public static final SqlColumn<String> vin = mtcRepairItemCheckInfo.vin;

    /**
     * Database Column Remarks:
     *   配件id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_id")
    public static final SqlColumn<Long> itemId = mtcRepairItemCheckInfo.itemId;

    /**
     * Database Column Remarks:
     *   配件编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_no")
    public static final SqlColumn<String> itemNo = mtcRepairItemCheckInfo.itemNo;

    /**
     * Database Column Remarks:
     *   配件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_name")
    public static final SqlColumn<String> itemName = mtcRepairItemCheckInfo.itemName;

    /**
     * Database Column Remarks:
     *   配件类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_type")
    public static final SqlColumn<Integer> itemType = mtcRepairItemCheckInfo.itemType;

    /**
     * Database Column Remarks:
     *   配件数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.item_number")
    public static final SqlColumn<Integer> itemNumber = mtcRepairItemCheckInfo.itemNumber;

    /**
     * Database Column Remarks:
     *   核损数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_number")
    public static final SqlColumn<Integer> viewNumber = mtcRepairItemCheckInfo.viewNumber;

    /**
     * Database Column Remarks:
     *   0非进保预审 1进保预审
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_pre_review_status")
    public static final SqlColumn<Integer> insurancePreReviewStatus = mtcRepairItemCheckInfo.insurancePreReviewStatus;

    /**
     * Database Column Remarks:
     *   定损材料费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_material_cost_price")
    public static final SqlColumn<BigDecimal> insuranceQuoteMaterialCostPrice = mtcRepairItemCheckInfo.insuranceQuoteMaterialCostPrice;

    /**
     * Database Column Remarks:
     *   定损工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_hour_fee_price")
    public static final SqlColumn<BigDecimal> insuranceQuoteHourFeePrice = mtcRepairItemCheckInfo.insuranceQuoteHourFeePrice;

    /**
     * Database Column Remarks:
     *   定损合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.insurance_quote_amount")
    public static final SqlColumn<BigDecimal> insuranceQuoteAmount = mtcRepairItemCheckInfo.insuranceQuoteAmount;

    /**
     * Database Column Remarks:
     *   核损材料费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_material_cost_price")
    public static final SqlColumn<BigDecimal> viewMaterialCostPrice = mtcRepairItemCheckInfo.viewMaterialCostPrice;

    /**
     * Database Column Remarks:
     *   核损工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_hour_fee_price")
    public static final SqlColumn<BigDecimal> viewHourFeePrice = mtcRepairItemCheckInfo.viewHourFeePrice;

    /**
     * Database Column Remarks:
     *   核损总计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.view_amount")
    public static final SqlColumn<BigDecimal> viewAmount = mtcRepairItemCheckInfo.viewAmount;

    /**
     * Database Column Remarks:
     *   核损状态 0未核损 1通过 2价格异议 3建议剔除
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.check_status")
    public static final SqlColumn<Integer> checkStatus = mtcRepairItemCheckInfo.checkStatus;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.remark")
    public static final SqlColumn<String> remark = mtcRepairItemCheckInfo.remark;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.status")
    public static final SqlColumn<Integer> status = mtcRepairItemCheckInfo.status;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.create_by")
    public static final SqlColumn<String> createBy = mtcRepairItemCheckInfo.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.created_time")
    public static final SqlColumn<Date> createdTime = mtcRepairItemCheckInfo.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.update_by")
    public static final SqlColumn<String> updateBy = mtcRepairItemCheckInfo.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_check_info.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcRepairItemCheckInfo.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    public static final class MtcRepairItemCheckInfo extends AliasableSqlTable<MtcRepairItemCheckInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<Long> itemId = column("item_id", JDBCType.BIGINT);

        public final SqlColumn<String> itemNo = column("item_no", JDBCType.VARCHAR);

        public final SqlColumn<String> itemName = column("item_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> itemType = column("item_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> itemNumber = column("item_number", JDBCType.INTEGER);

        public final SqlColumn<Integer> viewNumber = column("view_number", JDBCType.INTEGER);

        public final SqlColumn<Integer> insurancePreReviewStatus = column("insurance_pre_review_status", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> insuranceQuoteMaterialCostPrice = column("insurance_quote_material_cost_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> insuranceQuoteHourFeePrice = column("insurance_quote_hour_fee_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> insuranceQuoteAmount = column("insurance_quote_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> viewMaterialCostPrice = column("view_material_cost_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> viewHourFeePrice = column("view_hour_fee_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> viewAmount = column("view_amount", JDBCType.DECIMAL);

        public final SqlColumn<Integer> checkStatus = column("check_status", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcRepairItemCheckInfo() {
            super("mtc_repair_item_check_info", MtcRepairItemCheckInfo::new);
        }
    }
}