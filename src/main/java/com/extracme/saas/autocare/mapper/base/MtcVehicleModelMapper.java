package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcVehicleModelDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcVehicleModel;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcVehicleModelMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    BasicColumn[] selectList = BasicColumn.columnList(id, vehicleModelName, createTime, createBy, updateTime, updateBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcVehicleModel> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcVehicleModelResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR)
    })
    List<MtcVehicleModel> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcVehicleModelResult")
    Optional<MtcVehicleModel> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcVehicleModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcVehicleModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default int insert(MtcVehicleModel row) {
        return MyBatis3Utils.insert(this::insert, row, mtcVehicleModel, c ->
            c.map(vehicleModelName).toProperty("vehicleModelName")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default int insertSelective(MtcVehicleModel row) {
        return MyBatis3Utils.insert(this::insert, row, mtcVehicleModel, c ->
            c.map(vehicleModelName).toPropertyWhenPresent("vehicleModelName", row::getVehicleModelName)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default Optional<MtcVehicleModel> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcVehicleModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default List<MtcVehicleModel> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcVehicleModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default List<MtcVehicleModel> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcVehicleModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default Optional<MtcVehicleModel> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcVehicleModel, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcVehicleModel row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vehicleModelName).equalTo(row::getVehicleModelName)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcVehicleModel row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vehicleModelName).equalToWhenPresent(row::getVehicleModelName)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default int updateByPrimaryKey(MtcVehicleModel row) {
        return update(c ->
            c.set(vehicleModelName).equalTo(row::getVehicleModelName)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    default int updateByPrimaryKeySelective(MtcVehicleModel row) {
        return update(c ->
            c.set(vehicleModelName).equalToWhenPresent(row::getVehicleModelName)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }
}