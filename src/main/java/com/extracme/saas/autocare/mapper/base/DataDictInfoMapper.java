package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.DataDictInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.DataDictInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DataDictInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, dataName, dataCode, codeType, createTime, createBy, updateTime, updateBy, dataValue);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<DataDictInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DataDictInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="data_name", property="dataName", jdbcType=JdbcType.VARCHAR),
        @Result(column="data_code", property="dataCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="code_type", property="codeType", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="data_value", property="dataValue", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<DataDictInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DataDictInfoResult")
    Optional<DataDictInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, dataDictInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, dataDictInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default int insert(DataDictInfo row) {
        return MyBatis3Utils.insert(this::insert, row, dataDictInfo, c ->
            c.map(dataName).toProperty("dataName")
            .map(dataCode).toProperty("dataCode")
            .map(codeType).toProperty("codeType")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
            .map(dataValue).toProperty("dataValue")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default int insertSelective(DataDictInfo row) {
        return MyBatis3Utils.insert(this::insert, row, dataDictInfo, c ->
            c.map(dataName).toPropertyWhenPresent("dataName", row::getDataName)
            .map(dataCode).toPropertyWhenPresent("dataCode", row::getDataCode)
            .map(codeType).toPropertyWhenPresent("codeType", row::getCodeType)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(dataValue).toPropertyWhenPresent("dataValue", row::getDataValue)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default Optional<DataDictInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, dataDictInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default List<DataDictInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, dataDictInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default List<DataDictInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, dataDictInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default Optional<DataDictInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, dataDictInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    static UpdateDSL<UpdateModel> updateAllColumns(DataDictInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(dataName).equalTo(row::getDataName)
                .set(dataCode).equalTo(row::getDataCode)
                .set(codeType).equalTo(row::getCodeType)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(dataValue).equalTo(row::getDataValue);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DataDictInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(dataName).equalToWhenPresent(row::getDataName)
                .set(dataCode).equalToWhenPresent(row::getDataCode)
                .set(codeType).equalToWhenPresent(row::getCodeType)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(dataValue).equalToWhenPresent(row::getDataValue);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default int updateByPrimaryKey(DataDictInfo row) {
        return update(c ->
            c.set(dataName).equalTo(row::getDataName)
            .set(dataCode).equalTo(row::getDataCode)
            .set(codeType).equalTo(row::getCodeType)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(dataValue).equalTo(row::getDataValue)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_dict_info")
    default int updateByPrimaryKeySelective(DataDictInfo row) {
        return update(c ->
            c.set(dataName).equalToWhenPresent(row::getDataName)
            .set(dataCode).equalToWhenPresent(row::getDataCode)
            .set(codeType).equalToWhenPresent(row::getCodeType)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(dataValue).equalToWhenPresent(row::getDataValue)
            .where(id, isEqualTo(row::getId))
        );
    }
}