package com.extracme.saas.autocare.mapper.extend;

import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditDerogationItemCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditDerogationItemName;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditDerogationPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditDerogationPriceType;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditEvalOuterPirce;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditItemCoverCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditOutItemAmount;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditPartPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditReferencePartPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditRemark;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditRepairFactoryCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditRepairFactoryId;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditRepairFactoryName;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditRepairHandaddFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.auditRepairOuterSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.checkState;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.clmTms;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.createBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.createdTime;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.derogationItemCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.derogationItemName;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.derogationPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.derogationPriceType;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.evalOuterPirce;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.itemCoverCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.miscDesc;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.outItemAmount;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.outerId;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.outerName;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.partAmount;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.partPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.referencePartPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.referencePrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.remark;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.repairFactoryCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.repairFactoryId;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.repairFactoryName;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.repairHandaddFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.repairOuterSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.status;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.taskNo;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.updateBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import org.apache.ibatis.annotations.Mapper;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoMapper;
import com.extracme.saas.autocare.model.entity.MtcLossOuterRepairInfo;

@Mapper
@TenantSchema
public interface MtcLossOuterRepairInfoExtendMapper extends MtcLossOuterRepairInfoMapper {

    /**
     * 根据taskNo和outerId更新数据
     * 
     * @param row
     * @return
     */
    default int updateSelectiveByTaskNoAndStatus(MtcLossOuterRepairInfo row) {
        return update(c -> c.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(outerId).equalToWhenPresent(row::getOuterId)
                .set(outerName).equalToWhenPresent(row::getOuterName)
                .set(repairHandaddFlag).equalToWhenPresent(row::getRepairHandaddFlag)
                .set(evalOuterPirce).equalToWhenPresent(row::getEvalOuterPirce)
                .set(derogationPrice).equalToWhenPresent(row::getDerogationPrice)
                .set(derogationItemName).equalToWhenPresent(row::getDerogationItemName)
                .set(derogationItemCode).equalToWhenPresent(row::getDerogationItemCode)
                .set(derogationPriceType).equalToWhenPresent(row::getDerogationPriceType)
                .set(partPrice).equalToWhenPresent(row::getPartPrice)
                .set(repairFactoryId).equalToWhenPresent(row::getRepairFactoryId)
                .set(repairFactoryName).equalToWhenPresent(row::getRepairFactoryName)
                .set(repairFactoryCode).equalToWhenPresent(row::getRepairFactoryCode)
                .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(partAmount).equalToWhenPresent(row::getPartAmount)
                .set(repairOuterSum).equalToWhenPresent(row::getRepairOuterSum)
                .set(referencePartPrice).equalToWhenPresent(row::getReferencePartPrice)
                .set(outItemAmount).equalToWhenPresent(row::getOutItemAmount)
                .set(clmTms).equalToWhenPresent(row::getClmTms)
                .set(referencePrice).equalToWhenPresent(row::getReferencePrice)
                .set(auditRepairHandaddFlag).equalToWhenPresent(row::getAuditRepairHandaddFlag)
                .set(auditEvalOuterPirce).equalToWhenPresent(row::getAuditEvalOuterPirce)
                .set(auditDerogationPrice).equalToWhenPresent(row::getAuditDerogationPrice)
                .set(auditDerogationItemName).equalToWhenPresent(row::getAuditDerogationItemName)
                .set(auditDerogationItemCode).equalToWhenPresent(row::getAuditDerogationItemCode)
                .set(auditDerogationPriceType).equalToWhenPresent(row::getAuditDerogationPriceType)
                .set(auditPartPrice).equalToWhenPresent(row::getAuditPartPrice)
                .set(auditRepairFactoryId).equalToWhenPresent(row::getAuditRepairFactoryId)
                .set(auditRepairFactoryName).equalToWhenPresent(row::getAuditRepairFactoryName)
                .set(auditRepairFactoryCode).equalToWhenPresent(row::getAuditRepairFactoryCode)
                .set(auditItemCoverCode).equalToWhenPresent(row::getAuditItemCoverCode)
                .set(checkState).equalToWhenPresent(row::getCheckState)
                .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
                .set(auditRepairOuterSum).equalToWhenPresent(row::getAuditRepairOuterSum)
                .set(auditReferencePartPrice).equalToWhenPresent(row::getAuditReferencePartPrice)
                .set(auditOutItemAmount).equalToWhenPresent(row::getAuditOutItemAmount)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .where(taskNo, isEqualTo(row::getTaskNo))
                .and(status, isEqualTo(1))
                .and(outerId, isEqualTo(row::getOuterId)));
    }
}
