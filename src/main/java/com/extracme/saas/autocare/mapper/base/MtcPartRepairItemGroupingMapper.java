package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcPartRepairItemGroupingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcPartRepairItemGrouping;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcPartRepairItemGroupingMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    BasicColumn[] selectList = BasicColumn.columnList(id, groupingId, groupingName, groupingType, fatherId, levelNo, childFlag, status, remark, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcPartRepairItemGrouping> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcPartRepairItemGroupingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="grouping_id", property="groupingId", jdbcType=JdbcType.BIGINT),
        @Result(column="grouping_name", property="groupingName", jdbcType=JdbcType.VARCHAR),
        @Result(column="grouping_type", property="groupingType", jdbcType=JdbcType.INTEGER),
        @Result(column="father_id", property="fatherId", jdbcType=JdbcType.BIGINT),
        @Result(column="level_no", property="levelNo", jdbcType=JdbcType.INTEGER),
        @Result(column="child_flag", property="childFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcPartRepairItemGrouping> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcPartRepairItemGroupingResult")
    Optional<MtcPartRepairItemGrouping> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcPartRepairItemGrouping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcPartRepairItemGrouping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default int insert(MtcPartRepairItemGrouping row) {
        return MyBatis3Utils.insert(this::insert, row, mtcPartRepairItemGrouping, c ->
            c.map(groupingId).toProperty("groupingId")
            .map(groupingName).toProperty("groupingName")
            .map(groupingType).toProperty("groupingType")
            .map(fatherId).toProperty("fatherId")
            .map(levelNo).toProperty("levelNo")
            .map(childFlag).toProperty("childFlag")
            .map(status).toProperty("status")
            .map(remark).toProperty("remark")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default int insertSelective(MtcPartRepairItemGrouping row) {
        return MyBatis3Utils.insert(this::insert, row, mtcPartRepairItemGrouping, c ->
            c.map(groupingId).toPropertyWhenPresent("groupingId", row::getGroupingId)
            .map(groupingName).toPropertyWhenPresent("groupingName", row::getGroupingName)
            .map(groupingType).toPropertyWhenPresent("groupingType", row::getGroupingType)
            .map(fatherId).toPropertyWhenPresent("fatherId", row::getFatherId)
            .map(levelNo).toPropertyWhenPresent("levelNo", row::getLevelNo)
            .map(childFlag).toPropertyWhenPresent("childFlag", row::getChildFlag)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default Optional<MtcPartRepairItemGrouping> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcPartRepairItemGrouping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default List<MtcPartRepairItemGrouping> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcPartRepairItemGrouping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default List<MtcPartRepairItemGrouping> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcPartRepairItemGrouping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default Optional<MtcPartRepairItemGrouping> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcPartRepairItemGrouping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcPartRepairItemGrouping row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(groupingId).equalTo(row::getGroupingId)
                .set(groupingName).equalTo(row::getGroupingName)
                .set(groupingType).equalTo(row::getGroupingType)
                .set(fatherId).equalTo(row::getFatherId)
                .set(levelNo).equalTo(row::getLevelNo)
                .set(childFlag).equalTo(row::getChildFlag)
                .set(status).equalTo(row::getStatus)
                .set(remark).equalTo(row::getRemark)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcPartRepairItemGrouping row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(groupingId).equalToWhenPresent(row::getGroupingId)
                .set(groupingName).equalToWhenPresent(row::getGroupingName)
                .set(groupingType).equalToWhenPresent(row::getGroupingType)
                .set(fatherId).equalToWhenPresent(row::getFatherId)
                .set(levelNo).equalToWhenPresent(row::getLevelNo)
                .set(childFlag).equalToWhenPresent(row::getChildFlag)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default int updateByPrimaryKey(MtcPartRepairItemGrouping row) {
        return update(c ->
            c.set(groupingId).equalTo(row::getGroupingId)
            .set(groupingName).equalTo(row::getGroupingName)
            .set(groupingType).equalTo(row::getGroupingType)
            .set(fatherId).equalTo(row::getFatherId)
            .set(levelNo).equalTo(row::getLevelNo)
            .set(childFlag).equalTo(row::getChildFlag)
            .set(status).equalTo(row::getStatus)
            .set(remark).equalTo(row::getRemark)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    default int updateByPrimaryKeySelective(MtcPartRepairItemGrouping row) {
        return update(c ->
            c.set(groupingId).equalToWhenPresent(row::getGroupingId)
            .set(groupingName).equalToWhenPresent(row::getGroupingName)
            .set(groupingType).equalToWhenPresent(row::getGroupingType)
            .set(fatherId).equalToWhenPresent(row::getFatherId)
            .set(levelNo).equalToWhenPresent(row::getLevelNo)
            .set(childFlag).equalToWhenPresent(row::getChildFlag)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}