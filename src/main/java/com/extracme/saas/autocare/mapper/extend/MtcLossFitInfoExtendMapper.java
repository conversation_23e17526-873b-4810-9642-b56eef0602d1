package com.extracme.saas.autocare.mapper.extend;

import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.apprBackFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.apprManageFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.apprPartSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.apprRemainsPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.auditCount;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.auditMaterialFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.auditRecheckFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.auditRemark;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.checkState;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.chgCompSetCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.clmTms;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.cornerMark;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.count;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.createBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.createdTime;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.detectedFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.directSupplier;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.directSupplyFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.evalPartSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.evalPartSumFirst;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.fitBackFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.ifWading;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.itemCoverCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.itemName;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.localApplicablePrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.localBrandPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.localGuidePrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.localMarketPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.localPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.localRemanufacturePrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.manageRate;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.manageSingleFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.manageSingleRate;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.materialFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.miscDesc;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.partCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.partId;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.recheckFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.recyclePartFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.remainsPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.remark;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.selfConfigFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.selfPayPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.selfPayRate;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.status;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.sysGuidePrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.sysMarketPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.taskNo;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.updateBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import org.apache.ibatis.annotations.Mapper;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcLossFitInfoMapper;
import com.extracme.saas.autocare.model.entity.MtcLossFitInfo;

@Mapper
@TenantSchema
public interface MtcLossFitInfoExtendMapper extends MtcLossFitInfoMapper {

    /**
     * 根据任务编号删除损失配件信息
     * 
     * @param row
     * @return
     */
    default int updateSelectiveByTaskNoAndStatus(MtcLossFitInfo row) {
        return update(c -> c.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(partId).equalToWhenPresent(row::getPartId)
                .set(partCode).equalToWhenPresent(row::getPartCode)
                .set(itemName).equalToWhenPresent(row::getItemName)
                .set(sysGuidePrice).equalToWhenPresent(row::getSysGuidePrice)
                .set(sysMarketPrice).equalToWhenPresent(row::getSysMarketPrice)
                .set(localGuidePrice).equalToWhenPresent(row::getLocalGuidePrice)
                .set(localMarketPrice).equalToWhenPresent(row::getLocalMarketPrice)
                .set(localBrandPrice).equalToWhenPresent(row::getLocalBrandPrice)
                .set(localApplicablePrice).equalToWhenPresent(row::getLocalApplicablePrice)
                .set(localRemanufacturePrice).equalToWhenPresent(row::getLocalRemanufacturePrice)
                .set(localPrice).equalToWhenPresent(row::getLocalPrice)
                .set(count).equalToWhenPresent(row::getCount)
                .set(materialFee).equalToWhenPresent(row::getMaterialFee)
                .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
                .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(chgCompSetCode).equalToWhenPresent(row::getChgCompSetCode)
                .set(fitBackFlag).equalToWhenPresent(row::getFitBackFlag)
                .set(remainsPrice).equalToWhenPresent(row::getRemainsPrice)
                .set(detectedFlag).equalToWhenPresent(row::getDetectedFlag)
                .set(directSupplyFlag).equalToWhenPresent(row::getDirectSupplyFlag)
                .set(directSupplier).equalToWhenPresent(row::getDirectSupplier)
                .set(manageSingleRate).equalToWhenPresent(row::getManageSingleRate)
                .set(manageSingleFee).equalToWhenPresent(row::getManageSingleFee)
                .set(evalPartSum).equalToWhenPresent(row::getEvalPartSum)
                .set(selfPayRate).equalToWhenPresent(row::getSelfPayRate)
                .set(recyclePartFlag).equalToWhenPresent(row::getRecyclePartFlag)
                .set(evalPartSumFirst).equalToWhenPresent(row::getEvalPartSumFirst)
                .set(cornerMark).equalToWhenPresent(row::getCornerMark)
                .set(clmTms).equalToWhenPresent(row::getClmTms)
                .set(ifWading).equalToWhenPresent(row::getIfWading)
                .set(recheckFlag).equalToWhenPresent(row::getRecheckFlag)
                .set(auditMaterialFee).equalToWhenPresent(row::getAuditMaterialFee)
                .set(auditCount).equalToWhenPresent(row::getAuditCount)
                .set(apprPartSum).equalToWhenPresent(row::getApprPartSum)
                .set(selfPayPrice).equalToWhenPresent(row::getSelfPayPrice)
                .set(apprRemainsPrice).equalToWhenPresent(row::getApprRemainsPrice)
                .set(manageRate).equalToWhenPresent(row::getManageRate)
                .set(apprManageFee).equalToWhenPresent(row::getApprManageFee)
                .set(checkState).equalToWhenPresent(row::getCheckState)
                .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
                .set(apprBackFlag).equalToWhenPresent(row::getApprBackFlag)
                .set(auditRecheckFlag).equalToWhenPresent(row::getAuditRecheckFlag)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .where(taskNo, isEqualTo(row::getTaskNo))
                .and(status, isEqualTo(1))
                .and(partId, isEqualTo(row::getPartId)));
    }
}
