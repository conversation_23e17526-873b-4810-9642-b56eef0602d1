package com.extracme.saas.autocare.mapper.extend;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.WorkflowInstanceMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 流程实例扩展Mapper
 *
 * 注意：复杂查询应在Service层使用基础Mapper的selectMany方法获取数据，
 * 然后在内存中进行映射和处理，避免创建额外的SQL查询方法
 */
@Mapper
@TenantSchema(false) // 明确指示不使用租户Schema，访问默认的auto_care_saas库
public interface WorkflowInstanceExtendMapper extends WorkflowInstanceMapper {
}
