package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ActivityStatusDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    public static final ActivityStatus activityStatus = new ActivityStatus();

    /**
     * Database Column Remarks:
     *   状态编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status.id")
    public static final SqlColumn<Long> id = activityStatus.id;

    /**
     * Database Column Remarks:
     *   状态编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status.status_code")
    public static final SqlColumn<String> statusCode = activityStatus.statusCode;

    /**
     * Database Column Remarks:
     *   状态名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status.status_name")
    public static final SqlColumn<String> statusName = activityStatus.statusName;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status.create_time")
    public static final SqlColumn<Date> createTime = activityStatus.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status.create_by")
    public static final SqlColumn<String> createBy = activityStatus.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status.update_time")
    public static final SqlColumn<Date> updateTime = activityStatus.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status.update_by")
    public static final SqlColumn<String> updateBy = activityStatus.updateBy;

    /**
     * Database Column Remarks:
     *   状态描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status.description")
    public static final SqlColumn<String> description = activityStatus.description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    public static final class ActivityStatus extends AliasableSqlTable<ActivityStatus> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> statusCode = column("status_code", JDBCType.VARCHAR);

        public final SqlColumn<String> statusName = column("status_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<String> description = column("description", JDBCType.LONGVARCHAR);

        public ActivityStatus() {
            super("activity_status", ActivityStatus::new);
        }
    }
}