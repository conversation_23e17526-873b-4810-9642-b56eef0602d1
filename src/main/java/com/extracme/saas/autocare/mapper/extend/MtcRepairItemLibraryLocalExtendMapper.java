package com.extracme.saas.autocare.mapper.extend;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcRepairItemLibraryLocalMapper;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryCheckListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import java.util.List;

/**
 * 维修项目本地信息扩展Mapper
 */
@Mapper
@TenantSchema // 可以指定某些方法不使用租户Schema
public interface MtcRepairItemLibraryLocalExtendMapper extends MtcRepairItemLibraryLocalMapper {

    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "QueryLibraryCheckListResult", value = {
            @Result(column="id", property="itemId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="item_no", property="itemNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
            @Result(column="item_type", property="itemType", jdbcType=JdbcType.INTEGER),
            @Result(column="hour_fee_national_market_price", property="hourFeeNationalMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="material_cost_national_market_price", property="materialCostNationalMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="hour_fee_local_market_price", property="hourFeeLocalMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="material_cost_local_market_price", property="materialCostLocalMarketPrice", jdbcType=JdbcType.DECIMAL),
    })
    List<RepairItemLibraryCheckListVO> selectLibraryCheckList(SelectStatementProvider selectStatement);
} 