package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.SmsSendRecordDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.SmsSendRecord;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SmsSendRecordMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    BasicColumn[] selectList = BasicColumn.columnList(id, phoneNumber, ipAddress, type, createTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<SmsSendRecord> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SmsSendRecordResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="phone_number", property="phoneNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="ip_address", property="ipAddress", jdbcType=JdbcType.VARCHAR),
        @Result(column="type", property="type", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<SmsSendRecord> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SmsSendRecordResult")
    Optional<SmsSendRecord> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, smsSendRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, smsSendRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default int insert(SmsSendRecord row) {
        return MyBatis3Utils.insert(this::insert, row, smsSendRecord, c ->
            c.map(phoneNumber).toProperty("phoneNumber")
            .map(ipAddress).toProperty("ipAddress")
            .map(type).toProperty("type")
            .map(createTime).toProperty("createTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default int insertSelective(SmsSendRecord row) {
        return MyBatis3Utils.insert(this::insert, row, smsSendRecord, c ->
            c.map(phoneNumber).toPropertyWhenPresent("phoneNumber", row::getPhoneNumber)
            .map(ipAddress).toPropertyWhenPresent("ipAddress", row::getIpAddress)
            .map(type).toPropertyWhenPresent("type", row::getType)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default Optional<SmsSendRecord> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, smsSendRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default List<SmsSendRecord> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, smsSendRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default List<SmsSendRecord> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, smsSendRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default Optional<SmsSendRecord> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, smsSendRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    static UpdateDSL<UpdateModel> updateAllColumns(SmsSendRecord row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(phoneNumber).equalTo(row::getPhoneNumber)
                .set(ipAddress).equalTo(row::getIpAddress)
                .set(type).equalTo(row::getType)
                .set(createTime).equalTo(row::getCreateTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SmsSendRecord row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(phoneNumber).equalToWhenPresent(row::getPhoneNumber)
                .set(ipAddress).equalToWhenPresent(row::getIpAddress)
                .set(type).equalToWhenPresent(row::getType)
                .set(createTime).equalToWhenPresent(row::getCreateTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default int updateByPrimaryKey(SmsSendRecord row) {
        return update(c ->
            c.set(phoneNumber).equalTo(row::getPhoneNumber)
            .set(ipAddress).equalTo(row::getIpAddress)
            .set(type).equalTo(row::getType)
            .set(createTime).equalTo(row::getCreateTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    default int updateByPrimaryKeySelective(SmsSendRecord row) {
        return update(c ->
            c.set(phoneNumber).equalToWhenPresent(row::getPhoneNumber)
            .set(ipAddress).equalToWhenPresent(row::getIpAddress)
            .set(type).equalToWhenPresent(row::getType)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}