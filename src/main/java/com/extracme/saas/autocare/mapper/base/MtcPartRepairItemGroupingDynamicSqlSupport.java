package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcPartRepairItemGroupingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    public static final MtcPartRepairItemGrouping mtcPartRepairItemGrouping = new MtcPartRepairItemGrouping();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.id")
    public static final SqlColumn<Long> id = mtcPartRepairItemGrouping.id;

    /**
     * Database Column Remarks:
     *   零件分组ID/修理项目分组ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_id")
    public static final SqlColumn<Long> groupingId = mtcPartRepairItemGrouping.groupingId;

    /**
     * Database Column Remarks:
     *   零件分组名称/修理项目分组名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_name")
    public static final SqlColumn<String> groupingName = mtcPartRepairItemGrouping.groupingName;

    /**
     * Database Column Remarks:
     *   分组类型 1:零件分组 2:修理项目分组
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.grouping_type")
    public static final SqlColumn<Integer> groupingType = mtcPartRepairItemGrouping.groupingType;

    /**
     * Database Column Remarks:
     *   父节点ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.father_id")
    public static final SqlColumn<Long> fatherId = mtcPartRepairItemGrouping.fatherId;

    /**
     * Database Column Remarks:
     *   层次编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.level_no")
    public static final SqlColumn<Integer> levelNo = mtcPartRepairItemGrouping.levelNo;

    /**
     * Database Column Remarks:
     *   子项目有无标记 1:有子项目 0:无子项目
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.child_flag")
    public static final SqlColumn<Integer> childFlag = mtcPartRepairItemGrouping.childFlag;

    /**
     * Database Column Remarks:
     *   状态（1：有效 0：无效）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.status")
    public static final SqlColumn<Integer> status = mtcPartRepairItemGrouping.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.remark")
    public static final SqlColumn<String> remark = mtcPartRepairItemGrouping.remark;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.create_by")
    public static final SqlColumn<String> createBy = mtcPartRepairItemGrouping.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.created_time")
    public static final SqlColumn<Date> createdTime = mtcPartRepairItemGrouping.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.update_by")
    public static final SqlColumn<String> updateBy = mtcPartRepairItemGrouping.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_part_repair_item_grouping.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcPartRepairItemGrouping.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_part_repair_item_grouping")
    public static final class MtcPartRepairItemGrouping extends AliasableSqlTable<MtcPartRepairItemGrouping> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> groupingId = column("grouping_id", JDBCType.BIGINT);

        public final SqlColumn<String> groupingName = column("grouping_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> groupingType = column("grouping_type", JDBCType.INTEGER);

        public final SqlColumn<Long> fatherId = column("father_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> levelNo = column("level_no", JDBCType.INTEGER);

        public final SqlColumn<Integer> childFlag = column("child_flag", JDBCType.INTEGER);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcPartRepairItemGrouping() {
            super("mtc_part_repair_item_grouping", MtcPartRepairItemGrouping::new);
        }
    }
}