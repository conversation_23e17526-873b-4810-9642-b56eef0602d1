package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.SysDataSyncLogDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.SysDataSyncLog;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysDataSyncLogMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    BasicColumn[] selectList = BasicColumn.columnList(id, batchNo, tenantId, tenantCode, targetTable, syncStatus, errorMessage, sourceIp, syncStartTime, syncEndTime, syncDuration, createTime, createBy, updateTime, updateBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<SysDataSyncLog> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysDataSyncLogResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="batch_no", property="batchNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.BIGINT),
        @Result(column="tenant_code", property="tenantCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="target_table", property="targetTable", jdbcType=JdbcType.VARCHAR),
        @Result(column="sync_status", property="syncStatus", jdbcType=JdbcType.VARCHAR),
        @Result(column="error_message", property="errorMessage", jdbcType=JdbcType.VARCHAR),
        @Result(column="source_ip", property="sourceIp", jdbcType=JdbcType.VARCHAR),
        @Result(column="sync_start_time", property="syncStartTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="sync_end_time", property="syncEndTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="sync_duration", property="syncDuration", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR)
    })
    List<SysDataSyncLog> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysDataSyncLogResult")
    Optional<SysDataSyncLog> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysDataSyncLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysDataSyncLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default int insert(SysDataSyncLog row) {
        return MyBatis3Utils.insert(this::insert, row, sysDataSyncLog, c ->
            c.map(batchNo).toProperty("batchNo")
            .map(tenantId).toProperty("tenantId")
            .map(tenantCode).toProperty("tenantCode")
            .map(targetTable).toProperty("targetTable")
            .map(syncStatus).toProperty("syncStatus")
            .map(errorMessage).toProperty("errorMessage")
            .map(sourceIp).toProperty("sourceIp")
            .map(syncStartTime).toProperty("syncStartTime")
            .map(syncEndTime).toProperty("syncEndTime")
            .map(syncDuration).toProperty("syncDuration")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default int insertSelective(SysDataSyncLog row) {
        return MyBatis3Utils.insert(this::insert, row, sysDataSyncLog, c ->
            c.map(batchNo).toPropertyWhenPresent("batchNo", row::getBatchNo)
            .map(tenantId).toPropertyWhenPresent("tenantId", row::getTenantId)
            .map(tenantCode).toPropertyWhenPresent("tenantCode", row::getTenantCode)
            .map(targetTable).toPropertyWhenPresent("targetTable", row::getTargetTable)
            .map(syncStatus).toPropertyWhenPresent("syncStatus", row::getSyncStatus)
            .map(errorMessage).toPropertyWhenPresent("errorMessage", row::getErrorMessage)
            .map(sourceIp).toPropertyWhenPresent("sourceIp", row::getSourceIp)
            .map(syncStartTime).toPropertyWhenPresent("syncStartTime", row::getSyncStartTime)
            .map(syncEndTime).toPropertyWhenPresent("syncEndTime", row::getSyncEndTime)
            .map(syncDuration).toPropertyWhenPresent("syncDuration", row::getSyncDuration)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default Optional<SysDataSyncLog> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysDataSyncLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default List<SysDataSyncLog> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysDataSyncLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default List<SysDataSyncLog> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysDataSyncLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default Optional<SysDataSyncLog> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysDataSyncLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    static UpdateDSL<UpdateModel> updateAllColumns(SysDataSyncLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(batchNo).equalTo(row::getBatchNo)
                .set(tenantId).equalTo(row::getTenantId)
                .set(tenantCode).equalTo(row::getTenantCode)
                .set(targetTable).equalTo(row::getTargetTable)
                .set(syncStatus).equalTo(row::getSyncStatus)
                .set(errorMessage).equalTo(row::getErrorMessage)
                .set(sourceIp).equalTo(row::getSourceIp)
                .set(syncStartTime).equalTo(row::getSyncStartTime)
                .set(syncEndTime).equalTo(row::getSyncEndTime)
                .set(syncDuration).equalTo(row::getSyncDuration)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysDataSyncLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(batchNo).equalToWhenPresent(row::getBatchNo)
                .set(tenantId).equalToWhenPresent(row::getTenantId)
                .set(tenantCode).equalToWhenPresent(row::getTenantCode)
                .set(targetTable).equalToWhenPresent(row::getTargetTable)
                .set(syncStatus).equalToWhenPresent(row::getSyncStatus)
                .set(errorMessage).equalToWhenPresent(row::getErrorMessage)
                .set(sourceIp).equalToWhenPresent(row::getSourceIp)
                .set(syncStartTime).equalToWhenPresent(row::getSyncStartTime)
                .set(syncEndTime).equalToWhenPresent(row::getSyncEndTime)
                .set(syncDuration).equalToWhenPresent(row::getSyncDuration)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default int updateByPrimaryKey(SysDataSyncLog row) {
        return update(c ->
            c.set(batchNo).equalTo(row::getBatchNo)
            .set(tenantId).equalTo(row::getTenantId)
            .set(tenantCode).equalTo(row::getTenantCode)
            .set(targetTable).equalTo(row::getTargetTable)
            .set(syncStatus).equalTo(row::getSyncStatus)
            .set(errorMessage).equalTo(row::getErrorMessage)
            .set(sourceIp).equalTo(row::getSourceIp)
            .set(syncStartTime).equalTo(row::getSyncStartTime)
            .set(syncEndTime).equalTo(row::getSyncEndTime)
            .set(syncDuration).equalTo(row::getSyncDuration)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    default int updateByPrimaryKeySelective(SysDataSyncLog row) {
        return update(c ->
            c.set(batchNo).equalToWhenPresent(row::getBatchNo)
            .set(tenantId).equalToWhenPresent(row::getTenantId)
            .set(tenantCode).equalToWhenPresent(row::getTenantCode)
            .set(targetTable).equalToWhenPresent(row::getTargetTable)
            .set(syncStatus).equalToWhenPresent(row::getSyncStatus)
            .set(errorMessage).equalToWhenPresent(row::getErrorMessage)
            .set(sourceIp).equalToWhenPresent(row::getSourceIp)
            .set(syncStartTime).equalToWhenPresent(row::getSyncStartTime)
            .set(syncEndTime).equalToWhenPresent(row::getSyncEndTime)
            .set(syncDuration).equalToWhenPresent(row::getSyncDuration)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }
}