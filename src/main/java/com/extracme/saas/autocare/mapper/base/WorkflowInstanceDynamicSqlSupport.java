package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class WorkflowInstanceDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    public static final WorkflowInstance workflowInstance = new WorkflowInstance();

    /**
     * Database Column Remarks:
     *   流程实例编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.id")
    public static final SqlColumn<Long> id = workflowInstance.id;

    /**
     * Database Column Remarks:
     *   所采用的流程模板编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.workflow_id")
    public static final SqlColumn<Long> workflowId = workflowInstance.workflowId;

    /**
     * Database Column Remarks:
     *   业务对象编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.business_id")
    public static final SqlColumn<String> businessId = workflowInstance.businessId;

    /**
     * Database Column Remarks:
     *   租户编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.tenant_id")
    public static final SqlColumn<Integer> tenantId = workflowInstance.tenantId;

    /**
     * Database Column Remarks:
     *   当前所在的活动节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.current_activity_code")
    public static final SqlColumn<String> currentActivityCode = workflowInstance.currentActivityCode;

    /**
     * Database Column Remarks:
     *   流程状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.status_code")
    public static final SqlColumn<String> statusCode = workflowInstance.statusCode;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.create_time")
    public static final SqlColumn<Date> createTime = workflowInstance.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.create_by")
    public static final SqlColumn<String> createBy = workflowInstance.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.update_time")
    public static final SqlColumn<Date> updateTime = workflowInstance.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_instance.update_by")
    public static final SqlColumn<String> updateBy = workflowInstance.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instance")
    public static final class WorkflowInstance extends AliasableSqlTable<WorkflowInstance> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> workflowId = column("workflow_id", JDBCType.BIGINT);

        public final SqlColumn<String> businessId = column("business_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> tenantId = column("tenant_id", JDBCType.INTEGER);

        public final SqlColumn<String> currentActivityCode = column("current_activity_code", JDBCType.VARCHAR);

        public final SqlColumn<String> statusCode = column("status_code", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public WorkflowInstance() {
            super("workflow_instance", WorkflowInstance::new);
        }
    }
}