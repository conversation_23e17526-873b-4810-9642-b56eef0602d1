package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.SysOperationLogDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.SysOperationLog;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysOperationLogMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    BasicColumn[] selectList = BasicColumn.columnList(id, userId, username, orgId, tenantId, module, operation, method, ip, location, userAgent, status, operationTime, params, errorMsg);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=true, resultType=Long.class)
    int insert(InsertStatementProvider<SysOperationLog> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysOperationLogResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="user_id", property="userId", jdbcType=JdbcType.BIGINT),
        @Result(column="username", property="username", jdbcType=JdbcType.VARCHAR),
        @Result(column="org_id", property="orgId", jdbcType=JdbcType.BIGINT),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.BIGINT),
        @Result(column="module", property="module", jdbcType=JdbcType.VARCHAR),
        @Result(column="operation", property="operation", jdbcType=JdbcType.VARCHAR),
        @Result(column="method", property="method", jdbcType=JdbcType.VARCHAR),
        @Result(column="ip", property="ip", jdbcType=JdbcType.VARCHAR),
        @Result(column="location", property="location", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_agent", property="userAgent", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.BIT),
        @Result(column="operation_time", property="operationTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="params", property="params", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="error_msg", property="errorMsg", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<SysOperationLog> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysOperationLogResult")
    Optional<SysOperationLog> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysOperationLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysOperationLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default int insert(SysOperationLog row) {
        return MyBatis3Utils.insert(this::insert, row, sysOperationLog, c ->
            c.map(id).toProperty("id")
            .map(userId).toProperty("userId")
            .map(username).toProperty("username")
            .map(orgId).toProperty("orgId")
            .map(tenantId).toProperty("tenantId")
            .map(module).toProperty("module")
            .map(operation).toProperty("operation")
            .map(method).toProperty("method")
            .map(ip).toProperty("ip")
            .map(location).toProperty("location")
            .map(userAgent).toProperty("userAgent")
            .map(status).toProperty("status")
            .map(operationTime).toProperty("operationTime")
            .map(params).toProperty("params")
            .map(errorMsg).toProperty("errorMsg")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default int insertSelective(SysOperationLog row) {
        return MyBatis3Utils.insert(this::insert, row, sysOperationLog, c ->
            c.map(id).toProperty("id")
            .map(userId).toPropertyWhenPresent("userId", row::getUserId)
            .map(username).toPropertyWhenPresent("username", row::getUsername)
            .map(orgId).toPropertyWhenPresent("orgId", row::getOrgId)
            .map(tenantId).toPropertyWhenPresent("tenantId", row::getTenantId)
            .map(module).toPropertyWhenPresent("module", row::getModule)
            .map(operation).toPropertyWhenPresent("operation", row::getOperation)
            .map(method).toPropertyWhenPresent("method", row::getMethod)
            .map(ip).toPropertyWhenPresent("ip", row::getIp)
            .map(location).toPropertyWhenPresent("location", row::getLocation)
            .map(userAgent).toPropertyWhenPresent("userAgent", row::getUserAgent)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(operationTime).toPropertyWhenPresent("operationTime", row::getOperationTime)
            .map(params).toPropertyWhenPresent("params", row::getParams)
            .map(errorMsg).toPropertyWhenPresent("errorMsg", row::getErrorMsg)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default Optional<SysOperationLog> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysOperationLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default List<SysOperationLog> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysOperationLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default List<SysOperationLog> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysOperationLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default Optional<SysOperationLog> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysOperationLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    static UpdateDSL<UpdateModel> updateAllColumns(SysOperationLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(userId).equalTo(row::getUserId)
                .set(username).equalTo(row::getUsername)
                .set(orgId).equalTo(row::getOrgId)
                .set(tenantId).equalTo(row::getTenantId)
                .set(module).equalTo(row::getModule)
                .set(operation).equalTo(row::getOperation)
                .set(method).equalTo(row::getMethod)
                .set(ip).equalTo(row::getIp)
                .set(location).equalTo(row::getLocation)
                .set(userAgent).equalTo(row::getUserAgent)
                .set(status).equalTo(row::getStatus)
                .set(operationTime).equalTo(row::getOperationTime)
                .set(params).equalTo(row::getParams)
                .set(errorMsg).equalTo(row::getErrorMsg);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysOperationLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(userId).equalToWhenPresent(row::getUserId)
                .set(username).equalToWhenPresent(row::getUsername)
                .set(orgId).equalToWhenPresent(row::getOrgId)
                .set(tenantId).equalToWhenPresent(row::getTenantId)
                .set(module).equalToWhenPresent(row::getModule)
                .set(operation).equalToWhenPresent(row::getOperation)
                .set(method).equalToWhenPresent(row::getMethod)
                .set(ip).equalToWhenPresent(row::getIp)
                .set(location).equalToWhenPresent(row::getLocation)
                .set(userAgent).equalToWhenPresent(row::getUserAgent)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(operationTime).equalToWhenPresent(row::getOperationTime)
                .set(params).equalToWhenPresent(row::getParams)
                .set(errorMsg).equalToWhenPresent(row::getErrorMsg);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default int updateByPrimaryKey(SysOperationLog row) {
        return update(c ->
            c.set(userId).equalTo(row::getUserId)
            .set(username).equalTo(row::getUsername)
            .set(orgId).equalTo(row::getOrgId)
            .set(tenantId).equalTo(row::getTenantId)
            .set(module).equalTo(row::getModule)
            .set(operation).equalTo(row::getOperation)
            .set(method).equalTo(row::getMethod)
            .set(ip).equalTo(row::getIp)
            .set(location).equalTo(row::getLocation)
            .set(userAgent).equalTo(row::getUserAgent)
            .set(status).equalTo(row::getStatus)
            .set(operationTime).equalTo(row::getOperationTime)
            .set(params).equalTo(row::getParams)
            .set(errorMsg).equalTo(row::getErrorMsg)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_operation_log")
    default int updateByPrimaryKeySelective(SysOperationLog row) {
        return update(c ->
            c.set(userId).equalToWhenPresent(row::getUserId)
            .set(username).equalToWhenPresent(row::getUsername)
            .set(orgId).equalToWhenPresent(row::getOrgId)
            .set(tenantId).equalToWhenPresent(row::getTenantId)
            .set(module).equalToWhenPresent(row::getModule)
            .set(operation).equalToWhenPresent(row::getOperation)
            .set(method).equalToWhenPresent(row::getMethod)
            .set(ip).equalToWhenPresent(row::getIp)
            .set(location).equalToWhenPresent(row::getLocation)
            .set(userAgent).equalToWhenPresent(row::getUserAgent)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(operationTime).equalToWhenPresent(row::getOperationTime)
            .set(params).equalToWhenPresent(row::getParams)
            .set(errorMsg).equalToWhenPresent(row::getErrorMsg)
            .where(id, isEqualTo(row::getId))
        );
    }
}