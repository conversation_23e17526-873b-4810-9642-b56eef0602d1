package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcOperatorLogDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcOperatorLogMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    BasicColumn[] selectList = BasicColumn.columnList(id, tableName, recordId, opeContent, currentActivityCode, remark, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcOperatorLog> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcOperatorLogResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="table_name", property="tableName", jdbcType=JdbcType.VARCHAR),
        @Result(column="record_id", property="recordId", jdbcType=JdbcType.BIGINT),
        @Result(column="ope_content", property="opeContent", jdbcType=JdbcType.VARCHAR),
        @Result(column="current_activity_code", property="currentActivityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcOperatorLog> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcOperatorLogResult")
    Optional<MtcOperatorLog> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcOperatorLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcOperatorLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default int insert(MtcOperatorLog row) {
        return MyBatis3Utils.insert(this::insert, row, mtcOperatorLog, c ->
            c.map(tableName).toProperty("tableName")
            .map(recordId).toProperty("recordId")
            .map(opeContent).toProperty("opeContent")
            .map(currentActivityCode).toProperty("currentActivityCode")
            .map(remark).toProperty("remark")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default int insertSelective(MtcOperatorLog row) {
        return MyBatis3Utils.insert(this::insert, row, mtcOperatorLog, c ->
            c.map(tableName).toPropertyWhenPresent("tableName", row::getTableName)
            .map(recordId).toPropertyWhenPresent("recordId", row::getRecordId)
            .map(opeContent).toPropertyWhenPresent("opeContent", row::getOpeContent)
            .map(currentActivityCode).toPropertyWhenPresent("currentActivityCode", row::getCurrentActivityCode)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default Optional<MtcOperatorLog> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcOperatorLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default List<MtcOperatorLog> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcOperatorLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default List<MtcOperatorLog> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcOperatorLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default Optional<MtcOperatorLog> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcOperatorLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcOperatorLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(tableName).equalTo(row::getTableName)
                .set(recordId).equalTo(row::getRecordId)
                .set(opeContent).equalTo(row::getOpeContent)
                .set(currentActivityCode).equalTo(row::getCurrentActivityCode)
                .set(remark).equalTo(row::getRemark)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcOperatorLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(tableName).equalToWhenPresent(row::getTableName)
                .set(recordId).equalToWhenPresent(row::getRecordId)
                .set(opeContent).equalToWhenPresent(row::getOpeContent)
                .set(currentActivityCode).equalToWhenPresent(row::getCurrentActivityCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default int updateByPrimaryKey(MtcOperatorLog row) {
        return update(c ->
            c.set(tableName).equalTo(row::getTableName)
            .set(recordId).equalTo(row::getRecordId)
            .set(opeContent).equalTo(row::getOpeContent)
            .set(currentActivityCode).equalTo(row::getCurrentActivityCode)
            .set(remark).equalTo(row::getRemark)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    default int updateByPrimaryKeySelective(MtcOperatorLog row) {
        return update(c ->
            c.set(tableName).equalToWhenPresent(row::getTableName)
            .set(recordId).equalToWhenPresent(row::getRecordId)
            .set(opeContent).equalToWhenPresent(row::getOpeContent)
            .set(currentActivityCode).equalToWhenPresent(row::getCurrentActivityCode)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}