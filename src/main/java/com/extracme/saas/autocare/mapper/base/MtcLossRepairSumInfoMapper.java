package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcLossRepairSumInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcLossRepairSumInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNo, workTypeCode, itemCount, referencePrice, hourDiscount, discountRefPrice, evalRepairSum, auditItemCount, apprRepairSum, status, miscDesc, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcLossRepairSumInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcLossRepairSumInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_no", property="taskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="work_type_code", property="workTypeCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_count", property="itemCount", jdbcType=JdbcType.INTEGER),
        @Result(column="reference_price", property="referencePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="hour_discount", property="hourDiscount", jdbcType=JdbcType.DECIMAL),
        @Result(column="discount_ref_price", property="discountRefPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="eval_repair_sum", property="evalRepairSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_item_count", property="auditItemCount", jdbcType=JdbcType.INTEGER),
        @Result(column="appr_repair_sum", property="apprRepairSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_Desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcLossRepairSumInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcLossRepairSumInfoResult")
    Optional<MtcLossRepairSumInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcLossRepairSumInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcLossRepairSumInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default int insert(MtcLossRepairSumInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossRepairSumInfo, c ->
            c.map(taskNo).toProperty("taskNo")
            .map(workTypeCode).toProperty("workTypeCode")
            .map(itemCount).toProperty("itemCount")
            .map(referencePrice).toProperty("referencePrice")
            .map(hourDiscount).toProperty("hourDiscount")
            .map(discountRefPrice).toProperty("discountRefPrice")
            .map(evalRepairSum).toProperty("evalRepairSum")
            .map(auditItemCount).toProperty("auditItemCount")
            .map(apprRepairSum).toProperty("apprRepairSum")
            .map(status).toProperty("status")
            .map(miscDesc).toProperty("miscDesc")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default int insertSelective(MtcLossRepairSumInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossRepairSumInfo, c ->
            c.map(taskNo).toPropertyWhenPresent("taskNo", row::getTaskNo)
            .map(workTypeCode).toPropertyWhenPresent("workTypeCode", row::getWorkTypeCode)
            .map(itemCount).toPropertyWhenPresent("itemCount", row::getItemCount)
            .map(referencePrice).toPropertyWhenPresent("referencePrice", row::getReferencePrice)
            .map(hourDiscount).toPropertyWhenPresent("hourDiscount", row::getHourDiscount)
            .map(discountRefPrice).toPropertyWhenPresent("discountRefPrice", row::getDiscountRefPrice)
            .map(evalRepairSum).toPropertyWhenPresent("evalRepairSum", row::getEvalRepairSum)
            .map(auditItemCount).toPropertyWhenPresent("auditItemCount", row::getAuditItemCount)
            .map(apprRepairSum).toPropertyWhenPresent("apprRepairSum", row::getApprRepairSum)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default Optional<MtcLossRepairSumInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcLossRepairSumInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default List<MtcLossRepairSumInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcLossRepairSumInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default List<MtcLossRepairSumInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcLossRepairSumInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default Optional<MtcLossRepairSumInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcLossRepairSumInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcLossRepairSumInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalTo(row::getTaskNo)
                .set(workTypeCode).equalTo(row::getWorkTypeCode)
                .set(itemCount).equalTo(row::getItemCount)
                .set(referencePrice).equalTo(row::getReferencePrice)
                .set(hourDiscount).equalTo(row::getHourDiscount)
                .set(discountRefPrice).equalTo(row::getDiscountRefPrice)
                .set(evalRepairSum).equalTo(row::getEvalRepairSum)
                .set(auditItemCount).equalTo(row::getAuditItemCount)
                .set(apprRepairSum).equalTo(row::getApprRepairSum)
                .set(status).equalTo(row::getStatus)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcLossRepairSumInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(workTypeCode).equalToWhenPresent(row::getWorkTypeCode)
                .set(itemCount).equalToWhenPresent(row::getItemCount)
                .set(referencePrice).equalToWhenPresent(row::getReferencePrice)
                .set(hourDiscount).equalToWhenPresent(row::getHourDiscount)
                .set(discountRefPrice).equalToWhenPresent(row::getDiscountRefPrice)
                .set(evalRepairSum).equalToWhenPresent(row::getEvalRepairSum)
                .set(auditItemCount).equalToWhenPresent(row::getAuditItemCount)
                .set(apprRepairSum).equalToWhenPresent(row::getApprRepairSum)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default int updateByPrimaryKey(MtcLossRepairSumInfo row) {
        return update(c ->
            c.set(taskNo).equalTo(row::getTaskNo)
            .set(workTypeCode).equalTo(row::getWorkTypeCode)
            .set(itemCount).equalTo(row::getItemCount)
            .set(referencePrice).equalTo(row::getReferencePrice)
            .set(hourDiscount).equalTo(row::getHourDiscount)
            .set(discountRefPrice).equalTo(row::getDiscountRefPrice)
            .set(evalRepairSum).equalTo(row::getEvalRepairSum)
            .set(auditItemCount).equalTo(row::getAuditItemCount)
            .set(apprRepairSum).equalTo(row::getApprRepairSum)
            .set(status).equalTo(row::getStatus)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    default int updateByPrimaryKeySelective(MtcLossRepairSumInfo row) {
        return update(c ->
            c.set(taskNo).equalToWhenPresent(row::getTaskNo)
            .set(workTypeCode).equalToWhenPresent(row::getWorkTypeCode)
            .set(itemCount).equalToWhenPresent(row::getItemCount)
            .set(referencePrice).equalToWhenPresent(row::getReferencePrice)
            .set(hourDiscount).equalToWhenPresent(row::getHourDiscount)
            .set(discountRefPrice).equalToWhenPresent(row::getDiscountRefPrice)
            .set(evalRepairSum).equalToWhenPresent(row::getEvalRepairSum)
            .set(auditItemCount).equalToWhenPresent(row::getAuditItemCount)
            .set(apprRepairSum).equalToWhenPresent(row::getApprRepairSum)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}