package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ActivityStatusTransitionDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    public static final ActivityStatusTransition activityStatusTransition = new ActivityStatusTransition();

    /**
     * Database Column Remarks:
     *   转换规则编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.id")
    public static final SqlColumn<Long> id = activityStatusTransition.id;

    /**
     * Database Column Remarks:
     *   关联的活动节点转换规则编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.activity_transition_id")
    public static final SqlColumn<Long> activityTransitionId = activityStatusTransition.activityTransitionId;

    /**
     * Database Column Remarks:
     *   活动编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.activity_code")
    public static final SqlColumn<String> activityCode = activityStatusTransition.activityCode;

    /**
     * Database Column Remarks:
     *   起始状态编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.from_status_code")
    public static final SqlColumn<String> fromStatusCode = activityStatusTransition.fromStatusCode;

    /**
     * Database Column Remarks:
     *   目标状态编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.to_status_code")
    public static final SqlColumn<String> toStatusCode = activityStatusTransition.toStatusCode;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.create_time")
    public static final SqlColumn<Date> createTime = activityStatusTransition.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.create_by")
    public static final SqlColumn<String> createBy = activityStatusTransition.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.update_time")
    public static final SqlColumn<Date> updateTime = activityStatusTransition.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.update_by")
    public static final SqlColumn<String> updateBy = activityStatusTransition.updateBy;

    /**
     * Database Column Remarks:
     *   规则说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: activity_status_transition.description")
    public static final SqlColumn<String> description = activityStatusTransition.description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    public static final class ActivityStatusTransition extends AliasableSqlTable<ActivityStatusTransition> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> activityTransitionId = column("activity_transition_id", JDBCType.BIGINT);

        public final SqlColumn<String> activityCode = column("activity_code", JDBCType.VARCHAR);

        public final SqlColumn<String> fromStatusCode = column("from_status_code", JDBCType.VARCHAR);

        public final SqlColumn<String> toStatusCode = column("to_status_code", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<String> description = column("description", JDBCType.LONGVARCHAR);

        public ActivityStatusTransition() {
            super("activity_status_transition", ActivityStatusTransition::new);
        }
    }
}