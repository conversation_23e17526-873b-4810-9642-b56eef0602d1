package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.AttachmentInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.AttachmentInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface AttachmentInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, foreignKey, fileType, fileName, fileUrl, fileDesc, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<AttachmentInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="AttachmentInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="foreign_key", property="foreignKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_type", property="fileType", jdbcType=JdbcType.INTEGER),
        @Result(column="file_name", property="fileName", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_url", property="fileUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_desc", property="fileDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<AttachmentInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("AttachmentInfoResult")
    Optional<AttachmentInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, attachmentInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, attachmentInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default int insert(AttachmentInfo row) {
        return MyBatis3Utils.insert(this::insert, row, attachmentInfo, c ->
            c.map(foreignKey).toProperty("foreignKey")
            .map(fileType).toProperty("fileType")
            .map(fileName).toProperty("fileName")
            .map(fileUrl).toProperty("fileUrl")
            .map(fileDesc).toProperty("fileDesc")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default int insertSelective(AttachmentInfo row) {
        return MyBatis3Utils.insert(this::insert, row, attachmentInfo, c ->
            c.map(foreignKey).toPropertyWhenPresent("foreignKey", row::getForeignKey)
            .map(fileType).toPropertyWhenPresent("fileType", row::getFileType)
            .map(fileName).toPropertyWhenPresent("fileName", row::getFileName)
            .map(fileUrl).toPropertyWhenPresent("fileUrl", row::getFileUrl)
            .map(fileDesc).toPropertyWhenPresent("fileDesc", row::getFileDesc)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default Optional<AttachmentInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, attachmentInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default List<AttachmentInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, attachmentInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default List<AttachmentInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, attachmentInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default Optional<AttachmentInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, attachmentInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    static UpdateDSL<UpdateModel> updateAllColumns(AttachmentInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(foreignKey).equalTo(row::getForeignKey)
                .set(fileType).equalTo(row::getFileType)
                .set(fileName).equalTo(row::getFileName)
                .set(fileUrl).equalTo(row::getFileUrl)
                .set(fileDesc).equalTo(row::getFileDesc)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(AttachmentInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(foreignKey).equalToWhenPresent(row::getForeignKey)
                .set(fileType).equalToWhenPresent(row::getFileType)
                .set(fileName).equalToWhenPresent(row::getFileName)
                .set(fileUrl).equalToWhenPresent(row::getFileUrl)
                .set(fileDesc).equalToWhenPresent(row::getFileDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default int updateByPrimaryKey(AttachmentInfo row) {
        return update(c ->
            c.set(foreignKey).equalTo(row::getForeignKey)
            .set(fileType).equalTo(row::getFileType)
            .set(fileName).equalTo(row::getFileName)
            .set(fileUrl).equalTo(row::getFileUrl)
            .set(fileDesc).equalTo(row::getFileDesc)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    default int updateByPrimaryKeySelective(AttachmentInfo row) {
        return update(c ->
            c.set(foreignKey).equalToWhenPresent(row::getForeignKey)
            .set(fileType).equalToWhenPresent(row::getFileType)
            .set(fileName).equalToWhenPresent(row::getFileName)
            .set(fileUrl).equalToWhenPresent(row::getFileUrl)
            .set(fileDesc).equalToWhenPresent(row::getFileDesc)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}