package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcRepairItemDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item")
    public static final MtcRepairItem mtcRepairItem = new MtcRepairItem();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.id")
    public static final SqlColumn<Long> id = mtcRepairItem.id;

    /**
     * Database Column Remarks:
     *   组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.org_id")
    public static final SqlColumn<String> orgId = mtcRepairItem.orgId;

    /**
     * Database Column Remarks:
     *   修理名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.repair_name")
    public static final SqlColumn<String> repairName = mtcRepairItem.repairName;

    /**
     * Database Column Remarks:
     *   修理类型分组ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.grouping_id")
    public static final SqlColumn<Long> groupingId = mtcRepairItem.groupingId;

    /**
     * Database Column Remarks:
     *   系统市场价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.sys_market_price")
    public static final SqlColumn<BigDecimal> sysMarketPrice = mtcRepairItem.sysMarketPrice;

    /**
     * Database Column Remarks:
     *   系统专修价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.sys_major_in_price")
    public static final SqlColumn<BigDecimal> sysMajorInPrice = mtcRepairItem.sysMajorInPrice;

    /**
     * Database Column Remarks:
     *   本地市场价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.local_market_price")
    public static final SqlColumn<BigDecimal> localMarketPrice = mtcRepairItem.localMarketPrice;

    /**
     * Database Column Remarks:
     *   本地专修价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.local_major_in_price")
    public static final SqlColumn<BigDecimal> localMajorInPrice = mtcRepairItem.localMajorInPrice;

    /**
     * Database Column Remarks:
     *   是否支持另行报价 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.separate_quotation")
    public static final SqlColumn<Integer> separateQuotation = mtcRepairItem.separateQuotation;

    /**
     * Database Column Remarks:
     *   状态（1：有效 0：无效）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.status")
    public static final SqlColumn<Integer> status = mtcRepairItem.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.remark")
    public static final SqlColumn<String> remark = mtcRepairItem.remark;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.create_by")
    public static final SqlColumn<String> createBy = mtcRepairItem.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.created_time")
    public static final SqlColumn<Date> createdTime = mtcRepairItem.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.update_by")
    public static final SqlColumn<String> updateBy = mtcRepairItem.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcRepairItem.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item")
    public static final class MtcRepairItem extends AliasableSqlTable<MtcRepairItem> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> orgId = column("org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> repairName = column("repair_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> groupingId = column("grouping_id", JDBCType.BIGINT);

        public final SqlColumn<BigDecimal> sysMarketPrice = column("sys_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> sysMajorInPrice = column("sys_major_in_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> localMarketPrice = column("local_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> localMajorInPrice = column("local_major_in_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> separateQuotation = column("separate_quotation", JDBCType.INTEGER);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcRepairItem() {
            super("mtc_repair_item", MtcRepairItem::new);
        }
    }
}