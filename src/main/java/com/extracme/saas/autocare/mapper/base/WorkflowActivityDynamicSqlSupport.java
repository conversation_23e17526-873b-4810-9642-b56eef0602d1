package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class WorkflowActivityDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_activity")
    public static final WorkflowActivity workflowActivity = new WorkflowActivity();

    /**
     * Database Column Remarks:
     *   关联记录编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_activity.id")
    public static final SqlColumn<Long> id = workflowActivity.id;

    /**
     * Database Column Remarks:
     *   流程模板编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_activity.workflow_id")
    public static final SqlColumn<Long> workflowId = workflowActivity.workflowId;

    /**
     * Database Column Remarks:
     *   活动节点编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_activity.activity_code")
    public static final SqlColumn<String> activityCode = workflowActivity.activityCode;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_activity.create_time")
    public static final SqlColumn<Date> createTime = workflowActivity.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_activity.create_by")
    public static final SqlColumn<String> createBy = workflowActivity.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_activity.update_time")
    public static final SqlColumn<Date> updateTime = workflowActivity.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_activity.update_by")
    public static final SqlColumn<String> updateBy = workflowActivity.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_activity")
    public static final class WorkflowActivity extends AliasableSqlTable<WorkflowActivity> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> workflowId = column("workflow_id", JDBCType.BIGINT);

        public final SqlColumn<String> activityCode = column("activity_code", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public WorkflowActivity() {
            super("workflow_activity", WorkflowActivity::new);
        }
    }
}