package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DataCityInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    public static final DataCityInfo dataCityInfo = new DataCityInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.id")
    public static final SqlColumn<Long> id = dataCityInfo.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.cityid")
    public static final SqlColumn<Long> cityid = dataCityInfo.cityid;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.city")
    public static final SqlColumn<String> city = dataCityInfo.city;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.fatherid")
    public static final SqlColumn<Long> fatherid = dataCityInfo.fatherid;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.lon")
    public static final SqlColumn<BigDecimal> lon = dataCityInfo.lon;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.lat")
    public static final SqlColumn<BigDecimal> lat = dataCityInfo.lat;

    /**
     * Database Column Remarks:
     *   是否投入运营 0：不投入  1：投入运营
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.status")
    public static final SqlColumn<Integer> status = dataCityInfo.status;

    /**
     * Database Column Remarks:
     *   是否常用(0否，1是)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_city_info.in_common_use")
    public static final SqlColumn<Integer> inCommonUse = dataCityInfo.inCommonUse;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    public static final class DataCityInfo extends AliasableSqlTable<DataCityInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> cityid = column("cityid", JDBCType.BIGINT);

        public final SqlColumn<String> city = column("city", JDBCType.VARCHAR);

        public final SqlColumn<Long> fatherid = column("fatherid", JDBCType.BIGINT);

        public final SqlColumn<BigDecimal> lon = column("lon", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> lat = column("lat", JDBCType.DECIMAL);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<Integer> inCommonUse = column("in_common_use", JDBCType.INTEGER);

        public DataCityInfo() {
            super("data_city_info", DataCityInfo::new);
        }
    }
}