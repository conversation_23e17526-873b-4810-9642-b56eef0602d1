package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcCollisionPartsDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_collision_parts")
    public static final MtcCollisionParts mtcCollisionParts = new MtcCollisionParts();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_collision_parts.id")
    public static final SqlColumn<Long> id = mtcCollisionParts.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_collision_parts.task_no")
    public static final SqlColumn<String> taskNo = mtcCollisionParts.taskNo;

    /**
     * Database Column Remarks:
     *   损失部位
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_collision_parts.collision_way")
    public static final SqlColumn<String> collisionWay = mtcCollisionParts.collisionWay;

    /**
     * Database Column Remarks:
     *   损失程度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_collision_parts.collision_degree")
    public static final SqlColumn<String> collisionDegree = mtcCollisionParts.collisionDegree;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_collision_parts.status")
    public static final SqlColumn<Integer> status = mtcCollisionParts.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_collision_parts.misc_Desc")
    public static final SqlColumn<String> miscDesc = mtcCollisionParts.miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_collision_parts.create_by")
    public static final SqlColumn<String> createBy = mtcCollisionParts.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_collision_parts.created_time")
    public static final SqlColumn<Date> createdTime = mtcCollisionParts.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_collision_parts.update_by")
    public static final SqlColumn<String> updateBy = mtcCollisionParts.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_collision_parts.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcCollisionParts.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_collision_parts")
    public static final class MtcCollisionParts extends AliasableSqlTable<MtcCollisionParts> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<String> collisionWay = column("collision_way", JDBCType.VARCHAR);

        public final SqlColumn<String> collisionDegree = column("collision_degree", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_Desc", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcCollisionParts() {
            super("mtc_collision_parts", MtcCollisionParts::new);
        }
    }
}