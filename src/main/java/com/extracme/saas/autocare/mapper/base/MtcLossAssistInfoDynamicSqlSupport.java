package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcLossAssistInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    public static final MtcLossAssistInfo mtcLossAssistInfo = new MtcLossAssistInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.id")
    public static final SqlColumn<Long> id = mtcLossAssistInfo.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.task_no")
    public static final SqlColumn<String> taskNo = mtcLossAssistInfo.taskNo;

    /**
     * Database Column Remarks:
     *   辅料明细主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.assist_id")
    public static final SqlColumn<String> assistId = mtcLossAssistInfo.assistId;

    /**
     * Database Column Remarks:
     *   辅料名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.item_name")
    public static final SqlColumn<String> itemName = mtcLossAssistInfo.itemName;

    /**
     * Database Column Remarks:
     *   数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.count")
    public static final SqlColumn<BigDecimal> count = mtcLossAssistInfo.count;

    /**
     * Database Column Remarks:
     *   定损辅料单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.material_fee")
    public static final SqlColumn<BigDecimal> materialFee = mtcLossAssistInfo.materialFee;

    /**
     * Database Column Remarks:
     *   定损辅料合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.eval_mate_sum")
    public static final SqlColumn<BigDecimal> evalMateSum = mtcLossAssistInfo.evalMateSum;

    /**
     * Database Column Remarks:
     *   自定义辅料标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.self_config_flag")
    public static final SqlColumn<Integer> selfConfigFlag = mtcLossAssistInfo.selfConfigFlag;

    /**
     * Database Column Remarks:
     *   险种代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.item_cover_code")
    public static final SqlColumn<Integer> itemCoverCode = mtcLossAssistInfo.itemCoverCode;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.remark")
    public static final SqlColumn<String> remark = mtcLossAssistInfo.remark;

    /**
     * Database Column Remarks:
     *   重开次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.clm_tms")
    public static final SqlColumn<String> clmTms = mtcLossAssistInfo.clmTms;

    /**
     * Database Column Remarks:
     *   辅料核损单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_price")
    public static final SqlColumn<BigDecimal> auditPrice = mtcLossAssistInfo.auditPrice;

    /**
     * Database Column Remarks:
     *   辅料核损数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_count")
    public static final SqlColumn<BigDecimal> auditCount = mtcLossAssistInfo.auditCount;

    /**
     * Database Column Remarks:
     *   辅料核损小计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.appr_mate_sum")
    public static final SqlColumn<BigDecimal> apprMateSum = mtcLossAssistInfo.apprMateSum;

    /**
     * Database Column Remarks:
     *   核损状态（00：待处理 01：通过 02：价格异议 03：建议剔除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.check_state")
    public static final SqlColumn<String> checkState = mtcLossAssistInfo.checkState;

    /**
     * Database Column Remarks:
     *   核损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.audit_remark")
    public static final SqlColumn<String> auditRemark = mtcLossAssistInfo.auditRemark;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.status")
    public static final SqlColumn<Integer> status = mtcLossAssistInfo.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.misc_Desc")
    public static final SqlColumn<String> miscDesc = mtcLossAssistInfo.miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.create_by")
    public static final SqlColumn<String> createBy = mtcLossAssistInfo.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.created_time")
    public static final SqlColumn<Date> createdTime = mtcLossAssistInfo.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.update_by")
    public static final SqlColumn<String> updateBy = mtcLossAssistInfo.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_assist_info.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcLossAssistInfo.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    public static final class MtcLossAssistInfo extends AliasableSqlTable<MtcLossAssistInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<String> assistId = column("assist_id", JDBCType.VARCHAR);

        public final SqlColumn<String> itemName = column("item_name", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> count = column("count", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> materialFee = column("material_fee", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> evalMateSum = column("eval_mate_sum", JDBCType.DECIMAL);

        public final SqlColumn<Integer> selfConfigFlag = column("self_config_flag", JDBCType.INTEGER);

        public final SqlColumn<Integer> itemCoverCode = column("item_cover_code", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> clmTms = column("clm_tms", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> auditPrice = column("audit_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auditCount = column("audit_count", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> apprMateSum = column("appr_mate_sum", JDBCType.DECIMAL);

        public final SqlColumn<String> checkState = column("check_state", JDBCType.VARCHAR);

        public final SqlColumn<String> auditRemark = column("audit_remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_Desc", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcLossAssistInfo() {
            super("mtc_loss_assist_info", MtcLossAssistInfo::new);
        }
    }
}