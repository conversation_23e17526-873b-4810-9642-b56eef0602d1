package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcDataSyncLogDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    public static final MtcDataSyncLog mtcDataSyncLog = new MtcDataSyncLog();

    /**
     * Database Column Remarks:
     *   主键ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.id")
    public static final SqlColumn<Long> id = mtcDataSyncLog.id;

    /**
     * Database Column Remarks:
     *   同步批次号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.batch_no")
    public static final SqlColumn<String> batchNo = mtcDataSyncLog.batchNo;

    /**
     * Database Column Remarks:
     *   目标表名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.target_table")
    public static final SqlColumn<String> targetTable = mtcDataSyncLog.targetTable;

    /**
     * Database Column Remarks:
     *   同步状态：SUCCESS、FAILED、PROCESSING
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_status")
    public static final SqlColumn<String> syncStatus = mtcDataSyncLog.syncStatus;

    /**
     * Database Column Remarks:
     *   失败原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.error_message")
    public static final SqlColumn<String> errorMessage = mtcDataSyncLog.errorMessage;

    /**
     * Database Column Remarks:
     *   请求来源IP
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.source_ip")
    public static final SqlColumn<String> sourceIp = mtcDataSyncLog.sourceIp;

    /**
     * Database Column Remarks:
     *   同步开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_start_time")
    public static final SqlColumn<Date> syncStartTime = mtcDataSyncLog.syncStartTime;

    /**
     * Database Column Remarks:
     *   同步结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_end_time")
    public static final SqlColumn<Date> syncEndTime = mtcDataSyncLog.syncEndTime;

    /**
     * Database Column Remarks:
     *   同步耗时（毫秒）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.sync_duration")
    public static final SqlColumn<Long> syncDuration = mtcDataSyncLog.syncDuration;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.create_time")
    public static final SqlColumn<Date> createTime = mtcDataSyncLog.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.create_by")
    public static final SqlColumn<String> createBy = mtcDataSyncLog.createBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.update_time")
    public static final SqlColumn<Date> updateTime = mtcDataSyncLog.updateTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_data_sync_log.update_by")
    public static final SqlColumn<String> updateBy = mtcDataSyncLog.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_data_sync_log")
    public static final class MtcDataSyncLog extends AliasableSqlTable<MtcDataSyncLog> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> batchNo = column("batch_no", JDBCType.VARCHAR);

        public final SqlColumn<String> targetTable = column("target_table", JDBCType.VARCHAR);

        public final SqlColumn<String> syncStatus = column("sync_status", JDBCType.VARCHAR);

        public final SqlColumn<String> errorMessage = column("error_message", JDBCType.VARCHAR);

        public final SqlColumn<String> sourceIp = column("source_ip", JDBCType.VARCHAR);

        public final SqlColumn<Date> syncStartTime = column("sync_start_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> syncEndTime = column("sync_end_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> syncDuration = column("sync_duration", JDBCType.BIGINT);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public MtcDataSyncLog() {
            super("sys_data_sync_log", MtcDataSyncLog::new);
        }
    }
}
