package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.DataAreaInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.DataAreaInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DataAreaInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, areaid, area, fatherid, lon, lat);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<DataAreaInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DataAreaInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="areaid", property="areaid", jdbcType=JdbcType.BIGINT),
        @Result(column="area", property="area", jdbcType=JdbcType.VARCHAR),
        @Result(column="fatherid", property="fatherid", jdbcType=JdbcType.BIGINT),
        @Result(column="lon", property="lon", jdbcType=JdbcType.DECIMAL),
        @Result(column="lat", property="lat", jdbcType=JdbcType.DECIMAL)
    })
    List<DataAreaInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DataAreaInfoResult")
    Optional<DataAreaInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, dataAreaInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, dataAreaInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default int insert(DataAreaInfo row) {
        return MyBatis3Utils.insert(this::insert, row, dataAreaInfo, c ->
            c.map(areaid).toProperty("areaid")
            .map(area).toProperty("area")
            .map(fatherid).toProperty("fatherid")
            .map(lon).toProperty("lon")
            .map(lat).toProperty("lat")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default int insertSelective(DataAreaInfo row) {
        return MyBatis3Utils.insert(this::insert, row, dataAreaInfo, c ->
            c.map(areaid).toPropertyWhenPresent("areaid", row::getAreaid)
            .map(area).toPropertyWhenPresent("area", row::getArea)
            .map(fatherid).toPropertyWhenPresent("fatherid", row::getFatherid)
            .map(lon).toPropertyWhenPresent("lon", row::getLon)
            .map(lat).toPropertyWhenPresent("lat", row::getLat)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default Optional<DataAreaInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, dataAreaInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default List<DataAreaInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, dataAreaInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default List<DataAreaInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, dataAreaInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default Optional<DataAreaInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, dataAreaInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    static UpdateDSL<UpdateModel> updateAllColumns(DataAreaInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(areaid).equalTo(row::getAreaid)
                .set(area).equalTo(row::getArea)
                .set(fatherid).equalTo(row::getFatherid)
                .set(lon).equalTo(row::getLon)
                .set(lat).equalTo(row::getLat);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DataAreaInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(areaid).equalToWhenPresent(row::getAreaid)
                .set(area).equalToWhenPresent(row::getArea)
                .set(fatherid).equalToWhenPresent(row::getFatherid)
                .set(lon).equalToWhenPresent(row::getLon)
                .set(lat).equalToWhenPresent(row::getLat);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default int updateByPrimaryKey(DataAreaInfo row) {
        return update(c ->
            c.set(areaid).equalTo(row::getAreaid)
            .set(area).equalTo(row::getArea)
            .set(fatherid).equalTo(row::getFatherid)
            .set(lon).equalTo(row::getLon)
            .set(lat).equalTo(row::getLat)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    default int updateByPrimaryKeySelective(DataAreaInfo row) {
        return update(c ->
            c.set(areaid).equalToWhenPresent(row::getAreaid)
            .set(area).equalToWhenPresent(row::getArea)
            .set(fatherid).equalToWhenPresent(row::getFatherid)
            .set(lon).equalToWhenPresent(row::getLon)
            .set(lat).equalToWhenPresent(row::getLat)
            .where(id, isEqualTo(row::getId))
        );
    }
}