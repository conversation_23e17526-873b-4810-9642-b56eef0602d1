package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.ActivityStatusTransitionDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.ActivityStatusTransition;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ActivityStatusTransitionMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    BasicColumn[] selectList = BasicColumn.columnList(id, activityTransitionId, activityCode, fromStatusCode, toStatusCode, createTime, createBy, updateTime, updateBy, description);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<ActivityStatusTransition> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ActivityStatusTransitionResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="activity_transition_id", property="activityTransitionId", jdbcType=JdbcType.BIGINT),
        @Result(column="activity_code", property="activityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="from_status_code", property="fromStatusCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="to_status_code", property="toStatusCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="description", property="description", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<ActivityStatusTransition> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ActivityStatusTransitionResult")
    Optional<ActivityStatusTransition> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, activityStatusTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, activityStatusTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default int insert(ActivityStatusTransition row) {
        return MyBatis3Utils.insert(this::insert, row, activityStatusTransition, c ->
            c.map(activityTransitionId).toProperty("activityTransitionId")
            .map(activityCode).toProperty("activityCode")
            .map(fromStatusCode).toProperty("fromStatusCode")
            .map(toStatusCode).toProperty("toStatusCode")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
            .map(description).toProperty("description")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default int insertSelective(ActivityStatusTransition row) {
        return MyBatis3Utils.insert(this::insert, row, activityStatusTransition, c ->
            c.map(activityTransitionId).toPropertyWhenPresent("activityTransitionId", row::getActivityTransitionId)
            .map(activityCode).toPropertyWhenPresent("activityCode", row::getActivityCode)
            .map(fromStatusCode).toPropertyWhenPresent("fromStatusCode", row::getFromStatusCode)
            .map(toStatusCode).toPropertyWhenPresent("toStatusCode", row::getToStatusCode)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(description).toPropertyWhenPresent("description", row::getDescription)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default Optional<ActivityStatusTransition> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, activityStatusTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default List<ActivityStatusTransition> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, activityStatusTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default List<ActivityStatusTransition> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, activityStatusTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default Optional<ActivityStatusTransition> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, activityStatusTransition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    static UpdateDSL<UpdateModel> updateAllColumns(ActivityStatusTransition row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(activityTransitionId).equalTo(row::getActivityTransitionId)
                .set(activityCode).equalTo(row::getActivityCode)
                .set(fromStatusCode).equalTo(row::getFromStatusCode)
                .set(toStatusCode).equalTo(row::getToStatusCode)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(description).equalTo(row::getDescription);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ActivityStatusTransition row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(activityTransitionId).equalToWhenPresent(row::getActivityTransitionId)
                .set(activityCode).equalToWhenPresent(row::getActivityCode)
                .set(fromStatusCode).equalToWhenPresent(row::getFromStatusCode)
                .set(toStatusCode).equalToWhenPresent(row::getToStatusCode)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(description).equalToWhenPresent(row::getDescription);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default int updateByPrimaryKey(ActivityStatusTransition row) {
        return update(c ->
            c.set(activityTransitionId).equalTo(row::getActivityTransitionId)
            .set(activityCode).equalTo(row::getActivityCode)
            .set(fromStatusCode).equalTo(row::getFromStatusCode)
            .set(toStatusCode).equalTo(row::getToStatusCode)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(description).equalTo(row::getDescription)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status_transition")
    default int updateByPrimaryKeySelective(ActivityStatusTransition row) {
        return update(c ->
            c.set(activityTransitionId).equalToWhenPresent(row::getActivityTransitionId)
            .set(activityCode).equalToWhenPresent(row::getActivityCode)
            .set(fromStatusCode).equalToWhenPresent(row::getFromStatusCode)
            .set(toStatusCode).equalToWhenPresent(row::getToStatusCode)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(description).equalToWhenPresent(row::getDescription)
            .where(id, isEqualTo(row::getId))
        );
    }
}