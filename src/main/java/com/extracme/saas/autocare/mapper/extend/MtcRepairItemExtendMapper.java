package com.extracme.saas.autocare.mapper.extend;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcRepairItemMapper;
import com.extracme.saas.autocare.model.vo.RepairProjectDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairProjectListVO;

/**
 * 修理项目信息扩展Mapper
 */
@Mapper
@TenantSchema // 可以指定某些方法不使用租户Schema
public interface MtcRepairItemExtendMapper extends MtcRepairItemMapper {

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="QueryRepairItemListResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.BIGINT, id=true),
            @Result(column="org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="repair_name", property="repairName", jdbcType=JdbcType.VARCHAR),
            @Result(column="grouping_id", property="groupingId", jdbcType=JdbcType.BIGINT),
            @Result(column="local_market_price", property="localMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="separate_quotation", property="separateQuotation", jdbcType=JdbcType.INTEGER),
            @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
            @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="grouping_name", property="groupingName", jdbcType=JdbcType.VARCHAR)
    })
    List<RepairProjectListVO> queryRepairItemList(SelectStatementProvider selectStatement);

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="QueryRepairItemDetailsResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.BIGINT, id=true),
            @Result(column="org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="repair_name", property="repairName", jdbcType=JdbcType.VARCHAR),
            @Result(column="grouping_id", property="groupingId", jdbcType=JdbcType.BIGINT),
            @Result(column="sys_market_price", property="sysMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="sys_major_in_price", property="sysMajorInPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="local_market_price", property="localMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="local_major_in_price", property="localMajorInPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="separate_quotation", property="separateQuotation", jdbcType=JdbcType.INTEGER),
            @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
            @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="grouping_name", property="groupingName", jdbcType=JdbcType.VARCHAR),
            @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR)
    })
    RepairProjectDetailsVO queryRepairItemDetails(SelectStatementProvider selectStatement);

} 