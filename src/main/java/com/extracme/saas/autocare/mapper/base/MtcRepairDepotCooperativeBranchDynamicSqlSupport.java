package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcRepairDepotCooperativeBranchDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_cooperative_branch")
    public static final MtcRepairDepotCooperativeBranch mtcRepairDepotCooperativeBranch = new MtcRepairDepotCooperativeBranch();

    /**
     * Database Column Remarks:
     *   ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_cooperative_branch.id")
    public static final SqlColumn<Long> id = mtcRepairDepotCooperativeBranch.id;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_cooperative_branch.repair_depot_id")
    public static final SqlColumn<String> repairDepotId = mtcRepairDepotCooperativeBranch.repairDepotId;

    /**
     * Database Column Remarks:
     *   组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_cooperative_branch.org_id")
    public static final SqlColumn<String> orgId = mtcRepairDepotCooperativeBranch.orgId;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_cooperative_branch.create_by")
    public static final SqlColumn<String> createBy = mtcRepairDepotCooperativeBranch.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_cooperative_branch.created_time")
    public static final SqlColumn<Date> createdTime = mtcRepairDepotCooperativeBranch.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_cooperative_branch.update_by")
    public static final SqlColumn<String> updateBy = mtcRepairDepotCooperativeBranch.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_cooperative_branch.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcRepairDepotCooperativeBranch.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_cooperative_branch")
    public static final class MtcRepairDepotCooperativeBranch extends AliasableSqlTable<MtcRepairDepotCooperativeBranch> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> repairDepotId = column("repair_depot_id", JDBCType.VARCHAR);

        public final SqlColumn<String> orgId = column("org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcRepairDepotCooperativeBranch() {
            super("mtc_repair_depot_cooperative_branch", MtcRepairDepotCooperativeBranch::new);
        }
    }
}