package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcLossFitInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcLossFitInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNo, partId, partCode, itemName, sysGuidePrice, sysMarketPrice, localGuidePrice, localMarketPrice, localBrandPrice, localApplicablePrice, localRemanufacturePrice, localPrice, count, materialFee, selfConfigFlag, itemCoverCode, remark, chgCompSetCode, fitBackFlag, remainsPrice, detectedFlag, directSupplyFlag, directSupplier, manageSingleRate, manageSingleFee, evalPartSum, selfPayRate, recyclePartFlag, evalPartSumFirst, cornerMark, clmTms, ifWading, recheckFlag, auditMaterialFee, auditCount, apprPartSum, selfPayPrice, apprRemainsPrice, manageRate, apprManageFee, checkState, auditRemark, apprBackFlag, auditRecheckFlag, status, miscDesc, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcLossFitInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcLossFitInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_no", property="taskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="part_id", property="partId", jdbcType=JdbcType.VARCHAR),
        @Result(column="part_code", property="partCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
        @Result(column="sys_guide_price", property="sysGuidePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="sys_market_price", property="sysMarketPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="local_guide_price", property="localGuidePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="local_market_price", property="localMarketPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="local_brand_price", property="localBrandPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="local_applicable_price", property="localApplicablePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="local_remanufacture_price", property="localRemanufacturePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="local_price", property="localPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="count", property="count", jdbcType=JdbcType.INTEGER),
        @Result(column="material_fee", property="materialFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="self_config_flag", property="selfConfigFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="item_cover_code", property="itemCoverCode", jdbcType=JdbcType.INTEGER),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="chg_comp_set_code", property="chgCompSetCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="fit_back_flag", property="fitBackFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="remains_price", property="remainsPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="detected_flag", property="detectedFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="direct_supply_flag", property="directSupplyFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="direct_supplier", property="directSupplier", jdbcType=JdbcType.VARCHAR),
        @Result(column="manage_single_rate", property="manageSingleRate", jdbcType=JdbcType.DECIMAL),
        @Result(column="manage_single_fee", property="manageSingleFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="eval_part_sum", property="evalPartSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="self_pay_rate", property="selfPayRate", jdbcType=JdbcType.DECIMAL),
        @Result(column="recycle_part_flag", property="recyclePartFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="eval_part_sum_first", property="evalPartSumFirst", jdbcType=JdbcType.DECIMAL),
        @Result(column="corner_mark", property="cornerMark", jdbcType=JdbcType.VARCHAR),
        @Result(column="clm_tms", property="clmTms", jdbcType=JdbcType.VARCHAR),
        @Result(column="if_wading", property="ifWading", jdbcType=JdbcType.VARCHAR),
        @Result(column="recheck_flag", property="recheckFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_material_fee", property="auditMaterialFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_count", property="auditCount", jdbcType=JdbcType.DECIMAL),
        @Result(column="appr_part_sum", property="apprPartSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="self_pay_price", property="selfPayPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="appr_remains_price", property="apprRemainsPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="manage_rate", property="manageRate", jdbcType=JdbcType.DECIMAL),
        @Result(column="appr_manage_fee", property="apprManageFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="check_state", property="checkState", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_remark", property="auditRemark", jdbcType=JdbcType.VARCHAR),
        @Result(column="appr_back_flag", property="apprBackFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_recheck_flag", property="auditRecheckFlag", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_Desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcLossFitInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcLossFitInfoResult")
    Optional<MtcLossFitInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcLossFitInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcLossFitInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default int insert(MtcLossFitInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossFitInfo, c ->
            c.map(taskNo).toProperty("taskNo")
            .map(partId).toProperty("partId")
            .map(partCode).toProperty("partCode")
            .map(itemName).toProperty("itemName")
            .map(sysGuidePrice).toProperty("sysGuidePrice")
            .map(sysMarketPrice).toProperty("sysMarketPrice")
            .map(localGuidePrice).toProperty("localGuidePrice")
            .map(localMarketPrice).toProperty("localMarketPrice")
            .map(localBrandPrice).toProperty("localBrandPrice")
            .map(localApplicablePrice).toProperty("localApplicablePrice")
            .map(localRemanufacturePrice).toProperty("localRemanufacturePrice")
            .map(localPrice).toProperty("localPrice")
            .map(count).toProperty("count")
            .map(materialFee).toProperty("materialFee")
            .map(selfConfigFlag).toProperty("selfConfigFlag")
            .map(itemCoverCode).toProperty("itemCoverCode")
            .map(remark).toProperty("remark")
            .map(chgCompSetCode).toProperty("chgCompSetCode")
            .map(fitBackFlag).toProperty("fitBackFlag")
            .map(remainsPrice).toProperty("remainsPrice")
            .map(detectedFlag).toProperty("detectedFlag")
            .map(directSupplyFlag).toProperty("directSupplyFlag")
            .map(directSupplier).toProperty("directSupplier")
            .map(manageSingleRate).toProperty("manageSingleRate")
            .map(manageSingleFee).toProperty("manageSingleFee")
            .map(evalPartSum).toProperty("evalPartSum")
            .map(selfPayRate).toProperty("selfPayRate")
            .map(recyclePartFlag).toProperty("recyclePartFlag")
            .map(evalPartSumFirst).toProperty("evalPartSumFirst")
            .map(cornerMark).toProperty("cornerMark")
            .map(clmTms).toProperty("clmTms")
            .map(ifWading).toProperty("ifWading")
            .map(recheckFlag).toProperty("recheckFlag")
            .map(auditMaterialFee).toProperty("auditMaterialFee")
            .map(auditCount).toProperty("auditCount")
            .map(apprPartSum).toProperty("apprPartSum")
            .map(selfPayPrice).toProperty("selfPayPrice")
            .map(apprRemainsPrice).toProperty("apprRemainsPrice")
            .map(manageRate).toProperty("manageRate")
            .map(apprManageFee).toProperty("apprManageFee")
            .map(checkState).toProperty("checkState")
            .map(auditRemark).toProperty("auditRemark")
            .map(apprBackFlag).toProperty("apprBackFlag")
            .map(auditRecheckFlag).toProperty("auditRecheckFlag")
            .map(status).toProperty("status")
            .map(miscDesc).toProperty("miscDesc")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default int insertSelective(MtcLossFitInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossFitInfo, c ->
            c.map(taskNo).toPropertyWhenPresent("taskNo", row::getTaskNo)
            .map(partId).toPropertyWhenPresent("partId", row::getPartId)
            .map(partCode).toPropertyWhenPresent("partCode", row::getPartCode)
            .map(itemName).toPropertyWhenPresent("itemName", row::getItemName)
            .map(sysGuidePrice).toPropertyWhenPresent("sysGuidePrice", row::getSysGuidePrice)
            .map(sysMarketPrice).toPropertyWhenPresent("sysMarketPrice", row::getSysMarketPrice)
            .map(localGuidePrice).toPropertyWhenPresent("localGuidePrice", row::getLocalGuidePrice)
            .map(localMarketPrice).toPropertyWhenPresent("localMarketPrice", row::getLocalMarketPrice)
            .map(localBrandPrice).toPropertyWhenPresent("localBrandPrice", row::getLocalBrandPrice)
            .map(localApplicablePrice).toPropertyWhenPresent("localApplicablePrice", row::getLocalApplicablePrice)
            .map(localRemanufacturePrice).toPropertyWhenPresent("localRemanufacturePrice", row::getLocalRemanufacturePrice)
            .map(localPrice).toPropertyWhenPresent("localPrice", row::getLocalPrice)
            .map(count).toPropertyWhenPresent("count", row::getCount)
            .map(materialFee).toPropertyWhenPresent("materialFee", row::getMaterialFee)
            .map(selfConfigFlag).toPropertyWhenPresent("selfConfigFlag", row::getSelfConfigFlag)
            .map(itemCoverCode).toPropertyWhenPresent("itemCoverCode", row::getItemCoverCode)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(chgCompSetCode).toPropertyWhenPresent("chgCompSetCode", row::getChgCompSetCode)
            .map(fitBackFlag).toPropertyWhenPresent("fitBackFlag", row::getFitBackFlag)
            .map(remainsPrice).toPropertyWhenPresent("remainsPrice", row::getRemainsPrice)
            .map(detectedFlag).toPropertyWhenPresent("detectedFlag", row::getDetectedFlag)
            .map(directSupplyFlag).toPropertyWhenPresent("directSupplyFlag", row::getDirectSupplyFlag)
            .map(directSupplier).toPropertyWhenPresent("directSupplier", row::getDirectSupplier)
            .map(manageSingleRate).toPropertyWhenPresent("manageSingleRate", row::getManageSingleRate)
            .map(manageSingleFee).toPropertyWhenPresent("manageSingleFee", row::getManageSingleFee)
            .map(evalPartSum).toPropertyWhenPresent("evalPartSum", row::getEvalPartSum)
            .map(selfPayRate).toPropertyWhenPresent("selfPayRate", row::getSelfPayRate)
            .map(recyclePartFlag).toPropertyWhenPresent("recyclePartFlag", row::getRecyclePartFlag)
            .map(evalPartSumFirst).toPropertyWhenPresent("evalPartSumFirst", row::getEvalPartSumFirst)
            .map(cornerMark).toPropertyWhenPresent("cornerMark", row::getCornerMark)
            .map(clmTms).toPropertyWhenPresent("clmTms", row::getClmTms)
            .map(ifWading).toPropertyWhenPresent("ifWading", row::getIfWading)
            .map(recheckFlag).toPropertyWhenPresent("recheckFlag", row::getRecheckFlag)
            .map(auditMaterialFee).toPropertyWhenPresent("auditMaterialFee", row::getAuditMaterialFee)
            .map(auditCount).toPropertyWhenPresent("auditCount", row::getAuditCount)
            .map(apprPartSum).toPropertyWhenPresent("apprPartSum", row::getApprPartSum)
            .map(selfPayPrice).toPropertyWhenPresent("selfPayPrice", row::getSelfPayPrice)
            .map(apprRemainsPrice).toPropertyWhenPresent("apprRemainsPrice", row::getApprRemainsPrice)
            .map(manageRate).toPropertyWhenPresent("manageRate", row::getManageRate)
            .map(apprManageFee).toPropertyWhenPresent("apprManageFee", row::getApprManageFee)
            .map(checkState).toPropertyWhenPresent("checkState", row::getCheckState)
            .map(auditRemark).toPropertyWhenPresent("auditRemark", row::getAuditRemark)
            .map(apprBackFlag).toPropertyWhenPresent("apprBackFlag", row::getApprBackFlag)
            .map(auditRecheckFlag).toPropertyWhenPresent("auditRecheckFlag", row::getAuditRecheckFlag)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default Optional<MtcLossFitInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcLossFitInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default List<MtcLossFitInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcLossFitInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default List<MtcLossFitInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcLossFitInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default Optional<MtcLossFitInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcLossFitInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcLossFitInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalTo(row::getTaskNo)
                .set(partId).equalTo(row::getPartId)
                .set(partCode).equalTo(row::getPartCode)
                .set(itemName).equalTo(row::getItemName)
                .set(sysGuidePrice).equalTo(row::getSysGuidePrice)
                .set(sysMarketPrice).equalTo(row::getSysMarketPrice)
                .set(localGuidePrice).equalTo(row::getLocalGuidePrice)
                .set(localMarketPrice).equalTo(row::getLocalMarketPrice)
                .set(localBrandPrice).equalTo(row::getLocalBrandPrice)
                .set(localApplicablePrice).equalTo(row::getLocalApplicablePrice)
                .set(localRemanufacturePrice).equalTo(row::getLocalRemanufacturePrice)
                .set(localPrice).equalTo(row::getLocalPrice)
                .set(count).equalTo(row::getCount)
                .set(materialFee).equalTo(row::getMaterialFee)
                .set(selfConfigFlag).equalTo(row::getSelfConfigFlag)
                .set(itemCoverCode).equalTo(row::getItemCoverCode)
                .set(remark).equalTo(row::getRemark)
                .set(chgCompSetCode).equalTo(row::getChgCompSetCode)
                .set(fitBackFlag).equalTo(row::getFitBackFlag)
                .set(remainsPrice).equalTo(row::getRemainsPrice)
                .set(detectedFlag).equalTo(row::getDetectedFlag)
                .set(directSupplyFlag).equalTo(row::getDirectSupplyFlag)
                .set(directSupplier).equalTo(row::getDirectSupplier)
                .set(manageSingleRate).equalTo(row::getManageSingleRate)
                .set(manageSingleFee).equalTo(row::getManageSingleFee)
                .set(evalPartSum).equalTo(row::getEvalPartSum)
                .set(selfPayRate).equalTo(row::getSelfPayRate)
                .set(recyclePartFlag).equalTo(row::getRecyclePartFlag)
                .set(evalPartSumFirst).equalTo(row::getEvalPartSumFirst)
                .set(cornerMark).equalTo(row::getCornerMark)
                .set(clmTms).equalTo(row::getClmTms)
                .set(ifWading).equalTo(row::getIfWading)
                .set(recheckFlag).equalTo(row::getRecheckFlag)
                .set(auditMaterialFee).equalTo(row::getAuditMaterialFee)
                .set(auditCount).equalTo(row::getAuditCount)
                .set(apprPartSum).equalTo(row::getApprPartSum)
                .set(selfPayPrice).equalTo(row::getSelfPayPrice)
                .set(apprRemainsPrice).equalTo(row::getApprRemainsPrice)
                .set(manageRate).equalTo(row::getManageRate)
                .set(apprManageFee).equalTo(row::getApprManageFee)
                .set(checkState).equalTo(row::getCheckState)
                .set(auditRemark).equalTo(row::getAuditRemark)
                .set(apprBackFlag).equalTo(row::getApprBackFlag)
                .set(auditRecheckFlag).equalTo(row::getAuditRecheckFlag)
                .set(status).equalTo(row::getStatus)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcLossFitInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(partId).equalToWhenPresent(row::getPartId)
                .set(partCode).equalToWhenPresent(row::getPartCode)
                .set(itemName).equalToWhenPresent(row::getItemName)
                .set(sysGuidePrice).equalToWhenPresent(row::getSysGuidePrice)
                .set(sysMarketPrice).equalToWhenPresent(row::getSysMarketPrice)
                .set(localGuidePrice).equalToWhenPresent(row::getLocalGuidePrice)
                .set(localMarketPrice).equalToWhenPresent(row::getLocalMarketPrice)
                .set(localBrandPrice).equalToWhenPresent(row::getLocalBrandPrice)
                .set(localApplicablePrice).equalToWhenPresent(row::getLocalApplicablePrice)
                .set(localRemanufacturePrice).equalToWhenPresent(row::getLocalRemanufacturePrice)
                .set(localPrice).equalToWhenPresent(row::getLocalPrice)
                .set(count).equalToWhenPresent(row::getCount)
                .set(materialFee).equalToWhenPresent(row::getMaterialFee)
                .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
                .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(chgCompSetCode).equalToWhenPresent(row::getChgCompSetCode)
                .set(fitBackFlag).equalToWhenPresent(row::getFitBackFlag)
                .set(remainsPrice).equalToWhenPresent(row::getRemainsPrice)
                .set(detectedFlag).equalToWhenPresent(row::getDetectedFlag)
                .set(directSupplyFlag).equalToWhenPresent(row::getDirectSupplyFlag)
                .set(directSupplier).equalToWhenPresent(row::getDirectSupplier)
                .set(manageSingleRate).equalToWhenPresent(row::getManageSingleRate)
                .set(manageSingleFee).equalToWhenPresent(row::getManageSingleFee)
                .set(evalPartSum).equalToWhenPresent(row::getEvalPartSum)
                .set(selfPayRate).equalToWhenPresent(row::getSelfPayRate)
                .set(recyclePartFlag).equalToWhenPresent(row::getRecyclePartFlag)
                .set(evalPartSumFirst).equalToWhenPresent(row::getEvalPartSumFirst)
                .set(cornerMark).equalToWhenPresent(row::getCornerMark)
                .set(clmTms).equalToWhenPresent(row::getClmTms)
                .set(ifWading).equalToWhenPresent(row::getIfWading)
                .set(recheckFlag).equalToWhenPresent(row::getRecheckFlag)
                .set(auditMaterialFee).equalToWhenPresent(row::getAuditMaterialFee)
                .set(auditCount).equalToWhenPresent(row::getAuditCount)
                .set(apprPartSum).equalToWhenPresent(row::getApprPartSum)
                .set(selfPayPrice).equalToWhenPresent(row::getSelfPayPrice)
                .set(apprRemainsPrice).equalToWhenPresent(row::getApprRemainsPrice)
                .set(manageRate).equalToWhenPresent(row::getManageRate)
                .set(apprManageFee).equalToWhenPresent(row::getApprManageFee)
                .set(checkState).equalToWhenPresent(row::getCheckState)
                .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
                .set(apprBackFlag).equalToWhenPresent(row::getApprBackFlag)
                .set(auditRecheckFlag).equalToWhenPresent(row::getAuditRecheckFlag)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default int updateByPrimaryKey(MtcLossFitInfo row) {
        return update(c ->
            c.set(taskNo).equalTo(row::getTaskNo)
            .set(partId).equalTo(row::getPartId)
            .set(partCode).equalTo(row::getPartCode)
            .set(itemName).equalTo(row::getItemName)
            .set(sysGuidePrice).equalTo(row::getSysGuidePrice)
            .set(sysMarketPrice).equalTo(row::getSysMarketPrice)
            .set(localGuidePrice).equalTo(row::getLocalGuidePrice)
            .set(localMarketPrice).equalTo(row::getLocalMarketPrice)
            .set(localBrandPrice).equalTo(row::getLocalBrandPrice)
            .set(localApplicablePrice).equalTo(row::getLocalApplicablePrice)
            .set(localRemanufacturePrice).equalTo(row::getLocalRemanufacturePrice)
            .set(localPrice).equalTo(row::getLocalPrice)
            .set(count).equalTo(row::getCount)
            .set(materialFee).equalTo(row::getMaterialFee)
            .set(selfConfigFlag).equalTo(row::getSelfConfigFlag)
            .set(itemCoverCode).equalTo(row::getItemCoverCode)
            .set(remark).equalTo(row::getRemark)
            .set(chgCompSetCode).equalTo(row::getChgCompSetCode)
            .set(fitBackFlag).equalTo(row::getFitBackFlag)
            .set(remainsPrice).equalTo(row::getRemainsPrice)
            .set(detectedFlag).equalTo(row::getDetectedFlag)
            .set(directSupplyFlag).equalTo(row::getDirectSupplyFlag)
            .set(directSupplier).equalTo(row::getDirectSupplier)
            .set(manageSingleRate).equalTo(row::getManageSingleRate)
            .set(manageSingleFee).equalTo(row::getManageSingleFee)
            .set(evalPartSum).equalTo(row::getEvalPartSum)
            .set(selfPayRate).equalTo(row::getSelfPayRate)
            .set(recyclePartFlag).equalTo(row::getRecyclePartFlag)
            .set(evalPartSumFirst).equalTo(row::getEvalPartSumFirst)
            .set(cornerMark).equalTo(row::getCornerMark)
            .set(clmTms).equalTo(row::getClmTms)
            .set(ifWading).equalTo(row::getIfWading)
            .set(recheckFlag).equalTo(row::getRecheckFlag)
            .set(auditMaterialFee).equalTo(row::getAuditMaterialFee)
            .set(auditCount).equalTo(row::getAuditCount)
            .set(apprPartSum).equalTo(row::getApprPartSum)
            .set(selfPayPrice).equalTo(row::getSelfPayPrice)
            .set(apprRemainsPrice).equalTo(row::getApprRemainsPrice)
            .set(manageRate).equalTo(row::getManageRate)
            .set(apprManageFee).equalTo(row::getApprManageFee)
            .set(checkState).equalTo(row::getCheckState)
            .set(auditRemark).equalTo(row::getAuditRemark)
            .set(apprBackFlag).equalTo(row::getApprBackFlag)
            .set(auditRecheckFlag).equalTo(row::getAuditRecheckFlag)
            .set(status).equalTo(row::getStatus)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_fit_info")
    default int updateByPrimaryKeySelective(MtcLossFitInfo row) {
        return update(c ->
            c.set(taskNo).equalToWhenPresent(row::getTaskNo)
            .set(partId).equalToWhenPresent(row::getPartId)
            .set(partCode).equalToWhenPresent(row::getPartCode)
            .set(itemName).equalToWhenPresent(row::getItemName)
            .set(sysGuidePrice).equalToWhenPresent(row::getSysGuidePrice)
            .set(sysMarketPrice).equalToWhenPresent(row::getSysMarketPrice)
            .set(localGuidePrice).equalToWhenPresent(row::getLocalGuidePrice)
            .set(localMarketPrice).equalToWhenPresent(row::getLocalMarketPrice)
            .set(localBrandPrice).equalToWhenPresent(row::getLocalBrandPrice)
            .set(localApplicablePrice).equalToWhenPresent(row::getLocalApplicablePrice)
            .set(localRemanufacturePrice).equalToWhenPresent(row::getLocalRemanufacturePrice)
            .set(localPrice).equalToWhenPresent(row::getLocalPrice)
            .set(count).equalToWhenPresent(row::getCount)
            .set(materialFee).equalToWhenPresent(row::getMaterialFee)
            .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
            .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(chgCompSetCode).equalToWhenPresent(row::getChgCompSetCode)
            .set(fitBackFlag).equalToWhenPresent(row::getFitBackFlag)
            .set(remainsPrice).equalToWhenPresent(row::getRemainsPrice)
            .set(detectedFlag).equalToWhenPresent(row::getDetectedFlag)
            .set(directSupplyFlag).equalToWhenPresent(row::getDirectSupplyFlag)
            .set(directSupplier).equalToWhenPresent(row::getDirectSupplier)
            .set(manageSingleRate).equalToWhenPresent(row::getManageSingleRate)
            .set(manageSingleFee).equalToWhenPresent(row::getManageSingleFee)
            .set(evalPartSum).equalToWhenPresent(row::getEvalPartSum)
            .set(selfPayRate).equalToWhenPresent(row::getSelfPayRate)
            .set(recyclePartFlag).equalToWhenPresent(row::getRecyclePartFlag)
            .set(evalPartSumFirst).equalToWhenPresent(row::getEvalPartSumFirst)
            .set(cornerMark).equalToWhenPresent(row::getCornerMark)
            .set(clmTms).equalToWhenPresent(row::getClmTms)
            .set(ifWading).equalToWhenPresent(row::getIfWading)
            .set(recheckFlag).equalToWhenPresent(row::getRecheckFlag)
            .set(auditMaterialFee).equalToWhenPresent(row::getAuditMaterialFee)
            .set(auditCount).equalToWhenPresent(row::getAuditCount)
            .set(apprPartSum).equalToWhenPresent(row::getApprPartSum)
            .set(selfPayPrice).equalToWhenPresent(row::getSelfPayPrice)
            .set(apprRemainsPrice).equalToWhenPresent(row::getApprRemainsPrice)
            .set(manageRate).equalToWhenPresent(row::getManageRate)
            .set(apprManageFee).equalToWhenPresent(row::getApprManageFee)
            .set(checkState).equalToWhenPresent(row::getCheckState)
            .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
            .set(apprBackFlag).equalToWhenPresent(row::getApprBackFlag)
            .set(auditRecheckFlag).equalToWhenPresent(row::getAuditRecheckFlag)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}