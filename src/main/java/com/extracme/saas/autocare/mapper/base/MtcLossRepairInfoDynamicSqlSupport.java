package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcLossRepairInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    public static final MtcLossRepairInfo mtcLossRepairInfo = new MtcLossRepairInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.id")
    public static final SqlColumn<Long> id = mtcLossRepairInfo.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.task_no")
    public static final SqlColumn<String> taskNo = mtcLossRepairInfo.taskNo;

    /**
     * Database Column Remarks:
     *   定损明细主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_id")
    public static final SqlColumn<String> repairId = mtcLossRepairInfo.repairId;

    /**
     * Database Column Remarks:
     *   修理方式代码（1：喷漆项目 2：钣金项目 3：电工项目 4：机修项目 5：拆装项目）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_mode_code")
    public static final SqlColumn<String> repairModeCode = mtcLossRepairInfo.repairModeCode;

    /**
     * Database Column Remarks:
     *   项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.item_name")
    public static final SqlColumn<String> itemName = mtcLossRepairInfo.itemName;

    /**
     * Database Column Remarks:
     *   工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.manpower_fee")
    public static final SqlColumn<BigDecimal> manpowerFee = mtcLossRepairInfo.manpowerFee;

    /**
     * Database Column Remarks:
     *   工时参考价格
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.manpower_ref_fee")
    public static final SqlColumn<BigDecimal> manpowerRefFee = mtcLossRepairInfo.manpowerRefFee;

    /**
     * Database Column Remarks:
     *   自定义修理标记（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.self_config_flag")
    public static final SqlColumn<Integer> selfConfigFlag = mtcLossRepairInfo.selfConfigFlag;

    /**
     * Database Column Remarks:
     *   险种代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.item_cover_code")
    public static final SqlColumn<Integer> itemCoverCode = mtcLossRepairInfo.itemCoverCode;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.remark")
    public static final SqlColumn<String> remark = mtcLossRepairInfo.remark;

    /**
     * Database Column Remarks:
     *   重开次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.clm_tms")
    public static final SqlColumn<String> clmTms = mtcLossRepairInfo.clmTms;

    /**
     * Database Column Remarks:
     *   工时数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.eval_hour")
    public static final SqlColumn<BigDecimal> evalHour = mtcLossRepairInfo.evalHour;

    /**
     * Database Column Remarks:
     *   工时单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_unit_price")
    public static final SqlColumn<BigDecimal> repairUnitPrice = mtcLossRepairInfo.repairUnitPrice;

    /**
     * Database Column Remarks:
     *   损失程度代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_level_code")
    public static final SqlColumn<String> repairLevelCode = mtcLossRepairInfo.repairLevelCode;

    /**
     * Database Column Remarks:
     *   损失程度名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.repair_level_name")
    public static final SqlColumn<String> repairLevelName = mtcLossRepairInfo.repairLevelName;

    /**
     * Database Column Remarks:
     *   核损工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.audit_manpower_fee")
    public static final SqlColumn<BigDecimal> auditManpowerFee = mtcLossRepairInfo.auditManpowerFee;

    /**
     * Database Column Remarks:
     *   核损工时数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.appr_hour")
    public static final SqlColumn<BigDecimal> apprHour = mtcLossRepairInfo.apprHour;

    /**
     * Database Column Remarks:
     *   审核状态（00：待处理 01：通过 02：价格异议 03：建议剔除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.check_state")
    public static final SqlColumn<String> checkState = mtcLossRepairInfo.checkState;

    /**
     * Database Column Remarks:
     *   核损备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.audit_remark")
    public static final SqlColumn<String> auditRemark = mtcLossRepairInfo.auditRemark;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.status")
    public static final SqlColumn<Integer> status = mtcLossRepairInfo.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.misc_Desc")
    public static final SqlColumn<String> miscDesc = mtcLossRepairInfo.miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.create_by")
    public static final SqlColumn<String> createBy = mtcLossRepairInfo.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.created_time")
    public static final SqlColumn<Date> createdTime = mtcLossRepairInfo.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.update_by")
    public static final SqlColumn<String> updateBy = mtcLossRepairInfo.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_info.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcLossRepairInfo.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    public static final class MtcLossRepairInfo extends AliasableSqlTable<MtcLossRepairInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<String> repairId = column("repair_id", JDBCType.VARCHAR);

        public final SqlColumn<String> repairModeCode = column("repair_mode_code", JDBCType.VARCHAR);

        public final SqlColumn<String> itemName = column("item_name", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> manpowerFee = column("manpower_fee", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> manpowerRefFee = column("manpower_ref_fee", JDBCType.DECIMAL);

        public final SqlColumn<Integer> selfConfigFlag = column("self_config_flag", JDBCType.INTEGER);

        public final SqlColumn<Integer> itemCoverCode = column("item_cover_code", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> clmTms = column("clm_tms", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> evalHour = column("eval_hour", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> repairUnitPrice = column("repair_unit_price", JDBCType.DECIMAL);

        public final SqlColumn<String> repairLevelCode = column("repair_level_code", JDBCType.VARCHAR);

        public final SqlColumn<String> repairLevelName = column("repair_level_name", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> auditManpowerFee = column("audit_manpower_fee", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> apprHour = column("appr_hour", JDBCType.DECIMAL);

        public final SqlColumn<String> checkState = column("check_state", JDBCType.VARCHAR);

        public final SqlColumn<String> auditRemark = column("audit_remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_Desc", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcLossRepairInfo() {
            super("mtc_loss_repair_info", MtcLossRepairInfo::new);
        }
    }
}