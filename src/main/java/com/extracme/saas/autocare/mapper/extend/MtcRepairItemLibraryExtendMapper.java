package com.extracme.saas.autocare.mapper.extend;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcRepairItemLibraryMapper;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import java.util.List;

/**
 * 维修项目信息扩展Mapper
 */
@Mapper
@TenantSchema // 可以指定某些方法不使用租户Schema
public interface MtcRepairItemLibraryExtendMapper extends MtcRepairItemLibraryMapper {

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="QueryReplaceItemListResult", value = {
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="serial_number", property="serialNumber", jdbcType=JdbcType.INTEGER),
            @Result(column="item_no", property="itemNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
            @Result(column="item_type", property="itemType", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_model_seq", property="vehicleModelSeq", jdbcType=JdbcType.BIGINT),
            @Result(column="vehicle_model_name", property="vehicleModelInfo", jdbcType=JdbcType.VARCHAR),
            @Result(column="hour_fee_national_market_price", property="hourFeeNationalMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="hour_fee_national_highest_price", property="hourFeeNationalHighestPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="material_cost_national_market_price", property="materialCostNationalMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="material_cost_national_highest_price", property="materialCostNationalHighestPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="status", property="status", jdbcType=JdbcType.INTEGER)
    })
    List<RepairItemLibraryListVO> queryRepairItemLibraryList(SelectStatementProvider selectStatement);

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="ExportListResult", value = {
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="serial_number", property="serialNumber", jdbcType=JdbcType.INTEGER),
            @Result(column="item_no", property="itemNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
            @Result(column="item_type", property="itemType", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_model_seq", property="vehicleModelSeq", jdbcType=JdbcType.BIGINT),
            @Result(column="vehicle_model_name", property="vehicleModelInfo", jdbcType=JdbcType.VARCHAR),
            @Result(column="hour_fee_national_market_price", property="hourFeeNationalMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="hour_fee_national_highest_price", property="hourFeeNationalHighestPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="material_cost_national_market_price", property="materialCostNationalMarketPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="material_cost_national_highest_price", property="materialCostNationalHighestPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="status", property="status", jdbcType=JdbcType.INTEGER)
    })
    List<RepairItemLibraryListVO> exportList(SelectStatementProvider selectStatement);

} 