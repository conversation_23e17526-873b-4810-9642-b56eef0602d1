package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcRepairDepotInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcRepairDepotInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcRepairDepotInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, ssoUserId, repairDepotId, repairDepotSapCode, repairDepotName, repairDepotOrgId, repairDepotAccount, repairDepotGrade, repairDepotType, isShow, maintenancePoint, provinceId, cityId, areaId, address, taxRate, linkmanName, cooperationMode, accidentContacts, accidentTel, maintenanceContacts, maintenanceTel, repairDepotLongitude, repairDepotLatitude, vehicleModelAllFlag, ssoCreateTime, ssoUpdateTime, oldRepairFactoryCode, status, delFlag, remark, ssoFlag, canRepairItem, warrantyPoint, businessType, businessStartTime, businessEndTime, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcRepairDepotInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcRepairDepotInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="sso_user_id", property="ssoUserId", jdbcType=JdbcType.BIGINT),
        @Result(column="repair_depot_id", property="repairDepotId", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_sap_code", property="repairDepotSapCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_name", property="repairDepotName", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_org_id", property="repairDepotOrgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_account", property="repairDepotAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_grade", property="repairDepotGrade", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_type", property="repairDepotType", jdbcType=JdbcType.INTEGER),
        @Result(column="is_show", property="isShow", jdbcType=JdbcType.INTEGER),
        @Result(column="maintenance_point", property="maintenancePoint", jdbcType=JdbcType.INTEGER),
        @Result(column="province_id", property="provinceId", jdbcType=JdbcType.BIGINT),
        @Result(column="city_id", property="cityId", jdbcType=JdbcType.BIGINT),
        @Result(column="area_id", property="areaId", jdbcType=JdbcType.BIGINT),
        @Result(column="address", property="address", jdbcType=JdbcType.VARCHAR),
        @Result(column="tax_rate", property="taxRate", jdbcType=JdbcType.VARCHAR),
        @Result(column="linkman_name", property="linkmanName", jdbcType=JdbcType.VARCHAR),
        @Result(column="cooperation_mode", property="cooperationMode", jdbcType=JdbcType.VARCHAR),
        @Result(column="accident_contacts", property="accidentContacts", jdbcType=JdbcType.VARCHAR),
        @Result(column="accident_tel", property="accidentTel", jdbcType=JdbcType.VARCHAR),
        @Result(column="maintenance_contacts", property="maintenanceContacts", jdbcType=JdbcType.VARCHAR),
        @Result(column="maintenance_tel", property="maintenanceTel", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_longitude", property="repairDepotLongitude", jdbcType=JdbcType.DECIMAL),
        @Result(column="repair_depot_latitude", property="repairDepotLatitude", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_model_all_flag", property="vehicleModelAllFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="sso_create_time", property="ssoCreateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="sso_update_time", property="ssoUpdateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="old_repair_factory_code", property="oldRepairFactoryCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="del_flag", property="delFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="sso_flag", property="ssoFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="can_repair_item", property="canRepairItem", jdbcType=JdbcType.INTEGER),
        @Result(column="warranty_point", property="warrantyPoint", jdbcType=JdbcType.INTEGER),
        @Result(column="business_type", property="businessType", jdbcType=JdbcType.INTEGER),
        @Result(column="business_start_time", property="businessStartTime", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_end_time", property="businessEndTime", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcRepairDepotInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcRepairDepotInfoResult")
    Optional<MtcRepairDepotInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcRepairDepotInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcRepairDepotInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default int insert(MtcRepairDepotInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairDepotInfo, c ->
            c.map(ssoUserId).toProperty("ssoUserId")
            .map(repairDepotId).toProperty("repairDepotId")
            .map(repairDepotSapCode).toProperty("repairDepotSapCode")
            .map(repairDepotName).toProperty("repairDepotName")
            .map(repairDepotOrgId).toProperty("repairDepotOrgId")
            .map(repairDepotAccount).toProperty("repairDepotAccount")
            .map(repairDepotGrade).toProperty("repairDepotGrade")
            .map(repairDepotType).toProperty("repairDepotType")
            .map(isShow).toProperty("isShow")
            .map(maintenancePoint).toProperty("maintenancePoint")
            .map(provinceId).toProperty("provinceId")
            .map(cityId).toProperty("cityId")
            .map(areaId).toProperty("areaId")
            .map(address).toProperty("address")
            .map(taxRate).toProperty("taxRate")
            .map(linkmanName).toProperty("linkmanName")
            .map(cooperationMode).toProperty("cooperationMode")
            .map(accidentContacts).toProperty("accidentContacts")
            .map(accidentTel).toProperty("accidentTel")
            .map(maintenanceContacts).toProperty("maintenanceContacts")
            .map(maintenanceTel).toProperty("maintenanceTel")
            .map(repairDepotLongitude).toProperty("repairDepotLongitude")
            .map(repairDepotLatitude).toProperty("repairDepotLatitude")
            .map(vehicleModelAllFlag).toProperty("vehicleModelAllFlag")
            .map(ssoCreateTime).toProperty("ssoCreateTime")
            .map(ssoUpdateTime).toProperty("ssoUpdateTime")
            .map(oldRepairFactoryCode).toProperty("oldRepairFactoryCode")
            .map(status).toProperty("status")
            .map(delFlag).toProperty("delFlag")
            .map(remark).toProperty("remark")
            .map(ssoFlag).toProperty("ssoFlag")
            .map(canRepairItem).toProperty("canRepairItem")
            .map(warrantyPoint).toProperty("warrantyPoint")
            .map(businessType).toProperty("businessType")
            .map(businessStartTime).toProperty("businessStartTime")
            .map(businessEndTime).toProperty("businessEndTime")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default int insertSelective(MtcRepairDepotInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairDepotInfo, c ->
            c.map(ssoUserId).toPropertyWhenPresent("ssoUserId", row::getSsoUserId)
            .map(repairDepotId).toPropertyWhenPresent("repairDepotId", row::getRepairDepotId)
            .map(repairDepotSapCode).toPropertyWhenPresent("repairDepotSapCode", row::getRepairDepotSapCode)
            .map(repairDepotName).toPropertyWhenPresent("repairDepotName", row::getRepairDepotName)
            .map(repairDepotOrgId).toPropertyWhenPresent("repairDepotOrgId", row::getRepairDepotOrgId)
            .map(repairDepotAccount).toPropertyWhenPresent("repairDepotAccount", row::getRepairDepotAccount)
            .map(repairDepotGrade).toPropertyWhenPresent("repairDepotGrade", row::getRepairDepotGrade)
            .map(repairDepotType).toPropertyWhenPresent("repairDepotType", row::getRepairDepotType)
            .map(isShow).toPropertyWhenPresent("isShow", row::getIsShow)
            .map(maintenancePoint).toPropertyWhenPresent("maintenancePoint", row::getMaintenancePoint)
            .map(provinceId).toPropertyWhenPresent("provinceId", row::getProvinceId)
            .map(cityId).toPropertyWhenPresent("cityId", row::getCityId)
            .map(areaId).toPropertyWhenPresent("areaId", row::getAreaId)
            .map(address).toPropertyWhenPresent("address", row::getAddress)
            .map(taxRate).toPropertyWhenPresent("taxRate", row::getTaxRate)
            .map(linkmanName).toPropertyWhenPresent("linkmanName", row::getLinkmanName)
            .map(cooperationMode).toPropertyWhenPresent("cooperationMode", row::getCooperationMode)
            .map(accidentContacts).toPropertyWhenPresent("accidentContacts", row::getAccidentContacts)
            .map(accidentTel).toPropertyWhenPresent("accidentTel", row::getAccidentTel)
            .map(maintenanceContacts).toPropertyWhenPresent("maintenanceContacts", row::getMaintenanceContacts)
            .map(maintenanceTel).toPropertyWhenPresent("maintenanceTel", row::getMaintenanceTel)
            .map(repairDepotLongitude).toPropertyWhenPresent("repairDepotLongitude", row::getRepairDepotLongitude)
            .map(repairDepotLatitude).toPropertyWhenPresent("repairDepotLatitude", row::getRepairDepotLatitude)
            .map(vehicleModelAllFlag).toPropertyWhenPresent("vehicleModelAllFlag", row::getVehicleModelAllFlag)
            .map(ssoCreateTime).toPropertyWhenPresent("ssoCreateTime", row::getSsoCreateTime)
            .map(ssoUpdateTime).toPropertyWhenPresent("ssoUpdateTime", row::getSsoUpdateTime)
            .map(oldRepairFactoryCode).toPropertyWhenPresent("oldRepairFactoryCode", row::getOldRepairFactoryCode)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(delFlag).toPropertyWhenPresent("delFlag", row::getDelFlag)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(ssoFlag).toPropertyWhenPresent("ssoFlag", row::getSsoFlag)
            .map(canRepairItem).toPropertyWhenPresent("canRepairItem", row::getCanRepairItem)
            .map(warrantyPoint).toPropertyWhenPresent("warrantyPoint", row::getWarrantyPoint)
            .map(businessType).toPropertyWhenPresent("businessType", row::getBusinessType)
            .map(businessStartTime).toPropertyWhenPresent("businessStartTime", row::getBusinessStartTime)
            .map(businessEndTime).toPropertyWhenPresent("businessEndTime", row::getBusinessEndTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default Optional<MtcRepairDepotInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcRepairDepotInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default List<MtcRepairDepotInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcRepairDepotInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default List<MtcRepairDepotInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcRepairDepotInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default Optional<MtcRepairDepotInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcRepairDepotInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcRepairDepotInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(ssoUserId).equalTo(row::getSsoUserId)
                .set(repairDepotId).equalTo(row::getRepairDepotId)
                .set(repairDepotSapCode).equalTo(row::getRepairDepotSapCode)
                .set(repairDepotName).equalTo(row::getRepairDepotName)
                .set(repairDepotOrgId).equalTo(row::getRepairDepotOrgId)
                .set(repairDepotAccount).equalTo(row::getRepairDepotAccount)
                .set(repairDepotGrade).equalTo(row::getRepairDepotGrade)
                .set(repairDepotType).equalTo(row::getRepairDepotType)
                .set(isShow).equalTo(row::getIsShow)
                .set(maintenancePoint).equalTo(row::getMaintenancePoint)
                .set(provinceId).equalTo(row::getProvinceId)
                .set(cityId).equalTo(row::getCityId)
                .set(areaId).equalTo(row::getAreaId)
                .set(address).equalTo(row::getAddress)
                .set(taxRate).equalTo(row::getTaxRate)
                .set(linkmanName).equalTo(row::getLinkmanName)
                .set(cooperationMode).equalTo(row::getCooperationMode)
                .set(accidentContacts).equalTo(row::getAccidentContacts)
                .set(accidentTel).equalTo(row::getAccidentTel)
                .set(maintenanceContacts).equalTo(row::getMaintenanceContacts)
                .set(maintenanceTel).equalTo(row::getMaintenanceTel)
                .set(repairDepotLongitude).equalTo(row::getRepairDepotLongitude)
                .set(repairDepotLatitude).equalTo(row::getRepairDepotLatitude)
                .set(vehicleModelAllFlag).equalTo(row::getVehicleModelAllFlag)
                .set(ssoCreateTime).equalTo(row::getSsoCreateTime)
                .set(ssoUpdateTime).equalTo(row::getSsoUpdateTime)
                .set(oldRepairFactoryCode).equalTo(row::getOldRepairFactoryCode)
                .set(status).equalTo(row::getStatus)
                .set(delFlag).equalTo(row::getDelFlag)
                .set(remark).equalTo(row::getRemark)
                .set(ssoFlag).equalTo(row::getSsoFlag)
                .set(canRepairItem).equalTo(row::getCanRepairItem)
                .set(warrantyPoint).equalTo(row::getWarrantyPoint)
                .set(businessType).equalTo(row::getBusinessType)
                .set(businessStartTime).equalTo(row::getBusinessStartTime)
                .set(businessEndTime).equalTo(row::getBusinessEndTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcRepairDepotInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(ssoUserId).equalToWhenPresent(row::getSsoUserId)
                .set(repairDepotId).equalToWhenPresent(row::getRepairDepotId)
                .set(repairDepotSapCode).equalToWhenPresent(row::getRepairDepotSapCode)
                .set(repairDepotName).equalToWhenPresent(row::getRepairDepotName)
                .set(repairDepotOrgId).equalToWhenPresent(row::getRepairDepotOrgId)
                .set(repairDepotAccount).equalToWhenPresent(row::getRepairDepotAccount)
                .set(repairDepotGrade).equalToWhenPresent(row::getRepairDepotGrade)
                .set(repairDepotType).equalToWhenPresent(row::getRepairDepotType)
                .set(isShow).equalToWhenPresent(row::getIsShow)
                .set(maintenancePoint).equalToWhenPresent(row::getMaintenancePoint)
                .set(provinceId).equalToWhenPresent(row::getProvinceId)
                .set(cityId).equalToWhenPresent(row::getCityId)
                .set(areaId).equalToWhenPresent(row::getAreaId)
                .set(address).equalToWhenPresent(row::getAddress)
                .set(taxRate).equalToWhenPresent(row::getTaxRate)
                .set(linkmanName).equalToWhenPresent(row::getLinkmanName)
                .set(cooperationMode).equalToWhenPresent(row::getCooperationMode)
                .set(accidentContacts).equalToWhenPresent(row::getAccidentContacts)
                .set(accidentTel).equalToWhenPresent(row::getAccidentTel)
                .set(maintenanceContacts).equalToWhenPresent(row::getMaintenanceContacts)
                .set(maintenanceTel).equalToWhenPresent(row::getMaintenanceTel)
                .set(repairDepotLongitude).equalToWhenPresent(row::getRepairDepotLongitude)
                .set(repairDepotLatitude).equalToWhenPresent(row::getRepairDepotLatitude)
                .set(vehicleModelAllFlag).equalToWhenPresent(row::getVehicleModelAllFlag)
                .set(ssoCreateTime).equalToWhenPresent(row::getSsoCreateTime)
                .set(ssoUpdateTime).equalToWhenPresent(row::getSsoUpdateTime)
                .set(oldRepairFactoryCode).equalToWhenPresent(row::getOldRepairFactoryCode)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(delFlag).equalToWhenPresent(row::getDelFlag)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(ssoFlag).equalToWhenPresent(row::getSsoFlag)
                .set(canRepairItem).equalToWhenPresent(row::getCanRepairItem)
                .set(warrantyPoint).equalToWhenPresent(row::getWarrantyPoint)
                .set(businessType).equalToWhenPresent(row::getBusinessType)
                .set(businessStartTime).equalToWhenPresent(row::getBusinessStartTime)
                .set(businessEndTime).equalToWhenPresent(row::getBusinessEndTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default int updateByPrimaryKey(MtcRepairDepotInfo row) {
        return update(c ->
            c.set(ssoUserId).equalTo(row::getSsoUserId)
            .set(repairDepotId).equalTo(row::getRepairDepotId)
            .set(repairDepotSapCode).equalTo(row::getRepairDepotSapCode)
            .set(repairDepotName).equalTo(row::getRepairDepotName)
            .set(repairDepotOrgId).equalTo(row::getRepairDepotOrgId)
            .set(repairDepotAccount).equalTo(row::getRepairDepotAccount)
            .set(repairDepotGrade).equalTo(row::getRepairDepotGrade)
            .set(repairDepotType).equalTo(row::getRepairDepotType)
            .set(isShow).equalTo(row::getIsShow)
            .set(maintenancePoint).equalTo(row::getMaintenancePoint)
            .set(provinceId).equalTo(row::getProvinceId)
            .set(cityId).equalTo(row::getCityId)
            .set(areaId).equalTo(row::getAreaId)
            .set(address).equalTo(row::getAddress)
            .set(taxRate).equalTo(row::getTaxRate)
            .set(linkmanName).equalTo(row::getLinkmanName)
            .set(cooperationMode).equalTo(row::getCooperationMode)
            .set(accidentContacts).equalTo(row::getAccidentContacts)
            .set(accidentTel).equalTo(row::getAccidentTel)
            .set(maintenanceContacts).equalTo(row::getMaintenanceContacts)
            .set(maintenanceTel).equalTo(row::getMaintenanceTel)
            .set(repairDepotLongitude).equalTo(row::getRepairDepotLongitude)
            .set(repairDepotLatitude).equalTo(row::getRepairDepotLatitude)
            .set(vehicleModelAllFlag).equalTo(row::getVehicleModelAllFlag)
            .set(ssoCreateTime).equalTo(row::getSsoCreateTime)
            .set(ssoUpdateTime).equalTo(row::getSsoUpdateTime)
            .set(oldRepairFactoryCode).equalTo(row::getOldRepairFactoryCode)
            .set(status).equalTo(row::getStatus)
            .set(delFlag).equalTo(row::getDelFlag)
            .set(remark).equalTo(row::getRemark)
            .set(ssoFlag).equalTo(row::getSsoFlag)
            .set(canRepairItem).equalTo(row::getCanRepairItem)
            .set(warrantyPoint).equalTo(row::getWarrantyPoint)
            .set(businessType).equalTo(row::getBusinessType)
            .set(businessStartTime).equalTo(row::getBusinessStartTime)
            .set(businessEndTime).equalTo(row::getBusinessEndTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    default int updateByPrimaryKeySelective(MtcRepairDepotInfo row) {
        return update(c ->
            c.set(ssoUserId).equalToWhenPresent(row::getSsoUserId)
            .set(repairDepotId).equalToWhenPresent(row::getRepairDepotId)
            .set(repairDepotSapCode).equalToWhenPresent(row::getRepairDepotSapCode)
            .set(repairDepotName).equalToWhenPresent(row::getRepairDepotName)
            .set(repairDepotOrgId).equalToWhenPresent(row::getRepairDepotOrgId)
            .set(repairDepotAccount).equalToWhenPresent(row::getRepairDepotAccount)
            .set(repairDepotGrade).equalToWhenPresent(row::getRepairDepotGrade)
            .set(repairDepotType).equalToWhenPresent(row::getRepairDepotType)
            .set(isShow).equalToWhenPresent(row::getIsShow)
            .set(maintenancePoint).equalToWhenPresent(row::getMaintenancePoint)
            .set(provinceId).equalToWhenPresent(row::getProvinceId)
            .set(cityId).equalToWhenPresent(row::getCityId)
            .set(areaId).equalToWhenPresent(row::getAreaId)
            .set(address).equalToWhenPresent(row::getAddress)
            .set(taxRate).equalToWhenPresent(row::getTaxRate)
            .set(linkmanName).equalToWhenPresent(row::getLinkmanName)
            .set(cooperationMode).equalToWhenPresent(row::getCooperationMode)
            .set(accidentContacts).equalToWhenPresent(row::getAccidentContacts)
            .set(accidentTel).equalToWhenPresent(row::getAccidentTel)
            .set(maintenanceContacts).equalToWhenPresent(row::getMaintenanceContacts)
            .set(maintenanceTel).equalToWhenPresent(row::getMaintenanceTel)
            .set(repairDepotLongitude).equalToWhenPresent(row::getRepairDepotLongitude)
            .set(repairDepotLatitude).equalToWhenPresent(row::getRepairDepotLatitude)
            .set(vehicleModelAllFlag).equalToWhenPresent(row::getVehicleModelAllFlag)
            .set(ssoCreateTime).equalToWhenPresent(row::getSsoCreateTime)
            .set(ssoUpdateTime).equalToWhenPresent(row::getSsoUpdateTime)
            .set(oldRepairFactoryCode).equalToWhenPresent(row::getOldRepairFactoryCode)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(delFlag).equalToWhenPresent(row::getDelFlag)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(ssoFlag).equalToWhenPresent(row::getSsoFlag)
            .set(canRepairItem).equalToWhenPresent(row::getCanRepairItem)
            .set(warrantyPoint).equalToWhenPresent(row::getWarrantyPoint)
            .set(businessType).equalToWhenPresent(row::getBusinessType)
            .set(businessStartTime).equalToWhenPresent(row::getBusinessStartTime)
            .set(businessEndTime).equalToWhenPresent(row::getBusinessEndTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}