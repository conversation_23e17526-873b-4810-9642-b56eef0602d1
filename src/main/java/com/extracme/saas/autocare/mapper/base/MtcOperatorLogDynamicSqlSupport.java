package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcOperatorLogDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    public static final MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_operator_log.id")
    public static final SqlColumn<Long> id = mtcOperatorLog.id;

    /**
     * Database Column Remarks:
     *   表名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_operator_log.table_name")
    public static final SqlColumn<String> tableName = mtcOperatorLog.tableName;

    /**
     * Database Column Remarks:
     *   记录主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_operator_log.record_id")
    public static final SqlColumn<Long> recordId = mtcOperatorLog.recordId;

    /**
     * Database Column Remarks:
     *   操作内容
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_operator_log.ope_content")
    public static final SqlColumn<String> opeContent = mtcOperatorLog.opeContent;

    /**
     * Database Column Remarks:
     *   当前环节编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_operator_log.current_activity_code")
    public static final SqlColumn<String> currentActivityCode = mtcOperatorLog.currentActivityCode;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_operator_log.remark")
    public static final SqlColumn<String> remark = mtcOperatorLog.remark;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_operator_log.create_by")
    public static final SqlColumn<String> createBy = mtcOperatorLog.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_operator_log.created_time")
    public static final SqlColumn<Date> createdTime = mtcOperatorLog.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_operator_log.update_by")
    public static final SqlColumn<String> updateBy = mtcOperatorLog.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_operator_log.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcOperatorLog.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_operator_log")
    public static final class MtcOperatorLog extends AliasableSqlTable<MtcOperatorLog> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> tableName = column("table_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> recordId = column("record_id", JDBCType.BIGINT);

        public final SqlColumn<String> opeContent = column("ope_content", JDBCType.VARCHAR);

        public final SqlColumn<String> currentActivityCode = column("current_activity_code", JDBCType.VARCHAR);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcOperatorLog() {
            super("mtc_operator_log", MtcOperatorLog::new);
        }
    }
}