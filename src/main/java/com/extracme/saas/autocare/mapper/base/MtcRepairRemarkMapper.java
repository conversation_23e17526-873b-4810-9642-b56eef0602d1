package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcRepairRemarkDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcRepairRemark;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcRepairRemarkMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNo, activityCode, remark, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcRepairRemark> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcRepairRemarkResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_no", property="taskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="activity_code", property="activityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcRepairRemark> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcRepairRemarkResult")
    Optional<MtcRepairRemark> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcRepairRemark, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcRepairRemark, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default int insert(MtcRepairRemark row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairRemark, c ->
            c.map(taskNo).toProperty("taskNo")
            .map(activityCode).toProperty("activityCode")
            .map(remark).toProperty("remark")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default int insertSelective(MtcRepairRemark row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairRemark, c ->
            c.map(taskNo).toPropertyWhenPresent("taskNo", row::getTaskNo)
            .map(activityCode).toPropertyWhenPresent("activityCode", row::getActivityCode)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default Optional<MtcRepairRemark> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcRepairRemark, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default List<MtcRepairRemark> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcRepairRemark, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default List<MtcRepairRemark> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcRepairRemark, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default Optional<MtcRepairRemark> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcRepairRemark, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcRepairRemark row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalTo(row::getTaskNo)
                .set(activityCode).equalTo(row::getActivityCode)
                .set(remark).equalTo(row::getRemark)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcRepairRemark row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(activityCode).equalToWhenPresent(row::getActivityCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default int updateByPrimaryKey(MtcRepairRemark row) {
        return update(c ->
            c.set(taskNo).equalTo(row::getTaskNo)
            .set(activityCode).equalTo(row::getActivityCode)
            .set(remark).equalTo(row::getRemark)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    default int updateByPrimaryKeySelective(MtcRepairRemark row) {
        return update(c ->
            c.set(taskNo).equalToWhenPresent(row::getTaskNo)
            .set(activityCode).equalToWhenPresent(row::getActivityCode)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}