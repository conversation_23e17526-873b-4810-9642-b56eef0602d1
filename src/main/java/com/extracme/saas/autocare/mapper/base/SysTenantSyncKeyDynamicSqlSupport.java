package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SysTenantSyncKeyDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    public static final SysTenantSyncKey sysTenantSyncKey = new SysTenantSyncKey();

    /**
     * Database Column Remarks:
     *   主键ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.id")
    public static final SqlColumn<Long> id = sysTenantSyncKey.id;

    /**
     * Database Column Remarks:
     *   租户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.tenant_id")
    public static final SqlColumn<Long> tenantId = sysTenantSyncKey.tenantId;

    /**
     * Database Column Remarks:
     *   租户编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.tenant_code")
    public static final SqlColumn<String> tenantCode = sysTenantSyncKey.tenantCode;

    /**
     * Database Column Remarks:
     *   同步密钥（用于租户身份识别）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.sync_key")
    public static final SqlColumn<String> syncKey = sysTenantSyncKey.syncKey;

    /**
     * Database Column Remarks:
     *   加密密钥（Base64编码，已废弃）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.encryption_key")
    public static final SqlColumn<String> encryptionKey = sysTenantSyncKey.encryptionKey;

    /**
     * Database Column Remarks:
     *   密钥状态：1-启用，0-禁用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_status")
    public static final SqlColumn<Integer> keyStatus = sysTenantSyncKey.keyStatus;

    /**
     * Database Column Remarks:
     *   密钥生成时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_generate_time")
    public static final SqlColumn<Date> keyGenerateTime = sysTenantSyncKey.keyGenerateTime;

    /**
     * Database Column Remarks:
     *   密钥过期时间（NULL表示永不过期）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.key_expire_time")
    public static final SqlColumn<Date> keyExpireTime = sysTenantSyncKey.keyExpireTime;

    /**
     * Database Column Remarks:
     *   最后使用时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.last_used_time")
    public static final SqlColumn<Date> lastUsedTime = sysTenantSyncKey.lastUsedTime;

    /**
     * Database Column Remarks:
     *   使用次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.usage_count")
    public static final SqlColumn<Long> usageCount = sysTenantSyncKey.usageCount;

    /**
     * Database Column Remarks:
     *   备注信息
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.remark")
    public static final SqlColumn<String> remark = sysTenantSyncKey.remark;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.create_time")
    public static final SqlColumn<Date> createTime = sysTenantSyncKey.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.create_by")
    public static final SqlColumn<String> createBy = sysTenantSyncKey.createBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.update_time")
    public static final SqlColumn<Date> updateTime = sysTenantSyncKey.updateTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_tenant_sync_key.update_by")
    public static final SqlColumn<String> updateBy = sysTenantSyncKey.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    public static final class SysTenantSyncKey extends AliasableSqlTable<SysTenantSyncKey> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> tenantId = column("tenant_id", JDBCType.BIGINT);

        public final SqlColumn<String> tenantCode = column("tenant_code", JDBCType.VARCHAR);

        public final SqlColumn<String> syncKey = column("sync_key", JDBCType.VARCHAR);

        public final SqlColumn<String> encryptionKey = column("encryption_key", JDBCType.VARCHAR);

        public final SqlColumn<Integer> keyStatus = column("key_status", JDBCType.INTEGER);

        public final SqlColumn<Date> keyGenerateTime = column("key_generate_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> keyExpireTime = column("key_expire_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> lastUsedTime = column("last_used_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> usageCount = column("usage_count", JDBCType.BIGINT);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public SysTenantSyncKey() {
            super("sys_tenant_sync_key", SysTenantSyncKey::new);
        }
    }
}