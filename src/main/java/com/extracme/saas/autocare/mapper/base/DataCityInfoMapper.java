package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.DataCityInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.DataCityInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DataCityInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, cityid, city, fatherid, lon, lat, status, inCommonUse);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<DataCityInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DataCityInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="cityid", property="cityid", jdbcType=JdbcType.BIGINT),
        @Result(column="city", property="city", jdbcType=JdbcType.VARCHAR),
        @Result(column="fatherid", property="fatherid", jdbcType=JdbcType.BIGINT),
        @Result(column="lon", property="lon", jdbcType=JdbcType.DECIMAL),
        @Result(column="lat", property="lat", jdbcType=JdbcType.DECIMAL),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="in_common_use", property="inCommonUse", jdbcType=JdbcType.INTEGER)
    })
    List<DataCityInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DataCityInfoResult")
    Optional<DataCityInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, dataCityInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, dataCityInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default int insert(DataCityInfo row) {
        return MyBatis3Utils.insert(this::insert, row, dataCityInfo, c ->
            c.map(cityid).toProperty("cityid")
            .map(city).toProperty("city")
            .map(fatherid).toProperty("fatherid")
            .map(lon).toProperty("lon")
            .map(lat).toProperty("lat")
            .map(status).toProperty("status")
            .map(inCommonUse).toProperty("inCommonUse")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default int insertSelective(DataCityInfo row) {
        return MyBatis3Utils.insert(this::insert, row, dataCityInfo, c ->
            c.map(cityid).toPropertyWhenPresent("cityid", row::getCityid)
            .map(city).toPropertyWhenPresent("city", row::getCity)
            .map(fatherid).toPropertyWhenPresent("fatherid", row::getFatherid)
            .map(lon).toPropertyWhenPresent("lon", row::getLon)
            .map(lat).toPropertyWhenPresent("lat", row::getLat)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(inCommonUse).toPropertyWhenPresent("inCommonUse", row::getInCommonUse)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default Optional<DataCityInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, dataCityInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default List<DataCityInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, dataCityInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default List<DataCityInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, dataCityInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default Optional<DataCityInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, dataCityInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    static UpdateDSL<UpdateModel> updateAllColumns(DataCityInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(cityid).equalTo(row::getCityid)
                .set(city).equalTo(row::getCity)
                .set(fatherid).equalTo(row::getFatherid)
                .set(lon).equalTo(row::getLon)
                .set(lat).equalTo(row::getLat)
                .set(status).equalTo(row::getStatus)
                .set(inCommonUse).equalTo(row::getInCommonUse);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DataCityInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(cityid).equalToWhenPresent(row::getCityid)
                .set(city).equalToWhenPresent(row::getCity)
                .set(fatherid).equalToWhenPresent(row::getFatherid)
                .set(lon).equalToWhenPresent(row::getLon)
                .set(lat).equalToWhenPresent(row::getLat)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(inCommonUse).equalToWhenPresent(row::getInCommonUse);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default int updateByPrimaryKey(DataCityInfo row) {
        return update(c ->
            c.set(cityid).equalTo(row::getCityid)
            .set(city).equalTo(row::getCity)
            .set(fatherid).equalTo(row::getFatherid)
            .set(lon).equalTo(row::getLon)
            .set(lat).equalTo(row::getLat)
            .set(status).equalTo(row::getStatus)
            .set(inCommonUse).equalTo(row::getInCommonUse)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_city_info")
    default int updateByPrimaryKeySelective(DataCityInfo row) {
        return update(c ->
            c.set(cityid).equalToWhenPresent(row::getCityid)
            .set(city).equalToWhenPresent(row::getCity)
            .set(fatherid).equalToWhenPresent(row::getFatherid)
            .set(lon).equalToWhenPresent(row::getLon)
            .set(lat).equalToWhenPresent(row::getLat)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(inCommonUse).equalToWhenPresent(row::getInCommonUse)
            .where(id, isEqualTo(row::getId))
        );
    }
}