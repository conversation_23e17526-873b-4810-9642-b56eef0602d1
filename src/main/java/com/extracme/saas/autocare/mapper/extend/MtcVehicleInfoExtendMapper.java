package com.extracme.saas.autocare.mapper.extend;

import javax.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import static com.extracme.saas.autocare.mapper.base.MtcVehicleInfoDynamicSqlSupport.*;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcVehicleInfoMapper;
import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;

/**
 * 维修车辆信息扩展Mapper
 */
@Mapper
@TenantSchema
public interface MtcVehicleInfoExtendMapper extends MtcVehicleInfoMapper {

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: mtc_vehicle_info")
    default int insertSelectiveWithId(MtcVehicleInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcVehicleInfo,
                c -> c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(vin).toPropertyWhenPresent("vin", row::getVin)
                        .map(vehicleNo).toPropertyWhenPresent("vehicleNo", row::getVehicleNo)
                        .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", row::getVehicleModelId)
                        .map(vehicleOrgId).toPropertyWhenPresent("vehicleOrgId", row::getVehicleOrgId)
                        .map(operationOrgId).toPropertyWhenPresent("operationOrgId", row::getOperationOrgId)
                        .map(productLine).toPropertyWhenPresent("productLine", row::getProductLine)
                        .map(subProductLine).toPropertyWhenPresent("subProductLine", row::getSubProductLine)
                        .map(factOperateTag).toPropertyWhenPresent("factOperateTag", row::getFactOperateTag)
                        .map(engineId).toPropertyWhenPresent("engineId", row::getEngineId)
                        .map(registerDate).toPropertyWhenPresent("registerDate", row::getRegisterDate)
                        .map(tciStartdate).toPropertyWhenPresent("tciStartdate", row::getTciStartdate)
                        .map(tciEnddate).toPropertyWhenPresent("tciEnddate", row::getTciEnddate)
                        .map(insuranceBelongs).toPropertyWhenPresent("insuranceBelongs", row::getInsuranceBelongs)
                        .map(insuranceCompanyName).toPropertyWhenPresent("insuranceCompanyName", row::getInsuranceCompanyName)
                        .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
                        .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
                        .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
                        .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy));
    }
}
