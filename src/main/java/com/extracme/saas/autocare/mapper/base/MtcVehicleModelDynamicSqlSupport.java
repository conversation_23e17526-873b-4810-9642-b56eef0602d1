package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcVehicleModelDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    public static final MtcVehicleModel mtcVehicleModel = new MtcVehicleModel();

    /**
     * Database Column Remarks:
     *   主键ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_model.id")
    public static final SqlColumn<Long> id = mtcVehicleModel.id;

    /**
     * Database Column Remarks:
     *   车型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_model.vehicle_model_name")
    public static final SqlColumn<String> vehicleModelName = mtcVehicleModel.vehicleModelName;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_model.create_time")
    public static final SqlColumn<Date> createTime = mtcVehicleModel.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_model.create_by")
    public static final SqlColumn<String> createBy = mtcVehicleModel.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_model.update_time")
    public static final SqlColumn<Date> updateTime = mtcVehicleModel.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_model.update_by")
    public static final SqlColumn<String> updateBy = mtcVehicleModel.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_model")
    public static final class MtcVehicleModel extends AliasableSqlTable<MtcVehicleModel> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleModelName = column("vehicle_model_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public MtcVehicleModel() {
            super("mtc_vehicle_model", MtcVehicleModel::new);
        }
    }
}