package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.SysTenantDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.SysTenant;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysTenantMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    BasicColumn[] selectList = BasicColumn.columnList(id, tenantName, tenantCode, contactName, contactPhone, contactEmail, expireTime, maxUserCount, status, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<SysTenant> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysTenantResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="tenant_name", property="tenantName", jdbcType=JdbcType.VARCHAR),
        @Result(column="tenant_code", property="tenantCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="contact_name", property="contactName", jdbcType=JdbcType.VARCHAR),
        @Result(column="contact_phone", property="contactPhone", jdbcType=JdbcType.VARCHAR),
        @Result(column="contact_email", property="contactEmail", jdbcType=JdbcType.VARCHAR),
        @Result(column="expire_time", property="expireTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="max_user_count", property="maxUserCount", jdbcType=JdbcType.INTEGER),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<SysTenant> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysTenantResult")
    Optional<SysTenant> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysTenant, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysTenant, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default int insert(SysTenant row) {
        return MyBatis3Utils.insert(this::insert, row, sysTenant, c ->
            c.map(tenantName).toProperty("tenantName")
            .map(tenantCode).toProperty("tenantCode")
            .map(contactName).toProperty("contactName")
            .map(contactPhone).toProperty("contactPhone")
            .map(contactEmail).toProperty("contactEmail")
            .map(expireTime).toProperty("expireTime")
            .map(maxUserCount).toProperty("maxUserCount")
            .map(status).toProperty("status")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default int insertSelective(SysTenant row) {
        return MyBatis3Utils.insert(this::insert, row, sysTenant, c ->
            c.map(tenantName).toPropertyWhenPresent("tenantName", row::getTenantName)
            .map(tenantCode).toPropertyWhenPresent("tenantCode", row::getTenantCode)
            .map(contactName).toPropertyWhenPresent("contactName", row::getContactName)
            .map(contactPhone).toPropertyWhenPresent("contactPhone", row::getContactPhone)
            .map(contactEmail).toPropertyWhenPresent("contactEmail", row::getContactEmail)
            .map(expireTime).toPropertyWhenPresent("expireTime", row::getExpireTime)
            .map(maxUserCount).toPropertyWhenPresent("maxUserCount", row::getMaxUserCount)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default Optional<SysTenant> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysTenant, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default List<SysTenant> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysTenant, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default List<SysTenant> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysTenant, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default Optional<SysTenant> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysTenant, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    static UpdateDSL<UpdateModel> updateAllColumns(SysTenant row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(tenantName).equalTo(row::getTenantName)
                .set(tenantCode).equalTo(row::getTenantCode)
                .set(contactName).equalTo(row::getContactName)
                .set(contactPhone).equalTo(row::getContactPhone)
                .set(contactEmail).equalTo(row::getContactEmail)
                .set(expireTime).equalTo(row::getExpireTime)
                .set(maxUserCount).equalTo(row::getMaxUserCount)
                .set(status).equalTo(row::getStatus)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysTenant row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(tenantName).equalToWhenPresent(row::getTenantName)
                .set(tenantCode).equalToWhenPresent(row::getTenantCode)
                .set(contactName).equalToWhenPresent(row::getContactName)
                .set(contactPhone).equalToWhenPresent(row::getContactPhone)
                .set(contactEmail).equalToWhenPresent(row::getContactEmail)
                .set(expireTime).equalToWhenPresent(row::getExpireTime)
                .set(maxUserCount).equalToWhenPresent(row::getMaxUserCount)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default int updateByPrimaryKey(SysTenant row) {
        return update(c ->
            c.set(tenantName).equalTo(row::getTenantName)
            .set(tenantCode).equalTo(row::getTenantCode)
            .set(contactName).equalTo(row::getContactName)
            .set(contactPhone).equalTo(row::getContactPhone)
            .set(contactEmail).equalTo(row::getContactEmail)
            .set(expireTime).equalTo(row::getExpireTime)
            .set(maxUserCount).equalTo(row::getMaxUserCount)
            .set(status).equalTo(row::getStatus)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant")
    default int updateByPrimaryKeySelective(SysTenant row) {
        return update(c ->
            c.set(tenantName).equalToWhenPresent(row::getTenantName)
            .set(tenantCode).equalToWhenPresent(row::getTenantCode)
            .set(contactName).equalToWhenPresent(row::getContactName)
            .set(contactPhone).equalToWhenPresent(row::getContactPhone)
            .set(contactEmail).equalToWhenPresent(row::getContactEmail)
            .set(expireTime).equalToWhenPresent(row::getExpireTime)
            .set(maxUserCount).equalToWhenPresent(row::getMaxUserCount)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}