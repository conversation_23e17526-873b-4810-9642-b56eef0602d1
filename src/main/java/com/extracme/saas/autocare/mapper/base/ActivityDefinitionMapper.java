package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.ActivityDefinitionDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.ActivityDefinition;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ActivityDefinitionMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    BasicColumn[] selectList = BasicColumn.columnList(id, activityCode, activityName, sequence, isEnabled, createTime, createBy, updateTime, updateBy, description);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<ActivityDefinition> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ActivityDefinitionResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="activity_code", property="activityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="activity_name", property="activityName", jdbcType=JdbcType.VARCHAR),
        @Result(column="sequence", property="sequence", jdbcType=JdbcType.INTEGER),
        @Result(column="is_enabled", property="isEnabled", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="description", property="description", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<ActivityDefinition> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ActivityDefinitionResult")
    Optional<ActivityDefinition> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, activityDefinition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, activityDefinition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default int insert(ActivityDefinition row) {
        return MyBatis3Utils.insert(this::insert, row, activityDefinition, c ->
            c.map(activityCode).toProperty("activityCode")
            .map(activityName).toProperty("activityName")
            .map(sequence).toProperty("sequence")
            .map(isEnabled).toProperty("isEnabled")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
            .map(description).toProperty("description")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default int insertSelective(ActivityDefinition row) {
        return MyBatis3Utils.insert(this::insert, row, activityDefinition, c ->
            c.map(activityCode).toPropertyWhenPresent("activityCode", row::getActivityCode)
            .map(activityName).toPropertyWhenPresent("activityName", row::getActivityName)
            .map(sequence).toPropertyWhenPresent("sequence", row::getSequence)
            .map(isEnabled).toPropertyWhenPresent("isEnabled", row::getIsEnabled)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(description).toPropertyWhenPresent("description", row::getDescription)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default Optional<ActivityDefinition> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, activityDefinition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default List<ActivityDefinition> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, activityDefinition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default List<ActivityDefinition> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, activityDefinition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default Optional<ActivityDefinition> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, activityDefinition, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    static UpdateDSL<UpdateModel> updateAllColumns(ActivityDefinition row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(activityCode).equalTo(row::getActivityCode)
                .set(activityName).equalTo(row::getActivityName)
                .set(sequence).equalTo(row::getSequence)
                .set(isEnabled).equalTo(row::getIsEnabled)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(description).equalTo(row::getDescription);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ActivityDefinition row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(activityCode).equalToWhenPresent(row::getActivityCode)
                .set(activityName).equalToWhenPresent(row::getActivityName)
                .set(sequence).equalToWhenPresent(row::getSequence)
                .set(isEnabled).equalToWhenPresent(row::getIsEnabled)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(description).equalToWhenPresent(row::getDescription);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default int updateByPrimaryKey(ActivityDefinition row) {
        return update(c ->
            c.set(activityCode).equalTo(row::getActivityCode)
            .set(activityName).equalTo(row::getActivityName)
            .set(sequence).equalTo(row::getSequence)
            .set(isEnabled).equalTo(row::getIsEnabled)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(description).equalTo(row::getDescription)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_definition")
    default int updateByPrimaryKeySelective(ActivityDefinition row) {
        return update(c ->
            c.set(activityCode).equalToWhenPresent(row::getActivityCode)
            .set(activityName).equalToWhenPresent(row::getActivityName)
            .set(sequence).equalToWhenPresent(row::getSequence)
            .set(isEnabled).equalToWhenPresent(row::getIsEnabled)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(description).equalToWhenPresent(row::getDescription)
            .where(id, isEqualTo(row::getId))
        );
    }
}