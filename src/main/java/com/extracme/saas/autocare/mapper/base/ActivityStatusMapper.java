package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.ActivityStatusDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.ActivityStatus;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ActivityStatusMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    BasicColumn[] selectList = BasicColumn.columnList(id, statusCode, statusName, createTime, createBy, updateTime, updateBy, description);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<ActivityStatus> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ActivityStatusResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="status_code", property="statusCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="status_name", property="statusName", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="description", property="description", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<ActivityStatus> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ActivityStatusResult")
    Optional<ActivityStatus> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, activityStatus, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, activityStatus, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default int insert(ActivityStatus row) {
        return MyBatis3Utils.insert(this::insert, row, activityStatus, c ->
            c.map(statusCode).toProperty("statusCode")
            .map(statusName).toProperty("statusName")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
            .map(description).toProperty("description")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default int insertSelective(ActivityStatus row) {
        return MyBatis3Utils.insert(this::insert, row, activityStatus, c ->
            c.map(statusCode).toPropertyWhenPresent("statusCode", row::getStatusCode)
            .map(statusName).toPropertyWhenPresent("statusName", row::getStatusName)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(description).toPropertyWhenPresent("description", row::getDescription)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default Optional<ActivityStatus> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, activityStatus, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default List<ActivityStatus> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, activityStatus, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default List<ActivityStatus> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, activityStatus, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default Optional<ActivityStatus> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, activityStatus, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    static UpdateDSL<UpdateModel> updateAllColumns(ActivityStatus row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(statusCode).equalTo(row::getStatusCode)
                .set(statusName).equalTo(row::getStatusName)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(description).equalTo(row::getDescription);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ActivityStatus row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(statusCode).equalToWhenPresent(row::getStatusCode)
                .set(statusName).equalToWhenPresent(row::getStatusName)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(description).equalToWhenPresent(row::getDescription);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default int updateByPrimaryKey(ActivityStatus row) {
        return update(c ->
            c.set(statusCode).equalTo(row::getStatusCode)
            .set(statusName).equalTo(row::getStatusName)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(description).equalTo(row::getDescription)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_status")
    default int updateByPrimaryKeySelective(ActivityStatus row) {
        return update(c ->
            c.set(statusCode).equalToWhenPresent(row::getStatusCode)
            .set(statusName).equalToWhenPresent(row::getStatusName)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(description).equalToWhenPresent(row::getDescription)
            .where(id, isEqualTo(row::getId))
        );
    }
}