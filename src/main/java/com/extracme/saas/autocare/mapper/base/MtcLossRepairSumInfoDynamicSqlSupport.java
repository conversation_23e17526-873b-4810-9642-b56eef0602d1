package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcLossRepairSumInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    public static final MtcLossRepairSumInfo mtcLossRepairSumInfo = new MtcLossRepairSumInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.id")
    public static final SqlColumn<Long> id = mtcLossRepairSumInfo.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.task_no")
    public static final SqlColumn<String> taskNo = mtcLossRepairSumInfo.taskNo;

    /**
     * Database Column Remarks:
     *   工种编码（1：喷漆 2：钣金 3：电工 4：机修 5：拆装）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.work_type_code")
    public static final SqlColumn<String> workTypeCode = mtcLossRepairSumInfo.workTypeCode;

    /**
     * Database Column Remarks:
     *   项目数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.item_count")
    public static final SqlColumn<Integer> itemCount = mtcLossRepairSumInfo.itemCount;

    /**
     * Database Column Remarks:
     *   参考工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.reference_price")
    public static final SqlColumn<BigDecimal> referencePrice = mtcLossRepairSumInfo.referencePrice;

    /**
     * Database Column Remarks:
     *   工种折扣
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.hour_discount")
    public static final SqlColumn<BigDecimal> hourDiscount = mtcLossRepairSumInfo.hourDiscount;

    /**
     * Database Column Remarks:
     *   折后参考工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.discount_ref_price")
    public static final SqlColumn<BigDecimal> discountRefPrice = mtcLossRepairSumInfo.discountRefPrice;

    /**
     * Database Column Remarks:
     *   定损工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.eval_repair_sum")
    public static final SqlColumn<BigDecimal> evalRepairSum = mtcLossRepairSumInfo.evalRepairSum;

    /**
     * Database Column Remarks:
     *   项目数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.audit_item_count")
    public static final SqlColumn<Integer> auditItemCount = mtcLossRepairSumInfo.auditItemCount;

    /**
     * Database Column Remarks:
     *   核损工时费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.appr_repair_sum")
    public static final SqlColumn<BigDecimal> apprRepairSum = mtcLossRepairSumInfo.apprRepairSum;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.status")
    public static final SqlColumn<Integer> status = mtcLossRepairSumInfo.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.misc_Desc")
    public static final SqlColumn<String> miscDesc = mtcLossRepairSumInfo.miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.create_by")
    public static final SqlColumn<String> createBy = mtcLossRepairSumInfo.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.created_time")
    public static final SqlColumn<Date> createdTime = mtcLossRepairSumInfo.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.update_by")
    public static final SqlColumn<String> updateBy = mtcLossRepairSumInfo.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_loss_repair_sum_info.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcLossRepairSumInfo.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_sum_info")
    public static final class MtcLossRepairSumInfo extends AliasableSqlTable<MtcLossRepairSumInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<String> workTypeCode = column("work_type_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> itemCount = column("item_count", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> referencePrice = column("reference_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> hourDiscount = column("hour_discount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> discountRefPrice = column("discount_ref_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> evalRepairSum = column("eval_repair_sum", JDBCType.DECIMAL);

        public final SqlColumn<Integer> auditItemCount = column("audit_item_count", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> apprRepairSum = column("appr_repair_sum", JDBCType.DECIMAL);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_Desc", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcLossRepairSumInfo() {
            super("mtc_loss_repair_sum_info", MtcLossRepairSumInfo::new);
        }
    }
}