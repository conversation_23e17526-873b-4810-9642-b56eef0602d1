package com.extracme.saas.autocare.mapper.extend;

import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.apprRepairSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.auditItemCount;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.createBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.createdTime;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.discountRefPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.evalRepairSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.hourDiscount;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.itemCount;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.miscDesc;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.referencePrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.status;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.taskNo;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.updateBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.updatedTime;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.workTypeCode;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import org.apache.ibatis.annotations.Mapper;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoMapper;
import com.extracme.saas.autocare.model.entity.MtcLossRepairSumInfo;

@Mapper
@TenantSchema
public interface MtcLossRepairSumInfoExtendMapper extends MtcLossRepairSumInfoMapper {

    /**
     * 根据taskNo更新
     * 
     * @param row
     * @return
     */
    default int updateSelectiveByTaskNoAndStatus(MtcLossRepairSumInfo row) {
        return update(c -> c.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(workTypeCode).equalToWhenPresent(row::getWorkTypeCode)
                .set(itemCount).equalToWhenPresent(row::getItemCount)
                .set(referencePrice).equalToWhenPresent(row::getReferencePrice)
                .set(hourDiscount).equalToWhenPresent(row::getHourDiscount)
                .set(discountRefPrice).equalToWhenPresent(row::getDiscountRefPrice)
                .set(evalRepairSum).equalToWhenPresent(row::getEvalRepairSum)
                .set(auditItemCount).equalToWhenPresent(row::getAuditItemCount)
                .set(apprRepairSum).equalToWhenPresent(row::getApprRepairSum)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .where(taskNo, isEqualTo(row::getTaskNo))
                .and(status, isEqualTo(1))
                .and(workTypeCode, isEqualTo(row::getWorkTypeCode)));
    }
}
