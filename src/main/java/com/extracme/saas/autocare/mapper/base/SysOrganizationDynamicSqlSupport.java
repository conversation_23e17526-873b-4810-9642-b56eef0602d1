package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SysOrganizationDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    public static final SysOrganization sysOrganization = new SysOrganization();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.id")
    public static final SqlColumn<Long> id = sysOrganization.id;

    /**
     * Database Column Remarks:
     *   机构名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.org_name")
    public static final SqlColumn<String> orgName = sysOrganization.orgName;

    /**
     * Database Column Remarks:
     *   机构编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.org_code")
    public static final SqlColumn<String> orgCode = sysOrganization.orgCode;

    /**
     * Database Column Remarks:
     *   父级机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.parent_id")
    public static final SqlColumn<Long> parentId = sysOrganization.parentId;

    /**
     * Database Column Remarks:
     *   祖级机构列表
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.ancestors")
    public static final SqlColumn<String> ancestors = sysOrganization.ancestors;

    /**
     * Database Column Remarks:
     *   机构类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.org_type")
    public static final SqlColumn<String> orgType = sysOrganization.orgType;

    /**
     * Database Column Remarks:
     *   负责人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.leader")
    public static final SqlColumn<String> leader = sysOrganization.leader;

    /**
     * Database Column Remarks:
     *   联系电话
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.phone")
    public static final SqlColumn<String> phone = sysOrganization.phone;

    /**
     * Database Column Remarks:
     *   邮箱
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.email")
    public static final SqlColumn<String> email = sysOrganization.email;

    /**
     * Database Column Remarks:
     *   状态：0-禁用，1-启用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.status")
    public static final SqlColumn<Boolean> status = sysOrganization.status;

    /**
     * Database Column Remarks:
     *   排序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.sort")
    public static final SqlColumn<Integer> sort = sysOrganization.sort;

    /**
     * Database Column Remarks:
     *   租户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.tenant_id")
    public static final SqlColumn<Long> tenantId = sysOrganization.tenantId;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.created_by")
    public static final SqlColumn<Long> createdBy = sysOrganization.createdBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.created_time")
    public static final SqlColumn<Date> createdTime = sysOrganization.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.updated_by")
    public static final SqlColumn<Long> updatedBy = sysOrganization.updatedBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.updated_time")
    public static final SqlColumn<Date> updatedTime = sysOrganization.updatedTime;

    /**
     * Database Column Remarks:
     *   是否删除：0-否，1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_organization.deleted")
    public static final SqlColumn<Boolean> deleted = sysOrganization.deleted;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    public static final class SysOrganization extends AliasableSqlTable<SysOrganization> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> orgName = column("org_name", JDBCType.VARCHAR);

        public final SqlColumn<String> orgCode = column("org_code", JDBCType.VARCHAR);

        public final SqlColumn<Long> parentId = column("parent_id", JDBCType.BIGINT);

        public final SqlColumn<String> ancestors = column("ancestors", JDBCType.VARCHAR);

        public final SqlColumn<String> orgType = column("org_type", JDBCType.VARCHAR);

        public final SqlColumn<String> leader = column("leader", JDBCType.VARCHAR);

        public final SqlColumn<String> phone = column("phone", JDBCType.VARCHAR);

        public final SqlColumn<String> email = column("email", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> status = column("status", JDBCType.BIT);

        public final SqlColumn<Integer> sort = column("sort", JDBCType.INTEGER);

        public final SqlColumn<Long> tenantId = column("tenant_id", JDBCType.BIGINT);

        public final SqlColumn<Long> createdBy = column("created_by", JDBCType.BIGINT);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updatedBy = column("updated_by", JDBCType.BIGINT);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public SysOrganization() {
            super("sys_organization", SysOrganization::new);
        }
    }
}