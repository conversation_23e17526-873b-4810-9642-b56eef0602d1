package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcRepairItemLibraryLocalDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    public static final MtcRepairItemLibraryLocal mtcRepairItemLibraryLocal = new MtcRepairItemLibraryLocal();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.id")
    public static final SqlColumn<Long> id = mtcRepairItemLibraryLocal.id;

    /**
     * Database Column Remarks:
     *   维修项目库项目id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.item_id")
    public static final SqlColumn<Long> itemId = mtcRepairItemLibraryLocal.itemId;

    /**
     * Database Column Remarks:
     *   项目编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.item_no")
    public static final SqlColumn<String> itemNo = mtcRepairItemLibraryLocal.itemNo;

    /**
     * Database Column Remarks:
     *   运营公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.org_id")
    public static final SqlColumn<String> orgId = mtcRepairItemLibraryLocal.orgId;

    /**
     * Database Column Remarks:
     *   运营公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.org_name")
    public static final SqlColumn<String> orgName = mtcRepairItemLibraryLocal.orgName;

    /**
     * Database Column Remarks:
     *   工时费本地市场价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.hour_fee_local_market_price")
    public static final SqlColumn<BigDecimal> hourFeeLocalMarketPrice = mtcRepairItemLibraryLocal.hourFeeLocalMarketPrice;

    /**
     * Database Column Remarks:
     *   工时费本地上限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.hour_fee_local_highest_price")
    public static final SqlColumn<BigDecimal> hourFeeLocalHighestPrice = mtcRepairItemLibraryLocal.hourFeeLocalHighestPrice;

    /**
     * Database Column Remarks:
     *   材料费本地市场价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.material_cost_local_market_price")
    public static final SqlColumn<BigDecimal> materialCostLocalMarketPrice = mtcRepairItemLibraryLocal.materialCostLocalMarketPrice;

    /**
     * Database Column Remarks:
     *   材料费本地上限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.material_cost_local_highest_price")
    public static final SqlColumn<BigDecimal> materialCostLocalHighestPrice = mtcRepairItemLibraryLocal.materialCostLocalHighestPrice;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.status")
    public static final SqlColumn<Integer> status = mtcRepairItemLibraryLocal.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.misc_Desc")
    public static final SqlColumn<String> miscDesc = mtcRepairItemLibraryLocal.miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.create_by")
    public static final SqlColumn<String> createBy = mtcRepairItemLibraryLocal.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.created_time")
    public static final SqlColumn<Date> createdTime = mtcRepairItemLibraryLocal.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.update_by")
    public static final SqlColumn<String> updateBy = mtcRepairItemLibraryLocal.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcRepairItemLibraryLocal.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    public static final class MtcRepairItemLibraryLocal extends AliasableSqlTable<MtcRepairItemLibraryLocal> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> itemId = column("item_id", JDBCType.BIGINT);

        public final SqlColumn<String> itemNo = column("item_no", JDBCType.VARCHAR);

        public final SqlColumn<String> orgId = column("org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> orgName = column("org_name", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> hourFeeLocalMarketPrice = column("hour_fee_local_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> hourFeeLocalHighestPrice = column("hour_fee_local_highest_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> materialCostLocalMarketPrice = column("material_cost_local_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> materialCostLocalHighestPrice = column("material_cost_local_highest_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_Desc", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcRepairItemLibraryLocal() {
            super("mtc_repair_item_library_local", MtcRepairItemLibraryLocal::new);
        }
    }
}