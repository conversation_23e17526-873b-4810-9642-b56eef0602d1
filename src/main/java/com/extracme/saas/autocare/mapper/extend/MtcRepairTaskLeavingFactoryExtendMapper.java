package com.extracme.saas.autocare.mapper.extend;

import java.util.List;

import javax.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryMapper;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryResultVO;

/**
 * 维修任务车辆出厂记录扩展Mapper
 */
@Mapper
@TenantSchema // 可以指定某些方法不使用租户Schema
public interface MtcRepairTaskLeavingFactoryExtendMapper extends MtcRepairTaskLeavingFactoryMapper {

    /**
     * 查询维修任务出厂信息列表
     * 
     * @param queryDTO 查询条件
     * @return 维修任务出厂信息列表
     */
    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: mtc_repair_task")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "VehicleLeavingFactoryResult", value = {
        @Result(column = "id", property = "id"),
        @Result(column = "leaving_factory_id", property = "leavingFactoryId"),
        @Result(column = "task_no", property = "taskNo"), 
        @Result(column = "org_id", property = "orgId"),
        @Result(column = "org_name", property = "orgName"),
        @Result(column = "vehicle_no", property = "vehicleNo"),
        @Result(column = "vehicle_model_seq", property = "vehicleModelSeq"),
        @Result(column = "vehicle_model_info", property = "vehicleModelInfo"),
        @Result(column = "repair_type_id", property = "repairTypeId"),
        @Result(column = "repair_type_name", property = "repairTypeName"),
        @Result(column = "repair_depot_org_id", property = "repairDepotOrgId"),
        @Result(column = "repair_depot_name", property = "repairDepotName"),
        @Result(column = "repair_depot_sap_code", property = "repairDepotSapCode"),
        @Result(column = "sap_send_status", property = "sapSendStatus"),
        @Result(column = "sap_sync_flag", property = "sapSyncFlag"),
        @Result(column = "accounting_status", property = "accountingStatus"),
        @Result(column = "task_inflow_time", property = "taskInflowTime"),
        @Result(column = "vehicle_recive_time", property = "vehicleReciveTime"),
        @Result(column = "resurvey_flag", property = "resurveyFlag"),
        @Result(column = "time_out", property = "timeOut"),
        @Result(column = "repair_grade", property = "repairGrade"),
        @Result(column = "vehicle_check_time", property = "vehicleCheckTime"),
        @Result(column = "submit_date_time", property = "submitDateTime"),
        @Result(column = "current_activity_code", property = "currentActivityCode"),
        @Result(column = "status_code", property = "statusCode"),
        @Result(column = "delivery_time", property = "deliveryTime"),
        @Result(column = "renttype", property = "renttype"),
        @Result(column = "fact_operate_tag", property = "factOperateTag"),
        @Result(column = "updated_time", property = "updatedTime"),
        @Result(column = "total_money", property = "totalMoney"),
        @Result(column = "insurance_company_name", property = "insuranceCompanyName"),
        @Result(column = "review_to_sel_fee_flag", property = "reviewToSelFeeFlag"),
    })
    List<VehicleLeavingFactoryResultVO> selectLeavingFactoryList(SelectStatementProvider selectStatement);

    int selectLeavingFactoryCount(SelectStatementProvider selectStatement);
}
