package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.InsuranceCompanyInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.InsuranceCompanyInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface InsuranceCompanyInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyName, companyAbbreviation, contactNumber, status, createTime, createBy, updateTime, updateBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<InsuranceCompanyInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="InsuranceCompanyInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_name", property="companyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="company_abbreviation", property="companyAbbreviation", jdbcType=JdbcType.VARCHAR),
        @Result(column="contact_number", property="contactNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR)
    })
    List<InsuranceCompanyInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("InsuranceCompanyInfoResult")
    Optional<InsuranceCompanyInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, insuranceCompanyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, insuranceCompanyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default int insert(InsuranceCompanyInfo row) {
        return MyBatis3Utils.insert(this::insert, row, insuranceCompanyInfo, c ->
            c.map(companyName).toProperty("companyName")
            .map(companyAbbreviation).toProperty("companyAbbreviation")
            .map(contactNumber).toProperty("contactNumber")
            .map(status).toProperty("status")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default int insertSelective(InsuranceCompanyInfo row) {
        return MyBatis3Utils.insert(this::insert, row, insuranceCompanyInfo, c ->
            c.map(companyName).toPropertyWhenPresent("companyName", row::getCompanyName)
            .map(companyAbbreviation).toPropertyWhenPresent("companyAbbreviation", row::getCompanyAbbreviation)
            .map(contactNumber).toPropertyWhenPresent("contactNumber", row::getContactNumber)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default Optional<InsuranceCompanyInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, insuranceCompanyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default List<InsuranceCompanyInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, insuranceCompanyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default List<InsuranceCompanyInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, insuranceCompanyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default Optional<InsuranceCompanyInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, insuranceCompanyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    static UpdateDSL<UpdateModel> updateAllColumns(InsuranceCompanyInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyName).equalTo(row::getCompanyName)
                .set(companyAbbreviation).equalTo(row::getCompanyAbbreviation)
                .set(contactNumber).equalTo(row::getContactNumber)
                .set(status).equalTo(row::getStatus)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(InsuranceCompanyInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyName).equalToWhenPresent(row::getCompanyName)
                .set(companyAbbreviation).equalToWhenPresent(row::getCompanyAbbreviation)
                .set(contactNumber).equalToWhenPresent(row::getContactNumber)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default int updateByPrimaryKey(InsuranceCompanyInfo row) {
        return update(c ->
            c.set(companyName).equalTo(row::getCompanyName)
            .set(companyAbbreviation).equalTo(row::getCompanyAbbreviation)
            .set(contactNumber).equalTo(row::getContactNumber)
            .set(status).equalTo(row::getStatus)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    default int updateByPrimaryKeySelective(InsuranceCompanyInfo row) {
        return update(c ->
            c.set(companyName).equalToWhenPresent(row::getCompanyName)
            .set(companyAbbreviation).equalToWhenPresent(row::getCompanyAbbreviation)
            .set(contactNumber).equalToWhenPresent(row::getContactNumber)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }
}