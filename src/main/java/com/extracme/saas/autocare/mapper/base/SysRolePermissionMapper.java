package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.SysRolePermissionDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.SysRolePermission;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysRolePermissionMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    BasicColumn[] selectList = BasicColumn.columnList(id, roleId, permissionId, tenantId, createBy, createdTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<SysRolePermission> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysRolePermissionResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="role_id", property="roleId", jdbcType=JdbcType.BIGINT),
        @Result(column="permission_id", property="permissionId", jdbcType=JdbcType.BIGINT),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<SysRolePermission> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysRolePermissionResult")
    Optional<SysRolePermission> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysRolePermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysRolePermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default int insert(SysRolePermission row) {
        return MyBatis3Utils.insert(this::insert, row, sysRolePermission, c ->
            c.map(roleId).toProperty("roleId")
            .map(permissionId).toProperty("permissionId")
            .map(tenantId).toProperty("tenantId")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default int insertSelective(SysRolePermission row) {
        return MyBatis3Utils.insert(this::insert, row, sysRolePermission, c ->
            c.map(roleId).toPropertyWhenPresent("roleId", row::getRoleId)
            .map(permissionId).toPropertyWhenPresent("permissionId", row::getPermissionId)
            .map(tenantId).toPropertyWhenPresent("tenantId", row::getTenantId)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default Optional<SysRolePermission> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysRolePermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default List<SysRolePermission> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysRolePermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default List<SysRolePermission> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysRolePermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default Optional<SysRolePermission> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysRolePermission, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    static UpdateDSL<UpdateModel> updateAllColumns(SysRolePermission row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(roleId).equalTo(row::getRoleId)
                .set(permissionId).equalTo(row::getPermissionId)
                .set(tenantId).equalTo(row::getTenantId)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysRolePermission row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(roleId).equalToWhenPresent(row::getRoleId)
                .set(permissionId).equalToWhenPresent(row::getPermissionId)
                .set(tenantId).equalToWhenPresent(row::getTenantId)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default int updateByPrimaryKey(SysRolePermission row) {
        return update(c ->
            c.set(roleId).equalTo(row::getRoleId)
            .set(permissionId).equalTo(row::getPermissionId)
            .set(tenantId).equalTo(row::getTenantId)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role_permission")
    default int updateByPrimaryKeySelective(SysRolePermission row) {
        return update(c ->
            c.set(roleId).equalToWhenPresent(row::getRoleId)
            .set(permissionId).equalToWhenPresent(row::getPermissionId)
            .set(tenantId).equalToWhenPresent(row::getTenantId)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}