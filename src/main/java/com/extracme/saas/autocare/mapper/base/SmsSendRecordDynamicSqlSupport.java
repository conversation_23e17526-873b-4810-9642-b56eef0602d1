package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SmsSendRecordDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    public static final SmsSendRecord smsSendRecord = new SmsSendRecord();

    /**
     * Database Column Remarks:
     *   主键ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sms_send_record.id")
    public static final SqlColumn<Long> id = smsSendRecord.id;

    /**
     * Database Column Remarks:
     *   手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sms_send_record.phone_number")
    public static final SqlColumn<String> phoneNumber = smsSendRecord.phoneNumber;

    /**
     * Database Column Remarks:
     *   IP地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sms_send_record.ip_address")
    public static final SqlColumn<String> ipAddress = smsSendRecord.ipAddress;

    /**
     * Database Column Remarks:
     *   验证码类型：LOGIN-登录
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sms_send_record.type")
    public static final SqlColumn<String> type = smsSendRecord.type;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sms_send_record.create_time")
    public static final SqlColumn<Date> createTime = smsSendRecord.createTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sms_send_record")
    public static final class SmsSendRecord extends AliasableSqlTable<SmsSendRecord> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> phoneNumber = column("phone_number", JDBCType.VARCHAR);

        public final SqlColumn<String> ipAddress = column("ip_address", JDBCType.VARCHAR);

        public final SqlColumn<String> type = column("type", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public SmsSendRecord() {
            super("sms_send_record", SmsSendRecord::new);
        }
    }
}