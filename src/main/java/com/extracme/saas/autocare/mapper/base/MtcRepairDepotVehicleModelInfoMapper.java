package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcRepairDepotVehicleModelInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcRepairDepotVehicleModelInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcRepairDepotVehicleModelInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, repairDepotId, vehicleModelSeq, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcRepairDepotVehicleModelInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcRepairDepotVehicleModelInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="repair_depot_id", property="repairDepotId", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_seq", property="vehicleModelSeq", jdbcType=JdbcType.BIGINT),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcRepairDepotVehicleModelInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcRepairDepotVehicleModelInfoResult")
    Optional<MtcRepairDepotVehicleModelInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcRepairDepotVehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcRepairDepotVehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default int insert(MtcRepairDepotVehicleModelInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairDepotVehicleModelInfo, c ->
            c.map(repairDepotId).toProperty("repairDepotId")
            .map(vehicleModelSeq).toProperty("vehicleModelSeq")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default int insertSelective(MtcRepairDepotVehicleModelInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairDepotVehicleModelInfo, c ->
            c.map(repairDepotId).toPropertyWhenPresent("repairDepotId", row::getRepairDepotId)
            .map(vehicleModelSeq).toPropertyWhenPresent("vehicleModelSeq", row::getVehicleModelSeq)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default Optional<MtcRepairDepotVehicleModelInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcRepairDepotVehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default List<MtcRepairDepotVehicleModelInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcRepairDepotVehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default List<MtcRepairDepotVehicleModelInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcRepairDepotVehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default Optional<MtcRepairDepotVehicleModelInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcRepairDepotVehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcRepairDepotVehicleModelInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(repairDepotId).equalTo(row::getRepairDepotId)
                .set(vehicleModelSeq).equalTo(row::getVehicleModelSeq)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcRepairDepotVehicleModelInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(repairDepotId).equalToWhenPresent(row::getRepairDepotId)
                .set(vehicleModelSeq).equalToWhenPresent(row::getVehicleModelSeq)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default int updateByPrimaryKey(MtcRepairDepotVehicleModelInfo row) {
        return update(c ->
            c.set(repairDepotId).equalTo(row::getRepairDepotId)
            .set(vehicleModelSeq).equalTo(row::getVehicleModelSeq)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    default int updateByPrimaryKeySelective(MtcRepairDepotVehicleModelInfo row) {
        return update(c ->
            c.set(repairDepotId).equalToWhenPresent(row::getRepairDepotId)
            .set(vehicleModelSeq).equalToWhenPresent(row::getVehicleModelSeq)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}