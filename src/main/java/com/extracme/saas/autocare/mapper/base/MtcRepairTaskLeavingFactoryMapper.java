package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcRepairTaskLeavingFactory;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcRepairTaskLeavingFactoryMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNo, vin, name, phoneNumber, deliveryTime, deliveryPictures, remark, repairDepotId, repairDepotName, repairDepotOrgId, repairDepotSapCode, leavingStatus, repairTaskInflowTime, repairTaskReceiveTime, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcRepairTaskLeavingFactory> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcRepairTaskLeavingFactoryResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_no", property="taskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="phone_number", property="phoneNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="delivery_time", property="deliveryTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="delivery_pictures", property="deliveryPictures", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_id", property="repairDepotId", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_name", property="repairDepotName", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_org_id", property="repairDepotOrgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_sap_code", property="repairDepotSapCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="leaving_status", property="leavingStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="repair_task_inflow_time", property="repairTaskInflowTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="repair_task_receive_time", property="repairTaskReceiveTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcRepairTaskLeavingFactory> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcRepairTaskLeavingFactoryResult")
    Optional<MtcRepairTaskLeavingFactory> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcRepairTaskLeavingFactory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcRepairTaskLeavingFactory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default int insert(MtcRepairTaskLeavingFactory row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairTaskLeavingFactory, c ->
            c.map(taskNo).toProperty("taskNo")
            .map(vin).toProperty("vin")
            .map(name).toProperty("name")
            .map(phoneNumber).toProperty("phoneNumber")
            .map(deliveryTime).toProperty("deliveryTime")
            .map(deliveryPictures).toProperty("deliveryPictures")
            .map(remark).toProperty("remark")
            .map(repairDepotId).toProperty("repairDepotId")
            .map(repairDepotName).toProperty("repairDepotName")
            .map(repairDepotOrgId).toProperty("repairDepotOrgId")
            .map(repairDepotSapCode).toProperty("repairDepotSapCode")
            .map(leavingStatus).toProperty("leavingStatus")
            .map(repairTaskInflowTime).toProperty("repairTaskInflowTime")
            .map(repairTaskReceiveTime).toProperty("repairTaskReceiveTime")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default int insertSelective(MtcRepairTaskLeavingFactory row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairTaskLeavingFactory, c ->
            c.map(taskNo).toPropertyWhenPresent("taskNo", row::getTaskNo)
            .map(vin).toPropertyWhenPresent("vin", row::getVin)
            .map(name).toPropertyWhenPresent("name", row::getName)
            .map(phoneNumber).toPropertyWhenPresent("phoneNumber", row::getPhoneNumber)
            .map(deliveryTime).toPropertyWhenPresent("deliveryTime", row::getDeliveryTime)
            .map(deliveryPictures).toPropertyWhenPresent("deliveryPictures", row::getDeliveryPictures)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(repairDepotId).toPropertyWhenPresent("repairDepotId", row::getRepairDepotId)
            .map(repairDepotName).toPropertyWhenPresent("repairDepotName", row::getRepairDepotName)
            .map(repairDepotOrgId).toPropertyWhenPresent("repairDepotOrgId", row::getRepairDepotOrgId)
            .map(repairDepotSapCode).toPropertyWhenPresent("repairDepotSapCode", row::getRepairDepotSapCode)
            .map(leavingStatus).toPropertyWhenPresent("leavingStatus", row::getLeavingStatus)
            .map(repairTaskInflowTime).toPropertyWhenPresent("repairTaskInflowTime", row::getRepairTaskInflowTime)
            .map(repairTaskReceiveTime).toPropertyWhenPresent("repairTaskReceiveTime", row::getRepairTaskReceiveTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default Optional<MtcRepairTaskLeavingFactory> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcRepairTaskLeavingFactory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default List<MtcRepairTaskLeavingFactory> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcRepairTaskLeavingFactory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default List<MtcRepairTaskLeavingFactory> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcRepairTaskLeavingFactory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default Optional<MtcRepairTaskLeavingFactory> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcRepairTaskLeavingFactory, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcRepairTaskLeavingFactory row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalTo(row::getTaskNo)
                .set(vin).equalTo(row::getVin)
                .set(name).equalTo(row::getName)
                .set(phoneNumber).equalTo(row::getPhoneNumber)
                .set(deliveryTime).equalTo(row::getDeliveryTime)
                .set(deliveryPictures).equalTo(row::getDeliveryPictures)
                .set(remark).equalTo(row::getRemark)
                .set(repairDepotId).equalTo(row::getRepairDepotId)
                .set(repairDepotName).equalTo(row::getRepairDepotName)
                .set(repairDepotOrgId).equalTo(row::getRepairDepotOrgId)
                .set(repairDepotSapCode).equalTo(row::getRepairDepotSapCode)
                .set(leavingStatus).equalTo(row::getLeavingStatus)
                .set(repairTaskInflowTime).equalTo(row::getRepairTaskInflowTime)
                .set(repairTaskReceiveTime).equalTo(row::getRepairTaskReceiveTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcRepairTaskLeavingFactory row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(vin).equalToWhenPresent(row::getVin)
                .set(name).equalToWhenPresent(row::getName)
                .set(phoneNumber).equalToWhenPresent(row::getPhoneNumber)
                .set(deliveryTime).equalToWhenPresent(row::getDeliveryTime)
                .set(deliveryPictures).equalToWhenPresent(row::getDeliveryPictures)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(repairDepotId).equalToWhenPresent(row::getRepairDepotId)
                .set(repairDepotName).equalToWhenPresent(row::getRepairDepotName)
                .set(repairDepotOrgId).equalToWhenPresent(row::getRepairDepotOrgId)
                .set(repairDepotSapCode).equalToWhenPresent(row::getRepairDepotSapCode)
                .set(leavingStatus).equalToWhenPresent(row::getLeavingStatus)
                .set(repairTaskInflowTime).equalToWhenPresent(row::getRepairTaskInflowTime)
                .set(repairTaskReceiveTime).equalToWhenPresent(row::getRepairTaskReceiveTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default int updateByPrimaryKey(MtcRepairTaskLeavingFactory row) {
        return update(c ->
            c.set(taskNo).equalTo(row::getTaskNo)
            .set(vin).equalTo(row::getVin)
            .set(name).equalTo(row::getName)
            .set(phoneNumber).equalTo(row::getPhoneNumber)
            .set(deliveryTime).equalTo(row::getDeliveryTime)
            .set(deliveryPictures).equalTo(row::getDeliveryPictures)
            .set(remark).equalTo(row::getRemark)
            .set(repairDepotId).equalTo(row::getRepairDepotId)
            .set(repairDepotName).equalTo(row::getRepairDepotName)
            .set(repairDepotOrgId).equalTo(row::getRepairDepotOrgId)
            .set(repairDepotSapCode).equalTo(row::getRepairDepotSapCode)
            .set(leavingStatus).equalTo(row::getLeavingStatus)
            .set(repairTaskInflowTime).equalTo(row::getRepairTaskInflowTime)
            .set(repairTaskReceiveTime).equalTo(row::getRepairTaskReceiveTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    default int updateByPrimaryKeySelective(MtcRepairTaskLeavingFactory row) {
        return update(c ->
            c.set(taskNo).equalToWhenPresent(row::getTaskNo)
            .set(vin).equalToWhenPresent(row::getVin)
            .set(name).equalToWhenPresent(row::getName)
            .set(phoneNumber).equalToWhenPresent(row::getPhoneNumber)
            .set(deliveryTime).equalToWhenPresent(row::getDeliveryTime)
            .set(deliveryPictures).equalToWhenPresent(row::getDeliveryPictures)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(repairDepotId).equalToWhenPresent(row::getRepairDepotId)
            .set(repairDepotName).equalToWhenPresent(row::getRepairDepotName)
            .set(repairDepotOrgId).equalToWhenPresent(row::getRepairDepotOrgId)
            .set(repairDepotSapCode).equalToWhenPresent(row::getRepairDepotSapCode)
            .set(leavingStatus).equalToWhenPresent(row::getLeavingStatus)
            .set(repairTaskInflowTime).equalToWhenPresent(row::getRepairTaskInflowTime)
            .set(repairTaskReceiveTime).equalToWhenPresent(row::getRepairTaskReceiveTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}