package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SysUserOrgDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user_org")
    public static final SysUserOrg sysUserOrg = new SysUserOrg();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user_org.id")
    public static final SqlColumn<Long> id = sysUserOrg.id;

    /**
     * Database Column Remarks:
     *   用户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user_org.user_id")
    public static final SqlColumn<Long> userId = sysUserOrg.userId;

    /**
     * Database Column Remarks:
     *   组织ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user_org.org_id")
    public static final SqlColumn<String> orgId = sysUserOrg.orgId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user_org")
    public static final class SysUserOrg extends AliasableSqlTable<SysUserOrg> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> userId = column("user_id", JDBCType.BIGINT);

        public final SqlColumn<String> orgId = column("org_id", JDBCType.VARCHAR);

        public SysUserOrg() {
            super("sys_user_org", SysUserOrg::new);
        }
    }
}