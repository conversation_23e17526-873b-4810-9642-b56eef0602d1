package com.extracme.saas.autocare.mapper.extend;

import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.accidentCauseCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.allLoseRemainsSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.allLoseSalvSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.allLoseSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.allLoseTotalSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditAllLoseRemainsSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditAllLoseSalvSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditAllLoseSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditAllLoseTotalSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditDerogationSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditHandlerCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditMateSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditOuterSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditPartSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditRemark;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditRemnantFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditRepiarSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditSalvageFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.auditSelfPaySum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.brandName;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.clmTms;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.createBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.createdTime;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.derogationSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.engineNo;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.engineType;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.enrolDate;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.evalMateSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.evalPartSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.evalRepairSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.evalTypeCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.factoryQualification;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.fuelType;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.groupName;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.handlerCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.insuranceCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.insuranceName;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.manageFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.miscDesc;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.mixCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.modelMatchFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.outerSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.partDiscountPercent;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.plateNo;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.priceType;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.remark;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.remnantFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.repairFacCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.repairFacId;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.repairFacName;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.repairFacPhone;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.repairFacType;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.salvageFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.selfApproveFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.selfConfigFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.selfEstiFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.selfPaySum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.status;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.sumLossAmount;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.taskNo;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.totalManageSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.totalSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.updateBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.updatedTime;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.vehBrandCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.vehCertainCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.vehCertainName;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.vehGroupCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.vehicleOrigin;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.vehicleSettingMode;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.vehicleType;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.vinNo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import org.apache.ibatis.annotations.Mapper;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcLossInfoMapper;
import com.extracme.saas.autocare.model.entity.MtcLossInfo;

@Mapper
@TenantSchema
public interface MtcLossInfoExtendMapper extends MtcLossInfoMapper {

    /**
     * 根据任务编号查询损失信息列表
     * 
     * @param row
     * @return
     */
    default int updateSelectiveByTaskNoAndStatus(MtcLossInfo row) {
        return update(c -> c.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(vehCertainCode).equalToWhenPresent(row::getVehCertainCode)
                .set(vehCertainName).equalToWhenPresent(row::getVehCertainName)
                .set(vehGroupCode).equalToWhenPresent(row::getVehGroupCode)
                .set(groupName).equalToWhenPresent(row::getGroupName)
                .set(vehBrandCode).equalToWhenPresent(row::getVehBrandCode)
                .set(brandName).equalToWhenPresent(row::getBrandName)
                .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
                .set(salvageFee).equalToWhenPresent(row::getSalvageFee)
                .set(remnantFee).equalToWhenPresent(row::getRemnantFee)
                .set(manageFee).equalToWhenPresent(row::getManageFee)
                .set(evalPartSum).equalToWhenPresent(row::getEvalPartSum)
                .set(evalRepairSum).equalToWhenPresent(row::getEvalRepairSum)
                .set(evalMateSum).equalToWhenPresent(row::getEvalMateSum)
                .set(selfPaySum).equalToWhenPresent(row::getSelfPaySum)
                .set(outerSum).equalToWhenPresent(row::getOuterSum)
                .set(derogationSum).equalToWhenPresent(row::getDerogationSum)
                .set(sumLossAmount).equalToWhenPresent(row::getSumLossAmount)
                .set(handlerCode).equalToWhenPresent(row::getHandlerCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(priceType).equalToWhenPresent(row::getPriceType)
                .set(repairFacId).equalToWhenPresent(row::getRepairFacId)
                .set(repairFacCode).equalToWhenPresent(row::getRepairFacCode)
                .set(repairFacType).equalToWhenPresent(row::getRepairFacType)
                .set(factoryQualification).equalToWhenPresent(row::getFactoryQualification)
                .set(repairFacPhone).equalToWhenPresent(row::getRepairFacPhone)
                .set(repairFacName).equalToWhenPresent(row::getRepairFacName)
                .set(vinNo).equalToWhenPresent(row::getVinNo)
                .set(engineNo).equalToWhenPresent(row::getEngineNo)
                .set(plateNo).equalToWhenPresent(row::getPlateNo)
                .set(enrolDate).equalToWhenPresent(row::getEnrolDate)
                .set(selfEstiFlag).equalToWhenPresent(row::getSelfEstiFlag)
                .set(selfApproveFlag).equalToWhenPresent(row::getSelfApproveFlag)
                .set(insuranceCode).equalToWhenPresent(row::getInsuranceCode)
                .set(insuranceName).equalToWhenPresent(row::getInsuranceName)
                .set(mixCode).equalToWhenPresent(row::getMixCode)
                .set(vehicleSettingMode).equalToWhenPresent(row::getVehicleSettingMode)
                .set(modelMatchFlag).equalToWhenPresent(row::getModelMatchFlag)
                .set(evalTypeCode).equalToWhenPresent(row::getEvalTypeCode)
                .set(accidentCauseCode).equalToWhenPresent(row::getAccidentCauseCode)
                .set(clmTms).equalToWhenPresent(row::getClmTms)
                .set(allLoseSum).equalToWhenPresent(row::getAllLoseSum)
                .set(allLoseRemainsSum).equalToWhenPresent(row::getAllLoseRemainsSum)
                .set(allLoseSalvSum).equalToWhenPresent(row::getAllLoseSalvSum)
                .set(allLoseTotalSum).equalToWhenPresent(row::getAllLoseTotalSum)
                .set(partDiscountPercent).equalToWhenPresent(row::getPartDiscountPercent)
                .set(engineType).equalToWhenPresent(row::getEngineType)
                .set(fuelType).equalToWhenPresent(row::getFuelType)
                .set(vehicleOrigin).equalToWhenPresent(row::getVehicleOrigin)
                .set(vehicleType).equalToWhenPresent(row::getVehicleType)
                .set(auditSalvageFee).equalToWhenPresent(row::getAuditSalvageFee)
                .set(auditRemnantFee).equalToWhenPresent(row::getAuditRemnantFee)
                .set(auditPartSum).equalToWhenPresent(row::getAuditPartSum)
                .set(auditRepiarSum).equalToWhenPresent(row::getAuditRepiarSum)
                .set(auditMateSum).equalToWhenPresent(row::getAuditMateSum)
                .set(totalManageSum).equalToWhenPresent(row::getTotalManageSum)
                .set(auditSelfPaySum).equalToWhenPresent(row::getAuditSelfPaySum)
                .set(auditOuterSum).equalToWhenPresent(row::getAuditOuterSum)
                .set(auditDerogationSum).equalToWhenPresent(row::getAuditDerogationSum)
                .set(auditHandlerCode).equalToWhenPresent(row::getAuditHandlerCode)
                .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
                .set(totalSum).equalToWhenPresent(row::getTotalSum)
                .set(auditAllLoseSum).equalToWhenPresent(row::getAuditAllLoseSum)
                .set(auditAllLoseRemainsSum).equalToWhenPresent(row::getAuditAllLoseRemainsSum)
                .set(auditAllLoseSalvSum).equalToWhenPresent(row::getAuditAllLoseSalvSum)
                .set(auditAllLoseTotalSum).equalToWhenPresent(row::getAuditAllLoseTotalSum)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .where(taskNo, isEqualTo(row::getTaskNo))
                .and(status, isEqualTo(1)));
    }
}
