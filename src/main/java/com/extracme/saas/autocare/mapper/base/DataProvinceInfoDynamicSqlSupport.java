package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DataProvinceInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    public static final DataProvinceInfo dataProvinceInfo = new DataProvinceInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.id")
    public static final SqlColumn<Long> id = dataProvinceInfo.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.provinceid")
    public static final SqlColumn<Long> provinceid = dataProvinceInfo.provinceid;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.province")
    public static final SqlColumn<String> province = dataProvinceInfo.province;

    /**
     * Database Column Remarks:
     *   是否常用(0否，1是)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_province_info.in_common_use")
    public static final SqlColumn<Integer> inCommonUse = dataProvinceInfo.inCommonUse;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    public static final class DataProvinceInfo extends AliasableSqlTable<DataProvinceInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> provinceid = column("provinceid", JDBCType.BIGINT);

        public final SqlColumn<String> province = column("province", JDBCType.VARCHAR);

        public final SqlColumn<Integer> inCommonUse = column("in_common_use", JDBCType.INTEGER);

        public DataProvinceInfo() {
            super("data_province_info", DataProvinceInfo::new);
        }
    }
}