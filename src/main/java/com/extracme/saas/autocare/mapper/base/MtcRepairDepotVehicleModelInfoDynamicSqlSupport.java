package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcRepairDepotVehicleModelInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    public static final MtcRepairDepotVehicleModelInfo mtcRepairDepotVehicleModelInfo = new MtcRepairDepotVehicleModelInfo();

    /**
     * Database Column Remarks:
     *   ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.id")
    public static final SqlColumn<Long> id = mtcRepairDepotVehicleModelInfo.id;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.repair_depot_id")
    public static final SqlColumn<String> repairDepotId = mtcRepairDepotVehicleModelInfo.repairDepotId;

    /**
     * Database Column Remarks:
     *   车型SEQ
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.vehicle_model_seq")
    public static final SqlColumn<Long> vehicleModelSeq = mtcRepairDepotVehicleModelInfo.vehicleModelSeq;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.create_by")
    public static final SqlColumn<String> createBy = mtcRepairDepotVehicleModelInfo.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.created_time")
    public static final SqlColumn<Date> createdTime = mtcRepairDepotVehicleModelInfo.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.update_by")
    public static final SqlColumn<String> updateBy = mtcRepairDepotVehicleModelInfo.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_vehicle_model_info.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcRepairDepotVehicleModelInfo.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_vehicle_model_info")
    public static final class MtcRepairDepotVehicleModelInfo extends AliasableSqlTable<MtcRepairDepotVehicleModelInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> repairDepotId = column("repair_depot_id", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelSeq = column("vehicle_model_seq", JDBCType.BIGINT);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcRepairDepotVehicleModelInfo() {
            super("mtc_repair_depot_vehicle_model_info", MtcRepairDepotVehicleModelInfo::new);
        }
    }
}