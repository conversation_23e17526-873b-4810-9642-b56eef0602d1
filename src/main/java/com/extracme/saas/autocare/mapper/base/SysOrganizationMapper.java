package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.SysOrganizationDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.SysOrganization;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysOrganizationMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    BasicColumn[] selectList = BasicColumn.columnList(id, orgName, orgCode, parentId, ancestors, orgType, leader, phone, email, status, sort, tenantId, createdBy, createdTime, updatedBy, updatedTime, deleted);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=true, resultType=Long.class)
    int insert(InsertStatementProvider<SysOrganization> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysOrganizationResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
        @Result(column="org_code", property="orgCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="parent_id", property="parentId", jdbcType=JdbcType.BIGINT),
        @Result(column="ancestors", property="ancestors", jdbcType=JdbcType.VARCHAR),
        @Result(column="org_type", property="orgType", jdbcType=JdbcType.VARCHAR),
        @Result(column="leader", property="leader", jdbcType=JdbcType.VARCHAR),
        @Result(column="phone", property="phone", jdbcType=JdbcType.VARCHAR),
        @Result(column="email", property="email", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.BIT),
        @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.BIGINT),
        @Result(column="created_by", property="createdBy", jdbcType=JdbcType.BIGINT),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.BIGINT),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted", property="deleted", jdbcType=JdbcType.BIT)
    })
    List<SysOrganization> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysOrganizationResult")
    Optional<SysOrganization> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysOrganization, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysOrganization, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default int insert(SysOrganization row) {
        return MyBatis3Utils.insert(this::insert, row, sysOrganization, c ->
            c.map(id).toProperty("id")
            .map(orgName).toProperty("orgName")
            .map(orgCode).toProperty("orgCode")
            .map(parentId).toProperty("parentId")
            .map(ancestors).toProperty("ancestors")
            .map(orgType).toProperty("orgType")
            .map(leader).toProperty("leader")
            .map(phone).toProperty("phone")
            .map(email).toProperty("email")
            .map(status).toProperty("status")
            .map(sort).toProperty("sort")
            .map(tenantId).toProperty("tenantId")
            .map(createdBy).toProperty("createdBy")
            .map(createdTime).toProperty("createdTime")
            .map(updatedBy).toProperty("updatedBy")
            .map(updatedTime).toProperty("updatedTime")
            .map(deleted).toProperty("deleted")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default int insertSelective(SysOrganization row) {
        return MyBatis3Utils.insert(this::insert, row, sysOrganization, c ->
            c.map(id).toProperty("id")
            .map(orgName).toPropertyWhenPresent("orgName", row::getOrgName)
            .map(orgCode).toPropertyWhenPresent("orgCode", row::getOrgCode)
            .map(parentId).toPropertyWhenPresent("parentId", row::getParentId)
            .map(ancestors).toPropertyWhenPresent("ancestors", row::getAncestors)
            .map(orgType).toPropertyWhenPresent("orgType", row::getOrgType)
            .map(leader).toPropertyWhenPresent("leader", row::getLeader)
            .map(phone).toPropertyWhenPresent("phone", row::getPhone)
            .map(email).toPropertyWhenPresent("email", row::getEmail)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(sort).toPropertyWhenPresent("sort", row::getSort)
            .map(tenantId).toPropertyWhenPresent("tenantId", row::getTenantId)
            .map(createdBy).toPropertyWhenPresent("createdBy", row::getCreatedBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", row::getUpdatedBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
            .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default Optional<SysOrganization> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysOrganization, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default List<SysOrganization> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysOrganization, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default List<SysOrganization> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysOrganization, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default Optional<SysOrganization> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysOrganization, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    static UpdateDSL<UpdateModel> updateAllColumns(SysOrganization row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(orgName).equalTo(row::getOrgName)
                .set(orgCode).equalTo(row::getOrgCode)
                .set(parentId).equalTo(row::getParentId)
                .set(ancestors).equalTo(row::getAncestors)
                .set(orgType).equalTo(row::getOrgType)
                .set(leader).equalTo(row::getLeader)
                .set(phone).equalTo(row::getPhone)
                .set(email).equalTo(row::getEmail)
                .set(status).equalTo(row::getStatus)
                .set(sort).equalTo(row::getSort)
                .set(tenantId).equalTo(row::getTenantId)
                .set(createdBy).equalTo(row::getCreatedBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedBy).equalTo(row::getUpdatedBy)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysOrganization row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(orgName).equalToWhenPresent(row::getOrgName)
                .set(orgCode).equalToWhenPresent(row::getOrgCode)
                .set(parentId).equalToWhenPresent(row::getParentId)
                .set(ancestors).equalToWhenPresent(row::getAncestors)
                .set(orgType).equalToWhenPresent(row::getOrgType)
                .set(leader).equalToWhenPresent(row::getLeader)
                .set(phone).equalToWhenPresent(row::getPhone)
                .set(email).equalToWhenPresent(row::getEmail)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(sort).equalToWhenPresent(row::getSort)
                .set(tenantId).equalToWhenPresent(row::getTenantId)
                .set(createdBy).equalToWhenPresent(row::getCreatedBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedBy).equalToWhenPresent(row::getUpdatedBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default int updateByPrimaryKey(SysOrganization row) {
        return update(c ->
            c.set(orgName).equalTo(row::getOrgName)
            .set(orgCode).equalTo(row::getOrgCode)
            .set(parentId).equalTo(row::getParentId)
            .set(ancestors).equalTo(row::getAncestors)
            .set(orgType).equalTo(row::getOrgType)
            .set(leader).equalTo(row::getLeader)
            .set(phone).equalTo(row::getPhone)
            .set(email).equalTo(row::getEmail)
            .set(status).equalTo(row::getStatus)
            .set(sort).equalTo(row::getSort)
            .set(tenantId).equalTo(row::getTenantId)
            .set(createdBy).equalTo(row::getCreatedBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updatedBy).equalTo(row::getUpdatedBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .set(deleted).equalTo(row::getDeleted)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_organization")
    default int updateByPrimaryKeySelective(SysOrganization row) {
        return update(c ->
            c.set(orgName).equalToWhenPresent(row::getOrgName)
            .set(orgCode).equalToWhenPresent(row::getOrgCode)
            .set(parentId).equalToWhenPresent(row::getParentId)
            .set(ancestors).equalToWhenPresent(row::getAncestors)
            .set(orgType).equalToWhenPresent(row::getOrgType)
            .set(leader).equalToWhenPresent(row::getLeader)
            .set(phone).equalToWhenPresent(row::getPhone)
            .set(email).equalToWhenPresent(row::getEmail)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(sort).equalToWhenPresent(row::getSort)
            .set(tenantId).equalToWhenPresent(row::getTenantId)
            .set(createdBy).equalToWhenPresent(row::getCreatedBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updatedBy).equalToWhenPresent(row::getUpdatedBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .set(deleted).equalToWhenPresent(row::getDeleted)
            .where(id, isEqualTo(row::getId))
        );
    }
}