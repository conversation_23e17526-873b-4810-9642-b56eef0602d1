package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcRepairTaskLeavingFactoryDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    public static final MtcRepairTaskLeavingFactory mtcRepairTaskLeavingFactory = new MtcRepairTaskLeavingFactory();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.id")
    public static final SqlColumn<Long> id = mtcRepairTaskLeavingFactory.id;

    /**
     * Database Column Remarks:
     *   关联任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.task_no")
    public static final SqlColumn<String> taskNo = mtcRepairTaskLeavingFactory.taskNo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.vin")
    public static final SqlColumn<String> vin = mtcRepairTaskLeavingFactory.vin;

    /**
     * Database Column Remarks:
     *   提车人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.name")
    public static final SqlColumn<String> name = mtcRepairTaskLeavingFactory.name;

    /**
     * Database Column Remarks:
     *   提车人联系方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.phone_number")
    public static final SqlColumn<String> phoneNumber = mtcRepairTaskLeavingFactory.phoneNumber;

    /**
     * Database Column Remarks:
     *   车辆出厂时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.delivery_time")
    public static final SqlColumn<Date> deliveryTime = mtcRepairTaskLeavingFactory.deliveryTime;

    /**
     * Database Column Remarks:
     *   出厂图片,多个图片逗号分隔
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.delivery_pictures")
    public static final SqlColumn<String> deliveryPictures = mtcRepairTaskLeavingFactory.deliveryPictures;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.remark")
    public static final SqlColumn<String> remark = mtcRepairTaskLeavingFactory.remark;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_id")
    public static final SqlColumn<String> repairDepotId = mtcRepairTaskLeavingFactory.repairDepotId;

    /**
     * Database Column Remarks:
     *   修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_name")
    public static final SqlColumn<String> repairDepotName = mtcRepairTaskLeavingFactory.repairDepotName;

    /**
     * Database Column Remarks:
     *   修理厂组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_org_id")
    public static final SqlColumn<String> repairDepotOrgId = mtcRepairTaskLeavingFactory.repairDepotOrgId;

    /**
     * Database Column Remarks:
     *   供应商sap编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_sap_code")
    public static final SqlColumn<String> repairDepotSapCode = mtcRepairTaskLeavingFactory.repairDepotSapCode;

    /**
     * Database Column Remarks:
     *   维修厂登记状态 1-已登记 2-未登记 3-已关闭
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.leaving_status")
    public static final SqlColumn<Integer> leavingStatus = mtcRepairTaskLeavingFactory.leavingStatus;

    /**
     * Database Column Remarks:
     *   维修任务流入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_task_inflow_time")
    public static final SqlColumn<Date> repairTaskInflowTime = mtcRepairTaskLeavingFactory.repairTaskInflowTime;

    /**
     * Database Column Remarks:
     *   维修任务接车时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_task_receive_time")
    public static final SqlColumn<Date> repairTaskReceiveTime = mtcRepairTaskLeavingFactory.repairTaskReceiveTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.create_by")
    public static final SqlColumn<String> createBy = mtcRepairTaskLeavingFactory.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.created_time")
    public static final SqlColumn<Date> createdTime = mtcRepairTaskLeavingFactory.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.update_by")
    public static final SqlColumn<String> updateBy = mtcRepairTaskLeavingFactory.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcRepairTaskLeavingFactory.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    public static final class MtcRepairTaskLeavingFactory extends AliasableSqlTable<MtcRepairTaskLeavingFactory> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);

        public final SqlColumn<String> phoneNumber = column("phone_number", JDBCType.VARCHAR);

        public final SqlColumn<Date> deliveryTime = column("delivery_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> deliveryPictures = column("delivery_pictures", JDBCType.VARCHAR);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotId = column("repair_depot_id", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotName = column("repair_depot_name", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotOrgId = column("repair_depot_org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotSapCode = column("repair_depot_sap_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> leavingStatus = column("leaving_status", JDBCType.INTEGER);

        public final SqlColumn<Date> repairTaskInflowTime = column("repair_task_inflow_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> repairTaskReceiveTime = column("repair_task_receive_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcRepairTaskLeavingFactory() {
            super("mtc_repair_task_leaving_factory", MtcRepairTaskLeavingFactory::new);
        }
    }
}