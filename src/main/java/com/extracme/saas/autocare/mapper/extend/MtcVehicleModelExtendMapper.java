package com.extracme.saas.autocare.mapper.extend;

import org.apache.ibatis.annotations.Mapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcVehicleModelMapper;
import com.extracme.saas.autocare.model.entity.MtcVehicleModel;

import static com.extracme.saas.autocare.mapper.base.MtcVehicleModelDynamicSqlSupport.*;

import javax.annotation.Generated;

/**
 * 车型信息扩展Mapper
 */
@Mapper
@TenantSchema // 可以指定某些方法不使用租户Schema
public interface MtcVehicleModelExtendMapper extends MtcVehicleModelMapper {

    /**
     * 插入车型信息，包含ID字段
     * 用于数据同步时指定ID插入
     */
    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: mtc_vehicle_model")
    default int insertSelectiveWithId(MtcVehicleModel row) {
        return MyBatis3Utils.insert(this::insert, row, mtcVehicleModel,
                c -> c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(vehicleModelName).toPropertyWhenPresent("vehicleModelName", row::getVehicleModelName)
                        .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
                        .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
                        .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
                        .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
        );
    }
}
