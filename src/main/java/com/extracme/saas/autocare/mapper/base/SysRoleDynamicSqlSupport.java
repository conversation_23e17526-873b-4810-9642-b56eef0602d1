package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SysRoleDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role")
    public static final SysRole sysRole = new SysRole();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.id")
    public static final SqlColumn<Long> id = sysRole.id;

    /**
     * Database Column Remarks:
     *   角色名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.role_name")
    public static final SqlColumn<String> roleName = sysRole.roleName;

    /**
     * Database Column Remarks:
     *   角色编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.role_code")
    public static final SqlColumn<String> roleCode = sysRole.roleCode;

    /**
     * Database Column Remarks:
     *   角色类型：1-超级管理员，2-普通管理员，3-普通用户
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.role_type")
    public static final SqlColumn<Integer> roleType = sysRole.roleType;

    /**
     * Database Column Remarks:
     *   角色描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.description")
    public static final SqlColumn<String> description = sysRole.description;

    /**
     * Database Column Remarks:
     *   状态：0-禁用，1-启用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.status")
    public static final SqlColumn<Integer> status = sysRole.status;

    /**
     * Database Column Remarks:
     *   租户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.tenant_id")
    public static final SqlColumn<Long> tenantId = sysRole.tenantId;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.create_by")
    public static final SqlColumn<String> createBy = sysRole.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.created_time")
    public static final SqlColumn<Date> createdTime = sysRole.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.update_by")
    public static final SqlColumn<String> updateBy = sysRole.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_role.updated_time")
    public static final SqlColumn<Date> updatedTime = sysRole.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_role")
    public static final class SysRole extends AliasableSqlTable<SysRole> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> roleName = column("role_name", JDBCType.VARCHAR);

        public final SqlColumn<String> roleCode = column("role_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> roleType = column("role_type", JDBCType.INTEGER);

        public final SqlColumn<String> description = column("description", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<Long> tenantId = column("tenant_id", JDBCType.BIGINT);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public SysRole() {
            super("sys_role", SysRole::new);
        }
    }
}