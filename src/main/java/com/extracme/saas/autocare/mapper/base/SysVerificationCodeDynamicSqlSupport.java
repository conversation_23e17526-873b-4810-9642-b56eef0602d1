package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SysVerificationCodeDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    public static final SysVerificationCode sysVerificationCode = new SysVerificationCode();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_verification_code.id")
    public static final SqlColumn<Long> id = sysVerificationCode.id;

    /**
     * Database Column Remarks:
     *   手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_verification_code.mobile")
    public static final SqlColumn<String> mobile = sysVerificationCode.mobile;

    /**
     * Database Column Remarks:
     *   验证码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_verification_code.code")
    public static final SqlColumn<String> code = sysVerificationCode.code;

    /**
     * Database Column Remarks:
     *   验证码类型:LOGIN-登录,REGISTER-注册
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_verification_code.type")
    public static final SqlColumn<String> type = sysVerificationCode.type;

    /**
     * Database Column Remarks:
     *   失败次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_verification_code.fail_count")
    public static final SqlColumn<Integer> failCount = sysVerificationCode.failCount;

    /**
     * Database Column Remarks:
     *   状态：0-未使用，1-已使用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_verification_code.status")
    public static final SqlColumn<Integer> status = sysVerificationCode.status;

    /**
     * Database Column Remarks:
     *   过期时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_verification_code.expire_time")
    public static final SqlColumn<Date> expireTime = sysVerificationCode.expireTime;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_verification_code.created_time")
    public static final SqlColumn<Date> createdTime = sysVerificationCode.createdTime;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_verification_code.update_time")
    public static final SqlColumn<Date> updateTime = sysVerificationCode.updateTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_verification_code")
    public static final class SysVerificationCode extends AliasableSqlTable<SysVerificationCode> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> mobile = column("mobile", JDBCType.VARCHAR);

        public final SqlColumn<String> code = column("code", JDBCType.VARCHAR);

        public final SqlColumn<String> type = column("type", JDBCType.VARCHAR);

        public final SqlColumn<Integer> failCount = column("fail_count", JDBCType.INTEGER);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<Date> expireTime = column("expire_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public SysVerificationCode() {
            super("sys_verification_code", SysVerificationCode::new);
        }
    }
}