package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcVehicleInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcVehicleInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, vehicleNo, vehicleModelId, vehicleOrgId, operationOrgId, productLine, subProductLine, factOperateTag, propertyStatus, totalMileage, engineId, registerDate, tciStartdate, tciEnddate, insuranceBelongs, insuranceCompanyName, createTime, createBy, updateTime, updateBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcVehicleInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcVehicleInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_no", property="vehicleNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_org_id", property="vehicleOrgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="operation_org_id", property="operationOrgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
        @Result(column="sub_product_line", property="subProductLine", jdbcType=JdbcType.INTEGER),
        @Result(column="fact_operate_tag", property="factOperateTag", jdbcType=JdbcType.INTEGER),
        @Result(column="property_status", property="propertyStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="total_mileage", property="totalMileage", jdbcType=JdbcType.INTEGER),
        @Result(column="engine_id", property="engineId", jdbcType=JdbcType.VARCHAR),
        @Result(column="register_date", property="registerDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="tci_startdate", property="tciStartdate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="tci_enddate", property="tciEnddate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="insurance_belongs", property="insuranceBelongs", jdbcType=JdbcType.BIGINT),
        @Result(column="insurance_company_name", property="insuranceCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR)
    })
    List<MtcVehicleInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcVehicleInfoResult")
    Optional<MtcVehicleInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcVehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcVehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default int insert(MtcVehicleInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcVehicleInfo, c ->
            c.map(vin).toProperty("vin")
            .map(vehicleNo).toProperty("vehicleNo")
            .map(vehicleModelId).toProperty("vehicleModelId")
            .map(vehicleOrgId).toProperty("vehicleOrgId")
            .map(operationOrgId).toProperty("operationOrgId")
            .map(productLine).toProperty("productLine")
            .map(subProductLine).toProperty("subProductLine")
            .map(factOperateTag).toProperty("factOperateTag")
            .map(propertyStatus).toProperty("propertyStatus")
            .map(totalMileage).toProperty("totalMileage")
            .map(engineId).toProperty("engineId")
            .map(registerDate).toProperty("registerDate")
            .map(tciStartdate).toProperty("tciStartdate")
            .map(tciEnddate).toProperty("tciEnddate")
            .map(insuranceBelongs).toProperty("insuranceBelongs")
            .map(insuranceCompanyName).toProperty("insuranceCompanyName")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default int insertSelective(MtcVehicleInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcVehicleInfo, c ->
            c.map(vin).toPropertyWhenPresent("vin", row::getVin)
            .map(vehicleNo).toPropertyWhenPresent("vehicleNo", row::getVehicleNo)
            .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", row::getVehicleModelId)
            .map(vehicleOrgId).toPropertyWhenPresent("vehicleOrgId", row::getVehicleOrgId)
            .map(operationOrgId).toPropertyWhenPresent("operationOrgId", row::getOperationOrgId)
            .map(productLine).toPropertyWhenPresent("productLine", row::getProductLine)
            .map(subProductLine).toPropertyWhenPresent("subProductLine", row::getSubProductLine)
            .map(factOperateTag).toPropertyWhenPresent("factOperateTag", row::getFactOperateTag)
            .map(propertyStatus).toPropertyWhenPresent("propertyStatus", row::getPropertyStatus)
            .map(totalMileage).toPropertyWhenPresent("totalMileage", row::getTotalMileage)
            .map(engineId).toPropertyWhenPresent("engineId", row::getEngineId)
            .map(registerDate).toPropertyWhenPresent("registerDate", row::getRegisterDate)
            .map(tciStartdate).toPropertyWhenPresent("tciStartdate", row::getTciStartdate)
            .map(tciEnddate).toPropertyWhenPresent("tciEnddate", row::getTciEnddate)
            .map(insuranceBelongs).toPropertyWhenPresent("insuranceBelongs", row::getInsuranceBelongs)
            .map(insuranceCompanyName).toPropertyWhenPresent("insuranceCompanyName", row::getInsuranceCompanyName)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default Optional<MtcVehicleInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcVehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default List<MtcVehicleInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcVehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default List<MtcVehicleInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcVehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default Optional<MtcVehicleInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcVehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcVehicleInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(row::getVin)
                .set(vehicleNo).equalTo(row::getVehicleNo)
                .set(vehicleModelId).equalTo(row::getVehicleModelId)
                .set(vehicleOrgId).equalTo(row::getVehicleOrgId)
                .set(operationOrgId).equalTo(row::getOperationOrgId)
                .set(productLine).equalTo(row::getProductLine)
                .set(subProductLine).equalTo(row::getSubProductLine)
                .set(factOperateTag).equalTo(row::getFactOperateTag)
                .set(propertyStatus).equalTo(row::getPropertyStatus)
                .set(totalMileage).equalTo(row::getTotalMileage)
                .set(engineId).equalTo(row::getEngineId)
                .set(registerDate).equalTo(row::getRegisterDate)
                .set(tciStartdate).equalTo(row::getTciStartdate)
                .set(tciEnddate).equalTo(row::getTciEnddate)
                .set(insuranceBelongs).equalTo(row::getInsuranceBelongs)
                .set(insuranceCompanyName).equalTo(row::getInsuranceCompanyName)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcVehicleInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(row::getVin)
                .set(vehicleNo).equalToWhenPresent(row::getVehicleNo)
                .set(vehicleModelId).equalToWhenPresent(row::getVehicleModelId)
                .set(vehicleOrgId).equalToWhenPresent(row::getVehicleOrgId)
                .set(operationOrgId).equalToWhenPresent(row::getOperationOrgId)
                .set(productLine).equalToWhenPresent(row::getProductLine)
                .set(subProductLine).equalToWhenPresent(row::getSubProductLine)
                .set(factOperateTag).equalToWhenPresent(row::getFactOperateTag)
                .set(propertyStatus).equalToWhenPresent(row::getPropertyStatus)
                .set(totalMileage).equalToWhenPresent(row::getTotalMileage)
                .set(engineId).equalToWhenPresent(row::getEngineId)
                .set(registerDate).equalToWhenPresent(row::getRegisterDate)
                .set(tciStartdate).equalToWhenPresent(row::getTciStartdate)
                .set(tciEnddate).equalToWhenPresent(row::getTciEnddate)
                .set(insuranceBelongs).equalToWhenPresent(row::getInsuranceBelongs)
                .set(insuranceCompanyName).equalToWhenPresent(row::getInsuranceCompanyName)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default int updateByPrimaryKey(MtcVehicleInfo row) {
        return update(c ->
            c.set(vin).equalTo(row::getVin)
            .set(vehicleNo).equalTo(row::getVehicleNo)
            .set(vehicleModelId).equalTo(row::getVehicleModelId)
            .set(vehicleOrgId).equalTo(row::getVehicleOrgId)
            .set(operationOrgId).equalTo(row::getOperationOrgId)
            .set(productLine).equalTo(row::getProductLine)
            .set(subProductLine).equalTo(row::getSubProductLine)
            .set(factOperateTag).equalTo(row::getFactOperateTag)
            .set(propertyStatus).equalTo(row::getPropertyStatus)
            .set(totalMileage).equalTo(row::getTotalMileage)
            .set(engineId).equalTo(row::getEngineId)
            .set(registerDate).equalTo(row::getRegisterDate)
            .set(tciStartdate).equalTo(row::getTciStartdate)
            .set(tciEnddate).equalTo(row::getTciEnddate)
            .set(insuranceBelongs).equalTo(row::getInsuranceBelongs)
            .set(insuranceCompanyName).equalTo(row::getInsuranceCompanyName)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    default int updateByPrimaryKeySelective(MtcVehicleInfo row) {
        return update(c ->
            c.set(vin).equalToWhenPresent(row::getVin)
            .set(vehicleNo).equalToWhenPresent(row::getVehicleNo)
            .set(vehicleModelId).equalToWhenPresent(row::getVehicleModelId)
            .set(vehicleOrgId).equalToWhenPresent(row::getVehicleOrgId)
            .set(operationOrgId).equalToWhenPresent(row::getOperationOrgId)
            .set(productLine).equalToWhenPresent(row::getProductLine)
            .set(subProductLine).equalToWhenPresent(row::getSubProductLine)
            .set(factOperateTag).equalToWhenPresent(row::getFactOperateTag)
            .set(propertyStatus).equalToWhenPresent(row::getPropertyStatus)
            .set(totalMileage).equalToWhenPresent(row::getTotalMileage)
            .set(engineId).equalToWhenPresent(row::getEngineId)
            .set(registerDate).equalToWhenPresent(row::getRegisterDate)
            .set(tciStartdate).equalToWhenPresent(row::getTciStartdate)
            .set(tciEnddate).equalToWhenPresent(row::getTciEnddate)
            .set(insuranceBelongs).equalToWhenPresent(row::getInsuranceBelongs)
            .set(insuranceCompanyName).equalToWhenPresent(row::getInsuranceCompanyName)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }
}