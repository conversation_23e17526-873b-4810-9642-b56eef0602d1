package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcRepairDepotInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    public static final MtcRepairDepotInfo mtcRepairDepotInfo = new MtcRepairDepotInfo();

    /**
     * Database Column Remarks:
     *   ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.id")
    public static final SqlColumn<Long> id = mtcRepairDepotInfo.id;

    /**
     * Database Column Remarks:
     *   单点用户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_user_id")
    public static final SqlColumn<Long> ssoUserId = mtcRepairDepotInfo.ssoUserId;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_id")
    public static final SqlColumn<String> repairDepotId = mtcRepairDepotInfo.repairDepotId;

    /**
     * Database Column Remarks:
     *   维修厂sap编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_sap_code")
    public static final SqlColumn<String> repairDepotSapCode = mtcRepairDepotInfo.repairDepotSapCode;

    /**
     * Database Column Remarks:
     *   修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_name")
    public static final SqlColumn<String> repairDepotName = mtcRepairDepotInfo.repairDepotName;

    /**
     * Database Column Remarks:
     *   维修厂所属组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_org_id")
    public static final SqlColumn<String> repairDepotOrgId = mtcRepairDepotInfo.repairDepotOrgId;

    /**
     * Database Column Remarks:
     *   修理厂账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_account")
    public static final SqlColumn<String> repairDepotAccount = mtcRepairDepotInfo.repairDepotAccount;

    /**
     * Database Column Remarks:
     *   修理厂等级
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_grade")
    public static final SqlColumn<String> repairDepotGrade = mtcRepairDepotInfo.repairDepotGrade;

    /**
     * Database Column Remarks:
     *   修理厂类型(1:合作修理厂 2:非合作修理厂)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_type")
    public static final SqlColumn<Integer> repairDepotType = mtcRepairDepotInfo.repairDepotType;

    /**
     * Database Column Remarks:
     *   是否对外展示(1:展示 2:不展示)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.is_show")
    public static final SqlColumn<Integer> isShow = mtcRepairDepotInfo.isShow;

    /**
     * Database Column Remarks:
     *   是否保养点(0:否 1:是)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_point")
    public static final SqlColumn<Integer> maintenancePoint = mtcRepairDepotInfo.maintenancePoint;

    /**
     * Database Column Remarks:
     *   修理厂地址（省）ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.province_id")
    public static final SqlColumn<Long> provinceId = mtcRepairDepotInfo.provinceId;

    /**
     * Database Column Remarks:
     *   修理厂地址（市）ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.city_id")
    public static final SqlColumn<Long> cityId = mtcRepairDepotInfo.cityId;

    /**
     * Database Column Remarks:
     *   修理厂地址（区）ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.area_id")
    public static final SqlColumn<Long> areaId = mtcRepairDepotInfo.areaId;

    /**
     * Database Column Remarks:
     *   修理厂详细地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.address")
    public static final SqlColumn<String> address = mtcRepairDepotInfo.address;

    /**
     * Database Column Remarks:
     *   税率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.tax_rate")
    public static final SqlColumn<String> taxRate = mtcRepairDepotInfo.taxRate;

    /**
     * Database Column Remarks:
     *   负责人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.linkman_name")
    public static final SqlColumn<String> linkmanName = mtcRepairDepotInfo.linkmanName;

    /**
     * Database Column Remarks:
     *   合作模式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.cooperation_mode")
    public static final SqlColumn<String> cooperationMode = mtcRepairDepotInfo.cooperationMode;

    /**
     * Database Column Remarks:
     *   事故联系人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.accident_contacts")
    public static final SqlColumn<String> accidentContacts = mtcRepairDepotInfo.accidentContacts;

    /**
     * Database Column Remarks:
     *   事故联系电话
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.accident_tel")
    public static final SqlColumn<String> accidentTel = mtcRepairDepotInfo.accidentTel;

    /**
     * Database Column Remarks:
     *   维保联系人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_contacts")
    public static final SqlColumn<String> maintenanceContacts = mtcRepairDepotInfo.maintenanceContacts;

    /**
     * Database Column Remarks:
     *   维保联系电话
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_tel")
    public static final SqlColumn<String> maintenanceTel = mtcRepairDepotInfo.maintenanceTel;

    /**
     * Database Column Remarks:
     *   修理厂经度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_longitude")
    public static final SqlColumn<BigDecimal> repairDepotLongitude = mtcRepairDepotInfo.repairDepotLongitude;

    /**
     * Database Column Remarks:
     *   修理厂纬度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_latitude")
    public static final SqlColumn<BigDecimal> repairDepotLatitude = mtcRepairDepotInfo.repairDepotLatitude;

    /**
     * Database Column Remarks:
     *   车型是否全选标记(0:没有全选 1:全部选中)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.vehicle_model_all_flag")
    public static final SqlColumn<Integer> vehicleModelAllFlag = mtcRepairDepotInfo.vehicleModelAllFlag;

    /**
     * Database Column Remarks:
     *   单点用户创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_create_time")
    public static final SqlColumn<Date> ssoCreateTime = mtcRepairDepotInfo.ssoCreateTime;

    /**
     * Database Column Remarks:
     *   单点用户修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_update_time")
    public static final SqlColumn<Date> ssoUpdateTime = mtcRepairDepotInfo.ssoUpdateTime;

    /**
     * Database Column Remarks:
     *   负责人手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.old_repair_factory_code")
    public static final SqlColumn<String> oldRepairFactoryCode = mtcRepairDepotInfo.oldRepairFactoryCode;

    /**
     * Database Column Remarks:
     *   状态(1:有效 0:无效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.status")
    public static final SqlColumn<Integer> status = mtcRepairDepotInfo.status;

    /**
     * Database Column Remarks:
     *   逻辑删除状态(1:已删除 0:未删除)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.del_flag")
    public static final SqlColumn<Integer> delFlag = mtcRepairDepotInfo.delFlag;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.remark")
    public static final SqlColumn<String> remark = mtcRepairDepotInfo.remark;

    /**
     * Database Column Remarks:
     *   单点同步标志(1:从单点同步 2:手动从老平台移植)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_flag")
    public static final SqlColumn<Integer> ssoFlag = mtcRepairDepotInfo.ssoFlag;

    /**
     * Database Column Remarks:
     *   可修类目(0:全部 1:外观 2:易损件)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.can_repair_item")
    public static final SqlColumn<Integer> canRepairItem = mtcRepairDepotInfo.canRepairItem;

    /**
     * Database Column Remarks:
     *   是否保修点(0:否 1:是)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.warranty_point")
    public static final SqlColumn<Integer> warrantyPoint = mtcRepairDepotInfo.warrantyPoint;

    /**
     * Database Column Remarks:
     *   营业时间设定(0:全天 1:其他)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_type")
    public static final SqlColumn<Integer> businessType = mtcRepairDepotInfo.businessType;

    /**
     * Database Column Remarks:
     *   营业开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_start_time")
    public static final SqlColumn<String> businessStartTime = mtcRepairDepotInfo.businessStartTime;

    /**
     * Database Column Remarks:
     *   营业结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_end_time")
    public static final SqlColumn<String> businessEndTime = mtcRepairDepotInfo.businessEndTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.create_by")
    public static final SqlColumn<String> createBy = mtcRepairDepotInfo.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.created_time")
    public static final SqlColumn<Date> createdTime = mtcRepairDepotInfo.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.update_by")
    public static final SqlColumn<String> updateBy = mtcRepairDepotInfo.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcRepairDepotInfo.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    public static final class MtcRepairDepotInfo extends AliasableSqlTable<MtcRepairDepotInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> ssoUserId = column("sso_user_id", JDBCType.BIGINT);

        public final SqlColumn<String> repairDepotId = column("repair_depot_id", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotSapCode = column("repair_depot_sap_code", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotName = column("repair_depot_name", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotOrgId = column("repair_depot_org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotAccount = column("repair_depot_account", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotGrade = column("repair_depot_grade", JDBCType.VARCHAR);

        public final SqlColumn<Integer> repairDepotType = column("repair_depot_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> isShow = column("is_show", JDBCType.INTEGER);

        public final SqlColumn<Integer> maintenancePoint = column("maintenance_point", JDBCType.INTEGER);

        public final SqlColumn<Long> provinceId = column("province_id", JDBCType.BIGINT);

        public final SqlColumn<Long> cityId = column("city_id", JDBCType.BIGINT);

        public final SqlColumn<Long> areaId = column("area_id", JDBCType.BIGINT);

        public final SqlColumn<String> address = column("address", JDBCType.VARCHAR);

        public final SqlColumn<String> taxRate = column("tax_rate", JDBCType.VARCHAR);

        public final SqlColumn<String> linkmanName = column("linkman_name", JDBCType.VARCHAR);

        public final SqlColumn<String> cooperationMode = column("cooperation_mode", JDBCType.VARCHAR);

        public final SqlColumn<String> accidentContacts = column("accident_contacts", JDBCType.VARCHAR);

        public final SqlColumn<String> accidentTel = column("accident_tel", JDBCType.VARCHAR);

        public final SqlColumn<String> maintenanceContacts = column("maintenance_contacts", JDBCType.VARCHAR);

        public final SqlColumn<String> maintenanceTel = column("maintenance_tel", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> repairDepotLongitude = column("repair_depot_longitude", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> repairDepotLatitude = column("repair_depot_latitude", JDBCType.DECIMAL);

        public final SqlColumn<Integer> vehicleModelAllFlag = column("vehicle_model_all_flag", JDBCType.INTEGER);

        public final SqlColumn<Date> ssoCreateTime = column("sso_create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> ssoUpdateTime = column("sso_update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> oldRepairFactoryCode = column("old_repair_factory_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<Integer> delFlag = column("del_flag", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> ssoFlag = column("sso_flag", JDBCType.INTEGER);

        public final SqlColumn<Integer> canRepairItem = column("can_repair_item", JDBCType.INTEGER);

        public final SqlColumn<Integer> warrantyPoint = column("warranty_point", JDBCType.INTEGER);

        public final SqlColumn<Integer> businessType = column("business_type", JDBCType.INTEGER);

        public final SqlColumn<String> businessStartTime = column("business_start_time", JDBCType.VARCHAR);

        public final SqlColumn<String> businessEndTime = column("business_end_time", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcRepairDepotInfo() {
            super("mtc_repair_depot_info", MtcRepairDepotInfo::new);
        }
    }
}