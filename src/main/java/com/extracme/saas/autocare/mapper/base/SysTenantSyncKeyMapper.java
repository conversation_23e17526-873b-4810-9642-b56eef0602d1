package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.SysTenantSyncKeyDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.SysTenantSyncKey;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface SysTenantSyncKeyMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    BasicColumn[] selectList = BasicColumn.columnList(id, tenantId, tenantCode, syncKey, encryptionKey, keyStatus, keyGenerateTime, keyExpireTime, lastUsedTime, usageCount, remark, createTime, createBy, updateTime, updateBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<SysTenantSyncKey> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SysTenantSyncKeyResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.BIGINT),
        @Result(column="tenant_code", property="tenantCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="sync_key", property="syncKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="encryption_key", property="encryptionKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="key_status", property="keyStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="key_generate_time", property="keyGenerateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="key_expire_time", property="keyExpireTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="last_used_time", property="lastUsedTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="usage_count", property="usageCount", jdbcType=JdbcType.BIGINT),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR)
    })
    List<SysTenantSyncKey> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("SysTenantSyncKeyResult")
    Optional<SysTenantSyncKey> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, sysTenantSyncKey, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, sysTenantSyncKey, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default int insert(SysTenantSyncKey row) {
        return MyBatis3Utils.insert(this::insert, row, sysTenantSyncKey, c ->
            c.map(tenantId).toProperty("tenantId")
            .map(tenantCode).toProperty("tenantCode")
            .map(syncKey).toProperty("syncKey")
            .map(encryptionKey).toProperty("encryptionKey")
            .map(keyStatus).toProperty("keyStatus")
            .map(keyGenerateTime).toProperty("keyGenerateTime")
            .map(keyExpireTime).toProperty("keyExpireTime")
            .map(lastUsedTime).toProperty("lastUsedTime")
            .map(usageCount).toProperty("usageCount")
            .map(remark).toProperty("remark")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default int insertSelective(SysTenantSyncKey row) {
        return MyBatis3Utils.insert(this::insert, row, sysTenantSyncKey, c ->
            c.map(tenantId).toPropertyWhenPresent("tenantId", row::getTenantId)
            .map(tenantCode).toPropertyWhenPresent("tenantCode", row::getTenantCode)
            .map(syncKey).toPropertyWhenPresent("syncKey", row::getSyncKey)
            .map(encryptionKey).toPropertyWhenPresent("encryptionKey", row::getEncryptionKey)
            .map(keyStatus).toPropertyWhenPresent("keyStatus", row::getKeyStatus)
            .map(keyGenerateTime).toPropertyWhenPresent("keyGenerateTime", row::getKeyGenerateTime)
            .map(keyExpireTime).toPropertyWhenPresent("keyExpireTime", row::getKeyExpireTime)
            .map(lastUsedTime).toPropertyWhenPresent("lastUsedTime", row::getLastUsedTime)
            .map(usageCount).toPropertyWhenPresent("usageCount", row::getUsageCount)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default Optional<SysTenantSyncKey> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, sysTenantSyncKey, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default List<SysTenantSyncKey> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, sysTenantSyncKey, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default List<SysTenantSyncKey> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, sysTenantSyncKey, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default Optional<SysTenantSyncKey> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, sysTenantSyncKey, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    static UpdateDSL<UpdateModel> updateAllColumns(SysTenantSyncKey row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(tenantId).equalTo(row::getTenantId)
                .set(tenantCode).equalTo(row::getTenantCode)
                .set(syncKey).equalTo(row::getSyncKey)
                .set(encryptionKey).equalTo(row::getEncryptionKey)
                .set(keyStatus).equalTo(row::getKeyStatus)
                .set(keyGenerateTime).equalTo(row::getKeyGenerateTime)
                .set(keyExpireTime).equalTo(row::getKeyExpireTime)
                .set(lastUsedTime).equalTo(row::getLastUsedTime)
                .set(usageCount).equalTo(row::getUsageCount)
                .set(remark).equalTo(row::getRemark)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SysTenantSyncKey row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(tenantId).equalToWhenPresent(row::getTenantId)
                .set(tenantCode).equalToWhenPresent(row::getTenantCode)
                .set(syncKey).equalToWhenPresent(row::getSyncKey)
                .set(encryptionKey).equalToWhenPresent(row::getEncryptionKey)
                .set(keyStatus).equalToWhenPresent(row::getKeyStatus)
                .set(keyGenerateTime).equalToWhenPresent(row::getKeyGenerateTime)
                .set(keyExpireTime).equalToWhenPresent(row::getKeyExpireTime)
                .set(lastUsedTime).equalToWhenPresent(row::getLastUsedTime)
                .set(usageCount).equalToWhenPresent(row::getUsageCount)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default int updateByPrimaryKey(SysTenantSyncKey row) {
        return update(c ->
            c.set(tenantId).equalTo(row::getTenantId)
            .set(tenantCode).equalTo(row::getTenantCode)
            .set(syncKey).equalTo(row::getSyncKey)
            .set(encryptionKey).equalTo(row::getEncryptionKey)
            .set(keyStatus).equalTo(row::getKeyStatus)
            .set(keyGenerateTime).equalTo(row::getKeyGenerateTime)
            .set(keyExpireTime).equalTo(row::getKeyExpireTime)
            .set(lastUsedTime).equalTo(row::getLastUsedTime)
            .set(usageCount).equalTo(row::getUsageCount)
            .set(remark).equalTo(row::getRemark)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_tenant_sync_key")
    default int updateByPrimaryKeySelective(SysTenantSyncKey row) {
        return update(c ->
            c.set(tenantId).equalToWhenPresent(row::getTenantId)
            .set(tenantCode).equalToWhenPresent(row::getTenantCode)
            .set(syncKey).equalToWhenPresent(row::getSyncKey)
            .set(encryptionKey).equalToWhenPresent(row::getEncryptionKey)
            .set(keyStatus).equalToWhenPresent(row::getKeyStatus)
            .set(keyGenerateTime).equalToWhenPresent(row::getKeyGenerateTime)
            .set(keyExpireTime).equalToWhenPresent(row::getKeyExpireTime)
            .set(lastUsedTime).equalToWhenPresent(row::getLastUsedTime)
            .set(usageCount).equalToWhenPresent(row::getUsageCount)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }
}