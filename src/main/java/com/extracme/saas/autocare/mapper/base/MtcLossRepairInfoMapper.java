package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcLossRepairInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcLossRepairInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNo, repairId, repairModeCode, itemName, manpowerFee, manpowerRefFee, selfConfigFlag, itemCoverCode, remark, clmTms, evalHour, repairUnitPrice, repairLevelCode, repairLevelName, auditManpowerFee, apprHour, checkState, auditRemark, status, miscDesc, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcLossRepairInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcLossRepairInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_no", property="taskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_id", property="repairId", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_mode_code", property="repairModeCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
        @Result(column="manpower_fee", property="manpowerFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="manpower_ref_fee", property="manpowerRefFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="self_config_flag", property="selfConfigFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="item_cover_code", property="itemCoverCode", jdbcType=JdbcType.INTEGER),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="clm_tms", property="clmTms", jdbcType=JdbcType.VARCHAR),
        @Result(column="eval_hour", property="evalHour", jdbcType=JdbcType.DECIMAL),
        @Result(column="repair_unit_price", property="repairUnitPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="repair_level_code", property="repairLevelCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_level_name", property="repairLevelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_manpower_fee", property="auditManpowerFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="appr_hour", property="apprHour", jdbcType=JdbcType.DECIMAL),
        @Result(column="check_state", property="checkState", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_remark", property="auditRemark", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_Desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcLossRepairInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcLossRepairInfoResult")
    Optional<MtcLossRepairInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcLossRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcLossRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default int insert(MtcLossRepairInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossRepairInfo, c ->
            c.map(taskNo).toProperty("taskNo")
            .map(repairId).toProperty("repairId")
            .map(repairModeCode).toProperty("repairModeCode")
            .map(itemName).toProperty("itemName")
            .map(manpowerFee).toProperty("manpowerFee")
            .map(manpowerRefFee).toProperty("manpowerRefFee")
            .map(selfConfigFlag).toProperty("selfConfigFlag")
            .map(itemCoverCode).toProperty("itemCoverCode")
            .map(remark).toProperty("remark")
            .map(clmTms).toProperty("clmTms")
            .map(evalHour).toProperty("evalHour")
            .map(repairUnitPrice).toProperty("repairUnitPrice")
            .map(repairLevelCode).toProperty("repairLevelCode")
            .map(repairLevelName).toProperty("repairLevelName")
            .map(auditManpowerFee).toProperty("auditManpowerFee")
            .map(apprHour).toProperty("apprHour")
            .map(checkState).toProperty("checkState")
            .map(auditRemark).toProperty("auditRemark")
            .map(status).toProperty("status")
            .map(miscDesc).toProperty("miscDesc")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default int insertSelective(MtcLossRepairInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossRepairInfo, c ->
            c.map(taskNo).toPropertyWhenPresent("taskNo", row::getTaskNo)
            .map(repairId).toPropertyWhenPresent("repairId", row::getRepairId)
            .map(repairModeCode).toPropertyWhenPresent("repairModeCode", row::getRepairModeCode)
            .map(itemName).toPropertyWhenPresent("itemName", row::getItemName)
            .map(manpowerFee).toPropertyWhenPresent("manpowerFee", row::getManpowerFee)
            .map(manpowerRefFee).toPropertyWhenPresent("manpowerRefFee", row::getManpowerRefFee)
            .map(selfConfigFlag).toPropertyWhenPresent("selfConfigFlag", row::getSelfConfigFlag)
            .map(itemCoverCode).toPropertyWhenPresent("itemCoverCode", row::getItemCoverCode)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(clmTms).toPropertyWhenPresent("clmTms", row::getClmTms)
            .map(evalHour).toPropertyWhenPresent("evalHour", row::getEvalHour)
            .map(repairUnitPrice).toPropertyWhenPresent("repairUnitPrice", row::getRepairUnitPrice)
            .map(repairLevelCode).toPropertyWhenPresent("repairLevelCode", row::getRepairLevelCode)
            .map(repairLevelName).toPropertyWhenPresent("repairLevelName", row::getRepairLevelName)
            .map(auditManpowerFee).toPropertyWhenPresent("auditManpowerFee", row::getAuditManpowerFee)
            .map(apprHour).toPropertyWhenPresent("apprHour", row::getApprHour)
            .map(checkState).toPropertyWhenPresent("checkState", row::getCheckState)
            .map(auditRemark).toPropertyWhenPresent("auditRemark", row::getAuditRemark)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default Optional<MtcLossRepairInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcLossRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default List<MtcLossRepairInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcLossRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default List<MtcLossRepairInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcLossRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default Optional<MtcLossRepairInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcLossRepairInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcLossRepairInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalTo(row::getTaskNo)
                .set(repairId).equalTo(row::getRepairId)
                .set(repairModeCode).equalTo(row::getRepairModeCode)
                .set(itemName).equalTo(row::getItemName)
                .set(manpowerFee).equalTo(row::getManpowerFee)
                .set(manpowerRefFee).equalTo(row::getManpowerRefFee)
                .set(selfConfigFlag).equalTo(row::getSelfConfigFlag)
                .set(itemCoverCode).equalTo(row::getItemCoverCode)
                .set(remark).equalTo(row::getRemark)
                .set(clmTms).equalTo(row::getClmTms)
                .set(evalHour).equalTo(row::getEvalHour)
                .set(repairUnitPrice).equalTo(row::getRepairUnitPrice)
                .set(repairLevelCode).equalTo(row::getRepairLevelCode)
                .set(repairLevelName).equalTo(row::getRepairLevelName)
                .set(auditManpowerFee).equalTo(row::getAuditManpowerFee)
                .set(apprHour).equalTo(row::getApprHour)
                .set(checkState).equalTo(row::getCheckState)
                .set(auditRemark).equalTo(row::getAuditRemark)
                .set(status).equalTo(row::getStatus)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcLossRepairInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(repairId).equalToWhenPresent(row::getRepairId)
                .set(repairModeCode).equalToWhenPresent(row::getRepairModeCode)
                .set(itemName).equalToWhenPresent(row::getItemName)
                .set(manpowerFee).equalToWhenPresent(row::getManpowerFee)
                .set(manpowerRefFee).equalToWhenPresent(row::getManpowerRefFee)
                .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
                .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(clmTms).equalToWhenPresent(row::getClmTms)
                .set(evalHour).equalToWhenPresent(row::getEvalHour)
                .set(repairUnitPrice).equalToWhenPresent(row::getRepairUnitPrice)
                .set(repairLevelCode).equalToWhenPresent(row::getRepairLevelCode)
                .set(repairLevelName).equalToWhenPresent(row::getRepairLevelName)
                .set(auditManpowerFee).equalToWhenPresent(row::getAuditManpowerFee)
                .set(apprHour).equalToWhenPresent(row::getApprHour)
                .set(checkState).equalToWhenPresent(row::getCheckState)
                .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default int updateByPrimaryKey(MtcLossRepairInfo row) {
        return update(c ->
            c.set(taskNo).equalTo(row::getTaskNo)
            .set(repairId).equalTo(row::getRepairId)
            .set(repairModeCode).equalTo(row::getRepairModeCode)
            .set(itemName).equalTo(row::getItemName)
            .set(manpowerFee).equalTo(row::getManpowerFee)
            .set(manpowerRefFee).equalTo(row::getManpowerRefFee)
            .set(selfConfigFlag).equalTo(row::getSelfConfigFlag)
            .set(itemCoverCode).equalTo(row::getItemCoverCode)
            .set(remark).equalTo(row::getRemark)
            .set(clmTms).equalTo(row::getClmTms)
            .set(evalHour).equalTo(row::getEvalHour)
            .set(repairUnitPrice).equalTo(row::getRepairUnitPrice)
            .set(repairLevelCode).equalTo(row::getRepairLevelCode)
            .set(repairLevelName).equalTo(row::getRepairLevelName)
            .set(auditManpowerFee).equalTo(row::getAuditManpowerFee)
            .set(apprHour).equalTo(row::getApprHour)
            .set(checkState).equalTo(row::getCheckState)
            .set(auditRemark).equalTo(row::getAuditRemark)
            .set(status).equalTo(row::getStatus)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_repair_info")
    default int updateByPrimaryKeySelective(MtcLossRepairInfo row) {
        return update(c ->
            c.set(taskNo).equalToWhenPresent(row::getTaskNo)
            .set(repairId).equalToWhenPresent(row::getRepairId)
            .set(repairModeCode).equalToWhenPresent(row::getRepairModeCode)
            .set(itemName).equalToWhenPresent(row::getItemName)
            .set(manpowerFee).equalToWhenPresent(row::getManpowerFee)
            .set(manpowerRefFee).equalToWhenPresent(row::getManpowerRefFee)
            .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
            .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(clmTms).equalToWhenPresent(row::getClmTms)
            .set(evalHour).equalToWhenPresent(row::getEvalHour)
            .set(repairUnitPrice).equalToWhenPresent(row::getRepairUnitPrice)
            .set(repairLevelCode).equalToWhenPresent(row::getRepairLevelCode)
            .set(repairLevelName).equalToWhenPresent(row::getRepairLevelName)
            .set(auditManpowerFee).equalToWhenPresent(row::getAuditManpowerFee)
            .set(apprHour).equalToWhenPresent(row::getApprHour)
            .set(checkState).equalToWhenPresent(row::getCheckState)
            .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}