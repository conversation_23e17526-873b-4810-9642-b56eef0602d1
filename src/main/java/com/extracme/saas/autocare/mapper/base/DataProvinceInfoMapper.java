package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.DataProvinceInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.DataProvinceInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DataProvinceInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, provinceid, province, inCommonUse);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<DataProvinceInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DataProvinceInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="provinceid", property="provinceid", jdbcType=JdbcType.BIGINT),
        @Result(column="province", property="province", jdbcType=JdbcType.VARCHAR),
        @Result(column="in_common_use", property="inCommonUse", jdbcType=JdbcType.INTEGER)
    })
    List<DataProvinceInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DataProvinceInfoResult")
    Optional<DataProvinceInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, dataProvinceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, dataProvinceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default int insert(DataProvinceInfo row) {
        return MyBatis3Utils.insert(this::insert, row, dataProvinceInfo, c ->
            c.map(provinceid).toProperty("provinceid")
            .map(province).toProperty("province")
            .map(inCommonUse).toProperty("inCommonUse")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default int insertSelective(DataProvinceInfo row) {
        return MyBatis3Utils.insert(this::insert, row, dataProvinceInfo, c ->
            c.map(provinceid).toPropertyWhenPresent("provinceid", row::getProvinceid)
            .map(province).toPropertyWhenPresent("province", row::getProvince)
            .map(inCommonUse).toPropertyWhenPresent("inCommonUse", row::getInCommonUse)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default Optional<DataProvinceInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, dataProvinceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default List<DataProvinceInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, dataProvinceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default List<DataProvinceInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, dataProvinceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default Optional<DataProvinceInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, dataProvinceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    static UpdateDSL<UpdateModel> updateAllColumns(DataProvinceInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(provinceid).equalTo(row::getProvinceid)
                .set(province).equalTo(row::getProvince)
                .set(inCommonUse).equalTo(row::getInCommonUse);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DataProvinceInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(provinceid).equalToWhenPresent(row::getProvinceid)
                .set(province).equalToWhenPresent(row::getProvince)
                .set(inCommonUse).equalToWhenPresent(row::getInCommonUse);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default int updateByPrimaryKey(DataProvinceInfo row) {
        return update(c ->
            c.set(provinceid).equalTo(row::getProvinceid)
            .set(province).equalTo(row::getProvince)
            .set(inCommonUse).equalTo(row::getInCommonUse)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_province_info")
    default int updateByPrimaryKeySelective(DataProvinceInfo row) {
        return update(c ->
            c.set(provinceid).equalToWhenPresent(row::getProvinceid)
            .set(province).equalToWhenPresent(row::getProvince)
            .set(inCommonUse).equalToWhenPresent(row::getInCommonUse)
            .where(id, isEqualTo(row::getId))
        );
    }
}