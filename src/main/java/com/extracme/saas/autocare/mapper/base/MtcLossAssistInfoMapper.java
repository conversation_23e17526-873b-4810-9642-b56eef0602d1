package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcLossAssistInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcLossAssistInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNo, assistId, itemName, count, materialFee, evalMateSum, selfConfigFlag, itemCoverCode, remark, clmTms, auditPrice, auditCount, apprMateSum, checkState, auditRemark, status, miscDesc, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcLossAssistInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcLossAssistInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_no", property="taskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="assist_id", property="assistId", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
        @Result(column="count", property="count", jdbcType=JdbcType.DECIMAL),
        @Result(column="material_fee", property="materialFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="eval_mate_sum", property="evalMateSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="self_config_flag", property="selfConfigFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="item_cover_code", property="itemCoverCode", jdbcType=JdbcType.INTEGER),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="clm_tms", property="clmTms", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_price", property="auditPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="audit_count", property="auditCount", jdbcType=JdbcType.DECIMAL),
        @Result(column="appr_mate_sum", property="apprMateSum", jdbcType=JdbcType.DECIMAL),
        @Result(column="check_state", property="checkState", jdbcType=JdbcType.VARCHAR),
        @Result(column="audit_remark", property="auditRemark", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_Desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcLossAssistInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcLossAssistInfoResult")
    Optional<MtcLossAssistInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcLossAssistInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcLossAssistInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default int insert(MtcLossAssistInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossAssistInfo, c ->
            c.map(taskNo).toProperty("taskNo")
            .map(assistId).toProperty("assistId")
            .map(itemName).toProperty("itemName")
            .map(count).toProperty("count")
            .map(materialFee).toProperty("materialFee")
            .map(evalMateSum).toProperty("evalMateSum")
            .map(selfConfigFlag).toProperty("selfConfigFlag")
            .map(itemCoverCode).toProperty("itemCoverCode")
            .map(remark).toProperty("remark")
            .map(clmTms).toProperty("clmTms")
            .map(auditPrice).toProperty("auditPrice")
            .map(auditCount).toProperty("auditCount")
            .map(apprMateSum).toProperty("apprMateSum")
            .map(checkState).toProperty("checkState")
            .map(auditRemark).toProperty("auditRemark")
            .map(status).toProperty("status")
            .map(miscDesc).toProperty("miscDesc")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default int insertSelective(MtcLossAssistInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcLossAssistInfo, c ->
            c.map(taskNo).toPropertyWhenPresent("taskNo", row::getTaskNo)
            .map(assistId).toPropertyWhenPresent("assistId", row::getAssistId)
            .map(itemName).toPropertyWhenPresent("itemName", row::getItemName)
            .map(count).toPropertyWhenPresent("count", row::getCount)
            .map(materialFee).toPropertyWhenPresent("materialFee", row::getMaterialFee)
            .map(evalMateSum).toPropertyWhenPresent("evalMateSum", row::getEvalMateSum)
            .map(selfConfigFlag).toPropertyWhenPresent("selfConfigFlag", row::getSelfConfigFlag)
            .map(itemCoverCode).toPropertyWhenPresent("itemCoverCode", row::getItemCoverCode)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(clmTms).toPropertyWhenPresent("clmTms", row::getClmTms)
            .map(auditPrice).toPropertyWhenPresent("auditPrice", row::getAuditPrice)
            .map(auditCount).toPropertyWhenPresent("auditCount", row::getAuditCount)
            .map(apprMateSum).toPropertyWhenPresent("apprMateSum", row::getApprMateSum)
            .map(checkState).toPropertyWhenPresent("checkState", row::getCheckState)
            .map(auditRemark).toPropertyWhenPresent("auditRemark", row::getAuditRemark)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default Optional<MtcLossAssistInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcLossAssistInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default List<MtcLossAssistInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcLossAssistInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default List<MtcLossAssistInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcLossAssistInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default Optional<MtcLossAssistInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcLossAssistInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcLossAssistInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalTo(row::getTaskNo)
                .set(assistId).equalTo(row::getAssistId)
                .set(itemName).equalTo(row::getItemName)
                .set(count).equalTo(row::getCount)
                .set(materialFee).equalTo(row::getMaterialFee)
                .set(evalMateSum).equalTo(row::getEvalMateSum)
                .set(selfConfigFlag).equalTo(row::getSelfConfigFlag)
                .set(itemCoverCode).equalTo(row::getItemCoverCode)
                .set(remark).equalTo(row::getRemark)
                .set(clmTms).equalTo(row::getClmTms)
                .set(auditPrice).equalTo(row::getAuditPrice)
                .set(auditCount).equalTo(row::getAuditCount)
                .set(apprMateSum).equalTo(row::getApprMateSum)
                .set(checkState).equalTo(row::getCheckState)
                .set(auditRemark).equalTo(row::getAuditRemark)
                .set(status).equalTo(row::getStatus)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcLossAssistInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(assistId).equalToWhenPresent(row::getAssistId)
                .set(itemName).equalToWhenPresent(row::getItemName)
                .set(count).equalToWhenPresent(row::getCount)
                .set(materialFee).equalToWhenPresent(row::getMaterialFee)
                .set(evalMateSum).equalToWhenPresent(row::getEvalMateSum)
                .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
                .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(clmTms).equalToWhenPresent(row::getClmTms)
                .set(auditPrice).equalToWhenPresent(row::getAuditPrice)
                .set(auditCount).equalToWhenPresent(row::getAuditCount)
                .set(apprMateSum).equalToWhenPresent(row::getApprMateSum)
                .set(checkState).equalToWhenPresent(row::getCheckState)
                .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default int updateByPrimaryKey(MtcLossAssistInfo row) {
        return update(c ->
            c.set(taskNo).equalTo(row::getTaskNo)
            .set(assistId).equalTo(row::getAssistId)
            .set(itemName).equalTo(row::getItemName)
            .set(count).equalTo(row::getCount)
            .set(materialFee).equalTo(row::getMaterialFee)
            .set(evalMateSum).equalTo(row::getEvalMateSum)
            .set(selfConfigFlag).equalTo(row::getSelfConfigFlag)
            .set(itemCoverCode).equalTo(row::getItemCoverCode)
            .set(remark).equalTo(row::getRemark)
            .set(clmTms).equalTo(row::getClmTms)
            .set(auditPrice).equalTo(row::getAuditPrice)
            .set(auditCount).equalTo(row::getAuditCount)
            .set(apprMateSum).equalTo(row::getApprMateSum)
            .set(checkState).equalTo(row::getCheckState)
            .set(auditRemark).equalTo(row::getAuditRemark)
            .set(status).equalTo(row::getStatus)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_loss_assist_info")
    default int updateByPrimaryKeySelective(MtcLossAssistInfo row) {
        return update(c ->
            c.set(taskNo).equalToWhenPresent(row::getTaskNo)
            .set(assistId).equalToWhenPresent(row::getAssistId)
            .set(itemName).equalToWhenPresent(row::getItemName)
            .set(count).equalToWhenPresent(row::getCount)
            .set(materialFee).equalToWhenPresent(row::getMaterialFee)
            .set(evalMateSum).equalToWhenPresent(row::getEvalMateSum)
            .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
            .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(clmTms).equalToWhenPresent(row::getClmTms)
            .set(auditPrice).equalToWhenPresent(row::getAuditPrice)
            .set(auditCount).equalToWhenPresent(row::getAuditCount)
            .set(apprMateSum).equalToWhenPresent(row::getApprMateSum)
            .set(checkState).equalToWhenPresent(row::getCheckState)
            .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}