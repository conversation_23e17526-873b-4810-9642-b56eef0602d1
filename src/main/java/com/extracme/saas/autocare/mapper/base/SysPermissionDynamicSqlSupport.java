package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SysPermissionDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    public static final SysPermission sysPermission = new SysPermission();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.id")
    public static final SqlColumn<Long> id = sysPermission.id;

    /**
     * Database Column Remarks:
     *   权限名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.permission_name")
    public static final SqlColumn<String> permissionName = sysPermission.permissionName;

    /**
     * Database Column Remarks:
     *   权限编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.permission_code")
    public static final SqlColumn<String> permissionCode = sysPermission.permissionCode;

    /**
     * Database Column Remarks:
     *   权限类型：menu-菜单，button-按钮，api-接口
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.permission_type")
    public static final SqlColumn<String> permissionType = sysPermission.permissionType;

    /**
     * Database Column Remarks:
     *   父级权限ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.parent_id")
    public static final SqlColumn<Long> parentId = sysPermission.parentId;

    /**
     * Database Column Remarks:
     *   路由地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.path")
    public static final SqlColumn<String> path = sysPermission.path;

    /**
     * Database Column Remarks:
     *   组件路径
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.component")
    public static final SqlColumn<String> component = sysPermission.component;

    /**
     * Database Column Remarks:
     *   图标
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.icon")
    public static final SqlColumn<String> icon = sysPermission.icon;

    /**
     * Database Column Remarks:
     *   排序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.sort")
    public static final SqlColumn<Integer> sort = sysPermission.sort;

    /**
     * Database Column Remarks:
     *   状态：0-禁用，1-启用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.status")
    public static final SqlColumn<Integer> status = sysPermission.status;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.create_by")
    public static final SqlColumn<String> createBy = sysPermission.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.created_time")
    public static final SqlColumn<Date> createdTime = sysPermission.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.update_by")
    public static final SqlColumn<String> updateBy = sysPermission.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_permission.updated_time")
    public static final SqlColumn<Date> updatedTime = sysPermission.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_permission")
    public static final class SysPermission extends AliasableSqlTable<SysPermission> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> permissionName = column("permission_name", JDBCType.VARCHAR);

        public final SqlColumn<String> permissionCode = column("permission_code", JDBCType.VARCHAR);

        public final SqlColumn<String> permissionType = column("permission_type", JDBCType.VARCHAR);

        public final SqlColumn<Long> parentId = column("parent_id", JDBCType.BIGINT);

        public final SqlColumn<String> path = column("path", JDBCType.VARCHAR);

        public final SqlColumn<String> component = column("component", JDBCType.VARCHAR);

        public final SqlColumn<String> icon = column("icon", JDBCType.VARCHAR);

        public final SqlColumn<Integer> sort = column("sort", JDBCType.INTEGER);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public SysPermission() {
            super("sys_permission", SysPermission::new);
        }
    }
}