package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcVehicleInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    public static final MtcVehicleInfo mtcVehicleInfo = new MtcVehicleInfo();

    /**
     * Database Column Remarks:
     *   主键，自增
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.id")
    public static final SqlColumn<Long> id = mtcVehicleInfo.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vin")
    public static final SqlColumn<String> vin = mtcVehicleInfo.vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_no")
    public static final SqlColumn<String> vehicleNo = mtcVehicleInfo.vehicleNo;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_model_id")
    public static final SqlColumn<Long> vehicleModelId = mtcVehicleInfo.vehicleModelId;

    /**
     * Database Column Remarks:
     *   车辆所属公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_org_id")
    public static final SqlColumn<String> vehicleOrgId = mtcVehicleInfo.vehicleOrgId;

    /**
     * Database Column Remarks:
     *   车辆运营公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.operation_org_id")
    public static final SqlColumn<String> operationOrgId = mtcVehicleInfo.operationOrgId;

    /**
     * Database Column Remarks:
     *   产品线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.product_line")
    public static final SqlColumn<Integer> productLine = mtcVehicleInfo.productLine;

    /**
     * Database Column Remarks:
     *   子产品线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.sub_product_line")
    public static final SqlColumn<Integer> subProductLine = mtcVehicleInfo.subProductLine;

    /**
     * Database Column Remarks:
     *   实际运营标签
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.fact_operate_tag")
    public static final SqlColumn<Integer> factOperateTag = mtcVehicleInfo.factOperateTag;

    /**
     * Database Column Remarks:
     *   资产状态
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.property_status")
    public static final SqlColumn<Integer> propertyStatus = mtcVehicleInfo.propertyStatus;

    /**
     * Database Column Remarks:
     *   总里程数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.total_mileage")
    public static final SqlColumn<Integer> totalMileage = mtcVehicleInfo.totalMileage;

    /**
     * Database Column Remarks:
     *   发动机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.engine_id")
    public static final SqlColumn<String> engineId = mtcVehicleInfo.engineId;

    /**
     * Database Column Remarks:
     *   注册日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.register_date")
    public static final SqlColumn<Date> registerDate = mtcVehicleInfo.registerDate;

    /**
     * Database Column Remarks:
     *   交强险开始日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.tci_startdate")
    public static final SqlColumn<Date> tciStartdate = mtcVehicleInfo.tciStartdate;

    /**
     * Database Column Remarks:
     *   交强险结束日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.tci_enddate")
    public static final SqlColumn<Date> tciEnddate = mtcVehicleInfo.tciEnddate;

    /**
     * Database Column Remarks:
     *   保险所属id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.insurance_belongs")
    public static final SqlColumn<Long> insuranceBelongs = mtcVehicleInfo.insuranceBelongs;

    /**
     * Database Column Remarks:
     *   保险所属名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.insurance_company_name")
    public static final SqlColumn<String> insuranceCompanyName = mtcVehicleInfo.insuranceCompanyName;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.create_time")
    public static final SqlColumn<Date> createTime = mtcVehicleInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.create_by")
    public static final SqlColumn<String> createBy = mtcVehicleInfo.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.update_time")
    public static final SqlColumn<Date> updateTime = mtcVehicleInfo.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.update_by")
    public static final SqlColumn<String> updateBy = mtcVehicleInfo.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    public static final class MtcVehicleInfo extends AliasableSqlTable<MtcVehicleInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleNo = column("vehicle_no", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelId = column("vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleOrgId = column("vehicle_org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> operationOrgId = column("operation_org_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> productLine = column("product_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> subProductLine = column("sub_product_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> factOperateTag = column("fact_operate_tag", JDBCType.INTEGER);

        public final SqlColumn<Integer> propertyStatus = column("property_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> totalMileage = column("total_mileage", JDBCType.INTEGER);

        public final SqlColumn<String> engineId = column("engine_id", JDBCType.VARCHAR);

        public final SqlColumn<Date> registerDate = column("register_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> tciStartdate = column("tci_startdate", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> tciEnddate = column("tci_enddate", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> insuranceBelongs = column("insurance_belongs", JDBCType.BIGINT);

        public final SqlColumn<String> insuranceCompanyName = column("insurance_company_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public MtcVehicleInfo() {
            super("mtc_vehicle_info", MtcVehicleInfo::new);
        }
    }
}