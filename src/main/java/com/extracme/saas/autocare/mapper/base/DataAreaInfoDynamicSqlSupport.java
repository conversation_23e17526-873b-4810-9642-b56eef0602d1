package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DataAreaInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    public static final DataAreaInfo dataAreaInfo = new DataAreaInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.id")
    public static final SqlColumn<Long> id = dataAreaInfo.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.areaid")
    public static final SqlColumn<Long> areaid = dataAreaInfo.areaid;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.area")
    public static final SqlColumn<String> area = dataAreaInfo.area;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.fatherid")
    public static final SqlColumn<Long> fatherid = dataAreaInfo.fatherid;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.lon")
    public static final SqlColumn<BigDecimal> lon = dataAreaInfo.lon;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: data_area_info.lat")
    public static final SqlColumn<BigDecimal> lat = dataAreaInfo.lat;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: data_area_info")
    public static final class DataAreaInfo extends AliasableSqlTable<DataAreaInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> areaid = column("areaid", JDBCType.BIGINT);

        public final SqlColumn<String> area = column("area", JDBCType.VARCHAR);

        public final SqlColumn<Long> fatherid = column("fatherid", JDBCType.BIGINT);

        public final SqlColumn<BigDecimal> lon = column("lon", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> lat = column("lat", JDBCType.DECIMAL);

        public DataAreaInfo() {
            super("data_area_info", DataAreaInfo::new);
        }
    }
}