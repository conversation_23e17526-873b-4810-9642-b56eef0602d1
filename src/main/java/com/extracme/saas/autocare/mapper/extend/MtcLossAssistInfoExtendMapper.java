package com.extracme.saas.autocare.mapper.extend;

import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.apprMateSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.assistId;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.auditCount;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.auditPrice;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.auditRemark;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.checkState;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.clmTms;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.count;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.createBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.createdTime;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.evalMateSum;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.itemCoverCode;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.itemName;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.materialFee;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.miscDesc;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.remark;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.selfConfigFlag;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.status;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.taskNo;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.updateBy;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import org.apache.ibatis.annotations.Mapper;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoMapper;
import com.extracme.saas.autocare.model.entity.MtcLossAssistInfo;

@Mapper
@TenantSchema
public interface MtcLossAssistInfoExtendMapper extends MtcLossAssistInfoMapper {

    /**
     * 根据任务编号和状态更新损失辅料信息
     * 
     * @param row
     * @return
     */
    default int updateSelectiveByTaskNoAndStatus(MtcLossAssistInfo row) {
        return update(c -> c.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(assistId).equalToWhenPresent(row::getAssistId)
                .set(itemName).equalToWhenPresent(row::getItemName)
                .set(count).equalToWhenPresent(row::getCount)
                .set(materialFee).equalToWhenPresent(row::getMaterialFee)
                .set(evalMateSum).equalToWhenPresent(row::getEvalMateSum)
                .set(selfConfigFlag).equalToWhenPresent(row::getSelfConfigFlag)
                .set(itemCoverCode).equalToWhenPresent(row::getItemCoverCode)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(clmTms).equalToWhenPresent(row::getClmTms)
                .set(auditPrice).equalToWhenPresent(row::getAuditPrice)
                .set(auditCount).equalToWhenPresent(row::getAuditCount)
                .set(apprMateSum).equalToWhenPresent(row::getApprMateSum)
                .set(checkState).equalToWhenPresent(row::getCheckState)
                .set(auditRemark).equalToWhenPresent(row::getAuditRemark)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .where(taskNo, isEqualTo(row::getTaskNo))
                .and(status, isEqualTo(1))
                .and(assistId, isEqualTo(row::getAssistId)));
    }
}
