package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.ActivityInstanceDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.ActivityInstance;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ActivityInstanceMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    BasicColumn[] selectList = BasicColumn.columnList(id, instanceId, toActivityCode, fromActivityCode, transitionId, startTime, endTime, duration, currentStatusCode, operator, createTime, createBy, updateTime, updateBy, remarks);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<ActivityInstance> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ActivityInstanceResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="instance_id", property="instanceId", jdbcType=JdbcType.BIGINT),
        @Result(column="to_activity_code", property="toActivityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="from_activity_code", property="fromActivityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="transition_id", property="transitionId", jdbcType=JdbcType.BIGINT),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_time", property="endTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="duration", property="duration", jdbcType=JdbcType.INTEGER),
        @Result(column="current_status_code", property="currentStatusCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator", property="operator", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="remarks", property="remarks", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<ActivityInstance> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ActivityInstanceResult")
    Optional<ActivityInstance> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, activityInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, activityInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default int insert(ActivityInstance row) {
        return MyBatis3Utils.insert(this::insert, row, activityInstance, c ->
            c.map(instanceId).toProperty("instanceId")
            .map(toActivityCode).toProperty("toActivityCode")
            .map(fromActivityCode).toProperty("fromActivityCode")
            .map(transitionId).toProperty("transitionId")
            .map(startTime).toProperty("startTime")
            .map(endTime).toProperty("endTime")
            .map(duration).toProperty("duration")
            .map(currentStatusCode).toProperty("currentStatusCode")
            .map(operator).toProperty("operator")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
            .map(remarks).toProperty("remarks")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default int insertSelective(ActivityInstance row) {
        return MyBatis3Utils.insert(this::insert, row, activityInstance, c ->
            c.map(instanceId).toPropertyWhenPresent("instanceId", row::getInstanceId)
            .map(toActivityCode).toPropertyWhenPresent("toActivityCode", row::getToActivityCode)
            .map(fromActivityCode).toPropertyWhenPresent("fromActivityCode", row::getFromActivityCode)
            .map(transitionId).toPropertyWhenPresent("transitionId", row::getTransitionId)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(endTime).toPropertyWhenPresent("endTime", row::getEndTime)
            .map(duration).toPropertyWhenPresent("duration", row::getDuration)
            .map(currentStatusCode).toPropertyWhenPresent("currentStatusCode", row::getCurrentStatusCode)
            .map(operator).toPropertyWhenPresent("operator", row::getOperator)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(remarks).toPropertyWhenPresent("remarks", row::getRemarks)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default Optional<ActivityInstance> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, activityInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default List<ActivityInstance> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, activityInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default List<ActivityInstance> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, activityInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default Optional<ActivityInstance> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, activityInstance, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    static UpdateDSL<UpdateModel> updateAllColumns(ActivityInstance row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(instanceId).equalTo(row::getInstanceId)
                .set(toActivityCode).equalTo(row::getToActivityCode)
                .set(fromActivityCode).equalTo(row::getFromActivityCode)
                .set(transitionId).equalTo(row::getTransitionId)
                .set(startTime).equalTo(row::getStartTime)
                .set(endTime).equalTo(row::getEndTime)
                .set(duration).equalTo(row::getDuration)
                .set(currentStatusCode).equalTo(row::getCurrentStatusCode)
                .set(operator).equalTo(row::getOperator)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(remarks).equalTo(row::getRemarks);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ActivityInstance row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(instanceId).equalToWhenPresent(row::getInstanceId)
                .set(toActivityCode).equalToWhenPresent(row::getToActivityCode)
                .set(fromActivityCode).equalToWhenPresent(row::getFromActivityCode)
                .set(transitionId).equalToWhenPresent(row::getTransitionId)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(endTime).equalToWhenPresent(row::getEndTime)
                .set(duration).equalToWhenPresent(row::getDuration)
                .set(currentStatusCode).equalToWhenPresent(row::getCurrentStatusCode)
                .set(operator).equalToWhenPresent(row::getOperator)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(remarks).equalToWhenPresent(row::getRemarks);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default int updateByPrimaryKey(ActivityInstance row) {
        return update(c ->
            c.set(instanceId).equalTo(row::getInstanceId)
            .set(toActivityCode).equalTo(row::getToActivityCode)
            .set(fromActivityCode).equalTo(row::getFromActivityCode)
            .set(transitionId).equalTo(row::getTransitionId)
            .set(startTime).equalTo(row::getStartTime)
            .set(endTime).equalTo(row::getEndTime)
            .set(duration).equalTo(row::getDuration)
            .set(currentStatusCode).equalTo(row::getCurrentStatusCode)
            .set(operator).equalTo(row::getOperator)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(remarks).equalTo(row::getRemarks)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: activity_instance")
    default int updateByPrimaryKeySelective(ActivityInstance row) {
        return update(c ->
            c.set(instanceId).equalToWhenPresent(row::getInstanceId)
            .set(toActivityCode).equalToWhenPresent(row::getToActivityCode)
            .set(fromActivityCode).equalToWhenPresent(row::getFromActivityCode)
            .set(transitionId).equalToWhenPresent(row::getTransitionId)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(endTime).equalToWhenPresent(row::getEndTime)
            .set(duration).equalToWhenPresent(row::getDuration)
            .set(currentStatusCode).equalToWhenPresent(row::getCurrentStatusCode)
            .set(operator).equalToWhenPresent(row::getOperator)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(remarks).equalToWhenPresent(row::getRemarks)
            .where(id, isEqualTo(row::getId))
        );
    }
}