package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.WorkflowInstantProgressDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.WorkflowInstantProgress;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface WorkflowInstantProgressMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    BasicColumn[] selectList = BasicColumn.columnList(id, instanceId, activityCode, statusCode, createTime, createBy, updateTime, updateBy, remarks);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<WorkflowInstantProgress> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="WorkflowInstantProgressResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="instance_id", property="instanceId", jdbcType=JdbcType.BIGINT),
        @Result(column="activity_code", property="activityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="status_code", property="statusCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="remarks", property="remarks", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<WorkflowInstantProgress> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("WorkflowInstantProgressResult")
    Optional<WorkflowInstantProgress> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, workflowInstantProgress, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, workflowInstantProgress, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default int insert(WorkflowInstantProgress row) {
        return MyBatis3Utils.insert(this::insert, row, workflowInstantProgress, c ->
            c.map(instanceId).toProperty("instanceId")
            .map(activityCode).toProperty("activityCode")
            .map(statusCode).toProperty("statusCode")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
            .map(remarks).toProperty("remarks")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default int insertSelective(WorkflowInstantProgress row) {
        return MyBatis3Utils.insert(this::insert, row, workflowInstantProgress, c ->
            c.map(instanceId).toPropertyWhenPresent("instanceId", row::getInstanceId)
            .map(activityCode).toPropertyWhenPresent("activityCode", row::getActivityCode)
            .map(statusCode).toPropertyWhenPresent("statusCode", row::getStatusCode)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(remarks).toPropertyWhenPresent("remarks", row::getRemarks)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default Optional<WorkflowInstantProgress> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, workflowInstantProgress, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default List<WorkflowInstantProgress> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, workflowInstantProgress, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default List<WorkflowInstantProgress> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, workflowInstantProgress, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default Optional<WorkflowInstantProgress> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, workflowInstantProgress, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    static UpdateDSL<UpdateModel> updateAllColumns(WorkflowInstantProgress row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(instanceId).equalTo(row::getInstanceId)
                .set(activityCode).equalTo(row::getActivityCode)
                .set(statusCode).equalTo(row::getStatusCode)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(remarks).equalTo(row::getRemarks);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(WorkflowInstantProgress row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(instanceId).equalToWhenPresent(row::getInstanceId)
                .set(activityCode).equalToWhenPresent(row::getActivityCode)
                .set(statusCode).equalToWhenPresent(row::getStatusCode)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(remarks).equalToWhenPresent(row::getRemarks);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default int updateByPrimaryKey(WorkflowInstantProgress row) {
        return update(c ->
            c.set(instanceId).equalTo(row::getInstanceId)
            .set(activityCode).equalTo(row::getActivityCode)
            .set(statusCode).equalTo(row::getStatusCode)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(remarks).equalTo(row::getRemarks)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_instant_progress")
    default int updateByPrimaryKeySelective(WorkflowInstantProgress row) {
        return update(c ->
            c.set(instanceId).equalToWhenPresent(row::getInstanceId)
            .set(activityCode).equalToWhenPresent(row::getActivityCode)
            .set(statusCode).equalToWhenPresent(row::getStatusCode)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(remarks).equalToWhenPresent(row::getRemarks)
            .where(id, isEqualTo(row::getId))
        );
    }
}