package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class InsuranceCompanyInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    public static final InsuranceCompanyInfo insuranceCompanyInfo = new InsuranceCompanyInfo();

    /**
     * Database Column Remarks:
     *   主键，自增
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.id")
    public static final SqlColumn<Long> id = insuranceCompanyInfo.id;

    /**
     * Database Column Remarks:
     *   公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.company_name")
    public static final SqlColumn<String> companyName = insuranceCompanyInfo.companyName;

    /**
     * Database Column Remarks:
     *   公司简称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.company_abbreviation")
    public static final SqlColumn<String> companyAbbreviation = insuranceCompanyInfo.companyAbbreviation;

    /**
     * Database Column Remarks:
     *   联系电话
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.contact_number")
    public static final SqlColumn<String> contactNumber = insuranceCompanyInfo.contactNumber;

    /**
     * Database Column Remarks:
     *   状态（1：启用 0：禁用）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.status")
    public static final SqlColumn<Integer> status = insuranceCompanyInfo.status;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.create_time")
    public static final SqlColumn<Date> createTime = insuranceCompanyInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.create_by")
    public static final SqlColumn<String> createBy = insuranceCompanyInfo.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.update_time")
    public static final SqlColumn<Date> updateTime = insuranceCompanyInfo.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: insurance_company_info.update_by")
    public static final SqlColumn<String> updateBy = insuranceCompanyInfo.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: insurance_company_info")
    public static final class InsuranceCompanyInfo extends AliasableSqlTable<InsuranceCompanyInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> companyName = column("company_name", JDBCType.VARCHAR);

        public final SqlColumn<String> companyAbbreviation = column("company_abbreviation", JDBCType.VARCHAR);

        public final SqlColumn<String> contactNumber = column("contact_number", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public InsuranceCompanyInfo() {
            super("insurance_company_info", InsuranceCompanyInfo::new);
        }
    }
}