package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcRepairItemCheckInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcRepairItemCheckInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcRepairItemCheckInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNo, vin, itemId, itemNo, itemName, itemType, itemNumber, viewNumber, insurancePreReviewStatus, insuranceQuoteMaterialCostPrice, insuranceQuoteHourFeePrice, insuranceQuoteAmount, viewMaterialCostPrice, viewHourFeePrice, viewAmount, checkStatus, remark, status, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcRepairItemCheckInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcRepairItemCheckInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_no", property="taskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_id", property="itemId", jdbcType=JdbcType.BIGINT),
        @Result(column="item_no", property="itemNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_type", property="itemType", jdbcType=JdbcType.INTEGER),
        @Result(column="item_number", property="itemNumber", jdbcType=JdbcType.INTEGER),
        @Result(column="view_number", property="viewNumber", jdbcType=JdbcType.INTEGER),
        @Result(column="insurance_pre_review_status", property="insurancePreReviewStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="insurance_quote_material_cost_price", property="insuranceQuoteMaterialCostPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="insurance_quote_hour_fee_price", property="insuranceQuoteHourFeePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="insurance_quote_amount", property="insuranceQuoteAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="view_material_cost_price", property="viewMaterialCostPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="view_hour_fee_price", property="viewHourFeePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="view_amount", property="viewAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="check_status", property="checkStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcRepairItemCheckInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcRepairItemCheckInfoResult")
    Optional<MtcRepairItemCheckInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcRepairItemCheckInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcRepairItemCheckInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default int insert(MtcRepairItemCheckInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairItemCheckInfo, c ->
            c.map(taskNo).toProperty("taskNo")
            .map(vin).toProperty("vin")
            .map(itemId).toProperty("itemId")
            .map(itemNo).toProperty("itemNo")
            .map(itemName).toProperty("itemName")
            .map(itemType).toProperty("itemType")
            .map(itemNumber).toProperty("itemNumber")
            .map(viewNumber).toProperty("viewNumber")
            .map(insurancePreReviewStatus).toProperty("insurancePreReviewStatus")
            .map(insuranceQuoteMaterialCostPrice).toProperty("insuranceQuoteMaterialCostPrice")
            .map(insuranceQuoteHourFeePrice).toProperty("insuranceQuoteHourFeePrice")
            .map(insuranceQuoteAmount).toProperty("insuranceQuoteAmount")
            .map(viewMaterialCostPrice).toProperty("viewMaterialCostPrice")
            .map(viewHourFeePrice).toProperty("viewHourFeePrice")
            .map(viewAmount).toProperty("viewAmount")
            .map(checkStatus).toProperty("checkStatus")
            .map(remark).toProperty("remark")
            .map(status).toProperty("status")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default int insertSelective(MtcRepairItemCheckInfo row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairItemCheckInfo, c ->
            c.map(taskNo).toPropertyWhenPresent("taskNo", row::getTaskNo)
            .map(vin).toPropertyWhenPresent("vin", row::getVin)
            .map(itemId).toPropertyWhenPresent("itemId", row::getItemId)
            .map(itemNo).toPropertyWhenPresent("itemNo", row::getItemNo)
            .map(itemName).toPropertyWhenPresent("itemName", row::getItemName)
            .map(itemType).toPropertyWhenPresent("itemType", row::getItemType)
            .map(itemNumber).toPropertyWhenPresent("itemNumber", row::getItemNumber)
            .map(viewNumber).toPropertyWhenPresent("viewNumber", row::getViewNumber)
            .map(insurancePreReviewStatus).toPropertyWhenPresent("insurancePreReviewStatus", row::getInsurancePreReviewStatus)
            .map(insuranceQuoteMaterialCostPrice).toPropertyWhenPresent("insuranceQuoteMaterialCostPrice", row::getInsuranceQuoteMaterialCostPrice)
            .map(insuranceQuoteHourFeePrice).toPropertyWhenPresent("insuranceQuoteHourFeePrice", row::getInsuranceQuoteHourFeePrice)
            .map(insuranceQuoteAmount).toPropertyWhenPresent("insuranceQuoteAmount", row::getInsuranceQuoteAmount)
            .map(viewMaterialCostPrice).toPropertyWhenPresent("viewMaterialCostPrice", row::getViewMaterialCostPrice)
            .map(viewHourFeePrice).toPropertyWhenPresent("viewHourFeePrice", row::getViewHourFeePrice)
            .map(viewAmount).toPropertyWhenPresent("viewAmount", row::getViewAmount)
            .map(checkStatus).toPropertyWhenPresent("checkStatus", row::getCheckStatus)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default Optional<MtcRepairItemCheckInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcRepairItemCheckInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default List<MtcRepairItemCheckInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcRepairItemCheckInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default List<MtcRepairItemCheckInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcRepairItemCheckInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default Optional<MtcRepairItemCheckInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcRepairItemCheckInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcRepairItemCheckInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalTo(row::getTaskNo)
                .set(vin).equalTo(row::getVin)
                .set(itemId).equalTo(row::getItemId)
                .set(itemNo).equalTo(row::getItemNo)
                .set(itemName).equalTo(row::getItemName)
                .set(itemType).equalTo(row::getItemType)
                .set(itemNumber).equalTo(row::getItemNumber)
                .set(viewNumber).equalTo(row::getViewNumber)
                .set(insurancePreReviewStatus).equalTo(row::getInsurancePreReviewStatus)
                .set(insuranceQuoteMaterialCostPrice).equalTo(row::getInsuranceQuoteMaterialCostPrice)
                .set(insuranceQuoteHourFeePrice).equalTo(row::getInsuranceQuoteHourFeePrice)
                .set(insuranceQuoteAmount).equalTo(row::getInsuranceQuoteAmount)
                .set(viewMaterialCostPrice).equalTo(row::getViewMaterialCostPrice)
                .set(viewHourFeePrice).equalTo(row::getViewHourFeePrice)
                .set(viewAmount).equalTo(row::getViewAmount)
                .set(checkStatus).equalTo(row::getCheckStatus)
                .set(remark).equalTo(row::getRemark)
                .set(status).equalTo(row::getStatus)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcRepairItemCheckInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(vin).equalToWhenPresent(row::getVin)
                .set(itemId).equalToWhenPresent(row::getItemId)
                .set(itemNo).equalToWhenPresent(row::getItemNo)
                .set(itemName).equalToWhenPresent(row::getItemName)
                .set(itemType).equalToWhenPresent(row::getItemType)
                .set(itemNumber).equalToWhenPresent(row::getItemNumber)
                .set(viewNumber).equalToWhenPresent(row::getViewNumber)
                .set(insurancePreReviewStatus).equalToWhenPresent(row::getInsurancePreReviewStatus)
                .set(insuranceQuoteMaterialCostPrice).equalToWhenPresent(row::getInsuranceQuoteMaterialCostPrice)
                .set(insuranceQuoteHourFeePrice).equalToWhenPresent(row::getInsuranceQuoteHourFeePrice)
                .set(insuranceQuoteAmount).equalToWhenPresent(row::getInsuranceQuoteAmount)
                .set(viewMaterialCostPrice).equalToWhenPresent(row::getViewMaterialCostPrice)
                .set(viewHourFeePrice).equalToWhenPresent(row::getViewHourFeePrice)
                .set(viewAmount).equalToWhenPresent(row::getViewAmount)
                .set(checkStatus).equalToWhenPresent(row::getCheckStatus)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default int updateByPrimaryKey(MtcRepairItemCheckInfo row) {
        return update(c ->
            c.set(taskNo).equalTo(row::getTaskNo)
            .set(vin).equalTo(row::getVin)
            .set(itemId).equalTo(row::getItemId)
            .set(itemNo).equalTo(row::getItemNo)
            .set(itemName).equalTo(row::getItemName)
            .set(itemType).equalTo(row::getItemType)
            .set(itemNumber).equalTo(row::getItemNumber)
            .set(viewNumber).equalTo(row::getViewNumber)
            .set(insurancePreReviewStatus).equalTo(row::getInsurancePreReviewStatus)
            .set(insuranceQuoteMaterialCostPrice).equalTo(row::getInsuranceQuoteMaterialCostPrice)
            .set(insuranceQuoteHourFeePrice).equalTo(row::getInsuranceQuoteHourFeePrice)
            .set(insuranceQuoteAmount).equalTo(row::getInsuranceQuoteAmount)
            .set(viewMaterialCostPrice).equalTo(row::getViewMaterialCostPrice)
            .set(viewHourFeePrice).equalTo(row::getViewHourFeePrice)
            .set(viewAmount).equalTo(row::getViewAmount)
            .set(checkStatus).equalTo(row::getCheckStatus)
            .set(remark).equalTo(row::getRemark)
            .set(status).equalTo(row::getStatus)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_check_info")
    default int updateByPrimaryKeySelective(MtcRepairItemCheckInfo row) {
        return update(c ->
            c.set(taskNo).equalToWhenPresent(row::getTaskNo)
            .set(vin).equalToWhenPresent(row::getVin)
            .set(itemId).equalToWhenPresent(row::getItemId)
            .set(itemNo).equalToWhenPresent(row::getItemNo)
            .set(itemName).equalToWhenPresent(row::getItemName)
            .set(itemType).equalToWhenPresent(row::getItemType)
            .set(itemNumber).equalToWhenPresent(row::getItemNumber)
            .set(viewNumber).equalToWhenPresent(row::getViewNumber)
            .set(insurancePreReviewStatus).equalToWhenPresent(row::getInsurancePreReviewStatus)
            .set(insuranceQuoteMaterialCostPrice).equalToWhenPresent(row::getInsuranceQuoteMaterialCostPrice)
            .set(insuranceQuoteHourFeePrice).equalToWhenPresent(row::getInsuranceQuoteHourFeePrice)
            .set(insuranceQuoteAmount).equalToWhenPresent(row::getInsuranceQuoteAmount)
            .set(viewMaterialCostPrice).equalToWhenPresent(row::getViewMaterialCostPrice)
            .set(viewHourFeePrice).equalToWhenPresent(row::getViewHourFeePrice)
            .set(viewAmount).equalToWhenPresent(row::getViewAmount)
            .set(checkStatus).equalToWhenPresent(row::getCheckStatus)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}