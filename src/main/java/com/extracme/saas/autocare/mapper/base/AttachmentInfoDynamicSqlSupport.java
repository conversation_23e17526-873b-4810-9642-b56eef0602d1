package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AttachmentInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    public static final AttachmentInfo attachmentInfo = new AttachmentInfo();

    /**
     * Database Column Remarks:
     *   ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: attachment_info.id")
    public static final SqlColumn<Long> id = attachmentInfo.id;

    /**
     * Database Column Remarks:
     *   外键key
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: attachment_info.foreign_key")
    public static final SqlColumn<String> foreignKey = attachmentInfo.foreignKey;

    /**
     * Database Column Remarks:
     *   文件类型 1-事故用印附件
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: attachment_info.file_type")
    public static final SqlColumn<Integer> fileType = attachmentInfo.fileType;

    /**
     * Database Column Remarks:
     *   文件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: attachment_info.file_name")
    public static final SqlColumn<String> fileName = attachmentInfo.fileName;

    /**
     * Database Column Remarks:
     *   文件路径
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: attachment_info.file_url")
    public static final SqlColumn<String> fileUrl = attachmentInfo.fileUrl;

    /**
     * Database Column Remarks:
     *   文件描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: attachment_info.file_desc")
    public static final SqlColumn<String> fileDesc = attachmentInfo.fileDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: attachment_info.create_by")
    public static final SqlColumn<String> createBy = attachmentInfo.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: attachment_info.created_time")
    public static final SqlColumn<Date> createdTime = attachmentInfo.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: attachment_info.update_by")
    public static final SqlColumn<String> updateBy = attachmentInfo.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: attachment_info.updated_time")
    public static final SqlColumn<Date> updatedTime = attachmentInfo.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: attachment_info")
    public static final class AttachmentInfo extends AliasableSqlTable<AttachmentInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> foreignKey = column("foreign_key", JDBCType.VARCHAR);

        public final SqlColumn<Integer> fileType = column("file_type", JDBCType.INTEGER);

        public final SqlColumn<String> fileName = column("file_name", JDBCType.VARCHAR);

        public final SqlColumn<String> fileUrl = column("file_url", JDBCType.VARCHAR);

        public final SqlColumn<String> fileDesc = column("file_desc", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public AttachmentInfo() {
            super("attachment_info", AttachmentInfo::new);
        }
    }
}