package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class SysUserDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    public static final SysUser sysUser = new SysUser();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.id")
    public static final SqlColumn<Long> id = sysUser.id;

    /**
     * Database Column Remarks:
     *   租户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.tenant_id")
    public static final SqlColumn<Long> tenantId = sysUser.tenantId;

    /**
     * Database Column Remarks:
     *   用户名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.username")
    public static final SqlColumn<String> username = sysUser.username;

    /**
     * Database Column Remarks:
     *   手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.mobile")
    public static final SqlColumn<String> mobile = sysUser.mobile;

    /**
     * Database Column Remarks:
     *   昵称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.nickname")
    public static final SqlColumn<String> nickname = sysUser.nickname;

    /**
     * Database Column Remarks:
     *   头像
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.avatar")
    public static final SqlColumn<String> avatar = sysUser.avatar;

    /**
     * Database Column Remarks:
     *   邮箱
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.email")
    public static final SqlColumn<String> email = sysUser.email;

    /**
     * Database Column Remarks:
     *   状态:0-禁用,1-启用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.status")
    public static final SqlColumn<Integer> status = sysUser.status;

    /**
     * Database Column Remarks:
     *   账号类型:0-管理员 1-运营 2-修理厂 3-保险公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.account_type")
    public static final SqlColumn<Integer> accountType = sysUser.accountType;

    /**
     * Database Column Remarks:
     *   关联修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.repair_depot_id")
    public static final SqlColumn<String> repairDepotId = sysUser.repairDepotId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.approval_level")
    public static final SqlColumn<Integer> approvalLevel = sysUser.approvalLevel;

    /**
     * Database Column Remarks:
     *   保险公司ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.insurance_company_id")
    public static final SqlColumn<Long> insuranceCompanyId = sysUser.insuranceCompanyId;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.create_by")
    public static final SqlColumn<String> createBy = sysUser.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.created_time")
    public static final SqlColumn<Date> createdTime = sysUser.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.update_by")
    public static final SqlColumn<String> updateBy = sysUser.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: sys_user.updated_time")
    public static final SqlColumn<Date> updatedTime = sysUser.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: sys_user")
    public static final class SysUser extends AliasableSqlTable<SysUser> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> tenantId = column("tenant_id", JDBCType.BIGINT);

        public final SqlColumn<String> username = column("username", JDBCType.VARCHAR);

        public final SqlColumn<String> mobile = column("mobile", JDBCType.VARCHAR);

        public final SqlColumn<String> nickname = column("nickname", JDBCType.VARCHAR);

        public final SqlColumn<String> avatar = column("avatar", JDBCType.VARCHAR);

        public final SqlColumn<String> email = column("email", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<Integer> accountType = column("account_type", JDBCType.INTEGER);

        public final SqlColumn<String> repairDepotId = column("repair_depot_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> approvalLevel = column("approval_level", JDBCType.INTEGER);

        public final SqlColumn<Long> insuranceCompanyId = column("insurance_company_id", JDBCType.BIGINT);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public SysUser() {
            super("sys_user", SysUser::new);
        }
    }
}