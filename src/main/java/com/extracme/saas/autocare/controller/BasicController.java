package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.annotation.RequireLogin;
import com.extracme.saas.autocare.model.dto.*;
import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.extracme.saas.autocare.model.vo.*;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 基础数据字典控制器
 */
@Slf4j
@Api(tags = "基础数据管理")
@RestController
@RequestMapping("/api/v1/basic")
public class BasicController {

    @Autowired
    private DataDictService dataDictService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private ApprovalLevelsService approvalLevelsService;

    @Autowired
    private OperateLogService operateLogService;

    @Autowired
    private OperatorLogService operatorLogService;

    @Autowired
    private RegionService regionService;

    @Autowired
    private OssService ossService;

    @ApiOperation("分页查询数据字典列表")
    @PostMapping("/dict/list")
    public Result<BasePageVO<DataDictSimpleVO>> pageDictList(@Validated @RequestBody BasePageDTO pageDTO) {
        return Result.success(dataDictService.pageList(pageDTO));
    }

    @ApiOperation("创建数据字典")
    @PostMapping("/dict/create")
    public <T> Result<Void> create(@Validated @RequestBody DataDictAddDTO<T> addDTO) {
        dataDictService.add(addDTO);
        return Result.success();
    }

    @ApiOperation("修改数据字典")
    @PostMapping("/dict/update")
    public <T> Result<Void> updateDict(@Validated @RequestBody DataDictUpdateDTO<T> updateDTO) {
        dataDictService.update(updateDTO);
        return Result.success();
    }

    @ApiOperation("删除数据字典")
    @PostMapping("/dict/delete")
    public Result<Void> deleteDict(@RequestBody Long id) {
        boolean result = dataDictService.deleteById(id);
        return result ? Result.success() : Result.error("删除失败");
    }

    @ApiOperation("根据编码查询数据字典详情")
    @GetMapping("/dict/detail/{code}")
    public <T> Result<DataDictDetailVO<T>> getDictByCode(
            @ApiParam(value = "数据字典编码", required = true) @PathVariable String code) {
        DataDictDetailVO<T> detail = dataDictService.getByCode(code);
        return detail != null ? Result.success(detail) : Result.error("数据字典不存在");
    }

    /**
     * 获取组织机构下拉列表
     * 根据当前登录用户的租户信息，返回对应的组织机构列表
     *
     * @return 统一封装的组织机构下拉列表数据
     */
    @RequireLogin
    @ApiOperation(value = "获取组织机构下拉列表", notes = "获取当前租户下的组织机构下拉列表数据，id为org_id(String类型)，value为org_name")
    @GetMapping("/org/combo")
    public Result<List<ComboVO<String>>> getOrgCombo() {
        List<ComboVO<String>> data = orgService.getOrgCombo();
        return Result.success(data);
    }

    /**
     * 根据租户ID获取组织机构下拉列表
     * 如果请求中未传递tenantId或tenantId为空，则使用当前登录用户的租户ID进行查询
     * 如果请求中传递了有效的tenantId，则使用传入的租户ID进行查询
     *
     * @param orgTenantDTO 包含租户ID的DTO对象
     * @return 统一封装的组织机构下拉列表数据
     */
    @RequireLogin
    @ApiOperation(value = "根据租户ID获取组织机构下拉列表", notes = "根据指定租户ID获取组织机构下拉列表数据，如果未传递租户ID则使用当前登录用户的租户ID")
    @PostMapping("/org/combo/tenant")
    public Result<List<ComboVO<String>>> getOrgComboByTenant(@RequestBody OrgTenantDTO orgTenantDTO) {
        List<ComboVO<String>> data = orgService.getOrgComboByTenantId(orgTenantDTO != null ? orgTenantDTO.getTenantId() : null);
        return Result.success(data);
    }

    /**
     * 获取组织机构树
     */
    @RequireLogin
    @ApiOperation(value = "获取组织机构树")
    @GetMapping("/org/tree")
    public Result<List<OrgInfoTreeVO>> getOrgTree() {
        List<OrgInfoTreeVO> data = orgService.getOrgTree();
        return Result.success(data);
    }


    /**
     * 根据租户ID获取组织机构树
     * @param orgTenantDTO 包含租户ID的DTO对象
     * @return 统一封装的组织机构下拉列表数据
     */
    @RequireLogin
    @ApiOperation(value = "根据租户ID获取组织机构树", notes = "根据指定租户ID获取组织机构树数据，如果未传递租户ID则使用当前登录用户的租户ID")
    @PostMapping("/org/tree/tenant")
    public Result<List<OrgInfoTreeVO>> getOrgTreeByTenant(@RequestBody OrgTenantDTO orgTenantDTO) {
        List<OrgInfoTreeVO> data = orgService.getOrgTreeByTenant(orgTenantDTO != null ? orgTenantDTO.getTenantId() : null);
        return Result.success(data);
    }

    /**
     * 分页查询操作日志列表
     *
     * @param queryDTO 查询参数
     * @return 操作日志列表
     */
    @ApiOperation(value = "分页查询操作日志列表",
                  notes = "分页查询操作日志列表，支持多租户权限控制和多种筛选条件。" +
                         "支持按操作人姓名模糊查询、操作时间范围查询。" +
                         "超级管理员可查看所有租户日志，普通用户只能查看自己租户的日志")
    @PostMapping("/log/list")
    public Result<BasePageVO<OperateLogVO>> getOperateLogList(
            @ApiParam(value = "查询参数", required = true) @Valid @RequestBody OperateLogQueryDTO queryDTO) {
        BasePageVO<OperateLogVO> pageInfo = operateLogService.getOperateLogList(queryDTO);
        return Result.success(pageInfo);
    }

    /**
     * 分页查询业务操作日志列表
     *
     * @param queryDTO 查询参数
     * @return 操作日志列表
     */
    @ApiOperation(value = "分页查询业务操作日志列表",
            notes = "分页查询业务操作日志列表")
    @PostMapping("/businessLog/list")
    public Result<BasePageVO<BusinessOperateLogVO>> getBusinessOperateLogList(
            @ApiParam(value = "查询参数", required = true) @Valid @RequestBody BusinessOperateLogQueryDTO queryDTO) {
        BasePageVO<BusinessOperateLogVO> pageInfo = operatorLogService.queryOperatorLog(queryDTO);
        return Result.success(pageInfo);
    }

    // ==================== 审批层级管理接口 ====================

    /**
     * 新增审批层级
     *
     * @param createDTO 创建DTO
     * @return 统一响应结果
     */
    @RequireLogin
    @ApiOperation(value = "新增审批层级", notes = "创建新的审批层级配置")
    @PostMapping("/approval-levels/create")
    public Result<Void> createApprovalLevels(@Validated @RequestBody ApprovalLevelsCreateDTO createDTO) {
        approvalLevelsService.createApprovalLevels(createDTO);
        return Result.success();
    }

    /**
     * 修改审批层级
     *
     * @param updateDTO 更新DTO
     * @return 统一响应结果
     */
    @RequireLogin
    @ApiOperation(value = "修改审批层级", notes = "更新现有的审批层级配置")
    @PostMapping("/approval-levels/update")
    public Result<Void> updateApprovalLevels(@Validated @RequestBody ApprovalLevelsUpdateDTO updateDTO) {
        approvalLevelsService.updateApprovalLevels(updateDTO);
        return Result.success();
    }

    /**
     * 删除审批层级
     *
     * @param id 审批层级ID
     * @return 统一响应结果
     */
    @RequireLogin
    @ApiOperation(value = "删除审批层级", notes = "根据ID删除审批层级配置")
    @GetMapping("/approval-levels/delete/{id}")
    public Result<Void> deleteApprovalLevels(
            @ApiParam(value = "审批层级ID", required = true) @PathVariable Long id) {
        approvalLevelsService.deleteApprovalLevels(id);
        return Result.success();
    }

    /**
     * 分页查询审批层级列表
     * 根据当前登录用户的租户查询审批层级列表
     *
     * @param pageDTO 分页参数
     * @return 分页查询结果
     */
    @RequireLogin
    @ApiOperation(value = "查询审批层级列表", notes = "分页查询审批层级列表，基于当前用户租户进行数据隔离")
    @PostMapping("/approval-levels/list")
    public Result<BasePageVO<ApprovalLevelsVO>> getApprovalLevelsList(@Validated @RequestBody BasePageDTO pageDTO) {
        BasePageVO<ApprovalLevelsVO> result = approvalLevelsService.getApprovalLevelsList(pageDTO);
        return Result.success(result);
    }

    /**
     * 根据租户ID查询审批层级下拉框选项
     * 如果请求中未传递tenantId或tenantId为空，则使用当前登录用户的租户ID进行查询
     * 如果请求中传递了有效的tenantId，则使用传入的租户ID进行查询
     *
     * @param orgTenantDTO 包含租户ID的DTO对象
     * @return 下拉框选项列表
     */
    @RequireLogin
    @ApiOperation(value = "获取审批层级下拉框选项", notes = "根据指定租户ID获取审批层级下拉框选项，如果未传递租户ID则使用当前登录用户的租户ID")
    @PostMapping("/approval-levels/combo/tenant")
    public Result<List<ComboVO<Integer>>> getApprovalLevelsCombo(@RequestBody OrgTenantDTO orgTenantDTO) {
        List<ComboVO<Integer>> data = approvalLevelsService.getApprovalLevelsCombo(orgTenantDTO != null ? orgTenantDTO.getTenantId() : null);
        return Result.success(data);
    }

    // ==================== 地区数据管理接口 ====================

    /**
     * 获取所有省份下拉框数据
     *
     * @return 省份下拉框列表
     */
    @ApiOperation(value = "获取省份下拉框数据", notes = "获取所有省份的下拉框数据，id为省份ID，value为省份名称")
    @GetMapping("/provinces/combo")
    public Result<List<ComboVO<Long>>> getProvinceCombo() {
        List<ComboVO<Long>> data = regionService.getProvinceCombo();
        return Result.success(data);
    }

    /**
     * 根据省份ID获取城市下拉框数据
     *
     * @param provinceId 省份ID
     * @return 城市下拉框列表
     */
    @ApiOperation(value = "获取城市下拉框数据", notes = "根据省份ID获取城市下拉框数据，id为城市ID，value为城市名称")
    @GetMapping("/cities/combo/{provinceId}")
    public Result<List<ComboVO<Long>>> getCityCombo(
            @ApiParam(value = "省份ID", required = true) @PathVariable Long provinceId) {
        if (provinceId == null) {
            return Result.error("省份ID不能为空");
        }
        List<ComboVO<Long>> data = regionService.getCityCombo(provinceId);
        return Result.success(data);
    }

    /**
     * 根据城市ID获取区域下拉框数据
     *
     * @param cityId 城市ID
     * @return 区域下拉框列表
     */
    @ApiOperation(value = "获取区域下拉框数据", notes = "根据城市ID获取区域下拉框数据，id为区域ID，value为区域名称")
    @GetMapping("/areas/combo/{cityId}")
    public Result<List<ComboVO<Long>>> getAreaCombo(
            @ApiParam(value = "城市ID", required = true) @PathVariable Long cityId) {
        if (cityId == null) {
            return Result.error("城市ID不能为空");
        }
        List<ComboVO<Long>> data = regionService.getAreaCombo(cityId);
        return Result.success(data);
    }

    // ==================== 文件上传接口 ====================

    /**
     * 普通文件上传（小文件）
     * 适用于小于10MB的文件，直接上传到OSS
     *
     * @param file 上传的文件
     * @param category 文件分类目录（可选）
     * @return 文件上传结果
     */
    @RequireLogin
    @ApiOperation(value = "普通文件上传", notes = "适用于小于10MB的文件，直接上传到OSS。大于等于10MB的文件请使用断点续传接口")
    @PostMapping("/upload/simple")
    public Result<FileUploadResultVO> uploadSimpleFile(
            @RequestParam("file") @ApiParam(value = "上传的文件", required = true) MultipartFile file,
            @RequestParam(value = "category", required = false) @ApiParam(value = "文件分类目录", example = "documents") String category) {

        try {
            FileUploadResultVO result = ossService.uploadSimpleFile(file, category);
            return Result.success(result);
        } catch (Exception e) {
            log.error("普通文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 普通文件上传（大文件）
     * 适用于小于50MB的文件，直接上传到OSS，支持视频
     *
     * @param file 上传的文件
     * @param category 文件分类目录（可选）
     * @return 文件上传结果
     */
    @RequireLogin
    @ApiOperation(value = "普通大文件上传", notes = "适用于小于50MB的文件，直接上传到OSS。")
    @PostMapping("/upload/large-simple")
    public Result<FileUploadResultVO> uploadLargeSimpleFile(
            @RequestParam("file") @ApiParam(value = "上传的文件", required = true) MultipartFile file,
            @RequestParam(value = "category", required = false) @ApiParam(value = "文件分类目录", example = "documents") String category) {

        try {
            FileUploadResultVO result = ossService.uploadLargeSimpleFile(file, category);
            return Result.success(result);
        } catch (Exception e) {
            log.error("普通大文件上传失败", e);
            return Result.error("大文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量文件上传（小文件）
     * 适用于多个小于10MB的文件，直接上传到OSS
     *
     * @param files 上传的文件列表
     * @param category 文件分类目录（可选）
     * @return 文件上传结果列表
     */
    @RequireLogin
    @ApiOperation(value = "批量文件上传", notes = "适用于多个小于10MB的文件，直接上传到OSS。大于等于10MB的文件请使用断点续传接口")
    @PostMapping("/upload/simple/batch")
    public Result<List<FileUploadResultVO>> uploadSimpleFiles(
            @RequestParam("files") @ApiParam(value = "上传的文件列表", required = true) List<MultipartFile> files,
            @RequestParam(value = "category", required = false) @ApiParam(value = "文件分类目录", example = "documents") String category) {

        try {
            if (files == null || files.isEmpty()) {
                return Result.error("上传文件列表不能为空");
            }
            
            log.info("接收批量文件上传请求: 文件数量={}, 分类={}", files.size(), category);
            List<FileUploadResultVO> results = ossService.uploadSimpleFiles(files, category);
            log.info("批量文件上传完成: 成功上传文件数量={}", results.size());
            
            return Result.success(results);
        } catch (Exception e) {
            log.error("批量文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    // ==================== 断点续传分片上传接口 ====================

    /**
     * 初始化分片上传
     * 用于前端分片上传的第一步，后端自动生成文件路径并返回uploadId
     *
     * @param initDTO 初始化参数
     * @return 分片上传初始化结果
     */
    @RequireLogin
    @ApiOperation(value = "初始化分片上传", notes = "初始化分片上传，后端自动生成文件路径，返回uploadId和文件路径用于后续分片上传")
    @PostMapping("/upload/resumable/init")
    public Result<MultipartUploadInitResult> initializeResumableUpload(
            @ApiParam(value = "初始化参数", required = true) @Valid @RequestBody ResumableUploadInitDTO initDTO) {

        MultipartUploadInitResult result = ossService.initializeResumableUpload(initDTO);
        return Result.success(result);
    }

    /**
     * 生成分片上传的预签名URL
     * 用于前端获取每个分片的上传URL
     *
     * @param uploadId 分片上传ID
     * @param partNumber 分片编号（从1开始）
     * @param expiration 过期时间（秒，默认3600秒）
     * @return 预签名URL
     */
    @RequireLogin
    @ApiOperation(value = "生成分片上传预签名URL", notes = "生成指定分片的预签名上传URL")
    @GetMapping("/upload/resumable/presigned-url/{uploadId}/{partNumber}")
    public Result<String> generatePresignedPartUploadUrl(
            @ApiParam(value = "分片上传ID", required = true) @PathVariable String uploadId,
            @ApiParam(value = "分片编号（从1开始）", required = true) @PathVariable int partNumber,
            @ApiParam(value = "过期时间（秒）", example = "3600") @RequestParam(value = "expiration", defaultValue = "3600") long expiration) {

        String presignedUrl = ossService.generatePresignedPartUploadUrl(uploadId, partNumber, expiration);
        return Result.success(presignedUrl);
    }

    /**
     * 完成分片上传
     * 用于前端所有分片上传完成后，合并分片
     *
     * @param uploadId 分片上传ID
     * @param partETags 分片ETag列表
     * @return 完成结果
     */
    @RequireLogin
    @ApiOperation(value = "完成分片上传", notes = "完成分片上传，合并所有分片为完整文件")
    @PostMapping("/upload/resumable/complete/{uploadId}")
    public Result<MultipartUploadCompleteResult> completeMultipartUpload(
            @ApiParam(value = "分片上传ID", required = true) @PathVariable String uploadId,
            @ApiParam(value = "分片ETag列表", required = true) @Valid @RequestBody List<PartETagInfo> partETags) {

        MultipartUploadCompleteResult result = ossService.completeMultipartUpload(uploadId, partETags);
        return Result.success(result);
    }

    /**
     * 取消分片上传
     * 用于前端取消分片上传，清理已上传的分片
     *
     * @param uploadId 分片上传ID
     * @return 操作结果
     */
    @RequireLogin
    @ApiOperation(value = "取消分片上传", notes = "取消分片上传，清理已上传的分片数据")
    @GetMapping("/upload/resumable/abort/{uploadId}")
    public Result<Void> abortMultipartUpload(
            @ApiParam(value = "分片上传ID", required = true) @PathVariable String uploadId) {

        ossService.abortMultipartUpload(uploadId);
        return Result.success();
    }

    /**
     * 删除文件
     *
     * @param fileDTO 分片上传ID
     * @return 操作结果
     */
    @RequireLogin
    @ApiOperation(value = "删除文件")
    @PostMapping("/deleteFile")
    public Result<Void> deleteFile(
            @ApiParam(value = "删除文件路径", required = true) @RequestBody FileDTO fileDTO) {
        ossService.deleteFile(fileDTO.getUrl());
        return Result.success();
    }

}
