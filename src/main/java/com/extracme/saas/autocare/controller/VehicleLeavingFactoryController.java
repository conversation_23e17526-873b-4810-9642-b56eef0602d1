package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.annotation.RequireLogin;
import com.extracme.saas.autocare.model.dto.VehicleLeavingFactoryQueryDTO;
import com.extracme.saas.autocare.model.dto.VehicleLeavingFactorySubmitDTO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryCountVO;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryDetailsVO;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryResultVO;
import com.extracme.saas.autocare.service.VehicleLeavingFactoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 车辆出厂登记控制器
 *
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Api(tags = "车辆出厂登记管理")
@RestController
@RequestMapping("/api/v1/vehicle-leaving-factory")
@Slf4j
public class VehicleLeavingFactoryController {

    @Autowired
    private VehicleLeavingFactoryService vehicleLeavingFactoryService;

    /**
     * 查询任务信息
     *
     * @param queryDTO 查询条件
     * @return 出厂登记统计信息
     */
    @RequireLogin
    @ApiOperation(value = "查询任务信息", notes = "根据查询条件获取出厂登记统计信息")
    @PostMapping("/list")
    public Result<VehicleLeavingFactoryCountVO> queryLeavingFactoryList(
            @ApiParam(value = "查询条件", required = true) @Valid @RequestBody VehicleLeavingFactoryQueryDTO queryDTO) {
        log.info("查询任务信息，参数：{}", queryDTO);
        VehicleLeavingFactoryCountVO countVO = vehicleLeavingFactoryService.queryLeavingFactoryList(queryDTO);
        return Result.success(countVO);
    }

    /**
     * 导出数据
     *
     * @param queryDTO 查询条件
     * @param response HTTP响应
     */
    @RequireLogin
    @ApiOperation(value = "导出数据", notes = "根据查询条件导出出厂登记数据")
    @PostMapping("/export")
    public Result<Void> exportData(
            @ApiParam(value = "查询条件", required = true) @Valid @RequestBody VehicleLeavingFactoryQueryDTO queryDTO,
            HttpServletResponse response) {
        log.info("导出数据，参数：{}", queryDTO);
        vehicleLeavingFactoryService.exportData(queryDTO, response);
        return Result.success();
    }

    /**
     * 提交出厂登记
     *
     * @param submitDTO 提交信息
     * @return 提交结果
     */
    @RequireLogin
    @ApiOperation(value = "提交出厂登记", notes = "提交车辆出厂登记信息")
    @PostMapping("/submit")
    public Result<Void> submit(
            @ApiParam(value = "提交信息", required = true) @Valid @RequestBody VehicleLeavingFactorySubmitDTO submitDTO) {
        log.info("提交出厂登记，参数：{}", submitDTO);
        vehicleLeavingFactoryService.submit(submitDTO);
        return Result.success();
    }

    /**
     * 查询明细信息
     *
     * @param leavingId 出厂登记ID
     * @return 出厂登记详情
     */
    @RequireLogin
    @ApiOperation(value = "查询明细信息", notes = "根据出厂登记ID查询详情")
    @GetMapping("/details/{id}")
    public Result<VehicleLeavingFactoryDetailsVO> queryDetails(
            @ApiParam(value = "出厂登记ID", required = true) @PathVariable("id") @NotNull(message = "出厂登记ID不能为空") Long leavingId) {
        log.info("查询明细信息，出厂登记ID：{}", leavingId);
        VehicleLeavingFactoryDetailsVO detailsVO = vehicleLeavingFactoryService.queryDetails(leavingId);
        return Result.success(detailsVO);
    }

    /**
     * 根据任务编号查询出厂登记列表
     *
     * @param queryDTO 任务编号
     * @return 出厂登记列表
     */
    @RequireLogin
    @ApiOperation(value = "根据任务编号查询出厂登记列表", notes = "获取指定任务的所有出厂登记记录")
    @PostMapping("/listByTaskNo")
    public Result<List<VehicleLeavingFactoryResultVO>> queryDeliveryListByTaskNo(
            @Valid @RequestBody VehicleLeavingFactoryQueryDTO queryDTO) {
        log.info("根据任务编号查询出厂登记列表，任务编号：{}", queryDTO.getTaskNo());
        List<VehicleLeavingFactoryResultVO> list = vehicleLeavingFactoryService.queryDeliveryListByTaskNo(queryDTO.getTaskNo());
        return Result.success(list);
    }
}