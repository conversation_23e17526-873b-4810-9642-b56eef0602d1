package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.dto.*;
import com.extracme.saas.autocare.model.vo.*;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.service.RepairItemLibraryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 维修项目库管理接口 -4
 */

@Api(tags = "维修项目管理")
@RestController
@RequestMapping("/api/v1/item-library")
@Slf4j
public class RepairItemLibraryController {

    @Autowired
    private RepairItemLibraryService repairItemLibraryService;

    /**
     * 查询维修项目列表
     * @param queryDTO 查询参数
     * @return 维修项目列表
     */
    @ApiOperation(value = "查询维修项目列表", notes = "分页查询列表")
    @PostMapping("/list")
    public Result<BasePageVO<RepairItemLibraryListVO>> queryList(@Valid @RequestBody RepairItemLibraryQueryDTO queryDTO) {
        BasePageVO<RepairItemLibraryListVO> pageVO = repairItemLibraryService.queryItemLibraryList(queryDTO);
        return Result.success(pageVO);
    }

    /**
     * 获取维修项目详情
     */
    @ApiOperation(value = "获取维修项目详情", notes = "获取指定ID的维修项目详细信息")
    @GetMapping("/details/{id}")
    public Result<RepairItemLibraryVO> getItemLibraryDetails(@ApiParam(value = "维修项目id 必传", required = true) @PathVariable Long id) {
        RepairItemLibraryVO repairItemLibrary = repairItemLibraryService.getItemLibraryDetails(id);
        if (repairItemLibrary == null){
            return Result.error("未找到该数据");
        }
        return Result.success(repairItemLibrary);
    }


    @ApiOperation(value = "创建维修项目")
    @PostMapping("/create")
    public Result<Void> createItemLibrary(@Valid @RequestBody RepairItemLibraryCreateDTO createDTO) {
        repairItemLibraryService.createItemLibrary(createDTO);
        return Result.success();
    }


    @ApiOperation(value = "修改维修项目")
    @PostMapping("/update")
    public Result<Void> updateItemLibrary(@Valid @RequestBody RepairItemLibraryUpdateDTO updateDTO) {
        repairItemLibraryService.updateItemLibrary(updateDTO);
        return Result.success();
    }

    @ApiOperation(value = "更新维修项目状态", httpMethod = "POST")
    @PostMapping("/updateStatus")
    public Result<Void> updateStatus(@Valid @RequestBody RepairItemLibraryUpdateStatusDTO updateStatusDTO) {
        repairItemLibraryService.updateItemLibraryStatus(updateStatusDTO);
        return Result.success();
    }


    /**
     * 查询本地维修项目列表
     * @param queryDTO 查询参数
     * @return 维修项目列表
     */
    @ApiOperation(value = "查询本地维修项目列表", notes = "分页查询本地维修项目列表")
    @PostMapping("/localList")
    public Result<BasePageVO<RepairItemLibraryLocalVO>> queryLocalList(@Valid @RequestBody RepairItemLibraryLocalQueryDTO queryDTO) {
        BasePageVO<RepairItemLibraryLocalVO> pageVO = repairItemLibraryService.queryLocalList(queryDTO);
        return Result.success(pageVO);
    }
    /**
     * 获取本地维修项目详情
     */
    @ApiOperation(value = "获取本地维修项目详情", notes = "获取指定ID的本地维修项目详细信息")
    @GetMapping("/localDetails/{id}")
    public Result<RepairItemLibraryLocalVO> getItemLibraryLocalDetails(@ApiParam(value = "维修项目id 必传", required = true) @PathVariable Long id) {
        RepairItemLibraryLocalVO repairItemLibraryLocal = repairItemLibraryService.getItemLibraryLocalDetails(id);
        if (repairItemLibraryLocal == null){
            return Result.error("未找到该数据");
        }
        return Result.success(repairItemLibraryLocal);
    }


    @ApiOperation(value = "创建本地维修项目")
    @PostMapping("/createLocal")
    public Result<Void> createItemLibraryLocal(@Valid @RequestBody RepairItemLibraryLocalCreateDTO createDTO) {
        repairItemLibraryService.createItemLibraryLocal(createDTO);
        return Result.success();
    }


    @ApiOperation(value = "修改本地维修项目")
    @PostMapping("/updateLocal")
    public Result<Void> updateItemLibraryLocal(@Valid @RequestBody RepairItemLibraryLocalUpdateDTO updateDTO) {
        repairItemLibraryService.updateItemLibraryLocal(updateDTO);
        return Result.success();
    }

    @ApiOperation(value = "更新本地维修项目状态")
    @PostMapping("/updateLocalStatus")
    public Result<Void> updateLocalStatus(@Valid @RequestBody RepairItemLibraryUpdateStatusDTO updateStatusDTO) {
        repairItemLibraryService.updateItemLibraryLocalStatus(updateStatusDTO);
        return Result.success();
    }

    /**
     * 查询待定损配件库列表
     * @param queryDTO 查询参数
     * @return 维修项目列表
     */
    @ApiOperation(value = "查询待定损配件库列表", notes = "分页查询待定损配件库列表")
    @PostMapping("/queryLibraryCheckList")
    public Result<BasePageVO<RepairItemLibraryCheckListVO>> queryLibraryCheckList(@Valid @RequestBody RepairItemLibraryCheckQueryDTO queryDTO) {
        BasePageVO<RepairItemLibraryCheckListVO> pageVO = repairItemLibraryService.queryLibraryCheckList(queryDTO);
        return Result.success(pageVO);
    }

    /**
     * 获取导入模板
     */
    @ApiOperation(value = "获取导入模板")
    @GetMapping("/downloadTemplateExcel/{type}")
    public Result<String> downloadTemplateExcel(@ApiParam(value = "模板类型1：创建 2：修改  ", required = true) @PathVariable Integer type) {
        if (type == 1){
            return Result.success("https://evcard.oss-cn-shanghai.aliyuncs.com/prod/mtc/excel_Import_template/批量创建维修项目模板.xlsx");
        } else if (type == 2){
            return Result.success("https://evcard.oss-cn-shanghai.aliyuncs.com/prod/mtc/excel_Import_template/批量修改维修项目模板.xlsx");
        } else {
            return Result.error("参数错误");
        }
    }


    /**
     * 批量导入维修项目
     *
     * @param file 上传的Excel文件，支持.xlsx格式
     * @param type 操作类型：1-创建，2-修改
     * @return 导入结果
     */
    @ApiOperation(value = "批量导入维修项目", notes = "前端直接传递Excel文件流，支持批量创建和修改维修项目")
    @PostMapping("/importRepairItemLibrary")
    public Result<Void> importRepairItemLibrary(
            @ApiParam(value = "Excel文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "操作类型：1-创建，2-修改", required = true) @RequestParam("type") Integer type) {

        log.info("开始批量导入维修项目，文件名: {}, 操作类型: {}", file.getOriginalFilename(), type);

        try {
            // 校验文件格式
            if (file.isEmpty()) {
                return Result.error("上传文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || !fileName.toLowerCase().endsWith(".xlsx")) {
                return Result.error("只支持.xlsx格式的Excel文件");
            }

            // 校验操作类型
            if (type == null || (type != 1 && type != 2)) {
                return Result.error("操作类型参数错误，请传入1（创建）或2（修改）");
            }

            // 执行导入操作
            if (type == 1) {
                // 批量创建维修项目
                repairItemLibraryService.importRepairItemLibraryCreate(file.getInputStream());
                log.info("批量创建维修项目完成，文件: {}", fileName);
            } else {
                // 批量修改维修项目
                repairItemLibraryService.importRepairItemLibraryUpdate(file.getInputStream());
                log.info("批量修改维修项目完成，文件: {}", fileName);
            }

            return Result.success();

        } catch (Exception e) {
            log.error("批量导入维修项目失败，文件: {}, 错误: {}", file.getOriginalFilename(), e.getMessage(), e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出
     * @param dto
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value = "导出维修项目列表")
    @PostMapping(value = "exportRepairLibrary")
    public Result<Void> exportRepairItemInfo(@RequestBody RepairItemLibraryQueryDTO dto, HttpServletRequest request, HttpServletResponse response) {
        repairItemLibraryService.exportRepairLibrary(dto, request, response);
        return Result.success();
    }
}