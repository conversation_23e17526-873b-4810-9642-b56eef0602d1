package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.*;
import com.extracme.saas.autocare.model.dto.repairTask.GetFirstPageInfoDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskCreateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskProcessQueryDTO;
import com.extracme.saas.autocare.model.vo.*;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 维修任务管理接口
 */
@Slf4j
@Api(tags = "维修任务管理")
@RestController
@RequestMapping("/api/v1/repair-task")
public class RepairTaskController {

    @Autowired
    private RepairTaskService repairTaskService;

    @Autowired
    private RepairQuoteService repairQuoteService;

    @Autowired
    private LossAssessmentService lossAssessmentService;

    @Autowired
    private InRepairService inRepairService;

    @Autowired
    private VehicleInspectionService vehicleInspectionService;

    /**
     * 创建维修任务
     * 
     * @param createDTO 创建维修任务参数
     */
    @ApiOperation(value = "创建维修任务")
    @PostMapping("/create")
    public Result<Long> createRepairTask(@Valid @RequestBody RepairTaskCreateDTO createDTO) {
        Long taskId = repairTaskService.createRepairTask(createDTO);
        return Result.success(taskId);
    }

    /**
     * 流程查询-查询维修任务流程列表
     * 
     * @param queryDTO 查询参数
     * @return 维修任务列表
     */
    @ApiOperation(value = "流程查询-查询维修任务流程列表", notes = "分页查询列表")
    @PostMapping("/processList")
    public Result<BasePageVO<RepairTaskProcessListVO>> queryRepairTaskProcessList(
            @Valid @RequestBody RepairTaskProcessQueryDTO queryDTO) {
        BasePageVO<RepairTaskProcessListVO> pageVO = repairTaskService.queryRepairTaskProcessList(queryDTO);
        return Result.success(pageVO);
    }

    /**
     * 流程查询-导出
     * @param dto
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value = "流程查询-导出")
    @PostMapping(value = "/exportProcessList")
    public Result<Void> exportRepairItemInfo(@RequestBody RepairTaskProcessQueryDTO dto, HttpServletRequest request, HttpServletResponse response) {
        repairTaskService.exportRepairTaskProcessList(dto, request, response);
        return Result.success();
    }

    /**
     * 各环节-查询维修任务列表
     * 
     * @param queryDTO 查询参数
     * @return 维修任务列表
     */
    @ApiOperation(value = "各环节-查询维修任务列表", notes = "分页查询列表")
    @PostMapping("/listResult")
    public Result<MtcTaskListResultVO> queryRepairTaskList(@Valid @RequestBody RepairTaskListQueryDTO queryDTO) {
        MtcTaskListResultVO resultVO = repairTaskService.queryRepairTaskList(queryDTO);
        return Result.success(resultVO);
    }

    /**
     * 获取维修任务详情
     */
    @ApiOperation(value = "获取维修任务详情", notes = "获取指定ID的维修任务详细信息")
    @GetMapping("/details/{id}")
    public Result<RepairTaskDetailsVO> getRepairTaskDetails(
            @ApiParam(value = "维修任务id 必传", required = true) @PathVariable Long id) {
        RepairTaskDetailsVO repairTask = repairTaskService.getRepairTaskDetails(id);
        if (repairTask == null) {
            return Result.error("未找到该数据");
        }
        return Result.success(repairTask);
    }

    /**
     * 待分配-选择修理厂
     *
     * @param selectDTO 选择修理厂请求参数
     * @return 操作结果
     */
    @ApiOperation(value = "待分配-选择修理厂", notes = "为维修任务指定修理厂")
    @PostMapping("/pendingAssignment/selectRepairDepot")
    public Result<Void> selectRepairDepot(
            @ApiParam(value = "选择修理厂请求参数", required = true) @Valid @RequestBody RepairDepotSelectDTO selectDTO) {
        repairTaskService.selectRepairDepot(selectDTO.getTaskId(), selectDTO.getRepairDepotId());
        return Result.success();
    }

    /**
     * 维修报价-保存信息
     *
     * @param updateDTO 维修报价信息参数
     * @return 保存结果
     */
    @ApiOperation(value = "维修报价-保存信息", notes = "保存维修报价信息，不提交审核")
    @PostMapping("/repairQuotation/save")
    public Result<Void> saveRepairQuote(
            @ApiParam(value = "维修报价信息", required = true) @Valid @RequestBody RepairQuoteUpdateDTO updateDTO) {
        repairQuoteService.saveRepairQuote(updateDTO);
        return Result.success();
    }

    /**
     * 维修报价-提交审核
     *
     * @param submitDTO 提交维修报价审核参数
     * @return 提交结果
     */
    @ApiOperation(value = "维修报价-提交审核", notes = "提交维修报价信息")
    @PostMapping("/repairQuotation/submit")
    public Result<Void> submitRepairQuote(@Valid @RequestBody RepairQuoteSubmitDTO submitDTO) {
        return repairQuoteService.submitRepairQuote(submitDTO);
    }

    /**
     * 核损核价-平级移交
     *
     * @param taskNoDTO 包含维修任务编号的DTO
     * @return 操作结果
     */
    @ApiOperation(value = "核损核价-平级移交", notes = "将当前核损核价任务移交给其他人处理")
    @PostMapping("/lossAssessment/transfer")
    public Result<Void> transferLossAssessment(
            @ApiParam(value = "包含维修任务编号的DTO", required = true) @Valid @RequestBody TaskNoDTO taskNoDTO) {
        lossAssessmentService.updateTransferTask(taskNoDTO.getTaskNo());
        return Result.success();
    }

    /**
     * 核损核价-清除任务占据人
     *
     * @param taskNoDTO 包含维修任务编号的DTO
     * @return 操作结果
     */
    @ApiOperation(value = "核损核价-清除任务占据人", notes = "清除核损核价任务的占据人，使任务可以被其他人处理")
    @PostMapping("/lossAssessment/clearOwner")
    public Result<Void> clearLossAssessmentOwner(
            @ApiParam(value = "包含维修任务编号的DTO", required = true) @Valid @RequestBody TaskNoDTO taskNoDTO) {
        lossAssessmentService.clearOwner(taskNoDTO.getTaskNo());
        return Result.success();
    }

    /**
     * 核损核价-保存信息
     *
     * @param updatedDTO 核损核价保存参数
     * @return 审核结果
     */
    @ApiOperation(value = "核损核价-保存信息", notes = "保存核损核价信息")
    @PostMapping("/lossAssessment/save")
    public Result<Void> saveLossAssessment(@Valid @RequestBody LossAssessmentUpdateDTO updatedDTO) {
        lossAssessmentService.saveLossAssessment(updatedDTO);
        return Result.success();
    }

    /**
     * 核损核价-审核通过
     *
     * @param approveDTO 核损核价审核参数
     * @return 审核结果
     */
    @ApiOperation(value = "核损核价-审核通过", notes = "审核通过核损核价信息")
    @PostMapping("/lossAssessment/approve")
    public Result<Void> approveLossAssessment(@Valid @RequestBody LossAssessmentApproveDTO approveDTO) {
        lossAssessmentService.approveLossAssessment(approveDTO);
        return Result.success();
    }

    /**
     * 核损核价-退回
     *
     * @param rejectDTO 核损核价审核参数
     * @return 退回结果
     */
    @ApiOperation(value = "核损核价-退回", notes = "审核退回核损核价信息")
    @PostMapping("/lossAssessment/reject")
    public Result<Void> approveLossAssessment(@Valid @RequestBody LossAssessmentRejectDTO rejectDTO) {
        lossAssessmentService.rejectLossAssessment(rejectDTO);
        return Result.success();
    }

    /**
     * 车辆维修-保存信息
     *
     * @param updateDTO 车辆维修更新参数
     * @return 操作结果
     */
    @ApiOperation(value = "车辆维修-保存信息", notes = "车辆维修-保存信息")
    @PostMapping("/vehicleRepair/save")
    public Result<Void> saveVehicleInRepair(
            @ApiParam(value = "车辆维修活动更新信息", required = true) @Valid @RequestBody InRepairUpdateDTO updateDTO) {
        inRepairService.saveVehicleInRepair(updateDTO);
        return Result.success();
    }

    /**
     * 车辆维修-申请验收
     *
     * @param submitDTO 申请验收提交dto
     * @return 操作结果
     */
    @ApiOperation(value = "车辆维修-申请验收", notes = "车辆维修完成后申请验收")
    @PostMapping("/vehicleRepair/requestInspection")
    public Result<Void> changeVehicleCheck(
            @ApiParam(value = "车辆维修活动更新信息", required = true) @Valid @RequestBody InRepairSubmitDTO submitDTO) {
        inRepairService.changeVehicleCheck(submitDTO);
        return Result.success();
    }

    /**
     * 车辆验收-验收通过
     *
     * @param approveDTO 验收通过信息DTO
     * @return 操作结果
     */
    @ApiOperation(value = "车辆验收-验收通过", notes = "处理车辆维修任务的验收通过流程")
    @PostMapping("/vehicleInspection/approve")
    public Result<Void> approveVehicleInspection(
            @ApiParam(value = "验收通过信息", required = true) @Valid @RequestBody VehicleInspectionApproveDTO approveDTO) {
        vehicleInspectionService.approveInspection(approveDTO);
        return Result.success();
    }

    /**
     * 车辆验收-验收不通过
     *
     * @param rejectDTO 验收不通过信息DTO
     * @return 操作结果
     */
    @ApiOperation(value = "车辆验收-验收不通过", notes = "处理车辆维修任务的验收不通过流程")
    @PostMapping("/vehicleInspection/reject")
    public Result<Void> rejectVehicleInspection(
            @ApiParam(value = "验收不通过信息", required = true) @Valid @RequestBody VehicleInspectionRejectDTO rejectDTO) {
        vehicleInspectionService.rejectInspection(rejectDTO);
        return Result.success();
    }

    /**
     * 查看任务备注
     *
     * @param repairRemarkDTO 维修备注信息
     * @return 操作结果
     */
    @ApiOperation(value = "查看任务备注", notes = "查看任务备注")
    @PostMapping("/remark/view")
    public Result<List<ViewRepairRemarkVO>> viewRepairRemark(
            @ApiParam(value = "维修备注信息", required = true) @Valid @RequestBody RepairRemarkDTO repairRemarkDTO) {
        try {
            return Result.success(repairTaskService.viewRepairRemark(repairRemarkDTO.getTaskNo()));
        } catch (Exception e) {
            return Result.error("系统异常，请稍后重试");
        }
    }

    /**
     * 添加维修备注
     *
     * @param repairRemarkDTO 维修备注信息
     * @return 操作结果
     */
    @ApiOperation(value = "添加维修备注", notes = "为维修任务添加备注信息")
    @PostMapping("/remark/add")
    public Result<Void> addRepairRemark(
            @ApiParam(value = "维修备注信息", required = true) @Valid @RequestBody RepairRemarkDTO repairRemarkDTO) {
        try {
            repairTaskService.addRepairRemark(repairRemarkDTO);
            return Result.success();
        } catch (Exception e) {
            return Result.error("系统异常，请稍后重试");
        }
    }

    /**
     * 删除维修备注
     *
     * @param repairRemarkDTO   备注ID
     * @return 操作结果
     */
    @ApiOperation(value = "删除维修备注", notes = "删除指定ID的维修备注")
    @PostMapping("/remark/delete")
    public Result<Void> deleteRepairRemark(
            @ApiParam(value = "维修备注信息", required = true) @Valid @RequestBody RepairRemarkDTO repairRemarkDTO) {
        try {
            repairTaskService.deleteRepairRemark(repairRemarkDTO.getId(), repairRemarkDTO.getActivityCode());
            return Result.success();
        } catch (Exception e) {
            log.error("删除维修备注失败", e);
            return Result.error("系统异常，请稍后重试");
        }
    }

    /**
     * 首页看板三个数据统计
     * 
     * @param getFirstPageInfoDTO 查询参数
     * @return 首页信息
     */
    @ApiOperation(value = "获取首页看板三个数据统计", notes = "获取首页看板三个数据统计")
    @PostMapping("/vehicleRepairStatistics")
    public Result<VehicleRepairStatisticsVO> getVehicleRepairStatistics(
            @ApiParam(value = "查询参数", required = true) @Valid @RequestBody GetFirstPageInfoDTO getFirstPageInfoDTO) {
        VehicleRepairStatisticsVO vehicleRepairStatisticsVO = repairTaskService
                .getVehicleRepairStatistics(getFirstPageInfoDTO);
        return Result.success(vehicleRepairStatisticsVO);
    }

    /**
     * 获取首页信息
     * 
     * @param getFirstPageInfoDTO 查询参数
     * @return 首页信息
     */
    @ApiOperation(value = "获取首页信息", notes = "分页查询首页信息")
    @PostMapping("/firstPage")
    public Result<BasePageVO<RepairDepotFirstPageInfoVO>> getFirstPageInfo(
            @ApiParam(value = "查询参数", required = true) @Valid @RequestBody GetFirstPageInfoDTO getFirstPageInfoDTO) {
        BasePageVO<RepairDepotFirstPageInfoVO> pageVO = repairTaskService.getFirstPageInfo(getFirstPageInfoDTO);
        return Result.success(pageVO);
    }

    /**
     * 首页信息-导出
     *
     * @param dto
     * @param response
     * @return
     */
    @ApiOperation(value = "首页信息-导出")
    @PostMapping(value = "/exportFirstPageInfo")
    public Result<Void> exportFirstPageInfo(@RequestBody GetFirstPageInfoDTO dto, HttpServletResponse response) {
        repairTaskService.exportFirstPageInfo(dto, response);
        return Result.success();
    }

    /**
     * 获取修理厂在修车辆明细信息
     * 
     * @param getFirstPageInfoDTO 查询参数
     * @return 在修车辆明细列表
     */
    @ApiOperation(value = "获取修理厂在修车辆明细信息", notes = "分页查询修理厂当前在修车辆的详细信息")
    @PostMapping("/inRepairingInfo")
    public Result<BasePageVO<RepairDepotInRepairingVO>> getRepairDepotInRepairingInfo(
            @ApiParam(value = "查询参数", required = true) @Valid @RequestBody GetFirstPageInfoDTO getFirstPageInfoDTO) {
        BasePageVO<RepairDepotInRepairingVO> pageVO = repairTaskService
                .getRepairDepotInRepairingInfo(getFirstPageInfoDTO);
        return Result.success(pageVO);
    }

    /**
     * 查询指定车辆当前正在进行中的维修任务列表
     *
     * @param vin 车架号
     * @return 正在进行中的维修任务列表
     */
    @ApiOperation(value = "查询指定车辆当前正在进行中的维修任务列表", notes = "根据车架号(VIN)查询当前正在进行中的维修任务列表")
    @GetMapping("/currentRepairTasks/{vin}")
    public Result<List<RepairTaskCurrentListVO>> queryCurrentRepairTasksByVin(
            @ApiParam(value = "车架号(VIN) 必传", required = true) @PathVariable String vin) {
        try {
            if (StringUtils.isBlank(vin)) {
                return Result.error("车架号不能为空");
            }
            
            log.info("查询指定车辆当前正在进行中的维修任务列表, 车架号: {}", vin);
            List<RepairTaskCurrentListVO> repairTasks = repairTaskService.queryCurrentRepairTasksByVin(vin);
            return Result.success(repairTasks);
        } catch (BusinessException e) {
            log.error("查询指定车辆当前正在进行中的维修任务列表业务异常, 车架号: {}, 错误信息: {}", vin, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询指定车辆当前正在进行中的维修任务列表系统异常, 车架号: {}", vin, e);
            return Result.error("系统异常，请稍后重试");
        }
    }

    /**
     * 查询车辆历次维修记录
     *
     * @param queryDTO 查询条件
     * @return 车辆维修记录列表
     */
    @ApiOperation(value = "查询车辆历次维修记录", notes = "查询车辆历次维修记录")
    @PostMapping("/vehicleRepairRecord")
    public Result<BasePageVO<VehicleRepairRecordVO>> queryVehicleRepairRecord(
            @ApiParam(value = "查询条件", required = true) @Valid @RequestBody VehicleRepairRecordQueryDTO queryDTO) {
        BasePageVO<VehicleRepairRecordVO> result = repairTaskService.queryVehicleRepairRecord(queryDTO);
        return Result.success(result);
    }
}
