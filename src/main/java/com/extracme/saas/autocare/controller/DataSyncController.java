package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.dto.VehicleInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.VehicleModelSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.OrgInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.vo.DataSyncResultVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.service.DataSyncService;
import com.extracme.saas.autocare.util.IpUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 数据同步控制器
 *
 * <p>提供多租户数据同步服务的REST API接口，支持：</p>
 * <ul>
 *   <li>车辆信息批量同步</li>
 *   <li>车型信息批量同步</li>
 *   <li>机构信息批量同步</li>
 *   <li>同步状态查询</li>
 *   <li>失败任务重试</li>
 *   <li>支持的表查询</li>
 * </ul>
 *
 * <p>所有同步操作都会自动判断INSERT或UPDATE，无需指定操作类型。</p>
 * <p>数据传输采用明文方式，便于联调测试和数据观察。</p>
 * <p>每个同步表都有专用的API接口和参数校验逻辑。</p>
 * <p>通过同步密钥(syncKey)进行租户身份识别和验证。</p>
 *
 * <AUTHOR> Team
 * @version 2.0
 * @since 2024-01-01
 */
@Slf4j
@Api(tags = "数据同步管理", description = "多租户数据同步服务API")
@RestController
@RequestMapping("/api/v1/data-sync")
public class DataSyncController {

    @Autowired
    private DataSyncService dataSyncService;



    /**
     * 批量同步车辆信息数据
     *
     * <p>第三方系统通过此接口批量同步车辆信息数据到mtc_vehicle_info表中。</p>
     *
     * <h3>功能特性：</h3>
     * <ul>
     *   <li>通过同步密钥(syncKey)进行租户身份识别和验证</li>
     *   <li>数据传输采用明文JSON格式，便于联调测试</li>
     *   <li>专门针对车辆信息的参数校验</li>
     *   <li>支持批量处理，提高同步效率</li>
     *   <li>每条数据独立处理，部分失败不影响其他数据</li>
     *   <li>自动判断INSERT或UPDATE操作（基于车架号）</li>
     *   <li>提供详细的成功/失败统计</li>
     * </ul>
     *
     * @param requestDTO 车辆信息批量同步请求数据
     * @param request HTTP请求对象，用于获取客户端IP
     * @return 批量同步结果，包含总数、成功数、失败数和失败详情
     */
    @ApiOperation(
        value = "批量同步车辆信息数据",
        notes = "第三方系统通过此接口批量同步车辆信息数据，系统自动判断INSERT或UPDATE操作",
        response = DataSyncResultVO.class
    )
    @PostMapping("/vehicle-info/batch")
    public Result<DataSyncResultVO> syncVehicleInfoBatch(
            @ApiParam(value = "车辆信息批量同步请求", required = true)
            @Validated @RequestBody VehicleInfoSyncRequestDTO requestDTO,
            HttpServletRequest request) {

        String sourceIp = IpUtils.getClientIpAddress(request);
        log.info("接收到车辆信息批量同步请求，数据量：{}，来源IP：{}",
                requestDTO.getBatchData() != null ? requestDTO.getBatchData().size() : 0, sourceIp);

        try {
            DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, sourceIp);

            log.info("车辆信息批量同步完成，批次号：{}，状态：{}，总数：{}，成功：{}，失败：{}",
                    result.getBatchNo(), result.getSyncStatus(), result.getTotalCount(),
                    result.getSuccessCount(), result.getFailedCount());

            return Result.success(result);

        } catch (Exception e) {
            log.error("车辆信息批量同步失败：{}", e.getMessage(), e);
            return Result.error("车辆信息批量同步失败：" + e.getMessage());
        }
    }

    /**
     * 批量同步车型信息数据
     *
     * <p>第三方系统通过此接口批量同步车型信息数据到mtc_vehicle_model表中。</p>
     *
     * <h3>功能特性：</h3>
     * <ul>
     *   <li>通过同步密钥(syncKey)进行租户身份识别和验证</li>
     *   <li>数据传输采用明文JSON格式，便于联调测试</li>
     *   <li>专门针对车型信息的参数校验</li>
     *   <li>支持批量处理，提高同步效率</li>
     *   <li>每条数据独立处理，部分失败不影响其他数据</li>
     *   <li>自动判断INSERT或UPDATE操作（基于车型名称）</li>
     *   <li>提供详细的成功/失败统计</li>
     * </ul>
     *
     * @param requestDTO 车型信息批量同步请求数据
     * @param request HTTP请求对象，用于获取客户端IP
     * @return 批量同步结果，包含总数、成功数、失败数和失败详情
     */
    @ApiOperation(
        value = "批量同步车型信息数据",
        notes = "第三方系统通过此接口批量同步车型信息数据，系统自动判断INSERT或UPDATE操作",
        response = DataSyncResultVO.class
    )
    @PostMapping("/vehicle-model/batch")
    public Result<DataSyncResultVO> syncVehicleModelBatch(
            @ApiParam(value = "车型信息批量同步请求", required = true)
            @Validated @RequestBody VehicleModelSyncRequestDTO requestDTO,
            HttpServletRequest request) {

        String sourceIp = IpUtils.getClientIpAddress(request);
        log.info("接收到车型信息批量同步请求，数据量：{}，来源IP：{}",
                requestDTO.getBatchData() != null ? requestDTO.getBatchData().size() : 0, sourceIp);

        try {
            DataSyncResultVO result = dataSyncService.syncVehicleModelBatch(requestDTO, sourceIp);

            log.info("车型信息批量同步完成，批次号：{}，状态：{}，总数：{}，成功：{}，失败：{}",
                    result.getBatchNo(), result.getSyncStatus(), result.getTotalCount(),
                    result.getSuccessCount(), result.getFailedCount());

            return Result.success(result);

        } catch (Exception e) {
            log.error("车型信息批量同步失败：{}", e.getMessage(), e);
            return Result.error("车型信息批量同步失败：" + e.getMessage());
        }
    }

    /**
     * 批量同步机构信息数据
     *
     * <p>第三方系统通过此接口批量同步机构信息数据到mtc_org_info表中。</p>
     *
     * <h3>功能特性：</h3>
     * <ul>
     *   <li>通过同步密钥(syncKey)进行租户身份识别和验证</li>
     *   <li>数据传输采用明文JSON格式，便于联调测试</li>
     *   <li>专门针对机构信息的参数校验</li>
     *   <li>支持批量处理，提高同步效率</li>
     *   <li>每条数据独立处理，部分失败不影响其他数据</li>
     *   <li>自动判断INSERT或UPDATE操作（基于机构ID）</li>
     *   <li>提供详细的成功/失败统计</li>
     * </ul>
     *
     * @param requestDTO 机构信息批量同步请求数据
     * @param request HTTP请求对象，用于获取客户端IP
     * @return 批量同步结果，包含总数、成功数、失败数和失败详情
     */
    @ApiOperation(
        value = "批量同步机构信息数据",
        notes = "第三方系统通过此接口批量同步机构信息数据，系统自动判断INSERT或UPDATE操作",
        response = DataSyncResultVO.class
    )
    @PostMapping("/org-info/batch")
    public Result<DataSyncResultVO> syncOrgInfoBatch(
            @ApiParam(value = "机构信息批量同步请求", required = true)
            @Validated @RequestBody OrgInfoSyncRequestDTO requestDTO,
            HttpServletRequest request) {

        String sourceIp = IpUtils.getClientIpAddress(request);
        log.info("接收到机构信息批量同步请求，数据量：{}，来源IP：{}",
                requestDTO.getBatchData() != null ? requestDTO.getBatchData().size() : 0, sourceIp);

        try {
            DataSyncResultVO result = dataSyncService.syncOrgInfoBatch(requestDTO, sourceIp);

            log.info("机构信息批量同步完成，批次号：{}，状态：{}，总数：{}，成功：{}，失败：{}",
                    result.getBatchNo(), result.getSyncStatus(), result.getTotalCount(),
                    result.getSuccessCount(), result.getFailedCount());

            return Result.success(result);

        } catch (Exception e) {
            log.error("机构信息批量同步失败：{}", e.getMessage(), e);
            return Result.error("机构信息批量同步失败：" + e.getMessage());
        }
    }

    /**
     * 查询同步状态
     *
     * <p>根据批次号查询数据同步的状态和结果详情。</p>
     *
     * <h3>返回信息：</h3>
     * <ul>
     *   <li>同步状态：SUCCESS、FAILED、PARTIAL_SUCCESS</li>
     *   <li>统计信息：总数、成功数、失败数</li>
     *   <li>时间信息：开始时间、结束时间、耗时</li>
     *   <li>失败详情：失败数据的错误信息</li>
     * </ul>
     *
     * <h3>状态说明：</h3>
     * <ul>
     *   <li>SUCCESS：所有数据同步成功</li>
     *   <li>FAILED：所有数据同步失败</li>
     *   <li>PARTIAL_SUCCESS：部分数据同步成功</li>
     * </ul>
     *
     * @param batchNo 同步批次号，由同步接口返回
     * @return 同步状态和结果详情
     */
    @ApiOperation(
        value = "查询同步状态",
        notes = "根据批次号查询数据同步的状态和结果，包含详细的统计信息和失败详情",
        response = DataSyncResultVO.class
    )
    @GetMapping("/status/{batchNo}")
    public Result<DataSyncResultVO> querySyncStatus(
            @ApiParam(value = "同步批次号", required = true, example = "SYNC_1640995200000_1234")
            @PathVariable String batchNo) {

        log.info("查询同步状态，批次号：{}", batchNo);

        try {
            DataSyncResultVO result = dataSyncService.querySyncStatus(batchNo);

            log.info("查询同步状态完成，批次号：{}，状态：{}", batchNo, result.getSyncStatus());

            return Result.success(result);

        } catch (Exception e) {
            log.error("查询同步状态失败：{}", e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }



    /**
     * 获取支持的同步表列表
     *
     * <p>获取系统当前支持的所有可同步表名列表。</p>
     *
     * <h3>当前支持的表：</h3>
     * <ul>
     *   <li><strong>mtc_vehicle_info</strong> - 车辆信息表</li>
     *   <li><strong>mtc_vehicle_model</strong> - 车型信息表</li>
     *   <li><strong>mtc_org_info</strong> - 机构信息表</li>
     * </ul>
     *
     * <p>在调用同步接口前，建议先调用此接口确认目标表是否支持同步。</p>
     *
     * @return 支持的表名数组
     */
    @ApiOperation(
        value = "获取支持的同步表列表",
        notes = "获取系统支持的所有可同步表名列表，用于验证目标表是否支持同步"
    )
    @GetMapping("/supported-tables")
    public Result<String[]> getSupportedTables() {

        log.info("查询支持的同步表列表");

        try {
            // 返回支持的表名列表
            String[] supportedTables = {
                "mtc_vehicle_info",
                "mtc_vehicle_model",
                "mtc_org_info"
            };

            log.info("返回支持的同步表列表，共{}个表", supportedTables.length);
            return Result.success(supportedTables);

        } catch (Exception e) {
            log.error("查询支持的同步表列表失败：{}", e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }




}
