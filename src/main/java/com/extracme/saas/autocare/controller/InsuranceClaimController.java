package com.extracme.saas.autocare.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.extracme.saas.autocare.annotation.RequireLogin;
import com.extracme.saas.autocare.config.SystemConfig;
import com.extracme.saas.autocare.model.dto.FileDTO;
import com.extracme.saas.autocare.model.dto.InsuranceClaimCreateDTO;
import com.extracme.saas.autocare.model.dto.InsuranceClaimQueryDTO;
import com.extracme.saas.autocare.model.dto.InsuranceClaimQueryDetailDTO;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyCreateDTO;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyQueryDTO;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyUpdateDTO;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyUpdateStatusDTO;
import com.extracme.saas.autocare.model.entity.AttachmentInfo;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.FileUploadResultVO;
import com.extracme.saas.autocare.model.vo.InsuranceClaimDetailsVO;
import com.extracme.saas.autocare.model.vo.InsuranceClaimListVO;
import com.extracme.saas.autocare.model.vo.InsuranceCompanyVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.InsuranceCompanyService;
import com.extracme.saas.autocare.service.OssService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.util.UrlToMultipartFileUtil;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api(tags = "保险索赔管理")
@RestController
@RequestMapping("/api/v1/insurance")
public class InsuranceClaimController {

    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    @Autowired
    private SystemConfig systemConfig;

    @Autowired
    private OssService ossService;

    /**
     * 保司管理列表
     * @param queryDTO 查询参数
     * @return 保司管理列表
     */
    @ApiOperation(value = "保司配置-查询保司信息列表", notes = "分页查询列表")
    @PostMapping("/companyList")
    public Result<BasePageVO<InsuranceCompanyVO>> queryInsuranceCompanyList(@Valid @RequestBody InsuranceCompanyQueryDTO queryDTO) {
        BasePageVO<InsuranceCompanyVO> pageVO = insuranceCompanyService.queryInsuranceCompanyList(queryDTO);
        return Result.success(pageVO);
    }


    /**
     * 获取保司信息详情
     */
    @ApiOperation(value = "保司配置-获取保司信息详情", notes = "获取指定ID的保司信息详细信息")
    @GetMapping("/companyDetails/{id}")
    public Result<InsuranceCompanyVO> getInsuranceCompanyDetails(@ApiParam(value = "保司信息id 必传", required = true) @PathVariable Long id) {
        InsuranceCompanyVO insuranceCompanyVO = insuranceCompanyService.getInsuranceCompanyDetails(id);
        if (insuranceCompanyVO == null) {
            return Result.error("未找到该数据");
        }
        return Result.success(insuranceCompanyVO);
    }

    /**
     * 创建保司信息
     * @param createDTO 创建保司信息
     */
    @ApiOperation(value = "保司配置-创建保司信息")
    @PostMapping("/createCompany")
    public Result<Void> createInsuranceCompany(@Valid @RequestBody InsuranceCompanyCreateDTO createDTO) {
        insuranceCompanyService.createInsuranceCompany(createDTO);
        return Result.success();
    }

    /**
     * 修改保司信息
     * @param updateDTO 保司信息修改信息
     */
    @ApiOperation(value = "保司配置-修改保司信息")
    @PostMapping("/updateCompany")
    public Result<Void> updateInsuranceCompany(@Valid @RequestBody InsuranceCompanyUpdateDTO updateDTO) {
        insuranceCompanyService.updateInsuranceCompany(updateDTO);
        return Result.success();
    }

    @ApiOperation(value = "保司配置-删除保司信息")
    @PostMapping("/deleteCompany")
    public Result<Void> deleteInsuranceCompany(@ApiParam(value = "保司信息ID", required = true) @RequestBody Long id) {
        insuranceCompanyService.deleteInsuranceCompany(id);
        return Result.success();
    }

    /**
     * 更新保司信息状态
     */
    @ApiOperation(value = "保司配置-更新保司信息状态", httpMethod = "POST")
    @PostMapping("/updateCompanyStatus")
    public Result<Void> updateInsuranceCompanyStatus(@RequestBody InsuranceCompanyUpdateStatusDTO updateStatusDTO) {
        insuranceCompanyService.updateInsuranceCompanyStatus(updateStatusDTO);
        return Result.success();
    }

    /**
     * 获取全部保司信息下拉列表
     * 根据当前登录用户的租户信息，返回对应的保司信息列表
     *
     * @return 统一封装的保司信息下拉列表数据
     */
    @RequireLogin
    @ApiOperation(value = "保司配置-获取全部保司信息下拉列表", notes = "获取保司信息下拉列表，id为保司信息ID(String类型)，value为保司信息名称")
    @GetMapping("/companyCombo")
    public Result<List<ComboVO<Integer>>> getAllInsuranceCompanyCombo() {
        List<ComboVO<Integer>> data = insuranceCompanyService.getAllInsuranceCompanyCombo();
        return Result.success(data);
    }

    /**
     * 查询保险索赔用印申请列表
     */
    @ApiOperation(value = "保险索赔-查询保险索赔用印申请列表")
    @PostMapping("/queryClaimList")
    public Result<BasePageVO<InsuranceClaimListVO>> queryInsuranceClaimList(@Valid @RequestBody InsuranceClaimQueryDTO queryDTO) {
        long insuranceCompanyId = 0;
        // 获取登录信息
        LoginUser loginUser = SessionUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 0-管理员 1-运营 2-修理厂 3-保险公司
        int accountType = user.getAccountType();
        if (accountType == 2 || accountType == 3){
            insuranceCompanyId = user.getId();
        }
        List<String> loginOrgIds = loginUser.getDirectOrgIds();
        // 调用梧桐系统接口
        JSONObject jsonObject = JSONUtil.createObj();
        jsonObject.set("pageSize", queryDTO.getPageSize());
        jsonObject.set("pageNum", queryDTO.getPageNum());
        jsonObject.set("insuranceCompanyId", insuranceCompanyId);
        jsonObject.set("vehiclePlate", queryDTO.getLicensePlate());
        jsonObject.set("documentStatus", queryDTO.getDocumentStatus());
        jsonObject.set("documentNumber", queryDTO.getDocumentNumber());
        jsonObject.set("loginOrgIds", loginOrgIds);
        String body = HttpUtil.post(StrUtil.format("{}/inner/queryList", systemConfig.getBfcInsureUrl()), jsonObject.toString(), 10000);
        if (StrUtil.isBlank(body)) {
            return Result.error("数据查询失败");
        }
        JSONObject resp = JSONUtil.parseObj(body);
        Integer code = resp.getInt("code", -1);
        if (code != 0) {
            return Result.error(resp.getStr("message", "单据查询失败"));
        }
        BasePageVO<InsuranceClaimListVO> basePageVO = new BasePageVO<>();
        JSONObject dataJson = resp.getJSONObject("data");
        if (dataJson == null) {
            return Result.success(basePageVO);
        }
        long total = dataJson.getLong("total", 0L);
        basePageVO.setTotal(total);
        if (total > 0) {
            JSONArray list = dataJson.getJSONArray("list");
            basePageVO.setList(list.toList(InsuranceClaimListVO.class));
        }
        return Result.success(basePageVO);
    }

    /**
     * 获取保险索赔用印详情
     */
    @ApiOperation(value = "保险索赔-获取保险索赔用印详情")
    @PostMapping("/claimDetails")
    public Result<InsuranceClaimDetailsVO> getInsuranceClaimDetails(@Valid @RequestBody InsuranceClaimQueryDetailDTO queryDetailDTO) {
        // 调用梧桐系统接口
        JSONObject jsonObject = JSONUtil.createObj();
        jsonObject.set("id", queryDetailDTO.getId());
        jsonObject.set("documentNumber", queryDetailDTO.getDocumentNumber());
        String body = HttpUtil.post(StrUtil.format("{}/inner/getDetails", systemConfig.getBfcInsureUrl()), jsonObject.toString(), 10000);
        if (StrUtil.isBlank(body)) {
            return Result.error("数据查询失败");
        }
        JSONObject resp = JSONUtil.parseObj(body);
        Integer code = resp.getInt("code", -1);
        if (code != 0) {
            return Result.error(resp.getStr("message", "单据信息查询失败"));
        }
        JSONObject dataJson = resp.getJSONObject("data");
        if (dataJson == null) {
            return Result.error("数据查询失败");
        }
        InsuranceClaimDetailsVO insuranceClaimDetailsVO = new InsuranceClaimDetailsVO();
        JSONObject infoJson = dataJson.getJSONObject("info");
        if (infoJson != null) {
            insuranceClaimDetailsVO.setLicensePlate(infoJson.getStr("licensePlate"));
            insuranceClaimDetailsVO.setAccidentDate(infoJson.getStr("accidentDate"));
            insuranceClaimDetailsVO.setRemark(infoJson.getStr("remark"));
        }
        JSONArray attachmentsArray = dataJson.getJSONArray("attachments");
        if (attachmentsArray != null) {
            List<FileUploadResultVO> attachments = new ArrayList<>();
            for (Object object : attachmentsArray) {
                JSONObject attachment = (JSONObject) object;
                FileUploadResultVO attachmentVO = new FileUploadResultVO();
                attachmentVO.setFileName(attachment.getStr("fileName"));
                attachmentVO.setFullUrl(attachment.getStr("filePath"));
                attachments.add(attachmentVO);
            }
            insuranceClaimDetailsVO.setAttachments(attachments);
        }
        JSONArray otherAttachmentsArray = dataJson.getJSONArray("otherAttachments");
        if (otherAttachmentsArray != null) {
            List<FileUploadResultVO> attachments = new ArrayList<>();
            for (Object object : otherAttachmentsArray) {
                JSONObject attachment = (JSONObject) object;
                FileUploadResultVO attachmentVO = new FileUploadResultVO();
                attachmentVO.setFileName(attachment.getStr("fileName"));
                attachmentVO.setFullUrl(attachment.getStr("filePath"));
                attachments.add(attachmentVO);
            }
            insuranceClaimDetailsVO.setOtherAttachments(attachments);
        }

        // 查询用印白杨附件信息
        List<FileUploadResultVO> attachments = new ArrayList<>();
        insuranceClaimDetailsVO.setFinishAttachment(attachments);
        List<AttachmentInfo> attachmentList = insuranceCompanyService.queryAttachmentList(queryDetailDTO.getDocumentNumber());
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            for (AttachmentInfo info : attachmentList) {
                FileUploadResultVO attachmentVO = new FileUploadResultVO();
                attachmentVO.setFileName(info.getFileName());
                attachmentVO.setFullUrl(info.getFileUrl());
                attachments.add(attachmentVO);
            }
        } else {
            JSONArray finishAttachmentArray = dataJson.getJSONArray("finishAttachment");
            if (finishAttachmentArray != null) {
                try {
                    List<MultipartFile> files = new ArrayList<>();
                    // filePath 路径文件 转换成 MultipartFile 上次到oss
                    for (Object object : finishAttachmentArray) {
                        JSONObject attachment = (JSONObject) object;
                        String filePath = attachment.getStr("filePath");
                        MultipartFile multipartFile = UrlToMultipartFileUtil.urlToMultipartFile(filePath, attachment.getStr("fileName"));
                        files.add(multipartFile);
                    }
                    List<AttachmentInfo> attachmentInfoList = new ArrayList<>();
                    List<FileUploadResultVO> uploadResultList = ossService.uploadSimpleFiles(files, "bfc");
                    for (FileUploadResultVO result : uploadResultList) {
                        AttachmentInfo attachmentInfo = new AttachmentInfo();
                        attachmentInfo.setForeignKey(queryDetailDTO.getDocumentNumber());
                        attachmentInfo.setFileName(result.getOriginalFileName());
                        attachmentInfo.setFileUrl(result.getFullUrl());
                        attachmentInfo.setFileType(1);
                        attachmentInfoList.add(attachmentInfo);

                        FileUploadResultVO attachmentVO = new FileUploadResultVO();
                        attachmentVO.setFileName(result.getOriginalFileName());
                        attachmentVO.setFullUrl(result.getFullUrl());
                        attachments.add(attachmentVO);
                    }
                    insuranceCompanyService.insertAttachment(attachmentInfoList);
                } catch (IOException e) {
                    log.error("文件保存失败: {}", e);
                }
            }
        }
        return Result.success(insuranceClaimDetailsVO);
    }

    /**
     * 创建保险索赔用印申请
     */
    @ApiOperation(value = "保险索赔-创建保险索赔用印申请")
    @PostMapping("/createInsuranceClaim")
    public Result<Void> createInsuranceClaim(@Valid @RequestBody InsuranceClaimCreateDTO createDTO) {
        long insuranceCompanyId = 0;
        // 获取登录信息
        LoginUser loginUser = SessionUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 0-管理员 1-运营 2-修理厂 3-保险公司
        int accountType = user.getAccountType();
        if (accountType == 2 || accountType == 3){
            insuranceCompanyId = user.getId();
        }
        // 调用梧桐系统接口
        JSONObject jsonObject = JSONUtil.createObj();
        jsonObject.set("licensePlate", createDTO.getLicensePlate().toUpperCase());
        jsonObject.set("accidentDate", createDTO.getAccidentDate());
        jsonObject.set("registrationDate", DateUtil.now());
        jsonObject.set("registrant", StrUtil.format("{}({})", loginUser.getUser().getNickname(), loginUser.getUsername()));
        jsonObject.set("insuranceCompanyId", insuranceCompanyId);
        jsonObject.set("insuranceCompany", "保司简称");
        InsuranceCompanyVO insuranceCompanyVO = insuranceCompanyService.getInsuranceCompanyDetails(loginUser.getInsuranceCompanyId());
        if (insuranceCompanyVO != null) {
            jsonObject.set("insuranceCompany", insuranceCompanyVO.getCompanyAbbreviation());
        }
        jsonObject.set("remark", createDTO.getRemark());

        // 需用印附件信息
        List<FileDTO> attachments = createDTO.getAttachments();
        if (CollectionUtils.isNotEmpty(attachments)) {
            jsonObject.set("attachments", attachments.stream().map(fileDTO -> {
                JSONObject attachment = JSONUtil.createObj();
                attachment.set("fileName", fileDTO.getName());
                attachment.set("filePath", fileDTO.getUrl());
                return attachment;
            }).collect(Collectors.toList()));
        }
        // 其他附件信息
        List<FileDTO> otherAttachments = createDTO.getOtherAttachments();
        if (CollectionUtils.isNotEmpty(otherAttachments)) {
            jsonObject.set("otherAttachments", otherAttachments.stream().map(fileDTO -> {
                JSONObject attachment = JSONUtil.createObj();
                attachment.set("fileName", fileDTO.getName());
                attachment.set("filePath", fileDTO.getUrl());
                return attachment;
            }).collect(Collectors.toList()));
        }
        String body = HttpUtil.post(StrUtil.format("{}/inner/saveApplication", systemConfig.getBfcInsureUrl()), jsonObject.toString(), 10000);
        if (StrUtil.isBlank(body)) {
            return Result.error("单据保存失败");
        }
        JSONObject resp = JSONUtil.parseObj(body);
        Integer code = resp.getInt("code", -1);
        if (code != 0) {
            return Result.error(resp.getStr("message", "单据信息保存失败"));
        }
        return Result.success();
    }
}