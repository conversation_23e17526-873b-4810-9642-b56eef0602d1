package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.annotation.RequireLogin;
import com.extracme.saas.autocare.model.dto.*;
import com.extracme.saas.autocare.model.vo.RepairDepotDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairDepotListVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.RepairDepotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 修理厂管理接口 - 1
 */

@Api(tags = "修理厂管理")
@RestController
@RequestMapping("/api/v1/repair-depot")
public class RepairDepotController {

    @Autowired
    private RepairDepotService repairDepotService;

    /**
     * 查询维修厂列表
     * @param queryDTO 查询参数
     * @return 维修厂列表
     */
    @ApiOperation(value = "查询维修厂列表", notes = "分页查询列表")
    @PostMapping("/list")
    public Result<BasePageVO<RepairDepotListVO>> queryRepairDepotList(@Valid @RequestBody RepairDepotQueryDTO queryDTO) {
        BasePageVO<RepairDepotListVO> pageVO = repairDepotService.queryRepairDepotList(queryDTO);
        return Result.success(pageVO);
    }

    /**
     * 创建任务-查询维修厂列表
     * @param queryDTO 查询参数
     * @return 维修厂列表
     */
    @ApiOperation(value = "创建任务-查询维修厂列表", notes = "分页查询列表")
    @PostMapping("/checkRepairDepotList")
    public Result<BasePageVO<RepairDepotListVO>> queryCheckRepairDepotList(@Valid @RequestBody RepairDepotQueryDTO queryDTO) {
        BasePageVO<RepairDepotListVO> pageVO = repairDepotService.queryCheckRepairDepotList(queryDTO);
        return Result.success(pageVO);
    }

    /**
     * 获取维修厂详情
     */
    @ApiOperation(value = "获取维修厂详情", notes = "获取指定ID的维修厂详细信息")
    @GetMapping("/details/{id}")
    public Result<RepairDepotDetailsVO> getRepairDepotDetails(@ApiParam(value = "维修厂id 必传", required = true) @PathVariable Long id) {
        RepairDepotDetailsVO repairDepot = repairDepotService.getRepairDepotDetails(id);
        if (repairDepot == null){
            return Result.error("未找到该数据");
        }
        return Result.success(repairDepot);
    }

    /**
     * 获取维修厂详情-根据修理厂编码
     */
    @ApiOperation(value = "获取维修厂详情-根据修理厂编码", notes = "获取修理厂编码的维修厂详细信息")
    @GetMapping("/details-repairDepotId/{repairDepotId}")
    public Result<RepairDepotDetailsVO> getRepairDepotDetailsByRepairDepotId(@ApiParam(value = "修理厂编码 必传", required = true) @PathVariable String repairDepotId) {
        RepairDepotDetailsVO repairDepot = repairDepotService.getRepairDepotDetailsByRepairDepotId(repairDepotId);
        if (repairDepot == null){
            return Result.error("未找到该数据");
        }
        return Result.success(repairDepot);
    }

    /**
     * 创建维修厂
     * @param createDTO 创建维修厂
     */
    @ApiOperation(value = "创建修理厂")
    @PostMapping("/create")
    public Result<Void> createRepairDepot(@Valid @RequestBody RepairDepotCreateDTO createDTO) {
        repairDepotService.createRepairDepot(createDTO);
        return Result.success();
    }

    /**
     * 修改维修厂
     * @param updateDTO 维修厂修改信息
     */
    @ApiOperation(value = "修改维修厂")
    @PostMapping("/update")
    public Result<Void> updateRepairDepot(@Valid @RequestBody RepairDepotUpdateDTO updateDTO) {
        repairDepotService.updateRepairDepot(updateDTO);
        return Result.success();
    }

    @ApiOperation(value = "删除维修厂")
    @PostMapping("/delete")
    public Result<Void> deleteRepairDepot(@ApiParam(value = "维修厂ID", required = true) @RequestBody Long id) {
        repairDepotService.deleteRepairDepot(id);
        return Result.success();
    }


    /**
     * 更新维修厂状态
     */
    @ApiOperation(value = "更新维修厂状态", httpMethod = "POST")
    @PostMapping("/updateStatus")
    public Result<Void> updateStatus(@RequestBody RepairDepotUpdateStatusDTO updateStatusDTO) {
        repairDepotService.updateRepairDepotStatus(updateStatusDTO);
        return Result.success();
    }

    /**
     * 获取修理厂下拉列表
     * 根据当前登录用户的租户信息，返回对应的修理厂列表
     *
     * @return 统一封装的修理厂下拉列表数据
     */
    @RequireLogin
    @ApiOperation(value = "获取修理厂下拉列表", notes = "获取修理厂下拉列表，id为修理厂ID(String类型)，value为修理厂名称")
    @GetMapping("/combo")
    public Result<List<ComboVO<String>>> getRepairDepotCombo() {
        List<ComboVO<String>> data = repairDepotService.getRepairDepotCombo();
        return Result.success(data);
    }

    /**
     * 获取全部修理厂下拉列表
     * 根据当前登录用户的租户信息，返回对应的修理厂列表
     *
     * @return 统一封装的修理厂下拉列表数据
     */
    @RequireLogin
    @ApiOperation(value = "获取全部修理厂下拉列表", notes = "获取修理厂下拉列表，id为修理厂ID(String类型)，value为修理厂名称")
    @GetMapping("/allCombo")
    public Result<List<ComboVO<String>>> getAllRepairDepotCombo() {
        List<ComboVO<String>> data = repairDepotService.getAllRepairDepotCombo();
        return Result.success(data);
    }

    /**
     * 根据租户ID获取修理厂下拉列表
     * 如果请求中未传递tenantId或tenantId为空，则使用当前登录用户的租户ID进行查询
     * 如果请求中传递了有效的tenantId，则使用传入的租户ID进行查询
     *
     * @param repairDepotTenantDTO 包含租户ID的DTO对象
     * @return 统一封装的修理厂下拉列表数据
     */
    @RequireLogin
    @ApiOperation(value = "根据租户ID获取修理厂下拉列表", notes = "根据指定租户ID获取修理厂下拉列表数据，如果未传递租户ID则使用当前登录用户的租户ID")
    @PostMapping("/combo/tenant")
    public Result<List<ComboVO<String>>> getRepairDepotComboByTenant(@RequestBody RepairDepotTenantDTO repairDepotTenantDTO) {
        List<ComboVO<String>> data = repairDepotService.getRepairDepotComboByTenantId(repairDepotTenantDTO != null ? repairDepotTenantDTO.getTenantId() : null);
        return Result.success(data);
    }
}