package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.dto.RoleComboQueryDTO;
import com.extracme.saas.autocare.model.dto.RoleDTO;
import com.extracme.saas.autocare.model.dto.RoleQueryDTO;
import com.extracme.saas.autocare.model.dto.RoleUpdateDTO;
import com.extracme.saas.autocare.model.dto.StatusUpdateDTO;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.RoleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.RoleService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "角色管理", description = "角色管理相关接口")
@RestController
@RequestMapping("/api/v1/role")
public class RoleController {

    @Resource
    private RoleService roleService;

    /**
     * 创建角色
     */
    @ApiOperation(value = "创建角色", notes = "创建新的系统角色，同时可以分配权限")
    @PostMapping("/create")
    public Result<Long> createRole(
            @ApiParam(value = "角色信息", required = true) @Validated @RequestBody RoleDTO roleDTO) {
        SysRole role = new SysRole();
        BeanUtils.copyProperties(roleDTO, role);

        // 创建角色
        Long roleId = roleService.createRole(role);

        // 如果包含权限ID列表，则分配角色权限
        if (roleDTO.getPermissionIds() != null && !roleDTO.getPermissionIds().isEmpty()) {
            roleService.assignPermissions(roleId, roleDTO.getPermissionIds());
        }

        return Result.success(roleId);
    }

    /**
     * 更新角色
     */
    @ApiOperation(value = "更新角色", notes = "更新指定ID的角色信息，同时可以更新角色权限")
    @PostMapping("/update")
    public Result<Void> updateRole(
            @ApiParam(value = "角色信息", required = true) @Validated @RequestBody RoleUpdateDTO roleUpdateDTO) {
        SysRole role = new SysRole();
        BeanUtils.copyProperties(roleUpdateDTO, role);

        // 更新角色基本信息
        roleService.updateRole(role);

        // 如果包含权限ID列表，则更新角色权限
        if (roleUpdateDTO.getPermissionIds() != null) {
            roleService.assignPermissions(roleUpdateDTO.getId(), roleUpdateDTO.getPermissionIds());
        }

        return Result.success();
    }

    /**
     * 删除角色
     */
    @ApiOperation(value = "删除角色", notes = "删除指定ID的角色")
    @PostMapping("/delete")
    public Result<Void> deleteRole(
            @ApiParam(value = "角色ID", required = true) @RequestBody Long id) {
        roleService.deleteRole(id);
        return Result.success();
    }

    /**
     * 获取角色详情
     */
    @ApiOperation(value = "获取角色详情", notes = "获取指定ID的角色详细信息，包含角色基本信息、权限ID列表和权限树结构")
    @GetMapping("/detail/{id}")
    public Result<RoleVO> getRole(
            @ApiParam(value = "角色ID", required = true) @PathVariable Long id) {
        return Result.success(roleService.getRole(id));
    }

    /**
     * 分页查询角色列表
     */
    @ApiOperation(value = "分页查询角色列表", notes = "根据条件分页查询角色列表")
    @PostMapping("/list")
    public Result<BasePageVO<RoleVO>> getRoleList(
            @ApiParam(value = "查询条件", required = true) @RequestBody RoleQueryDTO queryDTO) {
        return Result.success(roleService.getRoleList(queryDTO));
    }

    /**
     * 获取角色下拉列表（旧版本，保持向后兼容）
     */
    @ApiOperation(value = "获取角色下拉列表", notes = "获取所有有效角色的下拉列表，id为角色ID(Long类型)，value为角色名称")
    @GetMapping("/combo")
    public Result<List<ComboVO<Long>>> getRoleCombo() {
        return Result.success(roleService.getRoleCombo());
    }

    /**
     * 获取角色下拉列表（支持租户权限控制）
     */
    @ApiOperation(value = "获取角色下拉列表", notes = "根据租户ID获取有效角色的下拉列表，支持超级管理员跨租户查询，id为角色ID(Long类型)，value为角色名称")
    @PostMapping("/combo")
    public Result<List<ComboVO<Long>>> getRoleCombo(
            @ApiParam(value = "角色下拉列表查询参数", required = true) @Valid @RequestBody RoleComboQueryDTO queryDTO) {
        return Result.success(roleService.getRoleCombo(queryDTO));
    }

    /**
     * 更新角色状态
     */
    @ApiOperation(value = "更新角色状态", notes = "更新指定ID的角色状态，0-禁用，1-启用")
    @PostMapping("/updateStatus")
    public Result<Void> updateRoleStatus(
            @ApiParam(value = "状态更新信息", required = true) @Validated @RequestBody StatusUpdateDTO statusUpdateDTO) {
        roleService.updateRoleStatus(statusUpdateDTO.getId(), statusUpdateDTO.getStatus());
        return Result.success();
    }
}