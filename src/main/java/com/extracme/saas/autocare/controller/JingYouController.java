package com.extracme.saas.autocare.controller;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.extracme.saas.autocare.model.dto.JingYouLossNotifyDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskProcessQueryDTO;
import com.extracme.saas.autocare.model.jingyou.PacketBeanAssBack;
import com.extracme.saas.autocare.model.jingyou.PacketBeanEvaBack;
import com.extracme.saas.autocare.model.jingyou.PacketResponse;
import com.extracme.saas.autocare.model.vo.LossDetailVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.BaseIdVO;
import com.extracme.saas.autocare.service.JingYouService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 精友相关接口
 */
@Slf4j
@Api(tags = "精友相关接口")
@RestController
@RequestMapping("/api/v1/jing-you")
public class JingYouController {

    @Autowired
    private JingYouService jingYouService;

    /**
     * 精友-维修报价
     * 
     * @param baseIdVO 基础id
     */
    @ApiOperation(value = "精友-维修报价")
    @PostMapping("/lossAssessment")
    public Result<String> lossAssessment(@Valid @RequestBody BaseIdVO baseIdVO) {
        String jingYouUrl = jingYouService.lossAssessment(baseIdVO.getId());
        return Result.success(jingYouUrl);
    }

    /**
     * 精友-核损核价
     * 
     * @param baseIdVO 基础id
     */
    @ApiOperation(value = "精友-核损核价")
    @PostMapping("/evaluateLoss")
    public Result<String> evaluateLoss(@Valid @RequestBody BaseIdVO baseIdVO) {
        String jingYouUrl = jingYouService.evaluateLoss(baseIdVO.getId());
        return Result.success(jingYouUrl);
    }

    /**
     * 精友-定损查看
     * 
     * @param baseIdVO 基础id
     */
    @ApiOperation(value = "精友-定损查看")
    @PostMapping("/viewLoss")
    public Result<String> viewLoss(@Valid @RequestBody BaseIdVO baseIdVO) {
        String jingYouUrl = jingYouService.viewLoss(baseIdVO.getId());
        return Result.success(jingYouUrl);
    }

    /**
     * 精友-定损回调
     * 
     * @param packetBeanAssBack 基础id
     */
    @ApiOperation(value = "精友-定损回调")
    @PostMapping(value = "/lossAssessmentBack", consumes = "application/xml", produces = "application/xml")
    public PacketResponse lossAssessmentBack(@RequestBody PacketBeanAssBack packetBeanAssBack) {
        log.warn("lossAssessmentBack----------------------->" + JSON.toJSONString(packetBeanAssBack));
        return jingYouService.lossAssessmentBack(packetBeanAssBack);
    }

    /**
     * 精友-核损回调
     * 
     * @param packetBeanAssBack 基础id
     */
    @ApiOperation(value = "精友-核损回调")
    @PostMapping(value = "/evaluateLossBack", consumes = "application/xml", produces = "application/xml")
    public PacketResponse evaluateLossBack(@RequestBody PacketBeanEvaBack packetBeanEvaBack) {
        log.warn("evaluateLossBack----------------------->" + JSON.toJSONString(packetBeanEvaBack));
        return jingYouService.evaluateLossBack(packetBeanEvaBack);
    }

    /**
     * 精友-获取定损详情
     * 
     * @param baseIdVO 基础id
     */
    @ApiOperation(value = "精友-获取定损详情")
    @PostMapping("/lossDetail")
    public Result<LossDetailVO> lossDetail(@Valid @RequestBody RepairTaskProcessQueryDTO queryDTO) {
        LossDetailVO LossDetailVO = jingYouService.getLossDetail(queryDTO.getTaskNo());
        return Result.success(LossDetailVO);
    }

    /**
     * 精友-状态变更通知
     * 
     * @param notifyDTO 状态通知参数
     * @return 操作结果
     */
    @ApiOperation(value = "精友-状态变更通知", notes = "向精友系统发送任务状态变更通知")
    @PostMapping("/lossNotify")
    public Result<Void> lossNotify(@Valid @RequestBody JingYouLossNotifyDTO notifyDTO) {
        jingYouService.lossNotify(notifyDTO.getTaskNo(), notifyDTO.getJingYouLossNotify());
        return Result.success();
    }
}
