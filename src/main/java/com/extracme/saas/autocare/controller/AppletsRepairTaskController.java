package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.baidumap.BaiduPositionName;
import com.extracme.saas.autocare.model.dto.*;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.vo.*;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.service.*;
import com.extracme.saas.autocare.util.BaiDuMapUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 小程序维修任务管理接口
 */
@Slf4j
@Api(tags = "小程序维修任务管理")
@RestController
@RequestMapping("/api/v1/applets/repair-task")
public class AppletsRepairTaskController {

    @Autowired
    private RepairTaskService repairTaskService;

    @Autowired
    private RepairQuoteService repairQuoteService;

    @Autowired
    private LossAssessmentService lossAssessmentService;

    @Autowired
    private InRepairService inRepairService;

    @Autowired
    private VehicleInspectionService vehicleInspectionService;

    /**
     * 获取各环节任务数
     * 
     * @param queryDTO 查询参数
     * @return 各环节任务数
     */
    @ApiOperation(value = "获取各环节任务数")
    @PostMapping("/countActivityCode")
    public Result<Map<String, Long>> countActivityCode(@Valid @RequestBody RepairTaskListQueryDTO queryDTO) {
        Map<String, Long> result = repairTaskService.countActivityCode(queryDTO);
        return Result.success(result);
    }

    /**
     * 各环节-当前环节任务列表
     * 
     * @param queryDTO 查询参数
     * @return 维修任务列表
     */
    @ApiOperation(value = "各环节-当前环节任务列表", notes = "分页查询列表")
    @PostMapping("/listResult")
    public Result<BasePageVO<RepairTaskProcessListVO>> queryCurrentActivityCodeRepairTaskList(@Valid @RequestBody RepairTaskListQueryDTO queryDTO) {
        BasePageVO<RepairTaskProcessListVO> basePageVO = repairTaskService.queryCurrentActivityCodeRepairTaskList(queryDTO);
        return Result.success(basePageVO);
    }

    /**
     * 获取维修任务详情
     */
    @ApiOperation(value = "获取维修任务详情", notes = "获取指定ID的维修任务详细信息")
    @GetMapping("/details/{id}")
    public Result<RepairTaskDetailsVO> getRepairTaskDetails(
            @ApiParam(value = "维修任务id 必传", required = true) @PathVariable Long id) {
        RepairTaskDetailsVO repairTask = repairTaskService.getRepairTaskDetails(id);
        if (repairTask == null) {
            return Result.error("未找到该数据");
        }
        return Result.success(repairTask);
    }

    /**
     * 待分配-选择修理厂
     *
     * @param selectDTO 选择修理厂请求参数
     * @return 操作结果
     */
    @ApiOperation(value = "待分配-选择修理厂", notes = "为维修任务指定修理厂")
    @PostMapping("/pendingAssignment/selectRepairDepot")
    public Result<Void> selectRepairDepot(
            @ApiParam(value = "选择修理厂请求参数", required = true) @Valid @RequestBody RepairDepotSelectDTO selectDTO) {
        repairTaskService.selectRepairDepot(selectDTO.getTaskId(), selectDTO.getRepairDepotId());
        return Result.success();
    }

    /**
     * 维修报价-保存信息
     *
     * @param updateDTO 维修报价信息参数
     * @return 保存结果
     */
    @ApiOperation(value = "维修报价-保存信息", notes = "保存维修报价信息，不提交审核")
    @PostMapping("/repairQuotation/save")
    public Result<Void> saveRepairQuote(
            @ApiParam(value = "维修报价信息", required = true) @Valid @RequestBody RepairQuoteUpdateDTO updateDTO) {
        repairQuoteService.saveRepairQuote(updateDTO);
        return Result.success();
    }

    /**
     * 维修报价-提交审核
     *
     * @param submitDTO 提交维修报价审核参数
     * @return 提交结果
     */
    @ApiOperation(value = "维修报价-提交审核", notes = "提交维修报价信息")
    @PostMapping("/repairQuotation/submit")
    public Result<Void> submitRepairQuote(@Valid @RequestBody RepairQuoteSubmitDTO submitDTO) {
        return repairQuoteService.submitRepairQuote(submitDTO);
    }

    /**
     * 核损核价-保存信息
     *
     * @param updatedDTO 核损核价保存参数
     * @return 审核结果
     */
    @ApiOperation(value = "核损核价-保存信息", notes = "保存核损核价信息")
    @PostMapping("/lossAssessment/save")
    public Result<Void> saveLossAssessment(@Valid @RequestBody LossAssessmentUpdateDTO updatedDTO) {
        lossAssessmentService.saveLossAssessment(updatedDTO);
        return Result.success();
    }

    /**
     * 核损核价-平级移交
     *
     * @param taskNoDTO 包含维修任务编号的DTO
     * @return 操作结果
     */
    @ApiOperation(value = "核损核价-平级移交", notes = "将当前核损核价任务移交给其他人处理")
    @PostMapping("/lossAssessment/transfer")
    public Result<Void> transferLossAssessment(
            @ApiParam(value = "包含维修任务编号的DTO", required = true) @Valid @RequestBody TaskNoDTO taskNoDTO) {
        lossAssessmentService.updateTransferTask(taskNoDTO.getTaskNo());
        return Result.success();
    }

    /**
     * 核损核价-清除任务占据人
     *
     * @param taskNoDTO 包含维修任务编号的DTO
     * @return 操作结果
     */
    @ApiOperation(value = "核损核价-清除任务占据人", notes = "清除核损核价任务的占据人，使任务可以被其他人处理")
    @PostMapping("/lossAssessment/clearOwner")
    public Result<Void> clearLossAssessmentOwner(
            @ApiParam(value = "包含维修任务编号的DTO", required = true) @Valid @RequestBody TaskNoDTO taskNoDTO) {
        lossAssessmentService.clearOwner(taskNoDTO.getTaskNo());
        return Result.success();
    }

    /**
     * 核损核价-退回
     *
     * @param rejectDTO 核损核价审核参数
     * @return 退回结果
     */
    @ApiOperation(value = "核损核价-退回", notes = "审核退回核损核价信息")
    @PostMapping("/lossAssessment/reject")
    public Result<Void> approveLossAssessment(@Valid @RequestBody LossAssessmentRejectDTO rejectDTO) {
        lossAssessmentService.rejectLossAssessment(rejectDTO);
        return Result.success();
    }

    /**
     * 核损核价-审核通过
     *
     * @param approveDTO 核损核价审核参数
     * @return 审核结果
     */
    @ApiOperation(value = "核损核价-审核通过", notes = "审核通过核损核价信息")
    @PostMapping("/lossAssessment/approve")
    public Result<Void> approveLossAssessment(@Valid @RequestBody LossAssessmentApproveDTO approveDTO) {
        lossAssessmentService.approveLossAssessment(approveDTO);
        return Result.success();
    }

    /**
     * 调整客户直付金额
     *
     * @param repairCustInfoDTO 调整客户直付金额DTO
     * @return 调整结果
     */
    @ApiOperation(value = "核损核价-审核通过", notes = "审核通过核损核价信息")
    @PostMapping("/adjustCustAmount")
    public Result<Void> adjustCustAmount(@Valid @RequestBody RepairCustInfoDTO repairCustInfoDTO) {
        repairTaskService.adjustCustAmount(repairCustInfoDTO);
        return Result.success();
    }

    /**
     * 车辆维修-保存信息
     *
     * @param updateDTO 车辆维修更新参数
     * @return 操作结果
     */
    @ApiOperation(value = "车辆维修-保存信息", notes = "车辆维修-保存信息")
    @PostMapping("/vehicleRepair/save")
    public Result<Void> saveVehicleInRepair(
            @ApiParam(value = "车辆维修活动更新信息", required = true) @Valid @RequestBody InRepairUpdateDTO updateDTO) {
        inRepairService.saveVehicleInRepair(updateDTO);
        return Result.success();
    }

    /**
     * 车辆维修-申请验收
     *
     * @param submitDTO 申请验收提交dto
     * @return 操作结果
     */
    @ApiOperation(value = "车辆维修-申请验收", notes = "车辆维修完成后申请验收")
    @PostMapping("/vehicleRepair/requestInspection")
    public Result<Void> changeVehicleCheck(
            @ApiParam(value = "车辆维修活动更新信息", required = true) @Valid @RequestBody InRepairSubmitDTO submitDTO) {
        inRepairService.changeVehicleCheck(submitDTO);
        return Result.success();
    }

    /**
     * 车辆验收-验收通过
     *
     * @param approveDTO 验收通过信息DTO
     * @return 操作结果
     */
    @ApiOperation(value = "车辆验收-验收通过", notes = "处理车辆维修任务的验收通过流程")
    @PostMapping("/vehicleInspection/approve")
    public Result<Void> approveVehicleInspection(
            @ApiParam(value = "验收通过信息", required = true) @Valid @RequestBody VehicleInspectionApproveDTO approveDTO) {
        vehicleInspectionService.approveInspection(approveDTO);
        return Result.success();
    }

    /**
     * 车辆验收-验收不通过
     *
     * @param rejectDTO 验收不通过信息DTO
     * @return 操作结果
     */
    @ApiOperation(value = "车辆验收-验收不通过", notes = "处理车辆维修任务的验收不通过流程")
    @PostMapping("/vehicleInspection/reject")
    public Result<Void> rejectVehicleInspection(
            @ApiParam(value = "验收不通过信息", required = true) @Valid @RequestBody VehicleInspectionRejectDTO rejectDTO) {
        vehicleInspectionService.rejectInspection(rejectDTO);
        return Result.success();
    }

    /**
     * 百度坐标转换具体位置信息
     *
     * @param positionVO 经纬度位置VO
     * @return 具体位置信息
     */
    @RequestMapping(value = "getBaiduPositionNameByPosition", method = RequestMethod.POST)
    public Result<BaiduPositionName.ResultInfo> getBaiduPositionNameByPosition(@RequestBody PositionVO positionVO) {
        BaiduPositionName baiduPositionName = BaiDuMapUtils.getBaiduPositionNameByPosition(positionVO.getLat(), positionVO.getLng());
        if (null != baiduPositionName && baiduPositionName.getStatus() == 0) {
            return Result.success(baiduPositionName.getResult());
        }

        return Result.error("查询失败！");
    }

    /**
     * 查看任务备注
     *
     * @param repairRemarkDTO 维修备注信息
     * @return 操作结果
     */
    @ApiOperation(value = "查看任务备注", notes = "查看任务备注")
    @PostMapping("/remark/view")
    public Result<List<ViewRepairRemarkVO>> viewRepairRemark(
            @ApiParam(value = "维修备注信息", required = true) @Valid @RequestBody RepairRemarkDTO repairRemarkDTO) {
        try {
            return Result.success(repairTaskService.viewRepairRemark(repairRemarkDTO.getTaskNo()));
        } catch (Exception e) {
            log.error("查看任务备注失败", e);
            return Result.error("系统异常，请稍后重试");
        }
    }

    /**
     * 添加维修备注
     *
     * @param repairRemarkDTO 维修备注信息
     * @return 操作结果
     */
    @ApiOperation(value = "添加维修备注", notes = "为维修任务添加备注信息")
    @PostMapping("/remark/add")
    public Result<Void> addRepairRemark(
            @ApiParam(value = "维修备注信息", required = true) @Valid @RequestBody RepairRemarkDTO repairRemarkDTO) {
        try {
            repairTaskService.addRepairRemark(repairRemarkDTO);
            return Result.success();
        } catch (Exception e) {
            log.error("添加维修备注失败", e);
            return Result.error("系统异常，请稍后重试");
        }
    }

    /**
     * 删除维修备注
     *
     * @param repairRemarkDTO   备注ID
     * @return 操作结果
     */
    @ApiOperation(value = "删除维修备注", notes = "删除指定ID的维修备注")
    @PostMapping("/remark/delete")
    public Result<Void> deleteRepairRemark(
            @ApiParam(value = "维修备注信息", required = true) @Valid @RequestBody RepairRemarkDTO repairRemarkDTO) {
        try {
            repairTaskService.deleteRepairRemark(repairRemarkDTO.getId(), repairRemarkDTO.getActivityCode());
            return Result.success();
        } catch (Exception e) {
            log.error("删除维修备注失败", e);
            return Result.error("系统异常，请稍后重试");
        }
    }
}