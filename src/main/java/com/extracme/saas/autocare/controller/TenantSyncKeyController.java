package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.entity.SysTenantSyncKey;
import com.extracme.saas.autocare.service.TenantIdentificationService;
import com.extracme.saas.autocare.repository.TableTenantSyncKeyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 租户同步密钥管理控制器
 * 
 * <p>提供租户同步密钥的生成、查询、更新等管理功能。</p>
 * <p>主要用于系统管理员管理各租户的数据同步密钥。</p>
 */
@Api(tags = "租户同步密钥管理")
@RestController
@RequestMapping("/api/tenant-sync-key")
@Slf4j
public class TenantSyncKeyController {

    @Autowired
    private TenantIdentificationService tenantIdentificationService;

    @Autowired
    private TableTenantSyncKeyService tableTenantSyncKeyService;

    /**
     * 为租户生成新的同步密钥
     *
     * @param tenantCode 租户编码
     * @return 生成的同步密钥
     */
    @ApiOperation(
        value = "生成租户同步密钥",
        notes = "为指定租户生成新的同步密钥，用于数据同步接口的身份识别"
    )
    @PostMapping("/generate/{tenantCode}")
    public Result<String> generateSyncKey(
            @ApiParam(value = "租户编码", required = true, example = "TENANT_001")
            @PathVariable String tenantCode) {

        log.info("开始为租户生成同步密钥，租户编码：{}", tenantCode);

        try {
            String syncKey = tenantIdentificationService.generateSyncKey(tenantCode);
            
            log.info("成功为租户 {} 生成同步密钥", tenantCode);
            return Result.success(syncKey);

        } catch (Exception e) {
            log.error("为租户 {} 生成同步密钥失败：{}", tenantCode, e.getMessage(), e);
            return Result.error("生成同步密钥失败：" + e.getMessage());
        }
    }

    /**
     * 查询租户的同步密钥信息
     *
     * @param tenantCode 租户编码
     * @return 租户同步密钥信息
     */
    @ApiOperation(
        value = "查询租户同步密钥",
        notes = "根据租户编码查询对应的同步密钥信息"
    )
    @GetMapping("/query/{tenantCode}")
    public Result<SysTenantSyncKey> querySyncKey(
            @ApiParam(value = "租户编码", required = true, example = "TENANT_001")
            @PathVariable String tenantCode) {

        log.info("查询租户同步密钥，租户编码：{}", tenantCode);

        try {
            SysTenantSyncKey syncKey = tableTenantSyncKeyService.findByTenantCode(tenantCode);
            
            if (syncKey == null) {
                return Result.error("未找到租户 " + tenantCode + " 的同步密钥");
            }

            log.info("成功查询到租户 {} 的同步密钥信息", tenantCode);
            return Result.success(syncKey);

        } catch (Exception e) {
            log.error("查询租户 {} 同步密钥失败：{}", tenantCode, e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 验证同步密钥是否有效
     *
     * @param syncKey 同步密钥
     * @return 验证结果
     */
    @ApiOperation(
        value = "验证同步密钥",
        notes = "验证指定的同步密钥是否有效（未过期且状态正常）"
    )
    @GetMapping("/validate")
    public Result<Boolean> validateSyncKey(
            @ApiParam(value = "同步密钥", required = true)
            @RequestParam String syncKey) {

        log.info("验证同步密钥有效性");

        try {
            boolean isValid = tenantIdentificationService.validateSyncKey(syncKey);
            
            log.info("同步密钥验证完成，结果：{}", isValid ? "有效" : "无效");
            return Result.success(isValid);

        } catch (Exception e) {
            log.error("验证同步密钥失败：{}", e.getMessage(), e);
            return Result.error("验证失败：" + e.getMessage());
        }
    }

    /**
     * 禁用租户的同步密钥
     *
     * @param tenantCode 租户编码
     * @return 操作结果
     */
    @ApiOperation(
        value = "禁用租户同步密钥",
        notes = "禁用指定租户的同步密钥，禁用后该租户无法进行数据同步"
    )
    @PostMapping("/disable/{tenantCode}")
    public Result<String> disableSyncKey(
            @ApiParam(value = "租户编码", required = true, example = "TENANT_001")
            @PathVariable String tenantCode) {

        log.info("禁用租户同步密钥，租户编码：{}", tenantCode);

        try {
            SysTenantSyncKey syncKey = tableTenantSyncKeyService.findByTenantCode(tenantCode);
            
            if (syncKey == null) {
                return Result.error("未找到租户 " + tenantCode + " 的同步密钥");
            }

            syncKey.setKeyStatus(0); // 设置为禁用状态
            tableTenantSyncKeyService.updateSelectiveById(syncKey);
            
            log.info("成功禁用租户 {} 的同步密钥", tenantCode);
            return Result.success("同步密钥已禁用");

        } catch (Exception e) {
            log.error("禁用租户 {} 同步密钥失败：{}", tenantCode, e.getMessage(), e);
            return Result.error("禁用失败：" + e.getMessage());
        }
    }

    /**
     * 启用租户的同步密钥
     *
     * @param tenantCode 租户编码
     * @return 操作结果
     */
    @ApiOperation(
        value = "启用租户同步密钥",
        notes = "启用指定租户的同步密钥，启用后该租户可以进行数据同步"
    )
    @PostMapping("/enable/{tenantCode}")
    public Result<String> enableSyncKey(
            @ApiParam(value = "租户编码", required = true, example = "TENANT_001")
            @PathVariable String tenantCode) {

        log.info("启用租户同步密钥，租户编码：{}", tenantCode);

        try {
            SysTenantSyncKey syncKey = tableTenantSyncKeyService.findByTenantCode(tenantCode);
            
            if (syncKey == null) {
                return Result.error("未找到租户 " + tenantCode + " 的同步密钥");
            }

            syncKey.setKeyStatus(1); // 设置为启用状态
            tableTenantSyncKeyService.updateSelectiveById(syncKey);
            
            log.info("成功启用租户 {} 的同步密钥", tenantCode);
            return Result.success("同步密钥已启用");

        } catch (Exception e) {
            log.error("启用租户 {} 同步密钥失败：{}", tenantCode, e.getMessage(), e);
            return Result.error("启用失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有租户的同步密钥列表
     *
     * @return 所有租户的同步密钥列表
     */
    @ApiOperation(
        value = "查询所有租户同步密钥",
        notes = "查询系统中所有租户的同步密钥信息列表"
    )
    @GetMapping("/list")
    public Result<List<SysTenantSyncKey>> listAllSyncKeys() {

        log.info("查询所有租户同步密钥列表");

        try {
            List<SysTenantSyncKey> syncKeys = tableTenantSyncKeyService.findAll();
            
            log.info("成功查询到 {} 个租户的同步密钥", syncKeys.size());
            return Result.success(syncKeys);

        } catch (Exception e) {
            log.error("查询所有租户同步密钥失败：{}", e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
