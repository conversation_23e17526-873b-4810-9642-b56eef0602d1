package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.util.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Redis 测试控制器
 * 用于演示和测试 Redis 功能
 * 
 * <AUTHOR>
 * @date 2024/03/21
 */
@Slf4j
@RestController
@RequestMapping("/api/redis-test")
@Api(tags = "Redis测试接口")
public class RedisTestController {

    @Autowired
    private RedisUtils redisUtils;

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 测试 Redis 连接状态
     */
    @GetMapping("/ping")
    @ApiOperation("测试Redis连接")
    public Result<Map<String, Object>> ping() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean connected = redisUtils.ping();
            result.put("connected", connected);
            result.put("timestamp", System.currentTimeMillis());
            
            if (connected) {
                String info = redisUtils.info();
                result.put("info", info.substring(0, Math.min(500, info.length())));
            }
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("Redis连接测试失败", e);
            result.put("connected", false);
            result.put("error", e.getMessage());
            return Result.success(result);
        }
    }

    /**
     * 测试基础字符串操作
     */
    @PostMapping("/string/set")
    @ApiOperation("设置字符串值")
    public Result<Boolean> setString(
            @ApiParam("键") @RequestParam String key,
            @ApiParam("值") @RequestParam String value,
            @ApiParam("过期时间(秒)") @RequestParam(required = false) Long expireSeconds) {
        
        try {
            boolean success;
            if (expireSeconds != null && expireSeconds > 0) {
                success = redisUtils.set(key, value, expireSeconds);
            } else {
                success = redisUtils.set(key, value);
            }
            return Result.success(success);
        } catch (Exception e) {
            log.error("设置字符串值失败", e);
            return Result.error("设置失败: " + e.getMessage());
        }
    }

    /**
     * 获取字符串值
     */
    @GetMapping("/string/get")
    @ApiOperation("获取字符串值")
    public Result<Object> getString(@ApiParam("键") @RequestParam String key) {
        try {
            Object value = redisUtils.get(key);
            return Result.success(value);
        } catch (Exception e) {
            log.error("获取字符串值失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 测试计数器功能
     */
    @PostMapping("/counter/incr")
    @ApiOperation("计数器递增")
    public Result<Long> increment(
            @ApiParam("计数器键") @RequestParam String key,
            @ApiParam("递增值") @RequestParam(defaultValue = "1") Long delta) {
        
        try {
            long result = redisUtils.incr(key, delta);
            return Result.success(result);
        } catch (Exception e) {
            log.error("计数器递增失败", e);
            return Result.error("递增失败: " + e.getMessage());
        }
    }

    /**
     * 测试哈希操作
     */
    @PostMapping("/hash/set")
    @ApiOperation("设置哈希字段")
    public Result<Boolean> setHash(
            @ApiParam("哈希键") @RequestParam String key,
            @ApiParam("字段") @RequestParam String field,
            @ApiParam("值") @RequestParam String value) {
        
        try {
            boolean success = redisUtils.hset(key, field, value);
            return Result.success(success);
        } catch (Exception e) {
            log.error("设置哈希字段失败", e);
            return Result.error("设置失败: " + e.getMessage());
        }
    }

    /**
     * 获取哈希所有字段
     */
    @GetMapping("/hash/getall")
    @ApiOperation("获取哈希所有字段")
    public Result<Map<Object, Object>> getHashAll(@ApiParam("哈希键") @RequestParam String key) {
        try {
            Map<Object, Object> result = redisUtils.hmget(key);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取哈希字段失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 测试列表操作
     */
    @PostMapping("/list/push")
    @ApiOperation("列表推入元素")
    public Result<Long> pushToList(
            @ApiParam("列表键") @RequestParam String key,
            @ApiParam("值") @RequestParam String value,
            @ApiParam("方向") @RequestParam(defaultValue = "right") String direction) {
        
        try {
            Long result;
            if ("left".equalsIgnoreCase(direction)) {
                result = redisUtils.lLeftPush(key, value);
            } else {
                result = redisUtils.lRightPush(key, value);
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("列表推入失败", e);
            return Result.error("推入失败: " + e.getMessage());
        }
    }

    /**
     * 获取列表范围元素
     */
    @GetMapping("/list/range")
    @ApiOperation("获取列表范围元素")
    public Result<List<Object>> getListRange(
            @ApiParam("列表键") @RequestParam String key,
            @ApiParam("开始位置") @RequestParam(defaultValue = "0") Long start,
            @ApiParam("结束位置") @RequestParam(defaultValue = "-1") Long end) {
        
        try {
            List<Object> result = redisUtils.lGet(key, start, end);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取列表范围失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 测试分布式锁
     */
    @PostMapping("/lock/test")
    @ApiOperation("测试分布式锁")
    public Result<Map<String, Object>> testDistributedLock(
            @ApiParam("锁键") @RequestParam String lockKey,
            @ApiParam("持有时间(秒)") @RequestParam(defaultValue = "10") Long holdSeconds) {
        
        Map<String, Object> result = new HashMap<>();
        String requestId = UUID.randomUUID().toString();
        
        try {
            boolean acquired = redisUtils.tryLock(lockKey, requestId, holdSeconds);
            result.put("acquired", acquired);
            result.put("requestId", requestId);
            result.put("timestamp", System.currentTimeMillis());
            
            if (acquired) {
                // 模拟业务处理
                Thread.sleep(1000);
                
                boolean released = redisUtils.releaseLock(lockKey, requestId);
                result.put("released", released);
            }
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("分布式锁测试失败", e);
            result.put("error", e.getMessage());
            return Result.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试缓存模式
     */
    @GetMapping("/cache/test")
    @ApiOperation("测试缓存模式")
    public Result<Map<String, Object>> testCachePattern(@ApiParam("用户ID") @RequestParam String userId) {
        Map<String, Object> result = new HashMap<>();
        String cacheKey = "user_info:" + userId;
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 使用缓存模式获取数据
            Map<String, Object> userInfo = redisUtils.getOrSet(
                cacheKey,
                300, // 缓存5分钟
                () -> {
                    // 模拟从数据库获取数据
                    try {
                        Thread.sleep(100); // 模拟数据库查询耗时
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    
                    Map<String, Object> data = new HashMap<>();
                    data.put("userId", userId);
                    data.put("username", "user_" + userId);
                    data.put("email", userId + "@example.com");
                    data.put("loadTime", System.currentTimeMillis());
                    return data;
                }
            );
            
            long endTime = System.currentTimeMillis();
            
            result.put("userInfo", userInfo);
            result.put("responseTime", endTime - startTime);
            result.put("fromCache", userInfo != null && userInfo.get("loadTime") != null);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("缓存模式测试失败", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 清理测试数据
     */
    @DeleteMapping("/cleanup")
    @ApiOperation("清理测试数据")
    public Result<Long> cleanup(@ApiParam("键模式") @RequestParam(defaultValue = "test:*") String pattern) {
        try {
            long deletedCount = redisUtils.deleteByPattern(pattern);
            return Result.success(deletedCount);
        } catch (Exception e) {
            log.error("清理测试数据失败", e);
            return Result.error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 并发测试
     */
    @PostMapping("/concurrent/test")
    @ApiOperation("并发操作测试")
    public Result<Map<String, Object>> testConcurrent(
            @ApiParam("并发数") @RequestParam(defaultValue = "10") Integer concurrency,
            @ApiParam("每个线程操作次数") @RequestParam(defaultValue = "10") Integer operationsPerThread) {
        
        Map<String, Object> result = new HashMap<>();
        String counterKey = "concurrent_test_counter";
        
        try {
            // 重置计数器
            redisUtils.del(counterKey);
            
            long startTime = System.currentTimeMillis();
            
            // 创建并发任务
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (int i = 0; i < concurrency; i++) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    for (int j = 0; j < operationsPerThread; j++) {
                        redisUtils.incr(counterKey, 1);
                    }
                }, executorService);
                futures.add(future);
            }
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            long endTime = System.currentTimeMillis();
            
            // 获取最终计数
            Object finalCount = redisUtils.get(counterKey);
            
            result.put("expectedCount", concurrency * operationsPerThread);
            result.put("actualCount", finalCount);
            result.put("duration", endTime - startTime);
            result.put("success", Objects.equals(String.valueOf(concurrency * operationsPerThread), String.valueOf(finalCount)));
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("并发测试失败", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }
}
