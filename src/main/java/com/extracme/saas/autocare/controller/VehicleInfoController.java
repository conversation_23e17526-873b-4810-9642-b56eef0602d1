package com.extracme.saas.autocare.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.saas.autocare.annotation.RequireLogin;
import com.extracme.saas.autocare.model.dto.VehicleInfoQueryDTO;
import com.extracme.saas.autocare.model.dto.VehicleModelQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.VehicleInfoVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.VehicleInfoService;
import com.extracme.saas.autocare.service.VehicleModelService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 车辆信息控制器
 */
@Api(tags = "车辆信息管理")
@RestController
@RequestMapping("/api/v1/vehicle")
public class VehicleInfoController {

    @Autowired
    private VehicleInfoService vehicleInfoService;

    @Autowired
    private VehicleModelService vehicleModelService;

    /**
     * 根据车架号或车牌号模糊查询车辆信息列表
     *
     * @param queryDTO 查询条件，包含vin和vehicleNo
     * @return 车辆信息列表，最多返回20条数据
     */
    @RequireLogin
    @ApiOperation(value = "查询车辆信息下拉列表框", notes = "根据车架号或车牌号模糊查询车辆信息下拉列表，最多返回20条数据")
    @PostMapping("/list")
    public Result<List<VehicleInfoVO>> getVehicleInfoList(
            @ApiParam(value = "查询条件", required = true) @RequestBody VehicleInfoQueryDTO queryDTO) {
        List<VehicleInfoVO> data = vehicleInfoService.getVehicleInfoList(queryDTO.getVin(), queryDTO.getVehicleNo());
        return Result.success(data);
    }

    /**
     * 根据车架号查询车辆详情
     *
     * @param vin 车架号
     * @return 车辆详情
     */
    @RequireLogin
    @ApiOperation(value = "根据车架号查询车辆详情", notes = "根据车架号精确查询车辆详情")
    @GetMapping("/detail/{vin}")
    public Result<VehicleInfoVO> getVehicleInfoByVin(
            @ApiParam(value = "车架号", required = true) @PathVariable String vin) {
        VehicleInfoVO vehicleInfo = vehicleInfoService.getVehicleInfoByVin(vin);
        if (vehicleInfo == null) {
            return Result.error("未找到该车辆信息");
        }
        return Result.success(vehicleInfo);
    }

    /**
     * 获取车型下拉列表
     * 根据车型名称模糊查询车型列表，返回下拉选项数据
     *
     * @param queryDTO 查询条件，包含vehicleModelName
     * @return 车型下拉列表数据，最多返回20条数据
     */
    @RequireLogin
    @ApiOperation(value = "获取车型下拉列表", notes = "根据车型名称模糊查询车型下拉列表，最多返回20条数据，id为车型id(Long类型)，value为车型名称")
    @PostMapping("/model/combo")
    public Result<List<ComboVO<Long>>> getVehicleModelCombo(
            @ApiParam(value = "查询条件", required = false) @RequestBody(required = false) VehicleModelQueryDTO queryDTO) {
        List<ComboVO<Long>> data = vehicleModelService.getVehicleModelCombo(queryDTO.getVehicleModelName());
        return Result.success(data);
    }
}
