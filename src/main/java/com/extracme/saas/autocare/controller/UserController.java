package com.extracme.saas.autocare.controller;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.saas.autocare.exception.UnauthorizedException;
import com.extracme.saas.autocare.model.dto.MerchantAdminQueryDTO;
import com.extracme.saas.autocare.model.dto.StatusUpdateDTO;
import com.extracme.saas.autocare.model.dto.UserCreateDTO;
import com.extracme.saas.autocare.model.dto.UserQueryDTO;
import com.extracme.saas.autocare.model.dto.UserUpdateDTO;

import java.util.List;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.MerchantAdminVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.UserVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.service.UserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;

/**
 * 用户管理控制器
 */
@Api(tags = "用户管理", description = "用户信息管理相关接口")
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 创建用户
     *
     * @param createDTO 用户创建信息
     * @return 用户ID
     */
    @ApiOperation(value = "创建用户", notes = "创建新用户")
    @PostMapping("/create")
    public Result<Long> createUser(
            @ApiParam(value = "用户创建信息", required = true) @Valid @RequestBody UserCreateDTO createDTO) {
        Long userId = userService.createUser(createDTO);
        return Result.success(userId);
    }

    /**
     * 查询用户列表
     *
     * @param queryDTO 查询参数
     * @return 用户列表
     */
    @ApiOperation(value = "查询用户列表", notes = "分页查询用户列表，支持按用户名、手机号和角色筛选，返回结果包含租户名称")
    @PostMapping("/list")
    public Result<BasePageVO<UserVO>> getUserList(@Valid @RequestBody UserQueryDTO queryDTO) {
        BasePageVO<UserVO> pageInfo = userService.getUserList(queryDTO);
        return Result.success(pageInfo);
    }

    /**
     * 获取当前登录用户信息
     *
     * @param request HTTP请求
     * @return 当前登录用户信息
     */
    @ApiOperation(value = "获取当前登录用户信息", notes = "获取当前登录用户的详细信息")
    @PostMapping("/current")
    public Result<LoginUser> getCurrentUser(HttpServletRequest request) {
        // 通过UserService获取包含过期天数的登录用户信息
        LoginUser loginUser = userService.getCurrentUserWithExpiry();
        if (loginUser == null) {
            throw new UnauthorizedException("用户未登录");
        }

        return Result.success(loginUser);
    }

    /**
     * 更新用户信息
     *
     * @param updateDTO 用户更新信息
     * @return 操作结果
     */
    @ApiOperation(value = "更新用户信息", notes = "更新指定用户的信息")
    @PostMapping("/update")
    public Result<Void> updateUser(
            @ApiParam(value = "用户更新信息", required = true) @Valid @RequestBody UserUpdateDTO updateDTO) {
        userService.updateUser(updateDTO);
        return Result.success();
    }

    /**
     * 根据ID获取用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    @ApiOperation(value = "获取用户详情", notes = "根据用户ID获取用户的详细信息")
    @GetMapping("/detail/{id}")
    public Result<UserVO> getUserDetail(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long id) {
        return Result.success(userService.getUserById(id));
    }


    /**
     * 分页查询商户管理员列表
     *
     * @param queryDTO 查询条件
     * @return 商户管理员列表
     */
    @ApiOperation(value = "分页查询商户管理员列表", notes = "根据条件分页查询商户管理员列表")
    @PostMapping("/merchant-admin/list")
    public Result<BasePageVO<MerchantAdminVO>> getMerchantAdminList(
            @ApiParam(value = "查询条件", required = true) @Validated @RequestBody MerchantAdminQueryDTO queryDTO) {
        return Result.success(userService.getMerchantAdminList(queryDTO));
    }

    /**
     * 获取商户管理员详情
     *
     * @param id 商户管理员ID
     * @return 商户管理员详情
     */
    @ApiOperation(value = "获取商户管理员详情", notes = "根据ID获取商户管理员详情")
    @GetMapping("/merchant-admin/detail/{id}")
    public Result<MerchantAdminVO> getMerchantAdminDetail(
            @ApiParam(value = "商户管理员ID", required = true) @PathVariable Long id) {
        return Result.success(userService.getMerchantAdminById(id));
    }

    /**
     * 更新用户状态
     *
     * @param statusUpdateDTO 状态更新信息
     * @return 操作结果
     */
    @ApiOperation(value = "更新用户状态", notes = "更新指定ID的用户状态，0-禁用，1-启用")
    @PostMapping("/updateStatus")
    public Result<Void> updateUserStatus(
            @ApiParam(value = "状态更新信息", required = true) @Valid @RequestBody StatusUpdateDTO statusUpdateDTO) {
        userService.updateUserStatus(statusUpdateDTO.getId(), statusUpdateDTO.getStatus());
        return Result.success();
    }

    /**
     * 更新用户机构关联
     *
     * @param userId 用户ID
     * @param orgIds 机构ID列表
     * @return 操作结果
     */
    @ApiOperation(value = "更新用户机构关联", notes = "更新指定用户的机构关联关系")
    @PostMapping("/updateOrgs/{userId}")
    public Result<Void> updateUserOrgs(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long userId,
            @ApiParam(value = "机构ID列表", required = true) @RequestBody List<String> orgIds) {
        boolean success = userService.updateUserOrgs(userId, orgIds);
        if (success) {
            return Result.success();
        } else {
            return Result.error("更新用户机构关联失败");
        }
    }
}