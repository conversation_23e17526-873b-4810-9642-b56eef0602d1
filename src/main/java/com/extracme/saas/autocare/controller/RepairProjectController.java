package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.dto.RepairProjectCreateDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectUpdateStatusDTO;
import com.extracme.saas.autocare.model.vo.RepairProjectDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairProjectListVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.service.RepairProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 修理项目管理接口 -3
 */

@Api(tags = "修理项目管理")
@RestController
@RequestMapping("/api/v1/repair-item")
public class RepairProjectController {

    @Autowired
    private RepairProjectService repairProjectService;

    /**
     * 查询修理项目列表
     * @param queryDTO 查询参数
     * @return 修理项目列表
     */
    @ApiOperation(value = "查询修理项目列表", notes = "分页查询列表")
    @PostMapping("/list")
    public Result<BasePageVO<RepairProjectListVO>> queryReplacePartItemList(@RequestBody RepairProjectQueryDTO queryDTO) {
        BasePageVO<RepairProjectListVO> pageVO = repairProjectService.queryRepairProjectList(queryDTO);
        return Result.success(pageVO);
    }

    /**
     * 获取修理项目详情
     */
    @ApiOperation(value = "获取修理项目详情", notes = "获取指定ID的修理项目详细信息")
    @GetMapping("/details/{id}")
    public Result<RepairProjectDetailsVO> getReplacePartItemDetails(@ApiParam(value = "修理项目id 必传", required = true) @PathVariable Long id) {
        RepairProjectDetailsVO repairProjectDetails = repairProjectService.getRepairProjectDetails(id);
        if (repairProjectDetails == null){
            return Result.error("未找到该数据");
        }
        return Result.success(repairProjectDetails);
    }

    /**
     * 创建修理项目
     * @param createDTO 创建修理项目
     */
    @ApiOperation(value = "创建修理项目")
    @PostMapping("/create")
    public Result<Void> createReplacePartItem(@Valid @RequestBody RepairProjectCreateDTO createDTO) {
        repairProjectService.createRepairProject(createDTO);
        return Result.success();
    }

    /**
     * 修改修理项目
     * @param updateDTO 修改修理项目
     */
    @ApiOperation(value = "修改修理项目")
    @PostMapping("/update")
    public Result<Void> updateReplacePartItem(@Valid @RequestBody RepairProjectUpdateDTO updateDTO) {
        repairProjectService.updateRepairProject(updateDTO);
        return Result.success();
    }

    @ApiOperation(value = "删除修理项目")
    @PostMapping("/delete")
    public Result<Void> deleteReplacePartItem(@ApiParam(value = "修理项目ID", required = true) @RequestBody Long id) {
        repairProjectService.deleteRepairProject(id);
        return Result.success();
    }


    @ApiOperation(value = "更新修理项目状态")
    @PostMapping("/updateStatus")
    public Result<Void> updateStatus(@RequestBody RepairProjectUpdateStatusDTO updateStatusDTO) {
        repairProjectService.updateRepairProjectStatus(updateStatusDTO);
        return Result.success();
    }
}