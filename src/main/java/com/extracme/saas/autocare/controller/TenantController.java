package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.annotation.RequireLogin;
import com.extracme.saas.autocare.model.dto.TenantDTO;
import com.extracme.saas.autocare.model.dto.TenantQueryDTO;
import com.extracme.saas.autocare.model.dto.TenantUpdateDTO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.TenantVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.TenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 租户管理控制器
 */
@Api(tags = "租户管理", description = "租户管理相关接口")
@RestController
@RequestMapping("/api/v1/tenant")
@RequiredArgsConstructor
public class TenantController {

    private final TenantService tenantService;

    /**
     * 创建租户
     */
    @ApiOperation(value = "创建租户", notes = "创建新的租户")
    @PostMapping("/create")
    public Result<Long> createTenant(
            @ApiParam(value = "租户信息", required = true) @Validated @RequestBody TenantDTO tenantDTO) {
        return Result.success(tenantService.createTenant(tenantDTO));
    }

    /**
     * 更新租户
     */
    @ApiOperation(value = "更新租户", notes = "更新指定ID的租户信息")
    @PostMapping("/update")
    public Result<Void> updateTenant(
            @ApiParam(value = "租户信息", required = true) @Validated @RequestBody TenantUpdateDTO tenantUpdateDTO) {
        tenantService.updateTenant(tenantUpdateDTO);
        return Result.success();
    }

    /**
     * 获取租户详情
     */
    @ApiOperation(value = "获取租户详情", notes = "获取指定ID的租户详细信息")
    @GetMapping("/detail/{id}")
    public Result<TenantVO> getTenant(@PathVariable Long id) {
        return Result.success(tenantService.getTenant(id));
    }

    /**
     * 分页查询租户列表
     */
    @ApiOperation(value = "分页查询租户列表", notes = "根据条件分页查询租户列表")
    @PostMapping("/list")
    public Result<BasePageVO<TenantVO>> getTenantList(
            @ApiParam(value = "查询条件", required = true) @RequestBody TenantQueryDTO queryDTO) {
        return Result.success(tenantService.getTenantList(queryDTO));
    }

    /**
     * 获取租户下拉列表
     * 返回所有有效的租户，用于下拉框选择
     *
     * @return 统一封装的租户下拉列表数据
     */
    @RequireLogin
    @ApiOperation(value = "获取租户下拉列表", notes = "获取所有有效租户的下拉列表数据，id为租户ID(Long类型)，value为租户名称，remark为租户编码")
    @GetMapping("/combo")
    public Result<List<ComboVO<Long>>> getTenantCombo() {
        return Result.success(tenantService.getTenantCombo());
    }
}
