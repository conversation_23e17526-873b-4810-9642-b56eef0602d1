package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.dto.ReplacePartItemCreateDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemQueryDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemUpdateDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemUpdateStatusDTO;
import com.extracme.saas.autocare.model.vo.PartRepairItemGroupingVO;
import com.extracme.saas.autocare.model.vo.ReplacePartItemDetailsVO;
import com.extracme.saas.autocare.model.vo.ReplacePartItemListVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.service.ReplacePartItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 换件项目管理接口 -2
 */

@Api(tags = "换件项目管理")
@RestController
@RequestMapping("/api/v1/replace-item")
public class ReplacePartItemController {

    @Autowired
    private ReplacePartItemService replacePartItemService;

    /**
     * 查询换件项目列表
     * @param queryDTO 查询参数
     * @return 换件项目列表
     */
    @ApiOperation(value = "查询换件项目列表", notes = "分页查询列表")
    @PostMapping("/list")
    public Result<BasePageVO<ReplacePartItemListVO>> queryReplacePartItemList(@RequestBody ReplacePartItemQueryDTO queryDTO) {
        BasePageVO<ReplacePartItemListVO> pageVO = replacePartItemService.queryReplacePartItemList(queryDTO);
        return Result.success(pageVO);
    }

    /**
     * 获取换件项目详情
     */
    @ApiOperation(value = "获取换件项目详情", notes = "获取指定ID的换件项目详细信息")
    @GetMapping("/details/{id}")
    public Result<ReplacePartItemDetailsVO> getReplacePartItemDetails(@ApiParam(value = "换件项目id 必传", required = true) @PathVariable Long id) {
        ReplacePartItemDetailsVO ReplacePartItem = replacePartItemService.getReplacePartItemDetails(id);
        if (ReplacePartItem == null){
            return Result.error("未找到该数据");
        }
        return Result.success(ReplacePartItem);
    }

    /**
     * 创建换件项目
     * @param createDTO 创建换件项目
     */
    @ApiOperation(value = "创建换件项目")
    @PostMapping("/create")
    public Result<Void> createReplacePartItem(@Valid @RequestBody ReplacePartItemCreateDTO createDTO) {
        replacePartItemService.createReplacePartItem(createDTO);
        return Result.success();
    }

    /**
     * 修改换件项目
     * @param updateDTO 修改换件项目
     */
    @ApiOperation(value = "修改换件项目")
    @PostMapping("/update")
    public Result<Void> updateReplacePartItem(@Valid @RequestBody ReplacePartItemUpdateDTO updateDTO) {
        replacePartItemService.updateReplacePartItem(updateDTO);
        return Result.success();
    }

    @ApiOperation(value = "删除换件项目")
    @PostMapping("/delete")
    public Result<Void> deleteReplacePartItem(@ApiParam(value = "换件项目ID", required = true) @RequestBody Long id) {
        replacePartItemService.deleteReplacePartItem(id);
        return Result.success();
    }


    @ApiOperation(value = "更新换件项目状态")
    @PostMapping("/updateStatus")
    public Result<Void> updateStatus(@RequestBody ReplacePartItemUpdateStatusDTO updateStatusDTO) {
        replacePartItemService.updateReplacePartItemStatus(updateStatusDTO);
        return Result.success();
    }

    /**
     * 获取零件修理项目分组数据
     */
    @ApiOperation(value = "获取零件修理项目分组数据")
    @GetMapping("/getItemGroupingTree/{groupingType}")
    public Result<List<PartRepairItemGroupingVO>> queryItemGroupingTree(@ApiParam(value = "分组类型 1:零件分组 2:修理项目分组 必传", required = true) @PathVariable Integer groupingType) {
        List<PartRepairItemGroupingVO> list = replacePartItemService.queryItemGroupingTree(groupingType);
        return Result.success(list);
    }
}