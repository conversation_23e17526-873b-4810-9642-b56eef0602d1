package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.DataAreaInfoDynamicSqlSupport.dataAreaInfo;
import static com.extracme.saas.autocare.mapper.base.DataCityInfoDynamicSqlSupport.dataCityInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.DataAreaInfoMapper;
import com.extracme.saas.autocare.mapper.base.DataCityInfoMapper;
import com.extracme.saas.autocare.mapper.base.DataProvinceInfoMapper;
import com.extracme.saas.autocare.model.entity.DataAreaInfo;
import com.extracme.saas.autocare.model.entity.DataCityInfo;
import com.extracme.saas.autocare.model.entity.DataProvinceInfo;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableRegionService;

import lombok.extern.slf4j.Slf4j;

/**
 * 地区数据表数据访问服务实现类
 * 
 * <AUTHOR>
 * @date 2024/06/03
 */
@Slf4j
@Repository
public class TableRegionServiceImpl implements TableRegionService {

    @Autowired
    private DataProvinceInfoMapper dataProvinceInfoMapper;

    @Autowired
    private DataCityInfoMapper dataCityInfoMapper;

    @Autowired
    private DataAreaInfoMapper dataAreaInfoMapper;

    @Override
    public List<DataProvinceInfo> findAllProvinces() {
        try {
            return dataProvinceInfoMapper.select(c -> c);
        } catch (Exception e) {
            log.error("查询所有省份信息失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<DataCityInfo> findCitiesByProvinceId(Long provinceId) {
        if (provinceId == null) {
            log.warn("省份ID为空，无法查询城市信息");
            return Collections.emptyList();
        }

        try {
            return dataCityInfoMapper.select(c ->
                c.where(dataCityInfo.fatherid, isEqualTo(provinceId))
            );
        } catch (Exception e) {
            log.error("根据省份ID查询城市信息失败，provinceId: {}", provinceId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<DataAreaInfo> findAreasByCityId(Long cityId) {
        if (cityId == null) {
            log.warn("城市ID为空，无法查询区域信息");
            return Collections.emptyList();
        }

        try {
            return dataAreaInfoMapper.select(c ->
                c.where(dataAreaInfo.fatherid, isEqualTo(cityId))
            );
        } catch (Exception e) {
            log.error("根据城市ID查询区域信息失败，cityId: {}", cityId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ComboVO<Long>> getProvinceCombo() {
        try {
            List<DataProvinceInfo> provinces = findAllProvinces();
            return provinces.stream()
                    .map(province -> {
                        ComboVO<Long> combo = new ComboVO<>();
                        combo.setId(province.getProvinceid());
                        combo.setValue(province.getProvince());
                        return combo;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取省份下拉框数据失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ComboVO<Long>> getCityCombo(Long provinceId) {
        try {
            List<DataCityInfo> cities = findCitiesByProvinceId(provinceId);
            return cities.stream()
                    .map(city -> {
                        ComboVO<Long> combo = new ComboVO<>();
                        combo.setId(city.getCityid());
                        combo.setValue(city.getCity());
                        return combo;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取城市下拉框数据失败，provinceId: {}", provinceId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ComboVO<Long>> getAreaCombo(Long cityId) {
        try {
            List<DataAreaInfo> areas = findAreasByCityId(cityId);
            return areas.stream()
                    .map(area -> {
                        ComboVO<Long> combo = new ComboVO<>();
                        combo.setId(area.getAreaid());
                        combo.setValue(area.getArea());
                        return combo;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取区域下拉框数据失败，cityId: {}", cityId, e);
            return Collections.emptyList();
        }
    }
}
