package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcCollisionPartsDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcCollisionPartsDynamicSqlSupport.mtcCollisionParts;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcCollisionPartsExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcCollisionParts;
import com.extracme.saas.autocare.repository.TableCollisionPartsService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 碰撞部件信息表数据访问服务实现
 */
@Repository
public class TableCollisionPartsServiceImpl implements TableCollisionPartsService {

    @Autowired
    private MtcCollisionPartsExtendMapper mtcCollisionPartsMapper;

    @Override
    public MtcCollisionParts insert(MtcCollisionParts record) {
        // 调用带操作人参数的方法
        return insert(record, "");
    }

    @Override
    public MtcCollisionParts insert(MtcCollisionParts record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcCollisionPartsMapper.insertSelective(record);
        return record;
    }

    @Override
    public MtcCollisionParts selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcCollisionPartsMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<MtcCollisionParts> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(mtcCollisionParts.taskNo, isEqualTo(taskNo))
                .orderBy(id.descending());
        return mtcCollisionPartsMapper.select(completer);
    }

    @Override
    public int updateSelectiveById(MtcCollisionParts record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcCollisionParts record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return mtcCollisionPartsMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchInsert(List<MtcCollisionParts> collisionPartsList) {
        if (collisionPartsList == null || collisionPartsList.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (MtcCollisionParts collisionParts : collisionPartsList) {
            count += mtcCollisionPartsMapper.insertSelective(collisionParts);
        }
        return count;
    }

    @Override
    public int updateStatus(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }
        
        Date now = new Date();
        String operator = null != SessionUtils.getLoginUser() ? SessionUtils.getLoginUser().getUsername() : "精友回调";
        
        return mtcCollisionPartsMapper.update(c -> 
            c.set(mtcCollisionParts.status).equalTo(0)
             .set(mtcCollisionParts.updatedTime).equalTo(now)
             .set(mtcCollisionParts.updateBy).equalTo(operator)
             .where(mtcCollisionParts.taskNo, isEqualTo(taskNo)));
    }
    
    @Override
    public int deleteByTaskNo(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }
        
        return mtcCollisionPartsMapper.delete(c -> 
            c.where(mtcCollisionParts.taskNo, isEqualTo(taskNo)));
    }
}
