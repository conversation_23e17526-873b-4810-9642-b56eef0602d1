package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcRepairDepotWarrantyVehicleModelInfo;

import java.util.List;

/**
 * 维修厂可保修车型表接口
 */
public interface TableRepairDepotWarrantyVehicleModelService extends DefaultTableService<MtcRepairDepotWarrantyVehicleModelInfo, Long> {

    /**
     * 查询维修厂可保修车型列表
     * @param repairDepotId
     * @return
     */
    List<MtcRepairDepotWarrantyVehicleModelInfo> queryVehicleModelList(String repairDepotId);

    /**
     * 批量插入维修厂可保修车型
     * @param vehicleModelList
     * @return
     */
    int batchInsert(List<MtcRepairDepotWarrantyVehicleModelInfo> vehicleModelList);

    /**
     * 根据维修厂id删除维修厂可保修车型
     * @param repairDepotId
     * @return
     */
    int deleteByRepairDepotId(String repairDepotId);

} 