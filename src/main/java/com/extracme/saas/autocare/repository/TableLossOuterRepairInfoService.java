package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcLossOuterRepairInfo;

import java.util.List;

/**
 * 损失外修信息表数据访问服务接口
 */
public interface TableLossOuterRepairInfoService extends DefaultTableService<MtcLossOuterRepairInfo, Long> {

    /**
     * 根据任务编号查询损失外修信息列表
     * @param taskNo 任务编号
     * @return 损失外修信息列表
     */
    List<MtcLossOuterRepairInfo> selectByTaskNo(String taskNo);
    
    /**
     * 批量插入损失外修信息
     * @param outerRepairInfoList 损失外修信息列表
     * @return 影响行数
     */
    int batchInsert(List<MtcLossOuterRepairInfo> outerRepairInfoList);
    
    /**
     * 根据任务编号更新损失外修信息状态为无效
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int updateStatus(String taskNo);
    
    /**
     * 根据任务编号删除损失外修信息
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int deleteByTaskNo(String taskNo);
    
    /**
     * 根据任务编号和状态更新损失外修信息
     * @param record 损失外修信息记录
     * @param taskNo 任务编号
     * @param operator 操作人
     * @return 影响行数
     */
    int updateSelectiveByTaskNoAndStatus(MtcLossOuterRepairInfo record, String taskNo, String operator);
}
