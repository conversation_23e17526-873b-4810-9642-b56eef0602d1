package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcPartRepairItemGrouping;

import java.util.List;

/**
 * 零件修理项目分组信息Repository接口
 */
public interface TablePartRepairItemGroupingService extends DefaultTableService<MtcPartRepairItemGrouping, Long> {

    /**
     * 零件修理项目分组查询
     * @param groupingType 分组类型 1:零件分组 2:修理项目分组
     * @return 零件修理项目分组列表信息
     */
    List<MtcPartRepairItemGrouping> queryPartRepairItemGroupingList(Integer groupingType);

}