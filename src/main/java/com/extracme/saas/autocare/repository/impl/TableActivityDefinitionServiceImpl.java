package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.ActivityDefinitionDynamicSqlSupport.activityDefinition;
import static com.extracme.saas.autocare.mapper.base.WorkflowActivityDynamicSqlSupport.workflowActivity;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.ActivityDefinitionMapper;
import com.extracme.saas.autocare.mapper.base.WorkflowActivityMapper;
import com.extracme.saas.autocare.model.entity.ActivityDefinition;
import com.extracme.saas.autocare.model.entity.WorkflowActivity;
import com.extracme.saas.autocare.repository.TableActivityDefinitionService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 活动节点定义表数据访问服务实现类
 */
@Repository
public class TableActivityDefinitionServiceImpl implements TableActivityDefinitionService {

    @Autowired
    private ActivityDefinitionMapper activityDefinitionMapper;

    @Autowired
    private WorkflowActivityMapper workflowActivityMapper;

    @Override
    public ActivityDefinition selectById(Long id) {
        return activityDefinitionMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public ActivityDefinition selectByActivityCode(String activityCode) {
        return activityDefinitionMapper.selectOne(c ->
                c.where(activityDefinition.activityCode, isEqualTo(activityCode))
        ).orElse(null);
    }

    @Override
    public List<ActivityDefinition> selectByWorkflowId(Long workflowId) {
        // 先查询工作流模板关联的活动节点编码
        List<WorkflowActivity> workflowActivities = workflowActivityMapper.select(c ->
                c.where(workflowActivity.workflowId, isEqualTo(workflowId))
        );

        if (workflowActivities.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取活动节点编码列表
        List<String> activityCodes = workflowActivities.stream()
                .map(WorkflowActivity::getActivityCode)
                .collect(Collectors.toList());

        // 查询活动节点定义
        return activityDefinitionMapper.select(c ->
                c.where(activityDefinition.activityCode, isIn(activityCodes))
                .orderBy(activityDefinition.sequence)
        );
    }

    @Override
    public int insert(ActivityDefinition activityDefinition) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(activityDefinition, operator);
    }

    @Override
    public int insert(ActivityDefinition activityDefinition, String operator) {
        if (activityDefinition == null) {
            return 0;
        }
        Date now = new Date();
        activityDefinition.setCreateTime(now);
        activityDefinition.setCreateBy(operator);
        activityDefinition.setUpdateTime(now);
        activityDefinition.setUpdateBy(operator);
        return activityDefinitionMapper.insertSelective(activityDefinition);
    }

    @Override
    public int updateSelectiveById(ActivityDefinition activityDefinition) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(activityDefinition, operator);
    }

    @Override
    public int updateSelectiveById(ActivityDefinition activityDefinition, String operator) {
        if (activityDefinition == null) {
            return 0;
        }
        Date now = new Date();
        activityDefinition.setUpdateTime(now);
        activityDefinition.setUpdateBy(operator);
        return activityDefinitionMapper.updateByPrimaryKeySelective(activityDefinition);
    }

    @Override
    public int update(ActivityDefinition activityDefinition) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的 updateSelectiveById 方法
        return updateSelectiveById(activityDefinition, operator);
    }

    @Override
    public int deleteById(Long id) {
        return activityDefinitionMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<ActivityDefinition> findAll() {
        return activityDefinitionMapper.select(c ->
                c.orderBy(activityDefinition.id)
        );
    }
}
