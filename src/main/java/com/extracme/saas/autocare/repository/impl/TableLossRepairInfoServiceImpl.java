package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairInfoDynamicSqlSupport.mtcLossRepairInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcLossRepairInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcLossRepairInfo;
import com.extracme.saas.autocare.repository.TableLossRepairInfoService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 损失维修信息表数据访问服务实现
 */
@Repository
public class TableLossRepairInfoServiceImpl implements TableLossRepairInfoService {

    @Autowired
    private MtcLossRepairInfoExtendMapper mtcLossRepairInfoMapper;

    @Override
    public MtcLossRepairInfo insert(MtcLossRepairInfo record) {
        // 调用带操作人参数的方法
        return insert(record, "");
    }

    @Override
    public MtcLossRepairInfo insert(MtcLossRepairInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcLossRepairInfoMapper.insertSelective(record);
        return record;
    }

    @Override
    public MtcLossRepairInfo selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcLossRepairInfoMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<MtcLossRepairInfo> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(mtcLossRepairInfo.taskNo, isEqualTo(taskNo))
                .and(mtcLossRepairInfo.status, isEqualTo(1))
                .orderBy(id.descending());
        return mtcLossRepairInfoMapper.select(completer);
    }

    @Override
    public int updateSelectiveById(MtcLossRepairInfo record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcLossRepairInfo record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return mtcLossRepairInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchInsert(List<MtcLossRepairInfo> repairInfoList) {
        if (repairInfoList == null || repairInfoList.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (MtcLossRepairInfo repairInfo : repairInfoList) {
            count += mtcLossRepairInfoMapper.insertSelective(repairInfo);
        }
        return count;
    }

    @Override
    public int updateStatus(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }
        
        Date now = new Date();
        String operator = null != SessionUtils.getLoginUser() ? SessionUtils.getLoginUser().getUsername() : "精友回调";
        
        return mtcLossRepairInfoMapper.update(c -> 
            c.set(mtcLossRepairInfo.status).equalTo(0)
             .set(mtcLossRepairInfo.updatedTime).equalTo(now)
             .set(mtcLossRepairInfo.updateBy).equalTo(operator)
             .where(mtcLossRepairInfo.taskNo, isEqualTo(taskNo)));
    }
    
    @Override
    public int deleteByTaskNo(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }
        
        return mtcLossRepairInfoMapper.delete(c -> 
            c.where(mtcLossRepairInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int updateSelectiveByTaskNoAndStatus(MtcLossRepairInfo record, String taskNo, String operator) {
        if (record == null || StringUtils.isBlank(taskNo)) {
            return 0;
        }
        
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        
        return mtcLossRepairInfoMapper.updateSelectiveByTaskNoAndStatus(record);
    }
}
