package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcRepairRemarkDynamicSqlSupport.createdTime;
import static com.extracme.saas.autocare.mapper.base.MtcRepairRemarkDynamicSqlSupport.id;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.List;

import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.MtcRepairRemarkDynamicSqlSupport;
import com.extracme.saas.autocare.mapper.extend.MtcRepairRemarkExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcRepairRemark;
import com.extracme.saas.autocare.repository.TableRepairRemarkService;

/**
 * 维修备注表数据访问服务实现
 */
@Repository
public class TableRepairRemarkServiceImpl implements TableRepairRemarkService {

    @Autowired
    private MtcRepairRemarkExtendMapper mtcRepairRemarkMapper;

    @Override
    public MtcRepairRemark insert(MtcRepairRemark record) {
        mtcRepairRemarkMapper.insertSelective(record);
        return record;
    }

    @Override
    public List<MtcRepairRemark> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairRemarkDynamicSqlSupport.taskNo, isEqualTo(taskNo))
                .orderBy(id.descending());
        return mtcRepairRemarkMapper.select(completer);
    }

    @Override
    public List<MtcRepairRemark> selectByTaskNoAndCreateBy(String taskNo, String createBy) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairRemarkDynamicSqlSupport.taskNo, isEqualTo(taskNo))
                .and(MtcRepairRemarkDynamicSqlSupport.createBy, isEqualTo(createBy))
                .orderBy(createdTime.descending());
        return mtcRepairRemarkMapper.select(completer);
    }

    @Override
    public List<MtcRepairRemark> selectByTaskNoAndActivityCode(String taskNo, String activityCode) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairRemarkDynamicSqlSupport.taskNo, isEqualTo(taskNo))
                .and(MtcRepairRemarkDynamicSqlSupport.activityCode, isEqualTo(activityCode))
                .orderBy(id.descending());
        return mtcRepairRemarkMapper.select(completer);
    }

    @Override
    public int batchInsert(List<MtcRepairRemark> remarkList) {
        int count = 0;
        for (MtcRepairRemark remark : remarkList) {
            count += mtcRepairRemarkMapper.insertSelective(remark);
        }
        return count;
    }

    /**
     * 根据ID删除维修备注
     * 
     * @param id 维修备注ID
     * @return 影响行数
     */
    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return mtcRepairRemarkMapper.deleteByPrimaryKey(id);
    }

    @Override
    public MtcRepairRemark selectById(Long id) {
        return mtcRepairRemarkMapper.selectByPrimaryKey(id).orElse(null);
    }
}
