package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcPartRepairItemGroupingDynamicSqlSupport.mtcPartRepairItemGrouping;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcPartRepairItemGroupingExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcPartRepairItemGrouping;
import com.extracme.saas.autocare.repository.TablePartRepairItemGroupingService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 零件修理项目分组Repository实现类
 */
@Repository
public class TablePartRepairItemGroupingServiceImpl implements TablePartRepairItemGroupingService {

    @Autowired
    private MtcPartRepairItemGroupingExtendMapper extendMapper;

    @Override
    public List<MtcPartRepairItemGrouping> queryPartRepairItemGroupingList(Integer groupingType) {
        if (groupingType == null) {
            return Collections.emptyList();
        }
        SelectStatementProvider selectStatementProvider = select(mtcPartRepairItemGrouping.allColumns())
                .from(mtcPartRepairItemGrouping)
                .where(mtcPartRepairItemGrouping.groupingType, isEqualTo(groupingType))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return extendMapper.selectMany(selectStatementProvider);
    }

    @Override
    public MtcPartRepairItemGrouping insert(MtcPartRepairItemGrouping record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(record, operator);
    }

    @Override
    public MtcPartRepairItemGrouping insert(MtcPartRepairItemGrouping record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        extendMapper.insertSelective(record);
        return record;
    }

    @Override
    public int updateSelectiveById(MtcPartRepairItemGrouping record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcPartRepairItemGrouping record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return extendMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public MtcPartRepairItemGrouping selectById(Long id) {
        if (id == null) {
            return null;
        }
        return extendMapper.selectByPrimaryKey(id).orElse(null);
    }
}