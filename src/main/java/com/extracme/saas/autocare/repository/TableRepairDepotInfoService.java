package com.extracme.saas.autocare.repository;

import java.util.List;

import com.extracme.saas.autocare.model.dto.RepairDepotQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.GetFirstPageInfoDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotInfo;

/**
 * 修理厂信息Repository接口
 */
public interface TableRepairDepotInfoService extends DefaultTableService<MtcRepairDepotInfo, Long> {

    /**
     * 根据条件查询列表
     * @param queryDTO 查询条件
     * @return 修理厂列表信息
     */
    List<MtcRepairDepotInfo> queryRepairDepotList(RepairDepotQueryDTO queryDTO,List<String> repairDepotIdList);


    /**
     * 根据修理厂名称查询修理厂信息
     * @param repairDepotName 修理厂名称
     * @return 修理厂信息
     */
    MtcRepairDepotInfo selectByRepairDepotName(String repairDepotName);

    /**
     * 根据修理厂编号查询修理厂信息
     * @param repairDepotCode 修理厂编号
     * @return 修理厂信息
     */
    MtcRepairDepotInfo selectByRepairDepotCode(String repairDepotCode);


    /**
     * 查询有效的修理厂列表
     * @return 修理厂列表
     */
    List<MtcRepairDepotInfo> findValidDepots();

    /**
     * 查询全部修理厂列表
     * @return 修理厂列表
     */
    List<MtcRepairDepotInfo> findAllDepots();

    /**
     * 查询所有租户的有效修理厂列表（超级管理员专用）
     * 绕过多租户拦截器，查询所有租户的修理厂信息
     * @return 所有租户的修理厂列表
     */
    List<MtcRepairDepotInfo> findAllValidDepotsForSuperAdmin();

    /**
     * 导出首页信息（游标分页）
     *
     * @param lastId 上一批次最后一条记录的ID（首次查询传入0）
     * @param getFirstPageInfoDTO 查询参数
     * @param pageSize 页大小
     * @return 维修厂首页统计信息列表
     */
    List<MtcRepairDepotInfo> exportFirstPageInfoWithCursor(Long lastId, GetFirstPageInfoDTO getFirstPageInfoDTO, int pageSize);
}
