package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcOrgInfoDynamicSqlSupport.mtcOrgInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.MtcOrgInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.util.OperatorUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 组织机构信息Repository实现类
 */
@Slf4j
@Repository
public class TableOrgInfoServiceImpl implements TableOrgInfoService {

    @Autowired
    private MtcOrgInfoExtendMapper mtcOrgInfoExtendMapper;

    @Override
    public MtcOrgInfo insert(MtcOrgInfo record) {
        // 使用公共工具类获取当前操作人，作为兜底机制
        String operator = OperatorUtils.getCurrentOperator();
        // 调用带操作人参数的方法
        return insert(record, operator);
    }

    @Override
    public MtcOrgInfo insert(MtcOrgInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreateTime(now);
        record.setCreateBy(operator);
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        mtcOrgInfoExtendMapper.insertSelective(record);
        return record;
    }

    @Override
    public List<MtcOrgInfo> findValidOrgs() {
        // 构建查询条件，查询所有组织机构
        SelectStatementProvider selectStatement = select(mtcOrgInfo.allColumns())
            .from(mtcOrgInfo)
            .orderBy(mtcOrgInfo.parentId)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询并处理空结果情况
        return Optional.ofNullable(mtcOrgInfoExtendMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public List<MtcOrgInfo> findValidOrgsByOrgId(String orgId) {
        // 构建查询条件，查询所有组织机构
        SelectStatementProvider selectStatement = select(mtcOrgInfo.allColumns())
                .from(mtcOrgInfo)
                .where(mtcOrgInfo.orgId, isLike(transRightFuzzyQueryParam(orgId)))
                .orderBy(mtcOrgInfo.parentId)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        // 执行查询并处理空结果情况
        return Optional.ofNullable(mtcOrgInfoExtendMapper.selectMany(selectStatement))
                .orElse(Collections.emptyList());
    }

    @Override
    public MtcOrgInfo selectByOrgId(String orgId) {
        // 参数校验
        if (orgId == null || orgId.isEmpty()) {
            return null;
        }
        
        // 使用MyBatis Dynamic SQL构建查询
        SelectStatementProvider selectStatement = select(mtcOrgInfo.allColumns())
            .from(mtcOrgInfo)
            .where(mtcOrgInfo.orgId, isEqualTo(orgId))
            .build()
            .render(RenderingStrategies.MYBATIS3);
        
        // 执行查询并处理结果
        return mtcOrgInfoExtendMapper.selectOne(selectStatement)
            .orElse(null);
    }

    @Override
    public MtcOrgInfo selectByUniqueId(String uniqueId) {
        if (uniqueId == null || uniqueId.isEmpty()) {
            return null;
        }
        SelectStatementProvider selectStatement = select(mtcOrgInfo.allColumns())
            .from(mtcOrgInfo)
            .where(mtcOrgInfo.uniqueId, isEqualTo(uniqueId))
            .build()
            .render(RenderingStrategies.MYBATIS3);
        return mtcOrgInfoExtendMapper.selectOne(selectStatement)
            .orElse(null);
    }

    @Override
    public MtcOrgInfo findByOrgId(String orgId) {
        // 复用selectByOrgId方法
        return selectByOrgId(orgId);
    }

    @Override
    public List<MtcOrgInfo> findByOrgIds(List<String> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 使用MyBatis Dynamic SQL构建IN查询
        SelectStatementProvider selectStatement = select(mtcOrgInfo.allColumns())
            .from(mtcOrgInfo)
            .where(mtcOrgInfo.orgId, isIn(orgIds))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询并处理空结果情况
        return Optional.ofNullable(mtcOrgInfoExtendMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public MtcOrgInfo selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcOrgInfoExtendMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public int updateSelectiveById(MtcOrgInfo record) {
        String operator = OperatorUtils.getCurrentOperator();
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcOrgInfo record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        return mtcOrgInfoExtendMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据主键删除记录
     *
     * @param id 主键ID
     * @return 影响行数
     */
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return mtcOrgInfoExtendMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<MtcOrgInfo> findDirectChildrenByParentId(String parentUniqueId) {
        if (parentUniqueId == null) {
            return Collections.emptyList();
        }

        // 使用MyBatis Dynamic SQL构建查询
        // 注意：这里的parentId字段存储的是父组织的uniqueId
        SelectStatementProvider selectStatement = select(mtcOrgInfo.allColumns())
            .from(mtcOrgInfo)
            .where(mtcOrgInfo.parentId, isEqualTo(parentUniqueId))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询并处理空结果情况
        return Optional.ofNullable(mtcOrgInfoExtendMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public List<String> findAllDescendantOrgIds(List<String> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return Collections.emptyList();
        }

        Set<String> allOrgIds = new LinkedHashSet<>(orgIds); // 保持顺序并去重
        Set<String> processedIds = new HashSet<>(); // 避免循环引用

        // 递归查询所有子机构
        for (String orgId : orgIds) {
            findDescendantOrgIdsRecursive(orgId, allOrgIds, processedIds);
        }

        return new ArrayList<>(allOrgIds);
    }

    /**
     * 递归查询指定机构的所有子机构ID（原有方法，保持向后兼容）
     * 注意：这个方法需要根据orgId找到对应的uniqueId，然后查询子机构
     *
     * @param orgId 机构ID
     * @param allOrgIds 结果集合
     * @param processedIds 已处理的机构ID集合，避免循环引用
     */
    private void findDescendantOrgIdsRecursive(String orgId, Set<String> allOrgIds, Set<String> processedIds) {
        if (orgId == null || processedIds.contains(orgId)) {
            return; // 避免循环引用
        }

        processedIds.add(orgId);

        // 首先根据orgId查找对应的组织信息，获取其uniqueId
        MtcOrgInfo orgInfo = selectByOrgId(orgId);
        if (orgInfo == null || orgInfo.getUniqueId() == null) {
            return;
        }

        // 使用uniqueId查询直接子机构
        List<MtcOrgInfo> children = findDirectChildrenByParentId(orgInfo.getUniqueId());
        for (MtcOrgInfo child : children) {
            String childOrgId = child.getOrgId();
            if (childOrgId != null && !allOrgIds.contains(childOrgId)) {
                allOrgIds.add(childOrgId);
                // 递归查询子机构的子机构
                findDescendantOrgIdsRecursive(childOrgId, allOrgIds, processedIds);
            }
        }
    }

    @Override
    public List<String> findAllDescendantOrgIdsBatch(List<String> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return Collections.emptyList();
        }

        try {
            // 1. 一次性查询当前租户下的所有有效组织数据
            List<MtcOrgInfo> allOrgs = findValidOrgs();
            if (allOrgs.isEmpty()) {
                log.debug("当前租户下没有有效的组织数据");
                return Collections.emptyList();
            }

            // 2. 构建组织映射关系
            // orgId -> uniqueId 的映射
            Map<String, String> orgIdToUniqueIdMap = allOrgs.stream()
                .filter(org -> org.getOrgId() != null && org.getUniqueId() != null)
                .collect(Collectors.toMap(MtcOrgInfo::getOrgId, MtcOrgInfo::getUniqueId));

            // parentId -> 子组织列表 的映射（基于uniqueId和parentId的关系）
            Map<String, List<MtcOrgInfo>> parentToChildrenMap = allOrgs.stream()
                .filter(org -> org.getParentId() != null)
                .collect(Collectors.groupingBy(MtcOrgInfo::getParentId));

            // 3. 将输入的orgId转换为uniqueId，然后递归查找所有子组织
            Set<String> resultOrgIds = new LinkedHashSet<>(orgIds); // 保持顺序并去重
            Set<String> processedUniqueIds = new HashSet<>(); // 避免循环引用

            for (String orgId : orgIds) {
                String uniqueId = orgIdToUniqueIdMap.get(orgId);
                if (uniqueId != null) {
                    findDescendantOrgIdsBatchRecursive(uniqueId, parentToChildrenMap,
                                                     resultOrgIds, processedUniqueIds);
                }
            }

            log.debug("批量查询组织层级完成，输入组织数: {}, 结果组织数: {}",
                     orgIds.size(), resultOrgIds.size());

            return new ArrayList<>(resultOrgIds);
        } catch (Exception e) {
            log.error("批量查询组织层级失败，输入组织: {}", orgIds, e);
            // 发生错误时返回原始列表，确保基本功能可用
            return new ArrayList<>(orgIds);
        }
    }

    /**
     * 批量模式下的递归查询子组织ID
     *
     * @param uniqueId 当前组织的uniqueId
     * @param parentToChildrenMap 父组织到子组织的映射
     * @param resultOrgIds 结果集合（存储orgId）
     * @param processedUniqueIds 已处理的uniqueId集合，避免循环引用
     */
    private void findDescendantOrgIdsBatchRecursive(String uniqueId,
                                                   Map<String, List<MtcOrgInfo>> parentToChildrenMap,
                                                   Set<String> resultOrgIds,
                                                   Set<String> processedUniqueIds) {
        if (uniqueId == null || processedUniqueIds.contains(uniqueId)) {
            return; // 避免循环引用
        }

        processedUniqueIds.add(uniqueId);

        // 查找以当前uniqueId为parentId的所有子组织
        List<MtcOrgInfo> children = parentToChildrenMap.get(uniqueId);
        if (children != null) {
            for (MtcOrgInfo child : children) {
                String childOrgId = child.getOrgId();
                String childUniqueId = child.getUniqueId();

                if (childOrgId != null && !resultOrgIds.contains(childOrgId)) {
                    resultOrgIds.add(childOrgId);

                    // 递归查询子组织的子组织
                    if (childUniqueId != null) {
                        findDescendantOrgIdsBatchRecursive(childUniqueId, parentToChildrenMap,
                                                         resultOrgIds, processedUniqueIds);
                    }
                }
            }
        }
    }

}
