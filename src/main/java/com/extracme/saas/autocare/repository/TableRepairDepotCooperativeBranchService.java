package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcRepairDepotCooperativeBranch;

import java.util.List;

/**
 * 维修厂合作分公司表接口
 */
public interface TableRepairDepotCooperativeBranchService extends DefaultTableService<MtcRepairDepotCooperativeBranch, Long> {


    /**
     * 根据维修厂ID查询合作分公司信息
     *
     * @param repairDepotId 维修厂ID
     * @return 维修厂合作分公司信息列表
     */
    List<MtcRepairDepotCooperativeBranch> queryCooperativeBranchList(String repairDepotId);

    /**
     * 根据组织机构ID查询合作分公司信息
     *
     * @param orgId 组织机构ID
     * @return 维修厂合作分公司信息列表
     */
    List<MtcRepairDepotCooperativeBranch> queryCooperativeBranchListByOrgId(String orgId);

    /**
     * 根据组织机构ID查询合作分公司信息
     *
     * @param orgIdList 组织机构ID
     * @return 维修厂合作分公司信息列表
     */
    List<MtcRepairDepotCooperativeBranch> queryCooperativeBranchList(List<String> orgIdList);

    /**
     * 批量插入合作分公司信息
     * @param cooperativeBranchList
     * @return
     */
    int batchInsert(List<MtcRepairDepotCooperativeBranch> cooperativeBranchList);

    /**
     * 根据维修厂ID删除合作分公司
     *
     * @param repairDepotId 维修厂ID
     * @return 删除结果
     */
    int deleteByRepairDepotId(String repairDepotId);
} 