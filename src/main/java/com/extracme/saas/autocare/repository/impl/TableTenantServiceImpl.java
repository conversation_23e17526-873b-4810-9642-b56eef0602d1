package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.SysTenantDynamicSqlSupport.sysTenant;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.extracme.saas.autocare.mapper.base.SysTenantMapper;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.repository.TableTenantService;

/**
 * 租户表数据访问服务实现类
 */
@Repository
public class TableTenantServiceImpl implements TableTenantService {

    @Autowired
    private SysTenantMapper tenantMapper;

    @Override
    public SysTenant selectById(Long id) {
        if (id == null) {
            return null;
        }
        return tenantMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public Optional<SysTenant> findByTenantCode(String tenantCode) {
        if (!StringUtils.hasText(tenantCode)) {
            return Optional.empty();
        }
        return tenantMapper.selectOne(c -> c.where(sysTenant.tenantCode, isEqualTo(tenantCode)));
    }

    @Override
    public List<SysTenant> findByCondition(String tenantName, String tenantCode, Integer status) {
        // 使用MyBatis Dynamic SQL的条件查询方式，统一处理所有查询条件
        // isLikeWhenPresent和isEqualToWhenPresent会自动处理参数为null或空的情况
        return tenantMapper.select(c -> c
            .where(sysTenant.tenantName, isLikeWhenPresent(transFuzzyQueryParam(tenantName)))
            .and(sysTenant.tenantCode, isLikeWhenPresent(transFuzzyQueryParam(tenantCode)))
            .and(sysTenant.status, isEqualToWhenPresent(status))
            .orderBy(sysTenant.tenantCode.descending())
        );
    }

    @Override
    public SysTenant insert(SysTenant tenant) {
        // 使用系统操作人
        return insert(tenant, "system");
    }

    @Override
    public SysTenant insert(SysTenant tenant, String operator) {
        Date now = new Date();
        tenant.setCreatedTime(now);
        tenant.setUpdatedTime(now);
        tenant.setCreateBy(operator);
        tenant.setUpdateBy(operator);
        tenantMapper.insertSelective(tenant);
        return tenant;
    }

    @Override
    public int updateSelectiveById(SysTenant tenant) {
        // 使用系统操作人
        return updateSelectiveById(tenant, "system");
    }

    @Override
    public int updateSelectiveById(SysTenant tenant, String operator) {
        Date now = new Date();
        tenant.setUpdatedTime(now);
        tenant.setUpdateBy(operator);
        return tenantMapper.updateByPrimaryKeySelective(tenant);
    }

    @Override
    public boolean existsByTenantCode(String tenantCode) {
        if (!StringUtils.hasText(tenantCode)) {
            return false;
        }
        return tenantMapper.count(c -> c.where(sysTenant.tenantCode, isEqualTo(tenantCode))) > 0;
    }

    @Override
    public boolean existsByTenantName(String tenantName) {
        if (!StringUtils.hasText(tenantName)) {
            return false;
        }
        return tenantMapper.count(c -> c.where(sysTenant.tenantName, isEqualTo(tenantName))) > 0;
    }
}
