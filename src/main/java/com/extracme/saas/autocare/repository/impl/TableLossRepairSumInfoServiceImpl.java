package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcLossRepairSumInfoDynamicSqlSupport.mtcLossRepairSumInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcLossRepairSumInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcLossRepairSumInfo;
import com.extracme.saas.autocare.repository.TableLossRepairSumInfoService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 损失维修汇总信息表数据访问服务实现
 */
@Repository
public class TableLossRepairSumInfoServiceImpl implements TableLossRepairSumInfoService {

    @Autowired
    private MtcLossRepairSumInfoExtendMapper mtcLossRepairSumInfoMapper;

    @Override
    public MtcLossRepairSumInfo insert(MtcLossRepairSumInfo record) {
        // 调用带操作人参数的方法
        return insert(record, "");
    }

    @Override
    public MtcLossRepairSumInfo insert(MtcLossRepairSumInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcLossRepairSumInfoMapper.insertSelective(record);
        return record;
    }

    @Override
    public MtcLossRepairSumInfo selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcLossRepairSumInfoMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<MtcLossRepairSumInfo> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(mtcLossRepairSumInfo.taskNo, isEqualTo(taskNo))
                .orderBy(id.descending());
        return mtcLossRepairSumInfoMapper.select(completer);
    }

    @Override
    public int updateSelectiveById(MtcLossRepairSumInfo record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcLossRepairSumInfo record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return mtcLossRepairSumInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchInsert(List<MtcLossRepairSumInfo> repairSumInfoList) {
        if (repairSumInfoList == null || repairSumInfoList.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (MtcLossRepairSumInfo repairSumInfo : repairSumInfoList) {
            count += mtcLossRepairSumInfoMapper.insertSelective(repairSumInfo);
        }
        return count;
    }

    @Override
    public int updateStatus(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }
        
        Date now = new Date();
        String operator = null != SessionUtils.getLoginUser() ? SessionUtils.getLoginUser().getUsername() : "精友回调";
        
        return mtcLossRepairSumInfoMapper.update(c -> 
            c.set(mtcLossRepairSumInfo.status).equalTo(0)
             .set(mtcLossRepairSumInfo.updatedTime).equalTo(now)
             .set(mtcLossRepairSumInfo.updateBy).equalTo(operator)
             .where(mtcLossRepairSumInfo.taskNo, isEqualTo(taskNo)));
    }
    
    @Override
    public int deleteByTaskNo(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }
        
        return mtcLossRepairSumInfoMapper.delete(c -> 
            c.where(mtcLossRepairSumInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int updateSelectiveByTaskNoAndStatus(MtcLossRepairSumInfo record, String taskNo, String operator) {
        if (record == null || StringUtils.isBlank(taskNo)) {
            return 0;
        }
        
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        
        return mtcLossRepairSumInfoMapper.updateSelectiveByTaskNoAndStatus(record);
    }
}
