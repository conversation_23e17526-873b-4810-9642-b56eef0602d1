package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskDynamicSqlSupport.mtcRepairTask;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryDynamicSqlSupport.mtcRepairTaskLeavingFactory;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryDynamicSqlSupport.updateBy;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryDynamicSqlSupport.updatedTime;
import static com.extracme.saas.autocare.mapper.base.WorkflowInstanceDynamicSqlSupport.workflowInstance;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryDynamicSqlSupport;
import com.extracme.saas.autocare.mapper.extend.MtcRepairTaskLeavingFactoryExtendMapper;
import com.extracme.saas.autocare.model.dto.VehicleLeavingFactoryQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTaskLeavingFactory;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryResultVO;
import com.extracme.saas.autocare.repository.TableRepairTaskLeavingFactoryService;
import com.extracme.saas.autocare.util.DateTimeUtils;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 维修任务出厂表数据访问服务实现
 */
@Repository
public class TableRepairTaskLeavingFactoryServiceImpl implements TableRepairTaskLeavingFactoryService {

    @Autowired
    private MtcRepairTaskLeavingFactoryExtendMapper mtcRepairTaskLeavingFactoryMapper;

    @Override
    public MtcRepairTaskLeavingFactory insert(MtcRepairTaskLeavingFactory record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(record, operator);
    }

    @Override
    public MtcRepairTaskLeavingFactory insert(MtcRepairTaskLeavingFactory record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcRepairTaskLeavingFactoryMapper.insertSelective(record);
        return record;
    }

    @Override
    public int updateSelectiveById(MtcRepairTaskLeavingFactory record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcRepairTaskLeavingFactory record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return mtcRepairTaskLeavingFactoryMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public MtcRepairTaskLeavingFactory selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcRepairTaskLeavingFactoryMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<MtcRepairTaskLeavingFactory> selectByVin(String vin) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairTaskLeavingFactoryDynamicSqlSupport.vin, isEqualTo(vin))
                .orderBy(mtcRepairTaskLeavingFactory.id.descending());
        return mtcRepairTaskLeavingFactoryMapper.select(completer);
    }

    @Override
    public List<MtcRepairTaskLeavingFactory> selectByRepairDepotId(String repairDepotId) {
        SelectDSLCompleter completer = c -> c
                .where(MtcRepairTaskLeavingFactoryDynamicSqlSupport.repairDepotId, isEqualTo(repairDepotId))
                .orderBy(mtcRepairTaskLeavingFactory.id.descending());
        return mtcRepairTaskLeavingFactoryMapper.select(completer);
    }

    @Override
    public int updateLeavingStatus(String taskNo, Integer leavingStatus, String operator) {
        MtcRepairTaskLeavingFactory record = new MtcRepairTaskLeavingFactory();
        record.setLeavingStatus(leavingStatus);
        record.setUpdateBy(operator);
        record.setUpdatedTime(new Date());

        return mtcRepairTaskLeavingFactoryMapper.update(
                c -> c.set(MtcRepairTaskLeavingFactoryDynamicSqlSupport.leavingStatus).equalTo(record::getLeavingStatus)
                        .set(updateBy).equalTo(record::getUpdateBy)
                        .set(updatedTime).equalTo(record::getUpdatedTime)
                        .where(MtcRepairTaskLeavingFactoryDynamicSqlSupport.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public List<VehicleLeavingFactoryResultVO> selectLeavingFactoryList(VehicleLeavingFactoryQueryDTO queryDTO) {
        SelectStatementProvider selectStatement = select(
                mtcRepairTask.id,
                mtcRepairTask.taskNo,
                mtcRepairTask.vin,
                mtcRepairTask.vehicleNo,
                mtcRepairTask.vehicleModelSeq,
                mtcRepairTask.vehicleModelInfo,
                mtcRepairTask.orgId,
                mtcRepairTask.orgName,
                mtcRepairTask.repairTypeId,
                mtcRepairTask.repairTypeName,
                mtcRepairTask.factOperateTag,
                mtcRepairTask.renttype,
                mtcRepairTask.repairGrade,
                mtcRepairTask.insuranceCompanyName,
                mtcRepairTask.reviewToSelFeeFlag,
                mtcRepairTaskLeavingFactory.id.as("leaving_factory_id"),
                mtcRepairTaskLeavingFactory.repairDepotId,
                mtcRepairTaskLeavingFactory.repairDepotName,
                mtcRepairTaskLeavingFactory.repairDepotOrgId,
                mtcRepairTaskLeavingFactory.repairDepotSapCode,
                mtcRepairTaskLeavingFactory.deliveryTime,
                mtcRepairTask.vehicleCheckTime,
                mtcRepairTaskLeavingFactory.repairTaskInflowTime.as("task_inflow_time"),
                mtcRepairTaskLeavingFactory.repairTaskReceiveTime.as("task_receive_time"),
                mtcRepairTaskLeavingFactory.createdTime.as("submit_date_time"),
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode)
                .from(mtcRepairTaskLeavingFactory)
                .leftJoin(mtcRepairTask).on(mtcRepairTaskLeavingFactory.taskNo, equalTo(mtcRepairTask.taskNo))
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .where()
                .and(mtcRepairTask.orgId, isInWhenPresent(queryDTO.getLoginUserOrgIds()))
                .and(mtcRepairTask.orgId, isEqualToWhenPresent(queryDTO.getOrgId()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairDepotId, isEqualToWhenPresent(queryDTO.getRepairDepotId()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTaskLeavingFactory.taskNo, isEqualToWhenPresent(queryDTO.getTaskNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.vehicleNo, isEqualToWhenPresent(queryDTO.getVehicleNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelId()))
                .and(mtcRepairTaskLeavingFactory.vin, isEqualToWhenPresent(queryDTO.getVin()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairTypeId, isEqualToWhenPresent(queryDTO.getRepairTypeId()))
                .and(workflowInstance.currentActivityCode, isEqualToWhenPresent(queryDTO.getCurrentActivityCode()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.taskInflowTime, isBetweenWhenPresent(queryDTO.getStartTaskInflowTime()).and(DateTimeUtils.addOneDay(queryDTO.getEndTaskInflowTime())))
                .and(mtcRepairTask.vehicleCheckTime, isBetweenWhenPresent(queryDTO.getStartVehicleCheckTime()).and(DateTimeUtils.addOneDay(queryDTO.getEndVehicleCheckTime())))
                .and(mtcRepairTaskLeavingFactory.deliveryTime, isBetweenWhenPresent(queryDTO.getStartDeliveryTime()).and(DateTimeUtils.addOneDay(queryDTO.getEndDeliveryTime())))
                .and(mtcRepairTaskLeavingFactory.leavingStatus, isEqualToWhenPresent(queryDTO.getLeavingStatus()))
                .orderBy(mtcRepairTaskLeavingFactory.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskLeavingFactoryMapper.selectLeavingFactoryList(selectStatement);
    }

    @Override
    public List<VehicleLeavingFactoryResultVO> exportLeavingFactoryList(VehicleLeavingFactoryQueryDTO queryDTO, long lastId, int pageSize) {
        SelectStatementProvider selectStatement = select(
                mtcRepairTask.id,
                mtcRepairTask.taskNo,
                mtcRepairTask.vin,
                mtcRepairTask.vehicleNo,
                mtcRepairTask.vehicleModelSeq,
                mtcRepairTask.vehicleModelInfo,
                mtcRepairTask.orgId,
                mtcRepairTask.orgName,
                mtcRepairTask.repairTypeId,
                mtcRepairTask.repairTypeName,
                mtcRepairTask.factOperateTag,
                mtcRepairTask.renttype,
                mtcRepairTask.repairGrade,
                mtcRepairTask.insuranceCompanyName,
                mtcRepairTask.reviewToSelFeeFlag,
                mtcRepairTask.vehicleCheckTime,
                mtcRepairTaskLeavingFactory.id.as("leaving_factory_id"),
                mtcRepairTaskLeavingFactory.repairDepotId,
                mtcRepairTaskLeavingFactory.repairDepotName,
                mtcRepairTaskLeavingFactory.repairDepotOrgId,
                mtcRepairTaskLeavingFactory.repairDepotSapCode,
                mtcRepairTaskLeavingFactory.deliveryTime,
                mtcRepairTaskLeavingFactory.repairTaskInflowTime.as("task_inflow_time"),
                mtcRepairTaskLeavingFactory.repairTaskReceiveTime.as("task_receive_time"),
                mtcRepairTaskLeavingFactory.createdTime.as("submit_date_time"),
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode)
                .from(mtcRepairTaskLeavingFactory)
                .leftJoin(mtcRepairTask).on(mtcRepairTaskLeavingFactory.taskNo, equalTo(mtcRepairTask.taskNo))
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .where()
                .and(mtcRepairTask.orgId, isInWhenPresent(queryDTO.getLoginUserOrgIds()))
                .and(mtcRepairTask.orgId, isEqualToWhenPresent(queryDTO.getOrgId()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairDepotId, isEqualToWhenPresent(queryDTO.getRepairDepotId()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTaskLeavingFactory.taskNo, isEqualToWhenPresent(queryDTO.getTaskNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.vehicleNo, isEqualToWhenPresent(queryDTO.getVehicleNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelId()))
                .and(mtcRepairTaskLeavingFactory.vin, isEqualToWhenPresent(queryDTO.getVin()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairTypeId, isEqualToWhenPresent(queryDTO.getRepairTypeId()))
                .and(workflowInstance.currentActivityCode, isEqualToWhenPresent(queryDTO.getCurrentActivityCode()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.taskInflowTime, isBetweenWhenPresent(queryDTO.getStartTaskInflowTime()).and(DateTimeUtils.addOneDay(queryDTO.getEndTaskInflowTime())))
                .and(mtcRepairTask.vehicleCheckTime, isBetweenWhenPresent(queryDTO.getStartVehicleCheckTime()).and(DateTimeUtils.addOneDay(queryDTO.getEndVehicleCheckTime())))
                .and(mtcRepairTaskLeavingFactory.deliveryTime, isBetweenWhenPresent(queryDTO.getStartDeliveryTime()).and(DateTimeUtils.addOneDay(queryDTO.getEndDeliveryTime())))
                .and(mtcRepairTaskLeavingFactory.leavingStatus, isEqualToWhenPresent(queryDTO.getLeavingStatus()))
                .and(mtcRepairTaskLeavingFactory.id, isGreaterThanWhenPresent(lastId))
                .orderBy(mtcRepairTaskLeavingFactory.id.descending())
                .limit(pageSize)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskLeavingFactoryMapper.selectLeavingFactoryList(selectStatement);
    }

    @Override
    public List<VehicleLeavingFactoryResultVO> selectByTaskNo(String taskNo) {
        SelectStatementProvider selectStatement = select(
                mtcRepairTask.id,
                mtcRepairTask.taskNo,
                mtcRepairTask.vin,
                mtcRepairTask.vehicleNo,
                mtcRepairTask.vehicleModelSeq,
                mtcRepairTask.vehicleModelInfo,
                mtcRepairTask.orgId,
                mtcRepairTask.orgName,
                mtcRepairTask.repairTypeId,
                mtcRepairTask.repairTypeName,
                mtcRepairTask.factOperateTag,
                mtcRepairTask.renttype,
                mtcRepairTask.repairGrade,
                mtcRepairTask.insuranceCompanyName,
                mtcRepairTask.reviewToSelFeeFlag,
                mtcRepairTaskLeavingFactory.id.as("leaving_factory_id"),
                mtcRepairTaskLeavingFactory.leavingStatus,
                mtcRepairTaskLeavingFactory.repairDepotId,
                mtcRepairTaskLeavingFactory.repairDepotName,
                mtcRepairTaskLeavingFactory.repairDepotOrgId,
                mtcRepairTaskLeavingFactory.repairDepotSapCode,
                mtcRepairTaskLeavingFactory.deliveryTime,
                mtcRepairTask.vehicleCheckTime,
                mtcRepairTaskLeavingFactory.repairTaskInflowTime.as("task_inflow_time"),
                mtcRepairTaskLeavingFactory.repairTaskReceiveTime.as("task_receive_time"),
                mtcRepairTaskLeavingFactory.createdTime.as("submit_date_time"),
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode)
                .from(mtcRepairTaskLeavingFactory)
                .leftJoin(mtcRepairTask).on(mtcRepairTaskLeavingFactory.taskNo, equalTo(mtcRepairTask.taskNo))
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .where()
                .and(mtcRepairTaskLeavingFactory.taskNo, isEqualTo(taskNo))
                .orderBy(mtcRepairTaskLeavingFactory.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskLeavingFactoryMapper.selectLeavingFactoryList(selectStatement);
    }

    @Override
    public MtcRepairTaskLeavingFactory selectLatestRecordByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c
                .where(MtcRepairTaskLeavingFactoryDynamicSqlSupport.taskNo, isEqualTo(taskNo))
                .orderBy(mtcRepairTaskLeavingFactory.id.descending()).limit(1);
        return mtcRepairTaskLeavingFactoryMapper.selectOne(completer).orElse(null);
    }

    @Override
    public long selectLeavingFactoryCount(VehicleLeavingFactoryQueryDTO queryDTO, Integer taskType) {
        SelectStatementProvider selectStatement = select(
                count(mtcRepairTaskLeavingFactory.id))
                .from(mtcRepairTaskLeavingFactory)
                .leftJoin(mtcRepairTask).on(mtcRepairTaskLeavingFactory.taskNo, equalTo(mtcRepairTask.taskNo))
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .where()
                .and(mtcRepairTask.orgId, isInWhenPresent(queryDTO.getLoginUserOrgIds()))
                .and(mtcRepairTask.orgId, isEqualToWhenPresent(queryDTO.getOrgId()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairDepotId, isEqualToWhenPresent(queryDTO.getRepairDepotId()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTaskLeavingFactory.taskNo, isEqualToWhenPresent(queryDTO.getTaskNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.vehicleNo, isEqualToWhenPresent(queryDTO.getVehicleNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTaskLeavingFactory.vin, isEqualToWhenPresent(queryDTO.getVin()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairTypeId, isEqualToWhenPresent(queryDTO.getRepairTypeId()))
                .and(workflowInstance.currentActivityCode, isEqualToWhenPresent(queryDTO.getCurrentActivityCode()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.taskInflowTime, isBetweenWhenPresent(queryDTO.getStartTaskInflowTime()).and(DateTimeUtils.addOneDay(queryDTO.getEndTaskInflowTime())))
                .and(mtcRepairTask.vehicleCheckTime, isBetweenWhenPresent(queryDTO.getStartVehicleCheckTime()).and(DateTimeUtils.addOneDay(queryDTO.getEndVehicleCheckTime())))
                .and(mtcRepairTaskLeavingFactory.deliveryTime, isBetweenWhenPresent(queryDTO.getStartDeliveryTime()).and(DateTimeUtils.addOneDay(queryDTO.getEndDeliveryTime())))
                .and(mtcRepairTaskLeavingFactory.leavingStatus, isEqualTo(taskType))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskLeavingFactoryMapper.count(selectStatement);
    }
}
