package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.SysTenantSyncKeyDynamicSqlSupport.keyStatus;
import static com.extracme.saas.autocare.mapper.base.SysTenantSyncKeyDynamicSqlSupport.lastUsedTime;
import static com.extracme.saas.autocare.mapper.base.SysTenantSyncKeyDynamicSqlSupport.syncKey;
import static com.extracme.saas.autocare.mapper.base.SysTenantSyncKeyDynamicSqlSupport.sysTenantSyncKey;
import static com.extracme.saas.autocare.mapper.base.SysTenantSyncKeyDynamicSqlSupport.tenantCode;
import static com.extracme.saas.autocare.mapper.base.SysTenantSyncKeyDynamicSqlSupport.updateBy;
import static com.extracme.saas.autocare.mapper.base.SysTenantSyncKeyDynamicSqlSupport.updateTime;
import static com.extracme.saas.autocare.mapper.base.SysTenantSyncKeyDynamicSqlSupport.usageCount;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.extracme.saas.autocare.mapper.extend.SysTenantSyncKeyExtendMapper;
import com.extracme.saas.autocare.model.entity.SysTenantSyncKey;
import com.extracme.saas.autocare.repository.TableTenantSyncKeyService;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 租户同步密钥管理表服务实现类
 */
@Slf4j
@Repository
public class TableTenantSyncKeyServiceImpl implements TableTenantSyncKeyService {

    @Autowired
    private SysTenantSyncKeyExtendMapper sysTenantSyncKeyMapper;

    @Override
    public SysTenantSyncKey insert(SysTenantSyncKey record) {
        String operator = SessionUtils.getUsername();
        return insert(record, operator);
    }

    @Override
    public SysTenantSyncKey insert(SysTenantSyncKey record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreateTime(now);
        record.setCreateBy(operator);
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        sysTenantSyncKeyMapper.insertSelective(record);
        return record;
    }

    @Override
    public SysTenantSyncKey selectById(Long id) {
        if (id == null) {
            return null;
        }
        return sysTenantSyncKeyMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public int updateSelectiveById(SysTenantSyncKey record) {
        String operator = SessionUtils.getUsername();
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(SysTenantSyncKey record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        return sysTenantSyncKeyMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public SysTenantSyncKey findByTenantCode(String tenantCode) {
        if (!StringUtils.hasText(tenantCode)) {
            return null;
        }

        SelectStatementProvider selectStatement = select(sysTenantSyncKey.allColumns())
            .from(sysTenantSyncKey)
            .where(sysTenantSyncKey.tenantCode, isEqualTo(tenantCode))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return sysTenantSyncKeyMapper.selectOne(selectStatement).orElse(null);
    }

    @Override
    public SysTenantSyncKey findByTenantId(Long tenantId) {
        if (tenantId == null) {
            return null;
        }

        SelectStatementProvider selectStatement = select(sysTenantSyncKey.allColumns())
            .from(sysTenantSyncKey)
            .where(sysTenantSyncKey.tenantId, isEqualTo(tenantId))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return sysTenantSyncKeyMapper.selectOne(selectStatement).orElse(null);
    }

    @Override
    public SysTenantSyncKey findBySyncKey(String syncKeyParam) {
        if (!StringUtils.hasText(syncKeyParam)) {
            return null;
        }

        SelectStatementProvider selectStatement = select(sysTenantSyncKey.allColumns())
            .from(sysTenantSyncKey)
            .where(syncKey, isEqualTo(syncKeyParam))
            .and(keyStatus, isEqualTo(1))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return sysTenantSyncKeyMapper.selectOne(selectStatement).orElse(null);
    }

    @Override
    public List<SysTenantSyncKey> findAll() {
        SelectStatementProvider selectStatement = select(sysTenantSyncKey.allColumns())
            .from(sysTenantSyncKey)
            .orderBy(sysTenantSyncKey.createTime.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return Optional.ofNullable(sysTenantSyncKeyMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public List<SysTenantSyncKey> findAllActiveKeys() {
        SelectStatementProvider selectStatement = select(sysTenantSyncKey.allColumns())
            .from(sysTenantSyncKey)
            .where(sysTenantSyncKey.keyStatus, isEqualTo(1))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return Optional.ofNullable(sysTenantSyncKeyMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public List<SysTenantSyncKey> findByKeyStatus(Integer keyStatus) {
        if (keyStatus == null) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = select(sysTenantSyncKey.allColumns())
            .from(sysTenantSyncKey)
            .where(sysTenantSyncKey.keyStatus, isEqualTo(keyStatus))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return Optional.ofNullable(sysTenantSyncKeyMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public boolean updateUsageStats(String tenantCodeParam) {
        if (!StringUtils.hasText(tenantCodeParam)) {
            return false;
        }

        try {
            Date now = new Date();

            // 先查询当前使用次数
            SysTenantSyncKey currentKey = findByTenantCode(tenantCodeParam);
            if (currentKey == null) {
                return false;
            }

            long newUsageCount = currentKey.getUsageCount() != null ? currentKey.getUsageCount() + 1 : 1L;

            int updatedRows = sysTenantSyncKeyMapper.update(c ->
                c.set(lastUsedTime).equalTo(now)
                .set(usageCount).equalTo(newUsageCount)
                .set(updateTime).equalTo(now)
                .set(updateBy).equalTo(SessionUtils.getUsername())
                .where(tenantCode, isEqualTo(tenantCodeParam))
            );

            return updatedRows > 0;

        } catch (Exception e) {
            log.error("更新租户密钥使用统计失败，租户编码：{}，错误：{}", tenantCodeParam, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean disableKey(String tenantCode) {
        return updateKeyStatus(tenantCode, 0);
    }

    @Override
    public boolean enableKey(String tenantCode) {
        return updateKeyStatus(tenantCode, 1);
    }

    /**
     * 更新密钥状态
     */
    private boolean updateKeyStatus(String tenantCodeParam, Integer status) {
        if (!StringUtils.hasText(tenantCodeParam) || status == null) {
            return false;
        }

        try {
            Date now = new Date();

            int updatedRows = sysTenantSyncKeyMapper.update(c ->
                c.set(keyStatus).equalTo(status)
                .set(updateTime).equalTo(now)
                .set(updateBy).equalTo(SessionUtils.getUsername())
                .where(tenantCode, isEqualTo(tenantCodeParam))
            );

            return updatedRows > 0;

        } catch (Exception e) {
            log.error("更新租户密钥状态失败，租户编码：{}，状态：{}，错误：{}",
                    tenantCodeParam, status, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据主键删除记录
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return sysTenantSyncKeyMapper.deleteByPrimaryKey(id);
    }
}
