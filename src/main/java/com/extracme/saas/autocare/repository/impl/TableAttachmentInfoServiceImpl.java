package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.AttachmentInfoDynamicSqlSupport.attachmentInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.List;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.AttachmentInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.AttachmentInfo;
import com.extracme.saas.autocare.repository.TableAttachmentInfoService;

import lombok.extern.slf4j.Slf4j;

/**
 * 保司信息Repository实现类
 */
@Slf4j
@Repository
public class TableAttachmentInfoServiceImpl implements TableAttachmentInfoService {

    @Autowired
    private AttachmentInfoExtendMapper attachmentInfoExtendMapper;


    @Override
    public List<AttachmentInfo> findAttachmentByForeignKey(String foreignKey) {
        SelectStatementProvider selectStatementProvider = select(attachmentInfo.allColumns())
                .from(attachmentInfo)
                .where(attachmentInfo.foreignKey, isEqualTo(foreignKey))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return attachmentInfoExtendMapper.selectMany(selectStatementProvider);
    }

    @Override
    public int deleteByForeignKey(String foreignKey) {

        return 0;
    }

    @Override
    public AttachmentInfo insert(AttachmentInfo attachmentInfo) {
        attachmentInfoExtendMapper.insertSelective(attachmentInfo);
        return attachmentInfo;
    }
}
