package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.base.WorkflowInstanceDynamicSqlSupport;
import com.extracme.saas.autocare.mapper.extend.WorkflowInstanceExtendMapper;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowInstanceQueryDTO;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.extracme.saas.autocare.mapper.base.WorkflowInstanceDynamicSqlSupport.workflowInstance;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 流程实例表数据访问服务实现
 */
@Repository
public class TableWorkflowInstanceServiceImpl implements TableWorkflowInstanceService {

    @Autowired
    private WorkflowInstanceExtendMapper workflowInstanceMapper;

    @Override
    public WorkflowInstance insert(WorkflowInstance record) {
        // 使用系统操作人
        return insert(record, "system");
    }

    @Override
    public WorkflowInstance insert(WorkflowInstance record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreateTime(now);
        record.setCreateBy(operator);
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        workflowInstanceMapper.insertSelective(record);
        return record;
    }

    @Override
    public WorkflowInstance selectById(Long id) {
        if (id == null) {
            return null;
        }
        Optional<WorkflowInstance> optional = workflowInstanceMapper.selectByPrimaryKey(id);
        return optional.orElse(null);
    }

    @Override
    public WorkflowInstance selectByBusinessId(String businessId) {
        if (businessId == null) {
            return null;
        }
        SelectDSLCompleter completer = c -> c.where(WorkflowInstanceDynamicSqlSupport.businessId, isEqualTo(businessId));
        Optional<WorkflowInstance> optional = workflowInstanceMapper.selectOne(completer);
        return optional.orElse(null);
    }

    @Override
    public WorkflowInstance selectByBusinessIdAndTenantId(String businessId, Integer tenantId) {
        if (businessId == null) {
            return null;
        }
        SelectDSLCompleter completer = c -> c.where(WorkflowInstanceDynamicSqlSupport.businessId, isEqualTo(businessId))
                .and(WorkflowInstanceDynamicSqlSupport.tenantId, isEqualToWhenPresent(tenantId));
        Optional<WorkflowInstance> optional = workflowInstanceMapper.selectOne(completer);
        return optional.orElse(null);
    }

    @Override
    public List<WorkflowInstance> selectByCurrentActivityCode(String currentActivityCode) {
        if (currentActivityCode == null) {
            return Collections.emptyList();
        }
        SelectDSLCompleter completer = c -> c.where(WorkflowInstanceDynamicSqlSupport.currentActivityCode, isEqualTo(currentActivityCode))
                .orderBy(WorkflowInstanceDynamicSqlSupport.id.descending());
        return workflowInstanceMapper.select(completer);
    }

    @Override
    public List<WorkflowInstance> selectByCurrentActivityCodeAndStatusCode(String currentActivityCode, String statusCode) {
        if (currentActivityCode == null || statusCode == null) {
            return Collections.emptyList();
        }
        SelectDSLCompleter completer = c -> c.where(WorkflowInstanceDynamicSqlSupport.currentActivityCode, isEqualTo(currentActivityCode))
                .and(WorkflowInstanceDynamicSqlSupport.statusCode, isEqualTo(statusCode))
                .orderBy(WorkflowInstanceDynamicSqlSupport.id.descending());
        return workflowInstanceMapper.select(completer);
    }

    @Override
    public Map<String, Long> countByActivityCodeGroupByStatusCode(String currentActivityCode) {
        if (currentActivityCode == null) {
            return new HashMap<>();
        }

        // 直接查询所有符合条件的记录
        SelectDSLCompleter completer = c -> c.where(WorkflowInstanceDynamicSqlSupport.currentActivityCode, isEqualTo(currentActivityCode));
        List<WorkflowInstance> allInstances = workflowInstanceMapper.select(completer);

        // 在内存中进行分组统计
        Map<String, Long> resultMap = new HashMap<>();
        for (WorkflowInstance instance : allInstances) {
            String statusCode = instance.getStatusCode();
            if (statusCode != null) {
                resultMap.put(statusCode, resultMap.getOrDefault(statusCode, 0L) + 1);
            }
        }

        return resultMap;
    }

    @Override
    public List<WorkflowInstance> selectByWorkflowId(Long workflowId) {
        if (workflowId == null) {
            return Collections.emptyList();
        }
        SelectDSLCompleter completer = c -> c.where(WorkflowInstanceDynamicSqlSupport.workflowId, isEqualTo(workflowId))
                .orderBy(WorkflowInstanceDynamicSqlSupport.id.descending());
        return workflowInstanceMapper.select(completer);
    }

    @Override
    public List<WorkflowInstance> selectByTenantId(Integer tenantId) {
        if (tenantId == null) {
            return Collections.emptyList();
        }
        SelectDSLCompleter completer = c -> c.where(WorkflowInstanceDynamicSqlSupport.tenantId, isEqualTo(tenantId))
                .orderBy(WorkflowInstanceDynamicSqlSupport.id.descending());
        return workflowInstanceMapper.select(completer);
    }

    @Override
    public List<WorkflowInstance> findByCondition(WorkflowInstanceQueryDTO queryDTO) {
        if (queryDTO == null) {
            return Collections.emptyList();
        }

        // 使用SelectStatementProvider构建查询
        SelectStatementProvider selectStatement = select(WorkflowInstanceDynamicSqlSupport.workflowInstance.allColumns())
            .from(workflowInstance)
            .where(WorkflowInstanceDynamicSqlSupport.workflowId, isEqualToWhenPresent(queryDTO.getWorkflowId()))
            .and(WorkflowInstanceDynamicSqlSupport.businessId, isEqualToWhenPresent(queryDTO.getBusinessId()))
            .and(WorkflowInstanceDynamicSqlSupport.currentActivityCode, isEqualToWhenPresent(queryDTO.getCurrentActivityCode()))
            .and(WorkflowInstanceDynamicSqlSupport.statusCode, isEqualToWhenPresent(queryDTO.getStatus()))
            .and(WorkflowInstanceDynamicSqlSupport.createBy, isLikeWhenPresent(transFuzzyQueryParam(queryDTO.getCreateBy())))
            // 处理时间范围查询可以在这里添加
            .orderBy(WorkflowInstanceDynamicSqlSupport.createTime.descending(), WorkflowInstanceDynamicSqlSupport.id.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return workflowInstanceMapper.selectMany(selectStatement);
    }

    @Override
    public int updateSelectiveById(WorkflowInstance record) {
        if (record == null) {
            return 0;
        }
        record.setUpdateTime(new Date());
        return workflowInstanceMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateSelectiveById(WorkflowInstance record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        return workflowInstanceMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return workflowInstanceMapper.deleteByPrimaryKey(id);
    }

}
