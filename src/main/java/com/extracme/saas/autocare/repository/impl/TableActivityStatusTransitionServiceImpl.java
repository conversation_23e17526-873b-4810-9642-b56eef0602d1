package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.base.ActivityStatusTransitionMapper;
import com.extracme.saas.autocare.model.entity.ActivityStatusTransition;
import com.extracme.saas.autocare.repository.TableActivityStatusTransitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.extracme.saas.autocare.mapper.base.ActivityStatusTransitionDynamicSqlSupport.activityStatusTransition;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

/**
 * 节点状态转换规则表数据访问服务实现类
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Repository
public class TableActivityStatusTransitionServiceImpl implements TableActivityStatusTransitionService {

    @Autowired
    private ActivityStatusTransitionMapper activityStatusTransitionMapper;

    @Override
    public ActivityStatusTransition selectById(Long id) {
        if (id == null) {
            return null;
        }
        Optional<ActivityStatusTransition> optional = activityStatusTransitionMapper.selectByPrimaryKey(id);
        return optional.orElse(null);
    }

    @Override
    public List<ActivityStatusTransition> selectByActivityTransitionId(Long activityTransitionId) {
        if (activityTransitionId == null) {
            return Collections.emptyList();
        }
        return activityStatusTransitionMapper.select(c ->
                c.where(activityStatusTransition.activityTransitionId, isEqualTo(activityTransitionId))
        );
    }

    @Override
    public int insert(ActivityStatusTransition activityStatusTransition) {
        if (activityStatusTransition == null) {
            return 0;
        }
        return activityStatusTransitionMapper.insertSelective(activityStatusTransition);
    }

    @Override
    public int insert(ActivityStatusTransition activityStatusTransition, String operator) {
        if (activityStatusTransition == null) {
            return 0;
        }
        Date now = new Date();
        activityStatusTransition.setCreateTime(now);
        activityStatusTransition.setCreateBy(operator);
        activityStatusTransition.setUpdateTime(now);
        activityStatusTransition.setUpdateBy(operator);
        return activityStatusTransitionMapper.insertSelective(activityStatusTransition);
    }

    @Override
    public int batchInsert(List<ActivityStatusTransition> activityStatusTransitions, String operator) {
        if (activityStatusTransitions == null || activityStatusTransitions.isEmpty()) {
            return 0;
        }

        int count = 0;
        Date now = new Date();
        for (ActivityStatusTransition transition : activityStatusTransitions) {
            transition.setCreateTime(now);
            transition.setCreateBy(operator);
            transition.setUpdateTime(now);
            transition.setUpdateBy(operator);
            count += activityStatusTransitionMapper.insertSelective(transition);
        }
        return count;
    }

    @Override
    public int update(ActivityStatusTransition activityStatusTransition) {
        if (activityStatusTransition == null || activityStatusTransition.getId() == null) {
            return 0;
        }
        return activityStatusTransitionMapper.updateByPrimaryKeySelective(activityStatusTransition);
    }

    @Override
    public int update(ActivityStatusTransition activityStatusTransition, String operator) {
        if (activityStatusTransition == null || activityStatusTransition.getId() == null) {
            return 0;
        }
        Date now = new Date();
        activityStatusTransition.setUpdateTime(now);
        activityStatusTransition.setUpdateBy(operator);
        return activityStatusTransitionMapper.updateByPrimaryKeySelective(activityStatusTransition);
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return activityStatusTransitionMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int deleteByActivityTransitionId(Long activityTransitionId) {
        if (activityTransitionId == null) {
            return 0;
        }
        return activityStatusTransitionMapper.delete(c ->
                c.where(activityStatusTransition.activityTransitionId, isEqualTo(activityTransitionId))
        );
    }

    @Override
    public List<ActivityStatusTransition> selectByActivityTransitionIds(List<Long> activityTransitionIds) {
        if (activityTransitionIds == null || activityTransitionIds.isEmpty()) {
            return Collections.emptyList();
        }
        return activityStatusTransitionMapper.select(c ->
                c.where(activityStatusTransition.activityTransitionId, isIn(activityTransitionIds))
        );
    }

    @Override
    public int updateByPrimaryKey(ActivityStatusTransition activityStatusTransition) {
        if (activityStatusTransition == null || activityStatusTransition.getId() == null) {
            return 0;
        }
        return activityStatusTransitionMapper.updateByPrimaryKey(activityStatusTransition);
    }

    @Override
    public int updateByPrimaryKey(ActivityStatusTransition activityStatusTransition, String operator) {
        if (activityStatusTransition == null || activityStatusTransition.getId() == null) {
            return 0;
        }
        Date now = new Date();
        activityStatusTransition.setUpdateTime(now);
        activityStatusTransition.setUpdateBy(operator);
        return activityStatusTransitionMapper.updateByPrimaryKey(activityStatusTransition);
    }
}
