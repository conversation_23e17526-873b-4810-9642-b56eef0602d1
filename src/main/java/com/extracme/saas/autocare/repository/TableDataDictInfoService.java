package com.extracme.saas.autocare.repository;

import java.util.List;
import com.extracme.saas.autocare.model.entity.DataDictInfo;

/**
 * 数据字典表数据访问服务接口
 */
public interface TableDataDictInfoService extends DefaultTableService<DataDictInfo, Long> {

    /**
     * 查询所有数据字典记录
     *
     * @return 数据字典列表
     */
    List<DataDictInfo> selectAll();

    /**
     * 根据字典编码查询数据字典
     * @param dataCode 字典编码
     * @return 数据字典信息，若不存在返回null
     */
    DataDictInfo selectByDataCode(String dataCode);

    /**
     * 根据ID删除数据字典
     * @param id 数据字典ID
     * @return 删除的记录数
     */
    int deleteById(Long id);

    /**
     * 根据条件查询数据字典列表
     * 支持字典名称和字典编码的模糊查询，按字典编码升序排序
     *
     * @param dictName 字典名称（可选，支持模糊查询）
     * @param dictCode 字典编码（可选，支持模糊查询）
     * @return 数据字典列表
     */
    List<DataDictInfo> findByCondition(String dictName, String dictCode);
}