package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.SysVerificationCodeDynamicSqlSupport.sysVerificationCode;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThan;

import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.extracme.saas.autocare.mapper.base.SysVerificationCodeMapper;
import com.extracme.saas.autocare.model.entity.SysVerificationCode;
import com.extracme.saas.autocare.repository.TableSysVerificationCodeService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 验证码表数据访问服务实现类
 */
@Repository
public class TableSysVerificationCodeServiceImpl implements TableSysVerificationCodeService {

    @Autowired
    private SysVerificationCodeMapper verificationCodeMapper;

    @Override
    public SysVerificationCode insert(SysVerificationCode verificationCode) {
        // 调用带操作人参数的方法
        return insert(verificationCode, "system");
    }

    @Override
    public SysVerificationCode insert(SysVerificationCode verificationCode, String operator) {
        if (verificationCode == null) {
            return null;
        }

        Date now = new Date();
        verificationCode.setCreatedTime(now);
        verificationCode.setUpdateTime(now);

        verificationCodeMapper.insertSelective(verificationCode);
        return verificationCode;
    }

    @Override
    public int updateSelectiveById(SysVerificationCode verificationCode) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(verificationCode, operator);
    }

    @Override
    public int updateSelectiveById(SysVerificationCode verificationCode, String operator) {
        if (verificationCode == null || verificationCode.getId() == null) {
            return 0;
        }
        Date now = new Date();
        verificationCode.setUpdateTime(now);
        return verificationCodeMapper.updateByPrimaryKeySelective(verificationCode);
    }

    @Override
    public SysVerificationCode selectById(Long id) {
        if (id == null) {
            return null;
        }
        return verificationCodeMapper.selectByPrimaryKey(id).orElse(null);
    }

    /**
     * 根据手机号和类型查询最新的未使用验证码
     *
     * @param mobile 手机号
     * @param type 验证码类型
     * @return 验证码信息
     */
    @Override
    public Optional<SysVerificationCode> findLatestUnused(String mobile, String type) {
        if (!StringUtils.hasText(mobile) || !StringUtils.hasText(type)) {
            return Optional.empty();
        }

        SelectStatementProvider selectStatement = SqlBuilder.select(sysVerificationCode.allColumns())
                .from(sysVerificationCode)
                .where(sysVerificationCode.mobile, isEqualTo(mobile))
                .and(sysVerificationCode.type, isEqualTo(type))
                .and(sysVerificationCode.status, isEqualTo(0))  // 未使用
                .and(sysVerificationCode.expireTime, isGreaterThan(new Date()))
                .orderBy(sysVerificationCode.createdTime.descending())
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        List<SysVerificationCode> codes = verificationCodeMapper.selectMany(selectStatement);
        if (codes.isEmpty()) {
            return Optional.empty();
        }
        return Optional.of(codes.get(0));
    }

    /**
     * 更新验证码状态
     *
     * @param id 验证码ID
     * @param status 状态（0-未使用，1-已使用，2-已过期）
     * @return 更新结果
     */
    @Override
    public boolean updateStatus(Long id, Boolean status) {
        if (id == null || status == null) {
            return false;
        }

        SysVerificationCode code = new SysVerificationCode();
        code.setId(id);
        code.setStatus(status ? 1 : 0);
        code.setUpdateTime(new Date());

        return verificationCodeMapper.updateByPrimaryKeySelective(code) > 0;
    }

    /**
     * 查询所有过期但未标记为过期的验证码
     *
     * @param now 当前时间
     * @return 过期验证码列表
     */
    @Override
    public List<SysVerificationCode> findExpiredButNotMarked(Date now) {
        if (now == null) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = SqlBuilder.select(sysVerificationCode.allColumns())
                .from(sysVerificationCode)
                .where(sysVerificationCode.expireTime, isLessThan(now))
                .and(sysVerificationCode.status, isEqualTo(0))  // 未使用
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return verificationCodeMapper.selectMany(selectStatement);
    }

    /**
     * 批量标记过期验证码
     *
     * @param now 当前时间
     * @return 更新数量
     */
    @Override
    public int markExpiredCodes(Date now) {
        if (now == null) {
            return 0;
        }

        SysVerificationCode record = new SysVerificationCode();
        record.setStatus(2);  // 已过期
        record.setUpdateTime(now);

        return verificationCodeMapper.update(c ->
            c.set(sysVerificationCode.status).equalTo(2)  // 标记为已过期
             .set(sysVerificationCode.updateTime).equalTo(now)
             .where(sysVerificationCode.expireTime, isLessThan(now))
             .and(sysVerificationCode.status, isEqualTo(0)));  // 未使用
    }

    /**
     * 删除指定天数前的验证码
     *
     * @param days 天数
     * @return 删除数量
     */
    @Override
    public int deleteOldCodes(int days) {
        if (days <= 0) {
            return 0;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date deadline = calendar.getTime();

        return verificationCodeMapper.delete(c -> c.where(sysVerificationCode.createdTime, isLessThan(deadline)));
    }

    /**
     * 查询最近一段时间内用户发送验证码的次数
     *
     * @param mobile 手机号
     * @param type 验证码类型
     * @param startTime 开始时间
     * @return 发送次数
     */
    @Override
    public int countRecentSendTimes(String mobile, String type, Date startTime) {
        if (!StringUtils.hasText(mobile) || !StringUtils.hasText(type) || startTime == null) {
            return 0;
        }

        return (int) verificationCodeMapper.count(c ->
            c.where(sysVerificationCode.mobile, isEqualTo(mobile))
             .and(sysVerificationCode.type, isEqualTo(type))
             .and(sysVerificationCode.createdTime, isGreaterThanOrEqualTo(startTime)));
    }

    @Override
    public boolean incrementFailCount(Long id) {
        // 先查询当前验证码
        Optional<SysVerificationCode> existingCode = verificationCodeMapper.selectByPrimaryKey(id);
        if (!existingCode.isPresent()) {
            return false;
        }

        // 更新失败次数
        SysVerificationCode code = new SysVerificationCode();
        code.setId(id);
        code.setFailCount(existingCode.get().getFailCount() == null ? 1 : existingCode.get().getFailCount() + 1);
        code.setUpdateTime(new Date());
        return verificationCodeMapper.updateByPrimaryKeySelective(code) > 0;
    }
}