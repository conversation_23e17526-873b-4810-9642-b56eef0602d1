package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcLossRepairSumInfo;

import java.util.List;

/**
 * 损失维修汇总信息表数据访问服务接口
 */
public interface TableLossRepairSumInfoService extends DefaultTableService<MtcLossRepairSumInfo, Long> {

    /**
     * 根据任务编号查询损失维修汇总信息列表
     * @param taskNo 任务编号
     * @return 损失维修汇总信息列表
     */
    List<MtcLossRepairSumInfo> selectByTaskNo(String taskNo);
    
    /**
     * 批量插入损失维修汇总信息
     * @param repairSumInfoList 损失维修汇总信息列表
     * @return 影响行数
     */
    int batchInsert(List<MtcLossRepairSumInfo> repairSumInfoList);
    
    /**
     * 根据任务编号更新损失维修汇总信息状态为无效
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int updateStatus(String taskNo);
    
    /**
     * 根据任务编号删除损失维修汇总信息
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int deleteByTaskNo(String taskNo);
    
    /**
     * 根据任务编号和状态更新损失维修汇总信息
     * @param record 损失维修汇总信息记录
     * @param taskNo 任务编号
     * @param status 状态
     * @param operator 操作人
     * @return 影响行数
     */
    int updateSelectiveByTaskNoAndStatus(MtcLossRepairSumInfo record, String taskNo, String operator);
}
