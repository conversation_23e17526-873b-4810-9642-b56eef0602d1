package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.config.OssConfig;
import com.extracme.saas.autocare.mapper.base.MtcVehicleRepairPicDynamicSqlSupport;
import com.extracme.saas.autocare.mapper.extend.MtcVehicleRepairPicExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcVehicleRepairPic;
import com.extracme.saas.autocare.repository.TableVehicleRepairPicService;
import com.extracme.saas.autocare.service.OssService;
import com.extracme.saas.autocare.util.SpringContextUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import static com.extracme.saas.autocare.mapper.base.MtcVehicleRepairPicDynamicSqlSupport.id;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 维修任务图片/视频表数据访问服务实现
 */
@Slf4j
@Repository
public class TableVehicleRepairPicServiceImpl implements TableVehicleRepairPicService {

    @Autowired
    private MtcVehicleRepairPicExtendMapper mtcVehicleRepairPicMapper;

    @Autowired
    private OssService ossService;

    @Override
    public MtcVehicleRepairPic insert(MtcVehicleRepairPic record) {
        mtcVehicleRepairPicMapper.insertSelective(record);
        return record;
    }

    @Override
    public List<MtcVehicleRepairPic> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(MtcVehicleRepairPicDynamicSqlSupport.taskNo, isEqualTo(taskNo))
                .orderBy(id);
        return mtcVehicleRepairPicMapper.select(completer);
    }

    @Override
    public List<MtcVehicleRepairPic> selectByTaskNoAndType(String taskNo, Integer picType) {
        SelectDSLCompleter completer = c -> c.where(MtcVehicleRepairPicDynamicSqlSupport.taskNo, isEqualTo(taskNo))
                .and(MtcVehicleRepairPicDynamicSqlSupport.picType, isEqualTo(picType))
                .orderBy(id.descending());
        return mtcVehicleRepairPicMapper.select(completer);
    }

    @Override
    public int batchInsert(List<MtcVehicleRepairPic> picList) {
        int count = 0;
        for (MtcVehicleRepairPic pic : picList) {
            count += mtcVehicleRepairPicMapper.insertSelective(pic);
        }
        return count;
    }

    @Override
    public int deleteByTaskNo(String taskNo) {
        log.info("开始删除任务编号为 {} 的所有图片", taskNo);
        
        // 先查询出要删除的所有图片记录
        List<MtcVehicleRepairPic> picList = selectByTaskNo(taskNo);
        if (CollectionUtils.isEmpty(picList)) {
            log.info("任务编号 {} 没有图片需要删除", taskNo);
            return 0;
        }
        
        // 执行数据库删除操作
        int deleteCount = mtcVehicleRepairPicMapper
                .delete(c -> c.where(MtcVehicleRepairPicDynamicSqlSupport.taskNo, isEqualTo(taskNo)));
        
        // 数据库删除成功后，删除OSS中的实际文件
        if (deleteCount > 0) {
            deleteOssFiles(picList);
        }
        
        log.info("成功删除任务编号为 {} 的 {} 张图片", taskNo, deleteCount);
        return deleteCount;
    }

    @Override
    public void deleteByTaskIdAndPicTypes(String taskNo, List<Integer> picTypes) {
        if (CollectionUtils.isEmpty(picTypes)) {
            log.info("图片类型列表为空，不执行删除操作");
            return;
        }
        
        log.info("开始删除任务编号为 {} 的图片类型 {} 的图片", taskNo, picTypes);
        
        // 先查询出要删除的所有图片记录
        List<MtcVehicleRepairPic> picList = new ArrayList<>();
        for (Integer picType : picTypes) {
            List<MtcVehicleRepairPic> typedPics = selectByTaskNoAndType(taskNo, picType);
            if (!CollectionUtils.isEmpty(typedPics)) {
                picList.addAll(typedPics);
            }
        }
        
        if (CollectionUtils.isEmpty(picList)) {
            log.info("任务编号 {} 的图片类型 {} 没有图片需要删除", taskNo, picTypes);
            return;
        }
        
        // 执行数据库删除操作
        int deleteCount = mtcVehicleRepairPicMapper.delete(c -> c.where()
                .and(MtcVehicleRepairPicDynamicSqlSupport.taskNo, isEqualTo(taskNo))
                .and(MtcVehicleRepairPicDynamicSqlSupport.picType, isInWhenPresent(picTypes)));
        
        // 数据库删除成功后，删除OSS中的实际文件
        if (deleteCount > 0) {
            deleteOssFiles(picList);
        }
        
        log.info("成功删除任务编号为 {} 的图片类型 {} 的 {} 张图片", taskNo, picTypes, deleteCount);
    }

    @Override
    public int delMaterialPic(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.info("图片ID列表为空，不执行删除操作");
            return 0;
        }
        
        log.info("开始删除图片ID为 {} 的图片", ids);
        
        // 先查询出要删除的所有图片记录
        SelectDSLCompleter completer = c -> c.where(MtcVehicleRepairPicDynamicSqlSupport.id, isIn(ids));
        List<MtcVehicleRepairPic> picList = mtcVehicleRepairPicMapper.select(completer);
        
        if (CollectionUtils.isEmpty(picList)) {
            log.info("图片ID {} 没有图片需要删除", ids);
            return 0;
        }
        
        // 执行数据库删除操作
        int deleteCount = mtcVehicleRepairPicMapper.delete(c -> c.where(MtcVehicleRepairPicDynamicSqlSupport.id, isIn(ids)));

        // 数据库删除成功后，删除OSS中的实际文件
        if (deleteCount > 0) {
            deleteOssFiles(picList);
        }
        
        log.info("成功删除图片ID为 {} 的 {} 张图片", ids, deleteCount);
        return deleteCount;
    }

    /**
     * 删除OSS中的实际文件
     *
     * @param picList 图片列表
     */
    private void deleteOssFiles(List<MtcVehicleRepairPic> picList) {
        if (CollectionUtils.isEmpty(picList)) {
            return;
        }
        
        try {
            // 获取OssConfig中的env配置
            OssConfig ossConfig = SpringContextUtil.getBean(OssConfig.class);
            String env = ossConfig.getUpload().getEnv();
            
            for (MtcVehicleRepairPic pic : picList) {
                try {
                    String picUrl = pic.getPicUrl();
                    if (StringUtils.isEmpty(picUrl)) {
                        continue;
                    }
                    
                    // 处理URL，提取相对路径部分
                    String relativePath = extractRelativePath(picUrl);
                    
                    // 构建OSS中的实际路径
                    String ossPath = StringUtils.isEmpty(env) ? relativePath : env + "/" + relativePath;
                    
                    // 删除OSS中的文件
                    boolean deleted = ossService.deleteFile(ossPath);
                    if (deleted) {
                        log.debug("成功删除OSS文件: {}", ossPath);
                    } else {
                        log.warn("删除OSS文件失败: {}", ossPath);
                    }
                } catch (Exception e) {
                    log.error("删除OSS文件时发生异常: {}", e.getMessage(), e);
                    // 继续处理下一个文件，不影响整体流程
                }
            }
        } catch (Exception e) {
            log.error("获取OSS配置或删除OSS文件时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 从URL中提取相对路径部分
     *
     * @param url 图片URL
     * @return 相对路径
     */
    private String extractRelativePath(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        
        // 如果URL不是以http://或https://开头，则认为是相对路径
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            // 确保路径不以/开头
            return url.startsWith("/") ? url.substring(1) : url;
        }
        
        try {
            // 获取OssConfig中的配置
            OssConfig ossConfig = SpringContextUtil.getBean(OssConfig.class);
            String baseUrl = ossConfig.getUpload().getBaseUrl();
            String env = ossConfig.getUpload().getEnv();
            
            if (StringUtils.isEmpty(baseUrl)) {
                return url;
            }
            
            // 构建完整前缀
            String fullPrefix = baseUrl;
            if (!fullPrefix.endsWith("/") && !StringUtils.isEmpty(env)) {
                fullPrefix += "/";
            }
            
            if (!StringUtils.isEmpty(env)) {
                fullPrefix += env;
                if (!fullPrefix.endsWith("/")) {
                    fullPrefix += "/";
                }
            }
            
            // 移除前缀
            if (url.startsWith(fullPrefix)) {
                return url.substring(fullPrefix.length());
            }
            
            // 处理URL不包含完整前缀的情况
            // 尝试解析URL并提取路径部分
            URL parsedUrl = new URL(url);
            String path = parsedUrl.getPath();
            
            // 如果路径包含环境目录，则移除环境目录部分
            if (!StringUtils.isEmpty(env) && path.startsWith("/" + env + "/")) {
                return path.substring(env.length() + 2); // 移除 "/env/" 部分
            } else if (path.startsWith("/")) {
                return path.substring(1); // 移除开头的 "/"
            }
            
            return path;
        } catch (Exception e) {
            log.warn("提取URL相对路径失败: {}", e.getMessage());
            // 如果解析失败，返回原始URL
            return url;
        }
    }
}
