package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcLossInfo;

import java.util.List;

/**
 * 损失信息表数据访问服务接口
 */
public interface TableLossInfoService extends DefaultTableService<MtcLossInfo, Long> {

    /**
     * 根据任务编号查询损失信息列表
     * @param taskNo 任务编号
     * @return 损失信息列表
     */
    List<MtcLossInfo> selectByTaskNo(String taskNo);
    
    /**
     * 根据任务编号更新损失信息状态为无效
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int updateStatus(String taskNo);
    
    /**
     * 根据任务编号删除损失信息
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int deleteByTaskNo(String taskNo);
    
    /**
     * 根据任务编号和状态更新损失信息
     * @param record 损失信息记录
     * @param taskNo 任务编号
     * @param operator 操作人
     * @return 影响行数
     */
    int updateSelectiveByTaskNoAndStatus(MtcLossInfo record, String taskNo, String operator);
}
