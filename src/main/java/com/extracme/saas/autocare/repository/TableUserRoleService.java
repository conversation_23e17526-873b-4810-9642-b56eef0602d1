package com.extracme.saas.autocare.repository;

import java.util.List;

import com.extracme.saas.autocare.model.entity.SysUserRole;

/**
 * 用户角色关联表数据访问服务
 */
public interface TableUserRoleService {
    
    /**
     * 根据用户ID查询用户角色关联
     *
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    List<SysUserRole> findByUserId(Long userId);
    
    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     * @return 删除的记录数
     */
    int deleteByUserId(Long userId);
    
    /**
     * 批量创建用户角色关联
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 创建的记录数
     */
    int batchCreate(Long userId, List<Long> roleIds);
    
    /**
     * 更新用户角色关联
     * 先删除原有关联，再创建新关联
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 更新的记录数
     */
    int updateUserRoles(Long userId, List<Long> roleIds);

    /**
     * 根据角色ID查询用户角色关联
     *
     * @param roleId 角色ID
     * @return 用户角色关联列表
     */
    List<SysUserRole> findByRoleId(Long roleId);

    /**
     * 根据角色ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Long> findUserIdsByRoleId(Long roleId);

    /**
     * 根据角色ID和租户ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 用户ID列表
     */
    List<Long> findUserIdsByRoleIdAndTenantId(Long roleId, Long tenantId);
}
