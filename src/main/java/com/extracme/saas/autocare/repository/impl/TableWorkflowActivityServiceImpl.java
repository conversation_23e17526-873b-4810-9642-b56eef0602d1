package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.base.WorkflowActivityMapper;
import com.extracme.saas.autocare.model.entity.WorkflowActivity;
import com.extracme.saas.autocare.repository.TableWorkflowActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.extracme.saas.autocare.mapper.base.WorkflowActivityDynamicSqlSupport.workflowActivity;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

/**
 * 工作流活动节点关联表数据访问服务实现类
 */
@Repository
public class TableWorkflowActivityServiceImpl implements TableWorkflowActivityService {

    @Autowired
    private WorkflowActivityMapper workflowActivityMapper;

    @Override
    public WorkflowActivity selectById(Long id) {
        return workflowActivityMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<WorkflowActivity> selectByWorkflowId(Long workflowId) {
        return workflowActivityMapper.select(c ->
                c.where(workflowActivity.workflowId, isEqualTo(workflowId))
        );
    }

    @Override
    public WorkflowActivity selectByWorkflowIdAndActivityCode(Long workflowId, String activityCode) {
        return workflowActivityMapper.selectOne(c ->
                c.where(workflowActivity.workflowId, isEqualTo(workflowId))
                .and(workflowActivity.activityCode, isEqualTo(activityCode))
        ).orElse(null);
    }

    @Override
    public int insert(WorkflowActivity workflowActivity) {
        return workflowActivityMapper.insertSelective(workflowActivity);
    }

    @Override
    public int batchInsert(List<WorkflowActivity> workflowActivities) {
        int count = 0;
        for (WorkflowActivity activity : workflowActivities) {
            count += workflowActivityMapper.insertSelective(activity);
        }
        return count;
    }

    @Override
    public int update(WorkflowActivity workflowActivity) {
        return workflowActivityMapper.updateByPrimaryKeySelective(workflowActivity);
    }

    @Override
    public int deleteById(Long id) {
        return workflowActivityMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int deleteByWorkflowId(Long workflowId) {
        return workflowActivityMapper.delete(c ->
                c.where(workflowActivity.workflowId, isEqualTo(workflowId))
        );
    }
}
