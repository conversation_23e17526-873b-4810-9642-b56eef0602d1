package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.WorkflowActivity;

import java.util.List;

/**
 * 工作流活动节点关联表数据访问服务接口
 */
public interface TableWorkflowActivityService {

    /**
     * 根据ID查询工作流活动节点关联
     *
     * @param id 关联ID
     * @return 工作流活动节点关联
     */
    WorkflowActivity selectById(Long id);

    /**
     * 根据工作流模板ID查询关联的活动节点
     *
     * @param workflowId 工作流模板ID
     * @return 工作流活动节点关联列表
     */
    List<WorkflowActivity> selectByWorkflowId(Long workflowId);

    /**
     * 根据工作流模板ID和活动节点编码查询关联
     *
     * @param workflowId 工作流模板ID
     * @param activityCode 活动节点编码
     * @return 工作流活动节点关联
     */
    WorkflowActivity selectByWorkflowIdAndActivityCode(Long workflowId, String activityCode);

    /**
     * 插入工作流活动节点关联
     *
     * @param workflowActivity 工作流活动节点关联
     * @return 影响行数
     */
    int insert(WorkflowActivity workflowActivity);

    /**
     * 批量插入工作流活动节点关联
     *
     * @param workflowActivities 工作流活动节点关联列表
     * @return 影响行数
     */
    int batchInsert(List<WorkflowActivity> workflowActivities);

    /**
     * 更新工作流活动节点关联
     *
     * @param workflowActivity 工作流活动节点关联
     * @return 影响行数
     */
    int update(WorkflowActivity workflowActivity);

    /**
     * 删除工作流活动节点关联
     *
     * @param id 关联ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据工作流模板ID删除关联的活动节点
     *
     * @param workflowId 工作流模板ID
     * @return 影响行数
     */
    int deleteByWorkflowId(Long workflowId);
}
