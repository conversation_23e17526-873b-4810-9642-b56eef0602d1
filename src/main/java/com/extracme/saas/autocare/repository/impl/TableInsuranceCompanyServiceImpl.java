package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.extend.InsuranceCompanyExtendMapper;
import com.extracme.saas.autocare.model.dto.InsuranceCompanyQueryDTO;
import com.extracme.saas.autocare.model.entity.InsuranceCompanyInfo;
import com.extracme.saas.autocare.repository.TableInsuranceCompanyService;
import com.extracme.saas.autocare.util.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

import static com.extracme.saas.autocare.mapper.base.InsuranceCompanyInfoDynamicSqlSupport.insuranceCompanyInfo;
import static com.extracme.saas.autocare.mapper.base.MtcRepairItemLibraryDynamicSqlSupport.mtcRepairItemLibrary;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 保司信息Repository实现类
 */
@Slf4j
@Repository
public class TableInsuranceCompanyServiceImpl implements TableInsuranceCompanyService {

    @Autowired
    private InsuranceCompanyExtendMapper insuranceCompanyExtendMapper;


    @Override
    public List<InsuranceCompanyInfo> queryInsuranceCompanyList(InsuranceCompanyQueryDTO queryDTO) {
        // 构建查询条件
        SelectStatementProvider selectStatement = select(insuranceCompanyInfo.allColumns())
                .from(insuranceCompanyInfo)
                .where(insuranceCompanyInfo.companyName, isLikeWhenPresent(transFuzzyQueryParam(queryDTO.getCompanyName())).filter(StringUtils::isNotBlank))
                .and(insuranceCompanyInfo.status, isEqualToWhenPresent(queryDTO.getStatus()))
                .orderBy(insuranceCompanyInfo.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return insuranceCompanyExtendMapper.selectMany(selectStatement);
    }

    @Override
    public InsuranceCompanyInfo selectById(Long id) {
        SelectDSLCompleter completer = c -> c.where(insuranceCompanyInfo.id, isEqualTo(id));
        return insuranceCompanyExtendMapper.selectOne(completer).orElse(null);
    }

    @Override
    public InsuranceCompanyInfo selectByCompanyName(String companyName) {
        SelectDSLCompleter completer = c -> c.where(insuranceCompanyInfo.companyName, isEqualTo(companyName));
        return insuranceCompanyExtendMapper.selectOne(completer).orElse(null);
    }

    @Override
    public List<InsuranceCompanyInfo> findAllInsuranceCompany() {
        SelectDSLCompleter completer = c -> c.where(insuranceCompanyInfo.status, isEqualTo(1));
        return insuranceCompanyExtendMapper.select(completer);
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return insuranceCompanyExtendMapper.deleteByPrimaryKey(id);
    }

    @Override
    public InsuranceCompanyInfo insert(InsuranceCompanyInfo insuranceCompanyInfo) {
        String operator = SessionUtils.getUsername();
        insuranceCompanyInfo.setCreateBy(operator);
        insuranceCompanyExtendMapper.insertSelective(insuranceCompanyInfo);
        return insuranceCompanyInfo;
    }

    @Override
    public int updateSelectiveById(InsuranceCompanyInfo insuranceCompanyInfo, String operator) {
        insuranceCompanyInfo.setUpdateBy(operator);
        insuranceCompanyInfo.setUpdateTime(new Date());
        return insuranceCompanyExtendMapper.updateByPrimaryKeySelective(insuranceCompanyInfo);
    }
}
