package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcVehicleInfoDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcVehicleInfoDynamicSqlSupport.mtcVehicleInfo;
import static com.extracme.saas.autocare.mapper.base.MtcVehicleInfoDynamicSqlSupport.vehicleNo;
import static com.extracme.saas.autocare.mapper.base.MtcVehicleInfoDynamicSqlSupport.vin;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.extracme.saas.autocare.mapper.extend.MtcVehicleInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;
import com.extracme.saas.autocare.repository.TableVehicleInfoService;
import com.extracme.saas.autocare.util.OperatorUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 维修车辆信息表服务实现类
 */
@Slf4j
@Repository
public class TableVehicleInfoServiceImpl implements TableVehicleInfoService {

    @Autowired
    private MtcVehicleInfoExtendMapper mtcVehicleInfoMapper;

    @Override
    public MtcVehicleInfo insert(MtcVehicleInfo record) {
        // 使用公共工具类获取当前操作人，作为兜底机制
        String operator = OperatorUtils.getCurrentOperator();
        // 调用带操作人参数的方法
        return insert(record, operator);
    }

    @Override
    public MtcVehicleInfo insertSelectiveWithId(MtcVehicleInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreateTime(now);
        record.setCreateBy(operator);
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        mtcVehicleInfoMapper.insertSelectiveWithId(record);
        return record;
    }

    @Override
    public MtcVehicleInfo insert(MtcVehicleInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreateTime(now);
        record.setCreateBy(operator);
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        mtcVehicleInfoMapper.insertSelective(record);
        return record;
    }

    @Override
    public int updateSelectiveById(MtcVehicleInfo record) {
        // 使用公共工具类获取当前操作人，作为兜底机制
        String operator = OperatorUtils.getCurrentOperator();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcVehicleInfo record, String operator) {
        if (record == null || record.getId() == null) {
            return 0;
        }
        record.setUpdateTime(new Date());
        record.setUpdateBy(operator);
        return mtcVehicleInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public MtcVehicleInfo selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcVehicleInfoMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public MtcVehicleInfo findByVin(String vinParam) {
        if (!StringUtils.hasText(vinParam)) {
            return null;
        }

        // 根据vin精确查询
        return mtcVehicleInfoMapper.selectOne(c ->
            c.where(vin, isEqualTo(vinParam))
        ).orElse(null);
    }

    @Override
    public List<MtcVehicleInfo> findByCondition(String vinParam, String vehicleNoParam, int limit) {
        // 如果两个参数都为空，返回空列表
        if (!StringUtils.hasText(vinParam) && !StringUtils.hasText(vehicleNoParam)) {
            return new ArrayList<>();
        }

        // 使用SelectStatementProvider构建查询，利用isLikeWhenPresent自动处理空参数
        SelectStatementProvider selectStatement = select(mtcVehicleInfo.allColumns())
            .from(mtcVehicleInfo)
            .where()
            .and(vin, isLikeWhenPresent(StringUtils.hasText(vinParam) ? transFuzzyQueryParam(vinParam) : null))
            .or(vehicleNo, isLikeWhenPresent(StringUtils.hasText(vehicleNoParam) ? transFuzzyQueryParam(vehicleNoParam) : null))
            .orderBy(id.descending())
            .limit(limit)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询并返回结果
        return mtcVehicleInfoMapper.selectMany(selectStatement);
    }

}