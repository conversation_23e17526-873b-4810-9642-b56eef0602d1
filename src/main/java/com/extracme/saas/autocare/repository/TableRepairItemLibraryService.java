package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.dto.RepairItemLibraryQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibrary;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryListVO;

import java.util.List;

/**
 * 维修项目库表数据访问服务接口
 */
public interface TableRepairItemLibraryService extends DefaultTableService<MtcRepairItemLibrary, Long> {

    /**
     * 查询列表
     *
     * @param queryDTO 查询参数
     * @return
     */
    List<RepairItemLibraryListVO> queryList(RepairItemLibraryQueryDTO queryDTO);

    /**
     * 导出列表（游标分页，支持动态页大小）
     * @param lastId 上一批次最后一条记录的ID（首次查询传入0）
     * @param queryDTO 查询参数
     * @param pageSize 页大小
     * @return
     */
    List<RepairItemLibraryListVO> exportListWithPageSize(long lastId, RepairItemLibraryQueryDTO queryDTO, int pageSize);

    /**
     * 根据项目编号查询
     * @param itemNo 项目编号
     * @return
     */
    MtcRepairItemLibrary queryByItemNo(String itemNo);

    /**
     * 检查名称是否唯一
     * @param itemNo      项目编号
     * @param itemName    名称
     * @param itemType    类型
     * @param vehicleModelSeq 车型
     * @return
     */
    boolean checkUnique(String itemNo, String itemName, Integer itemType, Long vehicleModelSeq);

    /**
     * 根据类型查询最新一条数据
     * @param itemType 类型
     * @return
     */
    MtcRepairItemLibrary queryLastDataByItemType(Integer itemType);
}