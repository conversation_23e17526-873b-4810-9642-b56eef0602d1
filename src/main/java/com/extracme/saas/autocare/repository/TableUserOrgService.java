package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.SysUserOrg;

import java.util.List;

/**
 * 用户机构关联表数据访问服务接口
 * 
 * 主要功能：
 * 1. 提供用户机构关联的基础CRUD操作
 * 2. 提供用户机构关联的查询功能
 * 3. 支持批量操作以提高性能
 */
public interface TableUserOrgService extends DefaultTableService<SysUserOrg, Long> {

    /**
     * 根据用户ID查询关联的所有机构ID
     *
     * @param userId 用户ID
     * @return 机构ID列表
     */
    List<String> findOrgIdsByUserId(Long userId);

    /**
     * 根据机构ID查询关联的所有用户ID
     *
     * @param orgId 机构ID
     * @return 用户ID列表
     */
    List<Long> findUserIdsByOrgId(String orgId);

    /**
     * 根据用户ID删除所有机构关联
     *
     * @param userId 用户ID
     * @return 删除的记录数
     */
    int deleteByUserId(Long userId);

    /**
     * 根据机构ID删除所有用户关联
     *
     * @param orgId 机构ID
     * @return 删除的记录数
     */
    int deleteByOrgId(String orgId);

    /**
     * 批量插入用户机构关联
     *
     * @param userId 用户ID
     * @param orgIds 机构ID列表
     * @return 插入的记录数
     */
    int batchInsert(Long userId, List<String> orgIds);

    /**
     * 更新用户的机构关联（先删除再插入）
     *
     * @param userId 用户ID
     * @param orgIds 新的机构ID列表
     * @return 操作是否成功
     */
    boolean updateUserOrgs(Long userId, List<String> orgIds);

    /**
     * 检查用户是否关联了指定机构
     *
     * @param userId 用户ID
     * @param orgId 机构ID
     * @return 是否关联
     */
    boolean existsByUserIdAndOrgId(Long userId, String orgId);

    /**
     * 根据用户ID列表批量查询用户机构关联
     *
     * @param userIds 用户ID列表
     * @return 用户机构关联列表
     */
    List<SysUserOrg> findByUserIds(List<Long> userIds);

    /**
     * 根据机构ID列表查询用户机构关联
     *
     * @param orgIds 机构ID列表
     * @return 用户机构关联列表
     */
    List<SysUserOrg> findByOrgIds(List<String> orgIds);
}
