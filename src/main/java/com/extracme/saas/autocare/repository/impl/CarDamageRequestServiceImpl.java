package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.DataCityInfoDynamicSqlSupport.dataCityInfo;
import static com.extracme.saas.autocare.mapper.base.DataProvinceInfoDynamicSqlSupport.dataProvinceInfo;
import static com.extracme.saas.autocare.mapper.base.MtcOrgInfoDynamicSqlSupport.mtcOrgInfo;
import static com.extracme.saas.autocare.mapper.base.MtcRepairDepotInfoDynamicSqlSupport.mtcRepairDepotInfo;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskDynamicSqlSupport.mtcRepairTask;
import static com.extracme.saas.autocare.mapper.base.MtcVehicleInfoDynamicSqlSupport.mtcVehicleInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.mapper.extend.MtcRepairTaskExtendMapper;
import com.extracme.saas.autocare.model.jingyou.EvalLossInfoAssessment;
import com.extracme.saas.autocare.model.jingyou.EvalLossInfoEvaluate;
import com.extracme.saas.autocare.model.jingyou.FactoryInfo;
import com.extracme.saas.autocare.model.jingyou.LossCoverVehicle;
import com.extracme.saas.autocare.model.jingyou.LossInsured;
import com.extracme.saas.autocare.model.jingyou.LossPolicy;
import com.extracme.saas.autocare.model.jingyou.LossReporting;
import com.extracme.saas.autocare.repository.CarDamageRequestService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CarDamageRequestServiceImpl implements CarDamageRequestService {

    @Autowired
    private MtcRepairTaskExtendMapper mtcRepairTaskMapper;

    @Override
    public EvalLossInfoAssessment queryEvalLossInfoAssessment(Long id) {
        log.info("查询评估定损信息, id: {}", id);
        try {
            if (id == null) {
                throw new BusinessException("维修任务ID不能为空");
            }

            // 使用SelectStatementProvider构建SQL查询
            SelectStatementProvider selectStatement = select(
                    mtcRepairTask.taskNo.as("loss_no"),
                    mtcRepairTask.taskNo.as("report_code"),
                    mtcRepairTask.vehicleNo.as("plate_no"),
                    mtcVehicleInfo.engineId.as("engine_no"),
                    mtcRepairTask.vin.as("vin_no"),
                    mtcRepairTask.repairDepotId.as("eval_handler_code"),
                    mtcRepairTask.repairDepotName.as("eval_handler_name"),
                    mtcVehicleInfo.registerDate.as("enrol_date"))
                    .from(mtcRepairTask)
                    .leftJoin(mtcVehicleInfo).on(mtcRepairTask.vin, equalTo(mtcVehicleInfo.vin))
                    .where(mtcRepairTask.id, isEqualTo(id))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            return mtcRepairTaskMapper.queryEvalLossInfoAssessment(selectStatement);
        } catch (Exception e) {
            log.error("查询评估定损信息失败, id: {}, 错误: {}", id, e.getMessage(), e);
            throw new BusinessException("查询评估定损信息失败: " + e.getMessage());
        }
    }

    @Override
    public FactoryInfo queryFactoryInfo(Long id) {
        log.info("查询修理厂信息, id: {}", id);
        try {
            if (id == null) {
                throw new BusinessException("维修任务ID不能为空");
            }

            // 使用SelectStatementProvider构建SQL查询
            SelectStatementProvider selectStatement = select(
                    mtcRepairDepotInfo.repairDepotId.as("factory_id"),
                    mtcRepairDepotInfo.repairDepotOrgId.as("com_code"),
                    mtcOrgInfo.orgName.as("com_name"),
                    mtcRepairDepotInfo.provinceId.as("province_code"),
                    dataProvinceInfo.province.as("province_name"),
                    mtcRepairDepotInfo.cityId.as("city_code"),
                    dataCityInfo.city.as("city_name"),
                    mtcRepairDepotInfo.repairDepotId.as("factory_code"),
                    mtcRepairDepotInfo.repairDepotName.as("factory_name"),
                    mtcRepairDepotInfo.repairDepotGrade.as("factory_qualification"))
                    .from(mtcRepairTask)
                    .leftJoin(mtcRepairDepotInfo).on(mtcRepairTask.repairDepotId, equalTo(mtcRepairDepotInfo.repairDepotId))
                    .leftJoin(mtcOrgInfo).on(mtcRepairDepotInfo.repairDepotOrgId, equalTo(mtcOrgInfo.orgId))
                    .leftJoin(dataProvinceInfo).on(mtcRepairDepotInfo.provinceId, equalTo(dataProvinceInfo.id))
                    .leftJoin(dataCityInfo).on(mtcRepairDepotInfo.cityId, equalTo(dataCityInfo.id))
                    .where(mtcRepairTask.id, isEqualTo(id))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            return mtcRepairTaskMapper.queryFactoryInfo(selectStatement);
        } catch (Exception e) {
            log.error("查询修理厂信息失败, id: {}, 错误: {}", id, e.getMessage(), e);
            throw new BusinessException("查询修理厂信息失败: " + e.getMessage());
        }
    }

    @Override
    public LossPolicy queryLossPolicy(Long id) {
        log.info("查询保单信息, id: {}", id);
        try {
            if (id == null) {
                throw new BusinessException("维修任务ID不能为空");
            }

            // 使用SelectStatementProvider构建SQL查询
            SelectStatementProvider selectStatement = select(
                    mtcRepairTask.taskNo.as("id"),
                    mtcRepairTask.taskNo.as("policy_code"),
                    mtcRepairTask.taskNo.as("report_code"),
                    mtcVehicleInfo.tciStartdate.as("insure_bgn_date"),
                    mtcVehicleInfo.tciEnddate.as("insure_end_date"),
                    mtcVehicleInfo.insuranceBelongs.as("company_code"),
                    mtcVehicleInfo.insuranceCompanyName.as("company_name"))
                    .from(mtcRepairTask)
                    .leftJoin(mtcVehicleInfo).on(mtcRepairTask.vin, equalTo(mtcVehicleInfo.vin))
                    .where(mtcRepairTask.id, isEqualTo(id))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);

            return mtcRepairTaskMapper.queryLossPolicy(selectStatement);
        } catch (Exception e) {
            log.error("查询保单信息失败, id: {}, 错误: {}", id, e.getMessage(), e);
            throw new BusinessException("查询保单信息失败: " + e.getMessage());
        }
    }

    @Override
    public LossCoverVehicle queryLossCoverVehicle(Long id) {
        log.info("查询车辆信息, id: {}", id);
        try {
            if (id == null) {
                throw new BusinessException("维修任务ID不能为空");
            }

            // 使用SelectStatementProvider构建SQL查询
            SelectStatementProvider selectStatement = select(
                    mtcRepairTask.vin.as("id"),
                    mtcVehicleInfo.registerDate.as("enrol_date"),
                    mtcRepairTask.vin.as("vin_no"),
                    mtcVehicleInfo.engineId.as("engine_no"),
                    mtcVehicleInfo.vehicleNo.as("plate_num"))
                    .from(mtcRepairTask)
                    .leftJoin(mtcVehicleInfo).on(mtcRepairTask.vin, equalTo(mtcVehicleInfo.vin))
                    .where(mtcRepairTask.id, isEqualTo(id))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);

            return mtcRepairTaskMapper.queryLossCoverVehicle(selectStatement);
        } catch (Exception e) {
            log.error("查询车辆信息失败, id: {}, 错误: {}", id, e.getMessage(), e);
            throw new BusinessException("查询车辆信息失败: " + e.getMessage());
        }
    }

    @Override
    public LossInsured queryLossInsured(Long id) {
        log.info("查询被保险人信息, id: {}", id);
        try {
            if (id == null) {
                throw new BusinessException("维修任务ID不能为空");
            }

            // 使用SelectStatementProvider构建SQL查询
            SelectStatementProvider selectStatement = select(
                    mtcRepairTask.taskNo.as("id"),
                    mtcRepairTask.taskNo.as("policy_id"))
                    .from(mtcRepairTask)
                    .where(mtcRepairTask.id, isEqualTo(id))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);

            return mtcRepairTaskMapper.queryLossInsured(selectStatement);
        } catch (Exception e) {
            log.error("查询被保险人信息失败, id: {}, 错误: {}", id, e.getMessage(), e);
            throw new BusinessException("查询被保险人信息失败: " + e.getMessage());
        }
    }

    @Override
    public LossReporting queryLossReporting(Long id) {
        log.info("查询报案信息, id: {}", id);
        try {
            if (id == null) {
                throw new BusinessException("维修任务ID不能为空");
            }

            // 使用SelectStatementProvider构建SQL查询
            SelectStatementProvider selectStatement = select(
                    mtcRepairTask.taskNo.as("id"),
                    mtcRepairTask.taskNo.as("report_code"))
                    .from(mtcRepairTask)
                    .where(mtcRepairTask.id, isEqualTo(id))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);

            return mtcRepairTaskMapper.queryLossReporting(selectStatement);
        } catch (Exception e) {
            log.error("查询报案信息失败, id: {}, 错误: {}", id, e.getMessage(), e);
            throw new BusinessException("查询报案信息失败: " + e.getMessage());
        }
    }

    @Override
    public EvalLossInfoEvaluate queryEvalLossInfoEvaluate(Long id) {
        log.info("查询评估信息, id: {}", id);
        try {
            if (id == null) {
                throw new BusinessException("维修任务ID不能为空");
            }

            // 使用SelectStatementProvider构建SQL查询
            SelectStatementProvider selectStatement = select(
                    mtcRepairTask.taskNo.as("dmg_vhcl_id"),
                    mtcRepairTask.taskNo.as("loss_no"),
                    mtcRepairTask.taskNo.as("report_code"))
                    .from(mtcRepairTask)
                    .where(mtcRepairTask.id, isEqualTo(id))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);

            return mtcRepairTaskMapper.queryEvalLossInfoEvaluate(selectStatement);
        } catch (Exception e) {
            log.error("查询评估信息失败, id: {}, 错误: {}", id, e.getMessage(), e);
            throw new BusinessException("查询评估信息失败: " + e.getMessage());
        }
    }
}
