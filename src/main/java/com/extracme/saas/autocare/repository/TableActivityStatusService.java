package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.ActivityStatus;

import java.util.List;

/**
 * 活动状态表数据访问服务接口
 */
public interface TableActivityStatusService extends DefaultTableService<ActivityStatus, Long> {

    /**
     * 根据ID查询活动状态
     *
     * @param id 活动状态ID
     * @return 活动状态
     */
    ActivityStatus selectById(Long id);

    /**
     * 根据状态编码查询活动状态
     *
     * @param statusCode 状态编码
     * @return 活动状态
     */
    ActivityStatus selectByStatusCode(String statusCode);

    /**
     * 查询所有活动状态
     *
     * @return 活动状态列表
     */
    List<ActivityStatus> findAll();

    /**
     * 根据状态名称模糊查询活动状态
     *
     * @param statusName 状态名称
     * @return 活动状态列表
     */
    List<ActivityStatus> findByStatusNameLike(String statusName);

    /**
     * 插入活动状态
     *
     * @param activityStatus 活动状态
     * @return 插入后的活动状态（包含ID）
     */
    ActivityStatus insert(ActivityStatus activityStatus);

    /**
     * 插入活动状态（带操作人）
     *
     * @param activityStatus 活动状态
     * @param operator 操作人
     * @return 插入后的活动状态（包含ID）
     */
    ActivityStatus insert(ActivityStatus activityStatus, String operator);

    /**
     * 根据ID选择性更新活动状态
     *
     * @param activityStatus 活动状态
     * @return 影响行数
     */
    int updateSelectiveById(ActivityStatus activityStatus);

    /**
     * 根据ID选择性更新活动状态（带操作人）
     *
     * @param activityStatus 活动状态
     * @param operator 操作人
     * @return 影响行数
     */
    int updateSelectiveById(ActivityStatus activityStatus, String operator);

    /**
     * 根据ID删除活动状态
     *
     * @param id 活动状态ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 检查状态编码是否存在
     *
     * @param statusCode 状态编码
     * @return 是否存在
     */
    boolean existsByStatusCode(String statusCode);
}
