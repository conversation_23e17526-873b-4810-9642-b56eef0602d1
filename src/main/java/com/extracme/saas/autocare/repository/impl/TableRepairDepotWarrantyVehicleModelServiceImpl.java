package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.base.MtcRepairDepotWarrantyVehicleModelInfoDynamicSqlSupport;
import com.extracme.saas.autocare.mapper.extend.MtcRepairDepotWarrantyVehicleModelExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotWarrantyVehicleModelInfo;
import com.extracme.saas.autocare.repository.TableRepairDepotWarrantyVehicleModelService;
import org.apache.commons.collections4.CollectionUtils;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.extracme.saas.autocare.mapper.base.MtcRepairDepotWarrantyVehicleModelInfoDynamicSqlSupport.mtcRepairDepotWarrantyVehicleModelInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.deleteFrom;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

/**
 * 维修厂可保修车型数据访问服务实现类
 */
@Repository
public class TableRepairDepotWarrantyVehicleModelServiceImpl implements TableRepairDepotWarrantyVehicleModelService {


    @Autowired
    private MtcRepairDepotWarrantyVehicleModelExtendMapper mapper;


    @Override
    public List<MtcRepairDepotWarrantyVehicleModelInfo> queryVehicleModelList(String repairDepotId) {
        if (repairDepotId != null){
            return mapper.select(dsl -> dsl.where(MtcRepairDepotWarrantyVehicleModelInfoDynamicSqlSupport.repairDepotId, isEqualTo(repairDepotId)));
        }
        return Collections.emptyList();
    }

    @Override
    public int batchInsert(List<MtcRepairDepotWarrantyVehicleModelInfo> vehicleModelList) {
        if (CollectionUtils.isEmpty(vehicleModelList)){
            return 0;
        }
        Date now = new Date();
        for (MtcRepairDepotWarrantyVehicleModelInfo vehicleModel : vehicleModelList) {
            vehicleModel.setCreatedTime(now);
            vehicleModel.setUpdatedTime(now);
            mapper.insertSelective(vehicleModel);
        }
        return 1;
    }

    @Override
    public int deleteByRepairDepotId(String repairDepotId) {
        DeleteStatementProvider render = deleteFrom(mtcRepairDepotWarrantyVehicleModelInfo).where()
                .and(mtcRepairDepotWarrantyVehicleModelInfo.repairDepotId, isEqualTo(repairDepotId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mapper.delete(render);
    }
}
