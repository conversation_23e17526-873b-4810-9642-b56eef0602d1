package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcRepairItemLibraryDynamicSqlSupport.mtcRepairItemLibrary;
import static com.extracme.saas.autocare.mapper.base.MtcRepairItemLibraryLocalDynamicSqlSupport.mtcRepairItemLibraryLocal;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcRepairItemLibraryLocalExtendMapper;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryCheckQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryLocalQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibraryLocal;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryCheckListVO;
import com.extracme.saas.autocare.repository.TableRepairItemLibraryLocalService;
import com.extracme.saas.autocare.util.SessionUtils;


/**
 * 维修项目库表数据访问服务实现类
 */
@Repository
public class TableRepairItemLibraryLocalServiceImpl implements TableRepairItemLibraryLocalService {


    @Autowired
    private MtcRepairItemLibraryLocalExtendMapper mapper;


    @Override
    public List<MtcRepairItemLibraryLocal> queryList(RepairItemLibraryLocalQueryDTO queryDTO) {
        SelectStatementProvider selectStatementProvider = select(mtcRepairItemLibraryLocal.allColumns())
                .from(mtcRepairItemLibraryLocal)
                .where(mtcRepairItemLibraryLocal.status, isEqualToWhenPresent(queryDTO.getStatus()))
                .and(mtcRepairItemLibraryLocal.itemId, isEqualToWhenPresent(queryDTO.getItemId()))
                .and(mtcRepairItemLibraryLocal.orgId, isEqualToWhenPresent(queryDTO.getOrgId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mapper.selectMany(selectStatementProvider);
    }

    @Override
    public MtcRepairItemLibraryLocal selectById(Long id) {
        return mapper.selectByPrimaryKey(id).orElse(null);
    }


    @Override
    public MtcRepairItemLibraryLocal insert(MtcRepairItemLibraryLocal repairItemLibraryLocal) {
         // 从会话中获取当前用户作为操作人
         String operator = SessionUtils.getLoginUser().getUsername();
         insert(repairItemLibraryLocal , operator);
        return repairItemLibraryLocal;
    }

    @Override
    public MtcRepairItemLibraryLocal insert(MtcRepairItemLibraryLocal repairItemLibraryLocal, String operator) {
        Date now = new Date();
        repairItemLibraryLocal.setCreatedTime(now);
        repairItemLibraryLocal.setUpdatedTime(now);
        repairItemLibraryLocal.setCreateBy(operator);
        repairItemLibraryLocal.setUpdateBy(operator);
        mapper.insertSelective(repairItemLibraryLocal);
        return repairItemLibraryLocal;
    }

    @Override
    public int updateSelectiveById(MtcRepairItemLibraryLocal repairItemLibraryLocal, String operator) {
        repairItemLibraryLocal.setUpdatedTime(new Date());
        repairItemLibraryLocal.setUpdateBy(operator);
        return mapper.updateByPrimaryKeySelective(repairItemLibraryLocal);
    }

    @Override
    public int updateSelectiveById(MtcRepairItemLibraryLocal repairItemLibraryLocal) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        return updateSelectiveById(repairItemLibraryLocal, operator);
    }

    @Override
    public MtcRepairItemLibraryLocal selectByItemIdAndOrgId(Long itemId, String orgId) {
        SelectStatementProvider selectStatementProvider = select(mtcRepairItemLibraryLocal.allColumns())
                .from(mtcRepairItemLibraryLocal)
                .where(mtcRepairItemLibraryLocal.itemId, isEqualTo(itemId))
                .and(mtcRepairItemLibraryLocal.orgId, isEqualTo(orgId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<MtcRepairItemLibraryLocal> optional = mapper.selectOne(selectStatementProvider);
        return optional.orElse(null);
    }

    @Override
    public List<RepairItemLibraryCheckListVO> queryLibraryCheckList(RepairItemLibraryCheckQueryDTO queryDTO) {
        SelectStatementProvider selectStatement = select(
                mtcRepairItemLibrary.id,
                mtcRepairItemLibrary.itemNo,
                mtcRepairItemLibrary.itemName,
                mtcRepairItemLibrary.itemType,
                mtcRepairItemLibrary.editable,
                mtcRepairItemLibrary.hourFeeNationalMarketPrice,
                mtcRepairItemLibrary.materialCostNationalMarketPrice,
                mtcRepairItemLibraryLocal.hourFeeLocalMarketPrice,
                mtcRepairItemLibraryLocal.materialCostLocalMarketPrice
        )
                .from(mtcRepairItemLibrary)
                .leftJoin(mtcRepairItemLibraryLocal)
                .on(mtcRepairItemLibrary.id, equalTo(mtcRepairItemLibraryLocal.itemId),
                        SqlBuilder.and(mtcRepairItemLibraryLocal.orgId,equalTo(SqlBuilder.stringConstant(queryDTO.getOrgId()))),
                        SqlBuilder.and(mtcRepairItemLibraryLocal.status,equalTo(SqlBuilder.constant("1"))))
                .where()
                .and(mtcRepairItemLibrary.itemType, isInWhenPresent(queryDTO.getItemTypeList()))
                .and(mtcRepairItemLibrary.itemName, isEqualToWhenPresent(queryDTO.getItemName()))
                .and(mtcRepairItemLibrary.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelSeq()))
                .and(mtcRepairItemLibrary.status, isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mapper.selectLibraryCheckList(selectStatement);
    }
}
