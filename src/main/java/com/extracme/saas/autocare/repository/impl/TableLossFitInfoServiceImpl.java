package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcLossFitInfoDynamicSqlSupport.mtcLossFitInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcLossFitInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcLossFitInfo;
import com.extracme.saas.autocare.repository.TableLossFitInfoService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 损失配件信息表数据访问服务实现
 */
@Repository
public class TableLossFitInfoServiceImpl implements TableLossFitInfoService {

    @Autowired
    private MtcLossFitInfoExtendMapper mtcLossFitInfoMapper;

    @Override
    public MtcLossFitInfo insert(MtcLossFitInfo record) {
        // 调用带操作人参数的方法
        return insert(record, "");
    }

    @Override
    public MtcLossFitInfo insert(MtcLossFitInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcLossFitInfoMapper.insertSelective(record);
        return record;
    }

    @Override
    public MtcLossFitInfo selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcLossFitInfoMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<MtcLossFitInfo> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(mtcLossFitInfo.taskNo, isEqualTo(taskNo))
                .and(mtcLossFitInfo.status, isEqualTo(1))
                .orderBy(id.descending());
        return mtcLossFitInfoMapper.select(completer);
    }

    @Override
    public int updateSelectiveById(MtcLossFitInfo record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcLossFitInfo record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return mtcLossFitInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchInsert(List<MtcLossFitInfo> fitInfoList) {
        if (fitInfoList == null || fitInfoList.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (MtcLossFitInfo fitInfo : fitInfoList) {
            count += mtcLossFitInfoMapper.insertSelective(fitInfo);
        }
        return count;
    }

    @Override
    public int updateStatus(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }

        Date now = new Date();
        String operator = null != SessionUtils.getLoginUser() ? SessionUtils.getLoginUser().getUsername() : "精友回调";

        return mtcLossFitInfoMapper.update(c -> c.set(mtcLossFitInfo.status).equalTo(0)
                .set(mtcLossFitInfo.updatedTime).equalTo(now)
                .set(mtcLossFitInfo.updateBy).equalTo(operator)
                .where(mtcLossFitInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int deleteByTaskNo(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }

        return mtcLossFitInfoMapper.delete(c -> c.where(mtcLossFitInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int updateSelectiveByTaskNoAndStatus(MtcLossFitInfo record, String taskNo, String operator) {
        if (record == null || StringUtils.isBlank(taskNo)) {
            return 0;
        }

        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);

        return mtcLossFitInfoMapper.updateSelectiveByTaskNoAndStatus(record);
    }
}
