package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.extend.MtcLossInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcLossInfo;
import com.extracme.saas.autocare.repository.TableLossInfoService;
import com.extracme.saas.autocare.util.SessionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcLossInfoDynamicSqlSupport.mtcLossInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

/**
 * 损失信息表数据访问服务实现
 */
@Repository
public class TableLossInfoServiceImpl implements TableLossInfoService {

    @Autowired
    private MtcLossInfoExtendMapper mtcLossInfoMapper;

    @Override
    public MtcLossInfo insert(MtcLossInfo record) {
        // 调用带操作人参数的方法
        return insert(record, "");
    }

    @Override
    public MtcLossInfo insert(MtcLossInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcLossInfoMapper.insertSelective(record);
        return record;
    }

    @Override
    public MtcLossInfo selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcLossInfoMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<MtcLossInfo> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(mtcLossInfo.taskNo, isEqualTo(taskNo))
                .and(mtcLossInfo.status, isEqualTo(1))
                .orderBy(id.descending());
        return mtcLossInfoMapper.select(completer);
    }

    @Override
    public int updateSelectiveById(MtcLossInfo record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcLossInfo record, String operator) {
        if (record == null || record.getId() == null) {
            return 0;
        }

        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);

        return mtcLossInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateStatus(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }

        Date now = new Date();
        String operator = null != SessionUtils.getLoginUser() ? SessionUtils.getLoginUser().getUsername() : "精友回调";

        return mtcLossInfoMapper.update(c -> c.set(mtcLossInfo.status).equalTo(0)
                .set(mtcLossInfo.updatedTime).equalTo(now)
                .set(mtcLossInfo.updateBy).equalTo(operator)
                .where(mtcLossInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int deleteByTaskNo(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }

        return mtcLossInfoMapper.delete(c -> c.where(mtcLossInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int updateSelectiveByTaskNoAndStatus(MtcLossInfo record, String taskNo, String operator) {
        if (record == null || StringUtils.isBlank(taskNo)) {
            return 0;
        }

        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);

        return mtcLossInfoMapper.updateSelectiveByTaskNoAndStatus(record);
    }
}
