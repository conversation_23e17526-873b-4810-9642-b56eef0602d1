package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcOrgInfo;

import java.util.List;

/**
 * 组织机构信息Repository接口
 */
public interface TableOrgInfoService extends DefaultTableService<MtcOrgInfo, Long> {

    /**
     * 查询有效的组织机构列表
     * @return 组织机构列表
     */
    List<MtcOrgInfo> findValidOrgs();

    /**
     * 查询有效的组织机构列表
     * @return 组织机构列表
     */
    List<MtcOrgInfo> findValidOrgsByOrgId(String orgId);

    /**
     * 根据组织机构ID查询组织机构信息
     * @param orgId 组织机构ID
     * @return 组织机构信息
     */
    MtcOrgInfo selectByOrgId(String orgId);

    /**
     * 根据组织机构ID查询组织机构信息
     * @param uniqueId 唯一标识
     * @return 组织机构信息
     */
    MtcOrgInfo selectByUniqueId(String uniqueId);

    /**
     * 根据机构ID查询机构信息
     *
     * @param orgId 机构ID
     * @return 机构信息
     */
    MtcOrgInfo findByOrgId(String orgId);

    /**
     * 根据机构ID列表批量查询机构信息
     *
     * @param orgIds 机构ID列表
     * @return 机构信息列表
     */
    List<MtcOrgInfo> findByOrgIds(List<String> orgIds);

    /**
     * 根据父机构的uniqueId查询所有直接子机构
     * 注意：子机构的parentId字段存储的是父机构的uniqueId
     *
     * @param parentUniqueId 父机构的uniqueId
     * @return 直接子机构列表
     */
    List<MtcOrgInfo> findDirectChildrenByParentId(String parentUniqueId);

    /**
     * 根据机构ID列表递归查询所有子机构（包括自身）
     * 返回扁平化的机构ID列表，包含传入的机构ID及其所有层级的子机构ID
     *
     * @param orgIds 机构ID列表
     * @return 包含所有子机构的扁平化机构ID列表
     */
    List<String> findAllDescendantOrgIds(List<String> orgIds);

    /**
     * 批量查询模式：根据机构ID列表递归查询所有子机构（包括自身）
     * 使用一次性查询所有组织数据，然后在内存中构建层级关系，避免N+1查询问题
     *
     * @param orgIds 机构ID列表（基于orgId字段）
     * @return 包含所有子机构的扁平化机构ID列表
     */
    List<String> findAllDescendantOrgIdsBatch(List<String> orgIds);
}
