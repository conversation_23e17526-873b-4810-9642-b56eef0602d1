package com.extracme.saas.autocare.repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.extracme.saas.autocare.model.dto.GetRepairNumDTO;
import com.extracme.saas.autocare.model.dto.RepairCustInfoDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotQueryDTO;
import com.extracme.saas.autocare.model.dto.VehicleRepairRecordQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskProcessQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskWorkflowDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.vo.RepairDepotInRepairingVO;
import com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO;
import com.extracme.saas.autocare.model.vo.VehicleRepairRecordVO;

/**
 * 维修任务表数据访问服务接口
 */
public interface TableRepairTaskService extends DefaultTableService<MtcRepairTask, Long> {

    /**
     * 根据任务编号查询维修任务
     *
     * @param taskNo 任务编号
     * @return 维修任务
     */
    MtcRepairTask selectByTaskNo(String taskNo);

    /**
     * 根据车架号查询维修任务
     *
     * @param vin 车架号
     * @return 维修任务列表
     */
    List<MtcRepairTask> selectByVin(String vin);

    /**
     * 流程查询-查询维修任务列表
     *
     * @param queryDTO 查询条件
     * @return 维修任务列表
     */
    List<RepairTaskProcessListVO> queryProcessList(RepairTaskProcessQueryDTO queryDTO);

    /**
     * 流程查询-导出维修任务列表
     *
     * @param lastId
     * @param queryDTO 查询条件
     * @param pageSize 每页条数
     * @return 维修任务列表
     */
    List<RepairTaskProcessListVO> exportProcessListWithPageSize(long lastId, RepairTaskProcessQueryDTO queryDTO, int pageSize);

    /**
     * 获取任务是否超时
     *
     * @param id 维修任务ID
     * @return 是否超时 0:是 1:否
     */
    String getOverTime(Long id);

    /**
     * 通过活动实例表查询维修任务列表
     *
     * @param queryDTO 查询条件
     * @param tenantId 租户ID
     * @return 维修任务列表
     */
    List<RepairTaskProcessListVO> queryRepairTaskListByActivityInstance(RepairTaskListQueryDTO queryDTO, Integer tenantId);

    /**
     * 通过活动实例表查询维修任务列表
     *
     * @param queryDTO 查询条件
     * @param tenantId 租户ID
     * @return 维修任务列表
     */
    List<RepairTaskProcessListVO> queryRepairTaskListByWorkflowInstance(RepairTaskListQueryDTO queryDTO, Integer tenantId);

    /**
     * 清除任务核损核价占有人
     *
     * @param taskId   维修任务主建
     * @param operator 操作人
     * @return 更新结果
     */
    int clearOwner(Long taskId, String operator);

    /**
     * 获取维修厂所有维修任务数量
     *
     * @param getRepairNumDTO
     * @return
     */
    List<RepairTaskWorkflowDTO> getRepairNum(GetRepairNumDTO getRepairNumDTO);

    /**
     * 获取维修厂正在维修的数量
     *
     * @param getRepairNumDTO
     * @return
     */
    List<RepairTaskWorkflowDTO> getCurrentRepairNum(GetRepairNumDTO getRepairNumDTO);

    /**
     * 获取维修厂当日新增任务的数量
     *
     * @param getRepairNumDTO
     * @return
     */
    List<RepairTaskWorkflowDTO> getTodayRepairNum(GetRepairNumDTO getRepairNumDTO);

    /**
     * 获取维修厂当日验收通过的数量
     *
     * @param getRepairNumDTO
     * @return
     */
    List<RepairTaskWorkflowDTO> getTodayCompleteNum(GetRepairNumDTO getRepairNumDTO);

    /**
     * 查询修理厂在修信息
     *
     * @param queryDTO 查询条件
     * @return 修理厂在修信息列表
     */
    List<RepairDepotInRepairingVO> queryRepairDepotInRepairingInfo(RepairDepotQueryDTO queryDTO);

    /**
     * 根据目标活动节点编码查询活动实例，并按状态码分组统计数量
     *
     * @param repairTaskListQueryDTO
     * @return 状态码及对应的数量Map，key为状态码，value为数量
     */
    Map<String, Long> countByToActivityCodeGroupByStatusCode(RepairTaskListQueryDTO repairTaskListQueryDTO);

    /**
     * 根据目标活动节点编码查询活动实例，并按状态码分组统计数量
     *
     * @param repairTaskListQueryDTO
     * @return 状态码及对应的数量Map，key为状态码，value为数量
     */
    Map<String, Long> countByActivityCode(RepairTaskListQueryDTO repairTaskListQueryDTO);

    /**
     * 查询车辆维修记录
     *
     * @param queryDTO
     * @return
     */
    List<VehicleRepairRecordVO> queryVehicleRepairRecord(VehicleRepairRecordQueryDTO queryDTO);

    /**
     * 调整客户支付信息
     *
     * @param repairCustInfoDTO
     * @return
     */
    int adjustCustAmount(RepairCustInfoDTO repairCustInfoDTO);

    /**
     * 修改自费金额
     *
     * @param id               维修任务id
     * @param selfFundedAmount 自费金额
     * @return
     */
    int updateSelfFundedAmount(Long id, BigDecimal selfFundedAmount);
}
