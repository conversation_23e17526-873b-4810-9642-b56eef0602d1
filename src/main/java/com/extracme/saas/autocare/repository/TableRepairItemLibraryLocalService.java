package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.dto.RepairItemLibraryCheckQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryLocalQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibraryLocal;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryCheckListVO;

import java.util.List;

/**
 * 维修项目本地库表数据访问服务接口
 */
public interface TableRepairItemLibraryLocalService extends DefaultTableService<MtcRepairItemLibraryLocal, Long> {

    /**
     * 查询列表
     *
     * @param queryDTO 查询参数
     * @return
     */
    List<MtcRepairItemLibraryLocal> queryList(RepairItemLibraryLocalQueryDTO queryDTO);

    /**
     * 根据项目ID和组织ID查询本地维修项目库信息
     *
     * @param itemId 维修项目ID
     * @param orgId 组织ID
     * @return 本地维修项目库信息，如果不存在则返回null
     */
    MtcRepairItemLibraryLocal selectByItemIdAndOrgId(Long itemId, String orgId);


    /**
     * 查询待定损配件库列表
     *
     * @param queryDTO 查询参数
     * @return
     */
    List<RepairItemLibraryCheckListVO> queryLibraryCheckList(RepairItemLibraryCheckQueryDTO queryDTO);
}
