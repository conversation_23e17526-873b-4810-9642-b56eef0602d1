package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.dto.workflow.WorkflowInstanceQueryDTO;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;

import java.util.List;
import java.util.Map;

/**
 * 流程实例表数据访问服务接口
 */
public interface TableWorkflowInstanceService extends DefaultTableService<WorkflowInstance, Long> {

    /**
     * 根据业务ID查询流程实例
     * @param businessId 业务ID
     * @return 流程实例
     */
    WorkflowInstance selectByBusinessId(String businessId);

    /**
     * 根据业务ID和租户ID查询流程实例
     * 用于多租户环境下的数据隔离查询
     * @param businessId 业务ID
     * @param tenantId 租户ID
     * @return 流程实例
     */
    WorkflowInstance selectByBusinessIdAndTenantId(String businessId, Integer tenantId);

    /**
     * 根据当前活动节点编号查询流程实例列表
     * @param currentActivityCode 当前活动节点编号
     * @return 流程实例列表
     */
    List<WorkflowInstance> selectByCurrentActivityCode(String currentActivityCode);

    /**
     * 根据当前活动节点编号和状态码查询流程实例列表
     * @param currentActivityCode 当前活动节点编号
     * @param statusCode 状态码
     * @return 流程实例列表
     */
    List<WorkflowInstance> selectByCurrentActivityCodeAndStatusCode(String currentActivityCode, String statusCode);

    /**
     * 根据当前活动节点编号查询流程实例，并按状态码分组统计数量
     * @param activityCode 当前活动节点编号
     * @return 状态码及对应的数量Map，key为状态码，value为数量
     */
    Map<String, Long> countByActivityCodeGroupByStatusCode(String activityCode);

    /**
     * 根据流程模板ID查询流程实例列表
     * @param workflowId 流程模板ID
     * @return 流程实例列表
     */
    List<WorkflowInstance> selectByWorkflowId(Long workflowId);

    /**
     * 根据租户ID查询流程实例列表
     * @param tenantId 租户ID
     * @return 流程实例列表
     */
    List<WorkflowInstance> selectByTenantId(Integer tenantId);

    /**
     * 根据查询条件查询工作流实例列表
     * 支持多条件组合查询，使用MyBatis Dynamic SQL实现
     *
     * @param queryDTO 查询条件对象
     * @return 工作流实例列表
     */
    List<WorkflowInstance> findByCondition(WorkflowInstanceQueryDTO queryDTO);

    /**
     * 根据ID删除工作流实例
     *
     * @param id 工作流实例ID
     * @return 影响行数
     */
    int deleteById(Long id);
}
