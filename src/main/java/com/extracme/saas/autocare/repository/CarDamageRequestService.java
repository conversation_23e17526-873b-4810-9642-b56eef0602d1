package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.jingyou.EvalLossInfoAssessment;
import com.extracme.saas.autocare.model.jingyou.EvalLossInfoEvaluate;
import com.extracme.saas.autocare.model.jingyou.FactoryInfo;
import com.extracme.saas.autocare.model.jingyou.LossCoverVehicle;
import com.extracme.saas.autocare.model.jingyou.LossInsured;
import com.extracme.saas.autocare.model.jingyou.LossPolicy;
import com.extracme.saas.autocare.model.jingyou.LossReporting;

/**
 * <AUTHOR>
 * @date 2019-03-14 09:16
 */
public interface CarDamageRequestService {
    EvalLossInfoAssessment queryEvalLossInfoAssessment(Long id);

    FactoryInfo queryFactoryInfo(Long id);

    LossPolicy queryLossPolicy(Long id);

    LossCoverVehicle queryLossCoverVehicle(Long id);

    LossInsured queryLossInsured(Long id);

    LossReporting queryLossReporting(Long id);

    EvalLossInfoEvaluate queryEvalLossInfoEvaluate(Long id);
}
