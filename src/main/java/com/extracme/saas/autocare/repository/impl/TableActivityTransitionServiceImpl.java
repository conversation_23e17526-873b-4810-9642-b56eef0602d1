package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.base.ActivityTransitionMapper;
import com.extracme.saas.autocare.model.entity.ActivityTransition;
import com.extracme.saas.autocare.repository.TableActivityTransitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.extracme.saas.autocare.mapper.base.ActivityTransitionDynamicSqlSupport.activityTransition;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

/**
 * 活动节点转换规则表数据访问服务实现类
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Repository
public class TableActivityTransitionServiceImpl implements TableActivityTransitionService {

    @Autowired
    private ActivityTransitionMapper activityTransitionMapper;

    @Override
    public ActivityTransition selectById(Long id) {
        if (id == null) {
            return null;
        }
        Optional<ActivityTransition> optional = activityTransitionMapper.selectByPrimaryKey(id);
        return optional.orElse(null);
    }

    @Override
    public List<ActivityTransition> selectByWorkflowId(Long workflowId) {
        if (workflowId == null) {
            return Collections.emptyList();
        }
        return activityTransitionMapper.select(c ->
                c.where(activityTransition.workflowId, isEqualTo(workflowId))
        );
    }

    @Override
    public List<ActivityTransition> selectByWorkflowIdAndFromActivityCode(Long workflowId, String fromActivityCode) {
        if (workflowId == null || fromActivityCode == null) {
            return Collections.emptyList();
        }
        return activityTransitionMapper.select(c ->
                c.where(activityTransition.workflowId, isEqualTo(workflowId))
                .and(activityTransition.fromActivityCode, isEqualTo(fromActivityCode))
        );
    }

    @Override
    public ActivityTransition selectByWorkflowIdAndFromActivityCodeAndTriggerEvent(Long workflowId, String fromActivityCode, String triggerEvent) {
        if (workflowId == null || fromActivityCode == null || triggerEvent == null) {
            return null;
        }
        Optional<ActivityTransition> optional = activityTransitionMapper.selectOne(c ->
                c.where(activityTransition.workflowId, isEqualTo(workflowId))
                .and(activityTransition.fromActivityCode, isEqualTo(fromActivityCode))
                .and(activityTransition.triggerEvent, isEqualTo(triggerEvent))
        );
        return optional.orElse(null);
    }

    @Override
    public int insert(ActivityTransition activityTransition) {
        if (activityTransition == null) {
            return 0;
        }
        return activityTransitionMapper.insertSelective(activityTransition);
    }

    @Override
    public int insert(ActivityTransition activityTransition, String operator) {
        if (activityTransition == null) {
            return 0;
        }
        Date now = new Date();
        activityTransition.setCreateTime(now);
        activityTransition.setCreateBy(operator);
        activityTransition.setUpdateTime(now);
        activityTransition.setUpdateBy(operator);
        return activityTransitionMapper.insertSelective(activityTransition);
    }

    @Override
    public int update(ActivityTransition activityTransition) {
        if (activityTransition == null || activityTransition.getId() == null) {
            return 0;
        }
        return activityTransitionMapper.updateByPrimaryKeySelective(activityTransition);
    }

    @Override
    public int updateByPrimaryKey(ActivityTransition activityTransition) {
        if (activityTransition == null || activityTransition.getId() == null) {
            return 0;
        }
        return activityTransitionMapper.updateByPrimaryKey(activityTransition);
    }

    @Override
    public int updateByPrimaryKey(ActivityTransition activityTransition, String operator) {
        if (activityTransition == null || activityTransition.getId() == null) {
            return 0;
        }
        Date now = new Date();
        activityTransition.setUpdateTime(now);
        activityTransition.setUpdateBy(operator);
        return activityTransitionMapper.updateByPrimaryKey(activityTransition);
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return activityTransitionMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int deleteByWorkflowId(Long workflowId) {
        if (workflowId == null) {
            return 0;
        }
        return activityTransitionMapper.delete(c ->
                c.where(activityTransition.workflowId, isEqualTo(workflowId))
        );
    }
}
