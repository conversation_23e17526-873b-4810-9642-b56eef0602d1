package com.extracme.saas.autocare.repository;

import java.util.List;
import java.util.Optional;

import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysRolePermission;

/**
 * 权限表数据访问服务
 */
public interface TablePermissionService extends DefaultTableService<SysPermission, Long> {

    /**
     * 根据权限编码查询权限
     *
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    Optional<SysPermission> findByPermissionCode(String permissionCode);

    /**
     * 根据父级ID查询权限列表
     *
     * @param parentId 父级ID
     * @return 权限列表
     */
    List<SysPermission> findByParentId(Long parentId);

    /**
     * 查询所有权限
     *
     * @return 权限列表
     */
    List<SysPermission> findAll();

    /**
     * 根据角色ID删除角色权限关联
     *
     * @param roleId 角色ID
     */
    void deleteByRoleId(Long roleId);

    /**
     * 批量创建角色权限关联
     *
     * @param rolePermissions 角色权限关联列表
     */
    void batchCreate(List<SysRolePermission> rolePermissions);

    /**
     * 根据角色ID查询权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> findPermissionIdsByRoleId(Long roleId);

    /**
     * 根据角色ID列表查询权限ID列表
     *
     * @param roleIds 角色ID列表
     * @return 权限ID列表
     */
    List<Long> findPermissionIdsByRoleIds(List<Long> roleIds);

    /**
     * 根据类型查询权限列表
     *
     * @param type 权限类型
     * @return 权限列表
     */
    List<SysPermission> findByType(Integer type);

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<SysPermission> findByUserId(Long userId);

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<SysPermission> findByRoleId(Long roleId);

    /**
     * 分配角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    void assignRolePermissions(Long roleId, List<Long> permissionIds);

    /**
     * 检查用户是否有指定权限
     *
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String permissionCode);

    /**
     * 检查权限编码是否已存在
     *
     * @param code 权限编码
     * @return 是否存在
     */
    boolean existsByCode(String code);

    /**
     * 检查权限是否被角色使用
     *
     * @param permissionId 权限ID
     * @return 是否被使用
     */
    boolean isUsedByRoles(Long permissionId);

    /**
     * 根据权限ID查询使用该权限的角色ID列表
     *
     * @param permissionId 权限ID
     * @return 角色ID列表
     */
    List<Long> findRoleIdsByPermissionId(Long permissionId);

    /**
     * 根据权限ID删除角色权限关联
     *
     * @param permissionId 权限ID
     */
    void deleteByPermissionId(Long permissionId);

    /**
     * 根据ID物理删除权限
     *
     * @param id 权限ID
     * @return 删除的记录数
     */
    int deleteById(Long id);
}