package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.extend.SysUserOrgExtendMapper;
import com.extracme.saas.autocare.model.entity.SysUserOrg;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.extracme.saas.autocare.mapper.base.SysUserOrgDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 用户机构关联表数据访问服务实现类
 *
 * 主要功能：
 * 1. 提供用户机构关联的基础CRUD操作
 * 2. 提供用户机构关联的查询功能
 * 3. 支持批量操作以提高性能
 * 4. 确保数据访问的事务性和安全性
 */
@Slf4j
@Repository
public class TableUserOrgServiceImpl implements TableUserOrgService {

    /**
     * 用户机构关联扩展Mapper，提供对用户机构关联表的操作能力
     */
    @Autowired
    private SysUserOrgExtendMapper userOrgExtendMapper;

    @Override
    public SysUserOrg selectById(Long id) {
        if (id == null) {
            return null;
        }
        return userOrgExtendMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public SysUserOrg insert(SysUserOrg entity) {
        if (entity == null) {
            return null;
        }
        userOrgExtendMapper.insertSelective(entity);
        return entity;
    }

    @Override
    public SysUserOrg insert(SysUserOrg entity, String operator) {
        // 用户机构关联表没有操作人字段，直接调用无操作人的方法
        return insert(entity);
    }

    /**
     * 更新用户机构关联记录
     *
     * @param entity 要更新的实体
     * @return 更新后的实体
     */
    public SysUserOrg update(SysUserOrg entity) {
        if (entity == null || entity.getId() == null) {
            return null;
        }
        userOrgExtendMapper.updateByPrimaryKeySelective(entity);
        return entity;
    }

    @Override
    public int updateSelectiveById(SysUserOrg entity) {
        if (entity == null || entity.getId() == null) {
            return 0;
        }
        return userOrgExtendMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public int updateSelectiveById(SysUserOrg entity, String operator) {
        // 用户机构关联表没有操作人字段，直接调用无操作人的方法
        return updateSelectiveById(entity);
    }

    /**
     * 根据主键删除用户机构关联记录
     *
     * @param id 主键ID
     * @return 是否删除成功
     */
    public boolean deleteById(Long id) {
        if (id == null) {
            return false;
        }
        return userOrgExtendMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public List<String> findOrgIdsByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = select(sysUserOrg.orgId)
            .from(sysUserOrg)
            .where(sysUserOrg.userId, isEqualTo(userId))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        List<SysUserOrg> userOrgs = userOrgExtendMapper.selectMany(selectStatement);
        return userOrgs.stream()
            .map(SysUserOrg::getOrgId)
            .collect(Collectors.toList());
    }

    @Override
    public List<Long> findUserIdsByOrgId(String orgId) {
        if (orgId == null || orgId.trim().isEmpty()) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = select(sysUserOrg.userId)
            .from(sysUserOrg)
            .where(sysUserOrg.orgId, isEqualTo(orgId))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        List<SysUserOrg> userOrgs = userOrgExtendMapper.selectMany(selectStatement);
        return userOrgs.stream()
            .map(SysUserOrg::getUserId)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByUserId(Long userId) {
        if (userId == null) {
            return 0;
        }

        return userOrgExtendMapper.delete(c ->
            c.where(sysUserOrg.userId, isEqualTo(userId))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByOrgId(String orgId) {
        if (orgId == null || orgId.trim().isEmpty()) {
            return 0;
        }

        return userOrgExtendMapper.delete(c ->
            c.where(sysUserOrg.orgId, isEqualTo(orgId))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(Long userId, List<String> orgIds) {
        if (userId == null || orgIds == null || orgIds.isEmpty()) {
            return 0;
        }

        int insertCount = 0;
        for (String orgId : orgIds) {
            if (orgId != null && !orgId.trim().isEmpty()) {
                SysUserOrg userOrg = new SysUserOrg();
                userOrg.setUserId(userId);
                userOrg.setOrgId(orgId.trim());
                
                try {
                    userOrgExtendMapper.insertSelective(userOrg);
                    insertCount++;
                } catch (Exception e) {
                    log.warn("插入用户机构关联失败，用户ID: {}, 机构ID: {}, 错误: {}", 
                            userId, orgId, e.getMessage());
                }
            }
        }
        
        log.debug("批量插入用户机构关联完成，用户ID: {}, 成功插入: {} 条", userId, insertCount);
        return insertCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserOrgs(Long userId, List<String> orgIds) {
        if (userId == null) {
            return false;
        }

        try {
            // 先删除用户的所有机构关联
            deleteByUserId(userId);
            
            // 如果有新的机构ID，则批量插入
            if (orgIds != null && !orgIds.isEmpty()) {
                batchInsert(userId, orgIds);
            }
            
            log.debug("更新用户机构关联成功，用户ID: {}, 机构数量: {}", 
                    userId, orgIds != null ? orgIds.size() : 0);
            return true;
        } catch (Exception e) {
            log.error("更新用户机构关联失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean existsByUserIdAndOrgId(Long userId, String orgId) {
        if (userId == null || orgId == null || orgId.trim().isEmpty()) {
            return false;
        }

        long count = userOrgExtendMapper.count(c ->
            c.where(sysUserOrg.userId, isEqualTo(userId))
            .and(sysUserOrg.orgId, isEqualTo(orgId))
        );
        
        return count > 0;
    }

    @Override
    public List<SysUserOrg> findByUserIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = select(sysUserOrg.allColumns())
            .from(sysUserOrg)
            .where(sysUserOrg.userId, isIn(userIds))
            .orderBy(sysUserOrg.userId, sysUserOrg.orgId)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return Optional.ofNullable(userOrgExtendMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public List<SysUserOrg> findByOrgIds(List<String> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = select(sysUserOrg.allColumns())
            .from(sysUserOrg)
            .where(sysUserOrg.orgId, isIn(orgIds))
            .orderBy(sysUserOrg.orgId, sysUserOrg.userId)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return Optional.ofNullable(userOrgExtendMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }
}
