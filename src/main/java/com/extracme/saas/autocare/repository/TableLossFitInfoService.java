package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcLossFitInfo;

import java.util.List;

/**
 * 损失配件信息表数据访问服务接口
 */
public interface TableLossFitInfoService extends DefaultTableService<MtcLossFitInfo, Long> {

    /**
     * 根据任务编号查询损失配件信息列表
     * @param taskNo 任务编号
     * @return 损失配件信息列表
     */
    List<MtcLossFitInfo> selectByTaskNo(String taskNo);
    
    /**
     * 批量插入损失配件信息
     * @param fitInfoList 损失配件信息列表
     * @return 影响行数
     */
    int batchInsert(List<MtcLossFitInfo> fitInfoList);
    
    /**
     * 根据任务编号更新损失配件信息状态为无效
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int updateStatus(String taskNo);
    
    /**
     * 根据任务编号删除损失配件信息
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int deleteByTaskNo(String taskNo);
    
    /**
     * 根据任务编号和状态更新损失配件信息
     * @param record 损失配件信息记录
     * @param taskNo 任务编号
     * @param operator 操作人
     * @return 影响行数
     */
    int updateSelectiveByTaskNoAndStatus(MtcLossFitInfo record, String taskNo, String operator);
}
