package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.extend.MtcRepairItemLibraryExtendMapper;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibrary;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryListVO;
import com.extracme.saas.autocare.repository.TableRepairItemLibraryService;
import com.extracme.saas.autocare.util.SessionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.extracme.saas.autocare.mapper.base.MtcRepairItemLibraryDynamicSqlSupport.mtcRepairItemLibrary;
import static com.extracme.saas.autocare.mapper.base.MtcVehicleModelDynamicSqlSupport.mtcVehicleModel;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 维修项目库表数据访问服务实现类
 */
@Repository
public class TableRepairItemLibraryServiceImpl implements TableRepairItemLibraryService {

    @Autowired
    private MtcRepairItemLibraryExtendMapper mapper;

    @Override
    public List<RepairItemLibraryListVO> queryList(RepairItemLibraryQueryDTO queryDTO) {
        SelectStatementProvider selectStatementProvider = select(
                mtcRepairItemLibrary.id,
                mtcRepairItemLibrary.itemNo,
                mtcRepairItemLibrary.itemName,
                mtcRepairItemLibrary.itemType,
                mtcRepairItemLibrary.vehicleModelSeq,
                mtcRepairItemLibrary.hourFeeNationalMarketPrice,
                mtcRepairItemLibrary.hourFeeNationalHighestPrice,
                mtcRepairItemLibrary.materialCostNationalMarketPrice,
                mtcRepairItemLibrary.materialCostNationalHighestPrice,
                mtcRepairItemLibrary.status,
                mtcVehicleModel.vehicleModelName
                )
                .from(mtcRepairItemLibrary)
                .leftJoin(mtcVehicleModel).on(mtcRepairItemLibrary.vehicleModelSeq, equalTo(mtcVehicleModel.id))
                .where(mtcRepairItemLibrary.status, isEqualToWhenPresent(queryDTO.getStatus()))
                .and(mtcRepairItemLibrary.itemNo, isEqualToWhenPresent(queryDTO.getItemNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairItemLibrary.itemName, isLikeWhenPresent(transFuzzyQueryParam(queryDTO.getItemName())).filter(StringUtils::isNotBlank))
                .and(mtcRepairItemLibrary.itemType, isEqualToWhenPresent(queryDTO.getItemType()))
                .and(mtcRepairItemLibrary.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelSeq()))
                .orderBy(mtcRepairItemLibrary.updatedTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mapper.queryRepairItemLibraryList(selectStatementProvider);
    }

    @Override
    public List<RepairItemLibraryListVO> exportListWithPageSize(long lastId, RepairItemLibraryQueryDTO queryDTO, int pageSize) {
        SelectStatementProvider selectStatementProvider = select(
                mtcRepairItemLibrary.id,
                mtcRepairItemLibrary.itemNo,
                mtcRepairItemLibrary.itemName,
                mtcRepairItemLibrary.itemType,
                mtcRepairItemLibrary.vehicleModelSeq,
                mtcRepairItemLibrary.hourFeeNationalMarketPrice,
                mtcRepairItemLibrary.hourFeeNationalHighestPrice,
                mtcRepairItemLibrary.materialCostNationalMarketPrice,
                mtcRepairItemLibrary.materialCostNationalHighestPrice,
                mtcRepairItemLibrary.status,
                mtcVehicleModel.vehicleModelName
        )
                .from(mtcRepairItemLibrary)
                .leftJoin(mtcVehicleModel).on(mtcRepairItemLibrary.vehicleModelSeq, equalTo(mtcVehicleModel.id))
                .where(mtcRepairItemLibrary.status, isEqualToWhenPresent(queryDTO.getStatus()))
                .and(mtcRepairItemLibrary.itemNo, isEqualToWhenPresent(queryDTO.getItemNo()))
                .and(mtcRepairItemLibrary.itemName, isLikeWhenPresent(transFuzzyQueryParam(queryDTO.getItemName())))
                .and(mtcRepairItemLibrary.itemType, isEqualToWhenPresent(queryDTO.getItemType()))
                .and(mtcRepairItemLibrary.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelSeq()))
                .and(mtcRepairItemLibrary.id, isGreaterThanWhenPresent(lastId))
                .orderBy(mtcRepairItemLibrary.id)
                .limit(pageSize)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mapper.exportList(selectStatementProvider);
    }

    @Override
    public MtcRepairItemLibrary queryByItemNo(String itemNo) {
        SelectStatementProvider selectStatement = select(mtcRepairItemLibrary.allColumns())
                .from(mtcRepairItemLibrary)
                .where(mtcRepairItemLibrary.itemNo, isEqualToWhenPresent(itemNo))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<MtcRepairItemLibrary> optional = mapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public boolean checkUnique(String itemNo, String itemName, Integer itemType, Long vehicleModelSeq) {
        SelectStatementProvider selectStatementProvider = select(mtcRepairItemLibrary.itemNo)
                .from(mtcRepairItemLibrary)
                .where(mtcRepairItemLibrary.itemNo, isNotEqualToWhenPresent(itemNo))
                .and(mtcRepairItemLibrary.itemName, isEqualToWhenPresent(itemName))
                .and(mtcRepairItemLibrary.itemType, isEqualToWhenPresent(itemType))
                .and(mtcRepairItemLibrary.vehicleModelSeq, isEqualToWhenPresent(vehicleModelSeq))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        if (mapper.selectMany(selectStatementProvider).size() > 0) {
            return true;
        }
        return false;
    }

    @Override
    public MtcRepairItemLibrary queryLastDataByItemType(Integer itemType) {
        SelectStatementProvider selectStatement = select(mtcRepairItemLibrary.allColumns())
                .from(mtcRepairItemLibrary)
                .where(mtcRepairItemLibrary.itemType, isEqualToWhenPresent(itemType))
                .orderBy(mtcRepairItemLibrary.id.descending())
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<MtcRepairItemLibrary> optional = mapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public MtcRepairItemLibrary selectById(Long id) {
        return mapper.selectByPrimaryKey(id).orElse(null);
    }


    @Override
    public MtcRepairItemLibrary insert(MtcRepairItemLibrary repairItemLibrary) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        insert(repairItemLibrary, operator);
        return repairItemLibrary;
    }

    @Override
    public MtcRepairItemLibrary insert(MtcRepairItemLibrary repairItemLibrary, String operator) {
        Date now = new Date();
        repairItemLibrary.setCreatedTime(now);
        repairItemLibrary.setUpdatedTime(now);
        repairItemLibrary.setCreateBy(operator);
        repairItemLibrary.setUpdateBy(operator);
        mapper.insertSelective(repairItemLibrary);
        return repairItemLibrary;
    }


    @Override
    public int updateSelectiveById(MtcRepairItemLibrary repairItemLibrary, String operator) {
        repairItemLibrary.setUpdatedTime(new Date());
        repairItemLibrary.setUpdateBy(operator);
        return mapper.updateByPrimaryKeySelective(repairItemLibrary);
    }

    @Override
    public int updateSelectiveById(MtcRepairItemLibrary repairItemLibrary) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        return updateSelectiveById(repairItemLibrary, operator);
    }
}