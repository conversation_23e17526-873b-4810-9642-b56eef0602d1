package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.SysDataSyncLog;

import java.util.List;

/**
 * 数据同步日志表服务接口
 */
public interface TableDataSyncLogService extends DefaultTableService<SysDataSyncLog, Long> {

    /**
     * 根据批次号查询同步日志
     *
     * @param batchNo 批次号
     * @return 同步日志列表
     */
    List<SysDataSyncLog> findByBatchNo(String batchNo);

    /**
     * 根据批次号查询失败的同步日志
     *
     * @param batchNo 批次号
     * @return 失败的同步日志列表
     */
    List<SysDataSyncLog> findFailedByBatchNo(String batchNo);

    /**
     * 根据租户和表名查询同步日志
     *
     * @param tenantCode 租户编码
     * @param targetTable 目标表名
     * @param limit 最大返回条数
     * @return 同步日志列表
     */
    List<SysDataSyncLog> findByTenantAndTable(String tenantCode, String targetTable, int limit);

    /**
     * 根据同步状态查询日志
     *
     * @param syncStatus 同步状态
     * @param limit 最大返回条数
     * @return 同步日志列表
     */
    List<SysDataSyncLog> findBySyncStatus(String syncStatus, int limit);

    /**
     * 清理过期的同步日志
     *
     * @param daysToKeep 保留天数
     * @return 清理的记录数
     */
    int cleanExpiredLogs(int daysToKeep);
}
