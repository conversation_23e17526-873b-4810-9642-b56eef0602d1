package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.extend.MtcReplaceItemExtendMapper;
import com.extracme.saas.autocare.model.dto.ReplacePartItemQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcReplaceItem;
import com.extracme.saas.autocare.model.vo.ReplacePartItemListVO;
import com.extracme.saas.autocare.repository.TableReplacePartItemService;
import com.extracme.saas.autocare.util.SessionUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.extracme.saas.autocare.mapper.base.MtcOrgInfoDynamicSqlSupport.mtcOrgInfo;
import static com.extracme.saas.autocare.mapper.base.MtcPartRepairItemGroupingDynamicSqlSupport.mtcPartRepairItemGrouping;
import static com.extracme.saas.autocare.mapper.base.MtcReplaceItemDynamicSqlSupport.mtcReplaceItem;
import static com.extracme.saas.autocare.mapper.base.MtcVehicleModelDynamicSqlSupport.mtcVehicleModel;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 修理厂信息Repository实现类
 */
@Repository
public class TableReplacePartItemServiceImpl implements TableReplacePartItemService {

    @Autowired
    private MtcReplaceItemExtendMapper mapper;

    @Override
    public List<ReplacePartItemListVO> queryReplaceItemList(ReplacePartItemQueryDTO queryDTO) {
        SelectStatementProvider selectStatement = select(
                mtcReplaceItem.id,
                mtcReplaceItem.orgId,
                mtcReplaceItem.partName,
                mtcReplaceItem.groupingId,
                mtcReplaceItem.vehicleModelSeq,
                mtcReplaceItem.originalFactoryPartNo,
                mtcReplaceItem.originalFactoryPartName,
                mtcReplaceItem.sysMarketPrice,
                mtcReplaceItem.sysMajorInPrice,
                mtcReplaceItem.localMarketPrice,
                mtcReplaceItem.localMajorInPrice,
                mtcReplaceItem.separateQuotation,
                mtcReplaceItem.status,
                mtcOrgInfo.orgName,
                mtcPartRepairItemGrouping.groupingName,
                mtcVehicleModel.vehicleModelName
                )
                .from(mtcReplaceItem,"t1")
                .leftJoin(mtcOrgInfo).on(mtcReplaceItem.orgId, equalTo(mtcOrgInfo.orgId))
                .leftJoin(mtcPartRepairItemGrouping).on(mtcReplaceItem.groupingId, equalTo(mtcPartRepairItemGrouping.groupingId))
                .leftJoin(mtcVehicleModel).on(mtcReplaceItem.vehicleModelSeq, equalTo(mtcVehicleModel.id))
                .where()
                .and(mtcPartRepairItemGrouping.groupingType, isEqualTo(1))
                .and(mtcReplaceItem.orgId, isEqualToWhenPresent(queryDTO.getOrgId()))
                .and(mtcReplaceItem.partName, isLikeWhenPresent(transFuzzyQueryParam(queryDTO.getPartName())))
                .and(mtcReplaceItem.groupingId, isEqualToWhenPresent(queryDTO.getGroupingId()))
                .and(mtcReplaceItem.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelSeq()))
                .and(mtcReplaceItem.status, isEqualToWhenPresent(queryDTO.getStatus()))
                .orderBy(sortColumn("t1.updated_time").descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mapper.queryReplaceItemList(selectStatement);
    }

    @Override
    public boolean checkExistPartItemName(String orgId,Long vehicleModelSeq, String partItemName) {
        SelectStatementProvider selectStatement = select(mtcReplaceItem.allColumns())
                .from(mtcReplaceItem)
                .where()
                .and(mtcReplaceItem.orgId, isEqualTo(orgId))
                .and(mtcReplaceItem.vehicleModelSeq, isEqualTo(vehicleModelSeq))
                .and(mtcReplaceItem.partName, isEqualTo(partItemName))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<MtcReplaceItem> optional = mapper.selectOne(selectStatement);
        return optional.isPresent();
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return mapper.deleteByPrimaryKey(id);
    }


    @Override
    public MtcReplaceItem selectById(Long id) {
        Optional<MtcReplaceItem> optional = mapper.selectByPrimaryKey(id);
        return optional.orElse(null);
    }

    @Override
    public MtcReplaceItem insert(MtcReplaceItem mtcReplaceItem) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(mtcReplaceItem, operator);
    }

    @Override
    public MtcReplaceItem insert(MtcReplaceItem mtcReplaceItem, String operator) {
        if (mtcReplaceItem == null) {
            return null;
        }
        Date now = new Date();
        mtcReplaceItem.setCreatedTime(now);
        mtcReplaceItem.setCreateBy(operator);
        mtcReplaceItem.setUpdatedTime(now);
        mtcReplaceItem.setUpdateBy(operator);
        mapper.insertSelective(mtcReplaceItem);
        return mtcReplaceItem;
    }

    @Override
    public int updateSelectiveById(MtcReplaceItem mtcReplaceItem) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(mtcReplaceItem, operator);
    }

    @Override
    public int updateSelectiveById(MtcReplaceItem mtcReplaceItem, String operator) {
        if (mtcReplaceItem == null) {
            return 0;
        }
        Date now = new Date();
        mtcReplaceItem.setUpdatedTime(now);
        mtcReplaceItem.setUpdateBy(operator);
        return mapper.updateByPrimaryKeySelective(mtcReplaceItem);
    }
}