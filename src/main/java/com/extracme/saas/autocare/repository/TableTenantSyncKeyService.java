package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.SysTenantSyncKey;

import java.util.List;

/**
 * 租户同步密钥管理表服务接口
 */
public interface TableTenantSyncKeyService extends DefaultTableService<SysTenantSyncKey, Long> {

    /**
     * 根据租户编码查询同步密钥
     *
     * @param tenantCode 租户编码
     * @return 同步密钥
     */
    SysTenantSyncKey findByTenantCode(String tenantCode);

    /**
     * 根据租户ID查询同步密钥
     *
     * @param tenantId 租户ID
     * @return 同步密钥
     */
    SysTenantSyncKey findByTenantId(Long tenantId);

    /**
     * 根据同步密钥查询租户信息
     *
     * @param syncKey 同步密钥
     * @return 租户同步密钥信息
     */
    SysTenantSyncKey findBySyncKey(String syncKey);

    /**
     * 查询所有租户同步密钥
     *
     * @return 所有租户同步密钥列表
     */
    List<SysTenantSyncKey> findAll();

    /**
     * 查询所有有效的租户密钥
     *
     * @return 有效的租户密钥列表
     */
    List<SysTenantSyncKey> findAllActiveKeys();

    /**
     * 根据密钥状态查询
     *
     * @param keyStatus 密钥状态
     * @return 密钥列表
     */
    List<SysTenantSyncKey> findByKeyStatus(Integer keyStatus);

    /**
     * 更新密钥使用统计
     *
     * @param tenantCode 租户编码
     * @return 是否更新成功
     */
    boolean updateUsageStats(String tenantCode);

    /**
     * 禁用租户密钥
     *
     * @param tenantCode 租户编码
     * @return 是否禁用成功
     */
    boolean disableKey(String tenantCode);

    /**
     * 启用租户密钥
     *
     * @param tenantCode 租户编码
     * @return 是否启用成功
     */
    boolean enableKey(String tenantCode);
}
