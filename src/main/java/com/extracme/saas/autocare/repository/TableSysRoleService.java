package com.extracme.saas.autocare.repository;

import java.util.List;
import com.extracme.saas.autocare.model.entity.SysRole;

/**
 * 系统角色Repository接口
 */
public interface TableSysRoleService extends DefaultTableService<SysRole, Long> {
    
    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> findByUserId(Long userId);
    
    /**
     * 检查用户是否有指定角色
     *
     * @param userId 用户ID
     * @param roleCode 角色编码
     * @return 是否有角色
     */
    boolean hasRole(Long userId, String roleCode);
} 