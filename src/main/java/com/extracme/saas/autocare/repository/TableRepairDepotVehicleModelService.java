package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcRepairDepotVehicleModelInfo;

import java.util.List;

/**
 * 维修厂可修理车型表接口
 */
public interface TableRepairDepotVehicleModelService extends DefaultTableService<MtcRepairDepotVehicleModelInfo, Long> {

    /**
     * 根据维修厂id查询维修厂可修理车型
     * @param repairDepotId
     * @return
     */
    List<MtcRepairDepotVehicleModelInfo> queryVehicleModelList(String repairDepotId);

    /**
     * 批量插入
     * @param vehicleModelList
     * @return
     */
    int batchInsert(List<MtcRepairDepotVehicleModelInfo> vehicleModelList);

    /**
     * 根据维修厂id删除维修厂可修理车型
     * @param repairDepotId
     * @return
     */
    int deleteByRepairDepotId(String repairDepotId);

} 