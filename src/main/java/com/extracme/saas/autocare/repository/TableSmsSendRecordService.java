package com.extracme.saas.autocare.repository;

import java.util.Date;
import java.util.List;

import com.extracme.saas.autocare.model.entity.SmsSendRecord;

/**
 * 短信发送记录表数据访问服务接口
 */
public interface TableSmsSendRecordService extends DefaultTableService<SmsSendRecord, Long> {
    
    /**
     * 保存发送记录
     *
     * @param record 发送记录
     * @return 记录ID
     */
    Long save(SmsSendRecord record);
    
    /**
     * 根据手机号和类型查询一段时间内的发送次数
     *
     * @param phoneNumber 手机号
     * @param type 验证码类型
     * @param startTime 开始时间
     * @return 发送次数
     */
    int countByPhoneAndTime(String phoneNumber, String type, Date startTime);
    
    /**
     * 根据IP地址和类型查询一段时间内的发送次数
     *
     * @param ipAddress IP地址
     * @param type 验证码类型
     * @param startTime 开始时间
     * @return 发送次数
     */
    int countByIpAndTime(String ipAddress, String type, Date startTime);
    
    /**
     * 查询最后一次发送时间
     *
     * @param phoneNumber 手机号
     * @param type 验证码类型
     * @return 最后发送时间
     */
    Date findLastSendTime(String phoneNumber, String type);
    
    /**
     * 删除指定天数前的发送记录
     *
     * @param days 天数
     * @return 删除数量
     */
    int deleteOldRecords(int days);
    
    /**
     * 查询指定时间段内的发送记录
     *
     * @param phoneNumber 手机号
     * @param type 验证码类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发送记录列表
     */
    List<SmsSendRecord> findBetweenTime(String phoneNumber, String type, Date startTime, Date endTime);
} 