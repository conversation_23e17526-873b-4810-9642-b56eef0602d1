package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.entity.ActivityInstance;

import java.util.List;
import java.util.Map;

/**
 * 活动实例记录表数据访问服务接口
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
public interface TableActivityInstanceService extends DefaultTableService<ActivityInstance, Long> {

    /**
     * 根据流程实例ID查询活动实例记录列表
     *
     * @param instanceId 流程实例ID
     * @return 活动实例记录列表
     */
    List<ActivityInstance> selectByInstanceId(Long instanceId);

    /**
     * 根据流程实例ID和目标活动节点编码查询活动实例记录
     *
     * @param instanceId 流程实例ID
     * @param toActivityCode 目标活动节点编码
     * @return 活动实例记录
     */
    ActivityInstance selectByInstanceIdAndToActivityCode(Long instanceId, String toActivityCode);

    /**
     * 根据流程实例ID和当前状态编码查询活动实例记录列表
     *
     * @param instanceId 流程实例ID
     * @param currentStatusCode 当前状态编码
     * @return 活动实例记录列表
     */
    List<ActivityInstance> selectByInstanceIdAndStatusCode(Long instanceId, String currentStatusCode);

    /**
     * 更新活动实例记录的结束时间和耗时
     *
     * @param id 活动实例记录ID
     * @param endTime 结束时间
     * @param duration 耗时（秒）
     * @return 影响行数
     */
    int updateEndTimeAndDuration(Long id, java.util.Date endTime, Integer duration);

    /**
     * 重置活动实例记录的结束时间和耗时为NULL
     *
     * @param id 活动实例记录ID
     * @return 影响行数
     */
    int resetEndTimeAndDuration(Long id);

    /**
     * 更新活动实例记录的状态
     *
     * @param id 活动实例记录ID
     * @param statusCode 状态编码
     * @return 影响行数
     */
    int updateStatusCode(Long id, String statusCode);

    /**
     * 根据目标活动节点编码查询活动实例，并按状态码分组统计数量
     * @param repairTaskListQueryDTO
     * @return 状态码及对应的数量Map，key为状态码，value为数量
     */
    Map<String, Long> countByToActivityCodeGroupByStatusCode(RepairTaskListQueryDTO repairTaskListQueryDTO);

    /**
     * 根据ID删除活动实例记录
     *
     * @param id 活动实例记录ID
     * @return 影响行数
     */
    int deleteById(Long id);
}
