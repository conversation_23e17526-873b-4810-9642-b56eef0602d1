package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.ActivityStatusTransition;

import java.util.List;

/**
 * 节点状态转换规则表数据访问服务接口
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
public interface TableActivityStatusTransitionService {

    /**
     * 根据ID查询节点状态转换规则
     *
     * @param id 转换规则ID
     * @return 节点状态转换规则
     */
    ActivityStatusTransition selectById(Long id);

    /**
     * 根据活动节点转换规则ID查询节点状态转换规则列表
     *
     * @param activityTransitionId 活动节点转换规则ID
     * @return 节点状态转换规则列表
     */
    List<ActivityStatusTransition> selectByActivityTransitionId(Long activityTransitionId);

    /**
     * 插入节点状态转换规则
     *
     * @param activityStatusTransition 节点状态转换规则
     * @return 影响行数
     */
    int insert(ActivityStatusTransition activityStatusTransition);

    /**
     * 插入节点状态转换规则（带操作人）
     *
     * @param activityStatusTransition 节点状态转换规则
     * @param operator 操作人
     * @return 影响行数
     */
    int insert(ActivityStatusTransition activityStatusTransition, String operator);

    /**
     * 批量插入节点状态转换规则
     *
     * @param activityStatusTransitions 节点状态转换规则列表
     * @param operator 操作人
     * @return 影响行数
     */
    int batchInsert(List<ActivityStatusTransition> activityStatusTransitions, String operator);

    /**
     * 更新节点状态转换规则（选择性更新，忽略null值字段）
     *
     * @param activityStatusTransition 节点状态转换规则
     * @return 影响行数
     */
    int update(ActivityStatusTransition activityStatusTransition);

    /**
     * 更新节点状态转换规则（选择性更新，忽略null值字段，带操作人）
     *
     * @param activityStatusTransition 节点状态转换规则
     * @param operator 操作人
     * @return 影响行数
     */
    int update(ActivityStatusTransition activityStatusTransition, String operator);

    /**
     * 更新节点状态转换规则（更新所有字段，包括null值字段）
     *
     * @param activityStatusTransition 节点状态转换规则
     * @return 影响行数
     */
    int updateByPrimaryKey(ActivityStatusTransition activityStatusTransition);

    /**
     * 更新节点状态转换规则（更新所有字段，包括null值字段，带操作人）
     *
     * @param activityStatusTransition 节点状态转换规则
     * @param operator 操作人
     * @return 影响行数
     */
    int updateByPrimaryKey(ActivityStatusTransition activityStatusTransition, String operator);

    /**
     * 删除节点状态转换规则
     *
     * @param id 转换规则ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据活动节点转换规则ID删除节点状态转换规则
     *
     * @param activityTransitionId 活动节点转换规则ID
     * @return 影响行数
     */
    int deleteByActivityTransitionId(Long activityTransitionId);

    /**
     * 根据活动节点转换规则ID列表批量查询节点状态转换规则
     *
     * @param activityTransitionIds 活动节点转换规则ID列表
     * @return 节点状态转换规则列表
     */
    List<ActivityStatusTransition> selectByActivityTransitionIds(List<Long> activityTransitionIds);
}
