package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.extend.SysDataSyncLogExtendMapper;
import com.extracme.saas.autocare.model.entity.SysDataSyncLog;
import com.extracme.saas.autocare.repository.TableDataSyncLogService;
import com.extracme.saas.autocare.util.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.extracme.saas.autocare.mapper.base.SysDataSyncLogDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 数据同步日志表服务实现类
 */
@Slf4j
@Repository
public class TableDataSyncLogServiceImpl implements TableDataSyncLogService {

    @Autowired
    private SysDataSyncLogExtendMapper sysDataSyncLogMapper;

    @Override
    public SysDataSyncLog insert(SysDataSyncLog record) {
        String operator = SessionUtils.getUsername();
        return insert(record, operator);
    }

    @Override
    public SysDataSyncLog insert(SysDataSyncLog record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreateTime(now);
        record.setCreateBy(operator);
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        sysDataSyncLogMapper.insertSelective(record);
        return record;
    }

    @Override
    public SysDataSyncLog selectById(Long id) {
        if (id == null) {
            return null;
        }
        return sysDataSyncLogMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public int updateSelectiveById(SysDataSyncLog record) {
        String operator = SessionUtils.getUsername();
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(SysDataSyncLog record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        return sysDataSyncLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<SysDataSyncLog> findByBatchNo(String batchNoParam) {
        if (!StringUtils.hasText(batchNoParam)) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = select(sysDataSyncLog.allColumns())
            .from(sysDataSyncLog)
            .where(batchNo, isEqualTo(batchNoParam))
            .orderBy(createTime)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return Optional.ofNullable(sysDataSyncLogMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public List<SysDataSyncLog> findFailedByBatchNo(String batchNo) {
        if (!StringUtils.hasText(batchNo)) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = select(sysDataSyncLog.allColumns())
            .from(sysDataSyncLog)
            .where(sysDataSyncLog.batchNo, isEqualTo(batchNo))
            .and(sysDataSyncLog.syncStatus, isEqualTo("FAILED"))
            .orderBy(sysDataSyncLog.createTime)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return Optional.ofNullable(sysDataSyncLogMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public List<SysDataSyncLog> findByTenantAndTable(String tenantCode, String targetTable, int limit) {
        // 注意：由于表结构简化，tenantCode参数已不再使用，仅根据targetTable查询
        SelectStatementProvider selectStatement = select(sysDataSyncLog.allColumns())
            .from(sysDataSyncLog)
            .where(sysDataSyncLog.targetTable, isEqualToWhenPresent(targetTable))
            .orderBy(createTime.descending())
            .limit(limit)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return Optional.ofNullable(sysDataSyncLogMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public List<SysDataSyncLog> findBySyncStatus(String syncStatus, int limit) {
        if (!StringUtils.hasText(syncStatus)) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = select(sysDataSyncLog.allColumns())
            .from(sysDataSyncLog)
            .where(sysDataSyncLog.syncStatus, isEqualTo(syncStatus))
            .orderBy(createTime.descending())
            .limit(limit)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return Optional.ofNullable(sysDataSyncLogMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public int cleanExpiredLogs(int daysToKeep) {
        if (daysToKeep <= 0) {
            log.warn("保留天数必须大于0，当前值：{}", daysToKeep);
            return 0;
        }

        try {
            // 计算过期时间
            Date expireDate = new Date(System.currentTimeMillis() - (long) daysToKeep * 24 * 60 * 60 * 1000);

            DeleteStatementProvider deleteStatement = deleteFrom(sysDataSyncLog)
                .where(createTime, isLessThan(expireDate))
                .build()
                .render(RenderingStrategies.MYBATIS3);

            int deletedCount = sysDataSyncLogMapper.delete(deleteStatement);
            log.info("清理过期同步日志完成，删除记录数：{}，过期时间：{}", deletedCount, expireDate);
            
            return deletedCount;

        } catch (Exception e) {
            log.error("清理过期同步日志失败：{}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 根据主键删除记录
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return sysDataSyncLogMapper.deleteByPrimaryKey(id);
    }
}
