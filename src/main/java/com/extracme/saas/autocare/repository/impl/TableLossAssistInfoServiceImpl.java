package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcLossAssistInfoDynamicSqlSupport.mtcLossAssistInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcLossAssistInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcLossAssistInfo;
import com.extracme.saas.autocare.repository.TableLossAssistInfoService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 损失辅料信息表数据访问服务实现
 */
@Repository
public class TableLossAssistInfoServiceImpl implements TableLossAssistInfoService {

    @Autowired
    private MtcLossAssistInfoExtendMapper mtcLossAssistInfoMapper;

    @Override
    public MtcLossAssistInfo insert(MtcLossAssistInfo record) {
        // 调用带操作人参数的方法
        return insert(record, "");
    }

    @Override
    public MtcLossAssistInfo insert(MtcLossAssistInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcLossAssistInfoMapper.insertSelective(record);
        return record;
    }

    @Override
    public MtcLossAssistInfo selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcLossAssistInfoMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<MtcLossAssistInfo> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(mtcLossAssistInfo.taskNo, isEqualTo(taskNo))
                .and(mtcLossAssistInfo.status, isEqualTo(1))
                .orderBy(id.descending());
        return mtcLossAssistInfoMapper.select(completer);
    }

    @Override
    public int updateSelectiveById(MtcLossAssistInfo record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcLossAssistInfo record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return mtcLossAssistInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchInsert(List<MtcLossAssistInfo> assistInfoList) {
        if (assistInfoList == null || assistInfoList.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (MtcLossAssistInfo assistInfo : assistInfoList) {
            count += mtcLossAssistInfoMapper.insertSelective(assistInfo);
        }
        return count;
    }

    @Override
    public int updateStatus(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }

        Date now = new Date();
        String operator = null != SessionUtils.getLoginUser() ? SessionUtils.getLoginUser().getUsername() : "精友回调";

        return mtcLossAssistInfoMapper.update(c -> c.set(mtcLossAssistInfo.status).equalTo(0)
                .set(mtcLossAssistInfo.updatedTime).equalTo(now)
                .set(mtcLossAssistInfo.updateBy).equalTo(operator)
                .where(mtcLossAssistInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int deleteByTaskNo(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }

        return mtcLossAssistInfoMapper.delete(c -> c.where(mtcLossAssistInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int updateSelectiveByTaskNoAndStatus(MtcLossAssistInfo record, String taskNo, String operator) {
        if (record == null || StringUtils.isBlank(taskNo)) {
            return 0;
        }

        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);

        return mtcLossAssistInfoMapper.updateSelectiveByTaskNoAndStatus(record);
    }
}
