package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcCollisionParts;

import java.util.List;

/**
 * 碰撞部件信息表数据访问服务接口
 */
public interface TableCollisionPartsService extends DefaultTableService<MtcCollisionParts, Long> {

    /**
     * 根据任务编号查询碰撞部件信息列表
     * @param taskNo 任务编号
     * @return 碰撞部件信息列表
     */
    List<MtcCollisionParts> selectByTaskNo(String taskNo);
    
    /**
     * 批量插入碰撞部件信息
     * @param collisionPartsList 碰撞部件信息列表
     * @return 影响行数
     */
    int batchInsert(List<MtcCollisionParts> collisionPartsList);
    
    /**
     * 根据任务编号更新碰撞部件信息状态为无效
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int updateStatus(String taskNo);
    
    /**
     * 根据任务编号删除碰撞部件信息
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int deleteByTaskNo(String taskNo);
}
