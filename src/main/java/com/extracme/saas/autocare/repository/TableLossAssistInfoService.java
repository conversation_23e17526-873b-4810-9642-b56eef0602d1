package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcLossAssistInfo;

import java.util.List;

/**
 * 损失辅料信息表数据访问服务接口
 */
public interface TableLossAssistInfoService extends DefaultTableService<MtcLossAssistInfo, Long> {

    /**
     * 根据任务编号查询损失辅料信息列表
     * @param taskNo 任务编号
     * @return 损失辅料信息列表
     */
    List<MtcLossAssistInfo> selectByTaskNo(String taskNo);
    
    /**
     * 批量插入损失辅料信息
     * @param assistInfoList 损失辅料信息列表
     * @return 影响行数
     */
    int batchInsert(List<MtcLossAssistInfo> assistInfoList);
    
    /**
     * 根据任务编号更新损失辅料信息状态为无效
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int updateStatus(String taskNo);
    
    /**
     * 根据任务编号删除损失辅料信息
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int deleteByTaskNo(String taskNo);
    
    /**
     * 根据任务编号和状态更新损失辅料信息
     * @param record 损失辅料信息记录
     * @param taskNo 任务编号
     * @param operator 操作人
     * @return 影响行数
     */
    int updateSelectiveByTaskNoAndStatus(MtcLossAssistInfo record, String taskNo, String operator);
}
