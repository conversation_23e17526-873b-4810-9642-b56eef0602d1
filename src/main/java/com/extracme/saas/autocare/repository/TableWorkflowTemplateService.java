package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.WorkflowTemplate;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateQueryDTO;

import java.util.List;

/**
 * 工作流模板表数据访问服务接口
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
public interface TableWorkflowTemplateService extends DefaultTableService<WorkflowTemplate, Long> {


    /**
     * 根据ID删除工作流模板
     *
     * @param id 模板ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 高级搜索工作流模板列表
     * 支持模糊查询和多条件组合查询
     *
     * @param queryDTO 查询条件对象
     * @return 工作流模板列表
     */
    List<WorkflowTemplate> findByCondition(WorkflowTemplateQueryDTO queryDTO);
} 