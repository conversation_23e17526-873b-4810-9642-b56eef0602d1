package com.extracme.saas.autocare.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.extracme.saas.autocare.model.entity.SysVerificationCode;

/**
 * 验证码表数据访问服务接口
 */
public interface TableSysVerificationCodeService extends DefaultTableService<SysVerificationCode, Long> {
    
    /**
     * 根据手机号和类型查询最新的未使用验证码
     *
     * @param mobile 手机号
     * @param type 验证码类型
     * @return 验证码信息
     */
    Optional<SysVerificationCode> findLatestUnused(String mobile, String type);
    
    /**
     * 更新验证码状态
     *
     * @param id 验证码ID
     * @param status 状态（0-未使用，1-已使用，2-已过期）
     * @return 更新结果
     */
    boolean updateStatus(Long id, Boolean status);
    
    /**
     * 查询所有过期但未标记为过期的验证码
     *
     * @param now 当前时间
     * @return 过期验证码列表
     */
    List<SysVerificationCode> findExpiredButNotMarked(Date now);
    
    /**
     * 批量标记过期验证码
     *
     * @param now 当前时间
     * @return 更新数量
     */
    int markExpiredCodes(Date now);
    
    /**
     * 删除指定天数前的验证码
     *
     * @param days 天数
     * @return 删除数量
     */
    int deleteOldCodes(int days);
    
    /**
     * 查询最近一段时间内用户发送验证码的次数
     *
     * @param mobile 手机号
     * @param type 验证码类型
     * @param startTime 开始时间
     * @return 发送次数
     */
    int countRecentSendTimes(String mobile, String type, Date startTime);
    
    /**
     * 增加验证失败次数
     *
     * @param id 验证码ID
     * @return 更新结果
     */
    boolean incrementFailCount(Long id);
} 