package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.extend.MtcRepairDepotVehicleModelExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotVehicleModelInfo;
import com.extracme.saas.autocare.repository.TableRepairDepotVehicleModelService;
import org.apache.commons.collections4.CollectionUtils;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.extracme.saas.autocare.mapper.base.MtcRepairDepotVehicleModelInfoDynamicSqlSupport.mtcRepairDepotVehicleModelInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 维修厂可修理车型数据访问服务实现类
 */
@Repository
public class TableRepairDepotVehicleModelServiceImpl implements TableRepairDepotVehicleModelService {

    @Autowired
    private MtcRepairDepotVehicleModelExtendMapper mapper;

    @Override
    public List<MtcRepairDepotVehicleModelInfo> queryVehicleModelList(String repairDepotId) {
        if (repairDepotId != null){
            SelectStatementProvider selectStatement = select(mtcRepairDepotVehicleModelInfo.allColumns())
                    .from(mtcRepairDepotVehicleModelInfo)
                    .where()
                    .and(mtcRepairDepotVehicleModelInfo.repairDepotId, isEqualTo(repairDepotId))
                    .orderBy(mtcRepairDepotVehicleModelInfo.updatedTime.descending())
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            return mapper.selectMany(selectStatement);
        }
        return Collections.emptyList();
    }

    @Override
    public int batchInsert(List<MtcRepairDepotVehicleModelInfo> vehicleModelList) {
        if (CollectionUtils.isEmpty(vehicleModelList)){
            return 0;
        }
        Date now = new Date();
        for (MtcRepairDepotVehicleModelInfo vehicleModel : vehicleModelList) {
            vehicleModel.setCreatedTime(now);
            vehicleModel.setUpdatedTime(now);
            mapper.insertSelective(vehicleModel);
        }
        return 1;
    }

    @Override
    public int deleteByRepairDepotId(String repairDepotId) {
        DeleteStatementProvider render = deleteFrom(mtcRepairDepotVehicleModelInfo).where()
                .and(mtcRepairDepotVehicleModelInfo.repairDepotId, isEqualTo(repairDepotId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mapper.delete(render);
    }
}
