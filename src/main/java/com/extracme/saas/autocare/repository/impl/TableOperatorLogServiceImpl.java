package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcOperatorLogDynamicSqlSupport.mtcOperatorLog;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcOperatorLogExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 操作日志数据访问服务实现类
 */
@Repository
public class TableOperatorLogServiceImpl implements TableOperatorLogService {

    @Autowired
    private MtcOperatorLogExtendMapper mtcOperatorLogMapper;

    @Override
    public List<MtcOperatorLog> queryOperatorLog(Long recordId, String tableName) {
        if (recordId == null || tableName == null || tableName.isEmpty()) {
            return Collections.emptyList();
        }

        // 构建查询条件
        SelectStatementProvider selectStatement = select(mtcOperatorLog.allColumns())
            .from(mtcOperatorLog)
            .where(mtcOperatorLog.recordId, isEqualTo(recordId))
            .and(mtcOperatorLog.tableName, isEqualTo(tableName))
            .orderBy(mtcOperatorLog.createdTime.descending(), mtcOperatorLog.id.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);
        List<MtcOperatorLog> logs = mtcOperatorLogMapper.selectMany(selectStatement);
        return logs;
    }

    @Override
    public int insertSelective(MtcOperatorLog log) {
        if (log == null) {
            return 0;
        }
        Date now = new Date();
        log.setCreateBy(SessionUtils.getNickname());
        log.setCreatedTime(now);
        log.setUpdateBy(SessionUtils.getUsername());
        log.setUpdatedTime(now);
        return mtcOperatorLogMapper.insertSelective(log);
    }

    @Override
    public List<MtcOperatorLog> selectByRecordId(Long recordId) {
        if (recordId == null) {
            return Collections.emptyList();
        }

        // 构建查询条件
        SelectStatementProvider selectStatement = select(mtcOperatorLog.allColumns())
            .from(mtcOperatorLog)
            .where(mtcOperatorLog.recordId, isEqualTo(recordId))
            .orderBy(mtcOperatorLog.createdTime.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return mtcOperatorLogMapper.selectMany(selectStatement);
    }

    @Override
    public List<MtcOperatorLog> selectByRecordIdAndTache(Long recordId, String currentActivityCode) {
        if (recordId == null || currentActivityCode == null) {
            return Collections.emptyList();
        }

        // 构建查询条件
        SelectStatementProvider selectStatement = select(mtcOperatorLog.allColumns())
            .from(mtcOperatorLog)
            .where(mtcOperatorLog.recordId, isEqualTo(recordId))
            .and(mtcOperatorLog.currentActivityCode, isEqualTo(currentActivityCode))
            .orderBy(mtcOperatorLog.createdTime.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return mtcOperatorLogMapper.selectMany(selectStatement);
    }

    @Override
    public int updateByPrimaryKeySelective(MtcOperatorLog log) {
        if (log == null || log.getId() == null) {
            return 0;
        }
        return mtcOperatorLogMapper.updateByPrimaryKeySelective(log);
    }
}
