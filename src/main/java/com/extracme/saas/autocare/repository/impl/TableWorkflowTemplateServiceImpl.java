package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.WorkflowTemplateDynamicSqlSupport.workflowTemplate;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.WorkflowTemplateMapper;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateQueryDTO;
import com.extracme.saas.autocare.model.entity.WorkflowTemplate;
import com.extracme.saas.autocare.repository.TableWorkflowTemplateService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 工作流模板表数据访问服务实现类
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Repository
public class TableWorkflowTemplateServiceImpl implements TableWorkflowTemplateService {

    @Autowired
    private WorkflowTemplateMapper workflowTemplateMapper;

    @Override
    public WorkflowTemplate insert(WorkflowTemplate template) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(template, operator);
    }

    @Override
    public WorkflowTemplate insert(WorkflowTemplate template, String operator) {
        Date now = new Date();
        template.setCreateTime(now);
        template.setCreateBy(operator);
        template.setUpdateTime(now);
        template.setUpdateBy(operator);

        workflowTemplateMapper.insertSelective(template);
        return template;
    }

    @Override
    public int updateSelectiveById(WorkflowTemplate template) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(template, operator);
    }

    @Override
    public int updateSelectiveById(WorkflowTemplate template, String operator) {
        Date now = new Date();
        template.setUpdateTime(now);
        template.setUpdateBy(operator);

        return workflowTemplateMapper.updateByPrimaryKeySelective(template);
    }

    @Override
    public WorkflowTemplate selectById(Long id) {
        return workflowTemplateMapper.selectByPrimaryKey(id).orElse(null);
    }


    @Override
    public int deleteById(Long id) {
        return workflowTemplateMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<WorkflowTemplate> findByCondition(WorkflowTemplateQueryDTO queryDTO) {
        if (queryDTO == null) {
            return Collections.emptyList();
        }

        // 使用SelectStatementProvider构建查询
        SelectStatementProvider selectStatement = select(workflowTemplate.allColumns())
            .from(workflowTemplate)
            .where(workflowTemplate.workflowName, isLikeWhenPresent(transFuzzyQueryParam(queryDTO.getWorkflowName())))
            .and(workflowTemplate.taskType, isEqualToWhenPresent(queryDTO.getTaskType()))
            .and(workflowTemplate.repairFactoryType, isEqualToWhenPresent(queryDTO.getRepairFactoryType()))
            .and(workflowTemplate.subProductLine, isEqualToWhenPresent(queryDTO.getSubProductLine()))
            .and(workflowTemplate.partsLibraryType, isEqualToWhenPresent(queryDTO.getPartsLibraryType()))
            .and(workflowTemplate.isActive, isEqualToWhenPresent(queryDTO.getIsActive()))
            .and(workflowTemplate.tenantId, isEqualToWhenPresent(queryDTO.getTenantId()))
            .orderBy(workflowTemplate.createTime.descending(), workflowTemplate.id.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return workflowTemplateMapper.selectMany(selectStatement);
    }

}