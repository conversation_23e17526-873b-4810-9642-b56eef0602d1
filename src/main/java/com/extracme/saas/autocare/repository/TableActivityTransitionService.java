package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.ActivityTransition;

import java.util.List;

/**
 * 活动节点转换规则表数据访问服务接口
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
public interface TableActivityTransitionService {

    /**
     * 根据ID查询活动节点转换规则
     *
     * @param id 转换规则ID
     * @return 活动节点转换规则
     */
    ActivityTransition selectById(Long id);

    /**
     * 根据工作流模板ID查询活动节点转换规则列表
     *
     * @param workflowId 工作流模板ID
     * @return 活动节点转换规则列表
     */
    List<ActivityTransition> selectByWorkflowId(Long workflowId);

    /**
     * 根据工作流模板ID和起始节点编码查询活动节点转换规则列表
     *
     * @param workflowId 工作流模板ID
     * @param fromActivityCode 起始节点编码
     * @return 活动节点转换规则列表
     */
    List<ActivityTransition> selectByWorkflowIdAndFromActivityCode(Long workflowId, String fromActivityCode);

    /**
     * 根据工作流模板ID、起始节点编码和触发事件查询活动节点转换规则
     *
     * @param workflowId 工作流模板ID
     * @param fromActivityCode 起始节点编码
     * @param triggerEvent 触发事件
     * @return 活动节点转换规则
     */
    ActivityTransition selectByWorkflowIdAndFromActivityCodeAndTriggerEvent(Long workflowId, String fromActivityCode, String triggerEvent);

    /**
     * 插入活动节点转换规则
     *
     * @param activityTransition 活动节点转换规则
     * @return 影响行数
     */
    int insert(ActivityTransition activityTransition);

    /**
     * 插入活动节点转换规则（带操作人）
     *
     * @param activityTransition 活动节点转换规则
     * @param operator 操作人
     * @return 影响行数
     */
    int insert(ActivityTransition activityTransition, String operator);

    /**
     * 更新活动节点转换规则（选择性更新，忽略null值字段）
     *
     * @param activityTransition 活动节点转换规则
     * @return 影响行数
     */
    int update(ActivityTransition activityTransition);

    /**
     * 更新活动节点转换规则（更新所有字段，包括null值字段）
     *
     * @param activityTransition 活动节点转换规则
     * @return 影响行数
     */
    int updateByPrimaryKey(ActivityTransition activityTransition);

    /**
     * 更新活动节点转换规则（更新所有字段，包括null值字段，带操作人）
     *
     * @param activityTransition 活动节点转换规则
     * @param operator 操作人
     * @return 影响行数
     */
    int updateByPrimaryKey(ActivityTransition activityTransition, String operator);

    /**
     * 删除活动节点转换规则
     *
     * @param id 转换规则ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据工作流模板ID删除活动节点转换规则
     *
     * @param workflowId 工作流模板ID
     * @return 影响行数
     */
    int deleteByWorkflowId(Long workflowId);
}
