package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.dto.VehicleLeavingFactoryQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTaskLeavingFactory;
import com.extracme.saas.autocare.model.vo.VehicleLeavingFactoryResultVO;

import java.util.List;

/**
 * 维修任务出厂表数据访问服务接口
 */
public interface TableRepairTaskLeavingFactoryService extends DefaultTableService<MtcRepairTaskLeavingFactory, Long> {

    /**
     * 根据车架号查询维修任务出厂信息列表
     * 
     * @param vin 车架号
     * @return 维修任务出厂信息列表
     */
    List<MtcRepairTaskLeavingFactory> selectByVin(String vin);

    /**
     * 根据修理厂ID查询维修任务出厂信息列表
     * 
     * @param repairDepotId 修理厂ID
     * @return 维修任务出厂信息列表
     */
    List<MtcRepairTaskLeavingFactory> selectByRepairDepotId(String repairDepotId);

    /**
     * 更新维修任务出厂状态
     * 
     * @param taskNo        任务编号
     * @param leavingStatus 出厂状态
     * @param operator      操作人
     * @return 影响行数
     */
    int updateLeavingStatus(String taskNo, Integer leavingStatus, String operator);

    /**
     * 查询维修任务出厂信息列表
     * 
     * @param queryDTO 查询条件
     * @return 维修任务出厂信息列表
     */
    List<VehicleLeavingFactoryResultVO> selectLeavingFactoryList(VehicleLeavingFactoryQueryDTO queryDTO);

    /**
     * 查询维修任务出厂信息列表
     *
     * @param queryDTO 查询条件
     * @return 维修任务出厂信息列表
     */
    List<VehicleLeavingFactoryResultVO> exportLeavingFactoryList(VehicleLeavingFactoryQueryDTO queryDTO, long lastId, int pageSize);

    /**
     * 根据任务编号查询维修任务出厂信息
     * 
     * @param taskNo 任务编号
     * @return 维修任务出厂信息
     */
    List<VehicleLeavingFactoryResultVO> selectByTaskNo(String taskNo);

    /**
     * 根据任务编号查询维修任务出厂信息
     * 
     * @param taskNo 任务编号
     * @return 维修任务出厂信息
     */
    MtcRepairTaskLeavingFactory selectLatestRecordByTaskNo(String taskNo);
    
    /**
     * 查询维修任务出厂数量
     * 
     * @param queryDTO 查询条件
     * @param taskType 任务类型
     * @return 维修任务出厂信息列表
     */
    long selectLeavingFactoryCount(VehicleLeavingFactoryQueryDTO queryDTO, Integer taskType);
}