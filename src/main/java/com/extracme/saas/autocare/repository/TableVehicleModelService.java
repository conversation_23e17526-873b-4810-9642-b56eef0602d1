package com.extracme.saas.autocare.repository;

import java.util.List;

import com.extracme.saas.autocare.model.entity.MtcVehicleModel;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

/**
 * 车型表服务接口
 */
public interface TableVehicleModelService extends DefaultTableService<MtcVehicleModel, Long> {

    /**
     * 插入车型信息带入id
     *
     * @param record   车型信息
     * @param operator 操作人
     * @return 车型信息
     */
    MtcVehicleModel insertSelectiveWithId(MtcVehicleModel record, String operator);

    /**
     * 根据车型名称模糊查询车型列表
     *
     * @param vehicleModelName 车型名称
     * @param limit 最大返回条数
     * @return 车型列表
     */
    List<MtcVehicleModel> findByCondition(String vehicleModelName, int limit);

    /**
     * 获取车型下拉列表数据
     *
     * @param vehicleModelName 车型名称，用于模糊查询
     * @param limit 最大返回条数
     * @return 下拉列表数据，ID类型为Long
     */
    List<ComboVO<Long>> getVehicleModelCombo(String vehicleModelName, int limit);

    /**
     * 根据车型名称查询车型信息
     *
     * @param vehicleModelName 车型名称
     * @return 车型信息
     */
    MtcVehicleModel findByName(String vehicleModelName);

    /**
     * 查询所有车型信息
     *
     * @return 所有车型信息
     */
    List<MtcVehicleModel> findAll();
}
