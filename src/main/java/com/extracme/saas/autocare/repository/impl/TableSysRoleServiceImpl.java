package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.SysRoleDynamicSqlSupport.sysRole;
import static com.extracme.saas.autocare.mapper.base.SysUserRoleDynamicSqlSupport.sysUserRole;
import static org.mybatis.dynamic.sql.SqlBuilder.count;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.SysRoleExtendMapper;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.repository.TableSysRoleService;

/**
 * 系统角色Repository实现类
 */
@Repository
public class TableSysRoleServiceImpl implements TableSysRoleService {

    @Autowired
    private SysRoleExtendMapper sysRoleExtendMapper;

    @Override
    public List<SysRole> findByUserId(Long userId) {
        SelectStatementProvider selectStatement = select(sysRole.allColumns())
            .from(sysRole)
            .join(sysUserRole).on(sysRole.id, equalTo(sysUserRole.roleId))
            .where(sysUserRole.userId, isEqualTo(userId))
            .and(sysRole.status, isEqualTo(1))  // 只查询启用的角色
            .build()
            .render(RenderingStrategies.MYBATIS3);
        
        return Optional.ofNullable(sysRoleExtendMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public boolean hasRole(Long userId, String roleCode) {
        SelectStatementProvider selectStatement = select(count())
            .from(sysRole)
            .join(sysUserRole).on(sysRole.id, equalTo(sysUserRole.roleId))
            .where(sysUserRole.userId, isEqualTo(userId))
            .and(sysRole.roleCode, isEqualTo(roleCode))
            .and(sysRole.status, isEqualTo(1))  // 只查询启用的角色
            .build()
            .render(RenderingStrategies.MYBATIS3);
        
        return sysRoleExtendMapper.count(selectStatement) > 0;
    }
}