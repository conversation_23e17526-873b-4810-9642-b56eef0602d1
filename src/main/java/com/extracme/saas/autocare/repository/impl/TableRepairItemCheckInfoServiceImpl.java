package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcRepairItemCheckInfoDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcRepairItemCheckInfoDynamicSqlSupport.mtcRepairItemCheckInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.update;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcRepairItemCheckInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcRepairItemCheckInfo;
import com.extracme.saas.autocare.repository.TableRepairItemCheckInfoService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 维修项目核损信息表数据访问服务实现
 */
@Repository
public class TableRepairItemCheckInfoServiceImpl implements TableRepairItemCheckInfoService {

    @Autowired
    private MtcRepairItemCheckInfoExtendMapper mtcRepairItemCheckInfoMapper;

    @Override
    public MtcRepairItemCheckInfo insert(MtcRepairItemCheckInfo record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(record, operator);
    }

    @Override
    public MtcRepairItemCheckInfo insert(MtcRepairItemCheckInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcRepairItemCheckInfoMapper.insertSelective(record);
        return record;
    }

    @Override
    public MtcRepairItemCheckInfo selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcRepairItemCheckInfoMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<MtcRepairItemCheckInfo> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(mtcRepairItemCheckInfo.taskNo, isEqualTo(taskNo))
                .and(mtcRepairItemCheckInfo.status, isEqualTo(1))
                .orderBy(id.descending());
        return mtcRepairItemCheckInfoMapper.select(completer);
    }

    @Override
    public List<MtcRepairItemCheckInfo> selectByTaskNoAndItemType(String taskNo, Integer itemType) {
        SelectDSLCompleter completer = c -> c.where(mtcRepairItemCheckInfo.taskNo, isEqualTo(taskNo))
                .and(mtcRepairItemCheckInfo.itemType, isEqualTo(itemType))
                .and(mtcRepairItemCheckInfo.status, isEqualTo(1))
                .orderBy(id.descending());
        return mtcRepairItemCheckInfoMapper.select(completer);
    }

    @Override
    public int deleteByTaskNo(String taskNo) {
        return mtcRepairItemCheckInfoMapper
                .delete(c -> c.where(mtcRepairItemCheckInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int batchInsert(List<MtcRepairItemCheckInfo> checkInfoList) {
        if (checkInfoList == null || checkInfoList.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (MtcRepairItemCheckInfo checkInfo : checkInfoList) {
            count += mtcRepairItemCheckInfoMapper.insertSelective(checkInfo);
        }
        return count;
    }

    @Override
    public MtcRepairItemCheckInfo selectByTaskNoAndItemId(String taskNo, Long itemId) {
        SelectStatementProvider selectStatement = select(mtcRepairItemCheckInfo.allColumns())
                .from(mtcRepairItemCheckInfo)
                .where(mtcRepairItemCheckInfo.taskNo, isEqualTo(taskNo))
                .and(mtcRepairItemCheckInfo.itemId, isEqualTo(itemId))
                .and(mtcRepairItemCheckInfo.status, isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<MtcRepairItemCheckInfo> optional = mtcRepairItemCheckInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public int updateCheckStatus(Long id, Integer checkStatus, String operator) {
        if (id == null || checkStatus == null) {
            return 0;
        }

        Date now = new Date();
        UpdateStatementProvider updateStatement = update(mtcRepairItemCheckInfo)
                .set(mtcRepairItemCheckInfo.checkStatus).equalTo(checkStatus)
                .set(mtcRepairItemCheckInfo.updateBy).equalTo(operator)
                .set(mtcRepairItemCheckInfo.updatedTime).equalTo(now)
                .where(mtcRepairItemCheckInfo.id, isEqualTo(id))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return mtcRepairItemCheckInfoMapper.update(updateStatement);
    }

    @Override
    public int updateSelectiveById(MtcRepairItemCheckInfo record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcRepairItemCheckInfo record, String operator) {
        if (record == null || record.getId() == null) {
            return 0;
        }

        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);

        return mtcRepairItemCheckInfoMapper.updateByPrimaryKeySelective(record);
    }
}