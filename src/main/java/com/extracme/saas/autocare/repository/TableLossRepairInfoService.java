package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcLossRepairInfo;

import java.util.List;

/**
 * 损失维修信息表数据访问服务接口
 */
public interface TableLossRepairInfoService extends DefaultTableService<MtcLossRepairInfo, Long> {

    /**
     * 根据任务编号查询损失维修信息列表
     * @param taskNo 任务编号
     * @return 损失维修信息列表
     */
    List<MtcLossRepairInfo> selectByTaskNo(String taskNo);
    
    /**
     * 批量插入损失维修信息
     * @param repairInfoList 损失维修信息列表
     * @return 影响行数
     */
    int batchInsert(List<MtcLossRepairInfo> repairInfoList);
    
    /**
     * 根据任务编号更新损失维修信息状态为无效
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int updateStatus(String taskNo);
    
    /**
     * 根据任务编号删除损失维修信息
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int deleteByTaskNo(String taskNo);
    
    /**
     * 根据任务编号和状态更新损失维修信息
     * @param record 损失维修信息记录
     * @param taskNo 任务编号
     * @param operator 操作人
     * @return 影响行数
     */
    int updateSelectiveByTaskNoAndStatus(MtcLossRepairInfo record, String taskNo, String operator);
}
