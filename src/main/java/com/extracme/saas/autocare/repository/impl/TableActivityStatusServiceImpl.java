package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.base.ActivityStatusMapper;
import com.extracme.saas.autocare.model.entity.ActivityStatus;
import com.extracme.saas.autocare.repository.TableActivityStatusService;
import com.extracme.saas.autocare.util.SessionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLike;
import static com.extracme.saas.autocare.mapper.base.ActivityStatusDynamicSqlSupport.activityStatus;

/**
 * 活动状态表数据访问服务实现类
 */
@Repository
public class TableActivityStatusServiceImpl implements TableActivityStatusService {

    private static final Logger logger = LoggerFactory.getLogger(TableActivityStatusServiceImpl.class);

    @Autowired
    private ActivityStatusMapper activityStatusMapper;

    @Override
    public ActivityStatus selectById(Long id) {
        if (id == null) {
            return null;
        }
        Optional<ActivityStatus> optional = activityStatusMapper.selectByPrimaryKey(id);
        return optional.orElse(null);
    }

    @Override
    public ActivityStatus selectByStatusCode(String statusCode) {
        if (statusCode == null || statusCode.isEmpty()) {
            return null;
        }
        Optional<ActivityStatus> optional = activityStatusMapper.selectOne(c ->
                c.where(activityStatus.statusCode, isEqualTo(statusCode))
        );
        return optional.orElse(null);
    }

    @Override
    public List<ActivityStatus> findAll() {
        return activityStatusMapper.select(c -> c.orderBy(activityStatus.id));
    }

    @Override
    public List<ActivityStatus> findByStatusNameLike(String statusName) {
        if (statusName == null || statusName.isEmpty()) {
            return Collections.emptyList();
        }
        return activityStatusMapper.select(c ->
                c.where(activityStatus.statusName, isLike("%" + statusName + "%"))
                .orderBy(activityStatus.id)
        );
    }

    @Override
    public ActivityStatus insert(ActivityStatus activityStatus) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(activityStatus, operator);
    }

    @Override
    public ActivityStatus insert(ActivityStatus activityStatus, String operator) {
        if (activityStatus == null) {
            return null;
        }
        try {
            Date now = new Date();
            activityStatus.setCreateTime(now);
            activityStatus.setCreateBy(operator);
            activityStatus.setUpdateTime(now);
            activityStatus.setUpdateBy(operator);
            activityStatusMapper.insertSelective(activityStatus);
            return activityStatus;
        } catch (Exception e) {
            logger.error("插入活动状态失败", e);
            return null;
        }
    }

    @Override
    public int updateSelectiveById(ActivityStatus activityStatus) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(activityStatus, operator);
    }

    @Override
    public int updateSelectiveById(ActivityStatus activityStatus, String operator) {
        if (activityStatus == null || activityStatus.getId() == null) {
            return 0;
        }
        try {
            Date now = new Date();
            activityStatus.setUpdateTime(now);
            activityStatus.setUpdateBy(operator);
            return activityStatusMapper.updateByPrimaryKeySelective(activityStatus);
        } catch (Exception e) {
            logger.error("更新活动状态失败", e);
            return 0;
        }
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        try {
            return activityStatusMapper.deleteByPrimaryKey(id);
        } catch (Exception e) {
            logger.error("删除活动状态失败", e);
            return 0;
        }
    }

    @Override
    public boolean existsByStatusCode(String statusCode) {
        if (statusCode == null || statusCode.isEmpty()) {
            return false;
        }
        return activityStatusMapper.selectOne(c ->
                c.where(activityStatus.statusCode, isEqualTo(statusCode))
        ).isPresent();
    }
}
