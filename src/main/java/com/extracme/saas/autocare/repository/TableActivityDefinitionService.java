package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.ActivityDefinition;

import java.util.List;

/**
 * 活动节点定义表数据访问服务接口
 */
public interface TableActivityDefinitionService {

    /**
     * 根据ID查询活动节点定义
     *
     * @param id 活动节点ID
     * @return 活动节点定义
     */
    ActivityDefinition selectById(Long id);

    /**
     * 根据活动编码查询活动节点定义
     *
     * @param activityCode 活动编码
     * @return 活动节点定义
     */
    ActivityDefinition selectByActivityCode(String activityCode);

    /**
     * 根据工作流模板ID查询关联的活动节点定义列表
     *
     * @param workflowId 工作流模板ID
     * @return 活动节点定义列表
     */
    List<ActivityDefinition> selectByWorkflowId(Long workflowId);

    /**
     * 查询所有活动节点定义
     *
     * @return 活动节点定义列表
     */
    List<ActivityDefinition> findAll();

    /**
     * 插入活动节点定义
     *
     * @param activityDefinition 活动节点定义
     * @return 影响行数
     */
    int insert(ActivityDefinition activityDefinition);

    /**
     * 插入活动节点定义（带操作人）
     *
     * @param activityDefinition 活动节点定义
     * @param operator 操作人
     * @return 影响行数
     */
    int insert(ActivityDefinition activityDefinition, String operator);

    /**
     * 根据ID选择性更新活动节点定义
     *
     * @param activityDefinition 活动节点定义
     * @return 影响行数
     */
    int updateSelectiveById(ActivityDefinition activityDefinition);

    /**
     * 根据ID选择性更新活动节点定义（带操作人）
     *
     * @param activityDefinition 活动节点定义
     * @param operator 操作人
     * @return 影响行数
     */
    int updateSelectiveById(ActivityDefinition activityDefinition, String operator);

    /**
     * 更新活动节点定义
     *
     * @param activityDefinition 活动节点定义
     * @return 影响行数
     */
    int update(ActivityDefinition activityDefinition);

    /**
     * 删除活动节点定义
     *
     * @param id 活动节点ID
     * @return 影响行数
     */
    int deleteById(Long id);
}
