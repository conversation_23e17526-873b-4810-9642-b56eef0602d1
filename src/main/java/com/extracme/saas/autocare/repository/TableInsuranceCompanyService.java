package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.dto.InsuranceCompanyQueryDTO;
import com.extracme.saas.autocare.model.entity.InsuranceCompanyInfo;

import java.util.List;

/**
 * 保司信息信息Repository接口
 */
public interface TableInsuranceCompanyService extends DefaultTableService<InsuranceCompanyInfo, Long> {

    /**
     * 根据条件查询列表
     * @param queryDTO 查询条件
     * @return 保司信息列表信息
     */
    List<InsuranceCompanyInfo> queryInsuranceCompanyList(InsuranceCompanyQueryDTO queryDTO);

    /**
     * 根据保司信息名称查询保司信息信息
     * @param companyName 保司信息名称
     * @return 保司信息信息
     */
    InsuranceCompanyInfo selectByCompanyName(String companyName);

    /**
     * 查询全部保司信息列表
     * @return 保司信息列表
     */
    List<InsuranceCompanyInfo> findAllInsuranceCompany();

    /**
     * 根据ID删除保司信息信息
     * @param id 保司信息ID
     * @return 删除结果
     */
    int deleteById(Long id);
}
