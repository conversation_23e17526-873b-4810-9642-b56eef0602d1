package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcRepairItemCheckInfo;

import java.util.List;

/**
 * 维修项目核损信息表数据访问服务接口
 */
public interface TableRepairItemCheckInfoService extends DefaultTableService<MtcRepairItemCheckInfo, Long> {

    /**
     * 根据任务编号查询维修项目核损信息列表
     * @param taskNo 任务编号
     * @return 维修项目核损信息列表
     */
    List<MtcRepairItemCheckInfo> selectByTaskNo(String taskNo);
    
    /**
     * 根据任务编号和项目类型查询维修项目核损信息列表
     * @param taskNo 任务编号
     * @param itemType 项目类型
     * @return 维修项目核损信息列表
     */
    List<MtcRepairItemCheckInfo> selectByTaskNoAndItemType(String taskNo, Integer itemType);
    
    /**
     * 根据任务编号删除维修项目核损信息
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int deleteByTaskNo(String taskNo);
    
    /**
     * 批量插入维修项目核损信息
     * @param checkInfoList 维修项目核损信息列表
     * @return 影响行数
     */
    int batchInsert(List<MtcRepairItemCheckInfo> checkInfoList);
    
    /**
     * 根据任务编号和项目ID查询维修项目核损信息
     * @param taskNo 任务编号
     * @param itemId 项目ID
     * @return 维修项目核损信息
     */
    MtcRepairItemCheckInfo selectByTaskNoAndItemId(String taskNo, Long itemId);
    
    /**
     * 更新维修项目核损状态
     * @param id 主键ID
     * @param checkStatus 核损状态
     * @param operator 操作人
     * @return 影响行数
     */
    int updateCheckStatus(Long id, Integer checkStatus, String operator);
}