package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.DataDictInfoDynamicSqlSupport.dataDictInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.DataDictInfoMapper;
import com.extracme.saas.autocare.model.entity.DataDictInfo;
import com.extracme.saas.autocare.repository.TableDataDictInfoService;

/**
 * 数据字典表数据访问服务实现类
 */
@Repository
public class TableDataDictInfoServiceImpl implements TableDataDictInfoService {

    @Autowired
    private DataDictInfoMapper dataDictInfoMapper;

    @Override
    public DataDictInfo insert(DataDictInfo dataDictInfo) {
        dataDictInfoMapper.insertSelective(dataDictInfo);
        return dataDictInfo;
    }

    @Override
    public DataDictInfo insert(DataDictInfo dataDictInfo, String operator) {
        Date now = new Date();
        dataDictInfo.setCreateBy(operator);
        dataDictInfo.setUpdateBy(operator);
        dataDictInfo.setCreateTime(now);
        dataDictInfo.setUpdateTime(now);
        return insert(dataDictInfo);
    }

    @Override
    public int updateSelectiveById(DataDictInfo dataDictInfo) {
        return dataDictInfoMapper.updateByPrimaryKeySelective(dataDictInfo);
    }

    @Override
    public int updateSelectiveById(DataDictInfo dataDictInfo, String operator) {
        dataDictInfo.setUpdateBy(operator);
        dataDictInfo.setUpdateTime(new Date());
        return updateSelectiveById(dataDictInfo);
    }

    @Override
    public DataDictInfo selectById(Long id) {
        return dataDictInfoMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<DataDictInfo> selectAll() {
        return dataDictInfoMapper.select(c ->
            c.orderBy(dataDictInfo.id.descending())
        );
    }

    @Override
    public DataDictInfo selectByDataCode(String dataCode) {
        return dataDictInfoMapper.selectOne(c -> c.where(dataDictInfo.dataCode, isEqualTo(dataCode))
                .limit(1))
                .orElse(null);
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return dataDictInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<DataDictInfo> findByCondition(String dictName, String dictCode) {
        // 使用 SelectStatementProvider 构建查询
        SelectStatementProvider selectStatement = select(dataDictInfo.allColumns())
            .from(dataDictInfo)
            .where(dataDictInfo.dataName, isLikeWhenPresent(transFuzzyQueryParam(dictName)))
            .and(dataDictInfo.dataCode, isLikeWhenPresent(transFuzzyQueryParam(dictCode)))
            .orderBy(dataDictInfo.dataCode, dataDictInfo.id)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return dataDictInfoMapper.selectMany(selectStatement);
    }
}