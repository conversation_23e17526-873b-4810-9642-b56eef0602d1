package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.SmsSendRecordDynamicSqlSupport.smsSendRecord;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThanOrEqualTo;

import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.extracme.saas.autocare.mapper.base.SmsSendRecordMapper;
import com.extracme.saas.autocare.model.entity.SmsSendRecord;
import com.extracme.saas.autocare.repository.TableSmsSendRecordService;

/**
 * 短信发送记录表数据访问服务实现类
 */
@Repository
public class TableSmsSendRecordServiceImpl implements TableSmsSendRecordService {

    @Autowired
    private SmsSendRecordMapper smsSendRecordMapper;

    /**
     * 保存发送记录
     *
     * @param record 发送记录
     * @return 记录ID
     */
    @Override
    public Long save(SmsSendRecord record) {
        if (record == null) {
            return null;
        }

        if (record.getCreateTime() == null) {
            record.setCreateTime(new Date());
        }

        smsSendRecordMapper.insertSelective(record);
        return record.getId();
    }

    /**
     * 根据手机号和类型查询一段时间内的发送次数
     *
     * @param phoneNumber 手机号
     * @param type 验证码类型
     * @param startTime 开始时间
     * @return 发送次数
     */
    @Override
    public int countByPhoneAndTime(String phoneNumber, String type, Date startTime) {
        if (!StringUtils.hasText(phoneNumber) || !StringUtils.hasText(type) || startTime == null) {
            return 0;
        }

        return (int) smsSendRecordMapper.count(c ->
            c.where(smsSendRecord.phoneNumber, isEqualTo(phoneNumber))
             .and(smsSendRecord.type, isEqualTo(type))
             .and(smsSendRecord.createTime, isGreaterThanOrEqualTo(startTime)));
    }

    /**
     * 根据IP地址和类型查询一段时间内的发送次数
     *
     * @param ipAddress IP地址
     * @param type 验证码类型
     * @param startTime 开始时间
     * @return 发送次数
     */
    @Override
    public int countByIpAndTime(String ipAddress, String type, Date startTime) {
        if (!StringUtils.hasText(ipAddress) || !StringUtils.hasText(type) || startTime == null) {
            return 0;
        }

        return (int) smsSendRecordMapper.count(c ->
            c.where(smsSendRecord.ipAddress, isEqualTo(ipAddress))
             .and(smsSendRecord.type, isEqualTo(type))
             .and(smsSendRecord.createTime, isGreaterThanOrEqualTo(startTime)));
    }

    /**
     * 查询最后一次发送时间
     *
     * @param phoneNumber 手机号
     * @param type 验证码类型
     * @return 最后发送时间
     */
    @Override
    public Date findLastSendTime(String phoneNumber, String type) {
        if (!StringUtils.hasText(phoneNumber) || !StringUtils.hasText(type)) {
            return null;
        }

        SelectStatementProvider selectStatement = SqlBuilder.select(smsSendRecord.createTime)
                .from(smsSendRecord)
                .where(smsSendRecord.phoneNumber, isEqualTo(phoneNumber))
                .and(smsSendRecord.type, isEqualTo(type))
                .orderBy(smsSendRecord.createTime.descending())
                .limit(1).offset(0)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        List<SmsSendRecord> records = smsSendRecordMapper.selectMany(selectStatement);
        if (records.isEmpty()) {
            return null;
        }
        return records.get(0).getCreateTime();
    }

    /**
     * 删除指定天数前的发送记录
     *
     * @param days 天数
     * @return 删除数量
     */
    @Override
    public int deleteOldRecords(int days) {
        if (days <= 0) {
            return 0;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date deadline = calendar.getTime();

        return smsSendRecordMapper.delete(c -> c.where(smsSendRecord.createTime, isLessThan(deadline)));
    }

    /**
     * 查询指定时间段内的发送记录
     *
     * @param phoneNumber 手机号
     * @param type 验证码类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发送记录列表
     */
    @Override
    public List<SmsSendRecord> findBetweenTime(String phoneNumber, String type, Date startTime, Date endTime) {
        if (!StringUtils.hasText(phoneNumber) || !StringUtils.hasText(type) || startTime == null || endTime == null) {
            return Collections.emptyList();
        }

        return smsSendRecordMapper.select(c ->
            c.where(smsSendRecord.phoneNumber, isEqualTo(phoneNumber))
             .and(smsSendRecord.type, isEqualTo(type))
             .and(smsSendRecord.createTime, isGreaterThanOrEqualTo(startTime))
             .and(smsSendRecord.createTime, isLessThanOrEqualTo(endTime))
             .orderBy(smsSendRecord.createTime.descending()));
    }
}