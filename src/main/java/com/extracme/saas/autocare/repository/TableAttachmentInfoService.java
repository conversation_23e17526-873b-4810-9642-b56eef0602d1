package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.AttachmentInfo;

import java.util.List;

/**
 * 附件信息Repository接口
 */
public interface TableAttachmentInfoService extends DefaultTableService<AttachmentInfo, Long> {


    /**
     * 根据外键查询附件信息
     * @param foreignKey
     * @return
     */
    List<AttachmentInfo> findAttachmentByForeignKey(String foreignKey);

    /**
     * 根据外键删除附件信息
     * @param foreignKey
     * @return
     */
    int deleteByForeignKey(String foreignKey);
}
