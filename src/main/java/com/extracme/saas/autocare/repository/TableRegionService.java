package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.DataAreaInfo;
import com.extracme.saas.autocare.model.entity.DataCityInfo;
import com.extracme.saas.autocare.model.entity.DataProvinceInfo;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

import java.util.List;

/**
 * 地区数据表数据访问服务接口
 * 
 * <AUTHOR>
 * @date 2024/06/03
 */
public interface TableRegionService {

    /**
     * 查询所有省份信息
     * 
     * @return 省份信息列表
     */
    List<DataProvinceInfo> findAllProvinces();

    /**
     * 根据省份ID查询城市信息
     * 
     * @param provinceId 省份ID
     * @return 城市信息列表
     */
    List<DataCityInfo> findCitiesByProvinceId(Long provinceId);

    /**
     * 根据城市ID查询区域信息
     * 
     * @param cityId 城市ID
     * @return 区域信息列表
     */
    List<DataAreaInfo> findAreasByCityId(Long cityId);

    /**
     * 获取省份下拉框数据
     * 
     * @return 省份下拉框列表
     */
    List<ComboVO<Long>> getProvinceCombo();

    /**
     * 获取城市下拉框数据
     * 
     * @param provinceId 省份ID
     * @return 城市下拉框列表
     */
    List<ComboVO<Long>> getCityCombo(Long provinceId);

    /**
     * 获取区域下拉框数据
     * 
     * @param cityId 城市ID
     * @return 区域下拉框列表
     */
    List<ComboVO<Long>> getAreaCombo(Long cityId);
}
