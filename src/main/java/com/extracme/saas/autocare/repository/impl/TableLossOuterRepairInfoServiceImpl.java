package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcLossOuterRepairInfoDynamicSqlSupport.mtcLossOuterRepairInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcLossOuterRepairInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcLossOuterRepairInfo;
import com.extracme.saas.autocare.repository.TableLossOuterRepairInfoService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 损失外修信息表数据访问服务实现
 */
@Repository
public class TableLossOuterRepairInfoServiceImpl implements TableLossOuterRepairInfoService {

    @Autowired
    private MtcLossOuterRepairInfoExtendMapper mtcLossOuterRepairInfoMapper;

    @Override
    public MtcLossOuterRepairInfo insert(MtcLossOuterRepairInfo record) {
        // 调用带操作人参数的方法
        return insert(record, "");
    }

    @Override
    public MtcLossOuterRepairInfo insert(MtcLossOuterRepairInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcLossOuterRepairInfoMapper.insertSelective(record);
        return record;
    }

    @Override
    public MtcLossOuterRepairInfo selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcLossOuterRepairInfoMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<MtcLossOuterRepairInfo> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(mtcLossOuterRepairInfo.taskNo, isEqualTo(taskNo))
                .and(mtcLossOuterRepairInfo.status, isEqualTo(1))
                .orderBy(id.descending());
        return mtcLossOuterRepairInfoMapper.select(completer);
    }

    @Override
    public int updateSelectiveById(MtcLossOuterRepairInfo record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcLossOuterRepairInfo record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return mtcLossOuterRepairInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchInsert(List<MtcLossOuterRepairInfo> outerRepairInfoList) {
        if (outerRepairInfoList == null || outerRepairInfoList.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (MtcLossOuterRepairInfo outerRepairInfo : outerRepairInfoList) {
            count += mtcLossOuterRepairInfoMapper.insertSelective(outerRepairInfo);
        }
        return count;
    }

    @Override
    public int updateStatus(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }

        Date now = new Date();
        String operator = null != SessionUtils.getLoginUser() ? SessionUtils.getLoginUser().getUsername() : "精友回调";

        return mtcLossOuterRepairInfoMapper.update(c -> c.set(mtcLossOuterRepairInfo.status).equalTo(0)
                .set(mtcLossOuterRepairInfo.updatedTime).equalTo(now)
                .set(mtcLossOuterRepairInfo.updateBy).equalTo(operator)
                .where(mtcLossOuterRepairInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int deleteByTaskNo(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return 0;
        }

        return mtcLossOuterRepairInfoMapper.delete(c -> c.where(mtcLossOuterRepairInfo.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public int updateSelectiveByTaskNoAndStatus(MtcLossOuterRepairInfo record, String taskNo, String operator) {
        if (record == null || StringUtils.isBlank(taskNo)) {
            return 0;
        }

        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);

        return mtcLossOuterRepairInfoMapper.updateSelectiveByTaskNoAndStatus(record);
    }
}
