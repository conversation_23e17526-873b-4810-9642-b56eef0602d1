# ========================================
# Auto Care SaaS 应用主配置文件
# 包含所有环境通用的配置项
# ========================================

# 服务器配置 - 应用服务器基础设置
server:
  port: 30030                                  # 应用服务端口号
  servlet:
    context-path: /auto-care-saas               # 应用上下文路径

# Spring Boot 核心配置
spring:
  application:
    name: auto-care-saas                       # 应用名称，用于服务发现和监控
  profiles:
    active: sit                                # 默认激活的环境配置文件（dev/sit/prod）

  # 数据源通用配置 - Druid连接池设置（所有环境共用）
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource # 使用Druid连接池
    druid:
      initial-size: 5                          # 初始化连接数
      min-idle: 5                              # 最小空闲连接数
      max-active: 20                           # 最大活跃连接数
      max-wait: 60000                          # 获取连接最大等待时间（毫秒）
      time-between-eviction-runs-millis: 60000 # 连接回收器运行间隔时间（毫秒）
      min-evictable-idle-time-millis: 300000   # 连接在池中最小生存时间（毫秒）
      validation-query: SELECT 1               # 验证连接有效性的SQL语句
      test-while-idle: true                    # 空闲时检测连接是否有效
      test-on-borrow: false                    # 获取连接时不检测
      test-on-return: false                    # 归还连接时不检测
      pool-prepared-statements: true           # 开启PSCache（预编译语句缓存）
      max-pool-prepared-statement-per-connection-size: 20 # 每个连接PSCache大小
      filters: stat,wall                       # 开启监控统计和防火墙功能
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000 # 合并SQL统计，慢SQL阈值5秒

  # Redis通用配置 - 连接池和超时设置（所有环境共用）
  redis:
    timeout: 10s                               # Redis操作超时时间
    connect-timeout: 10s                       # Redis连接超时时间
    lettuce:                                   # 使用Lettuce客户端
      pool:
        min-idle: 2                            # 连接池最小空闲连接数
        max-idle: 10                           # 连接池最大空闲连接数
        max-active: 20                         # 连接池最大活跃连接数
        max-wait: 5s                           # 连接池获取连接最大等待时间
        time-between-eviction-runs: 30s        # 连接回收器运行间隔
        min-evictable-idle-time: 60s           # 连接最小空闲时间
      shutdown-timeout: 100ms                  # 关闭超时时间

  # Spring MVC 配置
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER      # 路径匹配策略，兼容Swagger等组件

  # 文件上传配置 - 限制单个请求的文件大小
  servlet:
    multipart:
      max-file-size: 30MB                      # 单个文件最大大小
      max-request-size: 100MB                   # 单次请求最大大小

# MyBatis ORM框架配置 - 数据库操作映射配置
mybatis:
  mapper-locations: classpath:mapper/**/*.xml  # Mapper XML文件位置
  type-aliases-package: com.extracme.saas.autocare.model.entity # 实体类包路径，用于类型别名
  configuration:
    map-underscore-to-camel-case: true         # 下划线转驼峰命名
    cache-enabled: true                        # 开启二级缓存

# PageHelper 分页插件配置 - 用于数据库分页查询
pagehelper:
  helper-dialect: mysql                        # 数据库方言，支持MySQL分页语法
  reasonable: true                             # 分页合理化，页码<=0查询第一页，页码>=总页数查询最后一页
  support-methods-arguments: true              # 支持通过方法参数传递分页参数
  params: count=countSql                       # 分页参数映射

# JWT Token配置 - 用户身份认证和授权
jwt:
  secret: 8Zz5tw0Ionm3XPZZfN0NOml3z9FMfmpgXwovR9fp6ryDIoGRM8EPHAB6iHsc0fb  # JWT签名密钥（生产环境应使用环境变量）
  expiration: 86400                            # Token过期时间（秒），24小时
  header: Authorization                        # HTTP请求头名称
  token-start-with: Bearer                     # Token前缀

# ========================================
# 阿里云OSS对象存储通用配置
# 环境特定配置（endpoint、bucket等）在各环境配置文件中定义
# ========================================
oss:
  upload:
    max-file-size: 100MB                       # 单个文件最大大小限制
    allowed-extensions:                        # 允许上传的文件扩展名列表
      - jpg                                    # 图片格式
      - jpeg
      - png
      - gif
      - bmp
      - pdf                                    # 文档格式
      - doc
      - docx
      - xls
      - xlsx
      - ppt
      - pptx
      - txt
      - zip                                    # 压缩格式
      - rar

# ========================================
# SMS短信服务通用配置
# 环境特定配置（API地址、密钥等）在各环境配置文件中定义
# ========================================
sms:
  # 测试账号配置
  test:
    mobile: 13800138000                        # 测试手机号，该号码不发送真实短信
  cryun:
    api:
      connection-timeout: 30000                # API连接超时时间（毫秒）
      read-timeout: 60000                      # API读取超时时间（毫秒）
      max-batch-size: 50000                    # 批量发送最大数量
