# ========================================
# 生产环境配置 (Production Environment)
# 用于正式生产环境，注重性能、安全和稳定性
# ========================================

spring:
  profiles: prod

  # 生产环境数据库配置 - 连接到生产环境MySQL数据库
  datasource:
    url: *********************************************************************************************************************************************************************************
    username: auto_care_user                   # 生产环境数据库用户名
    password: 9EaKjRU5Zd4z                     # 生产环境数据库密码

  # 生产环境Redis配置 - 用于缓存用户会话、权限信息等
  redis:
    database: 0                                # Redis数据库索引，生产环境使用0号库
    host: r-uf6f419dbf4e3884.redis.rds.aliyuncs.com # Redis服务器地址，生产环境
    port: 6379                                 # Redis服务器端口
    password: Extracme888                      # Redis连接密码

# Spring Boot Actuator监控端点配置 - 生产环境开启健康检查
management:
  endpoints:
    web:
      exposure:
        include: health,info                   # 暴露健康检查和应用信息端点
  endpoint:
    health:
      show-details: always                     # 显示详细的健康检查信息

# 生产环境日志配置 - 使用info级别，减少日志输出，提升性能
logging:
  level:
    com.extracme.saas.autocare: info           # 应用主包日志级别，生产环境使用info级别
    org.springframework: warn                  # Spring框架日志级别，只输出警告和错误
    org.apache.ibatis: warn                    # MyBatis框架日志级别，只输出警告和错误
    org.mybatis: warn                          # MyBatis核心日志级别
    com.extracme.saas.autocare.mapper: warn    # Mapper接口日志级别，只输出警告和错误
    druid.sql: error                           # Druid连接池SQL日志，只输出错误
    root: warn                                 # 根日志级别，只输出警告和错误

# 生产环境外部接口URL配置 - 与第三方系统集成的接口地址
# 精友理赔接口地址 - 用于车辆理赔数据交互（生产环境）
jyInterfaceUrl: http://jingyou.evcard.vip/ClaimCloudProd-app/jyClaim/SipClaimInterfaceServlet?insCode=EXTRACME&requestType=
# 定损刷新页面URL - 定损完成后跳转的页面地址（生产环境）
assessmentRefreshURL: http://saas.gcsrental.com/repair-management/vehicleRepairModule/taskDetail?id=idReplace
# 评估刷新页面URL - 评估完成后跳转的页面地址（生产环境）
evaluateRefreshURL: http://saas.gcsrental.com/repair-management/vehicleRepairModule/taskDetail?id=idReplace
# 定损回调接口URL - 第三方定损完成后回调本系统的接口（生产环境）
assessmentBackURL: http://saas.gcsrental.com/auto-care-saas/api/v1/jing-you/lossAssessmentBack
# 评估回调接口URL - 第三方评估完成后回调本系统的接口（生产环境）
evaluateBackURL: http://saas.gcsrental.com/auto-care-saas/api/v1/jing-you/evaluateLossBack
# 业财保险接口 （生产环境）
bfcInsureUrl: https://bx.gcsrental.com/bfc/insur

# 生产环境阿里云OSS对象存储配置 - 用于文件上传和存储
oss:
  endpoint: https://oss-cn-shanghai.aliyuncs.com     # OSS服务端点，上海区域
  access-key-id: LTAI5tJumdVNXHPLszk49yAk           # OSS访问密钥ID
  access-key-secret: ****************************** # OSS访问密钥Secret
  bucket-name: evcard                               # OSS存储桶名称，生产环境
  bucket-short-time: evcard-short-time              # 短期存储桶名称，用于临时文件
  upload:
    base-url: https://evcard.oss-cn-shanghai.aliyuncs.com # OSS文件访问基础URL
    env: prod                                       # 环境标识，用于文件路径区分

# 生产环境SMS短信服务配置 - 用于发送验证码和通知短信
sms:
  # 短信发送限制配置 - 生产环境严格限制
  code:
    expiration: 300                            # 验证码有效期（秒），5分钟后过期
  send:
    interval: 5                                # 短信发送间隔（秒），生产环境较短间隔
  daily:
    limit:
      mobile: 100                              # 每个手机号每天最大发送次数
      ip: 100                                  # 每个IP地址每天最大发送次数

  # 创瑞云短信平台配置 - 生产环境
  cryun:
    main:
      # 短信服务主账号配置，支持环境变量覆盖
      access-key: ${SMS_MAIN_ACCESS_KEY:tpkam4wxgu1MBQ1b}     # 生产环境短信平台访问密钥
      secret: ${SMS_MAIN_SECRET:Y82MkGHzun0nE7BHvnNtENhW5i8d6cR3}  # 生产环境短信平台访问秘钥
      sign: ${SMS_MAIN_SIGN:370696}                          # 短信签名ID
    api:
      host: ${SMS_API_HOST:http://api.1cloudsp.com}           # 短信API服务地址，生产环境