<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.saas.autocare.mapper.extend.MtcRepairTaskExtendMapper">
  
  <!-- 查询超时的维修任务 -->
  <resultMap id="RepairTaskProcessListResultMap" type="com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO">
    <id column="id" property="id" />
    <result column="task_no" property="taskNo" />
    <result column="org_id" property="orgId" />
    <result column="org_name" property="orgName" />
    <result column="vehicle_no" property="vehicleNo" />
    <result column="vehicle_model_info" property="vehicleModelInfo" />
    <result column="vin" property="vin" />
    <result column="insurance_company_name" property="insuranceCompanyName" />
    <result column="repair_type_id" property="repairTypeId" />
    <result column="repair_type_name" property="repairTypeName" />
    <result column="repair_grade" property="repairGrade" />
    <result column="repair_depot_id" property="repairDepotId" />
    <result column="repair_depot_name" property="repairDepotName" />
    <result column="repair_depot_type" property="repairDepotType" />
    <result column="task_create_time" property="taskCreateTime" />
    <result column="task_inflow_time" property="taskInflowTime" />
    <result column="vehicle_recive_time" property="vehicleReciveTime" />
    <result column="vehicle_check_time" property="vehicleCheckTime" />
    <result column="renttype" property="renttype" />
    <result column="fact_operate_tag" property="factOperateTag" />
    <result column="is_used_applets" property="isUsedApplets" />
    <result column="property_status" property="propertyStatus" />
    <result column="product_line" property="productLine" />
    <result column="sub_product_line" property="subProductLine" />
    <result column="instance_id" property="instanceId" />
    <result column="current_activity_code" property="currentActivityCode" />
    <result column="status_code" property="statusCode" />
    <result column="is_over_time" property="isOverTime" />
    <result column="repair_total_amount" property="repairTotalAmount" />
    <result column="estimated_claim_amount" property="estimatedClaimAmount" />
    <result column="user_assumed_amount" property="userAssumedAmount" />
    <result column="self_funded_amount" property="selfFundedAmount" />
  </resultMap>
  
  <!-- 通过活动实例表查询维修任务列表 -->
  <select id="queryRepairTaskListByActivityInstance" resultMap="RepairTaskProcessListResultMap">
    SELECT 
      t.id,
      t.task_no,
      t.org_id,
      t.org_name,
      t.vehicle_no,
      t.vehicle_model_info,
      t.vin,
      t.insurance_company_name,
      t.repair_type_id,
      t.repair_type_name,
      t.repair_grade,
      t.repair_depot_id,
      t.repair_depot_name,
      rdi.repair_depot_type,
      t.expected_repair_complete,
      t.task_create_time,
      t.task_inflow_time,
      t.vehicle_recive_time,
      t.vehicle_check_time,
      t.vehicle_repair_time,
      t.renttype,
      t.fact_operate_tag,
      t.is_used_applets,
      t.property_status,
      t.product_line,
      t.sub_product_line,
      t.advanced_audit_level,
      t.verification_loss_task_oper_id,
      su.username as verification_loss_task_username,
      su.nickname as verification_loss_task_nickname,
      t.create_by,
      t.created_time,
      t.update_by,
      t.updated_time,
      wi.id as instance_id,
      wi.current_activity_code,
      wi.status_code,
      case when date_add(t.vehicle_recive_time, interval t.expected_repair_days day) > (case when t.vehicle_repair_time is not null then t.vehicle_repair_time else now() end)
        then 0 else 1 end as is_over_time
    FROM activity_instance ai
    LEFT JOIN workflow_instance wi ON ai.instance_id = wi.id
    LEFT JOIN mtc_repair_task t ON wi.business_id = t.task_no
    LEFT JOIN mtc_repair_depot_info rdi ON t.repair_depot_id = rdi.repair_depot_id
    LEFT JOIN sys_user su ON su.id = t.verification_loss_task_oper_id
    <where>
      <if test="queryDTO.loginOrgIds != null and queryDTO.loginOrgIds.size() > 0">
        AND t.org_id IN
        <foreach collection="queryDTO.loginOrgIds" item="orgId" open="(" separator="," close=")">
          #{orgId}
        </foreach>
      </if>
      AND wi.tenant_id = #{tenantId}
      <if test="queryDTO.vin != null and queryDTO.vin != ''">
        AND t.vin = #{queryDTO.vin}
      </if>
      <if test="queryDTO.vehicleNo != null and queryDTO.vehicleNo != ''">
        AND t.vehicle_no = #{queryDTO.vehicleNo}
      </if>
      <if test="queryDTO.taskNo != null and queryDTO.taskNo != ''">
        AND t.task_no = #{queryDTO.taskNo}
      </if>
      <if test="queryDTO.repairTypeId != null">
        AND t.repair_type_id = #{queryDTO.repairTypeId}
      </if>
      <if test="queryDTO.repairDepotId != null and queryDTO.repairDepotId != ''">
        AND t.repair_depot_id = #{queryDTO.repairDepotId}
      </if>
      <if test="queryDTO.repairDepotOrgId != null and queryDTO.repairDepotOrgId != ''">
        AND t.repair_depot_org_id = #{queryDTO.repairDepotOrgId}
      </if>
      <if test="queryDTO.vehicleModelId != null">
        AND t.vehicle_model_seq = #{queryDTO.vehicleModelId}
      </if>
      <if test="queryDTO.renttype != null">
        AND t.renttype = #{queryDTO.renttype}
      </if>
      <if test="queryDTO.orgId != null and queryDTO.orgId != ''">
        AND t.org_id = #{queryDTO.orgId}
      </if>
      <if test="queryDTO.advancedAuditLevel != null">
        AND t.advanced_audit_level = #{queryDTO.advancedAuditLevel}
      </if>
      <if test="queryDTO.currentActivityCode != null and queryDTO.currentActivityCode != ''">
        AND ai.to_activity_code = #{queryDTO.currentActivityCode}
      </if>
      <if test="queryDTO.statusCode != null and queryDTO.statusCode != ''">
        AND ai.current_status_code = #{queryDTO.statusCode}
      </if>
      <if test="queryDTO.taskCreateStartTime != null">
        AND t.task_create_time >= #{queryDTO.taskCreateStartTime}
      </if>
      <if test="queryDTO.taskCreateEndTime != null">
        AND t.task_create_time &lt;= DATE_ADD(#{queryDTO.taskCreateEndTime}, INTERVAL 1 DAY)
      </if>
      <if test="queryDTO.taskInflowStartTime != null">
        AND t.task_inflow_time >= #{queryDTO.taskInflowStartTime}
      </if>
      <if test="queryDTO.taskInflowEndTime != null">
        AND t.task_inflow_time &lt;= DATE_ADD(#{queryDTO.taskInflowEndTime}, INTERVAL 1 DAY)
      </if>
      <if test="queryDTO.vehicleReciveStartTime != null">
        AND t.vehicle_recive_time >= #{queryDTO.vehicleReciveStartTime}
      </if>
      <if test="queryDTO.vehicleReciveEndTime != null">
        AND t.vehicle_recive_time &lt;= DATE_ADD(#{queryDTO.vehicleReciveEndTime}, INTERVAL 1 DAY)
      </if>
      <if test="queryDTO.vehicleCheckStartTime != null">
        AND t.vehicle_check_time >= #{queryDTO.vehicleCheckStartTime}
      </if>
      <if test="queryDTO.vehicleCheckEndTime != null">
        AND t.vehicle_check_time &lt;= DATE_ADD(#{queryDTO.vehicleCheckEndTime}, INTERVAL 1 DAY)
      </if>
      <if test="queryDTO.isOverTime != null"> 
        AND case when date_add(t.vehicle_recive_time, interval t.expected_repair_days day) > 
        (case when t.vehicle_repair_time is not null then t.vehicle_repair_time else now() end)
        then 0 else 1 end = #{queryDTO.isOverTime, jdbcType=VARCHAR}
      </if>
    </where>
    ORDER BY t.updated_time DESC
  </select>

  <!-- 通过活动实例表查询维修任务列表 -->
  <select id="queryRepairTaskListByWorkflowInstance" resultMap="RepairTaskProcessListResultMap">
    SELECT 
      t.id,
      t.task_no,
      t.org_id,
      t.org_name,
      t.vehicle_no,
      t.vehicle_model_info,
      t.vin,
      t.insurance_company_name,
      t.repair_type_id,
      t.repair_type_name,
      t.repair_grade,
      t.repair_depot_id,
      t.repair_depot_name,
      rdi.repair_depot_type,
      t.expected_repair_complete,
      t.task_create_time,
      t.task_inflow_time,
      t.vehicle_recive_time,
      t.vehicle_check_time,
      t.vehicle_repair_time,
      t.renttype,
      t.fact_operate_tag,
      t.is_used_applets,
      t.property_status,
      t.product_line,
      t.sub_product_line,
      t.advanced_audit_level,
      t.verification_loss_task_oper_id,
      t.repair_total_amount,
      t.estimated_claim_amount,
      t.user_assumed_amount,
      t.self_funded_amount,
      su.username as verification_loss_task_username,
      su.nickname as verification_loss_task_nickname,
      t.create_by,
      t.created_time,
      t.update_by,
      t.updated_time,
      wi.id as instance_id,
      wi.current_activity_code,
      wi.status_code,
      case when date_add(t.vehicle_recive_time, interval t.expected_repair_days day) > (case when t.vehicle_repair_time is not null then t.vehicle_repair_time else now() end)
        then 0 else 1 end as is_over_time
    FROM workflow_instance wi
    LEFT JOIN mtc_repair_task t ON wi.business_id = t.task_no
    LEFT JOIN mtc_repair_depot_info rdi ON t.repair_depot_id = rdi.repair_depot_id
    LEFT JOIN sys_user su ON su.id = t.verification_loss_task_oper_id
    where
      wi.status_code != 'COMPLETED' AND wi.status_code != 'CLOSED'
      <if test="queryDTO.loginOrgIds != null and queryDTO.loginOrgIds.size() > 0">
        AND t.org_id IN
        <foreach collection="queryDTO.loginOrgIds" item="orgId" open="(" separator="," close=")">
          #{orgId}
        </foreach>
      </if>
      AND wi.tenant_id = #{tenantId}
      <if test="queryDTO.vin != null and queryDTO.vin != ''">
        AND t.vin = #{queryDTO.vin}
      </if>
      <if test="queryDTO.vehicleNo != null and queryDTO.vehicleNo != ''">
        AND t.vehicle_no = #{queryDTO.vehicleNo}
      </if>
      <if test="queryDTO.taskNo != null and queryDTO.taskNo != ''">
        AND t.task_no = #{queryDTO.taskNo}
      </if>
      <if test="queryDTO.repairTypeId != null">
        AND t.repair_type_id = #{queryDTO.repairTypeId}
      </if>
      <if test="queryDTO.repairDepotId != null and queryDTO.repairDepotId != ''">
        AND t.repair_depot_id = #{queryDTO.repairDepotId}
      </if>
      <if test="queryDTO.repairDepotOrgId != null and queryDTO.repairDepotOrgId != ''">
        AND t.repair_depot_org_id = #{queryDTO.repairDepotOrgId}
      </if>
      <if test="queryDTO.vehicleModelId != null">
        AND t.vehicle_model_seq = #{queryDTO.vehicleModelId}
      </if>
      <if test="queryDTO.renttype != null">
        AND t.renttype = #{queryDTO.renttype}
      </if>
      <if test="queryDTO.orgId != null and queryDTO.orgId != ''">
        AND t.org_id = #{queryDTO.orgId}
      </if>
      <if test="queryDTO.advancedAuditLevel != null">
        AND t.advanced_audit_level = #{queryDTO.advancedAuditLevel}
      </if>
      <if test="queryDTO.currentActivityCode != null and queryDTO.currentActivityCode != ''">
        AND wi.current_activity_code = #{queryDTO.currentActivityCode}
      </if>
      <if test="queryDTO.statusCode != null and queryDTO.statusCode != ''">
        AND wi.status_code = #{queryDTO.statusCode}
      </if>
      <if test="queryDTO.taskCreateStartTime != null">
        AND t.task_create_time >= #{queryDTO.taskCreateStartTime}
      </if>
      <if test="queryDTO.taskCreateEndTime != null">
        AND t.task_create_time &lt;= DATE_ADD(#{queryDTO.taskCreateEndTime}, INTERVAL 1 DAY)
      </if>
      <if test="queryDTO.taskInflowStartTime != null">
        AND t.task_inflow_time >= #{queryDTO.taskInflowStartTime}
      </if>
      <if test="queryDTO.taskInflowEndTime != null">
        AND t.task_inflow_time &lt;= DATE_ADD(#{queryDTO.taskInflowEndTime}, INTERVAL 1 DAY)
      </if>
      <if test="queryDTO.vehicleReciveStartTime != null">
        AND t.vehicle_recive_time >= #{queryDTO.vehicleReciveStartTime}
      </if>
      <if test="queryDTO.vehicleReciveEndTime != null">
        AND t.vehicle_recive_time &lt;= DATE_ADD(#{queryDTO.vehicleReciveEndTime}, INTERVAL 1 DAY)
      </if>
      <if test="queryDTO.vehicleCheckStartTime != null">
        AND t.vehicle_check_time >= #{queryDTO.vehicleCheckStartTime}
      </if>
      <if test="queryDTO.vehicleCheckEndTime != null">
        AND t.vehicle_check_time &lt;= DATE_ADD(#{queryDTO.vehicleCheckEndTime}, INTERVAL 1 DAY)
      </if>
      <if test="queryDTO.isOverTime != null"> 
        AND case when date_add(t.vehicle_recive_time, interval t.expected_repair_days day) > 
        (case when t.vehicle_repair_time is not null then t.vehicle_repair_time else now() end)
        then 0 else 1 end = #{queryDTO.isOverTime, jdbcType=VARCHAR}
      </if>
      <!-- 修理厂小程序可查看环节 -->
      <if test="queryDTO.repairDepotId != null and queryDTO.repairDepotId != ''">
        AND wi.current_activity_code in ("VEHICLE_TRANSFER", "REPAIR_QUOTATION", "IN_REPAIR")
      </if>
      <!-- 运营人员小程序可查看环节 -->
      <if test="queryDTO.repairDepotId == null or queryDTO.repairDepotId == ''">
        AND wi.current_activity_code in ("PENDING_ASSIGNMENT", "PRE_INSPECTION_REVIEW", "LOSS_ASSESSMENT", "QUALITY_INSPECTION")
      </if>
    ORDER BY t.updated_time DESC
  </select>
  
  <!-- 根据条件查询活动当前状态码列表 -->
  <select id="selectActivityCurrentStatusCodeListByCondition" resultType="java.lang.String">
    SELECT 
      ai.current_status_code
    FROM mtc_repair_task t
    LEFT JOIN workflow_instance wi ON wi.business_id = t.task_no
    LEFT JOIN activity_instance ai ON ai.instance_id = wi.id
    WHERE ai.to_activity_code = #{queryDTO.currentActivityCode}
    AND wi.tenant_id = #{tenantId}
    <if test="queryDTO.loginOrgIds != null and queryDTO.loginOrgIds.size() > 0">
      AND t.org_id IN
      <foreach collection="queryDTO.loginOrgIds" item="orgId" open="(" separator="," close=")">
        #{orgId}
      </foreach>
    </if>
    <if test="queryDTO.vin != null and queryDTO.vin != ''">
      AND t.vin = #{queryDTO.vin}
    </if>
    <if test="queryDTO.vehicleNo != null and queryDTO.vehicleNo != ''">
      AND t.vehicle_no = #{queryDTO.vehicleNo}
    </if>
    <if test="queryDTO.taskNo != null and queryDTO.taskNo != ''">
      AND t.task_no = #{queryDTO.taskNo}
    </if>
    <if test="queryDTO.repairTypeId != null">
      AND t.repair_type_id = #{queryDTO.repairTypeId}
    </if>
    <if test="queryDTO.repairDepotId != null and queryDTO.repairDepotId != ''">
      AND t.repair_depot_id = #{queryDTO.repairDepotId}
    </if>
    <if test="queryDTO.repairDepotOrgId != null and queryDTO.repairDepotOrgId != ''">
      AND t.repair_depot_org_id = #{queryDTO.repairDepotOrgId}
    </if>
    <if test="queryDTO.vehicleModelId != null">
      AND t.vehicle_model_seq = #{queryDTO.vehicleModelId}
    </if>
    <if test="queryDTO.renttype != null">
      AND t.renttype = #{queryDTO.renttype}
    </if>
    <if test="queryDTO.orgId != null and queryDTO.orgId != ''">
      AND t.org_id = #{queryDTO.orgId}
    </if>
    <if test="queryDTO.advancedAuditLevel != null">
      AND t.advanced_audit_level = #{queryDTO.advancedAuditLevel}
    </if>
    <if test="queryDTO.taskCreateStartTime != null">
      AND t.task_create_time >= #{queryDTO.taskCreateStartTime}
    </if>
    <if test="queryDTO.taskCreateEndTime != null">
      AND t.task_create_time &lt;= DATE_ADD(#{queryDTO.taskCreateEndTime}, INTERVAL 1 DAY)
    </if>
    <if test="queryDTO.taskInflowStartTime != null">
      AND t.task_inflow_time >= #{queryDTO.taskInflowStartTime}
    </if>
    <if test="queryDTO.taskInflowEndTime != null">
      AND t.task_inflow_time &lt;= DATE_ADD(#{queryDTO.taskInflowEndTime}, INTERVAL 1 DAY)
    </if>
    <if test="queryDTO.vehicleReciveStartTime != null">
      AND t.vehicle_recive_time >= #{queryDTO.vehicleReciveStartTime}
    </if>
    <if test="queryDTO.vehicleReciveEndTime != null">
      AND t.vehicle_recive_time &lt;= DATE_ADD(#{queryDTO.vehicleReciveEndTime}, INTERVAL 1 DAY)
    </if>
    <if test="queryDTO.vehicleCheckStartTime != null">
      AND t.vehicle_check_time >= #{queryDTO.vehicleCheckStartTime}
    </if>
    <if test="queryDTO.vehicleCheckEndTime != null">
      AND t.vehicle_check_time &lt;= DATE_ADD(#{queryDTO.vehicleCheckEndTime}, INTERVAL 1 DAY)
    </if>
    <if test="queryDTO.isOverTime != null"> 
      AND case when date_add(t.vehicle_recive_time, interval t.expected_repair_days day) > 
        (case when t.vehicle_repair_time is not null then t.vehicle_repair_time else now() end)
        then 0 else 1 end = #{queryDTO.isOverTime, jdbcType=VARCHAR}
    </if>
  </select>
  
</mapper>
