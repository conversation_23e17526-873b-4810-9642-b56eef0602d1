-- 1. 添加新的task_type_new列
ALTER TABLE workflow_template ADD COLUMN task_type_new INT COMMENT '任务类型';

-- 2. 将原有task_type列的数据转换并迁移到新列
-- 自建任务
UPDATE workflow_template SET task_type_new = 0 WHERE task_type = '自建任务';
-- 事故维修
UPDATE workflow_template SET task_type_new = 1 WHERE task_type = '事故维修';
-- 自费维修
UPDATE workflow_template SET task_type_new = 2 WHERE task_type = '自费维修';
-- 车辆保养
UPDATE workflow_template SET task_type_new = 3 WHERE task_type = '车辆保养';
-- 轮胎任务
UPDATE workflow_template SET task_type_new = 4 WHERE task_type = '轮胎任务';
-- 自费维修(原车辆保养)
UPDATE workflow_template SET task_type_new = 5 WHERE task_type = '自费维修(原车辆保养)';
-- 常规保养
UPDATE workflow_template SET task_type_new = 6 WHERE task_type = '常规保养';
-- 终端维修
UPDATE workflow_template SET task_type_new = 7 WHERE task_type = '终端维修';

-- 3. 删除原有的task_type列
ALTER TABLE workflow_template DROP COLUMN task_type;

-- 4. 重命名新列为task_type
ALTER TABLE workflow_template CHANGE task_type_new task_type INT COMMENT '任务类型'; 