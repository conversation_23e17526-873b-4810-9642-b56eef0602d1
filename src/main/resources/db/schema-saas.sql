-- 创建数据库
CREATE DATABASE IF NOT EXISTS `auto_care_saas` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE `auto_care_saas`;

-- 模块1：租户管理
-- 1.1 租户表
CREATE TABLE IF NOT EXISTS `sys_tenant` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_name` VARCHAR(100) NOT NULL COMMENT '租户名称',
    `tenant_code` VARCHAR(50) NOT NULL COMMENT '租户编码',
    `contact_name` VARCHAR(50) DEFAULT NULL COMMENT '联系人姓名',
    `contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系人电话',
    `contact_email` VARCHAR(100) DEFAULT NULL COMMENT '联系人邮箱',
    `expire_time` DATETIME DEFAULT NULL COMMENT '租约过期时间',
    `max_user_count` INT DEFAULT 0 COMMENT '最大用户数量',
    `status` INT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户表';

-- 模块2：用户管理
-- 2.1 用户表
CREATE TABLE IF NOT EXISTS `sys_user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `mobile` VARCHAR(11) NOT NULL COMMENT '手机号',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像',
    `email` VARCHAR(50) DEFAULT NULL COMMENT '邮箱',
    `status` INT NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-启用',
    `account_type` TINYINT NOT NULL DEFAULT '0' COMMENT '账号类型:0-普通,1-修理厂',
    `garage_id` BIGINT DEFAULT NULL COMMENT '关联修理厂ID',
    `org_id` BIGINT DEFAULT NULL COMMENT '组织ID',
    `approval_level` INT DEFAULT NULL COMMENT '审批层级（0：无，1：一级，2：二级，3：三级，4：四级）',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username_tenant` (`username`, `tenant_id`),
    UNIQUE KEY `uk_mobile_tenant` (`mobile`, `tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';

-- 模块3：角色与权限管理
-- 3.1 角色表
CREATE TABLE IF NOT EXISTS `sys_role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
    `role_type` INT NOT NULL DEFAULT 3 COMMENT '角色类型：1-超级管理员，2-普通管理员，3-普通用户',
    `description` VARCHAR(200) DEFAULT NULL COMMENT '角色描述',
    `status` INT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code_tenant` (`role_code`, `tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统角色表';

-- 3.2 权限表
CREATE TABLE IF NOT EXISTS `sys_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `permission_name` VARCHAR(50) NOT NULL COMMENT '权限名称',
    `permission_code` VARCHAR(50) NOT NULL COMMENT '权限编码',
    `permission_type` VARCHAR(20) NOT NULL COMMENT '权限类型：menu-菜单，button-按钮，hiddenMenu-隐藏菜单',
    `parent_id` BIGINT DEFAULT NULL COMMENT '父级权限ID',
    `path` VARCHAR(200) DEFAULT NULL COMMENT '路由地址',
    `component` VARCHAR(200) DEFAULT NULL COMMENT '组件路径',
    `icon` VARCHAR(50) DEFAULT NULL COMMENT '图标',
    `sort` INT DEFAULT 0 COMMENT '排序',
    `status` INT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统权限表';

-- 3.3 用户角色关联表
CREATE TABLE IF NOT EXISTS `sys_user_role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `tenant_id` BIGINT DEFAULT NULL COMMENT '租户ID',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 3.4 角色权限关联表
CREATE TABLE IF NOT EXISTS `sys_role_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `permission_id` BIGINT NOT NULL COMMENT '权限ID',
    `tenant_id` BIGINT DEFAULT NULL COMMENT '租户ID',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_permission_id` (`permission_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 模块4：验证码与短信服务
-- 4.1 验证码表
CREATE TABLE `sys_verification_code` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `mobile` VARCHAR(11) NOT NULL COMMENT '手机号',
    `code` VARCHAR(6) NOT NULL COMMENT '验证码',
    `type` VARCHAR(20) NOT NULL COMMENT '验证码类型:LOGIN-登录,REGISTER-注册',
    `fail_count` int NOT NULL DEFAULT 0 COMMENT '失败次数',
    `status` INT NOT NULL DEFAULT 0 COMMENT '状态：0-未使用，1-已使用',
    `expire_time` DATETIME NOT NULL COMMENT '过期时间',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_mobile_type` (`mobile`, `type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='验证码表';

-- 4.2 短信发送记录表
CREATE TABLE IF NOT EXISTS `sms_send_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `phone_number` VARCHAR(20) NOT NULL COMMENT '手机号',
    `ip_address` VARCHAR(50) NOT NULL COMMENT 'IP地址',
    `type` VARCHAR(20) NOT NULL COMMENT '验证码类型：LOGIN-登录',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_phone_type_time` (`phone_number`, `type`, `create_time`),
    KEY `idx_ip_type_time` (`ip_address`, `type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信发送记录表';

-- 模块5：流程管理
-- 5.1 流程模板表
CREATE TABLE workflow_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '流程模板编号',
    workflow_name VARCHAR(100) NOT NULL COMMENT '流程模板名称',
    description TEXT COMMENT '流程描述',
    task_type VARCHAR(50) NOT NULL COMMENT '任务类型',
    repair_factory_type INT NOT NULL COMMENT '修理厂类型：-1表示全部，1表示合作，2表示非合作',
    product_line VARCHAR(50) NOT NULL COMMENT '产品线',
    is_active INT NOT NULL DEFAULT TRUE COMMENT '是否启用',
    tenant_id INT NOT NULL COMMENT '租户编号',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5.2 活动节点定义表
CREATE TABLE activity_definition (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '活动节点编号',
    activity_code VARCHAR(50) NOT NULL COMMENT '活动编码',
    activity_name VARCHAR(100) NOT NULL COMMENT '活动名称',
    description TEXT COMMENT '活动描述',
    sequence INT NOT NULL COMMENT '节点顺序',
    is_enabled INT NOT NULL DEFAULT TRUE COMMENT '是否启用',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5.3 节点状态定义表
CREATE TABLE activity_status (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '状态编号',
    status_code VARCHAR(50) NOT NULL COMMENT '状态编码',
    status_name VARCHAR(100) NOT NULL COMMENT '状态名称',
    description TEXT COMMENT '状态描述',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5.4 节点状态转换规则表
CREATE TABLE activity_status_transition (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '转换规则编号',
    activity_transition_id BIGINT NOT NULL COMMENT '关联的活动节点转换规则编号',
    from_status_code BIGINT NOT NULL COMMENT '起始状态编码',
    to_status_code BIGINT NOT NULL COMMENT '目标状态编码',
    description TEXT COMMENT '规则说明',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5.5 流程模板活动节点关联表
CREATE TABLE workflow_activity (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联记录编号',
    workflow_id BIGINT NOT NULL COMMENT '流程模板编号',
    activity_code BIGINT NOT NULL COMMENT '活动节点编号',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5.6 活动节点转换规则表
CREATE TABLE activity_transition (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '转换规则编号',
    workflow_id BIGINT NOT NULL COMMENT '所属流程模板编号',
    from_activity_code BIGINT NOT NULL COMMENT '转换起始节点编号',
    to_activity_code BIGINT NOT NULL COMMENT '目标节点编号',
    trigger_event VARCHAR(50) NOT NULL COMMENT '触发事件名称',
    condition_handler VARCHAR(255) COMMENT '条件判断处理器类名',
    handler_class VARCHAR(255) NOT NULL COMMENT '处理逻辑类名',
    description TEXT COMMENT '规则说明',
    tenant_id INT NOT NULL COMMENT '租户编号',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5.7 流程实例表
CREATE TABLE workflow_instance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '流程实例编号',
    workflow_id BIGINT NOT NULL COMMENT '所采用的流程模板编号',
    business_id VARCHAR(100) NOT NULL COMMENT '业务对象编号',
    tenant_id INT NOT NULL COMMENT '租户编号',
    current_activity_code BIGINT NOT NULL COMMENT '当前所在的活动节点编号',
    status_code VARCHAR(50) NOT NULL COMMENT '流程状态',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5.8 活动实例记录表
CREATE TABLE activity_instance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '活动实例记录编号',
    instance_id BIGINT NOT NULL COMMENT '所属流程实例编号',
    to_activity_code BIGINT NOT NULL COMMENT '目标活动节点编号',
    from_activity_code BIGINT COMMENT '来源节点编号',
    transition_id BIGINT COMMENT '触发转换的规则编号',
    start_time DATETIME NOT NULL COMMENT '活动开始时间',
    end_time DATETIME COMMENT '活动结束时间',
    duration INT COMMENT '活动总耗时（单位：秒）',
    current_status_code VARCHAR(50) NOT NULL COMMENT '当前状态编码',
    operator VARCHAR(100) NOT NULL COMMENT '操作人',
    remarks TEXT COMMENT '备注说明',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5.9 流程实例进度表
CREATE TABLE workflow_instant_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '进度记录编号',
    instance_id BIGINT NOT NULL COMMENT '所属流程实例编号',
    activity_code BIGINT NOT NULL COMMENT '活动节点编号',
    status_code VARCHAR(50) NOT NULL COMMENT '节点状态',
    remarks TEXT COMMENT '备注说明',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;