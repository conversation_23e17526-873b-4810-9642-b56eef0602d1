USE `auto_care_saas`;

-- 初始化平台租户
INSERT INTO `sys_tenant`
(`id`, `tenant_name`, `tenant_code`, `contact_name`, `contact_phone`, `contact_email`, `status`, `create_by`, `update_by`)
VALUES (1, '平台运营商', 'PLATFORM', 'Admin', '***********', '<EMAIL>', 1, '', '');

-- 初始化超级管理员用户
INSERT INTO `sys_user`
(`id`, `tenant_id`, `username`, `mobile`, `nickname`, `status`, `org_id`, `create_by`, `update_by`)
VALUES (1, 1, 'admin', '***********', '超级管理员', 1, 1, '', '');

-- 初始化系统角色
INSERT INTO `sys_role`
(`id`, `role_name`, `role_code`, `description`, `status`, `tenant_id`, `create_by`, `update_by`)
VALUES
(1, '超级管理员', 'SUPER_ADMIN', '平台超级管理员', 1, 1, '', ''),
(2, '公司管理员', 'COMPANY_ADMIN', '公司管理员', 1, 1, '', '');

-- 初始化权限
INSERT INTO `sys_permission`
(`id`, `permission_name`, `permission_code`, `permission_type`, `parent_id`, `path`, `component`, `icon`, `sort`, `status`, `create_by`, `update_by`)
VALUES
-- 系统管理
(1, '系统管理', 'system', 'menu', NULL, '/system', NULL, 'system', 1, 1, '', ''),
-- 用户管理
(2, '用户管理', 'system:user', 'menu', 1, '/system/user', 'system/user/index', 'user', 1, 1, '', ''),
(3, '用户查询', 'system:user:query', 'button', 2, NULL, NULL, NULL, 1, 1, '', ''),
(4, '用户新增', 'system:user:add', 'button', 2, NULL, NULL, NULL, 2, 1, '', ''),
(5, '用户修改', 'system:user:edit', 'button', 2, NULL, NULL, NULL, 3, 1, '', ''),
(6, '用户删除', 'system:user:delete', 'button', 2, NULL, NULL, NULL, 4, 1, '', ''),
-- 角色管理
(7, '角色管理', 'system:role', 'menu', 1, '/system/role', 'system/role/index', 'role', 2, 1, '', ''),
(8, '角色查询', 'system:role:query', 'button', 7, NULL, NULL, NULL, 1, 1, '', ''),
(9, '角色新增', 'system:role:add', 'button', 7, NULL, NULL, NULL, 2, 1, '', ''),
(10, '角色修改', 'system:role:edit', 'button', 7, NULL, NULL, NULL, 3, 1, '', ''),
(11, '角色删除', 'system:role:delete', 'button', 7, NULL, NULL, NULL, 4, 1, '', ''),
-- 权限管理
(12, '权限管理', 'system:permission', 'menu', 1, '/system/permission', 'system/permission/index', 'permission', 3, 1, '', ''),
(13, '权限查询', 'system:permission:query', 'button', 12, NULL, NULL, NULL, 1, 1, '', ''),
(14, '权限新增', 'system:permission:add', 'button', 12, NULL, NULL, NULL, 2, 1, '', ''),
(15, '权限修改', 'system:permission:edit', 'button', 12, NULL, NULL, NULL, 3, 1, '', ''),
(16, '权限删除', 'system:permission:delete', 'button', 12, NULL, NULL, NULL, 4, 1, '', ''),
(24, '接口权限', 'system:api', 'hiddenMenu', 1, NULL, NULL, 'api', 6, 1, '', ''),
-- 机构管理
(17, '机构管理', 'system:org', 'menu', 1, '/system/org', 'system/org/index', 'org', 4, 1, '', ''),
(18, '机构查询', 'system:org:query', 'button', 17, NULL, NULL, NULL, 1, 1, '', ''),
(19, '机构新增', 'system:org:add', 'button', 17, NULL, NULL, NULL, 2, 1, '', ''),
(20, '机构修改', 'system:org:edit', 'button', 17, NULL, NULL, NULL, 3, 1, '', ''),
(21, '机构删除', 'system:org:delete', 'button', 17, NULL, NULL, NULL, 4, 1, '', ''),
-- 日志管理
(22, '日志管理', 'system:log', 'menu', 1, '/system/log', 'system/log/index', 'log', 5, 1, '', ''),
(23, '日志查询', 'system:log:query', 'button', 22, NULL, NULL, NULL, 1, 1, '', '');

-- 初始化超级管理员角色权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `tenant_id`, `create_by`)
SELECT 1, id, 1, '' FROM sys_permission;

-- 初始化用户角色关系
INSERT INTO `sys_user_role` (`user_id`, `role_id`, `tenant_id`, `create_by`)
VALUES (1, 1, 1, '');

-- 初始化流程模板
INSERT INTO workflow_template
(id, workflow_name, description, task_type, repair_factory_type, product_line, is_active, tenant_id, create_by, update_by)
VALUES
(1, '标准维修流程', '适用于所有标准维修业务的通用流程', 'STANDARD', -1, 'ALL', true, 1, '', ''),
(2, '快速维修流程', '适用于小型维修业务的快速流程', 'FAST', 1, 'ALL', true, 1, '', '');

-- 初始化活动节点定义
INSERT INTO activity_definition
(id, activity_code, activity_name, description, sequence, is_enabled, create_by, update_by)
VALUES
(1, 'VEHICLE_HANDOVER', '车辆交接', '接收客户车辆并进行初步检查', 1, true, '', ''),
(2, 'PRE_INSPECTION', '进保预审', '对车辆进行详细检查和预审', 2, true, '', ''),
(3, 'REPAIR_QUOTATION', '维修报价', '根据检查结果提供维修方案和报价', 3, true, '', ''),
(4, 'LOSS_ASSESSMENT', '核损核价', '对车辆损坏情况进行评估和定价', 5, true, '', ''),
(5, 'REPAIR_PROCESS', '维修施工', '按照确认的方案进行维修', 5, true, '', ''),
(6, 'QUALITY_CHECK', '质量检验', '对维修结果进行质量检查', 6, true, '', ''),
(7, 'VEHICLE_RETURN', '车辆返还', '将修好的车辆返还给客户', 7, true, '', '');

-- 初始化节点状态定义
INSERT INTO activity_status
(id, status_code, status_name, description, create_by, update_by)
VALUES
(1, 'UNPROCESSED', '未处理', '节点尚未开始处理', '', ''),
(2, 'PROCESSING', '处理中', '节点正在处理中', '', ''),
(3, 'COMPLETED', '已完成', '节点处理已完成', '', ''),
(4, 'REJECTED', '已驳回', '节点处理被驳回', '', ''),
(5, 'SUSPENDED', '已暂停', '节点处理暂停', '', ''),
(6, 'CLOSED', '已关闭', '节点处理已关闭', '', '');

-- 初始化流程模板活动节点关联
INSERT INTO workflow_activity
(workflow_id, activity_code, create_by, update_by)
VALUES
-- 标准维修流程
(1, 1, '', ''), -- 车辆交接
(1, 2, '', ''), -- 进保预审
(1, 3, '', ''), -- 维修报价
(1, 4, '', ''), -- 客户确认
(1, 5, '', ''), -- 维修施工
(1, 6, '', ''), -- 质量检验
(1, 7, '', ''), -- 车辆返还
-- 快速维修流程
(2, 1, '', ''), -- 车辆交接
(2, 3, '', ''), -- 维修报价
(2, 4, '', ''), -- 客户确认
(2, 5, '', ''), -- 维修施工
(2, 7, '', ''); -- 车辆返还

-- 初始化活动节点转换规则
INSERT INTO activity_transition
(workflow_id, from_activity_code, to_activity_code, trigger_event, condition_handler, handler_class, description, tenant_id, create_by, update_by)
VALUES
-- 标准维修流程转换规则
(1, 1, 2, 'COMPLETE_HANDOVER', NULL, 'com.extracme.saas.autocare.workflow.handler.CompleteHandoverHandler', '完成车辆交接后进入进保预审', 1, '', ''),
(1, 2, 3, 'COMPLETE_INSPECTION', NULL, 'com.extracme.saas.autocare.workflow.handler.CompleteInspectionHandler', '完成预审后进入维修报价', 1, '', ''),
(1, 3, 4, 'SUBMIT_QUOTE', NULL, 'com.extracme.saas.autocare.workflow.handler.SubmitQuoteHandler', '提交报价后等待客户确认', 1, '', ''),
(1, 4, 5, 'CUSTOMER_APPROVED', NULL, 'com.extracme.saas.autocare.workflow.handler.CustomerApprovalHandler', '客户确认后进入维修施工', 1, '', ''),
(1, 5, 6, 'COMPLETE_REPAIR', NULL, 'com.extracme.saas.autocare.workflow.handler.CompleteRepairHandler', '完成维修后进入质量检验', 1, '', ''),
(1, 6, 7, 'PASS_QUALITY_CHECK', NULL, 'com.extracme.saas.autocare.workflow.handler.QualityCheckHandler', '通过质检后进入车辆返还', 1, '', ''),
-- 快速维修流程转换规则
(2, 1, 3, 'COMPLETE_HANDOVER', NULL, 'com.extracme.saas.autocare.workflow.handler.FastCompleteHandoverHandler', '完成车辆交接后直接进入维修报价', 1, '', ''),
(2, 3, 4, 'SUBMIT_QUOTE', NULL, 'com.extracme.saas.autocare.workflow.handler.FastSubmitQuoteHandler', '提交报价后等待客户确认', 1, '', ''),
(2, 4, 5, 'CUSTOMER_APPROVED', NULL, 'com.extracme.saas.autocare.workflow.handler.FastCustomerApprovalHandler', '客户确认后进入维修施工', 1, '', ''),
(2, 5, 7, 'COMPLETE_REPAIR', NULL, 'com.extracme.saas.autocare.workflow.handler.FastCompleteRepairHandler', '完成维修后直接进入车辆返还', 1, '', '');

-- 初始化节点状态转换规则
INSERT INTO activity_status_transition
(activity_transition_id, from_status_code, to_status_code, description, create_by, update_by)
VALUES
-- 标准维修流程状态转换规则
(1, 1, 2, '开始处理车辆交接', '', ''),
(1, 2, 3, '完成车辆交接', '', ''),
(2, 1, 2, '开始进保预审', '', ''),
(2, 2, 3, '完成进保预审', '', ''),
(3, 1, 2, '开始维修报价', '', ''),
(3, 2, 3, '完成维修报价', '', ''),
(4, 1, 2, '等待客户确认', '', ''),
(4, 2, 3, '客户已确认', '', ''),
(5, 1, 2, '开始维修施工', '', ''),
(5, 2, 3, '完成维修施工', '', ''),
(6, 1, 2, '开始质量检验', '', ''),
(6, 2, 3, '完成质量检验', '', '');

-- 初始化验证码表
INSERT INTO sys_verification_code
(mobile, code, type, status, expire_time, created_time, update_time)
VALUES
('***********', '123456', 'LOGIN', 0, '2023-12-31 23:59:59', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 初始化短信发送记录表
INSERT INTO sms_send_record
(phone_number, ip_address, type, create_time)
VALUES
('***********', '***********', 'LOGIN', CURRENT_TIMESTAMP);

-- 初始化流程实例表
INSERT INTO workflow_instance
(workflow_id, business_id, tenant_id, current_activity_code, status_code, create_time, create_by, update_time, update_by)
VALUES
(1, 'BUSINESS_001', 1, 1, 'UNPROCESSED', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '');

-- 初始化活动实例记录表
INSERT INTO activity_instance
(instance_id, to_activity_code, from_activity_code, transition_id, start_time, end_time, duration, current_status_code, operator, remarks, create_time, create_by, update_time, update_by)
VALUES
(1, 1, NULL, NULL, CURRENT_TIMESTAMP, NULL, NULL, 'UNPROCESSED', 'admin', 'Initial activity', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '');

-- 初始化流程实例进度表
INSERT INTO workflow_instant_progress
(instance_id, activity_code, status_code, remarks, create_time, create_by, update_time, update_by)
VALUES
(1, 1, 'UNPROCESSED', 'Initial progress', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '');
