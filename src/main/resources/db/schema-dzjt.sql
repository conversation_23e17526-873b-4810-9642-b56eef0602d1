-- 创建数据库
CREATE DATABASE IF NOT EXISTS `auto_care_dzjt` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE `auto_care_dzjt`;

-- 模块1：维修厂管理
-- 1.1 维修厂基本信息表
CREATE TABLE `mtc_repair_depot_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `sso_user_id` BIGINT NOT NULL DEFAULT '-1' COMMENT '单点用户ID',
    `repair_depot_id` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '修理厂ID',
    `repair_depot_sap_code` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '维修厂sap编码',
    `repair_depot_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '修理厂名称',
    `repair_depot_org_id` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '维修厂所属组织机构ID',
    `repair_depot_account` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '修理厂账号',
    `repair_depot_grade` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '修理厂等级',
    `repair_depot_type` INT NOT NULL DEFAULT '1' COMMENT '修理厂类型(1:合作修理厂 2:非合作修理厂)',
    `is_show` INT NOT NULL DEFAULT '2' COMMENT '是否对外展示(1:展示 2:不展示)',
    `maintenance_point` VARCHAR(1) NOT NULL DEFAULT '1' COMMENT '是否保养点(0:否 1:是)',
    `province_id` BIGINT DEFAULT '-1' COMMENT '修理厂地址（省）ID',
    `city_id` BIGINT DEFAULT '-1' COMMENT '修理厂地址（市）ID',
    `area_id` BIGINT DEFAULT '-1' COMMENT '修理厂地址（区）ID',
    `address` VARCHAR(100) DEFAULT '' COMMENT '修理厂详细地址',
    `tax_rate` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '税率',
    `linkman_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '负责人',
    `cooperation_mode` VARCHAR(100) DEFAULT '' COMMENT '合作模式',
    `accident_contacts` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '事故联系人',
    `accident_tel` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '事故联系电话',
    `maintenance_contacts` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '维保联系人',
    `maintenance_tel` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '维保联系电话',
    `repair_depot_longitude` DECIMAL(12,6) DEFAULT '0.000000' COMMENT '修理厂经度',
    `repair_depot_latitude` DECIMAL(12,6) DEFAULT '0.000000' COMMENT '修理厂纬度',
    `vehicle_model_all_flag` DECIMAL(1,0) NOT NULL DEFAULT '0' COMMENT '车型是否全选标记(0:没有全选 1:全部选中)',
    `sso_create_time` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '单点用户创建时间',
    `sso_update_time` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '单点用户修改时间',
    `old_repair_factory_code` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '负责人手机号',
    `status` DECIMAL(1,0) NOT NULL DEFAULT '1' COMMENT '状态(1:有效 0:无效)',
    `del_flag` DECIMAL(1,0) NOT NULL DEFAULT '0' COMMENT '逻辑删除状态(1:已删除 0:未删除)',
    `remark` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注',
    `sso_flag` DECIMAL(1,0) NOT NULL DEFAULT '1' COMMENT '单点同步标志(1:从单点同步 2:手动从老平台移植)',
    `can_repair_item` VARCHAR(1) NOT NULL DEFAULT '0' COMMENT '可修类目(0:全部 1:外观 2:易损件)',
    `warranty_point` VARCHAR(1) NOT NULL DEFAULT '0' COMMENT '是否保修点(0:否 1:是)',
    `business_type` BIGINT NOT NULL DEFAULT '0' COMMENT '营业时间设定(0:全天 1:其他)',
    `business_start_time` BIGINT NOT NULL DEFAULT '-1' COMMENT '营业开始时间(每隔30分钟加1，0点0分:1 23:30:48)',
    `business_end_time` BIGINT NOT NULL DEFAULT '-1' COMMENT '营业结束时间(每隔30分钟加1，0点0分:1 23:30:48)',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_sso_user_id` (`sso_user_id`),
    KEY `idx_repair_depot_id` (`repair_depot_id`)
) ENGINE=InnoDB AUTO_INCREMENT=757 DEFAULT CHARSET=utf8mb4 COMMENT='维修厂信息表';

-- 1.2 维修厂合作分公司表
CREATE TABLE `mtc_repair_depot_cooperative_branch` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `repair_depot_id` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '修理厂ID',
    `org_id` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '组织机构ID',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1401 DEFAULT CHARSET=utf8mb4 COMMENT='维修厂合作分公司表';

-- 1.3 维修厂可修理车型表
CREATE TABLE `mtc_repair_depot_vehicle_model_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `repair_depot_id` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '修理厂ID',
    `vehicle_model_seq` BIGINT NOT NULL DEFAULT '-1' COMMENT '车型SEQ',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=95443 DEFAULT CHARSET=utf8mb4 COMMENT='维修厂可修理车型表';

-- 1.4 维修厂可保修车型表
CREATE TABLE `mtc_repair_depot_warranty_vehicle_model_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `repair_depot_id` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '修理厂ID',
    `vehicle_model_seq` BIGINT NOT NULL DEFAULT '-1' COMMENT '车型SEQ',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35400 DEFAULT CHARSET=utf8mb4 COMMENT='维修厂可保修车型表';

-- 初始化数据
-- 1. 维修厂基本信息表数据
INSERT INTO `mtc_repair_depot_info` 
(`sso_user_id`, `repair_depot_id`, `repair_depot_sap_code`, `repair_depot_name`, `repair_depot_org_id`, 
`repair_depot_account`, `repair_depot_grade`, `repair_depot_type`, `is_show`, `maintenance_point`,
`province_id`, `city_id`, `area_id`, `address`, `tax_rate`, `linkman_name`, `cooperation_mode`,
`accident_contacts`, `accident_tel`, `maintenance_contacts`, `maintenance_tel`, 
`repair_depot_longitude`, `repair_depot_latitude`, `vehicle_model_all_flag`, `old_repair_factory_code`,
`status`, `del_flag`, `remark`, `sso_flag`, `can_repair_item`, `warranty_point`, 
`business_type`, `business_start_time`, `business_end_time`, `create_by`, `update_by`)
VALUES
-- 上海某维修厂
(1001, 'RD001', 'SAP001', '上海大众汽车特约维修中心', 'ORG001',
'sh_vw_repair', 'A', 1, 1, '1',
31, 3101, 310115, '上海市浦东新区张江高科技园区科苑路123号', '0.13', '张三', '直营',
'李四', '***********', '王五', '***********',
121.626927, 31.207031, 1, '***********',
1, 0, '上海地区重点维修厂', 1, '0', '1',
0, -1, -1, 'SYSTEM', 'SYSTEM'),

-- 北京某维修厂
(1002, 'RD002', 'SAP002', '北京奔驰授权维修服务中心', 'ORG002',
'bj_benz_repair', 'A+', 1, 1, '1',
11, 1101, 110105, '北京市朝阳区酒仙桥路456号', '0.13', '赵六', '加盟',
'钱七', '***********', '孙八', '***********',
116.494904, 39.947464, 0, '***********',
1, 0, '北京地区高端维修服务中心', 1, '1', '1',
1, 1, 48, 'SYSTEM', 'SYSTEM'),

-- 广州某维修厂
(1003, 'RD003', 'SAP003', '广州丰田4S店维修中心', 'ORG003',
'gz_toyota_repair', 'B+', 1, 1, '1',
44, 4401, 440103, '广州市天河区天河路789号', '0.13', '周九', '合作',
'吴十', '13855555555', '郑十一', '13866666666',
113.326447, 23.135336, 0, '13855555555',
1, 0, '广州地区综合维修中心', 1, '2', '0',
1, 2, 46, 'SYSTEM', 'SYSTEM');

-- 2. 维修厂合作分公司表数据
INSERT INTO `mtc_repair_depot_cooperative_branch` 
(`repair_depot_id`, `org_id`, `create_by`, `update_by`)
VALUES
('RD001', 'ORG001_BR1', 'SYSTEM', 'SYSTEM'),
('RD001', 'ORG001_BR2', 'SYSTEM', 'SYSTEM'),
('RD002', 'ORG002_BR1', 'SYSTEM', 'SYSTEM'),
('RD003', 'ORG003_BR1', 'SYSTEM', 'SYSTEM');

-- 3. 维修厂可修理车型表数据
INSERT INTO `mtc_repair_depot_vehicle_model_info` 
(`repair_depot_id`, `vehicle_model_seq`, `create_by`, `update_by`)
VALUES
-- 上海大众维修厂可修车型
('RD001', 1001, 'SYSTEM', 'SYSTEM'),  -- 大众帕萨特
('RD001', 1002, 'SYSTEM', 'SYSTEM'),  -- 大众途观
('RD001', 1003, 'SYSTEM', 'SYSTEM'),  -- 大众高尔夫

-- 北京奔驰维修厂可修车型
('RD002', 2001, 'SYSTEM', 'SYSTEM'),  -- 奔驰C级
('RD002', 2002, 'SYSTEM', 'SYSTEM'),  -- 奔驰E级
('RD002', 2003, 'SYSTEM', 'SYSTEM'),  -- 奔驰GLC

-- 广州丰田维修厂可修车型
('RD003', 3001, 'SYSTEM', 'SYSTEM'),  -- 丰田凯美瑞
('RD003', 3002, 'SYSTEM', 'SYSTEM'),  -- 丰田RAV4
('RD003', 3003, 'SYSTEM', 'SYSTEM'); -- 丰田卡罗拉

-- 4. 维修厂可保修车型表数据
INSERT INTO `mtc_repair_depot_warranty_vehicle_model_info`
(`repair_depot_id`, `vehicle_model_seq`, `create_by`, `update_by`)
VALUES
-- 上海大众维修厂保修车型
('RD001', 1001, 'SYSTEM', 'SYSTEM'),  -- 大众帕萨特
('RD001', 1002, 'SYSTEM', 'SYSTEM'),  -- 大众途观

-- 北京奔驰维修厂保修车型
('RD002', 2001, 'SYSTEM', 'SYSTEM'),  -- 奔驰C级
('RD002', 2002, 'SYSTEM', 'SYSTEM'),  -- 奔驰E级

-- 广州丰田维修厂保修车型
('RD003', 3001, 'SYSTEM', 'SYSTEM'),  -- 丰田凯美瑞
('RD003', 3002, 'SYSTEM', 'SYSTEM'); -- 丰田RAV4

-- 创建新租户数据库（Extracme公司）
CREATE DATABASE IF NOT EXISTS `auto_care_extracme` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE `auto_care_extracme`;

-- 复制表结构
-- 1.1 维修厂基本信息表
CREATE TABLE `mtc_repair_depot_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `sso_user_id` BIGINT NOT NULL DEFAULT '-1' COMMENT '单点用户ID',
    `repair_depot_id` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '修理厂ID',
    `repair_depot_sap_code` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '维修厂sap编码',
    `repair_depot_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '修理厂名称',
    `repair_depot_org_id` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '维修厂所属组织机构ID',
    `repair_depot_account` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '修理厂账号',
    `repair_depot_grade` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '修理厂等级',
    `repair_depot_type` INT NOT NULL DEFAULT '1' COMMENT '修理厂类型(1:合作修理厂 2:非合作修理厂)',
    `is_show` INT NOT NULL DEFAULT '2' COMMENT '是否对外展示(1:展示 2:不展示)',
    `maintenance_point` VARCHAR(1) NOT NULL DEFAULT '1' COMMENT '是否保养点(0:否 1:是)',
    `province_id` BIGINT DEFAULT '-1' COMMENT '修理厂地址（省）ID',
    `city_id` BIGINT DEFAULT '-1' COMMENT '修理厂地址（市）ID',
    `area_id` BIGINT DEFAULT '-1' COMMENT '修理厂地址（区）ID',
    `address` VARCHAR(100) DEFAULT '' COMMENT '修理厂详细地址',
    `tax_rate` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '税率',
    `linkman_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '负责人',
    `cooperation_mode` VARCHAR(100) DEFAULT '' COMMENT '合作模式',
    `accident_contacts` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '事故联系人',
    `accident_tel` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '事故联系电话',
    `maintenance_contacts` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '维保联系人',
    `maintenance_tel` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '维保联系电话',
    `repair_depot_longitude` DECIMAL(12,6) DEFAULT '0.000000' COMMENT '修理厂经度',
    `repair_depot_latitude` DECIMAL(12,6) DEFAULT '0.000000' COMMENT '修理厂纬度',
    `vehicle_model_all_flag` DECIMAL(1,0) NOT NULL DEFAULT '0' COMMENT '车型是否全选标记(0:没有全选 1:全部选中)',
    `sso_create_time` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '单点用户创建时间',
    `sso_update_time` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '单点用户修改时间',
    `old_repair_factory_code` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '负责人手机号',
    `status` DECIMAL(1,0) NOT NULL DEFAULT '1' COMMENT '状态(1:有效 0:无效)',
    `del_flag` DECIMAL(1,0) NOT NULL DEFAULT '0' COMMENT '逻辑删除状态(1:已删除 0:未删除)',
    `remark` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注',
    `sso_flag` DECIMAL(1,0) NOT NULL DEFAULT '1' COMMENT '单点同步标志(1:从单点同步 2:手动从老平台移植)',
    `can_repair_item` VARCHAR(1) NOT NULL DEFAULT '0' COMMENT '可修类目(0:全部 1:外观 2:易损件)',
    `warranty_point` VARCHAR(1) NOT NULL DEFAULT '0' COMMENT '是否保修点(0:否 1:是)',
    `business_type` BIGINT NOT NULL DEFAULT '0' COMMENT '营业时间设定(0:全天 1:其他)',
    `business_start_time` BIGINT NOT NULL DEFAULT '-1' COMMENT '营业开始时间(每隔30分钟加1，0点0分:1 23:30:48)',
    `business_end_time` BIGINT NOT NULL DEFAULT '-1' COMMENT '营业结束时间(每隔30分钟加1，0点0分:1 23:30:48)',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_sso_user_id` (`sso_user_id`),
    KEY `idx_repair_depot_id` (`repair_depot_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='维修厂信息表';

-- 1.2 维修厂合作分公司表
CREATE TABLE `mtc_repair_depot_cooperative_branch` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `repair_depot_id` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '修理厂ID',
    `org_id` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '组织机构ID',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='维修厂合作分公司表';

-- 1.3 维修厂可修理车型表
CREATE TABLE `mtc_repair_depot_vehicle_model_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `repair_depot_id` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '修理厂ID',
    `vehicle_model_seq` BIGINT NOT NULL DEFAULT '-1' COMMENT '车型SEQ',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='维修厂可修理车型表';

-- 1.4 维修厂可保修车型表
CREATE TABLE `mtc_repair_depot_warranty_vehicle_model_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `repair_depot_id` VARCHAR(30) NOT NULL DEFAULT '' COMMENT '修理厂ID',
    `vehicle_model_seq` BIGINT NOT NULL DEFAULT '-1' COMMENT '车型SEQ',
    `create_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='维修厂可保修车型表';

-- Extracme公司的初始化数据
-- 1. 维修厂基本信息表数据
INSERT INTO `mtc_repair_depot_info` 
(`sso_user_id`, `repair_depot_id`, `repair_depot_sap_code`, `repair_depot_name`, `repair_depot_org_id`, 
`repair_depot_account`, `repair_depot_grade`, `repair_depot_type`, `is_show`, `maintenance_point`,
`province_id`, `city_id`, `area_id`, `address`, `tax_rate`, `linkman_name`, `cooperation_mode`,
`accident_contacts`, `accident_tel`, `maintenance_contacts`, `maintenance_tel`, 
`repair_depot_longitude`, `repair_depot_latitude`, `vehicle_model_all_flag`, `old_repair_factory_code`,
`status`, `del_flag`, `remark`, `sso_flag`, `can_repair_item`, `warranty_point`, 
`business_type`, `business_start_time`, `business_end_time`, `create_by`, `update_by`)
VALUES
-- 深圳某维修厂
(2001, 'RD101', 'SAP101', '深圳宝马授权服务中心', 'ORG101',
'sz_bmw_repair', 'A', 1, 1, '1',
44, 4403, 440305, '深圳市南山区科技园路888号', '0.13', '陈一', '直营',
'林二', '***********', '黄三', '***********',
114.057868, 22.543099, 1, '***********',
1, 0, '深圳地区豪华车维修中心', 1, '0', '1',
0, -1, -1, 'SYSTEM', 'SYSTEM'),

-- 成都某维修厂
(2002, 'RD102', 'SAP102', '成都奥迪维修服务中心', 'ORG102',
'cd_audi_repair', 'A+', 1, 1, '1',
51, 5101, 510107, '成都市武侯区天府大道999号', '0.13', '杨四', '加盟',
'吴五', '***********', '刘六', '***********',
104.065735, 30.572269, 0, '***********',
1, 0, '成都地区德系车维修中心', 1, '1', '1',
1, 1, 48, 'SYSTEM', 'SYSTEM'),

-- 杭州某维修厂
(2003, 'RD103', 'SAP103', '杭州沃尔沃专修中心', 'ORG103',
'hz_volvo_repair', 'B+', 1, 1, '1',
33, 3301, 330106, '杭州市西湖区文三路777号', '0.13', '郑七', '合作',
'王八', '13955555555', '冯九', '13966666666',
120.153576, 30.287459, 0, '13955555555',
1, 0, '杭州地区进口车维修中心', 1, '2', '0',
1, 2, 46, 'SYSTEM', 'SYSTEM');

-- 2. 维修厂合作分公司表数据
INSERT INTO `mtc_repair_depot_cooperative_branch` 
(`repair_depot_id`, `org_id`, `create_by`, `update_by`)
VALUES
('RD101', 'ORG101_BR1', 'SYSTEM', 'SYSTEM'),
('RD101', 'ORG101_BR2', 'SYSTEM', 'SYSTEM'),
('RD102', 'ORG102_BR1', 'SYSTEM', 'SYSTEM'),
('RD103', 'ORG103_BR1', 'SYSTEM', 'SYSTEM');

-- 3. 维修厂可修理车型表数据
INSERT INTO `mtc_repair_depot_vehicle_model_info` 
(`repair_depot_id`, `vehicle_model_seq`, `create_by`, `update_by`)
VALUES
-- 深圳宝马维修厂可修车型
('RD101', 4001, 'SYSTEM', 'SYSTEM'),  -- 宝马3系
('RD101', 4002, 'SYSTEM', 'SYSTEM'),  -- 宝马5系
('RD101', 4003, 'SYSTEM', 'SYSTEM'),  -- 宝马X5

-- 成都奥迪维修厂可修车型
('RD102', 5001, 'SYSTEM', 'SYSTEM'),  -- 奥迪A4L
('RD102', 5002, 'SYSTEM', 'SYSTEM'),  -- 奥迪A6L
('RD102', 5003, 'SYSTEM', 'SYSTEM'),  -- 奥迪Q5L

-- 杭州沃尔沃维修厂可修车型
('RD103', 6001, 'SYSTEM', 'SYSTEM'),  -- 沃尔沃S90
('RD103', 6002, 'SYSTEM', 'SYSTEM'),  -- 沃尔沃XC60
('RD103', 6003, 'SYSTEM', 'SYSTEM'); -- 沃尔沃XC90

-- 4. 维修厂可保修车型表数据
INSERT INTO `mtc_repair_depot_warranty_vehicle_model_info`
(`repair_depot_id`, `vehicle_model_seq`, `create_by`, `update_by`)
VALUES
-- 深圳宝马维修厂保修车型
('RD101', 4001, 'SYSTEM', 'SYSTEM'),  -- 宝马3系
('RD101', 4002, 'SYSTEM', 'SYSTEM'),  -- 宝马5系

-- 成都奥迪维修厂保修车型
('RD102', 5001, 'SYSTEM', 'SYSTEM'),  -- 奥迪A4L
('RD102', 5002, 'SYSTEM', 'SYSTEM'),  -- 奥迪A6L

-- 杭州沃尔沃维修厂保修车型
('RD103', 6001, 'SYSTEM', 'SYSTEM'),  -- 沃尔沃S90
('RD103', 6002, 'SYSTEM', 'SYSTEM'); -- 沃尔沃XC60

-- 模块2：维修任务模块
-- 2.1 维修任务表
CREATE TABLE `mtc_repair_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_no` varchar(50) NOT NULL DEFAULT '' COMMENT '任务编号',
  `task_type` decimal(1,0) NOT NULL DEFAULT '1' COMMENT '任务类型（1：从调度直接推送过来的任务 2：手动刷数据从老平台移植过来的任务 3： 内部生成）',
  `org_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '车辆运营机构ID',
  `org_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '车辆运营机构名称',
  `operate_org_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '车辆所属组织机构ID',
  `operate_org_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '车辆所属组织机构名称',
  `vehicle_no` varchar(20) NOT NULL DEFAULT '' COMMENT '车牌号',
  `vehicle_model_seq` bigint NOT NULL DEFAULT '-1' COMMENT '车型ID',
  `vehicle_model_info` varchar(100) NOT NULL DEFAULT '' COMMENT '车型名称',
  `vin` varchar(20) NOT NULL DEFAULT '' COMMENT '车架号',
  `insurance_company_name` varchar(50) NOT NULL DEFAULT '' COMMENT '车辆保险所属',
  `repair_type_id` int NOT NULL DEFAULT '0' COMMENT '修理类型ID（0：自建任务 1：进保维修 2：自费维修 3：车辆保养 4：轮胎任务 6：常规保养）',
  `repair_type_name` varchar(50) NOT NULL DEFAULT '' COMMENT '修理类型名称',
  `repair_grade` varchar(50) NOT NULL DEFAULT '' COMMENT '修理级别 A,B,C',
  `repair_depot_id` varchar(30) NOT NULL DEFAULT '' COMMENT '修理厂ID',
  `repair_depot_name` varchar(100) NOT NULL DEFAULT '' COMMENT '修理厂名称',
  `repair_depot_org_id` varchar(20) NOT NULL DEFAULT '' COMMENT '修理厂组织机构ID',
  `repair_depot_sap_code` varchar(50) NOT NULL DEFAULT '' COMMENT '供应商sap编号',
  `sap_send_status` int NOT NULL DEFAULT '0' COMMENT 'sap数据发送状态 0未发送 1已发送 2发送失败 3冲销需重新发送 4发送失败需重新发送 5发送中',
  `sap_sync_flag` int NOT NULL DEFAULT '0' COMMENT 'sap同步标识 0自动同步 1手动同步',
  `sap_success_time` datetime DEFAULT NULL COMMENT 'sap发送成功时间',
  `task_inflow_time` datetime DEFAULT NULL COMMENT '调度任务创建时间',
  `vehicle_recive_time` datetime DEFAULT NULL COMMENT '车辆接收时间',
  `vehicle_check_time` datetime DEFAULT NULL COMMENT '车辆验收完成时间',
  `vehicle_repair_time` datetime DEFAULT NULL COMMENT '车辆修理完成时间',
  `reassignment_pass_time` datetime DEFAULT NULL COMMENT '改派审核通过时间',
  `settle_closing_time` datetime DEFAULT NULL COMMENT '结案时间',
  `expected_repair_days` bigint NOT NULL DEFAULT '0' COMMENT '预计修理天数',
  `expected_repair_complete` datetime DEFAULT NULL COMMENT '预计修理完成日期',
  `accident_report_number` varchar(200) NOT NULL DEFAULT '' COMMENT '事故报案号',
  `repair_flag` decimal(1,0) NOT NULL DEFAULT '0' COMMENT '是否需要维修 0:否 1:是',
  `terminal_id` varchar(50) NOT NULL DEFAULT '' COMMENT '终端ID',
  `total_mileage` decimal(24,2) DEFAULT NULL COMMENT '总里程数',
  `terminal_mileage` varchar(30) NOT NULL DEFAULT '' COMMENT '终端实时里程数(核损时)',
  `associated_order` varchar(50) NOT NULL DEFAULT '' COMMENT '关联订单',
  `order_type` int NOT NULL DEFAULT '0' COMMENT '订单类型 1=长租订单 2=门店订单 3=渠道订单 4=内部订单 5=分时订单',
  `relate_type` int NOT NULL DEFAULT '0' COMMENT '关联类型： 1=订单匹配 2=手工关联 3=无关联订单',
  `order_remark` varchar(255) DEFAULT '' COMMENT '关联订单备注',
  `violation_name` varchar(50) NOT NULL DEFAULT '' COMMENT '违章联系人（长租订单）',
  `violation_tel_no` varchar(50) NOT NULL DEFAULT '' COMMENT '违章联系人联系方式（长租订单）',
  `vehicle_client_maintenance_fee` tinyint NOT NULL DEFAULT '0' COMMENT '是否客户承担维修费	1是 2否（长租订单）',
  `client_inspect_tag` tinyint NOT NULL DEFAULT '0' COMMENT '是否客户承担年检费 1=是 2=否（长租订单）',
  `client_upkeep_tag` tinyint NOT NULL DEFAULT '0' COMMENT '是否客户承担保养费 1=是 2=否（长租订单）',
  `auth_id` varchar(20) NOT NULL DEFAULT '' COMMENT '会员id',
  `no_deductibles_flag` int NOT NULL DEFAULT '-1' COMMENT '是否购买不计免赔 0:否 1:是',
  `service_type` int NOT NULL DEFAULT '0' COMMENT '服务类别 1-基础服务 2-优享服务 3-尊享服务 （门店订单）',
  `service_content` varchar(255) NOT NULL DEFAULT '' COMMENT '保障内容（门店订单）',
  `driver_name` varchar(20) NOT NULL DEFAULT '' COMMENT '驾驶员姓名',
  `driver_tel` varchar(20) NOT NULL DEFAULT '' COMMENT '驾驶员手机号',
  `routing_inspection_name` varchar(20) NOT NULL DEFAULT '' COMMENT '巡检姓名',
  `routing_inspection_tel` varchar(20) NOT NULL DEFAULT '' COMMENT '巡检手机号',
  `damaged_part_describe` varchar(200) NOT NULL DEFAULT '' COMMENT '受损部位描述',
  `accident_describe` varchar(200) NOT NULL DEFAULT '' COMMENT '事故描述',
  `trailer_flag` decimal(1,0) NOT NULL DEFAULT '0' COMMENT '是否有拖车 0:否 1:是',
  `repair_review_total_amount` decimal(9,2) DEFAULT NULL COMMENT '进保预审金额合计',
  `repair_replace_total_amount` decimal(9,2) DEFAULT '0.00' COMMENT '修理厂换件金额合计',
  `repair_repair_total_amount` decimal(9,2) DEFAULT '0.00' COMMENT '修理厂修理金额合计',
  `repair_insurance_total_amount` decimal(9,2) DEFAULT '0.00' COMMENT '修理厂定损总计',
  `vehicle_replace_total_amount` decimal(9,2) DEFAULT '0.00' COMMENT '车管换件金额合计',
  `vehicle_repair_total_amount` decimal(9,2) DEFAULT '0.00' COMMENT '车管修理金额合计',
  `vehicle_insurance_total_amount` decimal(9,2) DEFAULT '0.00' COMMENT '车管定损总计',
  `vehicle_manage_view_flag` decimal(1,0) NOT NULL DEFAULT '0' COMMENT '车管核价意见 0:同意报价 1:异议',
  `resurvey_flag` decimal(1,0) NOT NULL DEFAULT '0' COMMENT '是否需要复勘 0:否 1:是',
  `resurvey_part` varchar(50) NOT NULL DEFAULT '' COMMENT '复勘部位',
  `recovery_flag` decimal(1,0) NOT NULL DEFAULT '0' COMMENT '是否需要向用户追偿 0:否 1:是',
  `duty_situation` int NOT NULL DEFAULT '-1' COMMENT '责任情况 1:全责 2:主责 3:次责 4:平责 5:无责',
  `recovery_amount` decimal(9,2) DEFAULT NULL COMMENT '向用户追偿费用',
  `insurance_amount` decimal(9,2) DEFAULT NULL COMMENT '保险上付费',
  `acc_dep_amount` decimal(9,2) DEFAULT NULL COMMENT '加速折旧费',
  `outage_loss_amount` decimal(9,2) DEFAULT NULL COMMENT '停运损失费',
  `vehicle_loss_amount` decimal(9,2) DEFAULT NULL COMMENT '车辆损失费',
  `trailer_rescue_amount` decimal(9,2) DEFAULT NULL COMMENT '拖车救援费',
  `maintain_amount` decimal(9,2) DEFAULT NULL COMMENT '保养费用',
  `loss_order_amount` decimal(9,2) DEFAULT NULL COMMENT '定损单金额',
  `reassignment_repair_org_id` varchar(30) NOT NULL DEFAULT '' COMMENT '改派修理厂组织机构ID',
  `reassignment_reasons` varchar(500) NOT NULL DEFAULT '' COMMENT '改派原因',
  `reassignment_reject_reasons` varchar(500) NOT NULL DEFAULT '' COMMENT '改派驳回原因',
  `verification_reject_reasons` varchar(100) NOT NULL DEFAULT '' COMMENT '核损驳回原因',
  `verification_reject_reasons_detail` varchar(500) NOT NULL DEFAULT '' COMMENT '核损驳回原因详情',
  `verification_reject_level` varchar(10) NOT NULL DEFAULT '' COMMENT '核损驳回时等级',
  `check_result_flag` decimal(1,0) NOT NULL DEFAULT '-1' COMMENT '车辆验收结果 0:验收通过 1:验收不通过',
  `check_unqualified_reason` varchar(500) NOT NULL DEFAULT '' COMMENT '验收不合格原因',
  `confirm_type` int NOT NULL DEFAULT '0' COMMENT '自费和进保任务确认类型（1公司自费2客户付费3我方有责4我方无责）',
  `maintain_to_repair_flag` decimal(1,0) NOT NULL DEFAULT '0' COMMENT '保养转维修标志 -1:待选择0:否 1:是',
  `verification_loss_task_oper_id` bigint DEFAULT NULL COMMENT '核损核价任务占有人ID',
  `examine_level` decimal(1,0) NOT NULL DEFAULT '0' COMMENT '是否提交上级 0:未提交 1:已提交',
  `verification_loss_check_time` datetime DEFAULT NULL COMMENT '核损通过时间',
  `verification_loss_check_id` bigint DEFAULT NULL COMMENT '一级业务处理人',
  `over_time_reasons` varchar(500) DEFAULT '' COMMENT '超时原因',
  `repair_total_amount_first` decimal(9,2) DEFAULT NULL COMMENT '首次报价金额',
  `nuclear_loss_reversion_flag` int DEFAULT '0' COMMENT '是否驳回标识',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `tireNumber` bigint NOT NULL DEFAULT '0' COMMENT '轮胎数量',
  `tire_unit_price` decimal(9,2) NOT NULL DEFAULT '0.00' COMMENT '轮胎单价',
  `tire_brand` varchar(50) NOT NULL DEFAULT '' COMMENT '轮胎品牌',
  `associated_task_no` varchar(30) NOT NULL DEFAULT '' COMMENT '关联任务编号',
  `insurance_flag` int NOT NULL DEFAULT '0' COMMENT '是否进保（0：否 1：是）',
  `advanced_audit_leve` tinyint(1) NOT NULL DEFAULT '1' COMMENT '二级审核级别 2:待运营经理审核 3:待资产管理部审核 4:待运营管理部审核 5:资产管理已退回 6:运营管理部已退回',
  `already_incoming_amount` decimal(10,2) DEFAULT '0.00' COMMENT '已来款金额(元)',
  `already_pay_amount` decimal(10,2) DEFAULT '0.00' COMMENT '已支付金额(元)',
  `wait_pay_amount` decimal(10,2) DEFAULT '0.00' COMMENT '待支付金额(元)',
  `must_claim_amount` decimal(10,2) DEFAULT '0.00' COMMENT '应理赔金额总计(元)',
  `must_pay_amount` decimal(10,2) DEFAULT '0.00' COMMENT '应支付金额总计(元)',
  `vehicle_repair_cost_amount` double(9,2) DEFAULT '0.00' COMMENT '维修成本金额',
  `repair_settle_amount` double(9,2) DEFAULT '0.00' COMMENT '结算金额',
  `repair_fax_settle_amount` double(9,2) DEFAULT '0.00' COMMENT '税前结算金额',
  `transfer_flag` int DEFAULT '0' COMMENT '确定转为收入(0：未确认 1：已确认)',
  `rational_indemnity_cnt` int NOT NULL DEFAULT '0' COMMENT '理赔款关联计数',
  `accident_day_time` date DEFAULT NULL COMMENT '事故发生时间',
  `deduct_flag` int DEFAULT '0' COMMENT '免赔结算状态(0:无 1:未结算 2:已结算)',
  `origin` int NOT NULL DEFAULT '0' COMMENT '任务来源（0：调度系统 1：车管系统）',
  `renttype` int NOT NULL DEFAULT '0' COMMENT '车辆业务状态（-1：未出库 0：分时租赁 1：长租 3：短租 4：公务用车）',
  `fact_operate_tag` int NOT NULL DEFAULT '-1' COMMENT '实际运营标签 0.未投车辆 1.短租运营车辆 2.长租运营车辆 3.备库车辆 4.待退运车辆 5.已处置车辆 6.特殊车辆',
  `take_user_name` varchar(20) NOT NULL DEFAULT '' COMMENT '提车人姓名',
  `take_user_phone` varchar(11) NOT NULL DEFAULT '' COMMENT '提车人手机',
  `take_voucher` varchar(1000) NOT NULL DEFAULT '' COMMENT '提车凭证',
  `syn_take_time` datetime DEFAULT NULL COMMENT '提车信息同步时间',
  `close_reason` varchar(100) NOT NULL DEFAULT '' COMMENT '关闭原因',
  `claims_flag` int NOT NULL DEFAULT '0' COMMENT '是否保险理赔状态：0否 1是',
  `estimated_claim_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估理赔金额',
  `car_damage_type` int DEFAULT '-1' COMMENT '车损类型 1 人为损坏 2 自然损耗',
  `vehicle_scrape_value` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '车辆处置残值',
  `review_to_sel_fee_flag` int NOT NULL DEFAULT '0' COMMENT '进保预审转自费状态：0否 1是',
  `confirm_car_damage_type` int NOT NULL DEFAULT '0' COMMENT '确认车损类型 1：我司车辆原因 2：客户原因',
  `accident_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '事故理赔任务编号',
  `insurance_pre_review_time` datetime DEFAULT NULL COMMENT '进保预审通过时间',
  `insurance_pre_review_level` int NOT NULL DEFAULT '1' COMMENT '进保预审阶段',
  `pre_review_vehicle_scrape_value` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预审车辆处置残值',
  `pre_review_vehicle_scrape_time` datetime DEFAULT NULL COMMENT '预审车辆处置残值时间',
  `is_used_applets` varchar(2) NOT NULL DEFAULT '0' COMMENT '是否使用维修小程序 0-否 1-是',
  `criteria_id` bigint DEFAULT NULL COMMENT '标准id',
  `cust_pays_direct` int NOT NULL DEFAULT '0' COMMENT '是否客户直付 0-否 1-是',
  `cust_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '客户直付金额',
  `user_assumed_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '用户承担金额',
  `not_user_assumed_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '非用户承担金额',
  `sync_claim_flag` int NOT NULL DEFAULT '2' COMMENT '是否同步业财理赔金额 1-是 2-否',
  `tax_rate` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '税率',
  `repair_depot_type` int NOT NULL DEFAULT '1' COMMENT '修理厂类型(1:合作修理厂 2:非合作修理厂)',
  `declare_no` varchar(32) CHARACTER SET latin2 COLLATE latin2_general_ci DEFAULT '' COMMENT '关联账单(PO)',
  `declare_status` int DEFAULT '0' COMMENT '申报状态 1=未提交 2=审批中 3=审批通过 4=审批拒绝',
  `declare_settlement_status` int DEFAULT '0' COMMENT '结算状态 1=已生成 2=待结算 3=结算中 4=已结算 5=已作废',
  `settlement_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '关联结算单(GR)',
  `settlement_status` int DEFAULT '0' COMMENT '关联结算单状态 1=未提交 2=审核中 3=审批通过 4=审批拒绝 5=已作废 6=已关闭',
  `send_repair_time` datetime DEFAULT NULL COMMENT '送修时间',
  `property_status` int NOT NULL DEFAULT '-1' COMMENT '资产状态: 0=在建工程, 1=固定资产, 2=固定资产（待报废）, 3=报废, 4=固定资产(待处置), 5=固定资产(已处置), 6=以租代售, 7=库存商品, 8=已处置（未过户）',
  `product_line` int NOT NULL DEFAULT '-1' COMMENT '产品线: 1=车管中心, 2=长租, 3=短租, 4=公务用车',
  `sub_product_line` int NOT NULL DEFAULT '-1' COMMENT '子产品线: 1=携程短租-短租, 2=门店-短租, 3=分时-短租, 4=普通-长租, 5=时行-长租, 6=平台业务-长租, 7=政企业务-长租, 8=网约车业务-长租',
  `self_funded_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '自费金额',
  `continue_days` varchar(100) DEFAULT '' COMMENT '连续停放天数',
  `create_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_repair_depot_name` (`repair_depot_name`),
  KEY `idx_vehicle_no` (`vehicle_no`),
  KEY `idx_vin` (`vin`),
  KEY `idx_task_no` (`task_no`),
  KEY `idx_vehicle_model_seq` (`vehicle_model_seq`),
  KEY `idx_task_inflow_time` (`task_inflow_time`),
  KEY `idx_vehicle_recive_time` (`vehicle_recive_time`),
  KEY `idx_insurance_company_name` (`insurance_company_name`),
  KEY `idx_repair_depot_id` (`repair_depot_id`),
  KEY `idx_auth_id` (`auth_id`),
  KEY `idx_createtime_orgid` (`org_id`),
  KEY `idx_org_id` (`org_id`),
  KEY `idx_vehicle_check_time` (`vehicle_check_time`),
  KEY `idx_accident_no` (`accident_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=893119 DEFAULT CHARSET=utf8mb3 COMMENT='维修任务表';

-- 2.2 维修备注表
CREATE TABLE `mtc_repair_remark` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_no` varchar(50) NOT NULL DEFAULT '' COMMENT '任务编号',
  `repair_stage` decimal(1,0) NOT NULL DEFAULT '-1' COMMENT '维修阶段 1:定损报价 2:核损核价(一级审核) 3:核损核价(审核通过) 4:车辆维修 5:车辆验收 6:轮胎任务',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_no` (`task_no`)
) ENGINE=InnoDB AUTO_INCREMENT=570 DEFAULT CHARSET=utf8mb3 COMMENT='维修备注表';

-- 2.3 维修平台操作日志表
CREATE TABLE `mtc_operator_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `table_name` varchar(30) NOT NULL DEFAULT '' COMMENT '表名',
  `record_id` bigint NOT NULL DEFAULT '-1' COMMENT '记录主键',
  `ope_content` varchar(1000) NOT NULL DEFAULT '' COMMENT '操作内容',
  `current_tache` bigint DEFAULT '-1' COMMENT '当前环节(10:车辆交接 20:定损报价 30:核损核价 40:改派中 50:车辆维修 60:车辆验收 70:资料收集 80:损失登记 90:结算管理)',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=215 DEFAULT CHARSET=utf8mb3 COMMENT='维修平台操作日志';

-- 2.4 维修任务图片/视频表
CREATE TABLE `mtc_vehicle_repair_pic` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_no` varchar(50) NOT NULL DEFAULT '' COMMENT '任务编号',
  `pic_type` decimal(2,0) NOT NULL DEFAULT '0' COMMENT '图片区分 1:损坏部位图片 2:车辆维修图片 10:行驶证图片 11:保单图片 12:事故图片 13:车损图片(标的车) 14:车损图片(三者车) 15:理赔材料 16:其他图片 17:验收视频 18:轮胎更换修复后图片 19:事故责任认定书图片 20:保司定损单 21:我方驾驶证图片 22:维修任务垫付 23:定损视频',
  `pic_url` varchar(200) NOT NULL DEFAULT '' COMMENT '车辆维修图片URL',
  `create_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_no` (`task_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=764272 DEFAULT CHARSET=utf8mb3 COMMENT='维修任务图片/视频表';

-- 2.5 维修任务车辆出厂记录表
CREATE TABLE `mtc_repair_task_leaving_factory` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_no` varchar(50) NOT NULL DEFAULT '' COMMENT '关联任务编号',
  `vin` varchar(20) NOT NULL DEFAULT '' COMMENT '车架号',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '提车人姓名',
  `phone_number` varchar(50) NOT NULL DEFAULT '' COMMENT '提车人联系方式',
  `delivery_time` varchar(50) NOT NULL DEFAULT '' COMMENT '车辆出厂时间',
  `delivery_pictures` varchar(1000) NOT NULL DEFAULT '' COMMENT '出厂图片,多个图片逗号分隔',
  `remark` varchar(1000) NOT NULL DEFAULT '' COMMENT '备注',
  `repair_depot_id` varchar(30) NOT NULL DEFAULT '' COMMENT '修理厂ID',
  `repair_depot_name` varchar(100) NOT NULL DEFAULT '' COMMENT '修理厂名称',
  `repair_depot_org_id` varchar(20) NOT NULL DEFAULT '' COMMENT '修理厂组织机构ID',
  `repair_depot_sap_code` varchar(50) NOT NULL DEFAULT '' COMMENT '供应商sap编号',
  `leaving_status` int NOT NULL DEFAULT '1' COMMENT '维修厂登记状态 1-已登记 2-未登记 3-已关闭',
  `repair_task_inflow_time` datetime DEFAULT NULL COMMENT '维修任务流入时间',
  `repair_task_receive_time` datetime DEFAULT NULL COMMENT '维修任务接车时间',
  `create_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `index_taskNo` (`task_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='维修任务车辆出厂记录表';
