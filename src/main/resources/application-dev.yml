# ========================================
# 开发环境配置 (Development Environment)
# 用于本地开发和调试
# ========================================

spring:
  profiles: dev
  
  # 开发环境数据库配置 - 连接到开发环境MySQL数据库
  datasource:
    url: ************************************************************************************************************************************************************
    username: root                             # 开发环境数据库用户名
    password: 123456                           # 开发环境数据库密码
  
  # 开发环境Redis配置 - 用于缓存用户会话、权限信息等
  redis:
    database: 0                                # Redis数据库索引，开发环境使用0号库
    host: localhost                            # Redis服务器地址，本地开发环境
    port: 6379                                 # Redis服务器端口
    password:                                  # Redis连接密码，开发环境无密码

# 开发环境日志配置 - 使用debug级别便于开发调试
logging:
  level:
    com.extracme.saas.autocare: debug          # 应用主包日志级别，输出详细调试信息
    org.springframework: info                  # Spring框架日志级别，输出重要信息
    org.apache.ibatis: debug                   # MyBatis框架日志级别，输出SQL执行信息
    org.mybatis: debug                         # MyBatis核心日志级别
    com.extracme.saas.autocare.mapper: debug   # Mapper接口日志级别，输出SQL语句
    druid.sql: debug                           # Druid连接池SQL日志，监控数据库操作
    root: info                                 # 根日志级别

# 开发环境外部接口URL配置 - 与第三方系统集成的接口地址
# 精友理赔接口地址 - 用于车辆理赔数据交互（开发环境）
jyInterfaceUrl: http://jingyou-dev.evcard.vip/ClaimCloudProd-app/jyClaim/SipClaimInterfaceServlet?insCode=EXTRACME&requestType=
# 定损刷新页面URL - 定损完成后跳转的页面地址（本地开发环境）
assessmentRefreshURL: http://localhost:8010/repair-management/vehicleRepairModule/taskDetail?id=idReplace
# 评估刷新页面URL - 评估完成后跳转的页面地址（本地开发环境）
evaluateRefreshURL: http://localhost:8010/repair-management/vehicleRepairModule/taskDetail?id=idReplace
# 定损回调接口URL - 第三方定损完成后回调本系统的接口（本地开发环境）
assessmentBackURL: http://localhost:30030/auto-care-saas/api/v1/jing-you/lossAssessmentBack
# 评估回调接口URL - 第三方评估完成后回调本系统的接口（本地开发环境）
evaluateBackURL: http://localhost:30030/auto-care-saas/api/v1/jing-you/evaluateLossBack
# 业财保险接口 （SIT环境）
bfcInsureUrl: https://bx-sit.gcsrental.com/bfc/insur

# 开发环境阿里云OSS对象存储配置 - 用于文件上传和存储
oss:
  endpoint: https://oss-cn-shanghai.aliyuncs.com     # OSS服务端点，上海区域
  access-key-id: LTAI5tJumdVNXHPLszk49yAk           # OSS访问密钥ID
  access-key-secret: ****************************** # OSS访问密钥Secret
  bucket-name: evcard-dev                           # OSS存储桶名称，开发环境专用
  bucket-short-time: evcard-dev-short-time          # 短期存储桶名称，用于临时文件
  upload:
    base-url: https://evcard.oss-cn-shanghai.aliyuncs.com # OSS文件访问基础URL
    env: dev                                        # 环境标识，用于文件路径区分

# 开发环境SMS短信服务配置 - 用于发送验证码和通知短信
sms:
  # 短信发送限制配置 - 开发环境相对宽松的限制
  code:
    expiration: 300                            # 验证码有效期（秒），5分钟后过期
  send:
    interval: 60                               # 短信发送间隔（秒），防止频繁发送
  daily:
    limit:
      mobile: 20                               # 每个手机号每天最大发送次数，开发环境适当放宽
      ip: 50                                   # 每个IP地址每天最大发送次数，开发环境适当放宽
  
  # 创瑞云短信平台配置 - 开发环境
  cryun:
    main:
      # 短信服务主账号配置，支持环境变量覆盖
      access-key: ${SMS_MAIN_ACCESS_KEY:dev_access_key}     # 开发环境短信平台访问密钥
      secret: ${SMS_MAIN_SECRET:dev_secret}                 # 开发环境短信平台访问秘钥
      sign: ${SMS_MAIN_SIGN:370696}                         # 短信签名ID
    api:
      host: ${SMS_API_HOST:http://api-dev.1cloudsp.com}     # 短信API服务地址，开发环境
