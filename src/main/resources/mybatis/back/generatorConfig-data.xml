<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>


    <!-- MySQL驱动jar包位置 -->
    <classPathEntry location="src/main/resources/mybatis/jdbc/mysql-connector-java-8.0.25.jar"/>


    <context id="MySqlContext" targetRuntime="MyBatis3DynamicSql" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="javaFileEncoding" value="UTF-8"/>

        <!-- 为模型生成序列化方法 -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <!-- 为生成的Java模型创建toString方法 -->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>

        <!-- 注释 -->
        <commentGenerator>
            <property name="suppressAllComments" value="false"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
            <property name="remarkColumnName" value="remarks"/>
        </commentGenerator>

        <!-- 数据库连接 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="***********************************************************************************************************************************************"
                        userId="tester" password="dXtG3ISsnX6y">
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>

        <!-- 生成实体类的位置 -->
        <javaModelGenerator targetPackage="com.extracme.saas.autocare.model.entity"
                          targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- 生成Mapper接口的位置 -->
        <javaClientGenerator type="ANNOTATEDMAPPER"
                           targetPackage="com.extracme.saas.autocare.mapper.base"
                           targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!-- 要生成的表 -->

        <!-- 维修厂可修理车型表 -->
        <!-- 维修厂基本信息表 -->
        <table tableName="mtc_repair_depot_info" domainObjectName="MtcRepairDepotInfo">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>
        <!-- 维修厂合作分公司表 -->
        <table tableName="mtc_repair_depot_cooperative_branch" domainObjectName="MtcRepairDepotCooperativeBranch">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>
        <!-- 维修厂可修理车型表 -->
        <table tableName="mtc_repair_depot_vehicle_model_info" domainObjectName="MtcRepairDepotVehicleModelInfo">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>
        <!-- 维修厂可保修车型表 -->
        <table tableName="mtc_repair_depot_warranty_vehicle_model_info" domainObjectName="MtcRepairDepotWarrantyVehicleModelInfo">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 车辆信息表 -->
        <table tableName="mtc_vehicle_info" domainObjectName="MtcVehicleInfo">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

               <!-- 车型信息表 -->
        <table tableName="mtc_vehicle_model" domainObjectName="MtcVehicleModel">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 审批级别表 -->
        <table tableName="mtc_approval_levels" domainObjectName="MtcApprovalLevels">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

    </context>
</generatorConfiguration> 