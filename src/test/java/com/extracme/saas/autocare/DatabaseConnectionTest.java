package com.extracme.saas.autocare;

import org.junit.jupiter.api.Test;
import java.sql.Connection;
import java.sql.DriverManager;

public class DatabaseConnectionTest {
    
    @Test
    public void testDatabaseConnection() {
        String url = "********************************************************************************************************************************************************************************";
        String username = "devuser";
        String password = "blk2ZsEB";
        
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("Database connected!");
            connection.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("数据库连接失败: " + e.getMessage());
        }
    }
} 