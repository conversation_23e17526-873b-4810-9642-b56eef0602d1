package com.extracme.saas.autocare.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;

/**
 * JwtUtil简单功能测试
 */
class JwtUtilSimpleTest {

    private JwtUtil jwtUtil;

    @BeforeEach
    void setUp() {
        jwtUtil = new JwtUtil();
        ReflectionTestUtils.setField(jwtUtil, "secret", "test-secret-key-for-simple-test");
        ReflectionTestUtils.setField(jwtUtil, "expiration", 86400L);
    }

    @Test
    void testBasicTokenGeneration() {
        // 测试基本token生成
        String token = jwtUtil.generateToken("12345");
        assertNotNull(token);
        assertTrue(token.length() > 0);
        
        // 验证token有效性
        assertTrue(jwtUtil.validateToken(token));
        
        // 验证用户ID提取
        assertEquals("12345", jwtUtil.getUserIdFromToken(token));
    }

    @Test
    void testUserInfoTokenGeneration() {
        // 创建简单的用户信息
        JwtUserInfoDTO userInfo = new JwtUserInfoDTO();
        userInfo.setUserId(12345L);
        userInfo.setUsername("testuser");
        userInfo.setMobile("13800138000"); // 完整手机号
        userInfo.setRepairDepotId("RD001"); // 修理厂ID
        userInfo.setInsuranceCompanyId(1L); // 保险公司ID
        userInfo.setTenantId(100L);
        userInfo.setTenantCode("test");
        
        // 权限和组织信息现在存储在Redis缓存中，不再包含在JWT token中
        // Set<String> permissions = new HashSet<>();
        // permissions.add("read");
        // userInfo.setPermissions(permissions);

        // List<String> orgIds = Arrays.asList("org1");
        // userInfo.setOrgIds(orgIds);
        // userInfo.setAllAccessibleOrgIds(orgIds);
        
        // 设置时间信息
        userInfo.setLoginTime(System.currentTimeMillis());
        userInfo.setExpireTime(System.currentTimeMillis() + 86400000);

        try {
            // 生成包含用户信息的token
            String token = jwtUtil.generateTokenWithUserInfo(userInfo);
            assertNotNull(token, "Token should not be null");
            assertTrue(token.length() > 0, "Token should not be empty");
            
            // 验证token有效性
            assertTrue(jwtUtil.validateToken(token), "Token should be valid");
            
            // 验证token包含用户信息
            assertTrue(jwtUtil.hasUserInfo(token), "Token should contain user info");
            
            // 尝试解析用户信息
            JwtUserInfoDTO parsedInfo = jwtUtil.getUserInfoFromToken(token);
            if (parsedInfo != null) {
                assertEquals(userInfo.getUserId(), parsedInfo.getUserId());
                assertEquals(userInfo.getUsername(), parsedInfo.getUsername());
                assertEquals(userInfo.getRepairDepotId(), parsedInfo.getRepairDepotId());
                assertEquals(userInfo.getTenantId(), parsedInfo.getTenantId());
                System.out.println("User info parsing successful!");
            } else {
                System.out.println("User info parsing returned null - debugging...");

                // 手动测试JSON序列化和反序列化
                try {
                    com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    String json = mapper.writeValueAsString(userInfo);
                    System.out.println("Serialized JSON: " + json);

                    JwtUserInfoDTO deserialized = mapper.readValue(json, JwtUserInfoDTO.class);
                    System.out.println("Deserialized user ID: " + deserialized.getUserId());

                    // 检查token中的实际内容
                    Object userInfoFromToken = jwtUtil.getClaimFromToken(token, claims -> claims.get("userInfo"));
                    System.out.println("User info from token: " + userInfoFromToken);

                } catch (Exception e) {
                    System.out.println("JSON serialization test failed: " + e.getMessage());
                    e.printStackTrace();
                }
            }
            
        } catch (Exception e) {
            System.out.println("Exception during token generation/parsing: " + e.getMessage());
            e.printStackTrace();
            fail("Should not throw exception: " + e.getMessage());
        }
    }

    @Test
    void testTokenSizeInfo() {
        // 创建用户信息
        JwtUserInfoDTO userInfo = new JwtUserInfoDTO();
        userInfo.setUserId(12345L);
        userInfo.setUsername("testuser");
        
        // 生成token
        String token = jwtUtil.generateTokenWithUserInfo(userInfo);
        
        // 获取大小信息
        JwtUtil.TokenSizeInfo sizeInfo = jwtUtil.getTokenSizeInfo(token);
        assertNotNull(sizeInfo);
        assertTrue(sizeInfo.getTotalSize() > 0);
        
        System.out.println("Token size analysis: " + sizeInfo);
    }
}
