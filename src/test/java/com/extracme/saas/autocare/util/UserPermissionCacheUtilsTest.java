package com.extracme.saas.autocare.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 用户权限缓存工具类测试
 */
@ExtendWith(MockitoExtension.class)
class UserPermissionCacheUtilsTest {

    @Mock
    private RedisUtils redisUtils;

    @InjectMocks
    private UserPermissionCacheUtils userPermissionCacheUtils;

    private Long tenantId;
    private Long userId;
    private Set<String> permissions;
    private List<String> orgIds;
    private List<String> allAccessibleOrgIds;

    @BeforeEach
    void setUp() {
        tenantId = 100L;
        userId = 1001L;
        permissions = new HashSet<>(Arrays.asList("user:read", "user:write", "order:read"));
        orgIds = Arrays.asList("ORG001", "ORG002");
        allAccessibleOrgIds = Arrays.asList("ORG001", "ORG002", "ORG003", "ORG004");
    }

    @Test
    void testCacheUserPermissions_Success() {
        // 模拟Redis操作成功
        when(redisUtils.set(anyString(), anyString(), anyLong())).thenReturn(true);

        // 执行缓存操作
        boolean result = userPermissionCacheUtils.cacheUserPermissions(
            tenantId, userId, permissions, orgIds, allAccessibleOrgIds);

        // 验证结果
        assertTrue(result);
        verify(redisUtils).set(eq("user:permissions:100:1001"), anyString(), eq(86400L));
    }

    @Test
    void testCacheUserPermissions_Failure() {
        // 模拟Redis操作失败
        when(redisUtils.set(anyString(), anyString(), anyLong())).thenReturn(false);

        // 执行缓存操作
        boolean result = userPermissionCacheUtils.cacheUserPermissions(
            tenantId, userId, permissions, orgIds, allAccessibleOrgIds);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testGetUserPermissions_Success() {
        // 模拟Redis返回缓存数据
        String cacheJson = "{\"permissions\":[\"user:read\",\"user:write\"],\"orgIds\":[\"ORG001\"],\"allAccessibleOrgIds\":[\"ORG001\",\"ORG002\"]}";
        when(redisUtils.get("user:permissions:100:1001")).thenReturn(cacheJson);

        // 执行获取操作
        UserPermissionCacheUtils.UserPermissionCache result = 
            userPermissionCacheUtils.getUserPermissions(tenantId, userId);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getPermissions());
        assertTrue(result.getPermissions().contains("user:read"));
        assertTrue(result.getPermissions().contains("user:write"));
        assertEquals(Arrays.asList("ORG001"), result.getOrgIds());
        assertEquals(Arrays.asList("ORG001", "ORG002"), result.getAllAccessibleOrgIds());
    }

    @Test
    void testGetUserPermissions_NotFound() {
        // 模拟Redis返回null（缓存不存在）
        when(redisUtils.get("user:permissions:100:1001")).thenReturn(null);

        // 执行获取操作
        UserPermissionCacheUtils.UserPermissionCache result = 
            userPermissionCacheUtils.getUserPermissions(tenantId, userId);

        // 验证结果
        assertNull(result);
    }

    @Test
    void testDeleteUserPermissions() {
        // 执行删除操作
        boolean result = userPermissionCacheUtils.deleteUserPermissions(tenantId, userId);

        // 验证结果
        assertTrue(result);
        verify(redisUtils).del("user:permissions:100:1001");
    }

    @Test
    void testRefreshUserPermissions() {
        // 执行刷新操作
        boolean result = userPermissionCacheUtils.refreshUserPermissions(tenantId, userId);

        // 验证结果
        assertTrue(result);
        verify(redisUtils).del("user:permissions:100:1001");
    }

    @Test
    void testDeleteAllUserPermissionsByTenant() {
        // 模拟批量删除返回删除数量
        when(redisUtils.deleteByPattern("user:permissions:100:*")).thenReturn(5L);

        // 执行批量删除操作
        long result = userPermissionCacheUtils.deleteAllUserPermissionsByTenant(tenantId);

        // 验证结果
        assertEquals(5L, result);
        verify(redisUtils).deleteByPattern("user:permissions:100:*");
    }

    @Test
    void testIsRedisAvailable_True() {
        // 模拟Redis可用
        when(redisUtils.ping()).thenReturn(true);

        // 执行检查
        boolean result = userPermissionCacheUtils.isRedisAvailable();

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsRedisAvailable_False() {
        // 模拟Redis不可用
        when(redisUtils.ping()).thenReturn(false);

        // 执行检查
        boolean result = userPermissionCacheUtils.isRedisAvailable();

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testGetUserPermissionSet_WithCache() {
        // 模拟Redis返回缓存数据
        String cacheJson = "{\"permissions\":[\"user:read\",\"user:write\"],\"orgIds\":[\"ORG001\"],\"allAccessibleOrgIds\":[\"ORG001\",\"ORG002\"]}";
        when(redisUtils.get("user:permissions:100:1001")).thenReturn(cacheJson);

        // 执行获取操作
        Set<String> result = userPermissionCacheUtils.getUserPermissionSet(tenantId, userId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("user:read"));
        assertTrue(result.contains("user:write"));
    }

    @Test
    void testGetUserPermissionSet_NoCache() {
        // 模拟Redis返回null
        when(redisUtils.get("user:permissions:100:1001")).thenReturn(null);

        // 执行获取操作
        Set<String> result = userPermissionCacheUtils.getUserPermissionSet(tenantId, userId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetUserOrgIds_WithCache() {
        // 模拟Redis返回缓存数据
        String cacheJson = "{\"permissions\":[\"user:read\"],\"orgIds\":[\"ORG001\",\"ORG002\"],\"allAccessibleOrgIds\":[\"ORG001\",\"ORG002\",\"ORG003\"]}";
        when(redisUtils.get("user:permissions:100:1001")).thenReturn(cacheJson);

        // 执行获取操作
        List<String> result = userPermissionCacheUtils.getUserOrgIds(tenantId, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(Arrays.asList("ORG001", "ORG002"), result);
    }

    @Test
    void testGetUserOrgIds_NoCache() {
        // 模拟Redis返回null
        when(redisUtils.get("user:permissions:100:1001")).thenReturn(null);

        // 执行获取操作
        List<String> result = userPermissionCacheUtils.getUserOrgIds(tenantId, userId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testRefreshMultipleUserPermissions() {
        // 准备测试数据
        List<Long> userIds = Arrays.asList(1001L, 1002L, 1003L);

        // 执行批量刷新
        int result = userPermissionCacheUtils.refreshMultipleUserPermissions(tenantId, userIds);

        // 验证结果
        assertEquals(3, result);
        verify(redisUtils, times(3)).del(anyString());
    }

    @Test
    void testRefreshUserPermissionsByRole() {
        // 模拟批量删除返回删除数量
        when(redisUtils.deleteByPattern("user:permissions:100:*")).thenReturn(10L);

        // 执行角色权限刷新
        int result = userPermissionCacheUtils.refreshUserPermissionsByRole(tenantId, 2001L);

        // 验证结果
        assertEquals(10, result);
        verify(redisUtils).deleteByPattern("user:permissions:100:*");
    }

    @Test
    void testRefreshUserPermissionsByOrg() {
        // 模拟批量删除返回删除数量
        when(redisUtils.deleteByPattern("user:permissions:100:*")).thenReturn(8L);

        // 执行组织权限刷新
        int result = userPermissionCacheUtils.refreshUserPermissionsByOrg(tenantId, "ORG001");

        // 验证结果
        assertEquals(8, result);
        verify(redisUtils).deleteByPattern("user:permissions:100:*");
    }
}
