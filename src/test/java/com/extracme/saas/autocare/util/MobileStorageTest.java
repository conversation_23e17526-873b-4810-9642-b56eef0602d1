package com.extracme.saas.autocare.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;

/**
 * 手机号完整存储功能测试
 * 验证修改后的手机号存储策略
 */
class MobileStorageTest {

    private JwtUtil jwtUtil;

    @BeforeEach
    void setUp() {
        jwtUtil = new JwtUtil();
        ReflectionTestUtils.setField(jwtUtil, "secret", "test-secret-key-for-mobile-storage-test");
        ReflectionTestUtils.setField(jwtUtil, "expiration", 86400L);
    }

    /**
     * 测试手机号完整存储和解析
     */
    @Test
    void testCompleteMobileStorage() {
        // 创建包含完整手机号的用户信息
        JwtUserInfoDTO userInfo = createUserInfoWithMobile("***********");
        
        // 生成JWT token
        String token = jwtUtil.generateTokenWithUserInfo(userInfo);
        assertNotNull(token);
        
        // 从token中解析用户信息
        JwtUserInfoDTO parsedInfo = jwtUtil.getUserInfoFromToken(token);
        assertNotNull(parsedInfo);
        
        // 验证手机号完整性
        assertEquals("***********", parsedInfo.getMobile());
        assertEquals(userInfo.getMobile(), parsedInfo.getMobile());
        
        System.out.println("Complete mobile storage test passed!");
        System.out.println("Original mobile: " + userInfo.getMobile());
        System.out.println("Parsed mobile: " + parsedInfo.getMobile());
    }

    /**
     * 测试不同格式的手机号
     */
    @Test
    void testVariousMobileFormats() {
        String[] testMobiles = {
            "***********",      // 标准11位手机号
            "86-***********",   // 带国家代码
            "138-0013-8000",    // 带分隔符
            "************",     // 400电话
            "021-12345678",     // 固定电话
            "12345",            // 短号码
            ""                  // 空字符串
        };
        
        for (String mobile : testMobiles) {
            JwtUserInfoDTO userInfo = createUserInfoWithMobile(mobile);
            
            String token = jwtUtil.generateTokenWithUserInfo(userInfo);
            JwtUserInfoDTO parsedInfo = jwtUtil.getUserInfoFromToken(token);
            
            assertEquals(mobile, parsedInfo.getMobile(), 
                        "Mobile format test failed for: " + mobile);
        }
        
        System.out.println("Various mobile formats test passed!");
    }

    /**
     * 测试null手机号处理
     */
    @Test
    void testNullMobileHandling() {
        JwtUserInfoDTO userInfo = createUserInfoWithMobile(null);
        
        String token = jwtUtil.generateTokenWithUserInfo(userInfo);
        JwtUserInfoDTO parsedInfo = jwtUtil.getUserInfoFromToken(token);
        
        assertNull(parsedInfo.getMobile());
        
        System.out.println("Null mobile handling test passed!");
    }

    /**
     * 测试token大小影响
     */
    @Test
    void testTokenSizeWithCompleteMobile() {
        // 测试短手机号
        JwtUserInfoDTO shortMobileInfo = createUserInfoWithMobile("123");
        String shortToken = jwtUtil.generateTokenWithUserInfo(shortMobileInfo);
        
        // 测试长手机号
        JwtUserInfoDTO longMobileInfo = createUserInfoWithMobile("86-138-0013-8000-ext123");
        String longToken = jwtUtil.generateTokenWithUserInfo(longMobileInfo);
        
        // 验证token大小仍在合理范围内
        assertTrue(shortToken.length() < 4096, "Short mobile token size should be reasonable");
        assertTrue(longToken.length() < 4096, "Long mobile token size should be reasonable");
        
        // 长手机号的token应该稍大一些
        assertTrue(longToken.length() > shortToken.length(), 
                  "Longer mobile should result in larger token");
        
        System.out.println("Token size test passed!");
        System.out.println("Short mobile token size: " + shortToken.length());
        System.out.println("Long mobile token size: " + longToken.length());
    }

    /**
     * 测试业务场景兼容性
     */
    @Test
    void testBusinessScenarioCompatibility() {
        // 模拟真实业务场景中的手机号
        String[] businessMobiles = {
            "***********",      // 移动
            "***********",      // 电信
            "***********",      // 联通
            "***********",      // 虚拟运营商
            "***********"       // 新号段
        };
        
        for (String mobile : businessMobiles) {
            JwtUserInfoDTO userInfo = createUserInfoWithMobile(mobile);

            // 注意：权限和组织信息现在存储在Redis缓存中，不再包含在JWT token中
            // Set<String> permissions = new HashSet<>();
            // permissions.add("user:read");
            // permissions.add("order:write");
            // userInfo.setPermissions(permissions);

            // List<String> orgIds = Arrays.asList("org1", "org2");
            // userInfo.setOrgIds(orgIds);
            // userInfo.setAllAccessibleOrgIds(Arrays.asList("org1", "org2", "org3"));

            // 生成和解析token（仅包含基础用户信息）
            String token = jwtUtil.generateTokenWithUserInfo(userInfo);
            JwtUserInfoDTO parsedInfo = jwtUtil.getUserInfoFromToken(token);

            // 验证手机号和基础信息都正确
            assertEquals(mobile, parsedInfo.getMobile());
            assertEquals(userInfo.getUserId(), parsedInfo.getUserId());
            assertEquals(userInfo.getUsername(), parsedInfo.getUsername());
            assertEquals(userInfo.getTenantId(), parsedInfo.getTenantId());

            // 验证可以用于业务逻辑
            assertTrue(isValidMobileForBusiness(parsedInfo.getMobile()));
        }
        
        System.out.println("Business scenario compatibility test passed!");
    }

    /**
     * 创建包含指定手机号的用户信息
     */
    private JwtUserInfoDTO createUserInfoWithMobile(String mobile) {
        JwtUserInfoDTO userInfo = new JwtUserInfoDTO();
        
        userInfo.setUserId(12345L);
        userInfo.setUsername("testuser");
        userInfo.setNickname("测试用户");
        userInfo.setMobile(mobile);
        userInfo.setAccountType(1);
        userInfo.setStatus(1);
        
        userInfo.setTenantId(100L);
        userInfo.setTenantCode("test");
        userInfo.setTenantName("测试租户");
        
        long currentTime = System.currentTimeMillis();
        userInfo.setLoginTime(currentTime);
        userInfo.setExpireTime(currentTime + ********);
        
        return userInfo;
    }

    /**
     * 模拟业务逻辑中的手机号验证
     */
    private boolean isValidMobileForBusiness(String mobile) {
        if (mobile == null || mobile.isEmpty()) {
            return false;
        }
        
        // 简单的业务验证逻辑
        // 在实际业务中，这里可能包含复杂的手机号格式验证
        return mobile.length() >= 3; // 最基本的长度检查
    }

    /**
     * 测试性能影响
     */
    @Test
    void testPerformanceImpact() {
        int iterations = 100;
        
        // 测试完整手机号的性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            JwtUserInfoDTO userInfo = createUserInfoWithMobile("***********");
            String token = jwtUtil.generateTokenWithUserInfo(userInfo);
            JwtUserInfoDTO parsedInfo = jwtUtil.getUserInfoFromToken(token);
            assertNotNull(parsedInfo.getMobile());
        }
        long endTime = System.currentTimeMillis();
        
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;
        
        System.out.printf("Performance test: %d iterations in %d ms, avg: %.2f ms/iteration%n", 
                         iterations, totalTime, avgTime);
        
        // 验证性能仍然良好
        assertTrue(avgTime < 50.0, "Average time should be less than 50ms per iteration");
    }
}
