package com.extracme.saas.autocare.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;

/**
 * SessionUtils优化效果测试
 * 验证JWT token优化后的功能正确性（权限信息已移至Redis缓存）
 */
@ExtendWith(MockitoExtension.class)
class SessionUtilsOptimizationTest {

    @Mock
    private JwtUtil jwtUtil;

    private UserPermissionCacheUtils userPermissionCacheUtils;

    private JwtUserInfoDTO testUserInfo;

    @BeforeEach
    void setUp() {
        // 设置静态字段
        ReflectionTestUtils.setField(SessionUtils.class, "jwtUtil", jwtUtil);

        // Mock UserPermissionCacheUtils
        userPermissionCacheUtils = mock(UserPermissionCacheUtils.class);
        when(userPermissionCacheUtils.isRedisAvailable()).thenReturn(true);
        when(userPermissionCacheUtils.cacheUserPermissions(anyLong(), anyLong(), any(), any(), any()))
            .thenReturn(true);

        // 创建测试用户信息（仅包含基础信息）
        testUserInfo = createTestUserInfo();
    }

    /**
     * 测试JWT token中基础用户信息的完整性
     * 权限信息现在存储在Redis缓存中，不再包含在JWT token中
     */
    @Test
    void testJwtUserInfoCompleteness() {
        // 验证用户基本信息
        assertNotNull(testUserInfo.getUserId());
        assertNotNull(testUserInfo.getUsername());
        assertNotNull(testUserInfo.getTenantId());
        assertNotNull(testUserInfo.getTenantCode());
        assertNotNull(testUserInfo.getTenantName());
        assertNotNull(testUserInfo.getMobile());
        assertNotNull(testUserInfo.getAccountType());
        assertNotNull(testUserInfo.getStatus());

        // 验证权限信息已从JWT中移除
        // 权限信息现在通过UserPermissionCacheUtils从Redis获取
        // 这里不再验证权限字段，因为它们已被移除

        // 验证时间信息使用时间戳
        assertNotNull(testUserInfo.getLoginTime());
        assertNotNull(testUserInfo.getExpireTime());
        assertTrue(testUserInfo.getLoginTime() instanceof Long);
        assertTrue(testUserInfo.getExpireTime() instanceof Long);

        System.out.println("JWT基础用户信息完整性验证通过");
        System.out.println("注意：权限和组织信息现在存储在Redis缓存中");
    }

    /**
     * 测试手机号完整存储功能
     */
    @Test
    void testMobileStorage() {
        // 测试完整手机号存储
        String fullMobile = "***********";
        testUserInfo.setMobile(fullMobile);
        assertEquals(fullMobile, testUserInfo.getMobile());

        // 测试短手机号
        String shortMobile = "123";
        testUserInfo.setMobile(shortMobile);
        assertEquals(shortMobile, testUserInfo.getMobile());

        // 测试null值
        testUserInfo.setMobile(null);
        assertNull(testUserInfo.getMobile());
    }

    /**
     * 测试时间转换功能
     */
    @Test
    void testTimeConversion() {
        Date testDate = new Date();
        
        // 测试登录时间转换
        testUserInfo.setLoginTimeFromDate(testDate);
        assertEquals(testDate.getTime(), testUserInfo.getLoginTime().longValue());
        assertEquals(testDate, testUserInfo.getLoginTimeAsDate());

        // 测试过期时间转换
        testUserInfo.setExpireTimeFromDate(testDate);
        assertEquals(testDate.getTime(), testUserInfo.getExpireTime().longValue());
        assertEquals(testDate, testUserInfo.getExpireTimeAsDate());

        // 测试null值处理
        testUserInfo.setLoginTimeFromDate(null);
        assertNull(testUserInfo.getLoginTime());
        assertNull(testUserInfo.getLoginTimeAsDate());
    }

    /**
     * 测试JWT token大小优化效果
     */
    @Test
    void testTokenSizeOptimization() {
        JwtUtil realJwtUtil = new JwtUtil();
        ReflectionTestUtils.setField(realJwtUtil, "secret", "test-secret-key-for-jwt-token-generation");
        ReflectionTestUtils.setField(realJwtUtil, "expiration", 86400L);

        // 生成优化后的token
        String optimizedToken = realJwtUtil.generateTokenWithUserInfo(testUserInfo);
        
        // 验证token不为空
        assertNotNull(optimizedToken);
        assertTrue(optimizedToken.length() > 0);

        // 获取token大小信息
        JwtUtil.TokenSizeInfo sizeInfo = realJwtUtil.getTokenSizeInfo(optimizedToken);
        assertNotNull(sizeInfo);
        assertTrue(sizeInfo.getTotalSize() > 0);
        assertTrue(sizeInfo.getUserInfoSize() > 0);
        assertTrue(sizeInfo.getUserInfoRatio() > 0);

        // 验证token大小在合理范围内（应该小于8KB）
        assertTrue(sizeInfo.getTotalSize() < 8192, 
                  "Token size should be less than 8KB, actual: " + sizeInfo.getTotalSize());

        System.out.println("Token size info: " + sizeInfo);
    }

    /**
     * 测试向后兼容性
     */
    @Test
    void testBackwardCompatibility() {
        JwtUtil realJwtUtil = new JwtUtil();
        ReflectionTestUtils.setField(realJwtUtil, "secret", "test-secret-key-for-compatibility-test");
        ReflectionTestUtils.setField(realJwtUtil, "expiration", 86400L);

        // 生成旧版本token（只包含userId）
        String oldToken = realJwtUtil.generateToken("12345");

        // 验证旧版本token不包含完整用户信息
        assertFalse(realJwtUtil.hasUserInfo(oldToken));
        assertTrue(realJwtUtil.validateToken(oldToken));
        assertEquals("12345", realJwtUtil.getUserIdFromToken(oldToken));

        // 生成新版本token（包含完整用户信息）
        String newToken = realJwtUtil.generateTokenWithUserInfo(testUserInfo);

        // 验证新版本token包含完整用户信息
        assertTrue(realJwtUtil.hasUserInfo(newToken));
        assertTrue(realJwtUtil.validateToken(newToken));
        assertNotNull(realJwtUtil.getUserInfoFromToken(newToken));
    }

    /**
     * 创建测试用户信息（仅包含基础信息，权限信息现在存储在Redis中）
     */
    private JwtUserInfoDTO createTestUserInfo() {
        JwtUserInfoDTO userInfo = new JwtUserInfoDTO();

        // 基本信息
        userInfo.setUserId(12345L);
        userInfo.setUsername("testuser");
        userInfo.setNickname("测试用户");
        userInfo.setMobile("***********"); // 完整手机号
        userInfo.setRepairDepotId("RD001"); // 修理厂ID
        userInfo.setAccountType(1);
        userInfo.setApprovalLevel(2);
        userInfo.setStatus(1);

        // 权限信息现在存储在Redis缓存中，不再包含在JWT token中
        // Set<String> permissions = new HashSet<>();
        // permissions.add("user:read");
        // permissions.add("user:write");
        // permissions.add("order:read");
        // userInfo.setPermissions(permissions);

        // 组织信息现在存储在Redis缓存中，不再包含在JWT token中
        // List<String> orgIds = Arrays.asList("org1", "org2");
        // List<String> allAccessibleOrgIds = Arrays.asList("org1", "org2", "org3", "org4");
        // userInfo.setOrgIds(orgIds);
        // userInfo.setAllAccessibleOrgIds(allAccessibleOrgIds);

        // 租户信息
        userInfo.setTenantId(100L);
        userInfo.setTenantCode("tenant001");
        userInfo.setTenantName("测试租户");

        // 时间信息
        long currentTime = System.currentTimeMillis();
        userInfo.setLoginTime(currentTime);
        userInfo.setExpireTime(currentTime + 86400000); // 24小时后过期

        // IP地址
        userInfo.setIpaddr("*************");

        return userInfo;
    }

    /**
     * 测试多租户隔离
     */
    @Test
    void testMultiTenantIsolation() {
        // 验证租户信息正确设置
        assertEquals(100L, testUserInfo.getTenantId());
        assertEquals("tenant001", testUserInfo.getTenantCode());
        assertEquals("测试租户", testUserInfo.getTenantName());

        // 创建不同租户的用户信息
        JwtUserInfoDTO anotherUserInfo = createTestUserInfo();
        anotherUserInfo.setTenantId(200L);
        anotherUserInfo.setTenantCode("tenant002");
        anotherUserInfo.setTenantName("另一个租户");

        // 验证租户隔离
        assertNotEquals(testUserInfo.getTenantId(), anotherUserInfo.getTenantId());
        assertNotEquals(testUserInfo.getTenantCode(), anotherUserInfo.getTenantCode());
    }

    /**
     * 测试Redis缓存组织权限控制
     */
    @Test
    void testRedisOrganizationPermissionControl() {
        // 模拟Redis中的组织权限信息
        Set<String> permissions = new HashSet<>();
        permissions.add("user:read");
        permissions.add("user:write");
        permissions.add("order:read");

        List<String> directOrgs = Arrays.asList("org1", "org2");
        List<String> allAccessibleOrgs = Arrays.asList("org1", "org2", "org3", "org4");

        // 模拟从Redis获取权限信息
        UserPermissionCacheUtils.UserPermissionCache mockCache =
            new UserPermissionCacheUtils.UserPermissionCache(permissions, directOrgs, allAccessibleOrgs);
        when(userPermissionCacheUtils.getUserPermissions(testUserInfo.getTenantId(), testUserInfo.getUserId()))
            .thenReturn(mockCache);

        // 验证从Redis获取的组织权限信息
        UserPermissionCacheUtils.UserPermissionCache cachedPermissions =
            userPermissionCacheUtils.getUserPermissions(testUserInfo.getTenantId(), testUserInfo.getUserId());

        assertNotNull(cachedPermissions);

        // 验证直接关联组织
        List<String> cachedDirectOrgs = cachedPermissions.getOrgIds();
        assertNotNull(cachedDirectOrgs);
        assertEquals(2, cachedDirectOrgs.size());
        assertTrue(cachedDirectOrgs.contains("org1"));
        assertTrue(cachedDirectOrgs.contains("org2"));

        // 验证可访问的所有组织（包括子组织）
        List<String> cachedAllAccessibleOrgs = cachedPermissions.getAllAccessibleOrgIds();
        assertNotNull(cachedAllAccessibleOrgs);
        assertEquals(4, cachedAllAccessibleOrgs.size());
        assertTrue(cachedAllAccessibleOrgs.containsAll(cachedDirectOrgs));
        assertTrue(cachedAllAccessibleOrgs.contains("org3"));
        assertTrue(cachedAllAccessibleOrgs.contains("org4"));

        System.out.println("Redis组织权限控制测试通过");
    }

    /**
     * 测试完整的优化流程（基础用户信息 + Redis权限缓存）
     */
    @Test
    void testCompleteOptimizationFlow() {
        JwtUtil realJwtUtil = new JwtUtil();
        ReflectionTestUtils.setField(realJwtUtil, "secret", "test-secret-key-for-complete-flow-test");
        ReflectionTestUtils.setField(realJwtUtil, "expiration", 86400L);

        // 1. 生成包含基础用户信息的JWT token
        String optimizedToken = realJwtUtil.generateTokenWithUserInfo(testUserInfo);
        assertNotNull(optimizedToken);

        // 2. 验证token包含基础用户信息
        assertTrue(realJwtUtil.hasUserInfo(optimizedToken));

        // 3. 从token中解析基础用户信息
        JwtUserInfoDTO parsedUserInfo = realJwtUtil.getUserInfoFromToken(optimizedToken);
        assertNotNull(parsedUserInfo, "Parsed user info should not be null");

        // 4. 验证解析出的基础信息完整性
        assertEquals(testUserInfo.getUserId(), parsedUserInfo.getUserId());
        assertEquals(testUserInfo.getUsername(), parsedUserInfo.getUsername());
        assertEquals(testUserInfo.getTenantId(), parsedUserInfo.getTenantId());
        assertEquals(testUserInfo.getTenantCode(), parsedUserInfo.getTenantCode());
        assertEquals(testUserInfo.getTenantName(), parsedUserInfo.getTenantName());
        assertEquals(testUserInfo.getMobile(), parsedUserInfo.getMobile());
        assertEquals(testUserInfo.getAccountType(), parsedUserInfo.getAccountType());

        // 5. 验证token大小显著减少（不包含权限信息）
        JwtUtil.TokenSizeInfo sizeInfo = realJwtUtil.getTokenSizeInfo(optimizedToken);
        assertTrue(sizeInfo.getTotalSize() < 4096); // 应该小于4KB（比之前更小）

        // 6. 模拟从Redis获取权限信息
        Set<String> permissions = new HashSet<>();
        permissions.add("user:read");
        permissions.add("user:write");
        permissions.add("order:read");

        List<String> orgIds = Arrays.asList("org1", "org2");
        List<String> allAccessibleOrgIds = Arrays.asList("org1", "org2", "org3", "org4");

        UserPermissionCacheUtils.UserPermissionCache mockCache =
            new UserPermissionCacheUtils.UserPermissionCache(permissions, orgIds, allAccessibleOrgIds);
        when(userPermissionCacheUtils.getUserPermissions(testUserInfo.getTenantId(), testUserInfo.getUserId()))
            .thenReturn(mockCache);

        UserPermissionCacheUtils.UserPermissionCache cachedPermissions =
            userPermissionCacheUtils.getUserPermissions(testUserInfo.getTenantId(), testUserInfo.getUserId());

        System.out.println("Complete optimization flow test passed!");
        System.out.println("JWT Token size: " + sizeInfo.getTotalSize() + " bytes (显著减少)");
        System.out.println("Redis cached permissions: " + cachedPermissions.getPermissions().size());
        System.out.println("Redis cached direct orgs: " + cachedPermissions.getOrgIds().size());
        System.out.println("Redis cached accessible orgs: " + cachedPermissions.getAllAccessibleOrgIds().size());
    }

    /**
     * 测试安全性改进
     */
    @Test
    void testSecurityImprovements() {
        // 验证敏感信息处理策略

        // 1. 手机号完整存储（根据业务需求修改）
        assertEquals("***********", testUserInfo.getMobile()); // 完整手机号

        // 2. 邮箱信息已移除（在DTO中注释掉了）
        // 这里无法直接测试，但可以通过序列化验证

        // 3. 创建时间和更新时间已移除
        // 这些字段在优化后的DTO中已被移除

        // 4. 验证token不包含明文密码等敏感信息
        JwtUtil realJwtUtil = new JwtUtil();
        ReflectionTestUtils.setField(realJwtUtil, "secret", "test-secret-key");
        ReflectionTestUtils.setField(realJwtUtil, "expiration", 86400L);

        String token = realJwtUtil.generateTokenWithUserInfo(testUserInfo);

        // token中不应包含明文密码等敏感信息
        assertFalse(token.contains("password"));
        // 注意：现在手机号会包含在token中，这是业务需求
        assertTrue(token.contains("***********")); // 验证完整手机号确实存储在token中

        System.out.println("Security improvements verified!");
    }
}
