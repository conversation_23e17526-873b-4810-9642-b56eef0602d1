package com.extracme.saas.autocare.util;

import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试JWT token中repairDepotId字段的支持
 */
class RepairDepotIdJwtTest {

    private JwtUtil jwtUtil;

    @BeforeEach
    void setUp() {
        jwtUtil = new JwtUtil();
        ReflectionTestUtils.setField(jwtUtil, "secret", "test-secret-key-for-repair-depot-test");
        ReflectionTestUtils.setField(jwtUtil, "expiration", 86400L);
    }

    @Test
    void testRepairDepotIdInJwtToken() {
        // 创建包含修理厂ID的用户信息
        JwtUserInfoDTO userInfo = new JwtUserInfoDTO();
        userInfo.setUserId(12345L);
        userInfo.setUsername("testuser");
        userInfo.setMobile("13800138000");
        userInfo.setRepairDepotId("RD001"); // 设置修理厂ID
        userInfo.setTenantId(100L);
        userInfo.setTenantCode("test");
        userInfo.setTenantName("测试租户");
        userInfo.setLoginTime(System.currentTimeMillis());
        userInfo.setExpireTime(System.currentTimeMillis() + 86400000);

        // 生成JWT token
        String token = jwtUtil.generateTokenWithUserInfo(userInfo);
        assertNotNull(token, "Token should not be null");
        assertTrue(token.length() > 0, "Token should not be empty");

        // 验证token有效性
        assertTrue(jwtUtil.validateToken(token), "Token should be valid");

        // 验证token包含用户信息
        assertTrue(jwtUtil.hasUserInfo(token), "Token should contain user info");

        // 从token中解析用户信息
        JwtUserInfoDTO parsedInfo = jwtUtil.getUserInfoFromToken(token);
        assertNotNull(parsedInfo, "Parsed user info should not be null");

        // 验证修理厂ID正确解析
        assertEquals(userInfo.getRepairDepotId(), parsedInfo.getRepairDepotId(), 
                    "Repair depot ID should match");
        assertEquals("RD001", parsedInfo.getRepairDepotId(), 
                    "Repair depot ID should be RD001");

        // 验证其他字段也正确解析
        assertEquals(userInfo.getUserId(), parsedInfo.getUserId(), "User ID should match");
        assertEquals(userInfo.getUsername(), parsedInfo.getUsername(), "Username should match");
        assertEquals(userInfo.getMobile(), parsedInfo.getMobile(), "Mobile should match");
        assertEquals(userInfo.getTenantId(), parsedInfo.getTenantId(), "Tenant ID should match");

        System.out.println("RepairDepotId JWT test passed successfully!");
        System.out.println("Original repairDepotId: " + userInfo.getRepairDepotId());
        System.out.println("Parsed repairDepotId: " + parsedInfo.getRepairDepotId());
    }

    @Test
    void testNullRepairDepotIdInJwtToken() {
        // 创建不包含修理厂ID的用户信息
        JwtUserInfoDTO userInfo = new JwtUserInfoDTO();
        userInfo.setUserId(12345L);
        userInfo.setUsername("testuser");
        userInfo.setMobile("13800138000");
        userInfo.setRepairDepotId(null); // 修理厂ID为null
        userInfo.setTenantId(100L);
        userInfo.setTenantCode("test");
        userInfo.setTenantName("测试租户");
        userInfo.setLoginTime(System.currentTimeMillis());
        userInfo.setExpireTime(System.currentTimeMillis() + 86400000);

        // 生成JWT token
        String token = jwtUtil.generateTokenWithUserInfo(userInfo);
        assertNotNull(token, "Token should not be null");

        // 从token中解析用户信息
        JwtUserInfoDTO parsedInfo = jwtUtil.getUserInfoFromToken(token);
        assertNotNull(parsedInfo, "Parsed user info should not be null");

        // 验证修理厂ID为null
        assertNull(parsedInfo.getRepairDepotId(), "Repair depot ID should be null");

        System.out.println("Null RepairDepotId JWT test passed successfully!");
    }

    @Test
    void testEmptyRepairDepotIdInJwtToken() {
        // 创建包含空字符串修理厂ID的用户信息
        JwtUserInfoDTO userInfo = new JwtUserInfoDTO();
        userInfo.setUserId(12345L);
        userInfo.setUsername("testuser");
        userInfo.setMobile("13800138000");
        userInfo.setRepairDepotId(""); // 修理厂ID为空字符串
        userInfo.setTenantId(100L);
        userInfo.setTenantCode("test");
        userInfo.setTenantName("测试租户");
        userInfo.setLoginTime(System.currentTimeMillis());
        userInfo.setExpireTime(System.currentTimeMillis() + 86400000);

        // 生成JWT token
        String token = jwtUtil.generateTokenWithUserInfo(userInfo);
        assertNotNull(token, "Token should not be null");

        // 从token中解析用户信息
        JwtUserInfoDTO parsedInfo = jwtUtil.getUserInfoFromToken(token);
        assertNotNull(parsedInfo, "Parsed user info should not be null");

        // 验证修理厂ID为空字符串
        assertEquals("", parsedInfo.getRepairDepotId(), "Repair depot ID should be empty string");

        System.out.println("Empty RepairDepotId JWT test passed successfully!");
    }
}
