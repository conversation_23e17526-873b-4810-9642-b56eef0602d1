package com.extracme.saas.autocare.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;

@ExtendWith(MockitoExtension.class)
@DisplayName("SessionUtils 工具类测试")
class SessionUtilsTest {

    @Test
    @DisplayName("isSuperAdmin - 超级管理员用户应该返回true")
    void isSuperAdmin_SuperAdminUser_ShouldReturnTrue() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置超级管理员用户
            SysUser superAdminUser = new SysUser();
            superAdminUser.setId(1L);
            superAdminUser.setAccountType(0); // 超级管理员
            superAdminUser.setTenantId(100L);
            
            LoginUser superAdminLoginUser = new LoginUser();
            superAdminLoginUser.setUser(superAdminUser);
            
            // Mock getLoginUser 方法
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(superAdminLoginUser);
            
            // 调用真实的 isSuperAdmin 方法
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenCallRealMethod();
            
            // 验证结果
            assertTrue(SessionUtils.isSuperAdmin());
        }
    }

    @Test
    @DisplayName("isSuperAdmin - 普通用户应该返回false")
    void isSuperAdmin_RegularUser_ShouldReturnFalse() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置普通用户
            SysUser regularUser = new SysUser();
            regularUser.setId(2L);
            regularUser.setAccountType(1); // 普通用户
            regularUser.setTenantId(100L);
            
            LoginUser regularLoginUser = new LoginUser();
            regularLoginUser.setUser(regularUser);
            
            // Mock getLoginUser 方法
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(regularLoginUser);
            
            // 调用真实的 isSuperAdmin 方法
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenCallRealMethod();
            
            // 验证结果
            assertFalse(SessionUtils.isSuperAdmin());
        }
    }

    @Test
    @DisplayName("isSuperAdmin - 未登录用户应该返回false")
    void isSuperAdmin_NotLoggedInUser_ShouldReturnFalse() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // Mock getLoginUser 返回 null
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(null);
            
            // 调用真实的 isSuperAdmin 方法
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenCallRealMethod();
            
            // 验证结果
            assertFalse(SessionUtils.isSuperAdmin());
        }
    }

    @Test
    @DisplayName("isSuperAdmin - 用户实体为空应该返回false")
    void isSuperAdmin_NullUserEntity_ShouldReturnFalse() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置登录用户但用户实体为空
            LoginUser loginUserWithNullEntity = new LoginUser();
            loginUserWithNullEntity.setUser(null);
            
            // Mock getLoginUser 方法
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(loginUserWithNullEntity);
            
            // 调用真实的 isSuperAdmin 方法
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenCallRealMethod();
            
            // 验证结果
            assertFalse(SessionUtils.isSuperAdmin());
        }
    }

    @Test
    @DisplayName("isSuperAdmin - accountType为空应该返回false")
    void isSuperAdmin_NullAccountType_ShouldReturnFalse() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置用户但accountType为空
            SysUser userWithNullAccountType = new SysUser();
            userWithNullAccountType.setId(3L);
            userWithNullAccountType.setAccountType(null); // accountType为空
            userWithNullAccountType.setTenantId(100L);
            
            LoginUser loginUser = new LoginUser();
            loginUser.setUser(userWithNullAccountType);
            
            // Mock getLoginUser 方法
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(loginUser);
            
            // 调用真实的 isSuperAdmin 方法
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenCallRealMethod();
            
            // 验证结果
            assertFalse(SessionUtils.isSuperAdmin());
        }
    }

    @Test
    @DisplayName("getTenantCode - 正常情况下返回租户编码")
    void getTenantCode_NormalCase_ShouldReturnTenantCode() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 直接mock getTenantCode方法返回值
            sessionUtilsMock.when(SessionUtils::getTenantCode).thenReturn("TEST_TENANT");

            // 验证结果
            assertEquals("TEST_TENANT", SessionUtils.getTenantCode());
        }
    }

    @Test
    @DisplayName("getTenantCode - 异常情况下返回默认值")
    void getTenantCode_ExceptionCase_ShouldReturnDefault() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 直接mock getTenantCode方法返回默认值
            sessionUtilsMock.when(SessionUtils::getTenantCode).thenReturn("default");

            // 验证结果
            assertEquals("default", SessionUtils.getTenantCode());
        }
    }
}
