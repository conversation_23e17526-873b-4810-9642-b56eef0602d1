package com.extracme.saas.autocare.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * IpUtils工具类单元测试
 *
 * <p>测试在不同网络环境下的IP地址获取功能，包括：</p>
 * <ul>
 *   <li>直接连接场景</li>
 *   <li>单级代理场景</li>
 *   <li>多级代理场景</li>
 *   <li>各种代理请求头</li>
 *   <li>异常情况处理</li>
 * </ul>
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
class IpUtilsTest {

    @Mock
    private HttpServletRequest mockRequest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("测试空请求参数抛出异常")
    void testNullRequestThrowsException() {
        // 测试null参数
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> IpUtils.getClientIpAddress(null)
        );
        assertEquals("HttpServletRequest cannot be null", exception.getMessage());
    }

    @Test
    @DisplayName("测试从X-Forwarded-For头获取IP")
    void testGetIpFromXForwardedFor() {
        // 模拟X-Forwarded-For头
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn("*************");
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("*************", ip);
    }

    @Test
    @DisplayName("测试从X-Real-IP头获取IP")
    void testGetIpFromXRealIp() {
        // 模拟X-Real-IP头
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn(null);
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("*********");
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("*********", ip);
    }

    @Test
    @DisplayName("测试多级代理情况下获取第一个IP")
    void testMultipleProxyIps() {
        // 模拟多级代理的X-Forwarded-For头
        when(mockRequest.getHeader("X-Forwarded-For"))
            .thenReturn("*************, ***********, ********");
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("*************", ip);
    }

    @Test
    @DisplayName("测试跳过unknown值获取有效IP")
    void testSkipUnknownValues() {
        // 模拟包含unknown值的多IP字符串
        when(mockRequest.getHeader("X-Forwarded-For"))
            .thenReturn("unknown, *************, ********");
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("*************", ip);
    }

    @Test
    @DisplayName("测试从Proxy-Client-IP头获取IP")
    void testGetIpFromProxyClientIp() {
        // 模拟Proxy-Client-IP头
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn(null);
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn("************");
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("************", ip);
    }

    @Test
    @DisplayName("测试从WL-Proxy-Client-IP头获取IP")
    void testGetIpFromWLProxyClientIp() {
        // 模拟WL-Proxy-Client-IP头
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn(null);
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn("*************");
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("*************", ip);
    }

    @Test
    @DisplayName("测试从HTTP_CLIENT_IP头获取IP")
    void testGetIpFromHttpClientIp() {
        // 模拟HTTP_CLIENT_IP头
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn(null);
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn("***********");
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("***********", ip);
    }

    @Test
    @DisplayName("测试从HTTP_X_FORWARDED_FOR头获取IP")
    void testGetIpFromHttpXForwardedFor() {
        // 模拟HTTP_X_FORWARDED_FOR头
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn(null);
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn("************");

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("************", ip);
    }

    @Test
    @DisplayName("测试从RemoteAddr获取IP")
    void testGetIpFromRemoteAddr() {
        // 模拟所有代理头都为空，使用RemoteAddr
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn(null);
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);
        when(mockRequest.getRemoteAddr()).thenReturn("127.0.0.1");

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("127.0.0.1", ip);
    }

    @Test
    @DisplayName("测试所有方法都返回无效值时返回unknown")
    void testAllMethodsReturnInvalidValues() {
        // 模拟所有方法都返回无效值
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn("unknown");
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("");
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn("unknown");
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn("unknown");
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn("");
        when(mockRequest.getRemoteAddr()).thenReturn("unknown");

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("unknown", ip);
    }

    @Test
    @DisplayName("测试处理包含空格的IP地址")
    void testHandleIpWithSpaces() {
        // 模拟包含空格的IP地址
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn("  *************  ");
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("*************", ip);
    }

    @Test
    @DisplayName("测试请求头获取异常时的处理")
    void testHandleHeaderException() {
        // 模拟请求头获取时抛出异常
        when(mockRequest.getHeader("X-Forwarded-For")).thenThrow(new RuntimeException("Header error"));
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("*************");
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("*************", ip);
    }

    @Test
    @DisplayName("测试本地地址检测")
    void testIsLocalhost() {
        assertTrue(IpUtils.isLocalhost("127.0.0.1"));
        assertTrue(IpUtils.isLocalhost("0:0:0:0:0:0:0:1"));
        assertTrue(IpUtils.isLocalhost("localhost"));
        assertTrue(IpUtils.isLocalhost("LOCALHOST"));
        assertTrue(IpUtils.isLocalhost("  127.0.0.1  "));
        
        assertFalse(IpUtils.isLocalhost("***********"));
        assertFalse(IpUtils.isLocalhost("********"));
        assertFalse(IpUtils.isLocalhost(""));
        assertFalse(IpUtils.isLocalhost(null));
        assertFalse(IpUtils.isLocalhost("unknown"));
    }

    @Test
    @DisplayName("测试私有IP地址检测")
    void testIsPrivateIp() {
        // 测试10.x.x.x网段
        assertTrue(IpUtils.isPrivateIp("********"));
        assertTrue(IpUtils.isPrivateIp("**************"));
        assertTrue(IpUtils.isPrivateIp("***********"));
        
        // 测试172.16.x.x - 172.31.x.x网段
        assertTrue(IpUtils.isPrivateIp("**********"));
        assertTrue(IpUtils.isPrivateIp("**************"));
        assertTrue(IpUtils.isPrivateIp("***********"));
        
        // 测试192.168.x.x网段
        assertTrue(IpUtils.isPrivateIp("***********"));
        assertTrue(IpUtils.isPrivateIp("***************"));
        assertTrue(IpUtils.isPrivateIp("*************"));
        
        // 测试公网IP
        assertFalse(IpUtils.isPrivateIp("*******"));
        assertFalse(IpUtils.isPrivateIp("***********"));
        assertFalse(IpUtils.isPrivateIp("**********")); // 不在私有范围
        assertFalse(IpUtils.isPrivateIp("**********")); // 不在私有范围
        assertFalse(IpUtils.isPrivateIp("***********")); // 不在私有范围
        assertFalse(IpUtils.isPrivateIp("***********")); // 不在私有范围
        
        // 测试无效输入
        assertFalse(IpUtils.isPrivateIp(""));
        assertFalse(IpUtils.isPrivateIp(null));
        assertFalse(IpUtils.isPrivateIp("invalid.ip"));
        assertFalse(IpUtils.isPrivateIp("192.168.1"));
        assertFalse(IpUtils.isPrivateIp("***********.1"));
    }

    @Test
    @DisplayName("测试IPv6地址处理")
    void testIpv6Address() {
        // 模拟IPv6地址
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn("2001:db8::1");
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("2001:db8::1", ip);
    }

    @Test
    @DisplayName("测试优先级顺序")
    void testPriorityOrder() {
        // 模拟多个头都有值，应该按优先级返回X-Forwarded-For的值
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn("***********");
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("***********");
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn("***********");
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn("***********");
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn("***********");
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn("***********");
        when(mockRequest.getRemoteAddr()).thenReturn("***********");

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("***********", ip);
    }

    @Test
    @DisplayName("测试混合多IP字符串中的有效IP提取")
    void testMixedMultipleIpsExtraction() {
        // 模拟包含无效和有效IP的混合字符串
        when(mockRequest.getHeader("X-Forwarded-For"))
            .thenReturn("unknown, , *************, unknown, ********");
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("WL-Proxy-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_CLIENT_IP")).thenReturn(null);
        when(mockRequest.getHeader("HTTP_X_FORWARDED_FOR")).thenReturn(null);

        String ip = IpUtils.getClientIpAddress(mockRequest);
        assertEquals("*************", ip);
    }
} 