package com.extracme.saas.autocare.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.repository.TableOrgInfoService;

/**
 * 组织层级遍历工具类测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("组织层级遍历工具类测试")
class OrgHierarchyUtilsTest {

    @Mock(lenient = true)
    private TableOrgInfoService orgInfoService;

    @InjectMocks
    private OrgHierarchyUtils orgHierarchyUtils;

    private MtcOrgInfo createOrgInfo(String orgId, String parentId, String orgName) {
        MtcOrgInfo orgInfo = new MtcOrgInfo();
        orgInfo.setOrgId(orgId);
        orgInfo.setParentId(parentId);
        orgInfo.setOrgName(orgName);
        return orgInfo;
    }

    @BeforeEach
    void setUp() {
        // 模拟组织层级结构：
        // ROOT (null)
        // ├── ORG001 (总公司)
        // │   ├── ORG001001 (分公司A)
        // │   │   └── ORG001001001 (部门A1)
        // │   └── ORG001002 (分公司B)
        // └── ORG002 (独立公司)

        // 模拟 findDirectChildrenByParentId 方法
        when(orgInfoService.findDirectChildrenByParentId("ORG001"))
            .thenReturn(Arrays.asList(
                createOrgInfo("ORG001001", "ORG001", "分公司A"),
                createOrgInfo("ORG001002", "ORG001", "分公司B")
            ));

        when(orgInfoService.findDirectChildrenByParentId("ORG001001"))
            .thenReturn(Arrays.asList(
                createOrgInfo("ORG001001001", "ORG001001", "部门A1")
            ));

        when(orgInfoService.findDirectChildrenByParentId("ORG001002"))
            .thenReturn(Collections.emptyList());

        when(orgInfoService.findDirectChildrenByParentId("ORG001001001"))
            .thenReturn(Collections.emptyList());

        when(orgInfoService.findDirectChildrenByParentId("ORG002"))
            .thenReturn(Collections.emptyList());

        // 模拟 findAllDescendantOrgIdsBatch 方法（优化的批量查询）
        when(orgInfoService.findAllDescendantOrgIdsBatch(Arrays.asList("ORG001")))
            .thenReturn(Arrays.asList("ORG001", "ORG001001", "ORG001002", "ORG001001001"));

        when(orgInfoService.findAllDescendantOrgIdsBatch(Arrays.asList("ORG001001")))
            .thenReturn(Arrays.asList("ORG001001", "ORG001001001"));

        when(orgInfoService.findAllDescendantOrgIdsBatch(Arrays.asList("ORG002")))
            .thenReturn(Arrays.asList("ORG002"));

        when(orgInfoService.findAllDescendantOrgIdsBatch(Collections.emptyList()))
            .thenReturn(Collections.emptyList());

        // 保持原有方法的模拟以确保向后兼容性
        when(orgInfoService.findAllDescendantOrgIds(Arrays.asList("ORG001")))
            .thenReturn(Arrays.asList("ORG001", "ORG001001", "ORG001002", "ORG001001001"));

        when(orgInfoService.findAllDescendantOrgIds(Arrays.asList("ORG001001")))
            .thenReturn(Arrays.asList("ORG001001", "ORG001001001"));

        when(orgInfoService.findAllDescendantOrgIds(Arrays.asList("ORG002")))
            .thenReturn(Arrays.asList("ORG002"));

        when(orgInfoService.findAllDescendantOrgIds(Collections.emptyList()))
            .thenReturn(Collections.emptyList());
    }

    @Test
    @DisplayName("测试计算用户可访问的所有组织ID列表 - 正常情况")
    void testCalculateAllAccessibleOrgIds_Normal() {
        // 测试用户直接关联总公司，应该能访问所有子组织
        List<String> directOrgIds = Arrays.asList("ORG001");
        List<String> result = orgHierarchyUtils.calculateAllAccessibleOrgIds(directOrgIds);

        assertNotNull(result);
        assertEquals(4, result.size());
        assertTrue(result.contains("ORG001"));
        assertTrue(result.contains("ORG001001"));
        assertTrue(result.contains("ORG001002"));
        assertTrue(result.contains("ORG001001001"));

        verify(orgInfoService).findAllDescendantOrgIdsBatch(directOrgIds);
    }

    @Test
    @DisplayName("测试计算用户可访问的所有组织ID列表 - 空输入")
    void testCalculateAllAccessibleOrgIds_EmptyInput() {
        List<String> result1 = orgHierarchyUtils.calculateAllAccessibleOrgIds(null);
        List<String> result2 = orgHierarchyUtils.calculateAllAccessibleOrgIds(Collections.emptyList());

        assertTrue(result1.isEmpty());
        assertTrue(result2.isEmpty());

        verify(orgInfoService, never()).findAllDescendantOrgIds(any());
    }

    @Test
    @DisplayName("测试计算用户可访问的所有组织ID列表 - 异常处理")
    void testCalculateAllAccessibleOrgIds_Exception() {
        List<String> directOrgIds = Arrays.asList("ORG001");
        
        // 模拟异常情况
        when(orgInfoService.findAllDescendantOrgIdsBatch(directOrgIds))
            .thenThrow(new RuntimeException("数据库连接异常"));

        List<String> result = orgHierarchyUtils.calculateAllAccessibleOrgIds(directOrgIds);

        // 异常时应该返回直接关联的组织列表
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.contains("ORG001"));
    }

    @Test
    @DisplayName("测试验证组织层级结构的完整性 - 正常情况")
    void testValidateOrgHierarchy_Normal() {
        List<String> orgIds = Arrays.asList("ORG001");
        boolean result = orgHierarchyUtils.validateOrgHierarchy(orgIds);

        assertTrue(result);
        verify(orgInfoService).findAllDescendantOrgIdsBatch(orgIds);
    }

    @Test
    @DisplayName("测试验证组织层级结构的完整性 - 空输入")
    void testValidateOrgHierarchy_EmptyInput() {
        boolean result1 = orgHierarchyUtils.validateOrgHierarchy(null);
        boolean result2 = orgHierarchyUtils.validateOrgHierarchy(Collections.emptyList());

        assertTrue(result1);
        assertTrue(result2);

        verify(orgInfoService, never()).findAllDescendantOrgIds(any());
    }

    @Test
    @DisplayName("测试验证组织层级结构的完整性 - 异常情况")
    void testValidateOrgHierarchy_Exception() {
        List<String> orgIds = Arrays.asList("ORG001");
        
        when(orgInfoService.findAllDescendantOrgIdsBatch(orgIds))
            .thenThrow(new RuntimeException("循环引用异常"));

        boolean result = orgHierarchyUtils.validateOrgHierarchy(orgIds);

        assertFalse(result);
    }

    @Test
    @DisplayName("测试获取组织层级统计信息")
    void testGetHierarchyStats() {
        List<String> directOrgIds = Arrays.asList("ORG001");
        
        OrgHierarchyUtils.OrgHierarchyStats stats = orgHierarchyUtils.getHierarchyStats(directOrgIds);

        assertNotNull(stats);
        assertEquals(1, stats.getDirectOrgCount());
        assertEquals(3, stats.getChildOrgCount()); // 4 total - 1 direct = 3 children
        assertEquals(4, stats.getTotalAccessibleCount());

        String statsString = stats.toString();
        assertTrue(statsString.contains("直接关联=1"));
        assertTrue(statsString.contains("子组织=3"));
        assertTrue(statsString.contains("总可访问=4"));
    }

    @Test
    @DisplayName("测试批量查询性能优化 - 验证只调用一次数据库查询")
    void testBatchQueryPerformanceOptimization() {
        List<String> directOrgIds = Arrays.asList("ORG001", "ORG002");

        // 模拟批量查询返回结果
        when(orgInfoService.findAllDescendantOrgIdsBatch(directOrgIds))
            .thenReturn(Arrays.asList("ORG001", "ORG001001", "ORG001002", "ORG002"));

        List<String> result = orgHierarchyUtils.calculateAllAccessibleOrgIds(directOrgIds);

        assertNotNull(result);
        assertEquals(4, result.size());

        // 验证只调用了一次批量查询方法，而不是多次单独查询
        verify(orgInfoService, times(1)).findAllDescendantOrgIdsBatch(directOrgIds);

        // 验证没有调用原有的单个查询方法
        verify(orgInfoService, never()).findDirectChildrenByParentId(anyString());
    }

    @Test
    @DisplayName("测试字段关系修正 - uniqueId和parentId的正确映射")
    void testFieldRelationshipCorrection() {
        // 这个测试主要验证我们的实现逻辑是否正确处理了字段关系
        // 在实际的批量查询中，应该使用uniqueId和parentId建立层级关系

        List<String> directOrgIds = Arrays.asList("ORG001");

        // 模拟批量查询，验证字段关系的正确性
        when(orgInfoService.findAllDescendantOrgIdsBatch(directOrgIds))
            .thenReturn(Arrays.asList("ORG001", "ORG001001", "ORG001002", "ORG001001001"));

        List<String> result = orgHierarchyUtils.calculateAllAccessibleOrgIds(directOrgIds);

        assertNotNull(result);
        assertEquals(4, result.size());

        // 验证结果包含了正确的层级关系
        assertTrue(result.contains("ORG001"));      // 根组织
        assertTrue(result.contains("ORG001001"));   // 子组织
        assertTrue(result.contains("ORG001002"));   // 子组织
        assertTrue(result.contains("ORG001001001")); // 孙组织

        // 验证使用了优化的批量查询方法
        verify(orgInfoService).findAllDescendantOrgIdsBatch(directOrgIds);
    }
}
