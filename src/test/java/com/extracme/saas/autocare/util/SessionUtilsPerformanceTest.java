package com.extracme.saas.autocare.util;

import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SessionUtils性能测试
 * 验证JWT token优化后的性能提升效果
 */
class SessionUtilsPerformanceTest {

    private JwtUtil jwtUtil;
    private JwtUserInfoDTO testUserInfo;

    @BeforeEach
    void setUp() {
        jwtUtil = new JwtUtil();
        ReflectionTestUtils.setField(jwtUtil, "secret", "test-secret-key-for-jwt-token-generation-performance-test");
        ReflectionTestUtils.setField(jwtUtil, "expiration", 86400L);

        testUserInfo = createTestUserInfo();
    }

    /**
     * 测试JWT token生成性能
     */
    @Test
    void testTokenGenerationPerformance() {
        int iterations = 1000;
        
        // 预热
        for (int i = 0; i < 100; i++) {
            jwtUtil.generateTokenWithUserInfo(testUserInfo);
        }

        // 性能测试
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            String token = jwtUtil.generateTokenWithUserInfo(testUserInfo);
            assertNotNull(token);
        }
        long endTime = System.currentTimeMillis();

        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;

        System.out.printf("Token generation performance: %d iterations in %d ms, avg: %.2f ms/token%n", 
                         iterations, totalTime, avgTime);

        // 验证性能要求：平均每个token生成时间应该小于10ms
        assertTrue(avgTime < 10.0, "Token generation should be faster than 10ms per token, actual: " + avgTime);
    }

    /**
     * 测试JWT token解析性能
     */
    @Test
    void testTokenParsingPerformance() {
        // 生成测试token
        String token = jwtUtil.generateTokenWithUserInfo(testUserInfo);
        int iterations = 1000;

        // 预热
        for (int i = 0; i < 100; i++) {
            jwtUtil.getUserInfoFromToken(token);
        }

        // 性能测试
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            JwtUserInfoDTO userInfo = jwtUtil.getUserInfoFromToken(token);
            assertNotNull(userInfo);
            assertEquals(testUserInfo.getUserId(), userInfo.getUserId());
        }
        long endTime = System.currentTimeMillis();

        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;

        System.out.printf("Token parsing performance: %d iterations in %d ms, avg: %.2f ms/token%n", 
                         iterations, totalTime, avgTime);

        // 验证性能要求：平均每个token解析时间应该小于5ms
        assertTrue(avgTime < 5.0, "Token parsing should be faster than 5ms per token, actual: " + avgTime);
    }

    /**
     * 测试并发性能
     */
    @Test
    void testConcurrentPerformance() throws InterruptedException {
        int threadCount = 10;
        int iterationsPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        // 生成测试token
        String token = jwtUtil.generateTokenWithUserInfo(testUserInfo);

        long startTime = System.currentTimeMillis();

        // 启动并发测试
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < iterationsPerThread; j++) {
                        // 测试token解析
                        JwtUserInfoDTO userInfo = jwtUtil.getUserInfoFromToken(token);
                        assertNotNull(userInfo);
                        assertEquals(testUserInfo.getUserId(), userInfo.getUserId());

                        // 测试token生成
                        String newToken = jwtUtil.generateTokenWithUserInfo(testUserInfo);
                        assertNotNull(newToken);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        assertTrue(latch.await(30, TimeUnit.SECONDS), "Concurrent test should complete within 30 seconds");
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        int totalOperations = threadCount * iterationsPerThread * 2; // 每次迭代包含解析和生成两个操作

        System.out.printf("Concurrent performance: %d operations with %d threads in %d ms, avg: %.2f ms/operation%n", 
                         totalOperations, threadCount, totalTime, (double) totalTime / totalOperations);

        executor.shutdown();
    }

    /**
     * 测试内存使用情况
     */
    @Test
    void testMemoryUsage() {
        int tokenCount = 1000;
        List<String> tokens = new ArrayList<>();

        // 记录初始内存
        System.gc();
        long initialMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();

        // 生成大量token
        for (int i = 0; i < tokenCount; i++) {
            JwtUserInfoDTO userInfo = createTestUserInfo();
            userInfo.setUserId((long) i); // 确保每个token都不同
            String token = jwtUtil.generateTokenWithUserInfo(userInfo);
            tokens.add(token);
        }

        // 记录使用后内存
        System.gc();
        long usedMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        long memoryIncrease = usedMemory - initialMemory;

        System.out.printf("Memory usage: %d tokens use %d bytes, avg: %.2f bytes/token%n", 
                         tokenCount, memoryIncrease, (double) memoryIncrease / tokenCount);

        // 验证平均每个token的内存使用应该合理（小于10KB）
        double avgMemoryPerToken = (double) memoryIncrease / tokenCount;
        assertTrue(avgMemoryPerToken < 10240, "Memory usage per token should be less than 10KB, actual: " + avgMemoryPerToken);

        // 清理
        tokens.clear();
    }

    /**
     * 测试token大小分布
     */
    @Test
    void testTokenSizeDistribution() {
        int sampleCount = 100;
        List<Integer> tokenSizes = new ArrayList<>();

        for (int i = 0; i < sampleCount; i++) {
            JwtUserInfoDTO userInfo = createVariableUserInfo(i);
            String token = jwtUtil.generateTokenWithUserInfo(userInfo);
            tokenSizes.add(token.length());
        }

        // 计算统计信息
        int minSize = Collections.min(tokenSizes);
        int maxSize = Collections.max(tokenSizes);
        double avgSize = tokenSizes.stream().mapToInt(Integer::intValue).average().orElse(0.0);

        System.out.printf("Token size distribution: min=%d, max=%d, avg=%.2f%n", minSize, maxSize, avgSize);

        // 验证token大小在合理范围内
        assertTrue(minSize > 0, "Token size should be positive");
        assertTrue(maxSize < 8192, "Maximum token size should be less than 8KB");
        assertTrue(avgSize < 4096, "Average token size should be less than 4KB");
    }

    /**
     * 创建基础测试用户信息
     */
    private JwtUserInfoDTO createTestUserInfo() {
        JwtUserInfoDTO userInfo = new JwtUserInfoDTO();
        
        userInfo.setUserId(12345L);
        userInfo.setUsername("testuser");
        userInfo.setNickname("测试用户");
        userInfo.setMobile("***********"); // 完整手机号
        userInfo.setAccountType(1);
        userInfo.setApprovalLevel(2);
        userInfo.setStatus(1);

        // 权限和组织信息现在存储在Redis缓存中，不再包含在JWT token中
        // Set<String> permissions = new HashSet<>();
        // permissions.add("user:read");
        // permissions.add("user:write");
        // permissions.add("order:read");
        // userInfo.setPermissions(permissions);

        // List<String> orgIds = Arrays.asList("org1", "org2");
        // List<String> allAccessibleOrgIds = Arrays.asList("org1", "org2", "org3", "org4");
        // userInfo.setOrgIds(orgIds);
        // userInfo.setAllAccessibleOrgIds(allAccessibleOrgIds);

        userInfo.setTenantId(100L);
        userInfo.setTenantCode("tenant001");
        userInfo.setTenantName("测试租户");

        long currentTime = System.currentTimeMillis();
        userInfo.setLoginTime(currentTime);
        userInfo.setExpireTime(currentTime + ********);

        userInfo.setIpaddr("*************");

        return userInfo;
    }

    /**
     * 创建可变大小的测试用户信息（用于测试token大小分布）
     */
    private JwtUserInfoDTO createVariableUserInfo(int index) {
        JwtUserInfoDTO userInfo = createTestUserInfo();
        
        // 变化用户ID
        userInfo.setUserId((long) index);
        
        // 权限和组织信息现在存储在Redis缓存中，不再包含在JWT token中
        // 这里注释掉权限和组织信息的设置，因为它们已从JwtUserInfoDTO中移除

        // 变化权限数量
        // Set<String> permissions = new HashSet<>();
        // int permCount = (index % 10) + 1; // 1-10个权限
        // for (int i = 0; i < permCount; i++) {
        //     permissions.add("perm" + i + ":action" + (index % 3));
        // }
        // userInfo.setPermissions(permissions);

        // 变化组织数量
        // List<String> orgIds = new ArrayList<>();
        // List<String> allAccessibleOrgIds = new ArrayList<>();
        // int orgCount = (index % 5) + 1; // 1-5个组织
        // for (int i = 0; i < orgCount; i++) {
        //     orgIds.add("org" + index + "_" + i);
        //     allAccessibleOrgIds.add("org" + index + "_" + i);
        //     // 添加子组织
        //     for (int j = 0; j < 2; j++) {
        //         allAccessibleOrgIds.add("org" + index + "_" + i + "_child" + j);
        //     }
        // }
        // userInfo.setOrgIds(orgIds);
        // userInfo.setAllAccessibleOrgIds(allAccessibleOrgIds);
        
        return userInfo;
    }
}
