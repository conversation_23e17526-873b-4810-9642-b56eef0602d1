package com.extracme.saas.autocare.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ExcelExportUtil 游标分页功能测试
 */
@SpringBootTest
public class ExcelExportUtilTest {

    @Mock
    private HttpServletResponse response;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试数据实体
     */
    public static class TestData {
        private Long id;
        private String name;

        public TestData(Long id, String name) {
            this.id = id;
            this.name = name;
        }

        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }



    /**
     * 测试游标分页数据提供者
     */
    @Test
    void testCursorPagingDataProvider() {
        // 模拟游标分页数据提供者
        ExcelExportUtil.CursorDataProvider<TestData> cursorDataProvider = lastId -> {
            List<TestData> data = new ArrayList<>();
            long start = lastId + 1;
            int pageSize = 10; // 固定页大小
            for (long i = start; i < start + pageSize && i <= 100; i++) {
                data.add(new TestData(i, "Name" + i));
            }
            return data;
        };

        // 创建配置
        ExcelExportUtil.ExportConfig<TestData> config = ExcelExportUtil.builder(TestData.class)
                .fileName("test_cursor.xlsx")
                .cursorDataProvider(cursorDataProvider)
                .batchSize(10)
                .build();

        // 验证配置
        assertNotNull(config);
        assertEquals(10, config.getBatchSize());
    }

    /**
     * 测试构建器验证
     */
    @Test
    void testBuilderValidation() {
        // 测试缺少必要参数的情况
        assertThrows(IllegalArgumentException.class, () -> {
            ExcelExportUtil.builder(TestData.class)
                    .fileName("test.xlsx")
                    .build(); // 缺少数据提供者
        });

        // 测试缺少文件名的情况
        assertThrows(IllegalArgumentException.class, () -> {
            ExcelExportUtil.builder(TestData.class)
                    .cursorDataProvider(lastId -> new ArrayList<>())
                    .build(); // 缺少文件名
        });
    }

    /**
     * 测试进度回调
     */
    @Test
    void testProgressCallback() {
        final int[] callbackCount = {0};
        final int[] lastCurrent = {0};

        ExcelExportUtil.ProgressCallback progressCallback = (current, total) -> {
            callbackCount[0]++;
            lastCurrent[0] = current;
        };

        // 模拟游标分页数据提供者
        ExcelExportUtil.CursorDataProvider<TestData> cursorDataProvider = lastId -> {
            if (lastId >= 15) return new ArrayList<>(); // 模拟3批数据

            List<TestData> data = new ArrayList<>();
            int pageSize = 5;
            for (int i = 0; i < pageSize; i++) {
                long id = lastId + i + 1;
                if (id <= 15) {
                    data.add(new TestData(id, "Name" + id));
                }
            }
            return data;
        };

        ExcelExportUtil.ExportConfig<TestData> config = ExcelExportUtil.builder(TestData.class)
                .fileName("test_progress.xlsx")
                .cursorDataProvider(cursorDataProvider)
                .batchSize(5)
                .progressCallback(progressCallback)
                .build();

        assertNotNull(config.getProgressCallback());
    }

    /**
     * 测试游标分页的数据收集逻辑
     */
    @Test
    void testCursorDataCollection() {
        // 模拟游标分页数据提供者，返回有限数据
        ExcelExportUtil.CursorDataProvider<TestData> cursorDataProvider = lastId -> {
            List<TestData> data = new ArrayList<>();
            long start = lastId + 1;
            int pageSize = 5; // 固定页大小
            // 只返回20条数据
            for (long i = start; i < start + pageSize && i <= 20; i++) {
                data.add(new TestData(i, "Name" + i));
            }
            return data;
        };

        ExcelExportUtil.ExportConfig<TestData> config = ExcelExportUtil.builder(TestData.class)
                .fileName("test_cursor_collection.xlsx")
                .cursorDataProvider(cursorDataProvider)
                .batchSize(5)
                .build();

        // 验证配置正确
        assertNotNull(config.getCursorDataProvider());
    }
}
