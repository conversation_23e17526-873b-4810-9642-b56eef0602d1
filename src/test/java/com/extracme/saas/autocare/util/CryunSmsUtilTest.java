package com.extracme.saas.autocare.util;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.util.Arrays;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.config.SmsConfig;
import com.extracme.saas.autocare.model.dto.SendSmsDTO;

/**
 * CryunSmsUtil 单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class CryunSmsUtilTest {

    private SmsConfig mockSmsConfig;
    private SmsConfig.Account mockAccount;
    private SmsConfig.Api mockApi;

    @BeforeEach
    public void setup() {
        // 重置统计计数器
        CryunSmsUtil.resetStatistics();
        
        // 创建 Mock 对象
        mockSmsConfig = mock(SmsConfig.class);
        mockAccount = mock(SmsConfig.Account.class);
        mockApi = mock(SmsConfig.Api.class);
        
        // 设置默认配置
        when(mockSmsConfig.getMain()).thenReturn(mockAccount);
        when(mockSmsConfig.getApi()).thenReturn(mockApi);
        
        when(mockAccount.getAccessKey()).thenReturn("test_access_key");
        when(mockAccount.getSecret()).thenReturn("test_secret");
        when(mockAccount.getSign()).thenReturn("test_sign");
        
        when(mockApi.getHost()).thenReturn("http://test.api.com");
        when(mockApi.getBatchPath()).thenReturn("/batch/send");
        when(mockApi.getCharset()).thenReturn("UTF-8");
        when(mockApi.getMaxBatchSize()).thenReturn(1000);
        when(mockApi.getConnectionTimeout()).thenReturn(30000);
        when(mockApi.getReadTimeout()).thenReturn(60000);
    }

    /**
     * 测试发送短信 - 成功场景（基于main方法的测试数据）
     * 由于涉及HTTP请求，这里主要测试参数校验和配置获取
     */
    @Test
    public void testSendSms_Success() {
        try (MockedStatic<SpringContextUtil> mockedSpringUtil = mockStatic(SpringContextUtil.class)) {
            // Mock SpringContextUtil
            mockedSpringUtil.when(() -> SpringContextUtil.getBean(SmsConfig.class))
                .thenReturn(mockSmsConfig);
            
            // 准备测试数据（基于main方法）
            SendSmsDTO sendSmsDTO = new SendSmsDTO();
            sendSmsDTO.setMobile("***********");
            sendSmsDTO.setContent("的：5116。如非本人操作，请忽略本短信。");
            
            // 执行测试 - 由于HTTP请求会失败，我们主要验证参数校验通过
            // 实际的HTTP请求会失败，但统计计数器会更新
            try {
                String result = CryunSmsUtil.sendSms(sendSmsDTO, false);
                System.out.println(result);
                // 如果没有抛出异常，说明参数校验通过了
                // 由于HTTP请求会失败，result可能为null，这是正常的
            } catch (IllegalArgumentException e) {
                // 如果是参数校验异常，测试失败
                throw e;
            } catch (Exception e) {
                // HTTP请求相关的异常是预期的，忽略
                assertTrue(e instanceof RuntimeException);
            }
            
            // 验证统计计数器更新
            String stats = CryunSmsUtil.getStatistics();
            assertTrue(stats.contains("总数:1"));
        }
    }

    /**
     * 测试发送短信 - 批量发送成功
     */
    @Test
    public void testSendSms_BatchSuccess() {
        try (MockedStatic<SpringContextUtil> mockedSpringUtil = mockStatic(SpringContextUtil.class)) {
            // Mock SpringContextUtil
            mockedSpringUtil.when(() -> SpringContextUtil.getBean(SmsConfig.class))
                .thenReturn(mockSmsConfig);
            
            // 准备测试数据
            SendSmsDTO sendSmsDTO = new SendSmsDTO();
            sendSmsDTO.setMobiles(Arrays.asList("***********", "13800138000"));
            sendSmsDTO.setContent("验证码：1234");
            
            // 执行测试
            try {
                CryunSmsUtil.sendSms(sendSmsDTO, true);
            } catch (Exception e) {
                // HTTP请求失败是预期的
            }
            
            // 由于我们无法完全mock HTTP请求，这里主要验证参数校验通过
            // 实际的HTTP请求会失败，但我们可以验证统计计数器
            assertTrue(CryunSmsUtil.getStatistics().contains("总数:1"));
        }
    }

    /**
     * 测试参数校验 - 短信内容为空
     */
    @Test
    public void testSendSms_EmptyContent() {
        // 准备测试数据
        SendSmsDTO sendSmsDTO = new SendSmsDTO();
        sendSmsDTO.setMobile("***********");
        sendSmsDTO.setContent("");
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            CryunSmsUtil.sendSms(sendSmsDTO, false);
        });
        
        assertEquals("短信内容不能为空", exception.getMessage());
    }

    /**
     * 测试参数校验 - 手机号格式错误
     */
    @Test
    public void testSendSms_InvalidMobile() {
        // 准备测试数据
        SendSmsDTO sendSmsDTO = new SendSmsDTO();
        sendSmsDTO.setMobile("12345678901"); // 错误的手机号格式
        sendSmsDTO.setContent("测试内容");
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            CryunSmsUtil.sendSms(sendSmsDTO, false);
        });
        
        assertTrue(exception.getMessage().contains("手机号格式不正确"));
    }

    /**
     * 测试参数校验 - 短信内容过长
     */
    @Test
    public void testSendSms_ContentTooLong() {
        // 准备测试数据
        SendSmsDTO sendSmsDTO = new SendSmsDTO();
        sendSmsDTO.setMobile("***********");
        sendSmsDTO.setContent("a"); // 超过500字符限制
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            CryunSmsUtil.sendSms(sendSmsDTO, false);
        });
        
        assertEquals("短信内容长度不能超过500字符", exception.getMessage());
    }

    /**
     * 测试参数校验 - DTO为空
     */
    @Test
    public void testSendSms_NullDTO() {
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            CryunSmsUtil.sendSms(null, false);
        });
        
        assertEquals("短信发送参数不能为空", exception.getMessage());
    }

    /**
     * 测试配置获取失败
     */
    @Test
    public void testSendSms_ConfigFailure() {
        try (MockedStatic<SpringContextUtil> mockedSpringUtil = mockStatic(SpringContextUtil.class)) {
            // Mock SpringContextUtil 抛出异常
            mockedSpringUtil.when(() -> SpringContextUtil.getBean(SmsConfig.class))
                .thenThrow(new RuntimeException("Bean not found"));
            
            // 准备测试数据
            SendSmsDTO sendSmsDTO = new SendSmsDTO();
            sendSmsDTO.setMobile("***********");
            sendSmsDTO.setContent("测试内容");
            
            // 执行测试并验证异常
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                CryunSmsUtil.sendSms(sendSmsDTO, false);
            });
            
            assertTrue(exception.getMessage().contains("短信配置未初始化"));
        }
    }

    /**
     * 测试批量发送数量超限
     */
    @Test
    public void testSendSms_BatchSizeExceeded() {
        try (MockedStatic<SpringContextUtil> mockedSpringUtil = mockStatic(SpringContextUtil.class)) {
            // Mock SpringContextUtil
            mockedSpringUtil.when(() -> SpringContextUtil.getBean(SmsConfig.class))
                .thenReturn(mockSmsConfig);
            
            // 设置较小的批量限制
            when(mockApi.getMaxBatchSize()).thenReturn(2);
            
            // 准备测试数据（超过限制）
            SendSmsDTO sendSmsDTO = new SendSmsDTO();
            sendSmsDTO.setMobiles(Arrays.asList("***********", "13800138000", "13900139000"));
            sendSmsDTO.setContent("测试内容");
            
            // 执行测试并验证异常
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
                CryunSmsUtil.sendSms(sendSmsDTO, true);
            });
            
            assertTrue(exception.getMessage().contains("批量发送数量不能超过"));
        }
    }

    /**
     * 测试统计功能
     */
    @Test
    public void testStatistics() {
        // 重置统计
        CryunSmsUtil.resetStatistics();
        
        // 验证初始状态
        String initialStats = CryunSmsUtil.getStatistics();
        assertTrue(initialStats.contains("总数:0"));
        assertTrue(initialStats.contains("成功:0"));
        assertTrue(initialStats.contains("失败:0"));
        assertTrue(initialStats.contains("成功率:0.00%"));
        
        // 模拟一些发送操作（会因为配置问题失败，但会更新统计）
        try (MockedStatic<SpringContextUtil> mockedSpringUtil = mockStatic(SpringContextUtil.class)) {
            mockedSpringUtil.when(() -> SpringContextUtil.getBean(SmsConfig.class))
                .thenThrow(new RuntimeException("Config error"));
            
            SendSmsDTO sendSmsDTO = new SendSmsDTO();
            sendSmsDTO.setMobile("***********");
            sendSmsDTO.setContent("测试");
            
            // 尝试发送（会失败）
            try {
                CryunSmsUtil.sendSms(sendSmsDTO, false);
            } catch (Exception e) {
                // 忽略异常，我们只关心统计
            }
            
            // 验证统计更新
            String stats = CryunSmsUtil.getStatistics();
            assertTrue(stats.contains("总数:1"));
        }
    }

    /**
     * 测试手机号校验 - 正确格式
     */
    @Test
    public void testValidateMobile_ValidFormat() {
        // 这些手机号应该通过校验
        String[] validMobiles = {
            "13800138000", "***********", "18888888888", "19999999999"
        };
        
        for (String mobile : validMobiles) {
            SendSmsDTO sendSmsDTO = new SendSmsDTO();
            sendSmsDTO.setMobile(mobile);
            sendSmsDTO.setContent("测试内容");
            
            // 应该不抛出异常（只测试参数校验部分）
            assertDoesNotThrow(() -> {
                try {
                    CryunSmsUtil.sendSms(sendSmsDTO, false);
                } catch (RuntimeException e) {
                    // 忽略配置相关的异常，我们只关心参数校验
                    if (!e.getMessage().contains("短信配置未初始化")) {
                        throw e;
                    }
                }
            });
        }
    }

    /**
     * 测试手机号校验 - 错误格式
     */
    @Test
    public void testValidateMobile_InvalidFormat() {
        // 这些手机号应该校验失败
        String[] invalidMobiles = {
            "12345678901", // 不是1开头的正确格式
            "10000000000", // 第二位不是3-9
            "1380013800",  // 长度不够
            "138001380000", // 长度过长
            "abc12345678", // 包含字母
            ""             // 空字符串
        };
        
        for (String mobile : invalidMobiles) {
            SendSmsDTO sendSmsDTO = new SendSmsDTO();
            sendSmsDTO.setMobile(mobile);
            sendSmsDTO.setContent("测试内容");
            
            assertThrows(IllegalArgumentException.class, () -> {
                CryunSmsUtil.sendSms(sendSmsDTO, false);
            }, "手机号 " + mobile + " 应该校验失败");
        }
    }

    /**
     * 测试手机号为空的情况
     */
    @Test
    public void testSendSms_EmptyMobile() {
        // 准备测试数据
        SendSmsDTO sendSmsDTO = new SendSmsDTO();
        sendSmsDTO.setMobile(null);
        sendSmsDTO.setContent("测试内容");
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            CryunSmsUtil.sendSms(sendSmsDTO, false);
        });
        
        assertEquals("手机号不能为空", exception.getMessage());
    }

    /**
     * 测试批量发送手机号为空的情况
     */
    @Test
    public void testSendSms_EmptyMobiles() {
        // 准备测试数据
        SendSmsDTO sendSmsDTO = new SendSmsDTO();
        sendSmsDTO.setMobiles(Arrays.asList()); // 空列表
        sendSmsDTO.setContent("测试内容");
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            CryunSmsUtil.sendSms(sendSmsDTO, true);
        });
        
        assertEquals("手机号不能为空", exception.getMessage());
    }

    /**
     * 测试发送短信 - 成功场景（基于main方法的测试数据）
     * 由于涉及HTTP请求，这里主要测试参数校验和配置获取
     */
    @Test
    public void testSendSms_Success_Debug() {
        try (MockedStatic<SpringContextUtil> mockedSpringUtil = mockStatic(SpringContextUtil.class)) {
            // Mock SpringContextUtil 并添加调试信息
            mockedSpringUtil.when(() -> SpringContextUtil.getBean(SmsConfig.class))
                .thenAnswer(invocation -> {
                    System.out.println("Mock SpringContextUtil.getBean() called");
                    return mockSmsConfig;
                });
            
            // 准备测试数据
            SendSmsDTO sendSmsDTO = new SendSmsDTO();
            sendSmsDTO.setMobile("***********");
            sendSmsDTO.setContent("的：5116。如非本人操作，请忽略本短信。");
            
            // 验证 Mock 对象不为空
            assertNotNull(mockSmsConfig);
            assertNotNull(mockAccount);
            assertNotNull(mockApi);
            
            // 执行测试
            try {
                String result = CryunSmsUtil.sendSms(sendSmsDTO, false);
                System.out.println("发送结果: " + result);
            } catch (Exception e) {
                System.out.println("异常类型: " + e.getClass().getSimpleName());
                System.out.println("异常消息: " + e.getMessage());
                if (!(e instanceof IllegalArgumentException)) {
                    // 非参数校验异常是可以接受的
                    assertTrue(true);
                } else {
                    throw e;
                }
            }
            
            // 验证统计
            String stats = CryunSmsUtil.getStatistics();
            System.out.println("统计信息: " + stats);
            assertTrue(stats.contains("总数:1"));
        }
    }
}
