package com.extracme.saas.autocare.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * OperateLogUtil 角色相关方法测试
 */
@Slf4j
@DisplayName("操作日志工具类角色相关方法测试")
class OperateLogUtilRoleTest {

    @Test
    @DisplayName("测试角色启用日志记录方法")
    void testRecordRoleEnable() {
        log.info("开始测试角色启用日志记录方法");
        
        try {
            // 测试角色启用日志记录
            OperateLogUtil.recordRoleEnable("测试角色");
            log.info("✅ 角色启用日志记录方法调用成功");
        } catch (Exception e) {
            log.error("❌ 角色启用日志记录方法调用失败", e);
            throw e;
        }
    }

    @Test
    @DisplayName("测试角色禁用日志记录方法")
    void testRecordRoleDisable() {
        log.info("开始测试角色禁用日志记录方法");
        
        try {
            // 测试角色禁用日志记录
            OperateLogUtil.recordRoleDisable("测试角色");
            log.info("✅ 角色禁用日志记录方法调用成功");
        } catch (Exception e) {
            log.error("❌ 角色禁用日志记录方法调用失败", e);
            throw e;
        }
    }

    @Test
    @DisplayName("测试角色状态变更日志记录方法")
    void testRecordRoleStatusChange() {
        log.info("开始测试角色状态变更日志记录方法");
        
        try {
            // 测试角色状态变更日志记录
            OperateLogUtil.recordRoleStatusChange("测试角色", 1, 0);
            log.info("✅ 角色状态变更日志记录方法调用成功");
        } catch (Exception e) {
            log.error("❌ 角色状态变更日志记录方法调用失败", e);
            throw e;
        }
    }

    @Test
    @DisplayName("测试角色状态变更日志记录方法 - 从禁用到启用")
    void testRecordRoleStatusChangeFromDisableToEnable() {
        log.info("开始测试角色状态变更日志记录方法 - 从禁用到启用");
        
        try {
            // 测试角色状态变更日志记录（从禁用到启用）
            OperateLogUtil.recordRoleStatusChange("测试角色", 0, 1);
            log.info("✅ 角色状态变更日志记录方法（从禁用到启用）调用成功");
        } catch (Exception e) {
            log.error("❌ 角色状态变更日志记录方法（从禁用到启用）调用失败", e);
            throw e;
        }
    }

    @Test
    @DisplayName("测试角色状态变更日志记录方法 - 空值处理")
    void testRecordRoleStatusChangeWithNullValues() {
        log.info("开始测试角色状态变更日志记录方法 - 空值处理");
        
        try {
            // 测试空角色名称
            OperateLogUtil.recordRoleStatusChange(null, 1, 0);
            log.info("✅ 空角色名称处理成功");
            
            // 测试空状态值
            OperateLogUtil.recordRoleStatusChange("测试角色", null, null);
            log.info("✅ 空状态值处理成功");
            
            // 测试部分空值
            OperateLogUtil.recordRoleStatusChange("测试角色", null, 1);
            log.info("✅ 部分空值处理成功");
            
            log.info("✅ 角色状态变更日志记录方法空值处理测试通过");
        } catch (Exception e) {
            log.error("❌ 角色状态变更日志记录方法空值处理测试失败", e);
            throw e;
        }
    }

    @Test
    @DisplayName("测试角色基本信息更新日志记录方法")
    void testRecordRoleInfoUpdateDetailed() {
        log.info("开始测试角色基本信息更新日志记录方法");
        
        try {
            // 测试角色名称变更
            OperateLogUtil.recordRoleInfoUpdateDetailed("旧角色名", "新角色名", "旧描述", "新描述");
            log.info("✅ 角色名称和描述变更日志记录成功");
            
            // 测试只有角色名称变更
            OperateLogUtil.recordRoleInfoUpdateDetailed("旧角色名", "新角色名", "描述", "描述");
            log.info("✅ 只有角色名称变更日志记录成功");
            
            // 测试只有描述变更
            OperateLogUtil.recordRoleInfoUpdateDetailed("角色名", "角色名", "旧描述", "新描述");
            log.info("✅ 只有描述变更日志记录成功");
            
            // 测试没有变更
            OperateLogUtil.recordRoleInfoUpdateDetailed("角色名", "角色名", "描述", "描述");
            log.info("✅ 没有变更时日志记录成功");
            
            log.info("✅ 角色基本信息更新日志记录方法测试通过");
        } catch (Exception e) {
            log.error("❌ 角色基本信息更新日志记录方法测试失败", e);
            throw e;
        }
    }

    @Test
    @DisplayName("测试角色创建和删除日志记录方法")
    void testRecordRoleCreateAndDelete() {
        log.info("开始测试角色创建和删除日志记录方法");
        
        try {
            // 测试角色创建日志记录
            OperateLogUtil.recordRoleCreate("新创建的角色");
            log.info("✅ 角色创建日志记录成功");
            
            // 测试角色删除日志记录
            OperateLogUtil.recordRoleDelete("要删除的角色");
            log.info("✅ 角色删除日志记录成功");
            
            log.info("✅ 角色创建和删除日志记录方法测试通过");
        } catch (Exception e) {
            log.error("❌ 角色创建和删除日志记录方法测试失败", e);
            throw e;
        }
    }

    @Test
    @DisplayName("测试所有角色相关日志记录方法的综合调用")
    void testAllRoleLogMethods() {
        log.info("开始测试所有角色相关日志记录方法的综合调用");
        
        String roleName = "综合测试角色";
        
        try {
            // 1. 创建角色
            OperateLogUtil.recordRoleCreate(roleName);
            log.info("1. 角色创建日志记录完成");
            
            // 2. 更新角色基本信息
            OperateLogUtil.recordRoleInfoUpdateDetailed(roleName, roleName + "_更新", "旧描述", "新描述");
            log.info("2. 角色基本信息更新日志记录完成");
            
            // 3. 禁用角色
            OperateLogUtil.recordRoleDisable(roleName);
            log.info("3. 角色禁用日志记录完成");
            
            // 4. 启用角色
            OperateLogUtil.recordRoleEnable(roleName);
            log.info("4. 角色启用日志记录完成");
            
            // 5. 状态变更
            OperateLogUtil.recordRoleStatusChange(roleName, 1, 0);
            log.info("5. 角色状态变更日志记录完成");
            
            // 6. 删除角色
            OperateLogUtil.recordRoleDelete(roleName);
            log.info("6. 角色删除日志记录完成");
            
            log.info("✅ 所有角色相关日志记录方法综合调用测试通过");
        } catch (Exception e) {
            log.error("❌ 角色相关日志记录方法综合调用测试失败", e);
            throw e;
        }
    }
}
