package com.extracme.saas.autocare.util;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.enums.OperateLogModuleTypeEnum;
import com.extracme.saas.autocare.enums.OperateLogOperateTypeEnum;
import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.service.OperateLogService;

/**
 * OperateLogUtil 用户机构关联变更日志记录功能测试
 */
@ExtendWith(MockitoExtension.class)
public class OperateLogUtilOrgChangeTest {

    @Mock
    private OperateLogService operateLogService;

    @Mock
    private TableUserOrgService tableUserOrgService;

    @Mock
    private TableOrgInfoService tableOrgInfoService;

    @BeforeEach
    public void setUp() throws Exception {
        // 使用反射设置静态字段
        setStaticField("operateLogService", operateLogService);
        setStaticField("tableUserOrgService", tableUserOrgService);
        setStaticField("tableOrgInfoService", tableOrgInfoService);
    }

    /**
     * 测试用户机构关联变更日志记录 - 新增机构
     */
    @Test
    public void testRecordUserOrgChangeDetailed_AddOrgs() {
        // 准备测试数据
        Long userId = 1L;
        String mobile = "13800138000";
        String nickname = "张三";
        List<String> oldOrgIds = Arrays.asList("ORG001");
        List<String> newOrgIds = Arrays.asList("ORG001", "ORG002", "ORG003");

        // 模拟机构信息
        MtcOrgInfo org002 = createMtcOrgInfo("ORG002", "销售部");
        MtcOrgInfo org003 = createMtcOrgInfo("ORG003", "技术部");
        when(tableOrgInfoService.findByOrgIds(anyList()))
            .thenReturn(Arrays.asList(org002, org003));

        // 模拟SessionUtils
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            LoginUser loginUser = createMockLoginUser();
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(loginUser);

            // 执行测试
            OperateLogUtil.recordUserOrgChangeDetailed(userId, mobile, nickname, oldOrgIds, newOrgIds);

            // 验证结果 - 由于使用TreeSet排序，ORG002在ORG003之前
            verify(operateLogService, times(1)).recordOperateLogAsync(
                eq(OperateLogModuleTypeEnum.USER_MANAGEMENT),
                eq(OperateLogOperateTypeEnum.UPDATE),
                eq("更新用户：手机号【13800138000】姓名【张三】，机构关联变更：新增机构【销售部、技术部】"),
                eq(null),
                anyLong(),
                anyString(),
                anyString(),
                anyLong()
            );
        }
    }

    /**
     * 测试用户机构关联变更日志记录 - 删除机构
     */
    @Test
    public void testRecordUserOrgChangeDetailed_RemoveOrgs() {
        // 准备测试数据
        Long userId = 1L;
        String mobile = "13800138000";
        String nickname = "张三";
        List<String> oldOrgIds = Arrays.asList("ORG001", "ORG002", "ORG003");
        List<String> newOrgIds = Arrays.asList("ORG001");

        // 模拟机构信息
        MtcOrgInfo org002 = createMtcOrgInfo("ORG002", "销售部");
        MtcOrgInfo org003 = createMtcOrgInfo("ORG003", "技术部");
        when(tableOrgInfoService.findByOrgIds(anyList()))
            .thenReturn(Arrays.asList(org002, org003));

        // 模拟SessionUtils
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            LoginUser loginUser = createMockLoginUser();
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(loginUser);

            // 执行测试
            OperateLogUtil.recordUserOrgChangeDetailed(userId, mobile, nickname, oldOrgIds, newOrgIds);

            // 验证结果 - 由于使用TreeSet排序，ORG002在ORG003之前
            verify(operateLogService, times(1)).recordOperateLogAsync(
                eq(OperateLogModuleTypeEnum.USER_MANAGEMENT),
                eq(OperateLogOperateTypeEnum.UPDATE),
                eq("更新用户：手机号【13800138000】姓名【张三】，机构关联变更：删除机构【销售部、技术部】"),
                eq(null),
                anyLong(),
                anyString(),
                anyString(),
                anyLong()
            );
        }
    }

    /**
     * 测试用户机构关联变更日志记录 - 同时新增和删除机构
     */
    @Test
    public void testRecordUserOrgChangeDetailed_AddAndRemoveOrgs() {
        // 准备测试数据
        Long userId = 1L;
        String mobile = "13800138000";
        String nickname = "张三";
        List<String> oldOrgIds = Arrays.asList("ORG001", "ORG002");
        List<String> newOrgIds = Arrays.asList("ORG001", "ORG003");

        // 模拟机构信息
        MtcOrgInfo org002 = createMtcOrgInfo("ORG002", "销售部");
        MtcOrgInfo org003 = createMtcOrgInfo("ORG003", "技术部");
        when(tableOrgInfoService.findByOrgIds(Arrays.asList("ORG003")))
            .thenReturn(Arrays.asList(org003));
        when(tableOrgInfoService.findByOrgIds(Arrays.asList("ORG002")))
            .thenReturn(Arrays.asList(org002));

        // 模拟SessionUtils
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            LoginUser loginUser = createMockLoginUser();
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(loginUser);

            // 执行测试
            OperateLogUtil.recordUserOrgChangeDetailed(userId, mobile, nickname, oldOrgIds, newOrgIds);

            // 验证结果 - 应该记录新增和删除操作
            verify(operateLogService, times(1)).recordOperateLogAsync(
                eq(OperateLogModuleTypeEnum.USER_MANAGEMENT),
                eq(OperateLogOperateTypeEnum.UPDATE),
                eq("更新用户：手机号【13800138000】姓名【张三】，机构关联变更：新增机构【技术部】，删除机构【销售部】"),
                eq(null),
                anyLong(),
                anyString(),
                anyString(),
                anyLong()
            );
        }
    }

    /**
     * 测试用户机构关联变更日志记录 - 无变更
     */
    @Test
    public void testRecordUserOrgChangeDetailed_NoChanges() {
        // 准备测试数据
        Long userId = 1L;
        String mobile = "13800138000";
        String nickname = "张三";
        List<String> oldOrgIds = Arrays.asList("ORG001", "ORG002");
        List<String> newOrgIds = Arrays.asList("ORG001", "ORG002");

        // 模拟SessionUtils
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            LoginUser loginUser = createMockLoginUser();
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(loginUser);

            // 执行测试
            OperateLogUtil.recordUserOrgChangeDetailed(userId, mobile, nickname, oldOrgIds, newOrgIds);

            // 验证结果 - 无变更时不应该记录日志
            verify(operateLogService, never()).recordOperateLogAsync(any(), any(), anyString(), any(), anyLong(), anyString(), anyString(), anyLong());
        }
    }

    /**
     * 测试用户机构关联变更日志记录 - 当前机构关联查询
     */
    @Test
    public void testRecordUserOrgChangeDetailed_CurrentOrgs() {
        // 准备测试数据
        Long userId = 1L;
        String mobile = "13800138000";
        String nickname = "张三";
        List<String> currentOrgIds = Arrays.asList("ORG001", "ORG002");

        // 模拟当前机构关联查询
        when(tableUserOrgService.findOrgIdsByUserId(userId)).thenReturn(currentOrgIds);

        // 模拟机构信息
        MtcOrgInfo org001 = createMtcOrgInfo("ORG001", "管理部");
        MtcOrgInfo org002 = createMtcOrgInfo("ORG002", "销售部");
        when(tableOrgInfoService.findByOrgIds(currentOrgIds))
            .thenReturn(Arrays.asList(org001, org002));

        // 模拟SessionUtils
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            LoginUser loginUser = createMockLoginUser();
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(loginUser);

            // 执行测试
            OperateLogUtil.recordUserOrgChangeDetailed(userId, mobile, nickname);

            // 验证结果
            verify(operateLogService, times(1)).recordOperateLogAsync(
                eq(OperateLogModuleTypeEnum.USER_MANAGEMENT),
                eq(OperateLogOperateTypeEnum.UPDATE),
                eq("更新用户：手机号【13800138000】姓名【张三】，关联机构：【管理部、销售部】"),
                eq(null),
                anyLong(),
                anyString(),
                anyString(),
                anyLong()
            );
        }
    }

    /**
     * 测试用户机构关联变更日志记录 - 清空所有机构关联
     */
    @Test
    public void testRecordUserOrgChangeDetailed_ClearAllOrgs() {
        // 准备测试数据
        Long userId = 1L;
        String mobile = "13800138000";
        String nickname = "张三";

        // 模拟当前机构关联查询 - 返回空列表
        when(tableUserOrgService.findOrgIdsByUserId(userId)).thenReturn(Collections.emptyList());

        // 模拟SessionUtils
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            LoginUser loginUser = createMockLoginUser();
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(loginUser);

            // 执行测试
            OperateLogUtil.recordUserOrgChangeDetailed(userId, mobile, nickname);

            // 验证结果
            verify(operateLogService, times(1)).recordOperateLogAsync(
                eq(OperateLogModuleTypeEnum.USER_MANAGEMENT),
                eq(OperateLogOperateTypeEnum.UPDATE),
                eq("更新用户：手机号【13800138000】姓名【张三】，清空所有机构关联"),
                eq(null),
                anyLong(),
                anyString(),
                anyString(),
                anyLong()
            );
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 使用反射设置静态字段
     */
    private void setStaticField(String fieldName, Object value) throws Exception {
        Field field = OperateLogUtil.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    /**
     * 创建模拟的MtcOrgInfo对象
     */
    private MtcOrgInfo createMtcOrgInfo(String orgId, String orgName) {
        MtcOrgInfo orgInfo = new MtcOrgInfo();
        orgInfo.setOrgId(orgId);
        orgInfo.setOrgName(orgName);
        return orgInfo;
    }

    /**
     * 创建模拟的LoginUser对象
     */
    private LoginUser createMockLoginUser() {
        SysUser user = new SysUser();
        user.setId(1L);
        user.setNickname("测试用户");
        user.setMobile("13900139000");
        user.setTenantId(1L);

        LoginUser loginUser = new LoginUser();
        loginUser.setUser(user);
        return loginUser;
    }
}
