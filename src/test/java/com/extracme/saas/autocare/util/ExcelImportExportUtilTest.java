package com.extracme.saas.autocare.util;

import com.extracme.saas.autocare.model.excel.RepairItemLibraryCreateExcel;
import com.extracme.saas.autocare.model.excel.RepairItemLibraryExportExcel;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Excel导入导出工具类测试
 * 验证深度优化后的功能
 */
@DisplayName("Excel导入导出工具类测试")
public class ExcelImportExportUtilTest {

    @Test
    @DisplayName("测试导入配置构建器")
    public void testImportConfigBuilder() {
        // 创建导入配置
        ExcelImportUtil.ImportConfig<RepairItemLibraryCreateExcel> config = 
            ExcelImportUtil.builder(RepairItemLibraryCreateExcel.class)
                .dataValidator(this::validateTestData)
                .dataSaver(this::saveTestData)
                .maxRows(1000)
                .errorMessageBuilder(rowIndex -> String.format("第%d行测试失败", rowIndex))
                .build();

        // 验证配置
        assertNotNull(config);
        assertEquals(RepairItemLibraryCreateExcel.class, config.getDataClass());
        assertEquals(1000, config.getMaxRows());
        assertNotNull(config.getDataValidator());
        assertNotNull(config.getDataSaver());
    }

    @Test
    @DisplayName("测试导出配置构建器")
    public void testExportConfigBuilder() {
        // 创建导出配置
        ExcelExportUtil.ExportConfig<RepairItemLibraryExportExcel> config =
            ExcelExportUtil.builder(RepairItemLibraryExportExcel.class)
                .fileName("test.xlsx")
                .sheetName("测试表")
                .cursorDataProvider(this::provideTestData)
                .batchSize(500)
                .progressCallback(this::handleProgress)
                .build();

        // 验证配置
        assertNotNull(config);
        assertEquals(RepairItemLibraryExportExcel.class, config.getDataClass());
        assertEquals("test.xlsx", config.getFileName());
        assertEquals("测试表", config.getSheetName());
        assertEquals(500, config.getBatchSize());
        assertNotNull(config.getCursorDataProvider());
        assertNotNull(config.getProgressCallback());
    }

    @Test
    @DisplayName("测试简单导出功能")
    public void testSimpleExport() {
        // 准备测试数据
        List<RepairItemLibraryExportExcel> testData = createTestExportData();
        
        // 创建模拟响应
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 执行导出
        assertDoesNotThrow(() -> {
            ExcelExportUtil.simpleExport(testData, RepairItemLibraryExportExcel.class, "test.xlsx", response);
        });
        
        // 验证响应
        String contentType = response.getContentType();
        assertNotNull(contentType);
        assertTrue(contentType.contains("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
        assertTrue(response.getContentAsByteArray().length > 0);
    }

    @Test
    @DisplayName("测试两阶段导入流程")
    public void testTwoPhaseImport() {
        // 创建空的输入流（实际测试中应该使用真实的Excel文件）
        InputStream emptyStream = new ByteArrayInputStream(new byte[0]);
        
        // 创建导入配置
        ExcelImportUtil.ImportConfig<RepairItemLibraryCreateExcel> config = 
            ExcelImportUtil.builder(RepairItemLibraryCreateExcel.class)
                .dataValidator(this::validateTestData)
                .dataSaver(this::saveTestData)
                .maxRows(100)
                .build();

        // 执行导入（预期会因为空文件而失败，但不应该抛出未处理的异常）
        ExcelImportUtil.ImportResult<RepairItemLibraryCreateExcel> result = 
            ExcelImportUtil.importFromStream(emptyStream, config);
        
        // 验证结果
        assertNotNull(result);
        // 空文件应该有错误
        assertTrue(result.hasErrors() || result.getTotalRows() == 0);
    }

    @Test
    @DisplayName("测试进度回调功能")
    public void testProgressCallback() {
        final int[] callbackCount = {0};
        final int[] lastCurrent = {0};
        
        ExcelImportUtil.ProgressCallback callback = (current, total, phase) -> {
            callbackCount[0]++;
            lastCurrent[0] = current;
            assertNotNull(phase);
            assertTrue(current >= 0);
        };
        
        // 模拟进度回调
        callback.onProgress(10, 100, "VALIDATION");
        callback.onProgress(50, 100, "SAVING");
        
        assertEquals(2, callbackCount[0]);
        assertEquals(50, lastCurrent[0]);
    }

    @Test
    @DisplayName("测试数据验证器接口")
    public void testDataValidator() {
        ExcelImportUtil.DataValidator<RepairItemLibraryCreateExcel> validator = this::validateTestData;
        
        RepairItemLibraryCreateExcel validData = new RepairItemLibraryCreateExcel();
        validData.setItemName("测试项目");
        validData.setItemType("保养");
        validData.setHourFeeNationalMarketPrice(new BigDecimal("100.00"));
        validData.setMaterialCostNationalMarketPrice(new BigDecimal("200.00"));
        
        // 验证有效数据
        assertDoesNotThrow(() -> validator.validate(validData, 1));
        
        // 验证无效数据
        RepairItemLibraryCreateExcel invalidData = new RepairItemLibraryCreateExcel();
        assertThrows(Exception.class, () -> validator.validate(invalidData, 1));
    }

    // 辅助方法

    private void validateTestData(RepairItemLibraryCreateExcel data, int rowIndex) throws Exception {
        if (data.getItemName() == null || data.getItemName().trim().isEmpty()) {
            throw new Exception("项目名称不能为空");
        }
        if (data.getItemType() == null || data.getItemType().trim().isEmpty()) {
            throw new Exception("项目类型不能为空");
        }
        if (data.getHourFeeNationalMarketPrice() == null) {
            throw new Exception("工时费不能为空");
        }
        if (data.getMaterialCostNationalMarketPrice() == null) {
            throw new Exception("材料费不能为空");
        }
    }

    private void saveTestData(List<RepairItemLibraryCreateExcel> dataList) throws Exception {
        // 模拟保存操作
        if (dataList == null || dataList.isEmpty()) {
            throw new Exception("没有数据需要保存");
        }
        // 实际实现中会保存到数据库
    }

    private List<RepairItemLibraryExportExcel> provideTestData(Long lastId) {
        // 模拟游标分页数据提供
        if (lastId >= 10) {
            return new ArrayList<>(); // 模拟没有更多数据
        }

        return createTestExportData();
    }

    private void handleProgress(int current, int total) {
        // 模拟进度处理
        System.out.println("Progress: " + current + "/" + (total > 0 ? total : "unknown"));
    }

    private List<RepairItemLibraryExportExcel> createTestExportData() {
        List<RepairItemLibraryExportExcel> dataList = new ArrayList<>();
        
        for (int i = 1; i <= 5; i++) {
            RepairItemLibraryExportExcel data = new RepairItemLibraryExportExcel();
            data.setItemNo("TEST" + String.format("%03d", i));
            data.setItemName("测试项目" + i);
            data.setItemTypeName("保养");
            data.setVehicleModelInfo("测试车型" + i);
            data.setHourFeeNationalMarketPrice(new BigDecimal("100.00"));
            data.setMaterialCostNationalMarketPrice(new BigDecimal("200.00"));
            data.setStatusName("启用");
            dataList.add(data);
        }
        
        return dataList;
    }
}
