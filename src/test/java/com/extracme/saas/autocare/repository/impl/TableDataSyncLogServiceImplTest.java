package com.extracme.saas.autocare.repository.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;

import com.extracme.saas.autocare.mapper.extend.SysDataSyncLogExtendMapper;
import com.extracme.saas.autocare.model.entity.SysDataSyncLog;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * TableDataSyncLogServiceImpl 单元测试
 */
@ExtendWith(MockitoExtension.class)
class TableDataSyncLogServiceImplTest {

    @Mock
    private SysDataSyncLogExtendMapper sysDataSyncLogMapper;

    @InjectMocks
    private TableDataSyncLogServiceImpl tableDataSyncLogService;

    private SysDataSyncLog testLog;

    @BeforeEach
    void setUp() {
        testLog = new SysDataSyncLog();
        testLog.setId(1L);
        testLog.setBatchNo("SYNC_1640995200000_1234");
        testLog.setTargetTable("mtc_vehicle_info");
        testLog.setSyncStatus("SUCCESS");
        testLog.setSourceIp("127.0.0.1");
        testLog.setSyncStartTime(new Date());
        testLog.setSyncEndTime(new Date());
        testLog.setSyncDuration(1000L);
    }

    @Test
    void testInsert_Success() {
        try (MockedStatic<SessionUtils> sessionUtils = mockStatic(SessionUtils.class)) {
            sessionUtils.when(SessionUtils::getUsername).thenReturn("test_user");
            
            when(sysDataSyncLogMapper.insertSelective(any(SysDataSyncLog.class))).thenReturn(1);

            // 执行测试
            SysDataSyncLog result = tableDataSyncLogService.insert(testLog);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getCreateTime());
            assertNotNull(result.getUpdateTime());
            assertEquals("test_user", result.getCreateBy());
            assertEquals("test_user", result.getUpdateBy());

            // 验证方法调用
            verify(sysDataSyncLogMapper, times(1)).insertSelective(any(SysDataSyncLog.class));
        }
    }

    @Test
    void testInsert_WithOperator() {
        String operator = "admin_user";
        when(sysDataSyncLogMapper.insertSelective(any(SysDataSyncLog.class))).thenReturn(1);

        // 执行测试
        SysDataSyncLog result = tableDataSyncLogService.insert(testLog, operator);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getCreateTime());
        assertNotNull(result.getUpdateTime());
        assertEquals(operator, result.getCreateBy());
        assertEquals(operator, result.getUpdateBy());

        // 验证方法调用
        verify(sysDataSyncLogMapper, times(1)).insertSelective(any(SysDataSyncLog.class));
    }

    @Test
    void testInsert_NullRecord() {
        // 执行测试
        SysDataSyncLog result = tableDataSyncLogService.insert(null);

        // 验证结果
        assertNull(result);

        // 验证方法未被调用
        verify(sysDataSyncLogMapper, never()).insertSelective(any());
    }

    @Test
    void testSelectById_Success() {
        Long id = 1L;
        when(sysDataSyncLogMapper.selectByPrimaryKey(id)).thenReturn(Optional.of(testLog));

        // 执行测试
        SysDataSyncLog result = tableDataSyncLogService.selectById(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(testLog.getId(), result.getId());
        assertEquals(testLog.getBatchNo(), result.getBatchNo());

        // 验证方法调用
        verify(sysDataSyncLogMapper, times(1)).selectByPrimaryKey(id);
    }

    @Test
    void testSelectById_NotFound() {
        Long id = 999L;
        when(sysDataSyncLogMapper.selectByPrimaryKey(id)).thenReturn(Optional.empty());

        // 执行测试
        SysDataSyncLog result = tableDataSyncLogService.selectById(id);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(sysDataSyncLogMapper, times(1)).selectByPrimaryKey(id);
    }

    @Test
    void testSelectById_NullId() {
        // 执行测试
        SysDataSyncLog result = tableDataSyncLogService.selectById(null);

        // 验证结果
        assertNull(result);

        // 验证方法未被调用
        verify(sysDataSyncLogMapper, never()).selectByPrimaryKey(any());
    }

    @Test
    void testUpdateSelectiveById_Success() {
        try (MockedStatic<SessionUtils> sessionUtils = mockStatic(SessionUtils.class)) {
            sessionUtils.when(SessionUtils::getUsername).thenReturn("test_user");
            
            when(sysDataSyncLogMapper.updateByPrimaryKeySelective(any(SysDataSyncLog.class))).thenReturn(1);

            // 执行测试
            int result = tableDataSyncLogService.updateSelectiveById(testLog);

            // 验证结果
            assertEquals(1, result);
            assertNotNull(testLog.getUpdateTime());
            assertEquals("test_user", testLog.getUpdateBy());

            // 验证方法调用
            verify(sysDataSyncLogMapper, times(1)).updateByPrimaryKeySelective(any(SysDataSyncLog.class));
        }
    }

    @Test
    void testUpdateSelectiveById_WithOperator() {
        String operator = "admin_user";
        when(sysDataSyncLogMapper.updateByPrimaryKeySelective(any(SysDataSyncLog.class))).thenReturn(1);

        // 执行测试
        int result = tableDataSyncLogService.updateSelectiveById(testLog, operator);

        // 验证结果
        assertEquals(1, result);
        assertNotNull(testLog.getUpdateTime());
        assertEquals(operator, testLog.getUpdateBy());

        // 验证方法调用
        verify(sysDataSyncLogMapper, times(1)).updateByPrimaryKeySelective(any(SysDataSyncLog.class));
    }

    @Test
    void testUpdateSelectiveById_NullRecord() {
        // 执行测试
        int result = tableDataSyncLogService.updateSelectiveById(null);

        // 验证结果
        assertEquals(0, result);

        // 验证方法未被调用
        verify(sysDataSyncLogMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    void testFindByBatchNo_Success() {
        String batchNo = "SYNC_1640995200000_1234";
        List<SysDataSyncLog> expectedLogs = Arrays.asList(testLog);
        
        when(sysDataSyncLogMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(expectedLogs);

        // 执行测试
        List<SysDataSyncLog> result = tableDataSyncLogService.findByBatchNo(batchNo);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testLog.getBatchNo(), result.get(0).getBatchNo());

        // 验证方法调用
        verify(sysDataSyncLogMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    void testFindByBatchNo_EmptyBatchNo() {
        // 执行测试
        List<SysDataSyncLog> result = tableDataSyncLogService.findByBatchNo("");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法未被调用
        verify(sysDataSyncLogMapper, never()).selectMany(any());
    }

    @Test
    void testFindFailedByBatchNo_Success() {
        String batchNo = "SYNC_1640995200000_1234";
        SysDataSyncLog failedLog = new SysDataSyncLog();
        failedLog.setBatchNo(batchNo);
        failedLog.setSyncStatus("FAILED");
        failedLog.setErrorMessage("同步失败");
        
        List<SysDataSyncLog> expectedLogs = Arrays.asList(failedLog);
        
        when(sysDataSyncLogMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(expectedLogs);

        // 执行测试
        List<SysDataSyncLog> result = tableDataSyncLogService.findFailedByBatchNo(batchNo);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("FAILED", result.get(0).getSyncStatus());

        // 验证方法调用
        verify(sysDataSyncLogMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    void testFindByTenantAndTable_Success() {
        String tenantCode = "tenant_001"; // 注意：此参数已不再使用
        String targetTable = "mtc_vehicle_info";
        int limit = 10;
        
        List<SysDataSyncLog> expectedLogs = Arrays.asList(testLog);
        
        when(sysDataSyncLogMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(expectedLogs);

        // 执行测试
        List<SysDataSyncLog> result = tableDataSyncLogService.findByTenantAndTable(tenantCode, targetTable, limit);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(targetTable, result.get(0).getTargetTable());

        // 验证方法调用
        verify(sysDataSyncLogMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    void testFindBySyncStatus_Success() {
        String syncStatus = "SUCCESS";
        int limit = 10;
        
        List<SysDataSyncLog> expectedLogs = Arrays.asList(testLog);
        
        when(sysDataSyncLogMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(expectedLogs);

        // 执行测试
        List<SysDataSyncLog> result = tableDataSyncLogService.findBySyncStatus(syncStatus, limit);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(syncStatus, result.get(0).getSyncStatus());

        // 验证方法调用
        verify(sysDataSyncLogMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    void testFindBySyncStatus_EmptyStatus() {
        // 执行测试
        List<SysDataSyncLog> result = tableDataSyncLogService.findBySyncStatus("", 10);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法未被调用
        verify(sysDataSyncLogMapper, never()).selectMany(any());
    }

    @Test
    void testCleanExpiredLogs_Success() {
        int daysToKeep = 30;
        int expectedDeletedCount = 5;

        // 使用具体的方法签名避免歧义
        when(sysDataSyncLogMapper.delete((DeleteStatementProvider) any()))
            .thenReturn(expectedDeletedCount);

        // 执行测试
        int result = tableDataSyncLogService.cleanExpiredLogs(daysToKeep);

        // 验证结果
        assertEquals(expectedDeletedCount, result);

        // 验证方法调用
        verify(sysDataSyncLogMapper, times(1)).delete((DeleteStatementProvider) any());
    }

    @Test
    void testCleanExpiredLogs_InvalidDays() {
        // 执行测试
        int result = tableDataSyncLogService.cleanExpiredLogs(0);

        // 验证结果
        assertEquals(0, result);

        // 验证方法未被调用
        verify(sysDataSyncLogMapper, never()).delete((DeleteStatementProvider) any());
    }

    @Test
    void testDeleteById_Success() {
        Long id = 1L;
        when(sysDataSyncLogMapper.deleteByPrimaryKey(id)).thenReturn(1);

        // 执行测试
        int result = tableDataSyncLogService.deleteById(id);

        // 验证结果
        assertEquals(1, result);

        // 验证方法调用
        verify(sysDataSyncLogMapper, times(1)).deleteByPrimaryKey(id);
    }

    @Test
    void testDeleteById_NullId() {
        // 执行测试
        int result = tableDataSyncLogService.deleteById(null);

        // 验证结果
        assertEquals(0, result);

        // 验证方法未被调用
        verify(sysDataSyncLogMapper, never()).deleteByPrimaryKey(any());
    }
}
