package com.extracme.saas.autocare.debug;

import com.extracme.saas.autocare.enums.OperateLogModuleTypeEnum;
import com.extracme.saas.autocare.enums.OperateLogOperateTypeEnum;
import com.extracme.saas.autocare.model.entity.SysOperateLog;
import com.extracme.saas.autocare.repository.TableSysOperateLogService;
import com.extracme.saas.autocare.service.OperateLogService;
import com.extracme.saas.autocare.util.OperateLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 操作日志调试工具类
 *
 * 用于排查操作日志记录问题
 */
@Slf4j
@Component
public class OperateLogDebugTool {

    @Autowired
    private OperateLogService operateLogService;

    @Autowired
    private TableSysOperateLogService tableSysOperateLogService;

    /**
     * 测试直接数据库插入
     */
    public void testDirectDatabaseInsert() {
        log.info("=== 测试直接数据库插入 ===");

        try {
            SysOperateLog operateLog = new SysOperateLog();
            operateLog.setTenantId(1L);
            operateLog.setModuleType(OperateLogModuleTypeEnum.USER_MANAGEMENT.getCode());
            operateLog.setOperateType(OperateLogOperateTypeEnum.CREATE.getCode());
            operateLog.setContent("测试直接数据库插入");
            operateLog.setMiscDesc("调试工具测试");
            operateLog.setStatus(1);

            log.info("准备插入操作日志：{}", operateLog);

            SysOperateLog result = tableSysOperateLogService.insert(operateLog, "debug-tool");

            log.info("直接数据库插入结果：{}", result);

            if (result != null && result.getId() != null) {
                log.info("✅ 直接数据库插入成功，生成ID：{}", result.getId());
            } else {
                log.error("❌ 直接数据库插入失败");
            }

        } catch (Exception e) {
            log.error("❌ 直接数据库插入异常", e);
        }
    }

    /**
     * 测试带用户信息的异步方法
     */
    public void testAsyncWithUserInfo() {
        log.info("=== 测试带用户信息的异步方法 ===");

        try {
            operateLogService.recordOperateLogAsync(
                OperateLogModuleTypeEnum.USER_MANAGEMENT,
                OperateLogOperateTypeEnum.CREATE,
                "测试带用户信息的异步方法",
                "调试工具测试",
                999L,           // 测试用户ID
                "调试用户",      // 测试用户昵称
                "13800138000",  // 测试用户手机号
                1L              // 测试租户ID
            );

            log.info("✅ 带用户信息的异步方法调用完成");

            // 等待异步执行完成
            Thread.sleep(2000);

        } catch (Exception e) {
            log.error("❌ 带用户信息的异步方法调用异常", e);
        }
    }

    /**
     * 测试工具类方法
     */
    public void testUtilMethod() {
        log.info("=== 测试工具类方法 ===");

        try {
            OperateLogUtil.recordUserLog(
                OperateLogOperateTypeEnum.CREATE,
                "测试工具类方法"
            );

            log.info("✅ 工具类方法调用完成");

            // 等待异步执行完成
            Thread.sleep(2000);

        } catch (Exception e) {
            log.error("❌ 工具类方法调用异常", e);
        }
    }

    /**
     * 查询最近的操作日志
     */
    public void queryRecentLogs() {
        log.info("=== 查询最近的操作日志 ===");

        try {
            List<SysOperateLog> logs = tableSysOperateLogService.findByCondition(null);

            log.info("查询到 {} 条操作日志", logs.size());

            if (!logs.isEmpty()) {
                log.info("最近的5条操作日志：");
                logs.stream()
                    .limit(5)
                    .forEach(operateLog -> {
                        log.info("ID: {}, 内容: {}, 创建时间: {}, 创建人: {}",
                                operateLog.getId(), operateLog.getContent(), operateLog.getCreateTime(), operateLog.getCreateBy());
                    });
            } else {
                log.warn("❌ 没有查询到任何操作日志");
            }

        } catch (Exception e) {
            log.error("❌ 查询操作日志异常", e);
        }
    }

    /**
     * 运行所有测试
     */
    public void runAllTests() {
        log.info("开始运行操作日志调试测试");

        // 1. 测试直接数据库插入
        testDirectDatabaseInsert();

        // 2. 测试带用户信息的异步方法
        testAsyncWithUserInfo();

        // 3. 测试工具类方法
        testUtilMethod();

        // 4. 查询最近的操作日志
        queryRecentLogs();

        log.info("操作日志调试测试完成");
    }

    /**
     * 检查服务状态
     */
    public void checkServiceStatus() {
        log.info("=== 检查服务状态 ===");

        log.info("OperateLogService: {}", operateLogService != null ? "已注入" : "未注入");
        log.info("TableSysOperateLogService: {}", tableSysOperateLogService != null ? "已注入" : "未注入");

        if (operateLogService != null) {
            log.info("OperateLogService 类型: {}", operateLogService.getClass().getName());
        }

        if (tableSysOperateLogService != null) {
            log.info("TableSysOperateLogService 类型: {}", tableSysOperateLogService.getClass().getName());
        }
    }
}
