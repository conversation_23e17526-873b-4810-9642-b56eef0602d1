package com.extracme.saas.autocare.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Calendar;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.extracme.saas.autocare.service.impl.SmsServiceImpl;

/**
 * 发送验证码功能集成测试
 */
@DisplayName("发送验证码功能集成测试")
public class SendCodeIntegrationTest {

    @Test
    @DisplayName("测试账号验证码格式验证")
    void testTestAccountCodeFormat() {
        // 创建SmsService实例
        SmsServiceImpl smsService = new SmsServiceImpl();
        
        // 设置测试配置
        ReflectionTestUtils.setField(smsService, "testMobile", "***********");
        ReflectionTestUtils.setField(smsService, "activeProfile", "dev");
        
        // 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = SmsServiceImpl.class.getDeclaredMethod("generateVerificationCode", String.class);
            method.setAccessible(true);
            
            // 测试测试账号
            String testCode = (String) method.invoke(smsService, "***********");
            
            // 验证验证码格式：应该是6位数字
            assertEquals(6, testCode.length());
            assertTrue(testCode.matches("\\d{6}"));
            
            // 验证验证码是当前时间的月日时格式
            Calendar calendar = Calendar.getInstance();
            int month = calendar.get(Calendar.MONTH) + 1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            String expectedCode = String.format("%02d%02d%02d", month, day, hour);
            
            assertEquals(expectedCode, testCode);
            
            // 测试普通账号
            String normalCode = (String) method.invoke(smsService, "13900139000");
            assertEquals("123456", normalCode); // 非生产环境应该返回固定验证码
            
        } catch (Exception e) {
            throw new RuntimeException("反射调用失败", e);
        }
    }

    @Test
    @DisplayName("验证码格式边界测试")
    void testCodeFormatBoundary() {
        // 创建SmsService实例
        SmsServiceImpl smsService = new SmsServiceImpl();
        
        // 设置测试配置
        ReflectionTestUtils.setField(smsService, "testMobile", "***********");
        ReflectionTestUtils.setField(smsService, "activeProfile", "prod"); // 生产环境
        
        // 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = SmsServiceImpl.class.getDeclaredMethod("generateVerificationCode", String.class);
            method.setAccessible(true);
            
            // 测试测试账号在生产环境下的行为
            String testCode = (String) method.invoke(smsService, "***********");
            
            // 验证验证码格式：应该是6位数字
            assertEquals(6, testCode.length());
            assertTrue(testCode.matches("\\d{6}"));
            
            // 测试普通账号在生产环境下的行为
            String normalCode = (String) method.invoke(smsService, "13900139000");
            assertEquals(6, normalCode.length());
            assertTrue(normalCode.matches("\\d{6}"));
            // 生产环境下普通账号应该生成随机验证码，不应该是123456
            // 注意：由于是随机生成，我们只能验证格式，不能验证具体值
            
        } catch (Exception e) {
            throw new RuntimeException("反射调用失败", e);
        }
    }
}
