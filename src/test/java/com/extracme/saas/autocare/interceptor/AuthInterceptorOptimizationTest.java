package com.extracme.saas.autocare.interceptor;

import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.PermissionService;
import com.extracme.saas.autocare.util.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.HttpSession;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * AuthInterceptor优化效果测试
 * 验证JWT token优化后的功能正确性和性能提升
 */
@ExtendWith(MockitoExtension.class)
class AuthInterceptorOptimizationTest {

    @Mock
    private JwtUtil jwtUtil;
    
    @Mock
    private PermissionService permissionService;
    
    @Mock
    private TableUserService userService;
    
    @Mock
    private TableTenantService tenantService;
    
    @Mock
    private TableUserOrgService tableUserOrgService;
    
    @Mock
    private HandlerMethod handlerMethod;
    
    @Mock
    private HttpSession session;

    private AuthInterceptor authInterceptor;
    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    private String testToken;
    private JwtUserInfoDTO testUserInfo;

    @BeforeEach
    void setUp() {
        authInterceptor = new AuthInterceptor(jwtUtil, permissionService, userService, tenantService, tableUserOrgService);
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        testToken = "test.jwt.token";
        testUserInfo = createTestUserInfo();
        
        // 设置request的session
        request.setSession(session);
        
        // 设置私有字段
        ReflectionTestUtils.setField(authInterceptor, "request", request);
    }

    /**
     * 测试新版本token的处理（从JWT解析用户信息）
     */
    @Test
    void testNewTokenProcessing() throws Exception {
        // 设置Authorization头
        request.addHeader("Authorization", "Bearer " + testToken);
        
        // 模拟新版本token
        when(jwtUtil.validateToken(testToken)).thenReturn(true);
        when(jwtUtil.hasUserInfo(testToken)).thenReturn(true);
        when(jwtUtil.getUserInfoFromToken(testToken)).thenReturn(testUserInfo);
        
        // 模拟session为空，需要重新获取用户信息
        when(session.getAttribute("LOGIN_USER")).thenReturn(null);
        when(session.getAttribute("USER_TOKEN")).thenReturn(null);
        
        // 执行拦截器
        boolean result = authInterceptor.preHandle(request, response, handlerMethod);
        
        // 验证结果
        assertTrue(result);
        
        // 验证JWT方法被调用
        verify(jwtUtil).validateToken(testToken);
        verify(jwtUtil).hasUserInfo(testToken);
        verify(jwtUtil).getUserInfoFromToken(testToken);
        
        // 验证数据库查询方法没有被调用（性能优化）
        verify(userService, never()).selectById(any());
        verify(permissionService, never()).getUserPermissions(any());
        verify(tenantService, never()).selectById(any());
        verify(tableUserOrgService, never()).findOrgIdsByUserId(any());
        
        // 验证session被设置
        verify(session).setAttribute(eq("LOGIN_USER"), any(LoginUser.class));
        verify(session).setAttribute(eq("USER_PERMISSIONS"), any());
        verify(session).setAttribute(eq("USER_TOKEN"), eq(testToken));
        verify(session).setAttribute(eq("TENANT_CODE"), eq(testUserInfo.getTenantCode()));
        
        System.out.println("新版本token处理测试通过 - 无数据库查询");
    }

    /**
     * 测试旧版本token的处理（回退到数据库查询）
     */
    @Test
    void testOldTokenProcessing() throws Exception {
        // 设置Authorization头
        request.addHeader("Authorization", "Bearer " + testToken);
        
        // 模拟旧版本token
        when(jwtUtil.validateToken(testToken)).thenReturn(true);
        when(jwtUtil.hasUserInfo(testToken)).thenReturn(false);
        when(jwtUtil.getUserIdFromToken(testToken)).thenReturn("12345");
        when(jwtUtil.getExpirationDateFromToken(testToken)).thenReturn(new Date(System.currentTimeMillis() + ********));
        
        // 模拟session为空
        when(session.getAttribute("LOGIN_USER")).thenReturn(null);
        when(session.getAttribute("USER_TOKEN")).thenReturn(null);
        
        // 模拟数据库查询
        SysUser mockUser = createMockUser();
        when(userService.selectById(12345L)).thenReturn(mockUser);
        
        List<SysPermission> mockPermissions = createMockPermissions();
        when(permissionService.getUserPermissions(12345L)).thenReturn(mockPermissions);
        
        SysTenant mockTenant = createMockTenant();
        when(tenantService.selectById(100L)).thenReturn(mockTenant);
        
        List<String> mockOrgIds = Arrays.asList("org1", "org2");
        when(tableUserOrgService.findOrgIdsByUserId(12345L)).thenReturn(mockOrgIds);
        
        // 执行拦截器
        boolean result = authInterceptor.preHandle(request, response, handlerMethod);
        
        // 验证结果
        assertTrue(result);
        
        // 验证JWT方法被调用
        verify(jwtUtil).validateToken(testToken);
        verify(jwtUtil).hasUserInfo(testToken);
        verify(jwtUtil).getUserIdFromToken(testToken);
        
        // 验证数据库查询方法被调用（向后兼容）
        verify(userService).selectById(12345L);
        verify(permissionService).getUserPermissions(12345L);
        verify(tenantService).selectById(100L);
        verify(tableUserOrgService).findOrgIdsByUserId(12345L);
        
        System.out.println("旧版本token处理测试通过 - 回退到数据库查询");
    }

    /**
     * 测试session缓存机制
     */
    @Test
    void testSessionCaching() throws Exception {
        // 设置Authorization头
        request.addHeader("Authorization", "Bearer " + testToken);
        
        // 模拟session中已有用户信息
        LoginUser cachedUser = createMockLoginUser();
        when(session.getAttribute("LOGIN_USER")).thenReturn(cachedUser);
        when(session.getAttribute("USER_TOKEN")).thenReturn(testToken);
        
        when(jwtUtil.validateToken(testToken)).thenReturn(true);
        
        // 执行拦截器
        boolean result = authInterceptor.preHandle(request, response, handlerMethod);
        
        // 验证结果
        assertTrue(result);
        
        // 验证没有重新获取用户信息（使用缓存）
        verify(jwtUtil, never()).hasUserInfo(any());
        verify(jwtUtil, never()).getUserInfoFromToken(any());
        verify(userService, never()).selectById(any());
        verify(permissionService, never()).getUserPermissions(any());
        
        System.out.println("Session缓存机制测试通过");
    }

    /**
     * 测试token更新时的处理
     */
    @Test
    void testTokenUpdate() throws Exception {
        String newToken = "new.jwt.token";
        request.addHeader("Authorization", "Bearer " + newToken);
        
        // 模拟session中有旧token的用户信息
        LoginUser cachedUser = createMockLoginUser();
        when(session.getAttribute("LOGIN_USER")).thenReturn(cachedUser);
        when(session.getAttribute("USER_TOKEN")).thenReturn(testToken); // 旧token
        
        // 模拟新token
        when(jwtUtil.validateToken(newToken)).thenReturn(true);
        when(jwtUtil.hasUserInfo(newToken)).thenReturn(true);
        when(jwtUtil.getUserInfoFromToken(newToken)).thenReturn(testUserInfo);
        
        // 执行拦截器
        boolean result = authInterceptor.preHandle(request, response, handlerMethod);
        
        // 验证结果
        assertTrue(result);
        
        // 验证重新获取了用户信息
        verify(jwtUtil).hasUserInfo(newToken);
        verify(jwtUtil).getUserInfoFromToken(newToken);
        
        // 验证session被更新
        verify(session).setAttribute(eq("USER_TOKEN"), eq(newToken));
        
        System.out.println("Token更新处理测试通过");
    }

    /**
     * 创建测试用户信息
     */
    private JwtUserInfoDTO createTestUserInfo() {
        JwtUserInfoDTO userInfo = new JwtUserInfoDTO();
        userInfo.setUserId(12345L);
        userInfo.setUsername("testuser");
        userInfo.setNickname("测试用户");
        userInfo.setMobile("***********");
        userInfo.setAccountType(1);
        userInfo.setStatus(1);
        
        // 权限和组织信息现在存储在Redis缓存中，不再包含在JWT token中
        // Set<String> permissions = new HashSet<>();
        // permissions.add("user:read");
        // permissions.add("user:write");
        // userInfo.setPermissions(permissions);

        // List<String> orgIds = Arrays.asList("org1", "org2");
        // userInfo.setOrgIds(orgIds);
        // userInfo.setAllAccessibleOrgIds(Arrays.asList("org1", "org2", "org3"));
        
        userInfo.setTenantId(100L);
        userInfo.setTenantCode("test");
        userInfo.setTenantName("测试租户");
        
        userInfo.setLoginTime(System.currentTimeMillis());
        userInfo.setExpireTime(System.currentTimeMillis() + ********);
        
        return userInfo;
    }

    /**
     * 创建模拟用户
     */
    private SysUser createMockUser() {
        SysUser user = new SysUser();
        user.setId(12345L);
        user.setUsername("testuser");
        user.setNickname("测试用户");
        user.setMobile("***********");
        user.setAccountType(1);
        user.setStatus(1);
        user.setTenantId(100L);
        return user;
    }

    /**
     * 创建模拟权限列表
     */
    private List<SysPermission> createMockPermissions() {
        List<SysPermission> permissions = new ArrayList<>();
        
        SysPermission perm1 = new SysPermission();
        perm1.setPermissionCode("user:read");
        permissions.add(perm1);
        
        SysPermission perm2 = new SysPermission();
        perm2.setPermissionCode("user:write");
        permissions.add(perm2);
        
        return permissions;
    }

    /**
     * 创建模拟租户
     */
    private SysTenant createMockTenant() {
        SysTenant tenant = new SysTenant();
        tenant.setId(100L);
        tenant.setTenantCode("test");
        tenant.setTenantName("测试租户");
        return tenant;
    }

    /**
     * 创建模拟LoginUser
     */
    private LoginUser createMockLoginUser() {
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(createMockUser());
        loginUser.setTenantId(100L);
        loginUser.setTenantCode("test");
        loginUser.setToken(testToken);
        return loginUser;
    }

    /**
     * 测试性能对比：新版本token vs 旧版本token
     */
    @Test
    void testPerformanceComparison() throws Exception {
        int iterations = 100;

        // 测试新版本token性能
        long newTokenStartTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            testNewTokenPerformance();
        }
        long newTokenEndTime = System.currentTimeMillis();
        long newTokenTime = newTokenEndTime - newTokenStartTime;

        // 重置mocks
        reset(jwtUtil, userService, permissionService, tenantService, tableUserOrgService, session);

        // 测试旧版本token性能
        long oldTokenStartTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            testOldTokenPerformance();
        }
        long oldTokenEndTime = System.currentTimeMillis();
        long oldTokenTime = oldTokenEndTime - oldTokenStartTime;

        // 输出性能对比结果
        System.out.printf("性能对比结果 (%d 次迭代):%n", iterations);
        System.out.printf("新版本token (JWT解析): %d ms, 平均: %.2f ms/次%n",
                         newTokenTime, (double) newTokenTime / iterations);
        System.out.printf("旧版本token (数据库查询): %d ms, 平均: %.2f ms/次%n",
                         oldTokenTime, (double) oldTokenTime / iterations);
        System.out.printf("性能提升: %.1fx%n", (double) oldTokenTime / newTokenTime);

        // 验证新版本确实更快（至少快50%）
        assertTrue(newTokenTime < oldTokenTime * 0.5,
                  "新版本token处理应该比旧版本快至少50%");
    }

    /**
     * 测试新版本token的单次性能
     */
    private void testNewTokenPerformance() throws Exception {
        MockHttpServletRequest req = new MockHttpServletRequest();
        req.addHeader("Authorization", "Bearer " + testToken);
        req.setSession(session);

        when(jwtUtil.validateToken(testToken)).thenReturn(true);
        when(jwtUtil.hasUserInfo(testToken)).thenReturn(true);
        when(jwtUtil.getUserInfoFromToken(testToken)).thenReturn(testUserInfo);
        when(session.getAttribute("LOGIN_USER")).thenReturn(null);
        when(session.getAttribute("USER_TOKEN")).thenReturn(null);

        ReflectionTestUtils.setField(authInterceptor, "request", req);
        authInterceptor.preHandle(req, response, handlerMethod);
    }

    /**
     * 测试旧版本token的单次性能
     */
    private void testOldTokenPerformance() throws Exception {
        MockHttpServletRequest req = new MockHttpServletRequest();
        req.addHeader("Authorization", "Bearer " + testToken);
        req.setSession(session);

        when(jwtUtil.validateToken(testToken)).thenReturn(true);
        when(jwtUtil.hasUserInfo(testToken)).thenReturn(false);
        when(jwtUtil.getUserIdFromToken(testToken)).thenReturn("12345");
        when(jwtUtil.getExpirationDateFromToken(testToken)).thenReturn(new Date(System.currentTimeMillis() + ********));
        when(session.getAttribute("LOGIN_USER")).thenReturn(null);
        when(session.getAttribute("USER_TOKEN")).thenReturn(null);

        // 模拟数据库查询
        when(userService.selectById(12345L)).thenReturn(createMockUser());
        when(permissionService.getUserPermissions(12345L)).thenReturn(createMockPermissions());
        when(tenantService.selectById(100L)).thenReturn(createMockTenant());
        when(tableUserOrgService.findOrgIdsByUserId(12345L)).thenReturn(Arrays.asList("org1", "org2"));

        ReflectionTestUtils.setField(authInterceptor, "request", req);
        authInterceptor.preHandle(req, response, handlerMethod);
    }
}
