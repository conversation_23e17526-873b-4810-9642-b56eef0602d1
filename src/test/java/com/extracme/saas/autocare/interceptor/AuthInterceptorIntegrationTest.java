package com.extracme.saas.autocare.interceptor;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.extracme.saas.autocare.model.dto.JwtUserInfoDTO;
import com.extracme.saas.autocare.util.JwtUtil;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;

/**
 * AuthInterceptor集成测试
 * 验证JWT token优化流程（权限信息已移至Redis缓存）
 */
class AuthInterceptorIntegrationTest {

    private JwtUtil jwtUtil;
    private UserPermissionCacheUtils userPermissionCacheUtils;

    @BeforeEach
    void setUp() {
        jwtUtil = new JwtUtil();
        ReflectionTestUtils.setField(jwtUtil, "secret", "test-secret-key-for-auth-interceptor-integration-test");
        ReflectionTestUtils.setField(jwtUtil, "expiration", 86400L);

        // Mock UserPermissionCacheUtils for testing
        userPermissionCacheUtils = mock(UserPermissionCacheUtils.class);

        // 模拟Redis缓存可用
        when(userPermissionCacheUtils.isRedisAvailable()).thenReturn(true);

        // 模拟缓存权限信息成功
        when(userPermissionCacheUtils.cacheUserPermissions(anyLong(), anyLong(), any(), any(), any()))
            .thenReturn(true);
    }

    /**
     * 测试JWT token优化流程（基础用户信息）
     * 权限信息现在存储在Redis缓存中，不再包含在JWT token中
     */
    @Test
    void testJwtOptimizationFlow() {
        // 1. 创建包含基础用户信息的JWT token
        JwtUserInfoDTO userInfo = createTestUserInfo();
        String optimizedToken = jwtUtil.generateTokenWithUserInfo(userInfo);

        assertNotNull(optimizedToken);
        assertTrue(optimizedToken.length() > 0);

        // 2. 验证token包含基础用户信息
        assertTrue(jwtUtil.hasUserInfo(optimizedToken));

        // 3. 验证可以从token中解析基础用户信息
        JwtUserInfoDTO parsedInfo = jwtUtil.getUserInfoFromToken(optimizedToken);
        assertNotNull(parsedInfo);
        assertEquals(userInfo.getUserId(), parsedInfo.getUserId());
        assertEquals(userInfo.getUsername(), parsedInfo.getUsername());
        assertEquals(userInfo.getTenantId(), parsedInfo.getTenantId());
        assertEquals(userInfo.getTenantCode(), parsedInfo.getTenantCode());
        assertEquals(userInfo.getTenantName(), parsedInfo.getTenantName());
        assertEquals(userInfo.getMobile(), parsedInfo.getMobile());
        assertEquals(userInfo.getAccountType(), parsedInfo.getAccountType());

        // 4. 验证token大小显著减少（不包含权限信息）
        assertTrue(optimizedToken.length() < 2048, "优化后Token大小应该小于2KB");

        System.out.println("JWT优化流程测试通过！");
        System.out.println("Token大小: " + optimizedToken.length() + " 字符");
        System.out.println("注意：权限信息现在存储在Redis缓存中，不再包含在JWT token中");
    }

    /**
     * 测试向后兼容性
     */
    @Test
    void testBackwardCompatibility() {
        // 1. 生成旧版本token（只包含userId）
        String oldToken = jwtUtil.generateToken("12345");
        
        // 2. 验证旧版本token不包含完整用户信息
        assertFalse(jwtUtil.hasUserInfo(oldToken));
        assertTrue(jwtUtil.validateToken(oldToken));
        assertEquals("12345", jwtUtil.getUserIdFromToken(oldToken));
        
        // 3. 生成新版本token
        JwtUserInfoDTO userInfo = createTestUserInfo();
        String newToken = jwtUtil.generateTokenWithUserInfo(userInfo);
        
        // 4. 验证新版本token包含完整用户信息
        assertTrue(jwtUtil.hasUserInfo(newToken));
        assertTrue(jwtUtil.validateToken(newToken));
        assertNotNull(jwtUtil.getUserInfoFromToken(newToken));
        
        System.out.println("向后兼容性测试通过！");
        System.out.println("旧版本token长度: " + oldToken.length());
        System.out.println("新版本token长度: " + newToken.length());
    }

    /**
     * 测试多租户隔离
     */
    @Test
    void testMultiTenantIsolation() {
        // 创建不同租户的用户信息
        JwtUserInfoDTO tenant1User = createTestUserInfo();
        tenant1User.setTenantId(100L);
        tenant1User.setTenantCode("tenant100");
        tenant1User.setTenantName("租户100");
        
        JwtUserInfoDTO tenant2User = createTestUserInfo();
        tenant2User.setUserId(67890L);
        tenant2User.setTenantId(200L);
        tenant2User.setTenantCode("tenant200");
        tenant2User.setTenantName("租户200");
        
        // 生成各自的token
        String token1 = jwtUtil.generateTokenWithUserInfo(tenant1User);
        String token2 = jwtUtil.generateTokenWithUserInfo(tenant2User);
        
        // 验证token隔离
        JwtUserInfoDTO parsed1 = jwtUtil.getUserInfoFromToken(token1);
        JwtUserInfoDTO parsed2 = jwtUtil.getUserInfoFromToken(token2);
        
        assertNotEquals(parsed1.getTenantId(), parsed2.getTenantId());
        assertNotEquals(parsed1.getTenantCode(), parsed2.getTenantCode());
        assertNotEquals(parsed1.getUserId(), parsed2.getUserId());
        
        System.out.println("多租户隔离测试通过！");
        System.out.println("租户1: " + parsed1.getTenantCode() + " (ID: " + parsed1.getTenantId() + ")");
        System.out.println("租户2: " + parsed2.getTenantCode() + " (ID: " + parsed2.getTenantId() + ")");
    }

    /**
     * 测试Redis缓存权限信息功能
     */
    @Test
    void testRedisPermissionCaching() {
        JwtUserInfoDTO userInfo = createTestUserInfo();

        // 模拟权限和组织信息（这些现在存储在Redis中）
        Set<String> permissions = new HashSet<>();
        permissions.add("user:read");
        permissions.add("user:write");
        permissions.add("user:delete");
        permissions.add("order:read");
        permissions.add("order:write");

        List<String> orgIds = Arrays.asList("org1", "org2", "org3");
        List<String> allAccessibleOrgIds = Arrays.asList("org1", "org2", "org3", "org4", "org5", "org6");

        // 模拟从Redis获取权限信息
        UserPermissionCacheUtils.UserPermissionCache mockCache =
            new UserPermissionCacheUtils.UserPermissionCache(permissions, orgIds, allAccessibleOrgIds);
        when(userPermissionCacheUtils.getUserPermissions(userInfo.getTenantId(), userInfo.getUserId()))
            .thenReturn(mockCache);

        // 生成JWT token（只包含基础用户信息）
        String token = jwtUtil.generateTokenWithUserInfo(userInfo);
        JwtUserInfoDTO parsed = jwtUtil.getUserInfoFromToken(token);

        // 验证JWT token只包含基础信息
        assertNotNull(parsed);
        assertEquals(userInfo.getUserId(), parsed.getUserId());
        assertEquals(userInfo.getUsername(), parsed.getUsername());
        assertEquals(userInfo.getTenantId(), parsed.getTenantId());

        // 验证权限信息需要从Redis获取
        UserPermissionCacheUtils.UserPermissionCache cachedPermissions =
            userPermissionCacheUtils.getUserPermissions(userInfo.getTenantId(), userInfo.getUserId());

        assertNotNull(cachedPermissions);
        assertEquals(permissions.size(), cachedPermissions.getPermissions().size());
        assertTrue(cachedPermissions.getPermissions().containsAll(permissions));
        assertEquals(orgIds.size(), cachedPermissions.getOrgIds().size());
        assertEquals(allAccessibleOrgIds.size(), cachedPermissions.getAllAccessibleOrgIds().size());

        System.out.println("Redis缓存权限信息测试通过！");
        System.out.println("JWT Token大小: " + token.length() + " 字符");
        System.out.println("缓存权限数量: " + cachedPermissions.getPermissions().size());
        System.out.println("缓存组织数量: " + cachedPermissions.getOrgIds().size());
    }

    /**
     * 测试性能基准
     */
    @Test
    void testPerformanceBenchmark() {
        JwtUserInfoDTO userInfo = createTestUserInfo();
        int iterations = 1000;
        
        // 测试token生成性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            jwtUtil.generateTokenWithUserInfo(userInfo);
        }
        long generateTime = System.currentTimeMillis() - startTime;
        
        // 生成一个token用于解析测试
        String token = jwtUtil.generateTokenWithUserInfo(userInfo);
        
        // 测试token解析性能
        startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            jwtUtil.getUserInfoFromToken(token);
        }
        long parseTime = System.currentTimeMillis() - startTime;
        
        // 输出性能结果
        double avgGenerateTime = (double) generateTime / iterations;
        double avgParseTime = (double) parseTime / iterations;
        
        System.out.printf("性能基准测试结果 (%d 次迭代):%n", iterations);
        System.out.printf("Token生成: %d ms, 平均: %.3f ms/次%n", generateTime, avgGenerateTime);
        System.out.printf("Token解析: %d ms, 平均: %.3f ms/次%n", parseTime, avgParseTime);
        
        // 验证性能要求
        assertTrue(avgGenerateTime < 1.0, "Token生成平均时间应该小于1ms");
        assertTrue(avgParseTime < 1.0, "Token解析平均时间应该小于1ms");
    }

    /**
     * 创建测试用户信息（仅包含基础信息，权限信息现在存储在Redis中）
     */
    private JwtUserInfoDTO createTestUserInfo() {
        JwtUserInfoDTO userInfo = new JwtUserInfoDTO();

        userInfo.setUserId(12345L);
        userInfo.setUsername("testuser");
        userInfo.setNickname("测试用户");
        userInfo.setMobile("***********");
        userInfo.setAccountType(1);
        userInfo.setApprovalLevel(2);
        userInfo.setStatus(1);

        // 权限信息现在存储在Redis缓存中，不再包含在JWT token中
        // Set<String> permissions = new HashSet<>();
        // permissions.add("user:read");
        // permissions.add("user:write");
        // permissions.add("order:read");
        // userInfo.setPermissions(permissions);

        // List<String> orgIds = Arrays.asList("org1", "org2");
        // List<String> allAccessibleOrgIds = Arrays.asList("org1", "org2", "org3", "org4");
        // userInfo.setOrgIds(orgIds);
        // userInfo.setAllAccessibleOrgIds(allAccessibleOrgIds);

        userInfo.setTenantId(100L);
        userInfo.setTenantCode("test");
        userInfo.setTenantName("测试租户");

        long currentTime = System.currentTimeMillis();
        userInfo.setLoginTime(currentTime);
        userInfo.setExpireTime(currentTime + ********);

        userInfo.setIpaddr("*************");

        return userInfo;
    }
}
