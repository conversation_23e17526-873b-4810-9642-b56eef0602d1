package com.extracme.saas.autocare.model.security;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LoginUser类测试
 * 重点测试组织权限相关的方法
 */
@DisplayName("LoginUser类测试")
class LoginUserTest {

    private LoginUser loginUser;

    @BeforeEach
    void setUp() {
        loginUser = new LoginUser();
    }

    @Test
    @DisplayName("测试getDirectOrgIds方法 - 正常情况")
    void testGetDirectOrgIds_Normal() {
        List<String> directOrgIds = Arrays.asList("ORG001", "ORG002");
        loginUser.setOrgIds(directOrgIds);

        List<String> result = loginUser.getDirectOrgIds();

        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("ORG001"));
        assertTrue(result.contains("ORG002"));
        
        // 确保返回的是新的列表实例，不是原始引用
        assertNotSame(directOrgIds, result);
    }

    @Test
    @DisplayName("测试getDirectOrgIds方法 - 空情况")
    void testGetDirectOrgIds_Empty() {
        // 测试null情况
        loginUser.setOrgIds(null);
        List<String> result1 = loginUser.getDirectOrgIds();
        assertNotNull(result1);
        assertTrue(result1.isEmpty());

        // 测试空列表情况
        loginUser.setOrgIds(Collections.emptyList());
        List<String> result2 = loginUser.getDirectOrgIds();
        assertNotNull(result2);
        assertTrue(result2.isEmpty());
    }

    @Test
    @DisplayName("测试getAllAccessibleOrgIds方法 - 正常情况")
    void testGetAllAccessibleOrgIds_Normal() {
        List<String> allAccessibleOrgIds = Arrays.asList("ORG001", "ORG001001", "ORG001002", "ORG001001001");
        loginUser.setAllAccessibleOrgIds(allAccessibleOrgIds);

        List<String> result = loginUser.getAllAccessibleOrgIds();

        assertNotNull(result);
        assertEquals(4, result.size());
        assertTrue(result.contains("ORG001"));
        assertTrue(result.contains("ORG001001"));
        assertTrue(result.contains("ORG001002"));
        assertTrue(result.contains("ORG001001001"));
        
        // 确保返回的是新的列表实例，不是原始引用
        assertNotSame(allAccessibleOrgIds, result);
    }

    @Test
    @DisplayName("测试getAllAccessibleOrgIds方法 - 空情况")
    void testGetAllAccessibleOrgIds_Empty() {
        // 测试null情况
        loginUser.setAllAccessibleOrgIds(null);
        List<String> result1 = loginUser.getAllAccessibleOrgIds();
        assertNotNull(result1);
        assertTrue(result1.isEmpty());

        // 测试空列表情况
        loginUser.setAllAccessibleOrgIds(Collections.emptyList());
        List<String> result2 = loginUser.getAllAccessibleOrgIds();
        assertNotNull(result2);
        assertTrue(result2.isEmpty());
    }

    @Test
    @DisplayName("测试方法语义的正确性 - 直接关联vs可访问")
    void testMethodSemantics() {
        // 设置用户直接关联2个组织
        List<String> directOrgIds = Arrays.asList("ORG001", "ORG002");
        loginUser.setOrgIds(directOrgIds);

        // 设置用户可访问4个组织（包括子组织）
        List<String> allAccessibleOrgIds = Arrays.asList("ORG001", "ORG001001", "ORG001002", "ORG002");
        loginUser.setAllAccessibleOrgIds(allAccessibleOrgIds);

        // 验证getDirectOrgIds返回直接关联的组织
        List<String> directResult = loginUser.getDirectOrgIds();
        assertEquals(2, directResult.size());
        assertTrue(directResult.contains("ORG001"));
        assertTrue(directResult.contains("ORG002"));

        // 验证getAllAccessibleOrgIds返回所有可访问的组织（包括子组织）
        List<String> accessibleResult = loginUser.getAllAccessibleOrgIds();
        assertEquals(4, accessibleResult.size());
        assertTrue(accessibleResult.contains("ORG001"));
        assertTrue(accessibleResult.contains("ORG001001"));
        assertTrue(accessibleResult.contains("ORG001002"));
        assertTrue(accessibleResult.contains("ORG002"));

        // 验证两个方法返回的结果不同，体现了不同的语义
        assertNotEquals(directResult.size(), accessibleResult.size());
    }

    @Test
    @DisplayName("测试hasOrgAccess方法 - 基于直接关联组织")
    void testHasOrgAccess() {
        List<String> directOrgIds = Arrays.asList("ORG001", "ORG002");
        loginUser.setOrgIds(directOrgIds);

        // 测试有权限的组织
        assertTrue(loginUser.hasOrgAccess("ORG001"));
        assertTrue(loginUser.hasOrgAccess("ORG002"));

        // 测试没有权限的组织
        assertFalse(loginUser.hasOrgAccess("ORG003"));
        assertFalse(loginUser.hasOrgAccess("ORG001001")); // 子组织不在直接关联列表中

        // 测试null和空字符串
        assertFalse(loginUser.hasOrgAccess(null));
        assertFalse(loginUser.hasOrgAccess(""));

        // 测试orgIds为null的情况
        loginUser.setOrgIds(null);
        assertFalse(loginUser.hasOrgAccess("ORG001"));
    }


    @Test
    @DisplayName("测试数据一致性 - 确保getter方法返回新实例")
    void testDataConsistency() {
        List<String> directOrgIds = Arrays.asList("ORG001");
        List<String> allAccessibleOrgIds = Arrays.asList("ORG001", "ORG001001", "ORG001002");

        loginUser.setOrgIds(directOrgIds);
        loginUser.setAllAccessibleOrgIds(allAccessibleOrgIds);

        // 获取两次结果，验证每次都返回新的实例
        List<String> directResult1 = loginUser.getDirectOrgIds();
        List<String> directResult2 = loginUser.getDirectOrgIds();
        List<String> accessibleResult1 = loginUser.getAllAccessibleOrgIds();
        List<String> accessibleResult2 = loginUser.getAllAccessibleOrgIds();

        // 验证内容相同但实例不同
        assertEquals(directResult1, directResult2);
        assertEquals(accessibleResult1, accessibleResult2);
        assertNotSame(directResult1, directResult2);
        assertNotSame(accessibleResult1, accessibleResult2);

        // 验证修改返回的列表不会影响原始数据
        directResult1.clear();
        accessibleResult1.clear();

        // 再次获取，验证数据没有被影响
        assertEquals(1, loginUser.getDirectOrgIds().size());
        assertEquals(3, loginUser.getAllAccessibleOrgIds().size());
    }
}
