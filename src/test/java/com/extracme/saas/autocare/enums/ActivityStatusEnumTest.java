package com.extracme.saas.autocare.enums;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 活动状态枚举测试类
 */
public class ActivityStatusEnumTest {

    @Test
    @DisplayName("测试所有枚举值的基本属性")
    public void testEnumValues() {
        // 测试UNPROCESSED
        assertEquals("UNPROCESSED", ActivityStatusEnum.UNPROCESSED.getCode());
        assertEquals("未处理", ActivityStatusEnum.UNPROCESSED.getDescription());

        // 测试PROCESSING
        assertEquals("PROCESSING", ActivityStatusEnum.PROCESSING.getCode());
        assertEquals("处理中", ActivityStatusEnum.PROCESSING.getDescription());

        // 测试COMPLETED
        assertEquals("COMPLETED", ActivityStatusEnum.COMPLETED.getCode());
        assertEquals("已完成", ActivityStatusEnum.COMPLETED.getDescription());

        // 测试REJECTED
        assertEquals("REJECTED", ActivityStatusEnum.REJECTED.getCode());
        assertEquals("已驳回", ActivityStatusEnum.REJECTED.getDescription());

        // 测试SUSPENDED
        assertEquals("SUSPENDED", ActivityStatusEnum.SUSPENDED.getCode());
        assertEquals("已暂停", ActivityStatusEnum.SUSPENDED.getDescription());

        // 测试CLOSED
        assertEquals("CLOSED", ActivityStatusEnum.CLOSED.getCode());
        assertEquals("已关闭", ActivityStatusEnum.CLOSED.getDescription());

        // 测试SKIPPED
        assertEquals("SKIPPED", ActivityStatusEnum.SKIPPED.getCode());
        assertEquals("已跳过", ActivityStatusEnum.SKIPPED.getDescription());

        // 测试PENDING
        assertEquals("PENDING", ActivityStatusEnum.PENDING.getCode());
        assertEquals("待处理", ActivityStatusEnum.PENDING.getDescription());

        // 测试DELETE
        assertEquals("DELETE", ActivityStatusEnum.DELETE.getCode());
        assertEquals("已删除", ActivityStatusEnum.DELETE.getDescription());
    }

    @Test
    @DisplayName("测试根据状态码查找枚举")
    public void testGetByCode() {
        // 测试有效的状态码
        assertEquals(ActivityStatusEnum.UNPROCESSED, ActivityStatusEnum.getByCode("UNPROCESSED"));
        assertEquals(ActivityStatusEnum.PROCESSING, ActivityStatusEnum.getByCode("PROCESSING"));
        assertEquals(ActivityStatusEnum.COMPLETED, ActivityStatusEnum.getByCode("COMPLETED"));
        assertEquals(ActivityStatusEnum.REJECTED, ActivityStatusEnum.getByCode("REJECTED"));
        assertEquals(ActivityStatusEnum.SUSPENDED, ActivityStatusEnum.getByCode("SUSPENDED"));
        assertEquals(ActivityStatusEnum.CLOSED, ActivityStatusEnum.getByCode("CLOSED"));
        assertEquals(ActivityStatusEnum.SKIPPED, ActivityStatusEnum.getByCode("SKIPPED"));
        assertEquals(ActivityStatusEnum.PENDING, ActivityStatusEnum.getByCode("PENDING"));
        assertEquals(ActivityStatusEnum.DELETE, ActivityStatusEnum.getByCode("DELETE"));

        // 测试无效的状态码
        assertNull(ActivityStatusEnum.getByCode("INVALID_CODE"));
        assertNull(ActivityStatusEnum.getByCode(null));
        assertNull(ActivityStatusEnum.getByCode(""));
        assertNull(ActivityStatusEnum.getByCode("   "));
    }

    @Test
    @DisplayName("测试状态码有效性检查")
    public void testIsValidCode() {
        // 测试有效的状态码
        assertTrue(ActivityStatusEnum.isValidCode("UNPROCESSED"));
        assertTrue(ActivityStatusEnum.isValidCode("PROCESSING"));
        assertTrue(ActivityStatusEnum.isValidCode("COMPLETED"));
        assertTrue(ActivityStatusEnum.isValidCode("REJECTED"));
        assertTrue(ActivityStatusEnum.isValidCode("SUSPENDED"));
        assertTrue(ActivityStatusEnum.isValidCode("CLOSED"));
        assertTrue(ActivityStatusEnum.isValidCode("SKIPPED"));
        assertTrue(ActivityStatusEnum.isValidCode("PENDING"));
        assertTrue(ActivityStatusEnum.isValidCode("DELETE"));

        // 测试无效的状态码
        assertFalse(ActivityStatusEnum.isValidCode("INVALID_CODE"));
        assertFalse(ActivityStatusEnum.isValidCode(null));
        assertFalse(ActivityStatusEnum.isValidCode(""));
        assertFalse(ActivityStatusEnum.isValidCode("   "));
    }

    @Test
    @DisplayName("测试枚举数量")
    public void testEnumCount() {
        ActivityStatusEnum[] values = ActivityStatusEnum.values();
        assertEquals(9, values.length);
    }

    @Test
    @DisplayName("测试枚举不为空")
    public void testEnumNotNull() {
        for (ActivityStatusEnum status : ActivityStatusEnum.values()) {
            assertNotNull(status.getCode());
            assertNotNull(status.getDescription());
            assertFalse(status.getCode().isEmpty());
            assertFalse(status.getDescription().isEmpty());
        }
    }
}
