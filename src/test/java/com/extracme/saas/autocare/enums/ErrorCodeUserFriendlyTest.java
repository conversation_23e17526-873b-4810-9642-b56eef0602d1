package com.extracme.saas.autocare.enums;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

/**
 * 错误码用户友好性测试
 * 验证面向用户的错误信息使用"商户"术语而非"租户"
 */
class ErrorCodeUserFriendlyTest {

    @Test
    void testTenantRelatedErrorMessages_UseUserFriendlyTerms() {
        // 验证多租户账号错误信息
        assertEquals("用户存在多个商户账号，请选择具体的商户进行登录", 
                    ErrorCode.MULTIPLE_TENANT_ACCOUNTS.getMessage());
        
        // 验证无租户账号错误信息
        assertEquals("用户未关联任何商户账号", 
                    ErrorCode.NO_TENANT_ACCOUNTS.getMessage());
        
        // 验证租户访问权限错误信息
        assertEquals("用户无权限访问指定商户", 
                    ErrorCode.TENANT_ACCESS_DENIED.getMessage());
        
        // 验证租户不存在错误信息
        assertEquals("商户不存在", 
                    ErrorCode.TENANT_NOT_FOUND.getMessage());
        
        // 验证租户过期错误信息
        assertEquals("商户已过期，请联系管理员", 
                    ErrorCode.TENANT_EXPIRED.getMessage());
        
        // 验证租户禁用错误信息
        assertEquals("商户状态异常，无法登录", 
                    ErrorCode.TENANT_DISABLED.getMessage());
    }

    @Test
    void testUserFacingErrorMessages_DoNotContainTechnicalTerms() {
        // 验证面向用户的错误信息不包含"租户"这个技术术语
        String[] userFacingErrorCodes = {
            ErrorCode.MULTIPLE_TENANT_ACCOUNTS.getMessage(),
            ErrorCode.NO_TENANT_ACCOUNTS.getMessage(),
            ErrorCode.TENANT_ACCESS_DENIED.getMessage(),
            ErrorCode.TENANT_NOT_FOUND.getMessage(),
            ErrorCode.TENANT_EXPIRED.getMessage(),
            ErrorCode.TENANT_DISABLED.getMessage()
        };
        
        for (String message : userFacingErrorCodes) {
            // 这些面向用户的错误信息应该使用"商户"而不是"租户"
            if (message.contains("租户")) {
                // 只有在特定上下文中才允许使用"租户"，比如"请选择具体的商户进行登录"
                assertTrue(message.contains("请选择具体的商户进行登录"), 
                          "错误信息包含技术术语'租户': " + message);
            }
        }
    }

    @Test
    void testTechnicalErrorMessages_CanUseTechnicalTerms() {
        // 验证技术相关的错误信息可以使用技术术语
        // 这些错误码主要面向开发人员或系统管理员
        assertEquals("租户编码已存在", ErrorCode.TENANT_CODE_EXISTS.getMessage());
        assertEquals("租户名称已存在", ErrorCode.TENANT_NAME_EXISTS.getMessage());
        
        // 这些是系统内部使用的错误码，可以保留技术术语
    }

    @Test
    void testErrorCodeConsistency() {
        // 验证错误码的一致性
        // 用户模块的错误码应该都使用用户友好的术语
        
        // 220005-220007 是用户登录相关的错误码，应该使用"商户"
        assertTrue(ErrorCode.MULTIPLE_TENANT_ACCOUNTS.getMessage().contains("商户"));
        assertTrue(ErrorCode.NO_TENANT_ACCOUNTS.getMessage().contains("商户"));
        assertTrue(ErrorCode.TENANT_ACCESS_DENIED.getMessage().contains("商户"));
        
        // 240001, 240004, 240005 是面向用户的租户错误码，应该使用"商户"
        assertTrue(ErrorCode.TENANT_NOT_FOUND.getMessage().contains("商户"));
        assertTrue(ErrorCode.TENANT_EXPIRED.getMessage().contains("商户"));
        assertTrue(ErrorCode.TENANT_DISABLED.getMessage().contains("商户"));
        
        // 240002, 240003 是系统管理相关的错误码，可以使用"租户"
        assertTrue(ErrorCode.TENANT_CODE_EXISTS.getMessage().contains("租户"));
        assertTrue(ErrorCode.TENANT_NAME_EXISTS.getMessage().contains("租户"));
    }
}
