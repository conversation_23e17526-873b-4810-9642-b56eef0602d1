package com.extracme.saas.autocare.enums;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 维修项目类型枚举测试
 */
class RepairItemTypeEnumTest {

    @Test
    void testGetByCode() {
        // 测试有效编码
        assertEquals(RepairItemTypeEnum.MAINTENANCE, RepairItemTypeEnum.getByCode(1));
        assertEquals(RepairItemTypeEnum.TERMINAL, RepairItemTypeEnum.getByCode(2));
        assertEquals(RepairItemTypeEnum.REPAIR, RepairItemTypeEnum.getByCode(3));
        
        // 测试无效编码
        assertNull(RepairItemTypeEnum.getByCode(0));
        assertNull(RepairItemTypeEnum.getByCode(4));
        assertNull(RepairItemTypeEnum.getByCode(null));
    }

    @Test
    void testGetNameByCode() {
        // 测试有效编码
        assertEquals("保养", RepairItemTypeEnum.getNameByCode(1));
        assertEquals("终端", RepairItemTypeEnum.getNameByCode(2));
        assertEquals("维修", RepairItemTypeEnum.getNameByCode(3));
        
        // 测试无效编码
        assertEquals("", RepairItemTypeEnum.getNameByCode(0));
        assertEquals("", RepairItemTypeEnum.getNameByCode(4));
        assertEquals("", RepairItemTypeEnum.getNameByCode(null));
    }

    @Test
    void testIsValidCode() {
        // 测试有效编码
        assertTrue(RepairItemTypeEnum.isValidCode(1));
        assertTrue(RepairItemTypeEnum.isValidCode(2));
        assertTrue(RepairItemTypeEnum.isValidCode(3));
        
        // 测试无效编码
        assertFalse(RepairItemTypeEnum.isValidCode(0));
        assertFalse(RepairItemTypeEnum.isValidCode(4));
        assertFalse(RepairItemTypeEnum.isValidCode(null));
    }

    @Test
    void testGetByName() {
        // 测试有效名称
        assertEquals(RepairItemTypeEnum.MAINTENANCE, RepairItemTypeEnum.getByName("保养"));
        assertEquals(RepairItemTypeEnum.TERMINAL, RepairItemTypeEnum.getByName("终端"));
        assertEquals(RepairItemTypeEnum.REPAIR, RepairItemTypeEnum.getByName("维修"));
        
        // 测试带空格的名称
        assertEquals(RepairItemTypeEnum.MAINTENANCE, RepairItemTypeEnum.getByName(" 保养 "));
        
        // 测试无效名称
        assertNull(RepairItemTypeEnum.getByName("无效类型"));
        assertNull(RepairItemTypeEnum.getByName(""));
        assertNull(RepairItemTypeEnum.getByName(null));
    }

    @Test
    void testGetCodeByName() {
        // 测试有效名称
        assertEquals(Integer.valueOf(1), RepairItemTypeEnum.getCodeByName("保养"));
        assertEquals(Integer.valueOf(2), RepairItemTypeEnum.getCodeByName("终端"));
        assertEquals(Integer.valueOf(3), RepairItemTypeEnum.getCodeByName("维修"));
        
        // 测试带空格的名称
        assertEquals(Integer.valueOf(1), RepairItemTypeEnum.getCodeByName(" 保养 "));
        
        // 测试无效名称
        assertNull(RepairItemTypeEnum.getCodeByName("无效类型"));
        assertNull(RepairItemTypeEnum.getCodeByName(""));
        assertNull(RepairItemTypeEnum.getCodeByName(null));
    }

    @Test
    void testEnumValues() {
        // 测试枚举值的基本属性
        assertEquals(1, RepairItemTypeEnum.MAINTENANCE.getCode().intValue());
        assertEquals("保养", RepairItemTypeEnum.MAINTENANCE.getName());
        
        assertEquals(2, RepairItemTypeEnum.TERMINAL.getCode().intValue());
        assertEquals("终端", RepairItemTypeEnum.TERMINAL.getName());
        
        assertEquals(3, RepairItemTypeEnum.REPAIR.getCode().intValue());
        assertEquals("维修", RepairItemTypeEnum.REPAIR.getName());
    }
}
