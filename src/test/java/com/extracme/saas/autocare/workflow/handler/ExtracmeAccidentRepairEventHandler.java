package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.enums.TaskTypeEnum;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import org.springframework.stereotype.Component;

/**
 * Extracme租户自定义事故维修事件处理器
 * 用于测试租户自定义处理器
 */
@Component
public class ExtracmeAccidentRepairEventHandler extends AbstractEventHandler {

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        // 测试用，不需要实际实现
    }

    @Override
    public String getSupportedEventType() {
        return "COMPLETE_HANDOVER";
    }

    @Override
    public Integer getSupportedTaskType() {
        return TaskTypeEnum.ACCIDENT_REPAIR.getCode();
    }

    @Override
    public int getPriority() {
        return 50; // 优先级高于默认处理器
    }

    @Override
    public String getHandlerName() {
        return "Extracme租户自定义事故维修事件处理器";
    }
}
