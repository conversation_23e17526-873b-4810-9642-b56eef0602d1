package com.extracme.saas.autocare.workflow.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;

import com.extracme.saas.autocare.enums.TaskTypeEnum;

class EventHandlerRegistryTest {

    @InjectMocks
    private EventHandlerRegistry eventHandlerRegistry;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private AccidentRepairEventHandler accidentRepairEventHandler;

    @Mock
    private RegularMaintenanceEventHandler regularMaintenanceEventHandler;

    @Mock
    private ExtracmeAccidentRepairEventHandler extracmeAccidentRepairEventHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 配置模拟对象
        when(accidentRepairEventHandler.getSupportedEventType()).thenReturn("COMPLETE_HANDOVER");
        when(accidentRepairEventHandler.getSupportedTaskType()).thenReturn(TaskTypeEnum.ACCIDENT_REPAIR.getCode());
        when(accidentRepairEventHandler.getPriority()).thenReturn(100);
        when(accidentRepairEventHandler.getHandlerName()).thenReturn("事故维修事件处理器");
        
        when(regularMaintenanceEventHandler.getSupportedEventType()).thenReturn("COMPLETE_HANDOVER");
        when(regularMaintenanceEventHandler.getSupportedTaskType()).thenReturn(TaskTypeEnum.REGULAR_MAINTENANCE.getCode());
        when(regularMaintenanceEventHandler.getPriority()).thenReturn(100);
        when(regularMaintenanceEventHandler.getHandlerName()).thenReturn("常规保养事件处理器");
        
        when(extracmeAccidentRepairEventHandler.getSupportedEventType()).thenReturn("COMPLETE_HANDOVER");
        when(extracmeAccidentRepairEventHandler.getSupportedTaskType()).thenReturn(TaskTypeEnum.ACCIDENT_REPAIR.getCode());
        when(extracmeAccidentRepairEventHandler.getPriority()).thenReturn(50);
        when(extracmeAccidentRepairEventHandler.getHandlerName()).thenReturn("Extracme租户自定义事故维修事件处理器");
        
        // 手动注册处理器
        eventHandlerRegistry.registerHandler(accidentRepairEventHandler);
        eventHandlerRegistry.registerHandler(regularMaintenanceEventHandler);
        eventHandlerRegistry.registerHandler(extracmeAccidentRepairEventHandler);
    }

    @Test
    void testGetHandler_DefaultHandler() {
        // 获取默认处理器
        DefaultEventHandler handler = eventHandlerRegistry.getHandler(
                "COMPLETE_HANDOVER", 
                TaskTypeEnum.ACCIDENT_REPAIR.getCode(), 
                "other");
        
        assertNotNull(handler);
        assertEquals("事故维修事件处理器", handler.getHandlerName());
    }

    @Test
    void testGetHandler_TenantHandler() {
        // 获取租户自定义处理器
        DefaultEventHandler handler = eventHandlerRegistry.getHandler(
                "COMPLETE_HANDOVER", 
                TaskTypeEnum.ACCIDENT_REPAIR.getCode(), 
                "extracme");
        
        assertNotNull(handler);
        assertEquals("Extracme租户自定义事故维修事件处理器", handler.getHandlerName());
    }

    @Test
    void testGetHandlers_MultipleHandlers() {
        // 获取所有处理器
        List<DefaultEventHandler> handlers = eventHandlerRegistry.getHandlers(
                "COMPLETE_HANDOVER", 
                TaskTypeEnum.ACCIDENT_REPAIR.getCode(), 
                "extracme");
        
        assertNotNull(handlers);
        assertEquals(1, handlers.size());
        assertEquals("Extracme租户自定义事故维修事件处理器", handlers.get(0).getHandlerName());
    }

    @Test
    void testGetHandler_NoMatchingHandler() {
        // 获取不存在的处理器
        DefaultEventHandler handler = eventHandlerRegistry.getHandler(
                "NON_EXISTENT_EVENT", 
                TaskTypeEnum.ACCIDENT_REPAIR.getCode(), 
                "extracme");
        
        assertNull(handler);
    }

    @Test
    void testGetAllHandlers() {
        // 获取所有处理器
        List<DefaultEventHandler> allHandlers = eventHandlerRegistry.getAllHandlers();
        
        assertNotNull(allHandlers);
        assertEquals(3, allHandlers.size());
    }

    @Test
    void testHandlerPriority() {
        // 测试处理器优先级
        DefaultEventHandler handler = eventHandlerRegistry.getHandler(
                "COMPLETE_HANDOVER", 
                TaskTypeEnum.ACCIDENT_REPAIR.getCode(), 
                "extracme");
        
        assertNotNull(handler);
        assertEquals(50, handler.getPriority());
        assertEquals("Extracme租户自定义事故维修事件处理器", handler.getHandlerName());
    }
}
