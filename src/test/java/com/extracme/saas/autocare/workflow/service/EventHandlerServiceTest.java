package com.extracme.saas.autocare.workflow.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.extracme.saas.autocare.enums.TaskTypeEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import com.extracme.saas.autocare.workflow.handler.AccidentRepairEventHandler;
import com.extracme.saas.autocare.workflow.handler.EventHandlerRegistry;
import com.extracme.saas.autocare.workflow.handler.ExtracmeAccidentRepairEventHandler;
import com.extracme.saas.autocare.workflow.service.impl.EventHandlerServiceImpl;

class EventHandlerServiceTest {

    @InjectMocks
    private EventHandlerServiceImpl eventHandlerService;

    @Mock
    private EventHandlerRegistry eventHandlerRegistry;

    @Mock
    private AccidentRepairEventHandler accidentRepairEventHandler;

    @Mock
    private ExtracmeAccidentRepairEventHandler extracmeAccidentRepairEventHandler;

    @Mock
    private WorkflowInstance workflowInstance;

    private WorkflowContext context;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // 配置工作流实例
        // WorkflowInstance 没有 getTaskType 方法，在 EventHandlerServiceImpl 中使用了硬编码的任务类型
        // 这里只需要模拟 getBusinessId 方法
        when(workflowInstance.getBusinessId()).thenReturn("TEST_BUSINESS_ID");

        // 配置工作流上下文
        context = WorkflowContext.builder()
                .workflowInstance(workflowInstance)
                .triggerEvent("COMPLETE_HANDOVER")
                .operator("TEST_OPERATOR")
                .build();

        // 配置处理器
        when(accidentRepairEventHandler.getHandlerName()).thenReturn("事故维修事件处理器");
        when(extracmeAccidentRepairEventHandler.getHandlerName()).thenReturn("Extracme租户自定义事故维修事件处理器");
    }

    @Test
    void testHandleEvent_Success() {
        // 配置模拟对象
        when(eventHandlerRegistry.getHandler("COMPLETE_HANDOVER", TaskTypeEnum.ACCIDENT_REPAIR.getCode(), "extracme"))
                .thenReturn(extracmeAccidentRepairEventHandler);
        when(extracmeAccidentRepairEventHandler.handle(context, "extracme")).thenReturn(true);

        // 调用测试方法
        boolean result = eventHandlerService.handleEvent(context, "extracme");

        // 验证结果
        assertTrue(result);
        verify(eventHandlerRegistry).getHandler("COMPLETE_HANDOVER", TaskTypeEnum.ACCIDENT_REPAIR.getCode(), "extracme");
        verify(extracmeAccidentRepairEventHandler).handle(context, "extracme");
    }

    @Test
    void testHandleEvent_NoHandler() {
        // 配置模拟对象 - 两级查找都返回null
        when(eventHandlerRegistry.getHandler("COMPLETE_HANDOVER", TaskTypeEnum.ACCIDENT_REPAIR.getCode(), "extracme"))
                .thenReturn(null);
        when(eventHandlerRegistry.getHandler("COMPLETE_HANDOVER", -1, "extracme"))
                .thenReturn(null);

        // 调用测试方法
        boolean result = eventHandlerService.handleEvent(context, "extracme");

        // 验证结果
        assertFalse(result);
        verify(eventHandlerRegistry).getHandler("COMPLETE_HANDOVER", TaskTypeEnum.ACCIDENT_REPAIR.getCode(), "extracme");
        verify(eventHandlerRegistry).getHandler("COMPLETE_HANDOVER", -1, "extracme");
        verifyNoInteractions(extracmeAccidentRepairEventHandler);
    }

    @Test
    void testHandleEvent_FallbackToGenericHandler() {
        // 配置模拟对象 - 第一级查找返回null，第二级查找返回通用处理器
        when(eventHandlerRegistry.getHandler("COMPLETE_HANDOVER", TaskTypeEnum.ACCIDENT_REPAIR.getCode(), "extracme"))
                .thenReturn(null);
        when(eventHandlerRegistry.getHandler("COMPLETE_HANDOVER", -1, "extracme"))
                .thenReturn(accidentRepairEventHandler);
        when(accidentRepairEventHandler.handle(context, "extracme")).thenReturn(true);

        // 调用测试方法
        boolean result = eventHandlerService.handleEvent(context, "extracme");

        // 验证结果
        assertTrue(result);
        verify(eventHandlerRegistry).getHandler("COMPLETE_HANDOVER", TaskTypeEnum.ACCIDENT_REPAIR.getCode(), "extracme");
        verify(eventHandlerRegistry).getHandler("COMPLETE_HANDOVER", -1, "extracme");
        verify(accidentRepairEventHandler).handle(context, "extracme");
    }

    @Test
    void testHandleEvent_NullContext() {
        // 调用测试方法并验证异常
        assertThrows(BusinessException.class, () -> eventHandlerService.handleEvent(null, "extracme"));
    }

    @Test
    void testGetSupportedHandlers() {
        // 配置模拟对象
        when(eventHandlerRegistry.getHandlers("COMPLETE_HANDOVER", TaskTypeEnum.ACCIDENT_REPAIR.getCode(), "extracme"))
                .thenReturn(java.util.Arrays.asList(extracmeAccidentRepairEventHandler));

        // 调用测试方法
        String[] handlers = eventHandlerService.getSupportedHandlers(
                "COMPLETE_HANDOVER",
                TaskTypeEnum.ACCIDENT_REPAIR.getCode(),
                "extracme");

        // 验证结果
        assertNotNull(handlers);
        assertEquals(1, handlers.length);
        assertEquals("Extracme租户自定义事故维修事件处理器", handlers[0]);
    }
}
