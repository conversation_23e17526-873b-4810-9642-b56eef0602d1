package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 权限强制删除功能测试类
 */
@ExtendWith(MockitoExtension.class)
class PermissionServiceForceDeleteTest {

    @Mock
    private TablePermissionService permissionRepository;
    
    @Mock
    private TableUserService userRepository;
    
    @Mock
    private TableUserRoleService tableUserRoleService;
    
    @Mock
    private UserPermissionCacheUtils userPermissionCacheUtils;

    @InjectMocks
    private PermissionServiceImpl permissionService;

    private SysPermission testPermission;
    private List<Long> affectedRoleIds;
    private Long testTenantId;

    @BeforeEach
    void setUp() {
        // 设置测试权限
        testPermission = new SysPermission();
        testPermission.setId(1L);
        testPermission.setPermissionName("测试权限");
        testPermission.setPermissionCode("test:permission");
        
        // 设置受影响的角色ID列表
        affectedRoleIds = Arrays.asList(101L, 102L, 103L);
        
        // 设置测试租户ID
        testTenantId = 100L;
    }

    @Test
    @DisplayName("普通删除 - 权限未被使用时应该成功删除")
    void deletePermission_NotInUse_ShouldDeleteSuccessfully() {
        // Given
        when(permissionRepository.selectById(1L)).thenReturn(testPermission);
        when(permissionRepository.findRoleIdsByPermissionId(1L)).thenReturn(Collections.emptyList());
        
        // When
        permissionService.deletePermission(1L, false);
        
        // Then
        verify(permissionRepository).deleteById(1L);
        verify(permissionRepository, never()).deleteByPermissionId(any());
        verify(userPermissionCacheUtils, never()).refreshUserPermissionsByRole(any(), any());
    }

    @Test
    @DisplayName("普通删除 - 权限被使用时应该抛出异常")
    void deletePermission_InUse_ShouldThrowException() {
        // Given
        when(permissionRepository.selectById(1L)).thenReturn(testPermission);
        when(permissionRepository.findRoleIdsByPermissionId(1L)).thenReturn(affectedRoleIds);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> permissionService.deletePermission(1L, false));

        assertEquals(ErrorCode.PERMISSION_IN_USE.getCode(), exception.getCode());
        verify(permissionRepository, never()).deleteById(any());
        verify(permissionRepository, never()).deleteByPermissionId(any());
    }

    @Test
    @DisplayName("强制删除 - 权限被使用时应该删除关联并清除缓存")
    void deletePermission_ForceDelete_ShouldDeleteAssociationsAndClearCache() {
        // Given
        when(permissionRepository.selectById(1L)).thenReturn(testPermission);
        when(permissionRepository.findRoleIdsByPermissionId(1L)).thenReturn(affectedRoleIds);
        when(userPermissionCacheUtils.refreshUserPermissionsByRole(testTenantId, 101L)).thenReturn(2);
        when(userPermissionCacheUtils.refreshUserPermissionsByRole(testTenantId, 102L)).thenReturn(1);
        when(userPermissionCacheUtils.refreshUserPermissionsByRole(testTenantId, 103L)).thenReturn(3);
        
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getTenantId).thenReturn(testTenantId);
            
            // When
            permissionService.deletePermission(1L, true);
            
            // Then
            verify(permissionRepository).deleteByPermissionId(1L);
            verify(permissionRepository).deleteById(1L);
            verify(userPermissionCacheUtils).refreshUserPermissionsByRole(testTenantId, 101L);
            verify(userPermissionCacheUtils).refreshUserPermissionsByRole(testTenantId, 102L);
            verify(userPermissionCacheUtils).refreshUserPermissionsByRole(testTenantId, 103L);
        }
    }

    @Test
    @DisplayName("强制删除 - 权限未被使用时应该正常删除")
    void deletePermission_ForceDeleteNotInUse_ShouldDeleteNormally() {
        // Given
        when(permissionRepository.selectById(1L)).thenReturn(testPermission);
        when(permissionRepository.findRoleIdsByPermissionId(1L)).thenReturn(Collections.emptyList());
        
        // When
        permissionService.deletePermission(1L, true);
        
        // Then
        verify(permissionRepository).deleteById(1L);
        verify(permissionRepository, never()).deleteByPermissionId(any());
        verify(userPermissionCacheUtils, never()).refreshUserPermissionsByRole(any(), any());
    }

    @Test
    @DisplayName("删除不存在的权限应该抛出异常")
    void deletePermission_NotFound_ShouldThrowException() {
        // Given
        when(permissionRepository.selectById(999L)).thenReturn(null);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> permissionService.deletePermission(999L, false));

        assertEquals(ErrorCode.PERMISSION_NOT_FOUND.getCode(), exception.getCode());
        verify(permissionRepository, never()).deleteById(any());
    }

    @Test
    @DisplayName("强制删除时缓存刷新异常不应该影响删除操作")
    void deletePermission_ForceDelete_CacheRefreshException_ShouldContinueDelete() {
        // Given
        when(permissionRepository.selectById(1L)).thenReturn(testPermission);
        when(permissionRepository.findRoleIdsByPermissionId(1L)).thenReturn(affectedRoleIds);
        when(userPermissionCacheUtils.refreshUserPermissionsByRole(any(), any()))
            .thenThrow(new RuntimeException("缓存刷新失败"));
        
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getTenantId).thenReturn(testTenantId);
            
            // When
            assertDoesNotThrow(() -> permissionService.deletePermission(1L, true));
            
            // Then
            verify(permissionRepository).deleteByPermissionId(1L);
            verify(permissionRepository).deleteById(1L);
        }
    }

    @Test
    @DisplayName("默认删除方法应该调用非强制删除")
    void deletePermission_DefaultMethod_ShouldCallNonForceDelete() {
        // Given
        when(permissionRepository.selectById(1L)).thenReturn(testPermission);
        when(permissionRepository.findRoleIdsByPermissionId(1L)).thenReturn(Collections.emptyList());
        
        // When
        permissionService.deletePermission(1L);
        
        // Then
        verify(permissionRepository).deleteById(1L);
        verify(permissionRepository, never()).deleteByPermissionId(any());
    }

    @Test
    @DisplayName("多租户隔离 - 应该使用当前租户ID刷新缓存")
    void deletePermission_ForceDelete_ShouldUseTenantIsolation() {
        // Given
        Long anotherTenantId = 200L;
        when(permissionRepository.selectById(1L)).thenReturn(testPermission);
        when(permissionRepository.findRoleIdsByPermissionId(1L)).thenReturn(Arrays.asList(101L));
        when(userPermissionCacheUtils.refreshUserPermissionsByRole(anotherTenantId, 101L)).thenReturn(1);
        
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getTenantId).thenReturn(anotherTenantId);
            
            // When
            permissionService.deletePermission(1L, true);
            
            // Then
            verify(userPermissionCacheUtils).refreshUserPermissionsByRole(anotherTenantId, 101L);
            verify(userPermissionCacheUtils, never()).refreshUserPermissionsByRole(eq(testTenantId), any());
        }
    }
}
