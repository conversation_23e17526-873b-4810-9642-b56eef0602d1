package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateQueryDTO;
import com.extracme.saas.autocare.model.entity.WorkflowTemplate;
import com.extracme.saas.autocare.repository.TableWorkflowTemplateService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 工作流模板智能匹配算法测试
 */
public class WorkflowTemplateMatchingTest {

    @InjectMocks
    private WorkflowServiceImpl workflowService;

    @Mock
    private TableWorkflowTemplateService tableWorkflowTemplateService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试智能匹配算法的优先级
     */
    @Test
    @DisplayName("测试智能匹配算法的优先级：完全匹配 > 部分匹配 > 默认模板")
    public void testSmartMatchingPriority() throws Exception {
        // 准备测试数据
        Integer taskType = 1;
        Integer repairFactoryType = 2;
        Integer subProductLine = 3;

        // 创建不同匹配度的模板
        WorkflowTemplate exactMatch = createTemplate(1L, "完全匹配", taskType, repairFactoryType, subProductLine);
        WorkflowTemplate partialMatch1 = createTemplate(2L, "部分匹配1", -1, repairFactoryType, subProductLine);
        WorkflowTemplate partialMatch2 = createTemplate(3L, "部分匹配2", taskType, -1, subProductLine);
        WorkflowTemplate defaultTemplate = createTemplate(4L, "默认模板", -1, -1, -1);

        List<WorkflowTemplate> templates = Arrays.asList(defaultTemplate, partialMatch1, exactMatch, partialMatch2);

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = WorkflowServiceImpl.class.getDeclaredMethod(
                    "findMatchingWorkflowTemplate", Integer.class, Integer.class, Integer.class);
            method.setAccessible(true);

            WorkflowTemplate result = (WorkflowTemplate) method.invoke(workflowService, taskType, repairFactoryType, subProductLine);

            // 验证结果 - 应该选择完全匹配的模板
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals("完全匹配", result.getWorkflowName());
        }
    }

    /**
     * 测试严格匹配的评分机制
     */
    @Test
    @DisplayName("测试严格匹配的评分机制：更多完全匹配字段的模板优先级更高")
    public void testStrictMatchScoring() throws Exception {
        // 准备测试数据
        Integer taskType = 1;
        Integer repairFactoryType = 2;
        Integer subProductLine = 3;

        // 创建不同评分的严格匹配模板
        WorkflowTemplate twoMatches = createTemplate(1L, "两个匹配", taskType, repairFactoryType, -1); // 评分: 10+10+0=20
        WorkflowTemplate oneMatch = createTemplate(2L, "一个匹配", taskType, -1, -1); // 评分: 10+0+0=10
        WorkflowTemplate allWildcard = createTemplate(3L, "全通配符", -1, -1, -1); // 评分: 0+0+0=0

        List<WorkflowTemplate> templates = Arrays.asList(allWildcard, oneMatch, twoMatches);

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = WorkflowServiceImpl.class.getDeclaredMethod(
                    "findMatchingWorkflowTemplate", Integer.class, Integer.class, Integer.class);
            method.setAccessible(true);

            WorkflowTemplate result = (WorkflowTemplate) method.invoke(workflowService, taskType, repairFactoryType, subProductLine);

            // 验证结果 - 应该选择评分最高的模板（两个匹配，评分20）
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals("两个匹配", result.getWorkflowName());
        }
    }

    /**
     * 测试通配符匹配机制
     */
    @Test
    @DisplayName("测试通配符匹配机制：-1值表示匹配所有")
    public void testWildcardMatching() throws Exception {
        // 准备测试数据
        Integer taskType = 999; // 使用一个特殊值
        Integer repairFactoryType = 888;
        Integer subProductLine = 777;

        // 创建只有通配符模板能匹配的场景
        WorkflowTemplate wildcardTemplate = createTemplate(1L, "通配符模板", -1, -1, -1);
        WorkflowTemplate specificTemplate = createTemplate(2L, "特定模板", 1, 2, 3); // 不匹配

        List<WorkflowTemplate> templates = Arrays.asList(specificTemplate, wildcardTemplate);

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = WorkflowServiceImpl.class.getDeclaredMethod(
                    "findMatchingWorkflowTemplate", Integer.class, Integer.class, Integer.class);
            method.setAccessible(true);

            WorkflowTemplate result = (WorkflowTemplate) method.invoke(workflowService, taskType, repairFactoryType, subProductLine);

            // 验证结果 - 应该选择通配符模板
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals("通配符模板", result.getWorkflowName());
        }
    }

    /**
     * 测试部分字段不匹配的模板被完全排除
     */
    @Test
    @DisplayName("测试部分字段不匹配的模板被完全排除")
    public void testPartialMismatchExclusion() throws Exception {
        // 准备测试数据
        Integer taskType = 1;
        Integer repairFactoryType = 2;
        Integer subProductLine = 3;

        // 创建模板：一个完全匹配，一个部分不匹配
        WorkflowTemplate exactMatch = createTemplate(1L, "完全匹配", taskType, repairFactoryType, subProductLine);
        WorkflowTemplate partialMismatch = createTemplate(2L, "部分不匹配", taskType, repairFactoryType, 999); // 子产品线不匹配

        List<WorkflowTemplate> templates = Arrays.asList(partialMismatch, exactMatch);

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = WorkflowServiceImpl.class.getDeclaredMethod(
                    "findMatchingWorkflowTemplate", Integer.class, Integer.class, Integer.class);
            method.setAccessible(true);

            WorkflowTemplate result = (WorkflowTemplate) method.invoke(workflowService, taskType, repairFactoryType, subProductLine);

            // 验证结果 - 应该选择完全匹配的模板，部分不匹配的模板被排除
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals("完全匹配", result.getWorkflowName());
        }
    }

    /**
     * 测试所有模板都不匹配时抛出异常
     */
    @Test
    @DisplayName("测试所有模板都不匹配时抛出异常")
    public void testAllTemplatesMismatchException() throws Exception {
        // 准备测试数据
        Integer taskType = 1;
        Integer repairFactoryType = 2;
        Integer subProductLine = 3;

        // 创建都不匹配的模板
        WorkflowTemplate mismatch1 = createTemplate(1L, "不匹配1", 999, repairFactoryType, subProductLine);
        WorkflowTemplate mismatch2 = createTemplate(2L, "不匹配2", taskType, 888, subProductLine);
        WorkflowTemplate mismatch3 = createTemplate(3L, "不匹配3", taskType, repairFactoryType, 777);

        List<WorkflowTemplate> templates = Arrays.asList(mismatch1, mismatch2, mismatch3);

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = WorkflowServiceImpl.class.getDeclaredMethod(
                    "findMatchingWorkflowTemplate", Integer.class, Integer.class, Integer.class);
            method.setAccessible(true);

            // 验证异常 - 应该抛出BusinessException
            Exception exception = assertThrows(Exception.class, () -> {
                method.invoke(workflowService, taskType, repairFactoryType, subProductLine);
            });

            // 检查是否是BusinessException的反射包装
            assertTrue(exception.getCause() != null);
            assertTrue(exception.getCause().getMessage().contains("未找到匹配的工作流模板"));
        }
    }

    /**
     * 测试多个模板评分相同时的选择逻辑
     */
    @Test
    @DisplayName("测试多个模板评分相同时选择第一个找到的模板")
    public void testSameScoreSelection() throws Exception {
        // 准备测试数据
        Integer taskType = 1;
        Integer repairFactoryType = 2;
        Integer subProductLine = 3;

        // 创建评分相同的模板（都是全通配符，评分都为0）
        WorkflowTemplate template1 = createTemplate(1L, "第一个模板", -1, -1, -1);
        WorkflowTemplate template2 = createTemplate(2L, "第二个模板", -1, -1, -1);
        WorkflowTemplate template3 = createTemplate(3L, "第三个模板", -1, -1, -1);

        List<WorkflowTemplate> templates = Arrays.asList(template1, template2, template3);

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = WorkflowServiceImpl.class.getDeclaredMethod(
                    "findMatchingWorkflowTemplate", Integer.class, Integer.class, Integer.class);
            method.setAccessible(true);

            WorkflowTemplate result = (WorkflowTemplate) method.invoke(workflowService, taskType, repairFactoryType, subProductLine);

            // 验证结果 - 应该选择第一个找到的模板
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals("第一个模板", result.getWorkflowName());
        }
    }

    /**
     * 测试边界情况：所有字段都是通配符的模板
     */
    @Test
    @DisplayName("测试边界情况：所有字段都是通配符的模板评分为0但仍可被选中")
    public void testAllWildcardTemplate() throws Exception {
        // 准备测试数据
        Integer taskType = 1;
        Integer repairFactoryType = 2;
        Integer subProductLine = 3;

        // 创建只有全通配符模板的情况
        WorkflowTemplate allWildcard = createTemplate(1L, "全通配符模板", -1, -1, -1);

        List<WorkflowTemplate> templates = Arrays.asList(allWildcard);

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = WorkflowServiceImpl.class.getDeclaredMethod(
                    "findMatchingWorkflowTemplate", Integer.class, Integer.class, Integer.class);
            method.setAccessible(true);

            WorkflowTemplate result = (WorkflowTemplate) method.invoke(workflowService, taskType, repairFactoryType, subProductLine);

            // 验证结果 - 全通配符模板应该被选中（评分为0但仍然有效）
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals("全通配符模板", result.getWorkflowName());
        }
    }

    // 辅助方法：创建工作流模板
    private WorkflowTemplate createTemplate(Long id, String name, Integer taskType, Integer repairFactoryType, Integer subProductLine) {
        WorkflowTemplate template = new WorkflowTemplate();
        template.setId(id);
        template.setWorkflowName(name);
        template.setTaskType(taskType);
        template.setRepairFactoryType(repairFactoryType);
        template.setSubProductLine(subProductLine);
        template.setPartsLibraryType(1); // 自有配件库
        template.setIsActive(1);
        template.setTenantId(1);
        template.setCreateTime(new Date());
        template.setCreateBy("testuser");
        template.setUpdateTime(new Date());
        template.setUpdateBy("testuser");
        template.setDescription("测试模板");
        return template;
    }
}
