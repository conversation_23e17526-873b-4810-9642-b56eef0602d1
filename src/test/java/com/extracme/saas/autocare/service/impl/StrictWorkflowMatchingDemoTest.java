package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowStartDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateQueryDTO;
import com.extracme.saas.autocare.model.entity.ActivityDefinition;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.entity.WorkflowTemplate;
import com.extracme.saas.autocare.repository.TableActivityDefinitionService;
import com.extracme.saas.autocare.repository.TableActivityInstanceService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.repository.TableWorkflowTemplateService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 严格工作流匹配算法演示测试
 * 
 * 展示新的严格匹配逻辑：
 * - 完全匹配：+10分
 * - 通配符匹配（-1）：0分
 * - 不匹配：直接排除
 */
public class StrictWorkflowMatchingDemoTest {

    @InjectMocks
    private WorkflowServiceImpl workflowService;

    @Mock
    private TableWorkflowTemplateService tableWorkflowTemplateService;
    
    @Mock
    private TableActivityDefinitionService tableActivityDefinitionService;
    
    @Mock
    private TableWorkflowInstanceService tableWorkflowInstanceService;
    
    @Mock
    private TableActivityInstanceService tableActivityInstanceService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 综合演示：严格匹配算法的完整功能
     */
    @Test
    @DisplayName("综合演示：严格匹配算法选择最佳模板")
    public void testComprehensiveStrictMatching() {
        // 准备测试数据：查找 taskType=1, repairFactoryType=2, subProductLine=3 的模板
        Integer taskType = 1;
        Integer repairFactoryType = 2;
        Integer subProductLine = 3;

        // 创建各种类型的模板
        WorkflowTemplate perfectMatch = createTemplate(1L, "完全匹配模板", 1, 2, 3);           // 评分: 30
        WorkflowTemplate twoMatches = createTemplate(2L, "两字段匹配", 1, 2, -1);            // 评分: 20
        WorkflowTemplate oneMatch = createTemplate(3L, "一字段匹配", 1, -1, -1);             // 评分: 10
        WorkflowTemplate allWildcard = createTemplate(4L, "全通配符", -1, -1, -1);          // 评分: 0
        WorkflowTemplate partialMismatch = createTemplate(5L, "部分不匹配", 1, 2, 999);      // 被排除
        WorkflowTemplate totalMismatch = createTemplate(6L, "完全不匹配", 999, 888, 777);    // 被排除

        List<WorkflowTemplate> templates = Arrays.asList(
            totalMismatch, partialMismatch, allWildcard, oneMatch, twoMatches, perfectMatch
        );

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            // 使用反射调用私有方法进行测试
            try {
                java.lang.reflect.Method method = WorkflowServiceImpl.class.getDeclaredMethod(
                        "findMatchingWorkflowTemplate", Integer.class, Integer.class, Integer.class);
                method.setAccessible(true);

                WorkflowTemplate result = (WorkflowTemplate) method.invoke(workflowService, taskType, repairFactoryType, subProductLine);

                // 验证结果 - 应该选择完全匹配的模板（评分30）
                assertNotNull(result);
                assertEquals(1L, result.getId());
                assertEquals("完全匹配模板", result.getWorkflowName());
                
                System.out.println("✅ 严格匹配成功：选择了完全匹配的模板（评分30）");
                
            } catch (Exception e) {
                throw new RuntimeException("测试执行失败", e);
            }
        }
    }

    /**
     * 演示：只有通配符模板可用的情况
     */
    @Test
    @DisplayName("演示：只有通配符模板时仍能正常匹配")
    public void testOnlyWildcardTemplateAvailable() {
        // 准备测试数据
        Integer taskType = 999;  // 特殊值，只有通配符能匹配
        Integer repairFactoryType = 888;
        Integer subProductLine = 777;

        // 创建模板：只有通配符模板能匹配
        WorkflowTemplate specificTemplate = createTemplate(1L, "特定模板", 1, 2, 3);        // 被排除
        WorkflowTemplate wildcardTemplate = createTemplate(2L, "通配符模板", -1, -1, -1);   // 评分: 0

        List<WorkflowTemplate> templates = Arrays.asList(specificTemplate, wildcardTemplate);

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            try {
                java.lang.reflect.Method method = WorkflowServiceImpl.class.getDeclaredMethod(
                        "findMatchingWorkflowTemplate", Integer.class, Integer.class, Integer.class);
                method.setAccessible(true);

                WorkflowTemplate result = (WorkflowTemplate) method.invoke(workflowService, taskType, repairFactoryType, subProductLine);

                // 验证结果 - 应该选择通配符模板
                assertNotNull(result);
                assertEquals(2L, result.getId());
                assertEquals("通配符模板", result.getWorkflowName());
                
                System.out.println("✅ 通配符匹配成功：选择了通配符模板（评分0但仍有效）");
                
            } catch (Exception e) {
                throw new RuntimeException("测试执行失败", e);
            }
        }
    }

    /**
     * 演示：所有模板都不匹配的异常情况
     */
    @Test
    @DisplayName("演示：所有模板都不匹配时抛出异常")
    public void testNoTemplateMatchesException() {
        // 准备测试数据
        Integer taskType = 1;
        Integer repairFactoryType = 2;
        Integer subProductLine = 3;

        // 创建都不匹配的模板
        WorkflowTemplate mismatch1 = createTemplate(1L, "不匹配1", 999, 2, 3);
        WorkflowTemplate mismatch2 = createTemplate(2L, "不匹配2", 1, 888, 3);
        WorkflowTemplate mismatch3 = createTemplate(3L, "不匹配3", 1, 2, 777);

        List<WorkflowTemplate> templates = Arrays.asList(mismatch1, mismatch2, mismatch3);

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            try {
                java.lang.reflect.Method method = WorkflowServiceImpl.class.getDeclaredMethod(
                        "findMatchingWorkflowTemplate", Integer.class, Integer.class, Integer.class);
                method.setAccessible(true);

                // 验证异常
                Exception exception = assertThrows(Exception.class, () -> {
                    method.invoke(workflowService, taskType, repairFactoryType, subProductLine);
                });

                // 检查是否是BusinessException的反射包装
                assertNotNull(exception.getCause());
                assertEquals(BusinessException.class, exception.getCause().getClass());
                
                System.out.println("✅ 异常处理成功：所有模板都不匹配时正确抛出异常");
                
            } catch (Exception e) {
                throw new RuntimeException("测试执行失败", e);
            }
        }
    }

    /**
     * 演示：完整的工作流启动流程
     */
    @Test
    @DisplayName("演示：使用严格匹配算法启动工作流")
    public void testCompleteWorkflowStartWithStrictMatching() {
        // 准备测试数据
        WorkflowStartDTO startDTO = new WorkflowStartDTO();
        startDTO.setTaskType(1);
        startDTO.setRepairFactoryType(2);
        startDTO.setSubProductLine(3);
        startDTO.setPartsLibraryType(1);
        startDTO.setBusinessId("DEMO_BUSINESS_001");
        startDTO.setOperator("demo_user");
        startDTO.setRemarks("严格匹配算法演示");

        // 创建模板
        WorkflowTemplate bestTemplate = createTemplate(1L, "最佳匹配模板", 1, 2, 3);
        WorkflowTemplate fallbackTemplate = createTemplate(2L, "备用模板", -1, -1, -1);

        List<WorkflowTemplate> templates = Arrays.asList(fallbackTemplate, bestTemplate);

        // 创建活动定义
        ActivityDefinition activity = new ActivityDefinition();
        activity.setId(1L);
        activity.setActivityCode("DEMO_ACTIVITY");
        activity.setSequence(1);

        // 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);
        when(tableActivityDefinitionService.selectByWorkflowId(1L)).thenReturn(Arrays.asList(activity));
        when(tableWorkflowInstanceService.selectByBusinessId("DEMO_BUSINESS_001")).thenReturn(null);
        when(tableWorkflowInstanceService.insert(any(WorkflowInstance.class), any(String.class))).thenAnswer(invocation -> {
            WorkflowInstance instance = invocation.getArgument(0);
            instance.setId(100L);
            return instance;
        });

        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);
            mockedSessionUtils.when(SessionUtils::getUsername).thenReturn("demo_user");

            // 执行工作流启动
            Long instanceId = workflowService.startWorkflow(startDTO);

            // 验证结果
            assertNotNull(instanceId);
            assertEquals(100L, instanceId);
            
            System.out.println("✅ 工作流启动成功：使用严格匹配算法选择了最佳模板并创建了实例");
        }
    }

    // 辅助方法：创建工作流模板
    private WorkflowTemplate createTemplate(Long id, String name, Integer taskType, Integer repairFactoryType, Integer subProductLine) {
        WorkflowTemplate template = new WorkflowTemplate();
        template.setId(id);
        template.setWorkflowName(name);
        template.setTaskType(taskType);
        template.setRepairFactoryType(repairFactoryType);
        template.setSubProductLine(subProductLine);
        template.setPartsLibraryType(1);
        template.setIsActive(1);
        template.setTenantId(1);
        template.setCreateTime(new Date());
        template.setCreateBy("demo_user");
        template.setUpdateTime(new Date());
        template.setUpdateBy("demo_user");
        template.setDescription("演示模板");
        return template;
    }
}
