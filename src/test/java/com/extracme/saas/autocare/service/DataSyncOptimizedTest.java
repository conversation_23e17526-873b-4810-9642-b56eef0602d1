package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.dto.VehicleInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.vo.DataSyncResultVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 数据同步优化功能测试
 * 测试优化后的日志记录和租户上下文管理
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@ActiveProfiles("test")
class DataSyncOptimizedTest {

    @Mock
    private DataSyncService dataSyncService;

    @BeforeEach
    void setUp() {
        // 清理初始状态
        TenantContextHolder.clear();
    }

    /**
     * 测试租户上下文管理优化
     * 验证租户上下文只在批量同步开始时设置一次，结束时清理一次
     */
    @Test
    void testOptimizedTenantContextManagement() {
        log.info("测试租户上下文管理优化");

        // 清理初始上下文
        TenantContextHolder.clear();
        assertNull(TenantContextHolder.getTenantId(), "初始租户上下文应为空");

        // 模拟批量同步过程中的租户上下文设置
        Long testTenantId = 1L;
        TenantContextHolder.setTenant(testTenantId);
        
        // 验证租户上下文已设置
        assertEquals(testTenantId, TenantContextHolder.getTenantId(), "租户上下文应已设置");
        assertEquals("auto_care_dzjt", TenantContextHolder.getTenantSchema(), "租户schema应正确");

        // 模拟多次数据同步操作（在优化后，不会重复设置租户上下文）
        for (int i = 0; i < 5; i++) {
            // 验证租户上下文保持不变
            assertEquals(testTenantId, TenantContextHolder.getTenantId(), 
                        "第" + (i+1) + "次操作时租户上下文应保持不变");
        }

        // 模拟批量同步结束，清理租户上下文
        TenantContextHolder.clear();
        assertNull(TenantContextHolder.getTenantId(), "批量同步结束后租户上下文应被清理");

        log.info("租户上下文管理优化测试通过");
    }

    /**
     * 测试批量同步日志记录优化
     * 验证每次批量同步只记录一条汇总日志
     */
    @Test
    void testOptimizedBatchSyncLogging() {
        log.info("测试批量同步日志记录优化");

        // 创建测试数据
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> testData = createTestData(10);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey("test_key");
        requestDTO.setBatchData(testData);

        // 模拟批量同步服务调用
        DataSyncResultVO mockResult = new DataSyncResultVO();
        mockResult.setBatchNo("BATCH_TEST_001");
        mockResult.setTotalCount(10);
        mockResult.setSuccessCount(10);
        mockResult.setFailedCount(0);
        mockResult.setSyncStatus("SUCCESS");
        mockResult.setTenantCode("test_tenant");
        mockResult.setTargetTable("mtc_vehicle_info");

        when(dataSyncService.syncVehicleInfoBatch(any(), anyString()))
            .thenReturn(mockResult);

        // 执行批量同步
        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, "127.0.0.1");

        // 验证结果
        assertNotNull(result, "同步结果不应为空");
        assertEquals(10, result.getTotalCount(), "总数应为10");
        assertEquals(10, result.getSuccessCount(), "成功数应为10");
        assertEquals(0, result.getFailedCount(), "失败数应为0");
        assertEquals("SUCCESS", result.getSyncStatus(), "状态应为SUCCESS");

        // 验证服务被调用了一次（代表只记录一条汇总日志）
        verify(dataSyncService, times(1)).syncVehicleInfoBatch(any(), anyString());

        log.info("批量同步日志记录优化测试通过，批次号：{}，总数：{}，成功：{}", 
                result.getBatchNo(), result.getTotalCount(), result.getSuccessCount());
    }

    /**
     * 测试部分失败场景的日志记录优化
     */
    @Test
    void testOptimizedPartialFailureLogging() {
        log.info("测试部分失败场景的日志记录优化");

        // 创建测试数据
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> testData = createTestData(5);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey("test_key");
        requestDTO.setBatchData(testData);

        // 模拟部分失败的批量同步结果
        DataSyncResultVO mockResult = new DataSyncResultVO();
        mockResult.setBatchNo("BATCH_TEST_002");
        mockResult.setTotalCount(5);
        mockResult.setSuccessCount(3);
        mockResult.setFailedCount(2);
        mockResult.setSyncStatus("PARTIAL_SUCCESS");
        mockResult.setTenantCode("test_tenant");
        mockResult.setTargetTable("mtc_vehicle_info");

        // 添加失败详情
        List<DataSyncResultVO.SyncFailureDetailVO> failureDetails = new ArrayList<>();
        DataSyncResultVO.SyncFailureDetailVO failure1 = new DataSyncResultVO.SyncFailureDetailVO();
        failure1.setSourceDataId("车辆[TEST_VIN_004/TEST_NO_004]");
        failure1.setErrorMessage("车辆ID不能为空");
        failureDetails.add(failure1);

        DataSyncResultVO.SyncFailureDetailVO failure2 = new DataSyncResultVO.SyncFailureDetailVO();
        failure2.setSourceDataId("车辆[TEST_VIN_005/TEST_NO_005]");
        failure2.setErrorMessage("车架号格式不正确");
        failureDetails.add(failure2);

        mockResult.setFailureDetails(failureDetails);

        when(dataSyncService.syncVehicleInfoBatch(any(), anyString()))
            .thenReturn(mockResult);

        // 执行批量同步
        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, "127.0.0.1");

        // 验证结果
        assertNotNull(result, "同步结果不应为空");
        assertEquals(5, result.getTotalCount(), "总数应为5");
        assertEquals(3, result.getSuccessCount(), "成功数应为3");
        assertEquals(2, result.getFailedCount(), "失败数应为2");
        assertEquals("PARTIAL_SUCCESS", result.getSyncStatus(), "状态应为PARTIAL_SUCCESS");
        assertEquals(2, result.getFailureDetails().size(), "失败详情应有2条");

        log.info("部分失败场景日志记录优化测试通过，批次号：{}，成功：{}，失败：{}", 
                result.getBatchNo(), result.getSuccessCount(), result.getFailedCount());
    }

    /**
     * 创建测试数据
     */
    private List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> createTestData(int count) {
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> dataList = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO data = 
                new VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO();
            data.setId((long) i);
            data.setVin("TEST_VIN_" + String.format("%03d", i));
            data.setVehicleNo("TEST_NO_" + String.format("%03d", i));
            data.setVehicleModelId(1L);
            dataList.add(data);
        }
        
        return dataList;
    }
}
