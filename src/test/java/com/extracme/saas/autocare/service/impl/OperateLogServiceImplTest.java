package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.model.dto.OperateLogQueryDTO;
import com.extracme.saas.autocare.model.entity.SysOperateLog;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.vo.OperateLogVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableSysOperateLogService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.util.SessionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 操作日志服务测试类
 */
@ExtendWith(MockitoExtension.class)
class OperateLogServiceImplTest {

    @Mock
    private TableSysOperateLogService tableSysOperateLogService;

    @Mock
    private TableTenantService tableTenantService;

    @InjectMocks
    private OperateLogServiceImpl operateLogService;

    private OperateLogQueryDTO queryDTO;
    private SysOperateLog testLog;
    private SysTenant testTenant;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        queryDTO = new OperateLogQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        testLog = new SysOperateLog();
        testLog.setId(1L);
        testLog.setTenantId(1L);
        testLog.setModuleType(1); // 1：用户管理
        testLog.setOperateType(1); // 1：新增
        testLog.setContent("创建用户");
        testLog.setCreateBy("张三");
        testLog.setCreateTime(new Date());
        testLog.setStatus(1);

        testTenant = new SysTenant();
        testTenant.setId(1L);
        testTenant.setTenantName("测试租户");
    }

    @Test
    void testGetOperateLogList_WithOperatorNameFilter() {
        // 设置查询条件
        queryDTO.setOperatorName("张三");

        // Mock 数据
        when(tableSysOperateLogService.findByConditionWithFilters(any(), eq("张三"), isNull(), isNull()))
            .thenReturn(Arrays.asList(testLog));
        when(tableTenantService.findByCondition(any(), any(), any()))
            .thenReturn(Arrays.asList(testTenant));

        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(true);

            // 执行测试
            BasePageVO<OperateLogVO> result = operateLogService.getOperateLogList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            
            // 验证调用了正确的方法
            verify(tableSysOperateLogService).findByConditionWithFilters(isNull(), eq("张三"), isNull(), isNull());
        }
    }

    @Test
    void testGetOperateLogList_WithDateRangeFilter() {
        // 设置查询条件
        queryDTO.setStartDate("2024-01-01");
        queryDTO.setEndDate("2024-01-31");

        // Mock 数据
        when(tableSysOperateLogService.findByConditionWithFilters(any(), isNull(), eq("2024-01-01"), eq("2024-01-31")))
            .thenReturn(Arrays.asList(testLog));
        when(tableTenantService.findByCondition(any(), any(), any()))
            .thenReturn(Arrays.asList(testTenant));

        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(true);

            // 执行测试
            BasePageVO<OperateLogVO> result = operateLogService.getOperateLogList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            
            // 验证调用了正确的方法
            verify(tableSysOperateLogService).findByConditionWithFilters(isNull(), isNull(), eq("2024-01-01"), eq("2024-01-31"));
        }
    }

    @Test
    void testGetOperateLogList_WithAllFilters() {
        // 设置查询条件
        queryDTO.setOperatorName("张三");
        queryDTO.setStartDate("2024-01-01");
        queryDTO.setEndDate("2024-01-31");

        // Mock 数据
        when(tableSysOperateLogService.findByConditionWithFilters(any(), eq("张三"), eq("2024-01-01"), eq("2024-01-31")))
            .thenReturn(Arrays.asList(testLog));
        when(tableTenantService.findByCondition(any(), any(), any()))
            .thenReturn(Arrays.asList(testTenant));

        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(true);

            // 执行测试
            BasePageVO<OperateLogVO> result = operateLogService.getOperateLogList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            
            // 验证调用了正确的方法
            verify(tableSysOperateLogService).findByConditionWithFilters(isNull(), eq("张三"), eq("2024-01-01"), eq("2024-01-31"));
        }
    }

    @Test
    void testGetOperateLogList_EmptyResult() {
        // Mock 空结果
        when(tableSysOperateLogService.findByConditionWithFilters(any(), any(), any(), any()))
            .thenReturn(Collections.emptyList());

        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(true);

            // 执行测试
            BasePageVO<OperateLogVO> result = operateLogService.getOperateLogList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.getList().isEmpty());
        }
    }
}
