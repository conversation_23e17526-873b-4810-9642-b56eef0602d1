package com.extracme.saas.autocare.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.*;
import com.extracme.saas.autocare.config.OssConfig;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.MultipartUploadCompleteResult;
import com.extracme.saas.autocare.model.dto.MultipartUploadInitResult;
import com.extracme.saas.autocare.model.dto.PartETagInfo;
import com.extracme.saas.autocare.model.dto.ResumableUploadInitDTO;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.service.impl.OssServiceImpl;
import com.extracme.saas.autocare.util.SessionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.net.URL;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OSS断点续传服务测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("OSS断点续传服务测试")
class OssServiceResumableTest {

    @Mock
    private OSS ossClient;

    @Mock
    private OssConfig ossConfig;

    @InjectMocks
    private OssServiceImpl ossService;

    private SysUser mockUser;
    private LoginUser mockLoginUser;

    @BeforeEach
    void setUp() {
        // 设置模拟用户
        mockUser = new SysUser();
        mockUser.setId(123L);
        mockUser.setUsername("testuser");
        mockUser.setTenantId(1L);

        mockLoginUser = new LoginUser();
        mockLoginUser.setUser(mockUser);
    }

    @Test
    @DisplayName("测试初始化断点续传上传成功")
    void testInitializeResumableUploadSuccess() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置OSS配置
            when(ossConfig.getBucketName()).thenReturn("test-bucket");

            // 设置模拟数据
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            ResumableUploadInitDTO initDTO = new ResumableUploadInitDTO();
            initDTO.setOriginalFileName("test.txt");
            initDTO.setFileSize(1024L);
            initDTO.setContentType("text/plain");
            initDTO.setCategory("documents");
            
            String uploadId = "test-upload-id-123";
            
            InitiateMultipartUploadResult mockResult = new InitiateMultipartUploadResult();
            mockResult.setUploadId(uploadId);
            
            when(ossClient.initiateMultipartUpload(any(InitiateMultipartUploadRequest.class)))
                .thenReturn(mockResult);

            // 执行测试
            MultipartUploadInitResult result = ossService.initializeResumableUpload(initDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(uploadId, result.getUploadId());
            assertNotNull(result.getRelativePath());
            assertEquals("test-bucket", result.getBucketName());
            assertEquals(5 * 1024 * 1024L, result.getSuggestedPartSize());
            assertEquals(10000, result.getMaxPartCount());
            assertNotNull(result.getTimestamp());

            // 验证调用
            verify(ossClient).initiateMultipartUpload(any(InitiateMultipartUploadRequest.class));
        }
    }

    @Test
    @DisplayName("测试初始化断点续传上传失败")
    void testInitializeResumableUploadFailure() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置OSS配置
            when(ossConfig.getBucketName()).thenReturn("test-bucket");
            
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            ResumableUploadInitDTO initDTO = new ResumableUploadInitDTO();
            initDTO.setOriginalFileName("test.txt");
            initDTO.setFileSize(1024L);
            
            when(ossClient.initiateMultipartUpload(any(InitiateMultipartUploadRequest.class)))
                .thenThrow(new RuntimeException("OSS error"));

            // 执行测试并验证异常
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                ossService.initializeResumableUpload(initDTO);
            });

            assertTrue(exception.getMessage().contains("初始化断点续传上传失败"));
        }
    }

    @Test
    @DisplayName("测试生成分片上传预签名URL成功")
    void testGeneratePresignedPartUploadUrlSuccess() throws Exception {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置OSS配置和模拟会话
            when(ossConfig.getBucketName()).thenReturn("test-bucket");
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);

            // 先初始化一个上传会话
            ResumableUploadInitDTO initDTO = new ResumableUploadInitDTO();
            initDTO.setOriginalFileName("test.txt");
            initDTO.setFileSize(1024L);

            // 模拟OSS返回真实的uploadId
            String realOssUploadId = "0004B9894A22E5B1888A1E29F8066E07.test-upload-id-123";
            InitiateMultipartUploadResult mockInitResult = new InitiateMultipartUploadResult();
            mockInitResult.setUploadId(realOssUploadId);

            when(ossClient.initiateMultipartUpload(any(InitiateMultipartUploadRequest.class)))
                .thenReturn(mockInitResult);

            MultipartUploadInitResult initResult = ossService.initializeResumableUpload(initDTO);
            String sessionId = initResult.getUploadId(); // 这是内部会话ID

            // 测试生成预签名URL - 使用内部会话ID
            int partNumber = 1;
            long expiration = 3600;

            URL mockUrl = new URL("https://test-bucket.oss-cn-shanghai.aliyuncs.com/test.txt?partNumber=1&uploadId=" + realOssUploadId);

            when(ossClient.generatePresignedUrl(any(GeneratePresignedUrlRequest.class)))
                .thenReturn(mockUrl);

            // 执行测试 - 使用内部会话ID
            String result = ossService.generatePresignedPartUploadUrl(sessionId, partNumber, expiration);

            // 验证结果
            assertNotNull(result);
            assertEquals(mockUrl.toString(), result);

            // 验证调用 - 确保generatePresignedUrl被调用了
            verify(ossClient, atLeast(2)).generatePresignedUrl(any(GeneratePresignedUrlRequest.class));
        }
    }

    @Test
    @DisplayName("测试完成分片上传成功")
    void testCompleteMultipartUploadSuccess() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置OSS配置和模拟会话
            when(ossConfig.getBucketName()).thenReturn("test-bucket");
            OssConfig.Upload uploadConfig = new OssConfig.Upload();
            uploadConfig.setBaseUrl("https://test-bucket.oss-cn-shanghai.aliyuncs.com");
            when(ossConfig.getUpload()).thenReturn(uploadConfig);
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            // 先初始化一个上传会话
            ResumableUploadInitDTO initDTO = new ResumableUploadInitDTO();
            initDTO.setOriginalFileName("test.txt");
            initDTO.setFileSize(2048L);
            
            InitiateMultipartUploadResult mockInitResult = new InitiateMultipartUploadResult();
            mockInitResult.setUploadId("test-upload-id-123");
            
            when(ossClient.initiateMultipartUpload(any(InitiateMultipartUploadRequest.class)))
                .thenReturn(mockInitResult);
            
            ossService.initializeResumableUpload(initDTO);
            
            // 测试完成分片上传
            String uploadId = "test-upload-id-123";
            String eTag = "test-etag-123";
            
            List<PartETagInfo> partETags = Arrays.asList(
                new PartETagInfo(1, "etag1", 1024L),
                new PartETagInfo(2, "etag2", 1024L)
            );
            
            CompleteMultipartUploadResult mockResult = new CompleteMultipartUploadResult();
            mockResult.setETag(eTag);
            
            when(ossClient.completeMultipartUpload(any(CompleteMultipartUploadRequest.class)))
                .thenReturn(mockResult);

            // 执行测试
            MultipartUploadCompleteResult result = ossService.completeMultipartUpload(uploadId, partETags);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getRelativePath());
            assertEquals(eTag, result.getETag());
            assertEquals("test-bucket", result.getBucketName());
            assertEquals(2048L, result.getTotalSize());
            assertEquals(2, result.getTotalParts());
            assertEquals("tenant_1", result.getTenantCode());
            assertNotNull(result.getTimestamp());

            // 验证调用
            verify(ossClient).completeMultipartUpload(any(CompleteMultipartUploadRequest.class));
        }
    }

    @Test
    @DisplayName("测试取消分片上传成功")
    void testAbortMultipartUploadSuccess() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置OSS配置和模拟会话
            when(ossConfig.getBucketName()).thenReturn("test-bucket");
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            // 先初始化一个上传会话
            ResumableUploadInitDTO initDTO = new ResumableUploadInitDTO();
            initDTO.setOriginalFileName("test.txt");
            initDTO.setFileSize(1024L);
            
            InitiateMultipartUploadResult mockInitResult = new InitiateMultipartUploadResult();
            mockInitResult.setUploadId("test-upload-id-123");
            
            when(ossClient.initiateMultipartUpload(any(InitiateMultipartUploadRequest.class)))
                .thenReturn(mockInitResult);
            
            ossService.initializeResumableUpload(initDTO);
            
            // 测试取消分片上传
            String uploadId = "test-upload-id-123";

            // 执行测试（不应该抛出异常）
            assertDoesNotThrow(() -> {
                ossService.abortMultipartUpload(uploadId);
            });

            // 验证调用
            verify(ossClient).abortMultipartUpload(any(AbortMultipartUploadRequest.class));
        }
    }

    @Test
    @DisplayName("测试参数验证失败")
    void testValidationFailure() {
        ResumableUploadInitDTO initDTO = new ResumableUploadInitDTO();
        initDTO.setOriginalFileName(""); // 空文件名
        initDTO.setFileSize(1024L);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ossService.initializeResumableUpload(initDTO);
        });

        assertTrue(exception.getMessage().contains("文件名不能为空"));
    }

    @Test
    @DisplayName("测试不支持的文件类型")
    void testUnsupportedFileType() {
        ResumableUploadInitDTO initDTO = new ResumableUploadInitDTO();
        initDTO.setOriginalFileName("test.exe"); // 不支持的文件类型
        initDTO.setFileSize(1024L);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ossService.initializeResumableUpload(initDTO);
        });

        assertTrue(exception.getMessage().contains("不支持的文件类型"));
    }
}
