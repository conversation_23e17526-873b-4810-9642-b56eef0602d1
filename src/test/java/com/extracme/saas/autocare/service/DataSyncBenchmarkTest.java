package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.VehicleInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.vo.DataSyncResultVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 数据同步基准测试
 * 用于验证并行处理优化的性能提升效果
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class DataSyncBenchmarkTest {

    @Autowired
    private DataSyncService dataSyncService;

    private String testSyncKey = "benchmark_sync_key";
    private String testSourceIp = "127.0.0.1";

    /**
     * 基准测试：不同数据量的同步性能
     */
    @Test
    void benchmarkDifferentDataSizes() {
        log.info("开始基准测试：不同数据量的同步性能");
        
        int[] dataSizes = {50, 100, 200, 500, 1000};
        
        for (int dataSize : dataSizes) {
            log.info("测试数据量：{}", dataSize);
            
            // 创建测试数据
            List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> batchData = createTestData(dataSize);
            
            VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
            requestDTO.setSyncKey(testSyncKey);
            requestDTO.setBatchData(batchData);

            // 执行同步并测量性能
            long startTime = System.currentTimeMillis();
            DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, testSourceIp);
            long endTime = System.currentTimeMillis();
            
            long totalDuration = endTime - startTime;
            double throughput = (double) dataSize / totalDuration * 1000; // 每秒处理的记录数
            
            log.info("数据量：{}，总耗时：{}ms，同步耗时：{}ms，吞吐量：{:.2f} records/sec，成功率：{:.2f}%",
                    dataSize, 
                    totalDuration,
                    result.getSyncDuration(),
                    throughput,
                    (double) result.getSuccessCount() / result.getTotalCount() * 100);
            
            // 验证结果
            assert result.getTotalCount() == dataSize;
            assert result.getSuccessCount() >= 0;
            assert result.getSyncDuration() > 0;
        }
    }

    /**
     * 并发压力测试
     */
    @Test
    void stressTestConcurrentSync() {
        log.info("开始并发压力测试");
        
        int batchSize = 200;
        int concurrentBatches = 3;
        
        List<Thread> threads = new ArrayList<>();
        List<Long> durations = new ArrayList<>();
        
        for (int i = 0; i < concurrentBatches; i++) {
            final int batchIndex = i;
            Thread thread = new Thread(() -> {
                try {
                    List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> batchData = 
                        createTestData(batchSize, batchIndex * 1000); // 使用不同的ID范围避免冲突
                    
                    VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
                    requestDTO.setSyncKey(testSyncKey + "_" + batchIndex);
                    requestDTO.setBatchData(batchData);

                    long startTime = System.currentTimeMillis();
                    DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, testSourceIp);
                    long endTime = System.currentTimeMillis();
                    
                    synchronized (durations) {
                        durations.add(endTime - startTime);
                    }
                    
                    log.info("批次{}完成，耗时：{}ms，成功：{}，失败：{}", 
                            batchIndex, endTime - startTime, result.getSuccessCount(), result.getFailedCount());
                    
                } catch (Exception e) {
                    log.error("批次{}执行失败：{}", batchIndex, e.getMessage(), e);
                }
            });
            threads.add(thread);
        }
        
        // 启动所有线程
        long overallStartTime = System.currentTimeMillis();
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("线程等待被中断", e);
            }
        }
        long overallEndTime = System.currentTimeMillis();
        
        // 统计结果
        long totalOverallDuration = overallEndTime - overallStartTime;
        double avgDuration = durations.stream().mapToLong(Long::longValue).average().orElse(0.0);
        long maxDuration = durations.stream().mapToLong(Long::longValue).max().orElse(0L);
        long minDuration = durations.stream().mapToLong(Long::longValue).min().orElse(0L);
        
        log.info("并发压力测试完成：");
        log.info("  并发批次数：{}", concurrentBatches);
        log.info("  每批次数据量：{}", batchSize);
        log.info("  总体耗时：{}ms", totalOverallDuration);
        log.info("  平均批次耗时：{:.2f}ms", avgDuration);
        log.info("  最大批次耗时：{}ms", maxDuration);
        log.info("  最小批次耗时：{}ms", minDuration);
        log.info("  总吞吐量：{:.2f} records/sec", 
                (double) (concurrentBatches * batchSize) / totalOverallDuration * 1000);
    }

    /**
     * 内存使用测试
     */
    @Test
    void memoryUsageTest() {
        log.info("开始内存使用测试");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 执行GC并获取初始内存状态
        System.gc();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        log.info("初始内存使用：{} MB", initialMemory / 1024 / 1024);
        
        // 执行大批量同步
        int dataSize = 1000;
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> batchData = createTestData(dataSize);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey(testSyncKey);
        requestDTO.setBatchData(batchData);

        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, testSourceIp);
        
        // 获取同步后的内存状态
        long afterSyncMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = afterSyncMemory - initialMemory;
        
        log.info("同步后内存使用：{} MB", afterSyncMemory / 1024 / 1024);
        log.info("内存增长：{} MB", memoryIncrease / 1024 / 1024);
        log.info("平均每条记录内存消耗：{} KB", memoryIncrease / dataSize / 1024);
        
        // 验证结果
        assert result.getTotalCount() == dataSize;
        assert memoryIncrease >= 0; // 内存增长应该是合理的
        
        log.info("内存使用测试完成，成功：{}，失败：{}", result.getSuccessCount(), result.getFailedCount());
    }

    /**
     * 创建测试数据
     */
    private List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> createTestData(int count) {
        return createTestData(count, 0);
    }

    /**
     * 创建测试数据（带ID偏移）
     */
    private List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> createTestData(int count, int idOffset) {
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> batchData = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO data = 
                new VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO();
            data.setId((long) (i + idOffset));
            data.setVin("BENCH_VIN_" + (i + idOffset) + "_" + ThreadLocalRandom.current().nextInt(1000, 9999));
            data.setVehicleNo("基准测试" + String.format("%06d", i + idOffset));
            data.setVehicleModelId((long) ((i + idOffset) % 10 + 1));
            batchData.add(data);
        }
        
        return batchData;
    }
}
