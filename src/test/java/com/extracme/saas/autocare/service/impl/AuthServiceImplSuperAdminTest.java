package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.SmsService;
import com.extracme.saas.autocare.util.JwtUtil;
import com.extracme.saas.autocare.util.OrgHierarchyUtils;

/**
 * AuthServiceImpl 超级管理员权限测试类
 */
@ExtendWith(MockitoExtension.class)
class AuthServiceImplSuperAdminTest {

    @Mock
    private SmsService smsService;
    
    @Mock
    private TableUserService userService;
    
    @Mock
    private TableRoleService roleService;
    
    @Mock
    private TablePermissionService permissionService;
    
    @Mock
    private TableTenantService tenantService;
    
    @Mock
    private TableUserOrgService userOrgService;
    
    @Mock
    private OrgHierarchyUtils orgHierarchyUtils;
    
    @Mock
    private JwtUtil jwtUtil;

    @InjectMocks
    private AuthServiceImpl authService;

    private SysUser superAdminUser;
    private SysUser regularUser;
    private List<SysPermission> allPermissions;

    @BeforeEach
    void setUp() {
        // 设置超级管理员用户
        superAdminUser = new SysUser();
        superAdminUser.setId(1L);
        superAdminUser.setUsername("superadmin");
        superAdminUser.setAccountType(0); // 超级管理员
        superAdminUser.setTenantId(1L);
        superAdminUser.setStatus(1);

        // 设置普通用户
        regularUser = new SysUser();
        regularUser.setId(2L);
        regularUser.setUsername("regularuser");
        regularUser.setAccountType(1); // 运营人员
        regularUser.setTenantId(1L);
        regularUser.setStatus(1);

        // 设置系统中的所有权限
        SysPermission permission1 = new SysPermission();
        permission1.setId(1L);
        permission1.setPermissionCode("user:read");
        permission1.setPermissionName("用户查看");

        SysPermission permission2 = new SysPermission();
        permission2.setId(2L);
        permission2.setPermissionCode("user:write");
        permission2.setPermissionName("用户编辑");

        SysPermission permission3 = new SysPermission();
        permission3.setId(3L);
        permission3.setPermissionCode("admin:system");
        permission3.setPermissionName("系统管理");

        allPermissions = Arrays.asList(permission1, permission2, permission3);
    }

    @Test
    @DisplayName("超级管理员应该获得所有系统权限")
    void getUserPermissions_SuperAdmin_ShouldReturnAllPermissions() {
        // Given
        String token = "valid-token";
        when(jwtUtil.getUserIdFromToken(token)).thenReturn("1");
        when(userService.selectById(1L)).thenReturn(superAdminUser);
        when(permissionService.findAll()).thenReturn(allPermissions);

        // When
        List<SysPermission> result = authService.getUserPermissions(token);

        // Then
        assertEquals(3, result.size());
        assertEquals(allPermissions, result);
        verify(permissionService).findAll();
        verify(permissionService, never()).findByUserId(any());
    }

    @Test
    @DisplayName("普通用户应该通过角色关联获得权限")
    void getUserPermissions_RegularUser_ShouldReturnRoleBasedPermissions() {
        // Given
        String token = "valid-token";
        List<SysPermission> userPermissions = Arrays.asList(allPermissions.get(0)); // 只有第一个权限
        
        when(jwtUtil.getUserIdFromToken(token)).thenReturn("2");
        when(userService.selectById(2L)).thenReturn(regularUser);
        when(permissionService.findByUserId(2L)).thenReturn(userPermissions);

        // When
        List<SysPermission> result = authService.getUserPermissions(token);

        // Then
        assertEquals(1, result.size());
        assertEquals("user:read", result.get(0).getPermissionCode());
        verify(permissionService).findByUserId(2L);
        verify(permissionService, never()).findAll();
    }

    @Test
    @DisplayName("超级管理员对任何权限检查都应该返回true")
    void hasPermission_SuperAdmin_ShouldAlwaysReturnTrue() {
        // Given
        String token = "valid-token";
        when(jwtUtil.validateToken(token)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(token)).thenReturn("1");
        when(userService.selectById(1L)).thenReturn(superAdminUser);

        // When & Then
        assertTrue(authService.hasPermission(token, "user:read"));
        assertTrue(authService.hasPermission(token, "user:write"));
        assertTrue(authService.hasPermission(token, "admin:system"));
        assertTrue(authService.hasPermission(token, "any:permission"));

        verify(permissionService, never()).hasPermission(any(), any());
    }

    @Test
    @DisplayName("普通用户权限检查应该通过角色关联验证")
    void hasPermission_RegularUser_ShouldCheckRoleBasedPermissions() {
        // Given
        String token = "valid-token";
        when(jwtUtil.validateToken(token)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(token)).thenReturn("2");
        when(userService.selectById(2L)).thenReturn(regularUser);
        when(permissionService.hasPermission(2L, "user:read")).thenReturn(true);
        when(permissionService.hasPermission(2L, "admin:system")).thenReturn(false);

        // When & Then
        assertTrue(authService.hasPermission(token, "user:read"));
        assertFalse(authService.hasPermission(token, "admin:system"));

        verify(permissionService).hasPermission(2L, "user:read");
        verify(permissionService).hasPermission(2L, "admin:system");
    }

    @Test
    @DisplayName("用户不存在时权限检查应该返回false")
    void hasPermission_UserNotFound_ShouldReturnFalse() {
        // Given
        String token = "valid-token";
        when(jwtUtil.validateToken(token)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(token)).thenReturn("999");
        when(userService.selectById(999L)).thenReturn(null);

        // When & Then
        assertFalse(authService.hasPermission(token, "user:read"));
        verify(permissionService, never()).hasPermission(any(), any());
    }

    @Test
    @DisplayName("无效token的权限检查应该返回false")
    void hasPermission_InvalidToken_ShouldReturnFalse() {
        // Given
        String token = "invalid-token";
        when(jwtUtil.validateToken(token)).thenReturn(false);

        // When & Then
        assertFalse(authService.hasPermission(token, "user:read"));
        verify(userService, never()).selectById(any());
        verify(permissionService, never()).hasPermission(any(), any());
    }

    @Test
    @DisplayName("accountType为null的用户应该按普通用户处理")
    void hasPermission_NullAccountType_ShouldTreatAsRegularUser() {
        // Given
        String token = "valid-token";
        SysUser userWithNullAccountType = new SysUser();
        userWithNullAccountType.setId(3L);
        userWithNullAccountType.setAccountType(null);
        
        when(jwtUtil.validateToken(token)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(token)).thenReturn("3");
        when(userService.selectById(3L)).thenReturn(userWithNullAccountType);
        when(permissionService.hasPermission(3L, "user:read")).thenReturn(false);

        // When & Then
        assertFalse(authService.hasPermission(token, "user:read"));
        verify(permissionService).hasPermission(3L, "user:read");
    }
}
