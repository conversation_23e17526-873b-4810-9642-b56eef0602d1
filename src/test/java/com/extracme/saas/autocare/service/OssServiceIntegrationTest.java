package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.MockedStatic;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.extracme.saas.autocare.config.OssConfig;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.MultipartUploadCompleteResult;
import com.extracme.saas.autocare.model.dto.MultipartUploadInitResult;
import com.extracme.saas.autocare.model.dto.PartETagInfo;
import com.extracme.saas.autocare.model.dto.ResumableUploadInitDTO;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.service.impl.OssServiceImpl;
import com.extracme.saas.autocare.service.impl.StsCredentialServiceImpl;
import com.extracme.saas.autocare.util.RedisUtils;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * OSS断点续传集成测试类
 * 
 * 注意：此测试需要真实的OSS配置，请在运行前确保：
 * 1. 配置正确的OSS Access Key、Secret Key、Bucket、Endpoint
 * 2. OSS Bucket存在且有读写权限
 * 3. 网络连接正常
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("OSS断点续传集成测试")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class OssServiceIntegrationTest {

    // ==================== 测试配置 ====================
    
    // OSS配置 - 请根据实际情况修改
    private static final String ACCESS_KEY_ID = "LTAI5tJumdVNXHPLszk49yAk";
    private static final String ACCESS_KEY_SECRET = "******************************";
    private static final String ENDPOINT = "https://oss-cn-shanghai.aliyuncs.com";
    private static final String BUCKET_NAME = "evcard";
    private static final String BASE_URL = "http://evcard.oss-cn-shanghai.aliyuncs.com";
    
    // 测试文件配置
    private static final int TEST_FILE_SIZE = 2 * 1024 * 1024; // 2MB
    private static final int CHUNK_SIZE = 256 * 1024; // 256KB
    private static final String TEST_FILE_NAME = "integration-test-file.txt";
    private static final String TEST_CATEGORY = "integration-test";
    
    // 测试实例
    private OssServiceImpl ossService;
    private OSS ossClient;
    private OssConfig ossConfig;
    private StsCredentialService stsCredentialService;
    private RedisUtils redisUtils;
    
    // 测试数据
    private byte[] testFileContent;
    private String testFileMd5;
    private List<String> uploadedFiles = new ArrayList<>();
    
    // Mock用户信息
    private SysUser mockUser;
    private LoginUser mockLoginUser;

    @BeforeAll
    static void beforeAll() {
        log.info("=== OSS断点续传集成测试开始 ===");
        log.warn("注意：此测试需要真实的OSS配置，请确保配置正确！");
    }

    @BeforeEach
    void setUp() throws Exception {
        log.info("--- 测试准备开始 ---");

        // 检查OSS配置
        if ("your-access-key-id".equals(ACCESS_KEY_ID)) {
            throw new IllegalStateException("请先配置真实的OSS Access Key ID");
        }

        // 初始化OSS配置
        setupOssConfig();

        // 初始化OSS客户端
        setupOssClient();

        // 验证OSS权限
        validateOssPermissions();

        // 初始化OSS服务
        setupOssService();

        // 生成测试文件内容
        generateTestFileContent();

        // 设置Mock用户
        setupMockUser();

        log.info("测试准备完成");
    }

    @AfterEach
    void tearDown() {
        log.info("--- 测试清理开始 ---");
        
        // 清理OSS上的测试文件
        cleanupOssFiles();
        
        // 关闭OSS客户端
        if (ossClient != null) {
            ossClient.shutdown();
        }
        
        log.info("测试清理完成");
    }

    @AfterAll
    static void afterAll() {
        log.info("=== OSS断点续传集成测试结束 ===");
    }

    // ==================== 主要测试用例 ====================

    @Test
    @Order(1)
    @DisplayName("完整的断点续传流程测试")
    void testCompleteResumableUploadFlow() throws Exception {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            log.info("🚀 开始完整的断点续传流程测试");
            
            // 步骤1：初始化断点续传上传
            log.info("📋 步骤1：初始化断点续传上传");
            MultipartUploadInitResult initResult = initializeUpload();
            assertNotNull(initResult, "初始化结果不能为空");
            assertNotNull(initResult.getUploadId(), "上传ID不能为空");
            assertFalse(initResult.getUploadId().isEmpty(), "上传ID不能为空字符串");
            log.info("✅ 初始化成功，uploadId: {}", initResult.getUploadId());
            
            // 步骤2：计算分片信息
            log.info("📋 步骤2：计算分片信息");
            List<ChunkInfo> chunks = calculateChunks();
            log.info("✅ 分片计算完成，总分片数: {}", chunks.size());
            
            // 步骤3：上传所有分片
            log.info("📋 步骤3：上传所有分片");
            List<PartETagInfo> partETags = uploadAllChunks(initResult.getUploadId(), chunks);
            assertEquals(chunks.size(), partETags.size(), "分片ETag数量应该与分片数量一致");
            log.info("✅ 所有分片上传完成，分片数: {}", partETags.size());
            
            // 步骤4：完成分片上传合并
            log.info("📋 步骤4：完成分片上传合并");
            MultipartUploadCompleteResult completeResult = completeUpload(initResult.getUploadId(), partETags);
            assertNotNull(completeResult, "完成结果不能为空");
            assertNotNull(completeResult.getFullUrl(), "文件URL不能为空");
            log.info("✅ 文件合并完成，URL: {}", completeResult.getFullUrl());
            
             // 步骤5：验证上传的文件
             log.info("📋 步骤5：验证上传的文件");
             verifyUploadedFile(completeResult.getFullUrl());
             log.info("✅ 文件验证成功");
             
            // 记录上传的文件用于清理
            uploadedFiles.add(completeResult.getRelativePath());
            
            log.info("🎉 完整的断点续传流程测试成功！");
        }
    }

    @Test
    @Order(2)
    @DisplayName("测试无效uploadId的错误处理")
    void testInvalidUploadIdError() throws Exception {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            log.info("🧪 测试无效uploadId的错误处理");
            
            String invalidUploadId = "invalid-upload-id-123";
            
            // 测试生成预签名URL
            BusinessException exception1 = assertThrows(BusinessException.class, () -> {
                ossService.generatePresignedPartUploadUrl(invalidUploadId, 1, 3600);
            });
            assertTrue(exception1.getMessage().contains("上传会话不存在"), 
                "应该提示上传会话不存在");
            log.info("✅ 无效uploadId错误处理正确: {}", exception1.getMessage());
            
            // 测试完成上传
            List<PartETagInfo> partETags = Arrays.asList(
                new PartETagInfo(1, "test-etag-1", 1024L)
            );
            BusinessException exception2 = assertThrows(BusinessException.class, () -> {
                ossService.completeMultipartUpload(invalidUploadId, partETags);
            });
            assertTrue(exception2.getMessage().contains("上传会话不存在"), 
                "应该提示上传会话不存在");
            log.info("✅ 无效uploadId完成上传错误处理正确: {}", exception2.getMessage());
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试分片编号超出范围的错误处理")
    void testInvalidPartNumberError() throws Exception {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            log.info("🧪 测试分片编号超出范围的错误处理");
            
            // 先初始化一个正常的上传
            MultipartUploadInitResult initResult = initializeUpload();
            
            // 测试分片编号为0
            BusinessException exception1 = assertThrows(BusinessException.class, () -> {
                ossService.generatePresignedPartUploadUrl(initResult.getUploadId(), 0, 3600);
            });
            assertTrue(exception1.getMessage().contains("分片编号必须在1-10000之间"), 
                "应该提示分片编号范围错误");
            log.info("✅ 分片编号为0的错误处理正确: {}", exception1.getMessage());
            
            // 测试分片编号超过10000
            BusinessException exception2 = assertThrows(BusinessException.class, () -> {
                ossService.generatePresignedPartUploadUrl(initResult.getUploadId(), 10001, 3600);
            });
            assertTrue(exception2.getMessage().contains("分片编号必须在1-10000之间"), 
                "应该提示分片编号范围错误");
            log.info("✅ 分片编号超过10000的错误处理正确: {}", exception2.getMessage());
            
            // 清理
            try {
                ossService.abortMultipartUpload(initResult.getUploadId());
            } catch (Exception e) {
                log.warn("清理上传会话失败: {}", e.getMessage());
            }
        }
    }

    @Test
    @Order(4)
    @DisplayName("测试取消分片上传功能")
    void testAbortMultipartUpload() throws Exception {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            log.info("🧪 测试取消分片上传功能");
            
            // 初始化上传
            MultipartUploadInitResult initResult = initializeUpload();
            log.info("初始化上传成功，uploadId: {}", initResult.getUploadId());
            
            // 生成一个预签名URL确保上传会话存在
            String presignedUrl = ossService.generatePresignedPartUploadUrl(
                initResult.getUploadId(), 1, 3600);
            assertNotNull(presignedUrl, "预签名URL不能为空");
            log.info("生成预签名URL成功");
            
            // 取消上传
            assertDoesNotThrow(() -> {
                ossService.abortMultipartUpload(initResult.getUploadId());
            });
            log.info("✅ 取消上传成功");
            
            // 验证上传会话已被清理
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                ossService.generatePresignedPartUploadUrl(initResult.getUploadId(), 1, 3600);
            });
            assertTrue(exception.getMessage().contains("上传会话不存在"), 
                "取消后应该无法再生成预签名URL");
            log.info("✅ 上传会话清理验证成功: {}", exception.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 设置OSS配置
     */
    private void setupOssConfig() {
        ossConfig = new OssConfig();
        ossConfig.setAccessKeyId(ACCESS_KEY_ID);
        ossConfig.setAccessKeySecret(ACCESS_KEY_SECRET);
        ossConfig.setEndpoint(ENDPOINT);
        ossConfig.setBucketName(BUCKET_NAME);
        
        OssConfig.Upload uploadConfig = new OssConfig.Upload();
        uploadConfig.setBaseUrl(BASE_URL);
        ossConfig.setUpload(uploadConfig);

        // 禁用STS用于集成测试
        ossConfig.setStsEnabled(false);

        log.info("OSS配置初始化完成");
    }

    /**
     * 设置OSS客户端
     */
    private void setupOssClient() {
        ossClient = new OSSClientBuilder().build(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
        
        // 测试连接
        boolean bucketExists = ossClient.doesBucketExist(BUCKET_NAME);
        if (!bucketExists) {
            throw new IllegalStateException("OSS Bucket不存在: " + BUCKET_NAME);
        }
        
        log.info("OSS客户端初始化完成，Bucket验证成功");
    }

    /**
     * 验证OSS权限
     */
    private void validateOssPermissions() {
        log.info("验证OSS权限...");

        try {
            // 测试基本的Bucket访问权限
            boolean bucketExists = ossClient.doesBucketExist(BUCKET_NAME);
            if (!bucketExists) {
                throw new IllegalStateException("OSS Bucket不存在或无访问权限: " + BUCKET_NAME);
            }
            log.info("✅ Bucket访问权限验证通过");

            // 测试文件上传权限（上传一个小测试文件）
            String testObjectKey = "test-permissions-" + System.currentTimeMillis() + ".txt";
            String testContent = "OSS权限测试文件";

            try {
                // 上传测试文件
                ossClient.putObject(BUCKET_NAME, testObjectKey, new ByteArrayInputStream(testContent.getBytes()));
                log.info("✅ 文件上传权限验证通过");

                // 测试文件下载权限
                try {
                    ossClient.getObject(BUCKET_NAME, testObjectKey);
                    log.info("✅ 文件下载权限验证通过");
                } catch (Exception e) {
                    log.warn("⚠️ 文件下载权限验证失败: {}", e.getMessage());
                }

                // 测试文件删除权限
                try {
                    ossClient.deleteObject(BUCKET_NAME, testObjectKey);
                    log.info("✅ 文件删除权限验证通过");
                } catch (Exception e) {
                    log.warn("⚠️ 文件删除权限验证失败: {}", e.getMessage());
                    log.warn("注意：测试文件可能需要手动清理: {}", testObjectKey);
                }

            } catch (Exception e) {
                throw new IllegalStateException("OSS文件上传权限验证失败: " + e.getMessage(), e);
            }

            log.info("OSS权限验证完成");

        } catch (Exception e) {
            log.error("OSS权限验证失败", e);
            log.error("请检查以下配置:");
            log.error("  1. Access Key ID: {}", ACCESS_KEY_ID);
            log.error("  2. Endpoint: {}", ENDPOINT);
            log.error("  3. Bucket Name: {}", BUCKET_NAME);
            log.error("  4. 确保Access Key具有以下权限:");
            log.error("     - oss:PutObject (上传文件)");
            log.error("     - oss:GetObject (下载文件)");
            log.error("     - oss:DeleteObject (删除文件)");
            log.error("     - oss:InitiateMultipartUpload (初始化分片上传)");
            log.error("     - oss:UploadPart (上传分片)");
            log.error("     - oss:CompleteMultipartUpload (完成分片上传)");
            log.error("     - oss:AbortMultipartUpload (取消分片上传)");
            throw e;
        }
    }

    /**
     * 设置OSS服务
     */
    private void setupOssService() {
        // 创建STS凭证服务（用于测试，禁用STS）
        stsCredentialService = new StsCredentialServiceImpl(ossConfig);

        // 创建Mock RedisUtils（用于测试）
        redisUtils = mock(RedisUtils.class);

        // 模拟Redis操作成功
        when(redisUtils.set(org.mockito.ArgumentMatchers.anyString(),
                           org.mockito.ArgumentMatchers.any(),
                           org.mockito.ArgumentMatchers.anyLong())).thenReturn(true);
        when(redisUtils.hasKey(org.mockito.ArgumentMatchers.anyString())).thenReturn(true);

        // 创建OSS服务
        ossService = new OssServiceImpl(ossConfig, stsCredentialService, redisUtils);
        log.info("OSS服务初始化完成");
    }

    /**
     * 生成测试文件内容
     */
    private void generateTestFileContent() throws Exception {
        log.info("生成测试文件内容，大小: {} bytes", TEST_FILE_SIZE);
        
        testFileContent = new byte[TEST_FILE_SIZE];
        
        // 生成有意义的测试内容
        String pattern = "这是OSS断点续传集成测试的测试文件内容。";
        byte[] patternBytes = pattern.getBytes("UTF-8");
        
        for (int i = 0; i < TEST_FILE_SIZE; i++) {
            testFileContent[i] = patternBytes[i % patternBytes.length];
        }
        
        // 计算MD5
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] md5Bytes = md5.digest(testFileContent);
        StringBuilder sb = new StringBuilder();
        for (byte b : md5Bytes) {
            sb.append(String.format("%02x", b));
        }
        testFileMd5 = sb.toString();
        
        log.info("测试文件内容生成完成，MD5: {}", testFileMd5);
    }

    /**
     * 设置Mock用户
     */
    private void setupMockUser() {
        mockUser = new SysUser();
        mockUser.setId(1L);
        mockUser.setUsername("test-user");
        mockUser.setNickname("测试用户");
        mockUser.setTenantId(1L);

        mockLoginUser = new LoginUser();
        mockLoginUser.setUser(mockUser);

        log.info("Mock用户设置完成");
    }

    /**
     * 初始化断点续传上传
     */
    private MultipartUploadInitResult initializeUpload() throws Exception {
        ResumableUploadInitDTO initDTO = new ResumableUploadInitDTO();
        initDTO.setOriginalFileName(TEST_FILE_NAME);
        initDTO.setFileSize((long) TEST_FILE_SIZE);
        initDTO.setContentType("text/plain");
        initDTO.setCategory(TEST_CATEGORY);
        initDTO.setMd5(testFileMd5);
        initDTO.setChunkSize((long) CHUNK_SIZE);

        return ossService.initializeResumableUpload(initDTO);
    }

    /**
     * 计算分片信息
     */
    private List<ChunkInfo> calculateChunks() {
        List<ChunkInfo> chunks = new ArrayList<>();
        int totalChunks = (int) Math.ceil((double) TEST_FILE_SIZE / CHUNK_SIZE);

        for (int i = 0; i < totalChunks; i++) {
            int start = i * CHUNK_SIZE;
            int end = Math.min(start + CHUNK_SIZE, TEST_FILE_SIZE);
            int size = end - start;

            ChunkInfo chunk = new ChunkInfo();
            chunk.partNumber = i + 1;
            chunk.size = size;
            chunk.data = Arrays.copyOfRange(testFileContent, start, end);

            chunks.add(chunk);
        }

        log.info("分片计算完成：总大小={}, 分片大小={}, 分片数={}",
            TEST_FILE_SIZE, CHUNK_SIZE, chunks.size());

        return chunks;
    }

    /**
     * 上传所有分片
     */
    private List<PartETagInfo> uploadAllChunks(String uploadId, List<ChunkInfo> chunks) throws Exception {
        List<PartETagInfo> partETags = new ArrayList<>();

        for (ChunkInfo chunk : chunks) {
            log.info("上传分片 {}/{}: 大小={} bytes",
                chunk.partNumber, chunks.size(), chunk.size);

            // 生成预签名URL
            String presignedUrl = ossService.generatePresignedPartUploadUrl(
                uploadId, chunk.partNumber, 3600);
            assertNotNull(presignedUrl, "预签名URL不能为空");
            assertTrue(presignedUrl.startsWith("http"), "预签名URL格式应该正确");

            // 使用预签名URL上传分片
            String etag = uploadChunkToOss(presignedUrl, chunk.data);
            assertNotNull(etag, "ETag不能为空");
            assertFalse(etag.isEmpty(), "ETag不能为空字符串");

            // 记录分片信息
            PartETagInfo partETag = new PartETagInfo();
            partETag.setPartNumber(chunk.partNumber);
            partETag.setETag(etag);
            partETag.setPartSize((long) chunk.size);
            partETags.add(partETag);

            log.info("分片 {} 上传成功，ETag: {}", chunk.partNumber, etag);
        }

        return partETags;
    }

    /**
     * 使用预签名URL上传分片到OSS
     */
    private String uploadChunkToOss(String presignedUrl, byte[] data) throws Exception {
        log.debug("开始上传分片到OSS: url={}, dataSize={}", presignedUrl, data.length);

        URL url = new URL(presignedUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        try {
            // 设置请求方法和属性
            connection.setRequestMethod("PUT");
            connection.setDoOutput(true);

            connection.setRequestProperty("Content-Type", "text/plain");

            // 记录请求详情
            log.debug("HTTP请求详情: method=PUT", data.length);

            // 上传数据
            try (OutputStream outputStream = connection.getOutputStream()) {
                outputStream.write(data);
                outputStream.flush();
            }

            // 检查响应
            int responseCode = connection.getResponseCode();
            String responseMessage = connection.getResponseMessage();

            log.debug("HTTP响应: code={}, message={}", responseCode, responseMessage);

            if (responseCode != 200) {
                // 读取错误响应内容
                String errorResponse = "";
                try (InputStream errorStream = connection.getErrorStream()) {
                    if (errorStream != null) {
                        try (BufferedReader reader = new BufferedReader(new InputStreamReader(errorStream))) {
                            StringBuilder sb = new StringBuilder();
                            String line;
                            while ((line = reader.readLine()) != null) {
                                sb.append(line).append("\n");
                            }
                            errorResponse = sb.toString();
                        }
                    }
                } catch (Exception e) {
                    log.warn("读取错误响应失败: {}", e.getMessage());
                }

                // 记录详细的错误信息
                log.error("上传分片失败详情:");
                log.error("  - 响应码: {}", responseCode);
                log.error("  - 响应消息: {}", responseMessage);
                log.error("  - 错误响应: {}", errorResponse);
                log.error("  - 预签名URL: {}", presignedUrl);
                log.error("  - 数据大小: {} bytes", data.length);

                // 分析403错误的具体原因
                if (responseCode == 403) {
                    analyzeHttp403Error(presignedUrl, errorResponse);
                }

                throw new RuntimeException(String.format(
                    "上传分片失败，响应码: %d, 响应消息: %s, 错误详情: %s",
                    responseCode, responseMessage, errorResponse));
            }

            // 获取ETag
            String etag = connection.getHeaderField("ETag");
            if (etag != null && etag.startsWith("\"") && etag.endsWith("\"")) {
                etag = etag.substring(1, etag.length() - 1);
            }

            log.debug("分片上传成功: etag={}", etag);
            return etag;
        } finally {
            connection.disconnect();
        }
    }

    /**
     * 完成分片上传合并
     */
    private MultipartUploadCompleteResult completeUpload(String uploadId, List<PartETagInfo> partETags) throws Exception {
        return ossService.completeMultipartUpload(uploadId, partETags);
    }

    /**
     * 验证上传的文件
     */
    private void verifyUploadedFile(String fileUrl) throws Exception {
        log.info("验证上传的文件: {}", fileUrl);

        // 下载文件
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        try {
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();

            if (responseCode != 200) {
                throw new RuntimeException("下载文件失败，响应码: " + responseCode);
            }

            // 读取文件内容
            byte[] downloadedContent;
            try (InputStream inputStream = connection.getInputStream();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                downloadedContent = outputStream.toByteArray();
            }

            // 验证文件大小
            assertEquals(TEST_FILE_SIZE, downloadedContent.length,
                "下载的文件大小应该与原始文件一致");

            // 验证文件内容
            assertArrayEquals(testFileContent, downloadedContent,
                "下载的文件内容应该与原始文件一致");

            // 验证MD5
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] downloadedMd5Bytes = md5.digest(downloadedContent);
            StringBuilder sb = new StringBuilder();
            for (byte b : downloadedMd5Bytes) {
                sb.append(String.format("%02x", b));
            }
            String downloadedMd5 = sb.toString();

            assertEquals(testFileMd5, downloadedMd5, "下载文件的MD5应该与原始文件一致");

            log.info("文件验证成功：大小={}, MD5={}", downloadedContent.length, downloadedMd5);

        } finally {
            connection.disconnect();
        }
    }

    /**
     * 分析HTTP 403错误的具体原因
     */
    private void analyzeHttp403Error(String presignedUrl, String errorResponse) {
        log.error("=== HTTP 403错误详细分析 ===");

        // 分析预签名URL
        try {
            URL url = new URL(presignedUrl);
            log.error("预签名URL分析:");
            log.error("  - Protocol: {}", url.getProtocol());
            log.error("  - Host: {}", url.getHost());
            log.error("  - Path: {}", url.getPath());
            log.error("  - Query: {}", url.getQuery());

            // 检查查询参数
            if (url.getQuery() != null) {
                String[] params = url.getQuery().split("&");
                log.error("  - 查询参数:");
                for (String param : params) {
                    String[] kv = param.split("=", 2);
                    if (kv.length == 2) {
                        log.error("    * {}: {}", kv[0], kv[1]);

                        // 特别检查uploadId格式
                        if ("uploadId".equals(kv[0])) {
                            String uploadId = kv[1];
                            if (uploadId.startsWith("upload-")) {
                                log.error("    ❌ 检测到错误：使用了内部会话ID而非OSS真实Upload ID！");
                                log.error("    💡 解决方案：确保使用session.getOssUploadId()而不是会话ID");
                            } else {
                                log.error("    ✅ Upload ID格式看起来正确");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("分析预签名URL失败: {}", e.getMessage());
        }

        // 分析错误响应
        if (errorResponse != null && !errorResponse.trim().isEmpty()) {
            log.error("OSS错误响应: {}", errorResponse);

            if (errorResponse.contains("SignatureDoesNotMatch")) {
                log.error("💡 这是签名不匹配错误，可能原因:");
                log.error("  1. Upload ID不正确（最常见）");
                log.error("  2. Access Key或Secret Key错误");
                log.error("  3. 系统时间不同步");
                log.error("  4. 请求参数格式错误");
            } else if (errorResponse.contains("AccessDenied")) {
                log.error("💡 这是访问拒绝错误，可能原因:");
                log.error("  1. Access Key权限不足");
                log.error("  2. Bucket策略限制");
                log.error("  3. IP白名单限制");
            } else if (errorResponse.contains("NoSuchUpload")) {
                log.error("💡 这是Upload不存在错误，可能原因:");
                log.error("  1. Upload ID已过期或被取消");
                log.error("  2. Upload ID格式错误");
                log.error("  3. Bucket或Object Key不匹配");
            }
        }

        // 提供通用解决建议
        log.error("🔧 通用解决建议:");
        log.error("  1. 检查OSS配置是否正确（Access Key、Secret Key、Bucket、Endpoint）");
        log.error("  2. 确认使用的是OSS返回的真实Upload ID，而不是内部会话ID");
        log.error("  3. 验证OSS权限设置，确保有分片上传权限");
        log.error("  4. 检查系统时间是否与OSS服务器同步");
        log.error("  5. 确认网络连接正常，没有防火墙阻拦");

        log.error("=== 403错误分析结束 ===");
    }

    /**
     * 清理OSS上的测试文件
     */
    private void cleanupOssFiles() {
        for (String filePath : uploadedFiles) {
            try {
                if (ossClient.doesObjectExist(BUCKET_NAME, filePath)) {
                    ossClient.deleteObject(BUCKET_NAME, filePath);
                    log.info("清理测试文件成功: {}", filePath);
                }
            } catch (Exception e) {
                log.warn("清理测试文件失败: {}, 错误: {}", filePath, e.getMessage());
            }
        }
        uploadedFiles.clear();
    }

    /**
     * 分片信息类
     */
    private static class ChunkInfo {
        int partNumber;
        int size;
        byte[] data;
    }
}
