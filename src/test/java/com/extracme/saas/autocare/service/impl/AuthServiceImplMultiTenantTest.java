package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.SmsService;
import com.extracme.saas.autocare.util.JwtUtil;
import com.extracme.saas.autocare.util.OrgHierarchyUtils;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;

/**
 * AuthServiceImpl 多租户登录测试
 */
@ExtendWith(MockitoExtension.class)
class AuthServiceImplMultiTenantTest {

    @Mock
    private SmsService smsService;

    @Mock
    private TableUserService userService;

    @Mock
    private TableTenantService tenantService;

    @Mock
    private TableRoleService roleService;

    @Mock
    private TablePermissionService permissionService;

    @Mock
    private TableUserOrgService userOrgService;

    @Mock
    private OrgHierarchyUtils orgHierarchyUtils;

    @Mock
    private UserPermissionCacheUtils userPermissionCacheUtils;

    @Mock
    private JwtUtil jwtUtil;

    @InjectMocks
    private AuthServiceImpl authService;

    private String testMobile = "13800138000";
    private String testCode = "123456";

    @BeforeEach
    void setUp() {
        // JWT过期时间通过@Value注解设置，测试中不需要手动设置
    }

    @Test
    void testLoginByMobileWithTenant_SingleTenant_Success() {
        // 准备测试数据
        List<Long> tenantIds = Collections.singletonList(1L);
        SysUser user = createTestUser(1L, testMobile);

        // 准备租户数据
        SysTenant tenant = createValidTenant(1L, "测试租户");

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(Collections.singletonList(user));
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);
        when(userService.findByMobileAndTenantId(testMobile, 1L)).thenReturn(Optional.of(user));
        when(tenantService.selectById(1L)).thenReturn(tenant);
        when(roleService.findByUserId(1L)).thenReturn(Collections.emptyList());
        when(permissionService.findByUserId(1L)).thenReturn(Collections.emptyList());
        when(userOrgService.findOrgIdsByUserId(1L)).thenReturn(Collections.emptyList());
        when(orgHierarchyUtils.calculateAllAccessibleOrgIds(any())).thenReturn(Collections.emptyList());
        when(userPermissionCacheUtils.cacheUserPermissions(any(), any(), any(), any(), any())).thenReturn(true);
        when(jwtUtil.generateTokenWithUserInfo(any())).thenReturn("test-token");

        // 执行测试
        TokenDTO result = authService.loginByMobileWithTenant(testMobile, testCode, null);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-token", result.getAccessToken());
        assertEquals(Long.valueOf(1), result.getUserId());
        assertEquals(testMobile, result.getMobile());

        // 验证方法调用
        verify(smsService).markCodeAsUsed(testMobile, testCode, "LOGIN");
    }

    @Test
    void testLoginByMobileWithTenant_MultipleTenants_ThrowsException() {
        // 准备测试数据
        List<Long> tenantIds = Arrays.asList(1L, 2L);
        SysUser user1 = createTestUser(1L, testMobile);
        SysUser user2 = createTestUser(2L, testMobile);

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(Arrays.asList(user1, user2));
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            authService.loginByMobileWithTenant(testMobile, testCode, null);
        });

        assertEquals(ErrorCode.MULTIPLE_TENANT_ACCOUNTS.getCode(), exception.getCode());
    }

    @Test
    void testLoginByMobileWithTenant_SpecificTenant_Success() {
        // 准备测试数据
        List<Long> tenantIds = Arrays.asList(1L, 2L);
        SysUser user = createTestUser(2L, testMobile);

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(Collections.singletonList(user));
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);
        when(userService.findByMobileAndTenantId(testMobile, 2L)).thenReturn(Optional.of(user));
        when(jwtUtil.generateToken("1")).thenReturn("test-token");

        // 执行测试
        TokenDTO result = authService.loginByMobileWithTenant(testMobile, testCode, 2L);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-token", result.getAccessToken());
        assertEquals(Long.valueOf(1), result.getUserId());
    }

    @Test
    void testLoginByMobileWithTenant_UserNotFound_ThrowsException() {
        // Mock 方法调用 - 用户不存在
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(Collections.emptyList());

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            authService.loginByMobileWithTenant(testMobile, testCode, null);
        });

        assertEquals(ErrorCode.USER_NOT_FOUND.getCode(), exception.getCode());

        // 验证不会调用租户相关的方法，因为用户不存在时应该直接抛出异常
        verify(userService).findAllByMobile(testMobile);
        // 确保没有调用租户相关的查询方法
        verify(userService, never()).findTenantIdsByMobile(testMobile);
    }

    @Test
    void testLoginByMobileWithTenant_UserDisabled_ThrowsException() {
        // 准备测试数据 - 禁用用户
        SysUser disabledUser = createTestUser(1L, testMobile);
        disabledUser.setStatus(0); // 设置为禁用状态

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(Collections.singletonList(disabledUser));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            authService.loginByMobileWithTenant(testMobile, testCode, null);
        });

        assertEquals(ErrorCode.ACCOUNT_LOCKED.getCode(), exception.getCode());

        // 验证调用顺序：先验证用户存在和状态，再验证租户权限
        verify(userService).findAllByMobile(testMobile);
        // 确保没有调用租户相关的查询方法，因为用户状态异常时应该直接抛出异常
        verify(userService, never()).findTenantIdsByMobile(testMobile);
    }

    @Test
    void testLoginByMobileWithTenant_NoTenantAccounts_ThrowsException() {
        // 准备测试数据 - 正常用户但无租户关联
        SysUser user = createTestUser(1L, testMobile);

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(Collections.singletonList(user));
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(Collections.emptyList());

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            authService.loginByMobileWithTenant(testMobile, testCode, null);
        });

        assertEquals(ErrorCode.NO_TENANT_ACCOUNTS.getCode(), exception.getCode());

        // 验证调用顺序：先验证用户基础信息，再验证租户权限
        verify(userService).findAllByMobile(testMobile);
        verify(userService).findTenantIdsByMobile(testMobile);
    }

    @Test
    void testGetUserTenantAccounts_Success() {
        // 准备测试数据
        List<Long> tenantIds = Arrays.asList(1L, 2L);
        SysTenant tenant1 = createTestTenant(1L, "租户1");
        SysTenant tenant2 = createTestTenant(2L, "租户2");

        // Mock 方法调用
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);
        when(tenantService.selectById(1L)).thenReturn(tenant1);
        when(tenantService.selectById(2L)).thenReturn(tenant2);

        // 执行测试
        List<ComboVO<String>> result = authService.getUserTenantAccounts(testMobile);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("1", result.get(0).getId());
        assertEquals("租户1", result.get(0).getValue());
        assertEquals("2", result.get(1).getId());
        assertEquals("租户2", result.get(1).getValue());
    }

    @Test
    void testLoginByMobileWithTenant_TenantExpired_ThrowsException() {
        // 准备测试数据 - 过期租户
        SysUser user = createTestUser(1L, testMobile);
        SysTenant expiredTenant = createExpiredTenant(1L, "过期租户");
        List<Long> tenantIds = Collections.singletonList(1L);

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(Collections.singletonList(user));
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);
        when(userService.findByMobileAndTenantId(testMobile, 1L)).thenReturn(Optional.of(user));
        when(tenantService.selectById(1L)).thenReturn(expiredTenant);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            authService.loginByMobileWithTenant(testMobile, testCode, null);
        });

        assertEquals(ErrorCode.TENANT_EXPIRED.getCode(), exception.getCode());
        assertEquals("商户已过期，请联系管理员", exception.getMessage());
    }

    @Test
    void testLoginByMobileWithTenant_TenantDisabled_ThrowsException() {
        // 准备测试数据 - 禁用租户
        SysUser user = createTestUser(1L, testMobile);
        SysTenant disabledTenant = createDisabledTenant(1L, "禁用租户");
        List<Long> tenantIds = Collections.singletonList(1L);

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(Collections.singletonList(user));
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);
        when(userService.findByMobileAndTenantId(testMobile, 1L)).thenReturn(Optional.of(user));
        when(tenantService.selectById(1L)).thenReturn(disabledTenant);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            authService.loginByMobileWithTenant(testMobile, testCode, null);
        });

        assertEquals(ErrorCode.TENANT_DISABLED.getCode(), exception.getCode());
        assertEquals("商户状态异常，无法登录", exception.getMessage());
    }

    @Test
    void testGetUserTenantAccounts_FilterExpiredTenants() {
        // 准备测试数据 - 包含过期和有效租户
        List<Long> tenantIds = Arrays.asList(1L, 2L, 3L);
        SysTenant validTenant = createValidTenant(1L, "有效租户");
        SysTenant expiredTenant = createExpiredTenant(2L, "过期租户");
        SysTenant disabledTenant = createDisabledTenant(3L, "禁用租户");

        // Mock 方法调用
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);
        when(tenantService.selectById(1L)).thenReturn(validTenant);
        when(tenantService.selectById(2L)).thenReturn(expiredTenant);
        when(tenantService.selectById(3L)).thenReturn(disabledTenant);

        // 执行测试
        List<ComboVO<String>> result = authService.getUserTenantAccounts(testMobile);

        // 验证结果 - 只应该返回有效租户
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("1", result.get(0).getId());
        assertEquals("有效租户", result.get(0).getValue());
    }

    private SysUser createTestUser(Long tenantId, String mobile) {
        SysUser user = new SysUser();
        user.setId(1L);
        user.setTenantId(tenantId);
        user.setMobile(mobile);
        user.setUsername("testuser");
        user.setStatus(1);
        return user;
    }

    private SysTenant createTestTenant(Long id, String name) {
        SysTenant tenant = new SysTenant();
        tenant.setId(id);
        tenant.setTenantName(name);
        tenant.setStatus(1);
        return tenant;
    }

    private SysTenant createValidTenant(Long id, String name) {
        SysTenant tenant = new SysTenant();
        tenant.setId(id);
        tenant.setTenantName(name);
        tenant.setTenantCode("VALID_TENANT");
        tenant.setStatus(1); // 启用状态

        // 设置未来的过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        tenant.setExpireTime(calendar.getTime());

        return tenant;
    }

    private SysTenant createExpiredTenant(Long id, String name) {
        SysTenant tenant = new SysTenant();
        tenant.setId(id);
        tenant.setTenantName(name);
        tenant.setTenantCode("EXPIRED_TENANT");
        tenant.setStatus(1); // 启用状态但已过期

        // 设置过去的过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        tenant.setExpireTime(calendar.getTime());

        return tenant;
    }

    private SysTenant createDisabledTenant(Long id, String name) {
        SysTenant tenant = new SysTenant();
        tenant.setId(id);
        tenant.setTenantName(name);
        tenant.setTenantCode("DISABLED_TENANT");
        tenant.setStatus(0); // 禁用状态

        // 设置未来的过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        tenant.setExpireTime(calendar.getTime());

        return tenant;
    }
}
