package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableRegionService;
import com.extracme.saas.autocare.service.impl.RegionServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 地区数据服务测试类
 */
@ExtendWith(MockitoExtension.class)
public class RegionServiceTest {

    @Mock
    private TableRegionService tableRegionService;

    @InjectMocks
    private RegionServiceImpl regionService;

    private List<ComboVO<Long>> mockProvinceData;
    private List<ComboVO<Long>> mockCityData;
    private List<ComboVO<Long>> mockAreaData;

    @BeforeEach
    void setUp() {
        // 创建模拟的省份数据
        mockProvinceData = Arrays.asList(
                createComboVO(110000L, "北京市"),
                createComboVO(120000L, "天津市"),
                createComboVO(130000L, "河北省")
        );

        // 创建模拟的城市数据
        mockCityData = Arrays.asList(
                createComboVO(110100L, "北京市"),
                createComboVO(110200L, "县")
        );

        // 创建模拟的区域数据
        mockAreaData = Arrays.asList(
                createComboVO(110101L, "东城区"),
                createComboVO(110102L, "西城区"),
                createComboVO(110105L, "朝阳区")
        );
    }

    private ComboVO<Long> createComboVO(Long id, String value) {
        ComboVO<Long> combo = new ComboVO<>();
        combo.setId(id);
        combo.setValue(value);
        return combo;
    }

    @Test
    @DisplayName("测试获取省份下拉框数据 - 成功场景")
    void testGetProvinceCombo_Success() {
        // 模拟数据
        when(tableRegionService.getProvinceCombo()).thenReturn(mockProvinceData);

        // 执行测试
        List<ComboVO<Long>> result = regionService.getProvinceCombo();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(110000L, result.get(0).getId());
        assertEquals("北京市", result.get(0).getValue());
        assertEquals(120000L, result.get(1).getId());
        assertEquals("天津市", result.get(1).getValue());
        assertEquals(130000L, result.get(2).getId());
        assertEquals("河北省", result.get(2).getValue());

        // 验证方法调用
        verify(tableRegionService).getProvinceCombo();
    }

    @Test
    @DisplayName("测试获取省份下拉框数据 - 空数据")
    void testGetProvinceCombo_EmptyData() {
        // 模拟空数据
        when(tableRegionService.getProvinceCombo()).thenReturn(Collections.emptyList());

        // 执行测试
        List<ComboVO<Long>> result = regionService.getProvinceCombo();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(tableRegionService).getProvinceCombo();
    }

    @Test
    @DisplayName("测试获取城市下拉框数据 - 成功场景")
    void testGetCityCombo_Success() {
        Long provinceId = 110000L;

        // 模拟数据
        when(tableRegionService.getCityCombo(provinceId)).thenReturn(mockCityData);

        // 执行测试
        List<ComboVO<Long>> result = regionService.getCityCombo(provinceId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(110100L, result.get(0).getId());
        assertEquals("北京市", result.get(0).getValue());

        // 验证方法调用
        verify(tableRegionService).getCityCombo(provinceId);
    }

    @Test
    @DisplayName("测试获取城市下拉框数据 - 省份ID为空")
    void testGetCityCombo_NullProvinceId() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            regionService.getCityCombo(null);
        });

        assertEquals("省份ID不能为空", exception.getMessage());

        // 验证没有调用Repository方法
        verify(tableRegionService, never()).getCityCombo(any());
    }

    @Test
    @DisplayName("测试获取区域下拉框数据 - 成功场景")
    void testGetAreaCombo_Success() {
        Long cityId = 110100L;

        // 模拟数据
        when(tableRegionService.getAreaCombo(cityId)).thenReturn(mockAreaData);

        // 执行测试
        List<ComboVO<Long>> result = regionService.getAreaCombo(cityId);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(110101L, result.get(0).getId());
        assertEquals("东城区", result.get(0).getValue());
        assertEquals(110102L, result.get(1).getId());
        assertEquals("西城区", result.get(1).getValue());
        assertEquals(110105L, result.get(2).getId());
        assertEquals("朝阳区", result.get(2).getValue());

        // 验证方法调用
        verify(tableRegionService).getAreaCombo(cityId);
    }

    @Test
    @DisplayName("测试获取区域下拉框数据 - 城市ID为空")
    void testGetAreaCombo_NullCityId() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            regionService.getAreaCombo(null);
        });

        assertEquals("城市ID不能为空", exception.getMessage());

        // 验证没有调用Repository方法
        verify(tableRegionService, never()).getAreaCombo(any());
    }

    @Test
    @DisplayName("测试获取城市下拉框数据 - 空数据")
    void testGetCityCombo_EmptyData() {
        Long provinceId = 999999L; // 不存在的省份ID

        // 模拟空数据
        when(tableRegionService.getCityCombo(provinceId)).thenReturn(Collections.emptyList());

        // 执行测试
        List<ComboVO<Long>> result = regionService.getCityCombo(provinceId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(tableRegionService).getCityCombo(provinceId);
    }

    @Test
    @DisplayName("测试获取区域下拉框数据 - 空数据")
    void testGetAreaCombo_EmptyData() {
        Long cityId = 999999L; // 不存在的城市ID

        // 模拟空数据
        when(tableRegionService.getAreaCombo(cityId)).thenReturn(Collections.emptyList());

        // 执行测试
        List<ComboVO<Long>> result = regionService.getAreaCombo(cityId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(tableRegionService).getAreaCombo(cityId);
    }
}
