package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.extracme.saas.autocare.model.entity.DataDictInfo;
import com.extracme.saas.autocare.model.vo.DataDictSimpleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableDataDictInfoService;
import com.extracme.saas.autocare.service.impl.DataDictServiceImpl;

/**
 * 数据字典分页查询功能测试
 */
@ExtendWith(MockitoExtension.class)
public class DataDictPageQueryTest {

    @Mock
    private TableDataDictInfoService dataDictInfoService;

    @InjectMocks
    private DataDictServiceImpl dataDictService;

    private DataDictInfo testDict1;
    private DataDictInfo testDict2;
    private DataDictInfo testDict3;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testDict1 = new DataDictInfo();
        testDict1.setId(1L);
        testDict1.setDataName("性别");
        testDict1.setDataCode("SEX");
        testDict1.setCodeType(1);

        testDict2 = new DataDictInfo();
        testDict2.setId(2L);
        testDict2.setDataName("状态");
        testDict2.setDataCode("STATUS");
        testDict2.setCodeType(1);

        testDict3 = new DataDictInfo();
        testDict3.setId(3L);
        testDict3.setDataName("用户类型");
        testDict3.setDataCode("USER_TYPE");
        testDict3.setCodeType(1);
    }

    @Test
    @DisplayName("测试无查询条件的分页查询")
    void testPageListWithoutConditions() {
        // 准备测试数据
        List<DataDictInfo> mockData = Arrays.asList(testDict1, testDict2, testDict3);
        when(dataDictInfoService.findByCondition(null, null)).thenReturn(mockData);

        // 创建分页请求
        BasePageDTO pageDTO = new BasePageDTO();
        pageDTO.setPageNum(1);
        pageDTO.setPageSize(10);

        // 执行查询
        BasePageVO<DataDictSimpleVO> result = dataDictService.pageList(pageDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(3, result.getList().size());

        // 验证第一条数据
        DataDictSimpleVO firstItem = result.getList().get(0);
        assertEquals(1L, firstItem.getId());
        assertEquals("性别", firstItem.getDataName());
        assertEquals("SEX", firstItem.getDataCode());
        assertEquals(1, firstItem.getCodeType());

        // 验证调用了正确的方法
        verify(dataDictInfoService).findByCondition(null, null);
    }

    @Test
    @DisplayName("测试按字典名称模糊查询")
    void testPageListWithDictNameCondition() {
        // 准备测试数据 - 只返回匹配的数据
        List<DataDictInfo> mockData = Arrays.asList(testDict1);
        when(dataDictInfoService.findByCondition("性别", null)).thenReturn(mockData);

        // 创建分页请求
        BasePageDTO pageDTO = new BasePageDTO();
        pageDTO.setPageNum(1);
        pageDTO.setPageSize(10);
        pageDTO.setDictName("性别");

        // 执行查询
        BasePageVO<DataDictSimpleVO> result = dataDictService.pageList(pageDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        assertEquals("性别", result.getList().get(0).getDataName());

        // 验证调用了正确的方法
        verify(dataDictInfoService).findByCondition("性别", null);
    }

    @Test
    @DisplayName("测试按字典编码模糊查询")
    void testPageListWithDictCodeCondition() {
        // 准备测试数据 - 只返回匹配的数据
        List<DataDictInfo> mockData = Arrays.asList(testDict2);
        when(dataDictInfoService.findByCondition(null, "STATUS")).thenReturn(mockData);

        // 创建分页请求
        BasePageDTO pageDTO = new BasePageDTO();
        pageDTO.setPageNum(1);
        pageDTO.setPageSize(10);
        pageDTO.setDictCode("STATUS");

        // 执行查询
        BasePageVO<DataDictSimpleVO> result = dataDictService.pageList(pageDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        assertEquals("STATUS", result.getList().get(0).getDataCode());

        // 验证调用了正确的方法
        verify(dataDictInfoService).findByCondition(null, "STATUS");
    }

    @Test
    @DisplayName("测试同时按字典名称和编码查询")
    void testPageListWithBothConditions() {
        // 准备测试数据 - 只返回匹配的数据
        List<DataDictInfo> mockData = Arrays.asList(testDict1);
        when(dataDictInfoService.findByCondition("性别", "SEX")).thenReturn(mockData);

        // 创建分页请求
        BasePageDTO pageDTO = new BasePageDTO();
        pageDTO.setPageNum(1);
        pageDTO.setPageSize(10);
        pageDTO.setDictName("性别");
        pageDTO.setDictCode("SEX");

        // 执行查询
        BasePageVO<DataDictSimpleVO> result = dataDictService.pageList(pageDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        assertEquals("性别", result.getList().get(0).getDataName());
        assertEquals("SEX", result.getList().get(0).getDataCode());

        // 验证调用了正确的方法
        verify(dataDictInfoService).findByCondition("性别", "SEX");
    }

    @Test
    @DisplayName("测试查询无结果的情况")
    void testPageListWithNoResults() {
        // 准备测试数据 - 返回空列表
        when(dataDictInfoService.findByCondition("不存在", "NOT_EXIST")).thenReturn(Collections.emptyList());

        // 创建分页请求
        BasePageDTO pageDTO = new BasePageDTO();
        pageDTO.setPageNum(1);
        pageDTO.setPageSize(10);
        pageDTO.setDictName("不存在");
        pageDTO.setDictCode("NOT_EXIST");

        // 执行查询
        BasePageVO<DataDictSimpleVO> result = dataDictService.pageList(pageDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(0, result.getList().size());

        // 验证调用了正确的方法
        verify(dataDictInfoService).findByCondition("不存在", "NOT_EXIST");
    }

    @Test
    @DisplayName("测试参数验证")
    void testParameterValidation() {
        // 测试空参数
        assertThrows(IllegalArgumentException.class, () -> {
            dataDictService.pageList(null);
        });

        // 测试无效页码
        BasePageDTO invalidPageDTO = new BasePageDTO();
        invalidPageDTO.setPageNum(0);
        invalidPageDTO.setPageSize(10);
        
        assertThrows(IllegalArgumentException.class, () -> {
            dataDictService.pageList(invalidPageDTO);
        });

        // 测试无效页面大小
        BasePageDTO invalidSizeDTO = new BasePageDTO();
        invalidSizeDTO.setPageNum(1);
        invalidSizeDTO.setPageSize(0);
        
        assertThrows(IllegalArgumentException.class, () -> {
            dataDictService.pageList(invalidSizeDTO);
        });
    }
}
