package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableUserService;

/**
 * PermissionServiceImpl 超级管理员权限测试类
 */
@ExtendWith(MockitoExtension.class)
class PermissionServiceImplSuperAdminTest {

    @Mock
    private TablePermissionService permissionRepository;
    
    @Mock
    private TableUserService userRepository;

    @InjectMocks
    private PermissionServiceImpl permissionService;

    private SysUser superAdminUser;
    private SysUser regularUser;
    private List<SysPermission> allPermissions;

    @BeforeEach
    void setUp() {
        // 设置超级管理员用户
        superAdminUser = new SysUser();
        superAdminUser.setId(1L);
        superAdminUser.setUsername("superadmin");
        superAdminUser.setAccountType(0); // 超级管理员
        superAdminUser.setTenantId(1L);
        superAdminUser.setStatus(1);

        // 设置普通用户
        regularUser = new SysUser();
        regularUser.setId(2L);
        regularUser.setUsername("regularuser");
        regularUser.setAccountType(1); // 运营人员
        regularUser.setTenantId(1L);
        regularUser.setStatus(1);

        // 设置系统中的所有权限
        SysPermission permission1 = new SysPermission();
        permission1.setId(1L);
        permission1.setPermissionCode("user:read");
        permission1.setPermissionName("用户查看");

        SysPermission permission2 = new SysPermission();
        permission2.setId(2L);
        permission2.setPermissionCode("user:write");
        permission2.setPermissionName("用户编辑");

        SysPermission permission3 = new SysPermission();
        permission3.setId(3L);
        permission3.setPermissionCode("admin:system");
        permission3.setPermissionName("系统管理");

        allPermissions = Arrays.asList(permission1, permission2, permission3);
    }

    @Test
    @DisplayName("超级管理员应该获得所有系统权限")
    void getUserPermissions_SuperAdmin_ShouldReturnAllPermissions() {
        // Given
        when(userRepository.selectById(1L)).thenReturn(superAdminUser);
        when(permissionRepository.findAll()).thenReturn(allPermissions);

        // When
        List<SysPermission> result = permissionService.getUserPermissions(1L);

        // Then
        assertEquals(3, result.size());
        assertEquals(allPermissions, result);
        verify(permissionRepository).findAll();
        verify(permissionRepository, never()).findByUserId(any());
    }

    @Test
    @DisplayName("普通用户应该通过角色关联获得权限")
    void getUserPermissions_RegularUser_ShouldReturnRoleBasedPermissions() {
        // Given
        List<SysPermission> userPermissions = Arrays.asList(allPermissions.get(0)); // 只有第一个权限
        
        when(userRepository.selectById(2L)).thenReturn(regularUser);
        when(permissionRepository.findByUserId(2L)).thenReturn(userPermissions);

        // When
        List<SysPermission> result = permissionService.getUserPermissions(2L);

        // Then
        assertEquals(1, result.size());
        assertEquals("user:read", result.get(0).getPermissionCode());
        verify(permissionRepository).findByUserId(2L);
        verify(permissionRepository, never()).findAll();
    }

    @Test
    @DisplayName("用户不存在时应该返回空列表")
    void getUserPermissions_UserNotFound_ShouldReturnEmptyList() {
        // Given
        when(userRepository.selectById(999L)).thenReturn(null);

        // When
        List<SysPermission> result = permissionService.getUserPermissions(999L);

        // Then
        assertTrue(result.isEmpty());
        verify(permissionRepository, never()).findAll();
        verify(permissionRepository, never()).findByUserId(any());
    }

    @Test
    @DisplayName("超级管理员对任何权限检查都应该返回true")
    void hasPermission_SuperAdmin_ShouldAlwaysReturnTrue() {
        // Given
        when(userRepository.selectById(1L)).thenReturn(superAdminUser);

        // When & Then
        assertTrue(permissionService.hasPermission(1L, "user:read"));
        assertTrue(permissionService.hasPermission(1L, "user:write"));
        assertTrue(permissionService.hasPermission(1L, "admin:system"));
        assertTrue(permissionService.hasPermission(1L, "any:permission"));

        verify(permissionRepository, never()).hasPermission(any(), any());
    }

    @Test
    @DisplayName("普通用户权限检查应该通过角色关联验证")
    void hasPermission_RegularUser_ShouldCheckRoleBasedPermissions() {
        // Given
        when(userRepository.selectById(2L)).thenReturn(regularUser);
        when(permissionRepository.hasPermission(2L, "user:read")).thenReturn(true);
        when(permissionRepository.hasPermission(2L, "admin:system")).thenReturn(false);

        // When & Then
        assertTrue(permissionService.hasPermission(2L, "user:read"));
        assertFalse(permissionService.hasPermission(2L, "admin:system"));

        verify(permissionRepository).hasPermission(2L, "user:read");
        verify(permissionRepository).hasPermission(2L, "admin:system");
    }

    @Test
    @DisplayName("用户不存在时权限检查应该返回false")
    void hasPermission_UserNotFound_ShouldReturnFalse() {
        // Given
        when(userRepository.selectById(999L)).thenReturn(null);

        // When & Then
        assertFalse(permissionService.hasPermission(999L, "user:read"));
        verify(permissionRepository, never()).hasPermission(any(), any());
    }

    @Test
    @DisplayName("accountType为null的用户应该按普通用户处理")
    void hasPermission_NullAccountType_ShouldTreatAsRegularUser() {
        // Given
        SysUser userWithNullAccountType = new SysUser();
        userWithNullAccountType.setId(3L);
        userWithNullAccountType.setAccountType(null);
        
        when(userRepository.selectById(3L)).thenReturn(userWithNullAccountType);
        when(permissionRepository.hasPermission(3L, "user:read")).thenReturn(false);

        // When & Then
        assertFalse(permissionService.hasPermission(3L, "user:read"));
        verify(permissionRepository).hasPermission(3L, "user:read");
    }

    @Test
    @DisplayName("accountType为2的修理厂用户应该按普通用户处理")
    void hasPermission_GarageUser_ShouldTreatAsRegularUser() {
        // Given
        SysUser garageUser = new SysUser();
        garageUser.setId(4L);
        garageUser.setAccountType(2); // 修理厂用户
        
        when(userRepository.selectById(4L)).thenReturn(garageUser);
        when(permissionRepository.hasPermission(4L, "user:read")).thenReturn(true);

        // When & Then
        assertTrue(permissionService.hasPermission(4L, "user:read"));
        verify(permissionRepository).hasPermission(4L, "user:read");
    }
}
