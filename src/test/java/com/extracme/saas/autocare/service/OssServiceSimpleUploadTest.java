package com.extracme.saas.autocare.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.extracme.saas.autocare.config.OssConfig;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.FileUploadResultVO;
import com.extracme.saas.autocare.service.impl.OssServiceImpl;
import com.extracme.saas.autocare.util.SessionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OSS普通文件上传服务测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("OSS普通文件上传服务测试")
class OssServiceSimpleUploadTest {

    @Mock
    private OSS ossClient;

    @Mock
    private OssConfig ossConfig;

    @InjectMocks
    private OssServiceImpl ossService;

    private SysUser mockUser;
    private LoginUser mockLoginUser;

    @BeforeEach
    void setUp() {
        // 设置模拟用户
        mockUser = new SysUser();
        mockUser.setId(123L);
        mockUser.setUsername("testuser");
        mockUser.setNickname("测试用户");
        mockUser.setTenantId(1L);

        mockLoginUser = new LoginUser();
        mockLoginUser.setUser(mockUser);
    }

    @Test
    @DisplayName("测试普通文件上传成功")
    void testUploadSimpleFileSuccess() throws IOException {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置OSS配置
            when(ossConfig.getBucketName()).thenReturn("test-bucket");
            OssConfig.Upload uploadConfig = new OssConfig.Upload();
            uploadConfig.setBaseUrl("https://test-bucket.oss-cn-shanghai.aliyuncs.com");
            when(ossConfig.getUpload()).thenReturn(uploadConfig);
            
            // 设置模拟会话
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            // 创建测试文件
            MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.txt",
                "text/plain",
                "Hello World".getBytes()
            );
            
            // 模拟OSS上传结果
            PutObjectResult putResult = new PutObjectResult();
            putResult.setETag("test-etag-123");
            
            when(ossClient.putObject(any(PutObjectRequest.class))).thenReturn(putResult);

            // 执行测试
            FileUploadResultVO result = ossService.uploadSimpleFile(file, "documents");

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getFileName());
            assertEquals("test.txt", result.getOriginalFileName());
            assertNotNull(result.getRelativePath());
            assertTrue(result.getRelativePath().contains("tenant_1/uploads"));
            assertTrue(result.getRelativePath().contains("documents"));
            assertTrue(result.getRelativePath().endsWith(".txt"));
            assertNotNull(result.getFullUrl());
            assertTrue(result.getFullUrl().startsWith("https://test-bucket.oss-cn-shanghai.aliyuncs.com"));
            assertEquals(11L, result.getFileSize()); // "Hello World".length()
            assertEquals("text/plain", result.getContentType());
            assertEquals("txt", result.getFileExtension());
            assertNotNull(result.getUploadTime());
            assertEquals("tenant_1", result.getTenantCode());
            assertEquals(123L, result.getUploadUserId());
            assertEquals("测试用户", result.getUploadUserName());
            assertEquals(false, result.getIsResumable());
            assertEquals(100, result.getProgress());

            // 验证调用
            verify(ossClient).putObject(any(PutObjectRequest.class));
        }
    }

    @Test
    @DisplayName("测试普通文件上传成功 - 不带分类")
    void testUploadSimpleFileSuccessWithoutCategory() throws IOException {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置OSS配置
            when(ossConfig.getBucketName()).thenReturn("test-bucket");
            OssConfig.Upload uploadConfig = new OssConfig.Upload();
            uploadConfig.setBaseUrl("https://test-bucket.oss-cn-shanghai.aliyuncs.com");
            when(ossConfig.getUpload()).thenReturn(uploadConfig);
            
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.txt",
                "text/plain",
                "Hello World".getBytes()
            );
            
            PutObjectResult putResult = new PutObjectResult();
            putResult.setETag("test-etag-123");
            when(ossClient.putObject(any(PutObjectRequest.class))).thenReturn(putResult);

            // 执行测试 - 不传分类
            FileUploadResultVO result = ossService.uploadSimpleFile(file, null);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getRelativePath());
            // 路径中不应包含分类目录
            assertFalse(result.getRelativePath().contains("/documents/"));
            assertTrue(result.getRelativePath().contains("tenant_1/uploads"));
        }
    }

    @Test
    @DisplayName("测试普通文件上传失败 - 文件为空")
    void testUploadSimpleFileFailureEmptyFile() {
        // 创建空文件
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.txt",
            "text/plain",
            new byte[0]
        );

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ossService.uploadSimpleFile(file, "documents");
        });

        assertTrue(exception.getMessage().contains("上传文件不能为空"));
    }

    @Test
    @DisplayName("测试普通文件上传失败 - 文件名为空")
    void testUploadSimpleFileFailureEmptyFileName() {
        // 创建文件名为空的文件
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "",
            "text/plain",
            "Hello World".getBytes()
        );

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ossService.uploadSimpleFile(file, "documents");
        });

        assertTrue(exception.getMessage().contains("文件名不能为空"));
    }

    @Test
    @DisplayName("测试普通文件上传失败 - 文件过大")
    void testUploadSimpleFileFailureTooLarge() {
        // 创建大文件（模拟超过10MB）
        byte[] largeContent = new byte[11 * 1024 * 1024]; // 11MB
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "large-file.txt",
            "text/plain",
            largeContent
        );

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ossService.uploadSimpleFile(file, "documents");
        });

        assertTrue(exception.getMessage().contains("文件大小不能超过10MB"));
    }

    @Test
    @DisplayName("测试普通文件上传失败 - 不支持的文件类型")
    void testUploadSimpleFileFailureUnsupportedType() {
        // 创建不支持的文件类型
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.exe",
            "application/x-msdownload",
            "Executable content".getBytes()
        );

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ossService.uploadSimpleFile(file, "documents");
        });

        assertTrue(exception.getMessage().contains("不支持的文件类型: exe"));
    }

    @Test
    @DisplayName("测试普通文件上传失败 - OSS上传异常")
    void testUploadSimpleFileFailureOssError() throws IOException {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置OSS配置
            when(ossConfig.getBucketName()).thenReturn("test-bucket");

            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);

            MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.txt",
                "text/plain",
                "Hello World".getBytes()
            );

            // 模拟OSS上传失败
            when(ossClient.putObject(any(PutObjectRequest.class)))
                .thenThrow(new RuntimeException("OSS upload failed"));

            // 执行测试并验证异常
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                ossService.uploadSimpleFile(file, "documents");
            });

            assertTrue(exception.getMessage().contains("文件上传失败"));
        }
    }

    @Test
    @DisplayName("测试普通文件上传 - 支持的各种文件类型")
    void testUploadSimpleFileSupportedTypes() throws IOException {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置OSS配置
            when(ossConfig.getBucketName()).thenReturn("test-bucket");
            OssConfig.Upload uploadConfig = new OssConfig.Upload();
            uploadConfig.setBaseUrl("https://test-bucket.oss-cn-shanghai.aliyuncs.com");
            when(ossConfig.getUpload()).thenReturn(uploadConfig);
            
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(mockLoginUser);
            
            PutObjectResult putResult = new PutObjectResult();
            putResult.setETag("test-etag-123");
            when(ossClient.putObject(any(PutObjectRequest.class))).thenReturn(putResult);

            // 测试支持的文件类型
            String[] supportedTypes = {"jpg", "jpeg", "png", "pdf", "doc", "docx", "txt"};
            
            for (String type : supportedTypes) {
                MockMultipartFile file = new MockMultipartFile(
                    "file",
                    "test." + type,
                    "application/octet-stream",
                    "Test content".getBytes()
                );
                
                // 应该不抛出异常
                assertDoesNotThrow(() -> {
                    FileUploadResultVO result = ossService.uploadSimpleFile(file, "documents");
                    assertNotNull(result);
                    assertEquals(type, result.getFileExtension());
                });
            }
        }
    }

    @Test
    @DisplayName("测试普通文件上传 - 用户信息为空")
    void testUploadSimpleFileWithNullUser() throws IOException {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置OSS配置
            when(ossConfig.getBucketName()).thenReturn("test-bucket");
            OssConfig.Upload uploadConfig = new OssConfig.Upload();
            uploadConfig.setBaseUrl("https://test-bucket.oss-cn-shanghai.aliyuncs.com");
            when(ossConfig.getUpload()).thenReturn(uploadConfig);
            
            // 模拟用户为空
            sessionUtilsMock.when(() -> SessionUtils.getLoginUser()).thenReturn(null);
            
            MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.txt",
                "text/plain",
                "Hello World".getBytes()
            );
            
            PutObjectResult putResult = new PutObjectResult();
            putResult.setETag("test-etag-123");
            when(ossClient.putObject(any(PutObjectRequest.class))).thenReturn(putResult);

            // 执行测试
            FileUploadResultVO result = ossService.uploadSimpleFile(file, "documents");

            // 验证结果 - 用户信息应该为空，但上传仍然成功
            assertNotNull(result);
            assertNull(result.getUploadUserId());
            assertNull(result.getUploadUserName());
            assertEquals("default", result.getTenantCode());
        }
    }
}
