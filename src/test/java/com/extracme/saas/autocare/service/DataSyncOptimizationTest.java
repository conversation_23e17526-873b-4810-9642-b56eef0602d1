package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.SyncDataResultDTO;
import com.extracme.saas.autocare.model.dto.TenantIdentificationResultDTO;
import com.extracme.saas.autocare.model.dto.VehicleInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.vo.DataSyncResultVO;
import com.extracme.saas.autocare.service.impl.DataSyncServiceImpl;
import com.extracme.saas.autocare.service.sync.SyncStrategy;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 数据同步优化效果测试
 * 使用Mock对象验证并行处理逻辑和性能优化
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
public class DataSyncOptimizationTest {

    @Mock
    private TenantIdentificationService tenantIdentificationService;

    @Mock
    private SyncStrategy mockSyncStrategy;

    @Mock
    private Executor taskExecutor;

    @InjectMocks
    private DataSyncServiceImpl dataSyncService;

    private TenantIdentificationResultDTO mockTenantResult;

    @BeforeEach
    void setUp() {
        // 设置Mock策略
        List<SyncStrategy> strategies = new ArrayList<>();
        strategies.add(mockSyncStrategy);
        ReflectionTestUtils.setField(dataSyncService, "syncStrategies", strategies);
        
        // 设置Mock租户识别结果
        mockTenantResult = new TenantIdentificationResultDTO();
        mockTenantResult.setSuccess(true);
        mockTenantResult.setTenantId(1L);
        mockTenantResult.setTenantCode("test_tenant");

        when(tenantIdentificationService.identifyTenantBySyncKey(anyString()))
            .thenReturn(mockTenantResult);

        when(mockSyncStrategy.getSupportedTable()).thenReturn("mtc_vehicle_info");
        when(mockSyncStrategy.syncData(anyString(), any(), anyLong(), anyString()))
            .thenReturn(SyncDataResultDTO.success(1L));
    }

    /**
     * 测试并行处理逻辑是否正确启用
     */
    @Test
    void testParallelProcessingLogic() {
        log.info("测试并行处理逻辑");

        // 创建大于MIN_PARALLEL_SIZE的数据量
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> largeDataSet = createTestData(150);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey("test_key");
        requestDTO.setBatchData(largeDataSet);

        // 执行同步
        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, "127.0.0.1");

        // 验证结果
        assertNotNull(result);
        assertEquals(150, result.getTotalCount());
        assertEquals("test_tenant", result.getTenantCode());
        assertEquals("mtc_vehicle_info", result.getTargetTable());
        assertEquals("AUTO", result.getOperationType());

        // 验证策略被调用了正确的次数
        verify(mockSyncStrategy, times(150)).syncData(anyString(), any(), eq(1L), eq("test_tenant"));
        
        log.info("并行处理逻辑测试通过，数据量：{}，成功：{}", 
                result.getTotalCount(), result.getSuccessCount());
    }

    /**
     * 测试顺序处理逻辑
     */
    @Test
    void testSequentialProcessingLogic() {
        log.info("测试顺序处理逻辑");

        // 创建小于MIN_PARALLEL_SIZE的数据量
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> smallDataSet = createTestData(50);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey("test_key");
        requestDTO.setBatchData(smallDataSet);

        // 执行同步
        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, "127.0.0.1");

        // 验证结果
        assertNotNull(result);
        assertEquals(50, result.getTotalCount());
        assertEquals("SUCCESS", result.getSyncStatus());

        // 验证策略被调用了正确的次数
        verify(mockSyncStrategy, times(50)).syncData(anyString(), any(), eq(1L), eq("test_tenant"));
        
        log.info("顺序处理逻辑测试通过，数据量：{}，成功：{}", 
                result.getTotalCount(), result.getSuccessCount());
    }

    /**
     * 测试错误处理优化
     */
    @Test
    void testOptimizedErrorHandling() {
        log.info("测试优化的错误处理");

        // 设置部分失败的场景
        when(mockSyncStrategy.syncData(anyString(), any(), anyLong(), anyString()))
            .thenReturn(SyncDataResultDTO.success(1L))  // 前几次成功
            .thenReturn(SyncDataResultDTO.failure("测试失败"))        // 后几次失败
            .thenReturn(SyncDataResultDTO.success(2L))
            .thenReturn(SyncDataResultDTO.failure("另一个测试失败"));

        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> testData = createTestData(4);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey("test_key");
        requestDTO.setBatchData(testData);

        // 执行同步
        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, "127.0.0.1");

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.getTotalCount());
        assertEquals(2, result.getSuccessCount());
        assertEquals(2, result.getFailedCount());
        assertEquals("PARTIAL_SUCCESS", result.getSyncStatus());
        assertNotNull(result.getFailureDetails());
        assertEquals(2, result.getFailureDetails().size());

        log.info("错误处理测试通过，总数：{}，成功：{}，失败：{}", 
                result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());
    }

    /**
     * 测试性能指标
     */
    @Test
    void testPerformanceMetrics() {
        log.info("测试性能指标");

        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> testData = createTestData(100);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey("test_key");
        requestDTO.setBatchData(testData);

        long startTime = System.currentTimeMillis();
        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, "127.0.0.1");
        long endTime = System.currentTimeMillis();

        // 验证性能指标
        assertNotNull(result);
        assertTrue(result.getSyncDuration() > 0, "同步耗时应该大于0");
        assertNotNull(result.getSyncStartTime(), "开始时间不能为空");
        assertNotNull(result.getSyncEndTime(), "结束时间不能为空");
        assertTrue(result.getSyncEndTime().after(result.getSyncStartTime()), "结束时间应该晚于开始时间");

        long totalDuration = endTime - startTime;
        double throughput = (double) testData.size() / totalDuration * 1000;

        log.info("性能指标测试通过：");
        log.info("  数据量：{}", testData.size());
        log.info("  总耗时：{}ms", totalDuration);
        log.info("  同步耗时：{}ms", result.getSyncDuration());
        log.info("  吞吐量：{:.2f} records/sec", throughput);
        log.info("  成功率：{:.2f}%", (double) result.getSuccessCount() / result.getTotalCount() * 100);
    }

    /**
     * 测试日志优化效果
     */
    @Test
    void testLogOptimization() {
        log.info("测试日志优化效果");

        // 创建测试数据
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> testData = createTestData(10);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey("test_key");
        requestDTO.setBatchData(testData);

        // 执行同步
        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, "127.0.0.1");

        // 验证结果（主要验证没有异常，日志优化主要体现在运行时性能）
        assertNotNull(result);
        assertEquals(10, result.getTotalCount());
        assertEquals(10, result.getSuccessCount());
        assertEquals("SUCCESS", result.getSyncStatus());

        // 验证SyncDataResultDTO.success方法不再记录详细数据变化
        verify(mockSyncStrategy, times(10)).syncData(anyString(), any(), anyLong(), anyString());

        log.info("日志优化测试通过，减少了详细数据变化记录，提升了性能");
    }

    /**
     * 测试业务逻辑简化效果
     */
    @Test
    void testBusinessLogicSimplification() {
        log.info("测试业务逻辑简化效果");

        // 测试强制要求ID的逻辑
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> testData = new ArrayList<>();
        
        // 添加有ID的数据
        VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO validData = 
            new VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO();
        validData.setId(1L);
        validData.setVin("TEST_VIN_001");
        validData.setVehicleNo("TEST_NO_001");
        testData.add(validData);

        // 添加无ID的数据（应该失败）
        VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO invalidData = 
            new VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO();
        // 故意不设置ID
        invalidData.setVin("TEST_VIN_002");
        invalidData.setVehicleNo("TEST_NO_002");
        testData.add(invalidData);

        // 设置策略行为：有ID成功，无ID失败
        when(mockSyncStrategy.syncData(eq("车辆[TEST_VIN_001/TEST_NO_001]"), any(), anyLong(), anyString()))
            .thenReturn(SyncDataResultDTO.success(1L));
        when(mockSyncStrategy.syncData(eq("车辆[TEST_VIN_002/TEST_NO_002]"), any(), anyLong(), anyString()))
            .thenReturn(SyncDataResultDTO.failure("车辆ID不能为空，请提供有效的车辆ID进行同步"));

        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey("test_key");
        requestDTO.setBatchData(testData);

        // 执行同步
        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, "127.0.0.1");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotalCount());
        assertEquals(1, result.getSuccessCount());
        assertEquals(1, result.getFailedCount());
        assertEquals("PARTIAL_SUCCESS", result.getSyncStatus());

        log.info("业务逻辑简化测试通过，强制要求ID参数，简化了INSERT/UPDATE判断逻辑");
    }

    /**
     * 创建测试数据
     */
    private List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> createTestData(int count) {
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> testData = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO data = 
                new VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO();
            data.setId((long) i);
            data.setVin("TEST_VIN_" + String.format("%03d", i));
            data.setVehicleNo("测试" + String.format("%03d", i));
            data.setVehicleModelId((long) (i % 10 + 1));
            testData.add(data);
        }
        
        return testData;
    }
}
