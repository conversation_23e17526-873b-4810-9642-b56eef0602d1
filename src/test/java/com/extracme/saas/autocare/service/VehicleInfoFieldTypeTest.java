package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;
import com.extracme.saas.autocare.model.entity.MtcVehicleModel;
import com.extracme.saas.autocare.model.vo.VehicleInfoVO;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.repository.TableVehicleInfoService;
import com.extracme.saas.autocare.repository.TableVehicleModelService;
import com.extracme.saas.autocare.service.impl.VehicleInfoServiceImpl;

/**
 * 车辆信息字段类型测试
 * 验证 productLine 和 subProductLine 字段的 Integer 类型转换
 */
@ExtendWith(MockitoExtension.class)
public class VehicleInfoFieldTypeTest {

    @Mock
    private TableVehicleInfoService tableVehicleInfoService;

    @Mock
    private TableVehicleModelService tableVehicleModelService;

    @Mock
    private TableOrgInfoService tableOrgInfoService;

    @InjectMocks
    private VehicleInfoServiceImpl vehicleInfoService;

    private MtcVehicleInfo testVehicleInfo;
    private MtcVehicleModel testVehicleModel;
    private MtcOrgInfo testOrgInfo;
    private MtcOrgInfo testOperateOrgInfo;

    @BeforeEach
    void setUp() {
        // 创建测试车辆信息
        testVehicleInfo = new MtcVehicleInfo();
        testVehicleInfo.setId(1L);
        testVehicleInfo.setVin("LSGPC52U8HA123456");
        testVehicleInfo.setVehicleNo("沪A12345");
        testVehicleInfo.setVehicleModelId(1001L);
        testVehicleInfo.setVehicleOrgId("ORG001");
        testVehicleInfo.setOperationOrgId("ORG002");
        testVehicleInfo.setProductLine(2);  // Integer 类型：长租
        testVehicleInfo.setSubProductLine(4);  // Integer 类型：普通长租
        testVehicleInfo.setFactOperateTag(2);
        testVehicleInfo.setCreateTime(new Date());
        testVehicleInfo.setCreateBy("test");
        testVehicleInfo.setUpdateTime(new Date());
        testVehicleInfo.setUpdateBy("test");

        // 创建测试车型信息
        testVehicleModel = new MtcVehicleModel();
        testVehicleModel.setId(1001L);
        testVehicleModel.setVehicleModelName("奥迪A6L 2.0T");

        // 创建测试组织信息
        testOrgInfo = new MtcOrgInfo();
        testOrgInfo.setOrgId("ORG001");
        testOrgInfo.setOrgName("上海分公司");

        testOperateOrgInfo = new MtcOrgInfo();
        testOperateOrgInfo.setOrgId("ORG002");
        testOperateOrgInfo.setOrgName("浦东运营中心");
    }

    @Test
    @DisplayName("测试 productLine 和 subProductLine 字段的 Integer 类型转换")
    void testProductLineAndSubProductLineIntegerType() {
        // 设置模拟行为
        when(tableVehicleInfoService.findByVin("LSGPC52U8HA123456")).thenReturn(testVehicleInfo);
        when(tableVehicleModelService.selectById(1001L)).thenReturn(testVehicleModel);
        when(tableOrgInfoService.selectByOrgId("ORG001")).thenReturn(testOrgInfo);
        when(tableOrgInfoService.selectByOrgId("ORG002")).thenReturn(testOperateOrgInfo);

        // 执行测试
        VehicleInfoVO result = vehicleInfoService.getVehicleInfoByVin("LSGPC52U8HA123456");

        // 验证结果
        assertNotNull(result);
        
        // 验证基本字段
        assertEquals(1L, result.getId());
        assertEquals("LSGPC52U8HA123456", result.getVin());
        assertEquals("沪A12345", result.getVehicleNo());
        assertEquals(1001L, result.getVehicleModelId());
        assertEquals("奥迪A6L 2.0T", result.getVehicleModelName());
        assertEquals("ORG001", result.getVehicleOrgId());
        assertEquals("上海分公司", result.getVehicleOrgName());
        assertEquals("ORG002", result.getOperationOrgId());
        assertEquals("浦东运营中心", result.getOperationOrgName());
        assertEquals(2, result.getFactOperateTag());

        // 重点验证 productLine 和 subProductLine 字段的类型和值
        assertNotNull(result.getProductLine());
        assertNotNull(result.getSubProductLine());
        
        // 验证字段类型为 Integer
        assertTrue(result.getProductLine() instanceof Integer);
        assertTrue(result.getSubProductLine() instanceof Integer);
        
        // 验证字段值正确
        assertEquals(Integer.valueOf(2), result.getProductLine());  // 长租
        assertEquals(Integer.valueOf(4), result.getSubProductLine());  // 普通长租
        
        // 验证值的范围合理性
        assertTrue(result.getProductLine() >= 1 && result.getProductLine() <= 4);
        assertTrue(result.getSubProductLine() >= 1 && result.getSubProductLine() <= 8);
    }

    @Test
    @DisplayName("测试 productLine 和 subProductLine 为 null 的情况")
    void testProductLineAndSubProductLineNullValues() {
        // 设置 productLine 和 subProductLine 为 null
        testVehicleInfo.setProductLine(null);
        testVehicleInfo.setSubProductLine(null);

        // 设置模拟行为
        when(tableVehicleInfoService.findByVin("LSGPC52U8HA123456")).thenReturn(testVehicleInfo);
        when(tableVehicleModelService.selectById(1001L)).thenReturn(testVehicleModel);
        when(tableOrgInfoService.selectByOrgId("ORG001")).thenReturn(testOrgInfo);
        when(tableOrgInfoService.selectByOrgId("ORG002")).thenReturn(testOperateOrgInfo);

        // 执行测试
        VehicleInfoVO result = vehicleInfoService.getVehicleInfoByVin("LSGPC52U8HA123456");

        // 验证结果
        assertNotNull(result);
        
        // 验证 null 值能够正确处理
        assertNull(result.getProductLine());
        assertNull(result.getSubProductLine());
    }

    @Test
    @DisplayName("测试各种 productLine 和 subProductLine 的有效值")
    void testValidProductLineAndSubProductLineValues() {
        // 测试不同的有效值组合
        Integer[][] testCases = {
            {1, 1},  // 车管中心 - 携程短租
            {2, 4},  // 长租 - 普通长租
            {3, 2},  // 短租 - 门店短租
            {4, 8}   // 公务用车 - 网约车业务长租
        };

        for (Integer[] testCase : testCases) {
            Integer productLine = testCase[0];
            Integer subProductLine = testCase[1];

            // 设置测试数据
            testVehicleInfo.setProductLine(productLine);
            testVehicleInfo.setSubProductLine(subProductLine);

            // 设置模拟行为
            when(tableVehicleInfoService.findByVin("LSGPC52U8HA123456")).thenReturn(testVehicleInfo);
            when(tableVehicleModelService.selectById(1001L)).thenReturn(testVehicleModel);
            when(tableOrgInfoService.selectByOrgId("ORG001")).thenReturn(testOrgInfo);
            when(tableOrgInfoService.selectByOrgId("ORG002")).thenReturn(testOperateOrgInfo);

            // 执行测试
            VehicleInfoVO result = vehicleInfoService.getVehicleInfoByVin("LSGPC52U8HA123456");

            // 验证结果
            assertNotNull(result);
            assertEquals(productLine, result.getProductLine());
            assertEquals(subProductLine, result.getSubProductLine());
            
            // 验证类型
            assertTrue(result.getProductLine() instanceof Integer);
            assertTrue(result.getSubProductLine() instanceof Integer);
        }
    }

    @Test
    @DisplayName("测试字段类型转换的兼容性")
    void testFieldTypeCompatibility() {
        // 设置模拟行为
        when(tableVehicleInfoService.findByVin("LSGPC52U8HA123456")).thenReturn(testVehicleInfo);
        when(tableVehicleModelService.selectById(1001L)).thenReturn(testVehicleModel);
        when(tableOrgInfoService.selectByOrgId("ORG001")).thenReturn(testOrgInfo);
        when(tableOrgInfoService.selectByOrgId("ORG002")).thenReturn(testOperateOrgInfo);

        // 执行测试
        VehicleInfoVO result = vehicleInfoService.getVehicleInfoByVin("LSGPC52U8HA123456");

        // 验证结果
        assertNotNull(result);
        
        // 验证 BeanUtils.copyProperties 能够正确处理 Integer 到 Integer 的复制
        assertEquals(testVehicleInfo.getProductLine(), result.getProductLine());
        assertEquals(testVehicleInfo.getSubProductLine(), result.getSubProductLine());
        
        // 验证其他 Integer 类型字段也能正确复制
        assertEquals(testVehicleInfo.getFactOperateTag(), result.getFactOperateTag());
        
        // 验证 Long 类型字段能正确复制
        assertEquals(testVehicleInfo.getId(), result.getId());
        assertEquals(testVehicleInfo.getVehicleModelId(), result.getVehicleModelId());
        
        // 验证 String 类型字段能正确复制
        assertEquals(testVehicleInfo.getVin(), result.getVin());
        assertEquals(testVehicleInfo.getVehicleNo(), result.getVehicleNo());
        assertEquals(testVehicleInfo.getVehicleOrgId(), result.getVehicleOrgId());
        assertEquals(testVehicleInfo.getOperationOrgId(), result.getOperationOrgId());
    }
}
