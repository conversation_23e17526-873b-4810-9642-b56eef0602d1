package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Date;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import com.extracme.saas.autocare.model.entity.SmsSendRecord;
import com.extracme.saas.autocare.model.entity.SysVerificationCode;
import com.extracme.saas.autocare.repository.TableSmsSendRecordService;
import com.extracme.saas.autocare.repository.TableSysVerificationCodeService;

/**
 * 短信服务测试类
 */
public class SmsServiceImplTest {

    @InjectMocks
    private SmsServiceImpl smsService;
    
    @Mock
    private TableSysVerificationCodeService verificationCodeService;
    
    @Mock
    private TableSmsSendRecordService smsSendRecordService;
    
    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        
        // 设置配置属性
        ReflectionTestUtils.setField(smsService, "codeExpiration", 300);
        ReflectionTestUtils.setField(smsService, "sendInterval", 60);
        ReflectionTestUtils.setField(smsService, "dailyLimitMobile", 10);
        ReflectionTestUtils.setField(smsService, "dailyLimitIP", 20);
    }
    
    /**
     * 测试发送登录验证码 - 成功场景
     */
    @Test
    public void testSendLoginVerificationCode_Success() {
        // 准备测试数据
        String mobile = "13800138000";
        String ipAddress = "127.0.0.1";
        
        // 模拟最后一次发送时间是1小时前
        Date lastSendTime = new Date(System.currentTimeMillis() - 3600 * 1000);
        when(smsSendRecordService.findLastSendTime(eq(mobile), anyString())).thenReturn(lastSendTime);
        
        // 模拟当天发送次数
        when(smsSendRecordService.countByPhoneAndTime(eq(mobile), anyString(), any(Date.class))).thenReturn(5);
        when(smsSendRecordService.countByIpAndTime(eq(ipAddress), anyString(), any(Date.class))).thenReturn(5);
        
        // 模拟保存验证码和记录
        SysVerificationCode mockVerificationCode = new SysVerificationCode();
        mockVerificationCode.setId(1L);
        when(verificationCodeService.insert(any(SysVerificationCode.class))).thenReturn(mockVerificationCode);
        when(smsSendRecordService.save(any(SmsSendRecord.class))).thenReturn(1L);
        
        // 执行测试
        boolean result = smsService.sendLoginVerificationCode(mobile, ipAddress);
        
        // 验证结果
        assertTrue(result);
        verify(verificationCodeService, times(1)).insert(any(SysVerificationCode.class));
        verify(smsSendRecordService, times(1)).save(any(SmsSendRecord.class));
    }
    
    /**
     * 测试发送登录验证码 - 手机号格式错误
     */
    @Test
    public void testSendLoginVerificationCode_InvalidMobile() {
        // 准备测试数据
        String mobile = "1380013800"; // 手机号不足11位
        String ipAddress = "127.0.0.1";
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            smsService.sendLoginVerificationCode(mobile, ipAddress);
        });
        
        assertEquals("手机号格式不正确", exception.getMessage());
        verify(verificationCodeService, never()).insert(any(SysVerificationCode.class));
        verify(smsSendRecordService, never()).save(any(SmsSendRecord.class));
    }
    
    /**
     * 测试发送登录验证码 - 发送频率过高（30秒内重复发送）
     */
    @Test
    public void testSendLoginVerificationCode_TooFrequent() {
        // 准备测试数据
        String mobile = "13800138000";
        String ipAddress = "127.0.0.1";
        
        // 模拟最后一次发送时间是30秒前
        Date lastSendTime = new Date(System.currentTimeMillis() - 30 * 1000);
        when(smsSendRecordService.findLastSendTime(eq(mobile), anyString())).thenReturn(lastSendTime);
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            smsService.sendLoginVerificationCode(mobile, ipAddress);
        });
        
        assertEquals("请求过于频繁，请在30秒后重试", exception.getMessage());
        verify(verificationCodeService, never()).insert(any(SysVerificationCode.class));
        verify(smsSendRecordService, never()).save(any(SmsSendRecord.class));
    }
    
    /**
     * 测试发送登录验证码 - 发送间隔正常（60秒后重新发送）
     */
    @Test
    public void testSendLoginVerificationCode_NormalInterval() {
        // 准备测试数据
        String mobile = "13800138000";
        String ipAddress = "127.0.0.1";
        
        // 模拟最后一次发送时间是70秒前（大于60秒限制）
        Date lastSendTime = new Date(System.currentTimeMillis() - 70 * 1000);
        when(smsSendRecordService.findLastSendTime(eq(mobile), anyString())).thenReturn(lastSendTime);
        
        // 模拟当天发送次数
        when(smsSendRecordService.countByPhoneAndTime(eq(mobile), anyString(), any(Date.class))).thenReturn(5);
        when(smsSendRecordService.countByIpAndTime(eq(ipAddress), anyString(), any(Date.class))).thenReturn(5);
        
        // 模拟保存验证码和记录
        SysVerificationCode mockVerificationCode = new SysVerificationCode();
        mockVerificationCode.setId(1L);
        when(verificationCodeService.insert(any(SysVerificationCode.class))).thenReturn(mockVerificationCode);
        when(smsSendRecordService.save(any(SmsSendRecord.class))).thenReturn(1L);
        
        // 执行测试
        boolean result = smsService.sendLoginVerificationCode(mobile, ipAddress);
        
        // 验证结果
        assertTrue(result);
        verify(verificationCodeService, times(1)).insert(any(SysVerificationCode.class));
        verify(smsSendRecordService, times(1)).save(any(SmsSendRecord.class));
    }
    
    /**
     * 测试验证码验证 - 成功场景
     */
    @Test
    public void testVerifyCode_Success() {
        // 准备测试数据
        String mobile = "13800138000";
        String code = "123456";
        String type = "LOGIN";
        
        // 模拟验证码查询结果
        SysVerificationCode verificationCode = new SysVerificationCode();
        verificationCode.setId(1L);
        verificationCode.setMobile(mobile);
        verificationCode.setCode(code);
        verificationCode.setType(type);
        verificationCode.setFailCount(0); // 设置错误次数为0
        
        // 设置过期时间为1小时后
        Date expireTime = new Date(System.currentTimeMillis() + 3600 * 1000);
        verificationCode.setExpireTime(expireTime);
        
        when(verificationCodeService.findLatestUnused(eq(mobile), eq(type)))
            .thenReturn(Optional.of(verificationCode));
        
        // 执行测试
        boolean result = smsService.verifyCode(mobile, code, type);
        
        // 验证结果
        assertTrue(result);
        verify(verificationCodeService, never()).incrementFailCount(any()); // 验证成功时不应该增加错误次数
    }
    
    /**
     * 测试验证码验证 - 验证码不正确
     */
    @Test
    public void testVerifyCode_WrongCode() {
        // 准备测试数据
        String mobile = "13800138000";
        String correctCode = "123456";
        String wrongCode = "654321";
        String type = "LOGIN";
        
        // 模拟验证码查询结果
        SysVerificationCode verificationCode = new SysVerificationCode();
        verificationCode.setId(1L);
        verificationCode.setMobile(mobile);
        verificationCode.setCode(correctCode); // 设置正确的验证码
        verificationCode.setType(type);
        verificationCode.setFailCount(0); // 初始错误次数为0
        
        // 设置过期时间为1小时后
        Date expireTime = new Date(System.currentTimeMillis() + 3600 * 1000);
        verificationCode.setExpireTime(expireTime);
        
        when(verificationCodeService.findLatestUnused(eq(mobile), eq(type)))
            .thenReturn(Optional.of(verificationCode));
        when(verificationCodeService.incrementFailCount(eq(1L))).thenReturn(true);
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            smsService.verifyCode(mobile, wrongCode, type);
        });
        
        assertEquals("验证码错误", exception.getMessage());
        verify(verificationCodeService, times(1)).incrementFailCount(eq(1L));
    }
    
    /**
     * 测试验证码验证 - 验证码已过期
     */
    @Test
    public void testVerifyCode_Expired() {
        // 准备测试数据
        String mobile = "13800138000";
        String code = "123456";
        String type = "LOGIN";
        
        // 模拟验证码查询结果
        SysVerificationCode verificationCode = new SysVerificationCode();
        verificationCode.setId(1L);
        verificationCode.setMobile(mobile);
        verificationCode.setCode(code);
        verificationCode.setType(type);
        
        // 设置过期时间为1小时前
        Date expireTime = new Date(System.currentTimeMillis() - 3600 * 1000);
        verificationCode.setExpireTime(expireTime);
        
        when(verificationCodeService.findLatestUnused(eq(mobile), eq(type)))
            .thenReturn(Optional.of(verificationCode));
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            smsService.verifyCode(mobile, code, type);
        });
        
        assertEquals("验证码已过期", exception.getMessage());
    }
    
    /**
     * 测试标记验证码为已使用 - 成功场景
     */
    @Test
    public void testMarkCodeAsUsed_Success() {
        // 准备测试数据
        String mobile = "13800138000";
        String code = "123456";
        String type = "LOGIN";
        
        // 模拟验证码查询结果
        SysVerificationCode verificationCode = new SysVerificationCode();
        verificationCode.setId(1L);
        verificationCode.setMobile(mobile);
        verificationCode.setCode(code);
        verificationCode.setType(type);
        
        when(verificationCodeService.findLatestUnused(eq(mobile), eq(type)))
            .thenReturn(Optional.of(verificationCode));
        when(verificationCodeService.updateStatus(eq(1L), eq(true))).thenReturn(true);
        
        // 执行测试
        boolean result = smsService.markCodeAsUsed(mobile, code, type);
        
        // 验证结果
        assertTrue(result);
        verify(verificationCodeService, times(1)).updateStatus(eq(1L), eq(true));
    }
    
    /**
     * 测试清理过期验证码
     */
    @Test
    public void testCleanExpiredCodes() {
        // 模拟标记过期验证码
        when(verificationCodeService.markExpiredCodes(any(Date.class))).thenReturn(5);
        
        // 执行测试
        int result = smsService.cleanExpiredCodes();
        
        // 验证结果
        assertEquals(5, result);
        verify(verificationCodeService, times(1)).markExpiredCodes(any(Date.class));
        verify(verificationCodeService, times(1)).deleteOldCodes(eq(7));
        verify(smsSendRecordService, times(1)).deleteOldRecords(eq(7));
    }
    
    /**
     * 测试发送登录验证码 - 每日手机号限制
     */
    @Test
    public void testSendLoginVerificationCode_DailyMobileLimitExceeded() {
        // 准备测试数据
        String mobile = "13800138000";
        String ipAddress = "127.0.0.1";
        
        // 模拟最后一次发送时间是1小时前
        Date lastSendTime = new Date(System.currentTimeMillis() - 3600 * 1000);
        when(smsSendRecordService.findLastSendTime(eq(mobile), anyString())).thenReturn(lastSendTime);
        
        // 模拟当天发送次数达到限制（10次）
        when(smsSendRecordService.countByPhoneAndTime(eq(mobile), anyString(), any(Date.class))).thenReturn(10);
        when(smsSendRecordService.countByIpAndTime(eq(ipAddress), anyString(), any(Date.class))).thenReturn(5);
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            smsService.sendLoginVerificationCode(mobile, ipAddress);
        });
        
        assertEquals("当天发送次数已达上限，请明天再试", exception.getMessage());
        verify(verificationCodeService, never()).insert(any(SysVerificationCode.class));
        verify(smsSendRecordService, never()).save(any(SmsSendRecord.class));
    }
    
    /**
     * 测试发送登录验证码 - 每日IP限制
     */
    @Test
    public void testSendLoginVerificationCode_DailyIPLimitExceeded() {
        // 准备测试数据
        String mobile = "13800138000";
        String ipAddress = "127.0.0.1";
        
        // 模拟最后一次发送时间是1小时前
        Date lastSendTime = new Date(System.currentTimeMillis() - 3600 * 1000);
        when(smsSendRecordService.findLastSendTime(eq(mobile), anyString())).thenReturn(lastSendTime);
        
        // 模拟当天发送次数：手机号未超限，IP超限
        when(smsSendRecordService.countByPhoneAndTime(eq(mobile), anyString(), any(Date.class))).thenReturn(5);
        when(smsSendRecordService.countByIpAndTime(eq(ipAddress), anyString(), any(Date.class))).thenReturn(20);
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            smsService.sendLoginVerificationCode(mobile, ipAddress);
        });
        
        assertEquals("当前IP发送次数过多，请稍后再试", exception.getMessage());
        verify(verificationCodeService, never()).insert(any(SysVerificationCode.class));
        verify(smsSendRecordService, never()).save(any(SmsSendRecord.class));
    }

    /**
     * 测试验证码错误次数限制 - 连续3次错误后锁定
     */
    @Test
    public void testVerifyCode_ErrorCountLimit() {
        // 准备测试数据
        String mobile = "13800138000";
        String correctCode = "123456";
        String wrongCode = "000000";
        String type = "LOGIN";
        
        // 模拟验证码查询结果
        SysVerificationCode verificationCode = new SysVerificationCode();
        verificationCode.setId(1L);
        verificationCode.setMobile(mobile);
        verificationCode.setCode(correctCode);
        verificationCode.setType(type);
        verificationCode.setFailCount(2); // 已经错误2次
        verificationCode.setStatus(0); // 未使用
        
        // 设置过期时间为1小时后
        Date expireTime = new Date(System.currentTimeMillis() + 3600 * 1000);
        verificationCode.setExpireTime(expireTime);
        
        when(verificationCodeService.findLatestUnused(eq(mobile), eq(type)))
            .thenReturn(Optional.of(verificationCode));
        when(verificationCodeService.incrementFailCount(eq(1L))).thenReturn(true);
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            smsService.verifyCode(mobile, wrongCode, type);
        });
        
        assertEquals("验证码错误次数过多，请15分钟后重试", exception.getMessage());
        verify(verificationCodeService, times(1)).incrementFailCount(eq(1L));
    }

    /**
     * 测试验证码重复使用
     */
    @Test
    public void testVerifyCode_AlreadyUsed() {
        // 准备测试数据
        String mobile = "13800138000";
        String code = "123456";
        String type = "LOGIN";
        
        // 模拟验证码查询结果 - 因为已使用，所以找不到未使用的验证码
        when(verificationCodeService.findLatestUnused(eq(mobile), eq(type)))
            .thenReturn(Optional.empty());
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            smsService.verifyCode(mobile, code, type);
        });
        
        assertEquals("验证码已失效", exception.getMessage());
    }

    /**
     * 测试验证码5分钟后过期
     */
    @Test
    public void testVerifyCode_ExpirationTime() {
        // 准备测试数据
        String mobile = "13800138000";
        String code = "123456";
        String type = "LOGIN";
        
        // 模拟验证码查询结果
        SysVerificationCode verificationCode = new SysVerificationCode();
        verificationCode.setId(1L);
        verificationCode.setMobile(mobile);
        verificationCode.setCode(code);
        verificationCode.setType(type);
        verificationCode.setStatus(0); // 未使用
        
        // 设置过期时间为6分钟前（超过5分钟有效期）
        Date expireTime = new Date(System.currentTimeMillis() - 6 * 60 * 1000);
        verificationCode.setExpireTime(expireTime);
        
        when(verificationCodeService.findLatestUnused(eq(mobile), eq(type)))
            .thenReturn(Optional.of(verificationCode));
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            smsService.verifyCode(mobile, code, type);
        });
        
        assertEquals("验证码已过期", exception.getMessage());
    }
} 