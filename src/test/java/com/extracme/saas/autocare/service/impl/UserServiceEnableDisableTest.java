package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableRepairDepotInfoService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 用户启用/禁用功能测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("用户启用/禁用功能测试")
class UserServiceEnableDisableTest {

    @Mock
    private TableUserService tableUserService;

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TableUserRoleService tableUserRoleService;

    @Mock
    private TableTenantService tableTenantService;

    @Mock
    private TableRepairDepotInfoService tableRepairDepotInfoService;

    @InjectMocks
    private UserServiceImpl userService;

    private SysUser mockUser;
    private LoginUser mockLoginUser;

    @BeforeEach
    void setUp() {
        // 创建模拟用户
        mockUser = new SysUser();
        mockUser.setId(1L);
        mockUser.setMobile("***********");
        mockUser.setNickname("张三");
        mockUser.setStatus(1); // 启用状态
        mockUser.setTenantId(100L);

        // 创建模拟登录用户
        mockLoginUser = new LoginUser();
        SysUser loginUserEntity = new SysUser();
        loginUserEntity.setId(2L);
        loginUserEntity.setTenantId(100L);
        loginUserEntity.setAccountType(1); // 普通用户
        loginUserEntity.setUsername("admin");
        mockLoginUser.setUser(loginUserEntity);
    }

    @Test
    @DisplayName("启用用户 - 成功")
    void enableUser_Success() {
        // 设置用户当前状态为禁用
        mockUser.setStatus(0);

        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为普通用户
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);

            // 设置模拟行为
            when(tableUserService.selectById(1L)).thenReturn(mockUser);
            when(tableUserService.updateSelectiveById(any(SysUser.class), anyString())).thenReturn(1);

            // 执行测试
            assertDoesNotThrow(() -> userService.enableUser(1L));

            // 验证调用
            verify(tableUserService).selectById(1L);
            verify(tableUserService).updateSelectiveById(argThat(user -> 
                user.getId().equals(1L) && user.getStatus().equals(1)
            ), eq("admin"));
        }
    }

    @Test
    @DisplayName("禁用用户 - 成功")
    void disableUser_Success() {
        // 设置用户当前状态为启用
        mockUser.setStatus(1);

        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为普通用户
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);

            // 设置模拟行为
            when(tableUserService.selectById(1L)).thenReturn(mockUser);
            when(tableUserService.updateSelectiveById(any(SysUser.class), anyString())).thenReturn(1);

            // 执行测试
            assertDoesNotThrow(() -> userService.disableUser(1L));

            // 验证调用
            verify(tableUserService).selectById(1L);
            verify(tableUserService).updateSelectiveById(argThat(user -> 
                user.getId().equals(1L) && user.getStatus().equals(0)
            ), eq("admin"));
        }
    }

    @Test
    @DisplayName("启用用户 - 用户不存在")
    void enableUser_UserNotFound() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);

            // 设置模拟行为 - 用户不存在
            when(tableUserService.selectById(1L)).thenReturn(null);

            // 执行测试并验证异常
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> userService.enableUser(1L));
            
            assertEquals("用户不存在", exception.getMessage());
        }
    }

    @Test
    @DisplayName("启用用户 - 无权限操作其他租户用户")
    void enableUser_NoPermissionForOtherTenant() {
        // 设置目标用户属于不同租户
        mockUser.setTenantId(200L);

        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);

            // 设置模拟行为
            when(tableUserService.selectById(1L)).thenReturn(mockUser);

            // 执行测试并验证异常
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> userService.enableUser(1L));
            
            assertEquals("无权限操作其他租户的用户", exception.getMessage());
        }
    }

    @Test
    @DisplayName("超级管理员启用其他租户用户 - 成功")
    void enableUser_SuperAdminSuccess() {
        // 设置目标用户属于不同租户
        mockUser.setTenantId(200L);
        mockUser.setStatus(0); // 禁用状态

        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为超级管理员
            mockLoginUser.getUser().setAccountType(0);
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(true);

            // 设置模拟行为
            when(tableUserService.selectById(1L)).thenReturn(mockUser);
            when(tableUserService.updateSelectiveById(any(SysUser.class), anyString())).thenReturn(1);

            // 执行测试
            assertDoesNotThrow(() -> userService.enableUser(1L));

            // 验证调用
            verify(tableUserService).selectById(1L);
            verify(tableUserService).updateSelectiveById(argThat(user -> 
                user.getId().equals(1L) && user.getStatus().equals(1)
            ), eq("admin"));
        }
    }

    @Test
    @DisplayName("启用用户 - 状态无需更新")
    void enableUser_StatusNoChange() {
        // 设置用户当前状态已经是启用
        mockUser.setStatus(1);

        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);

            // 设置模拟行为
            when(tableUserService.selectById(1L)).thenReturn(mockUser);

            // 执行测试
            assertDoesNotThrow(() -> userService.enableUser(1L));

            // 验证不会调用更新方法
            verify(tableUserService).selectById(1L);
            verify(tableUserService, never()).updateSelectiveById(any(SysUser.class), anyString());
        }
    }

    @Test
    @DisplayName("启用用户 - 参数为空")
    void enableUser_NullUserId() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);

            // 执行测试并验证异常
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> userService.enableUser(null));
            
            assertEquals("用户ID不能为空", exception.getMessage());
        }
    }

    @Test
    @DisplayName("启用用户 - 更新失败")
    void enableUser_UpdateFailed() {
        // 设置用户当前状态为禁用
        mockUser.setStatus(0);

        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);

            // 设置模拟行为 - 更新失败
            when(tableUserService.selectById(1L)).thenReturn(mockUser);
            when(tableUserService.updateSelectiveById(any(SysUser.class), anyString())).thenReturn(0);

            // 执行测试并验证异常
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> userService.enableUser(1L));
            
            assertEquals("启用用户失败", exception.getMessage());
        }
    }
}
