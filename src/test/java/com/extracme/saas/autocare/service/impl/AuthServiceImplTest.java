package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.SmsService;
import com.extracme.saas.autocare.util.JwtUtil;
import com.extracme.saas.autocare.util.OrgHierarchyUtils;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;

/**
 * 身份认证服务测试类
 */
public class AuthServiceImplTest {

    @InjectMocks
    private AuthServiceImpl authService;
    
    @Mock
    private SmsService smsService;
    
    @Mock
    private TableUserService userService;
    
    @Mock
    private TableRoleService roleService;
    
    @Mock
    private TablePermissionService permissionService;

    @Mock
    private TableTenantService tenantService;

    @Mock
    private TableUserOrgService userOrgService;

    @Mock
    private OrgHierarchyUtils orgHierarchyUtils;

    @Mock
    private UserPermissionCacheUtils userPermissionCacheUtils;

    @Mock
    private JwtUtil jwtUtil;
    
    private static final String TEST_MOBILE = "13800138000";
    private static final String TEST_CODE = "123456";
    private static final String TEST_TOKEN = "test.jwt.token";
    private static final Long TEST_USER_ID = 1L;
    private static final String TEST_PERMISSION_CODE = "test:permission";
    
    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(authService, "jwtExpiration", 86400);
        
        // 默认token验证通过
        when(jwtUtil.validateToken(TEST_TOKEN)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(TEST_TOKEN)).thenReturn(String.valueOf(TEST_USER_ID));
    }
    
    /**
     * 测试手机号验证码登录 - 成功场景
     */
    @Test
    public void testLoginByMobile_Success() {
        // 准备测试数据
        SysUser user = new SysUser();
        user.setId(1L);
        user.setUsername("testUser");
        user.setMobile(TEST_MOBILE);
        user.setStatus(1);

        // Mock验证码验证
        when(smsService.verifyCode(eq(TEST_MOBILE), eq(TEST_CODE), anyString())).thenReturn(true);

        // Mock用户查询 - 新增 findAllByMobile 方法的 mock
        when(userService.findAllByMobile(TEST_MOBILE)).thenReturn(Arrays.asList(user));
        when(userService.findByMobile(TEST_MOBILE)).thenReturn(Optional.of(user));

        // Mock 权限和角色查询
        when(permissionService.findByUserId(1L)).thenReturn(Collections.emptyList());
        when(userOrgService.findOrgIdsByUserId(1L)).thenReturn(Collections.emptyList());
        when(orgHierarchyUtils.calculateAllAccessibleOrgIds(any())).thenReturn(Collections.emptyList());
        when(userPermissionCacheUtils.cacheUserPermissions(any(), any(), any(), any(), any())).thenReturn(true);

        // Mock JWT生成 - 使用新的 generateTokenWithUserInfo 方法
        when(jwtUtil.generateTokenWithUserInfo(any())).thenReturn(TEST_TOKEN);
        
        // 执行测试
        TokenDTO result = authService.loginByMobile(TEST_MOBILE, TEST_CODE);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(TEST_TOKEN, result.getAccessToken());
        assertEquals(user.getId(), result.getUserId());
        assertEquals(user.getUsername(), result.getUsername());
        assertEquals(user.getMobile(), result.getMobile());
        assertNotNull(result.getExpireTime());
        
        // 验证方法调用
        verify(smsService).markCodeAsUsed(eq(TEST_MOBILE), eq(TEST_CODE), anyString());
    }
    
    /**
     * 测试手机号验证码登录 - 验证码错误
     */
    @Test
    public void testLoginByMobile_InvalidCode() {
        // Mock验证码验证失败
        when(smsService.verifyCode(eq(TEST_MOBILE), eq(TEST_CODE), anyString())).thenReturn(false);
        
        // 执行测试
        TokenDTO result = authService.loginByMobile(TEST_MOBILE, TEST_CODE);
        
        // 验证结果
        assertNull(result);
    }
    
    /**
     * 测试手机号验证码登录 - 用户不存在
     */
    @Test
    public void testLoginByMobile_UserNotFound() {
        // Mock验证码验证成功
        when(smsService.verifyCode(eq(TEST_MOBILE), eq(TEST_CODE), anyString())).thenReturn(true);

        // Mock用户不存在
        when(userService.findAllByMobile(TEST_MOBILE)).thenReturn(Collections.emptyList());
        when(userService.findByMobile(TEST_MOBILE)).thenReturn(Optional.empty());
        
        // 执行测试
        TokenDTO result = authService.loginByMobile(TEST_MOBILE, TEST_CODE);
        
        // 验证结果
        assertNull(result);
    }
    
    /**
     * 测试手机号验证码登录 - 用户已禁用
     */
    @Test
    public void testLoginByMobile_UserDisabled() {
        // 准备测试数据
        SysUser user = new SysUser();
        user.setId(1L);
        user.setUsername("testUser");
        user.setMobile(TEST_MOBILE);
        user.setStatus(0); // 禁用状态
        
        // Mock验证码验证
        when(smsService.verifyCode(eq(TEST_MOBILE), eq(TEST_CODE), anyString())).thenReturn(true);

        // Mock用户查询 - 添加 findAllByMobile 方法的 mock
        when(userService.findAllByMobile(TEST_MOBILE)).thenReturn(Arrays.asList(user));
        when(userService.findByMobile(TEST_MOBILE)).thenReturn(Optional.of(user));
        
        // 执行测试
        TokenDTO result = authService.loginByMobile(TEST_MOBILE, TEST_CODE);
        
        // 验证结果
        assertNull(result);
    }
    
    /**
     * 测试token验证
     */
    @Test
    public void testValidateToken() {
        // Mock token验证
        when(jwtUtil.validateToken(TEST_TOKEN)).thenReturn(true);
        
        // 执行测试
        boolean result = authService.validateToken(TEST_TOKEN);
        
        // 验证结果
        assertTrue(result);
    }
    
    /**
     * 测试从token获取用户ID
     */
    @Test
    public void testGetUserIdFromToken() {
        // Mock获取用户ID
        when(jwtUtil.getUserIdFromToken(TEST_TOKEN)).thenReturn(String.valueOf(TEST_USER_ID));
        
        // 执行测试
        String result = authService.getUserIdFromToken(TEST_TOKEN);
        
        // 验证结果
        assertEquals("1", result);
    }
    
    /**
     * 测试用户权限验证 - 有权限
     */
    @Test
    public void testHasPermission_Success() {
        // 准备测试用户数据
        SysUser user = new SysUser();
        user.setId(TEST_USER_ID);
        user.setAccountType(1); // 普通用户

        // Mock JWT解析、用户查询和权限查询
        when(jwtUtil.validateToken(TEST_TOKEN)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(TEST_TOKEN)).thenReturn(TEST_USER_ID.toString());
        when(userService.selectById(TEST_USER_ID)).thenReturn(user);
        when(permissionService.hasPermission(TEST_USER_ID, TEST_PERMISSION_CODE)).thenReturn(true);

        // 执行测试
        boolean result = authService.hasPermission(TEST_TOKEN, TEST_PERMISSION_CODE);

        // 验证结果
        assertTrue(result);
        verify(permissionService).hasPermission(TEST_USER_ID, TEST_PERMISSION_CODE);
    }
    
    /**
     * 测试用户权限验证 - 无权限
     */
    @Test
    public void testHasPermission_Failure() {
        // 准备测试用户数据
        SysUser user = new SysUser();
        user.setId(TEST_USER_ID);
        user.setAccountType(1); // 普通用户

        // Mock JWT解析、用户查询和权限查询
        when(jwtUtil.validateToken(TEST_TOKEN)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(TEST_TOKEN)).thenReturn(TEST_USER_ID.toString());
        when(userService.selectById(TEST_USER_ID)).thenReturn(user);
        when(permissionService.hasPermission(TEST_USER_ID, TEST_PERMISSION_CODE)).thenReturn(false);

        // 执行测试
        boolean result = authService.hasPermission(TEST_TOKEN, TEST_PERMISSION_CODE);

        // 验证结果
        assertFalse(result);
        verify(permissionService).hasPermission(TEST_USER_ID, TEST_PERMISSION_CODE);
    }
    
    /**
     * 测试用户权限验证 - Token无效
     */
    @Test
    public void testHasPermission_InvalidToken() {
        // Mock token验证失败
        when(jwtUtil.validateToken(TEST_TOKEN)).thenReturn(false);
        
        // 执行测试
        boolean result = authService.hasPermission(TEST_TOKEN, TEST_PERMISSION_CODE);
        
        // 验证结果
        assertFalse(result);
        verify(jwtUtil).validateToken(TEST_TOKEN);
        verify(jwtUtil, never()).getUserIdFromToken(any());
        verify(permissionService, never()).hasPermission(any(), any());
    }
    
    /**
     * 测试获取用户角色列表
     */
    @Test
    public void testGetUserRoles() {
        // 准备测试数据
        List<SysRole> roles = Arrays.asList(
            createTestRole(1L, "ADMIN", "系统管理员"),
            createTestRole(2L, "USER", "普通用户")
        );
        
        // Mock JWT解析和角色查询
        when(jwtUtil.getUserIdFromToken(TEST_TOKEN)).thenReturn(TEST_USER_ID.toString());
        when(roleService.findByUserId(TEST_USER_ID)).thenReturn(roles);

        // 执行测试
        List<SysRole> result = authService.getUserRoles(TEST_TOKEN);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("ADMIN", result.get(0).getRoleCode());
        assertEquals("USER", result.get(1).getRoleCode());
        verify(roleService).findByUserId(TEST_USER_ID);
    }
    
    /**
     * 测试获取用户权限列表
     */
    @Test
    public void testGetUserPermissions() {
        // 准备测试数据
        List<SysPermission> permissions = Arrays.asList(
            createTestPermission(1L, "system:user:view", "查看用户"),
            createTestPermission(2L, "system:user:edit", "编辑用户")
        );
        
        // 准备测试用户数据
        SysUser user = new SysUser();
        user.setId(TEST_USER_ID);
        user.setAccountType(1); // 普通用户

        // Mock JWT解析和用户查询
        when(jwtUtil.getUserIdFromToken(TEST_TOKEN)).thenReturn(TEST_USER_ID.toString());
        when(userService.selectById(TEST_USER_ID)).thenReturn(user);
        when(permissionService.findByUserId(TEST_USER_ID)).thenReturn(permissions);

        // 执行测试
        List<SysPermission> result = authService.getUserPermissions(TEST_TOKEN);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("system:user:view", result.get(0).getPermissionCode());
        assertEquals("system:user:edit", result.get(1).getPermissionCode());
        verify(permissionService).findByUserId(TEST_USER_ID);
    }
    
    /**
     * 测试用户是否具有指定角色
     */
    @Test
    public void testHasRole() {
        // 准备测试数据
        String roleCode = "ADMIN";
        
        // Mock角色查询
        when(roleService.hasRole(TEST_USER_ID, roleCode)).thenReturn(true);
        
        // 执行测试
        boolean result = authService.hasRole(TEST_TOKEN, roleCode);
        
        // 验证结果
        assertTrue(result);
        verify(roleService).hasRole(TEST_USER_ID, roleCode);
    }
    
    // 辅助方法
    private SysRole createTestRole(Long id, String code, String name) {
        SysRole role = new SysRole();
        role.setId(id);
        role.setRoleCode(code);
        role.setRoleName(name);
        return role;
    }
    
    private SysPermission createTestPermission(Long id, String code, String name) {
        SysPermission permission = new SysPermission();
        permission.setId(id);
        permission.setPermissionCode(code);
        permission.setPermissionName(name);
        return permission;
    }
} 