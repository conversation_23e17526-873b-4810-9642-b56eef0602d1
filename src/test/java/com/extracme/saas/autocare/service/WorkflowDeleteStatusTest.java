package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.reflect.Method;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.model.entity.ActivityInstance;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableActivityInstanceService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.impl.WorkflowServiceImpl;

/**
 * 工作流删除状态功能测试
 */
@ExtendWith(MockitoExtension.class)
public class WorkflowDeleteStatusTest {

    private static final Logger log = LoggerFactory.getLogger(WorkflowDeleteStatusTest.class);

    @Mock
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Mock
    private TableActivityInstanceService tableActivityInstanceService;

    @InjectMocks
    private WorkflowServiceImpl workflowService;

    private ActivityInstance testActivityInstance;
    private WorkflowInstance testWorkflowInstance;

    @BeforeEach
    void setUp() {
        // 创建测试用的活动实例
        testActivityInstance = new ActivityInstance();
        testActivityInstance.setId(1L);
        testActivityInstance.setInstanceId(100L);
        testActivityInstance.setCurrentStatusCode(ActivityStatusEnum.PROCESSING.getCode());

        // 创建测试用的工作流实例
        testWorkflowInstance = new WorkflowInstance();
        testWorkflowInstance.setId(100L);
        testWorkflowInstance.setBusinessId("TEST_BUSINESS_001");
        testWorkflowInstance.setCurrentActivityCode("VEHICLE_TRANSFER");
        testWorkflowInstance.setStatusCode(ActivityStatusEnum.PROCESSING.getCode());
    }

    @Test
    @DisplayName("测试DELETE状态枚举")
    void testDeleteStatusEnum() {
        // 验证DELETE状态枚举的基本属性
        assertEquals("DELETE", ActivityStatusEnum.DELETE.getCode());
        assertEquals("已删除", ActivityStatusEnum.DELETE.getDescription());
        
        // 验证可以通过代码查找到DELETE状态
        assertEquals(ActivityStatusEnum.DELETE, ActivityStatusEnum.getByCode("DELETE"));
        
        // 验证DELETE状态码有效性
        assertTrue(ActivityStatusEnum.isValidCode("DELETE"));
        
        log.info("DELETE状态枚举测试通过");
    }

    @Test
    @DisplayName("测试活动实例删除功能 - 成功场景")
    void testHandleActivityInstanceDeletion_Success() throws Exception {
        // 模拟查询活动实例成功
        when(tableActivityInstanceService.selectById(1L)).thenReturn(testActivityInstance);

        // 模拟删除操作成功
        when(tableActivityInstanceService.deleteById(1L)).thenReturn(1);

        // 使用反射调用私有方法
        Method method = WorkflowServiceImpl.class.getDeclaredMethod("handleActivityInstanceDeletion", ActivityInstance.class);
        method.setAccessible(true);

        boolean result = (boolean) method.invoke(workflowService, testActivityInstance);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(tableActivityInstanceService).selectById(1L);
        verify(tableActivityInstanceService).deleteById(1L);

        log.info("活动实例删除成功场景测试通过");
    }

    @Test
    @DisplayName("测试活动实例删除功能 - 实例不存在")
    void testHandleActivityInstanceDeletion_InstanceNotFound() throws Exception {
        // 模拟查询活动实例返回null
        when(tableActivityInstanceService.selectById(1L)).thenReturn(null);

        // 使用反射调用私有方法
        Method method = WorkflowServiceImpl.class.getDeclaredMethod("handleActivityInstanceDeletion", ActivityInstance.class);
        method.setAccessible(true);

        boolean result = (boolean) method.invoke(workflowService, testActivityInstance);

        // 验证结果 - 实例不存在视为删除成功
        assertTrue(result);

        // 验证只调用了查询，没有调用删除
        verify(tableActivityInstanceService).selectById(1L);
        verify(tableActivityInstanceService, never()).deleteById(any());

        log.info("活动实例不存在场景测试通过");
    }

    @Test
    @DisplayName("测试活动实例删除功能 - 活动实例ID为空")
    void testHandleActivityInstanceDeletion_NullInstanceId() throws Exception {
        // 设置活动实例的ID为空
        testActivityInstance.setId(null);

        // 使用反射调用私有方法
        Method method = WorkflowServiceImpl.class.getDeclaredMethod("handleActivityInstanceDeletion", ActivityInstance.class);
        method.setAccessible(true);

        boolean result = (boolean) method.invoke(workflowService, testActivityInstance);

        // 验证结果 - 应该返回false
        assertFalse(result);

        // 验证没有调用任何服务方法
        verify(tableActivityInstanceService, never()).selectById(any());
        verify(tableActivityInstanceService, never()).deleteById(any());

        log.info("活动实例ID为空场景测试通过");
    }

    @Test
    @DisplayName("测试活动实例删除功能 - 删除失败")
    void testHandleActivityInstanceDeletion_DeleteFailed() throws Exception {
        // 模拟查询活动实例成功
        when(tableActivityInstanceService.selectById(1L)).thenReturn(testActivityInstance);

        // 模拟删除操作失败（返回0）
        when(tableActivityInstanceService.deleteById(1L)).thenReturn(0);

        // 使用反射调用私有方法
        Method method = WorkflowServiceImpl.class.getDeclaredMethod("handleActivityInstanceDeletion", ActivityInstance.class);
        method.setAccessible(true);

        boolean result = (boolean) method.invoke(workflowService, testActivityInstance);

        // 验证结果 - 应该返回false
        assertFalse(result);

        // 验证方法调用
        verify(tableActivityInstanceService).selectById(1L);
        verify(tableActivityInstanceService).deleteById(1L);

        log.info("活动实例删除失败场景测试通过");
    }

    @Test
    @DisplayName("测试DELETE状态检查逻辑")
    void testDeleteStatusCheck() {
        // 测试DELETE状态检查
        String deleteStatusCode = ActivityStatusEnum.DELETE.getCode();
        assertTrue(deleteStatusCode.equals("DELETE"));
        
        // 测试其他状态不会触发删除
        String processingStatusCode = ActivityStatusEnum.PROCESSING.getCode();
        assertFalse(deleteStatusCode.equals(processingStatusCode));
        
        String completedStatusCode = ActivityStatusEnum.COMPLETED.getCode();
        assertFalse(deleteStatusCode.equals(completedStatusCode));
        
        log.info("DELETE状态检查逻辑测试通过");
    }
}
