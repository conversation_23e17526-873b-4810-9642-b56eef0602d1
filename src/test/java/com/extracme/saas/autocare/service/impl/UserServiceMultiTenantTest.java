package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.dto.UserQueryDTO;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.UserVO;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.model.security.LoginUser;

@ExtendWith(MockitoExtension.class)
@DisplayName("用户服务多租户测试")
class UserServiceMultiTenantTest {

    @Mock
    private TableUserService tableUserService;

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TableUserRoleService tableUserRoleService;

    @Mock
    private TableTenantService tableTenantService;

    @InjectMocks
    private UserServiceImpl userService;

    private UserQueryDTO queryDTO;
    private SysUser mockUser;
    private SysRole mockRole;
    private LoginUser mockLoginUser;

    @BeforeEach
    void setUp() {
        // 查询DTO
        queryDTO = new UserQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        // 模拟用户数据
        mockUser = new SysUser();
        mockUser.setId(1L);
        mockUser.setTenantId(100L);
        mockUser.setUsername("testuser");
        mockUser.setNickname("Test User");
        mockUser.setMobile("***********");
        mockUser.setEmail("<EMAIL>");
        mockUser.setStatus(1);
        mockUser.setAccountType(1); // 普通用户
        mockUser.setCreatedTime(new Date());
        mockUser.setUpdatedTime(new Date());

        // 模拟角色数据
        mockRole = new SysRole();
        mockRole.setId(1L);
        mockRole.setRoleName("测试角色");
        mockRole.setRoleCode("TEST_ROLE");

        // 模拟登录用户
        mockLoginUser = new LoginUser();
        SysUser loginUserEntity = new SysUser();
        loginUserEntity.setId(2L);
        loginUserEntity.setTenantId(100L);
        loginUserEntity.setAccountType(1); // 普通用户
        mockLoginUser.setUser(loginUserEntity);
    }

    @Test
    @DisplayName("普通用户查询 - 应该只能查询自己租户下的用户")
    void getUserList_RegularUser_ShouldFilterByTenantId() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为普通用户
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);

            // 设置模拟行为
            when(tableUserService.findByCondition(any(), any(), any(), eq(100L)))
                .thenReturn(Arrays.asList(mockUser));
            when(tableRoleService.findByUserId(anyLong()))
                .thenReturn(Arrays.asList(mockRole));

            // 执行测试
            BasePageVO<UserVO> result = userService.getUserList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getList());
            assertEquals(1, result.getList().size());

            // 验证调用时传入了租户ID过滤条件
            verify(tableUserService).findByCondition(eq(null), eq(null), eq(null), eq(100L));
            verify(tableRoleService).findByUserId(1L);
        }
    }

    @Test
    @DisplayName("超级管理员查询 - 应该能查询所有租户的用户")
    void getUserList_SuperAdmin_ShouldNotFilterByTenantId() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为超级管理员
            SysUser superAdminUser = new SysUser();
            superAdminUser.setId(3L);
            superAdminUser.setTenantId(100L);
            superAdminUser.setAccountType(0); // 超级管理员
            LoginUser superAdminLoginUser = new LoginUser();
            superAdminLoginUser.setUser(superAdminUser);

            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(superAdminLoginUser);

            // 设置模拟行为
            when(tableUserService.findByCondition(any(), any(), any(), eq(null)))
                .thenReturn(Arrays.asList(mockUser));
            when(tableRoleService.findByUserId(anyLong()))
                .thenReturn(Arrays.asList(mockRole));

            // 执行测试
            BasePageVO<UserVO> result = userService.getUserList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getList());
            assertEquals(1, result.getList().size());

            // 验证调用时没有传入租户ID过滤条件（传入null）
            verify(tableUserService).findByCondition(eq(null), eq(null), eq(null), eq(null));
            verify(tableRoleService).findByUserId(1L);
        }
    }

    @Test
    @DisplayName("普通用户带查询条件 - 应该同时应用查询条件和租户过滤")
    void getUserList_RegularUserWithConditions_ShouldApplyBothFilters() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为普通用户
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);

            // 设置查询条件
            queryDTO.setNickname("张三");
            queryDTO.setMobile("138");
            queryDTO.setRoleId(1L);

            // 设置模拟行为
            when(tableUserService.findByCondition(eq("张三"), eq("138"), eq(1L), eq(100L)))
                .thenReturn(Arrays.asList(mockUser));
            when(tableRoleService.findByUserId(anyLong()))
                .thenReturn(Arrays.asList(mockRole));

            // 执行测试
            BasePageVO<UserVO> result = userService.getUserList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getList());
            assertEquals(1, result.getList().size());

            // 验证调用时同时传入了查询条件和租户ID过滤条件
            verify(tableUserService).findByCondition(eq("张三"), eq("138"), eq(1L), eq(100L));
            verify(tableRoleService).findByUserId(1L);
        }
    }
}
