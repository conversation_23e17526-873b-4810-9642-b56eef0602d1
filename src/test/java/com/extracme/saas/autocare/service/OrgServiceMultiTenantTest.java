package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.service.impl.OrgServiceImpl;
import com.extracme.saas.autocare.util.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 组织机构服务多租户访问控制测试
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@DisplayName("组织机构服务多租户访问控制测试")
class OrgServiceMultiTenantTest {

    @Mock
    private TableOrgInfoService orgInfoRepository;

    @InjectMocks
    private OrgServiceImpl orgService;

    private SysUser mockSuperAdminUser;
    private SysUser mockRegularUser;
    private LoginUser mockSuperAdminLoginUser;
    private LoginUser mockRegularUserLoginUser;
    private List<MtcOrgInfo> mockOrgList;

    @BeforeEach
    void setUp() {
        // 创建超级管理员用户
        mockSuperAdminUser = new SysUser();
        mockSuperAdminUser.setId(1L);
        mockSuperAdminUser.setUsername("superadmin");
        mockSuperAdminUser.setAccountType(0); // 超级管理员
        mockSuperAdminUser.setTenantId(100L);

        mockSuperAdminLoginUser = new LoginUser();
        mockSuperAdminLoginUser.setUser(mockSuperAdminUser);
        mockSuperAdminLoginUser.setTenantId(100L);

        // 创建普通用户
        mockRegularUser = new SysUser();
        mockRegularUser.setId(2L);
        mockRegularUser.setUsername("regularuser");
        mockRegularUser.setAccountType(1); // 普通用户
        mockRegularUser.setTenantId(200L);

        mockRegularUserLoginUser = new LoginUser();
        mockRegularUserLoginUser.setUser(mockRegularUser);
        mockRegularUserLoginUser.setTenantId(200L);

        // 创建模拟组织数据
        MtcOrgInfo org1 = new MtcOrgInfo();
        org1.setOrgId("ORG001");
        org1.setOrgName("组织1");

        MtcOrgInfo org2 = new MtcOrgInfo();
        org2.setOrgId("ORG002");
        org2.setOrgName("组织2");

        mockOrgList = Arrays.asList(org1, org2);
    }

    @Test
    @DisplayName("超级管理员查询组织下拉列表 - 应该能查询所有租户的组织")
    void getOrgCombo_SuperAdmin_ShouldQueryAllTenants() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为超级管理员
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockSuperAdminLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(true);
            sessionUtilsMock.when(SessionUtils::getTenantId).thenReturn(100L);

            // 模拟查询所有租户的组织数据
            when(orgInfoRepository.findValidOrgs()).thenReturn(mockOrgList);

            // 执行测试
            List<ComboVO<String>> result = orgService.getOrgCombo();

            // 验证结果
            assertNotNull(result, "结果不应该为空");
            assertEquals(2, result.size(), "应该返回2个组织");
            assertEquals("ORG001", result.get(0).getId(), "第一个组织ID应该是ORG001");
            assertEquals("组织1", result.get(0).getValue(), "第一个组织名称应该是组织1");
            assertEquals("ORG002", result.get(1).getId(), "第二个组织ID应该是ORG002");
            assertEquals("组织2", result.get(1).getValue(), "第二个组织名称应该是组织2");

            // 验证方法调用
            verify(orgInfoRepository, times(1)).findValidOrgs();
            
            // 验证SessionUtils方法被调用
            sessionUtilsMock.verify(SessionUtils::isSuperAdmin, times(1));
            sessionUtilsMock.verify(SessionUtils::getTenantId, never()); // 超级管理员不需要获取租户ID

            log.info("✅ 超级管理员查询组织下拉列表测试通过");
        }
    }

    @Test
    @DisplayName("普通用户查询组织下拉列表 - 应该只能查询自己租户的组织")
    void getOrgCombo_RegularUser_ShouldQueryOwnTenantOnly() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为普通用户
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockRegularUserLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);
            sessionUtilsMock.when(SessionUtils::getTenantId).thenReturn(200L);

            // 模拟查询指定租户的组织数据
            when(orgInfoRepository.findValidOrgs()).thenReturn(mockOrgList);

            // 执行测试
            List<ComboVO<String>> result = orgService.getOrgCombo();

            // 验证结果
            assertNotNull(result, "结果不应该为空");
            assertEquals(2, result.size(), "应该返回2个组织");
            assertEquals("ORG001", result.get(0).getId(), "第一个组织ID应该是ORG001");
            assertEquals("组织1", result.get(0).getValue(), "第一个组织名称应该是组织1");

            // 验证方法调用
            verify(orgInfoRepository, times(1)).findValidOrgs();
            
            // 验证SessionUtils方法被调用
            sessionUtilsMock.verify(SessionUtils::isSuperAdmin, times(1));
            sessionUtilsMock.verify(SessionUtils::getTenantId, times(1)); // 普通用户需要获取租户ID

            log.info("✅ 普通用户查询组织下拉列表测试通过");
        }
    }

    @Test
    @DisplayName("未登录用户查询组织下拉列表 - 应该按普通用户处理")
    void getOrgCombo_NotLoggedIn_ShouldTreatAsRegularUser() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置未登录状态
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(null);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);
            sessionUtilsMock.when(SessionUtils::getTenantId).thenReturn(null);

            // 模拟查询组织数据
            when(orgInfoRepository.findValidOrgs()).thenReturn(mockOrgList);

            // 执行测试
            List<ComboVO<String>> result = orgService.getOrgCombo();

            // 验证结果
            assertNotNull(result, "结果不应该为空");
            assertEquals(2, result.size(), "应该返回2个组织");

            // 验证方法调用
            verify(orgInfoRepository, times(1)).findValidOrgs();
            
            // 验证SessionUtils方法被调用
            sessionUtilsMock.verify(SessionUtils::isSuperAdmin, times(1));
            sessionUtilsMock.verify(SessionUtils::getTenantId, times(1)); // 未登录用户也会尝试获取租户ID

            log.info("✅ 未登录用户查询组织下拉列表测试通过");
        }
    }

    @Test
    @DisplayName("测试getOrgComboByTenantId方法的直接调用")
    void getOrgComboByTenantId_DirectCall_ShouldWork() {
        // 模拟查询组织数据
        when(orgInfoRepository.findValidOrgs()).thenReturn(mockOrgList);

        // 执行测试 - 直接调用带租户ID的方法
        List<ComboVO<String>> result = orgService.getOrgComboByTenantId(300L);

        // 验证结果
        assertNotNull(result, "结果不应该为空");
        assertEquals(2, result.size(), "应该返回2个组织");
        assertEquals("ORG001", result.get(0).getId(), "第一个组织ID应该是ORG001");
        assertEquals("组织1", result.get(0).getValue(), "第一个组织名称应该是组织1");

        // 验证方法调用
        verify(orgInfoRepository, times(1)).findValidOrgs();

        log.info("✅ getOrgComboByTenantId方法直接调用测试通过");
    }

    @Test
    @DisplayName("测试空组织列表的处理")
    void getOrgCombo_EmptyOrgList_ShouldReturnEmptyList() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为超级管理员
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockSuperAdminLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(true);

            // 模拟查询返回空列表
            when(orgInfoRepository.findValidOrgs()).thenReturn(Arrays.asList());

            // 执行测试
            List<ComboVO<String>> result = orgService.getOrgCombo();

            // 验证结果
            assertNotNull(result, "结果不应该为空");
            assertEquals(0, result.size(), "应该返回空列表");

            // 验证方法调用
            verify(orgInfoRepository, times(1)).findValidOrgs();

            log.info("✅ 空组织列表处理测试通过");
        }
    }

    @Test
    @DisplayName("测试多租户访问控制的一致性")
    void testMultiTenantAccessControlConsistency() {
        log.info("开始测试多租户访问控制的一致性");

        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            // 模拟查询组织数据
            when(orgInfoRepository.findValidOrgs()).thenReturn(mockOrgList);

            // 测试1：超级管理员
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockSuperAdminLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(true);
            
            List<ComboVO<String>> superAdminResult = orgService.getOrgCombo();
            assertNotNull(superAdminResult, "超级管理员结果不应该为空");
            log.info("超级管理员查询结果：{} 个组织", superAdminResult.size());

            // 测试2：普通用户
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockRegularUserLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);
            sessionUtilsMock.when(SessionUtils::getTenantId).thenReturn(200L);
            
            List<ComboVO<String>> regularUserResult = orgService.getOrgCombo();
            assertNotNull(regularUserResult, "普通用户结果不应该为空");
            log.info("普通用户查询结果：{} 个组织", regularUserResult.size());

            // 验证两次查询都正常执行
            verify(orgInfoRepository, times(2)).findValidOrgs();

            log.info("✅ 多租户访问控制一致性测试通过");
        }
    }
}
