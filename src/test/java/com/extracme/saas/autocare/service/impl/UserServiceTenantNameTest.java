package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.dto.UserQueryDTO;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.UserVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.repository.TableUserService;

/**
 * 用户服务租户名称功能测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("用户服务租户名称功能测试")
class UserServiceTenantNameTest {

    @Mock
    private TableUserService tableUserService;

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TableUserRoleService tableUserRoleService;

    @Mock
    private TableTenantService tableTenantService;

    @InjectMocks
    private UserServiceImpl userService;

    private SysUser mockUser;
    private SysTenant mockTenant;
    private SysRole mockRole;
    private UserQueryDTO queryDTO;

    @BeforeEach
    void setUp() {
        // 创建模拟租户
        mockTenant = new SysTenant();
        mockTenant.setId(100L);
        mockTenant.setTenantName("测试租户");

        // 创建模拟用户
        mockUser = new SysUser();
        mockUser.setId(1L);
        mockUser.setTenantId(100L);
        mockUser.setNickname("测试用户");
        mockUser.setMobile("13800138000");

        // 创建模拟角色
        mockRole = new SysRole();
        mockRole.setId(1L);
        mockRole.setRoleName("测试角色");

        // 创建查询DTO
        queryDTO = new UserQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
    }

    @Test
    @DisplayName("查询用户列表 - 验证租户名称字段映射")
    void getUserList_VerifyTenantNameMapping() {
        // 设置模拟行为
        when(tableUserService.findByCondition(any(), any(), any(), any()))
            .thenReturn(Arrays.asList(mockUser));
        when(tableTenantService.findByCondition(null, null, null))
            .thenReturn(Arrays.asList(mockTenant));
        when(tableRoleService.findByUserId(anyLong()))
            .thenReturn(Arrays.asList(mockRole));

        // 执行测试
        BasePageVO<UserVO> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());

        UserVO userVO = result.getList().get(0);
        assertNotNull(userVO.getTenantId());
        assertNotNull(userVO.getTenantName());
        assertEquals(100L, userVO.getTenantId());
        assertEquals("测试租户", userVO.getTenantName());
        assertEquals(1L, userVO.getId());
        assertEquals("测试用户", userVO.getNickname());

        // 验证调用
        verify(tableUserService).findByCondition(any(), any(), any(), any());
        verify(tableTenantService).findByCondition(null, null, null);
        verify(tableRoleService).findByUserId(1L);
    }

    @Test
    @DisplayName("查询用户列表 - 用户无租户ID时租户名称为空")
    void getUserList_UserWithoutTenantId() {
        // 创建无租户ID的用户
        SysUser userWithoutTenant = new SysUser();
        userWithoutTenant.setId(2L);
        userWithoutTenant.setTenantId(null);
        userWithoutTenant.setNickname("无租户用户");

        // 设置模拟行为
        when(tableUserService.findByCondition(any(), any(), any(), any()))
            .thenReturn(Arrays.asList(userWithoutTenant));
        when(tableTenantService.findByCondition(null, null, null))
            .thenReturn(Arrays.asList(mockTenant));
        when(tableRoleService.findByUserId(anyLong()))
            .thenReturn(Collections.emptyList());

        // 执行测试
        BasePageVO<UserVO> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());

        UserVO userVO = result.getList().get(0);
        assertNull(userVO.getTenantId());
        assertNull(userVO.getTenantName());
        assertEquals("无租户用户", userVO.getNickname());
    }

    @Test
    @DisplayName("查询用户详情 - 验证租户名称字段映射")
    void getUserById_VerifyTenantNameMapping() {
        // 设置模拟行为
        when(tableUserService.selectById(1L)).thenReturn(mockUser);
        when(tableTenantService.selectById(100L)).thenReturn(mockTenant);
        when(tableRoleService.findByUserId(1L)).thenReturn(Arrays.asList(mockRole));

        // 执行测试
        UserVO result = userService.getUserById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals(100L, result.getTenantId());
        assertEquals("测试租户", result.getTenantName());
        assertEquals("测试用户", result.getNickname());

        // 验证调用
        verify(tableUserService).selectById(1L);
        verify(tableTenantService).selectById(100L);
        verify(tableRoleService).findByUserId(1L);
    }

    @Test
    @DisplayName("查询用户详情 - 用户无租户ID时租户名称为空")
    void getUserById_UserWithoutTenantId() {
        // 创建无租户ID的用户
        SysUser userWithoutTenant = new SysUser();
        userWithoutTenant.setId(2L);
        userWithoutTenant.setTenantId(null);
        userWithoutTenant.setNickname("无租户用户");

        // 设置模拟行为
        when(tableUserService.selectById(2L)).thenReturn(userWithoutTenant);
        when(tableRoleService.findByUserId(2L)).thenReturn(Collections.emptyList());

        // 执行测试
        UserVO result = userService.getUserById(2L);

        // 验证结果
        assertNotNull(result);
        assertEquals(2L, result.getId());
        assertNull(result.getTenantId());
        assertNull(result.getTenantName());
        assertEquals("无租户用户", result.getNickname());

        // 验证没有调用租户查询
        verify(tableTenantService, never()).selectById(any());
    }

    @Test
    @DisplayName("查询用户列表 - 性能优化验证：一次性查询所有租户")
    void getUserList_PerformanceOptimization() {
        // 创建多个用户，都属于同一个租户
        SysUser user1 = new SysUser();
        user1.setId(1L);
        user1.setTenantId(100L);
        user1.setNickname("用户1");

        SysUser user2 = new SysUser();
        user2.setId(2L);
        user2.setTenantId(100L);
        user2.setNickname("用户2");

        // 设置模拟行为
        when(tableUserService.findByCondition(any(), any(), any(), any()))
            .thenReturn(Arrays.asList(user1, user2));
        when(tableTenantService.findByCondition(null, null, null))
            .thenReturn(Arrays.asList(mockTenant));
        when(tableRoleService.findByUserId(anyLong()))
            .thenReturn(Collections.emptyList());

        // 执行测试
        BasePageVO<UserVO> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());

        // 验证两个用户都有正确的租户名称
        for (UserVO userVO : result.getList()) {
            assertEquals(100L, userVO.getTenantId());
            assertEquals("测试租户", userVO.getTenantName());
        }

        // 验证性能优化：只调用一次租户查询，而不是为每个用户单独查询
        verify(tableTenantService, times(1)).findByCondition(null, null, null);
        verify(tableTenantService, never()).selectById(any());
    }
}
