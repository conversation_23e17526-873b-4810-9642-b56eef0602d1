package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.SmsService;
import com.extracme.saas.autocare.util.JwtUtil;
import com.extracme.saas.autocare.util.OrgHierarchyUtils;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;

/**
 * 身份认证服务多账号状态验证测试类
 * 测试一个手机号关联多个用户账号时的状态验证逻辑
 */
public class AuthServiceImplMultiAccountStatusTest {

    @InjectMocks
    private AuthServiceImpl authService;
    
    @Mock
    private SmsService smsService;
    
    @Mock
    private TableUserService userService;
    
    @Mock
    private TableRoleService roleService;
    
    @Mock
    private TablePermissionService permissionService;
    
    @Mock
    private TableTenantService tenantService;
    
    @Mock
    private TableUserOrgService userOrgService;
    
    @Mock
    private OrgHierarchyUtils orgHierarchyUtils;
    
    @Mock
    private JwtUtil jwtUtil;
    
    @Mock
    private UserPermissionCacheUtils userPermissionCacheUtils;

    private final String testMobile = "***********";
    private final String testCode = "123456";
    private final Long testTenantId1 = 1L;
    private final Long testTenantId2 = 2L;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(authService, "jwtExpiration", 86400);
    }

    /**
     * 创建测试用户
     */
    private SysUser createTestUser(Long id, String mobile, Long tenantId, Integer status) {
        SysUser user = new SysUser();
        user.setId(id);
        user.setMobile(mobile);
        user.setTenantId(tenantId);
        user.setStatus(status);
        user.setUsername("testuser" + id);
        user.setNickname("测试用户" + id);
        user.setCreatedTime(new Date());
        user.setUpdatedTime(new Date());
        return user;
    }

    /**
     * 创建测试租户
     */
    private SysTenant createTestTenant(Long id, String code, String name) {
        SysTenant tenant = new SysTenant();
        tenant.setId(id);
        tenant.setTenantCode(code);
        tenant.setTenantName(name);
        tenant.setStatus(1);
        tenant.setCreatedTime(new Date());
        tenant.setUpdatedTime(new Date());
        return tenant;
    }

    @Test
    @DisplayName("loginByMobile - 手机号下有多个账号，至少一个启用 - 应该成功登录")
    void testLoginByMobile_MultipleAccounts_AtLeastOneActive_ShouldSucceed() {
        // 准备测试数据 - 两个账号，一个启用一个禁用
        SysUser activeUser = createTestUser(1L, testMobile, testTenantId1, 1);
        SysUser disabledUser = createTestUser(2L, testMobile, testTenantId2, 0);
        List<SysUser> allUsers = Arrays.asList(activeUser, disabledUser);

        SysTenant tenant = createTestTenant(testTenantId1, "tenant1", "租户1");

        // Mock 方法调用
        when(userService.findAllByMobile(testMobile)).thenReturn(allUsers);
        when(userService.findByMobile(testMobile)).thenReturn(Optional.of(activeUser));
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(tenantService.selectById(testTenantId1)).thenReturn(tenant);
        when(roleService.findByUserId(1L)).thenReturn(Collections.emptyList());
        when(permissionService.findByUserId(1L)).thenReturn(Collections.emptyList());
        when(userOrgService.findOrgIdsByUserId(1L)).thenReturn(Collections.emptyList());
        when(jwtUtil.generateTokenWithUserInfo(any())).thenReturn("test-token");

        // 执行测试
        TokenDTO result = authService.loginByMobile(testMobile, testCode);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-token", result.getAccessToken());

        // 验证调用顺序
        verify(userService).findAllByMobile(testMobile);
        verify(userService).findByMobile(testMobile);
        verify(smsService).verifyCode(testMobile, testCode, "LOGIN");
    }

    @Test
    @DisplayName("loginByMobile - 手机号下所有账号都被禁用 - 应该返回null")
    void testLoginByMobile_AllAccountsDisabled_ShouldReturnNull() {
        // 准备测试数据 - 两个账号都被禁用
        SysUser disabledUser1 = createTestUser(1L, testMobile, testTenantId1, 0);
        SysUser disabledUser2 = createTestUser(2L, testMobile, testTenantId2, 0);
        List<SysUser> allUsers = Arrays.asList(disabledUser1, disabledUser2);

        // Mock 方法调用
        when(userService.findAllByMobile(testMobile)).thenReturn(allUsers);

        // 执行测试
        TokenDTO result = authService.loginByMobile(testMobile, testCode);

        // 验证结果
        assertNull(result);

        // 验证调用顺序 - 不应该调用后续的验证码验证
        verify(userService).findAllByMobile(testMobile);
        verify(smsService, never()).verifyCode(anyString(), anyString(), anyString());
    }

    @Test
    @DisplayName("loginByMobileWithTenant - 手机号下有多个账号，至少一个启用 - 应该成功登录")
    void testLoginByMobileWithTenant_MultipleAccounts_AtLeastOneActive_ShouldSucceed() {
        // 准备测试数据 - 两个账号，一个启用一个禁用
        SysUser activeUser = createTestUser(1L, testMobile, testTenantId1, 1);
        SysUser disabledUser = createTestUser(2L, testMobile, testTenantId2, 0);
        List<SysUser> allUsers = Arrays.asList(activeUser, disabledUser);

        SysTenant tenant = createTestTenant(testTenantId1, "tenant1", "租户1");

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(allUsers);
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(Arrays.asList(testTenantId1));
        when(userService.findByMobileAndTenantId(testMobile, testTenantId1)).thenReturn(Optional.of(activeUser));
        when(tenantService.selectById(testTenantId1)).thenReturn(tenant);
        when(roleService.findByUserId(1L)).thenReturn(Collections.emptyList());
        when(permissionService.findByUserId(1L)).thenReturn(Collections.emptyList());
        when(userOrgService.findOrgIdsByUserId(1L)).thenReturn(Collections.emptyList());
        when(jwtUtil.generateTokenWithUserInfo(any())).thenReturn("test-token");

        // 执行测试
        TokenDTO result = authService.loginByMobileWithTenant(testMobile, testCode, testTenantId1);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-token", result.getAccessToken());

        // 验证调用顺序
        verify(userService).findAllByMobile(testMobile);
        verify(userService).findTenantIdsByMobile(testMobile);
    }

    @Test
    @DisplayName("loginByMobileWithTenant - 手机号下所有账号都被禁用 - 应该抛出ACCOUNT_LOCKED异常")
    void testLoginByMobileWithTenant_AllAccountsDisabled_ShouldThrowAccountLocked() {
        // 准备测试数据 - 两个账号都被禁用
        SysUser disabledUser1 = createTestUser(1L, testMobile, testTenantId1, 0);
        SysUser disabledUser2 = createTestUser(2L, testMobile, testTenantId2, 0);
        List<SysUser> allUsers = Arrays.asList(disabledUser1, disabledUser2);

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(allUsers);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            authService.loginByMobileWithTenant(testMobile, testCode, testTenantId1);
        });

        assertEquals(ErrorCode.ACCOUNT_LOCKED.getCode(), exception.getCode());

        // 验证调用顺序 - 不应该调用租户相关的查询方法
        verify(userService).findAllByMobile(testMobile);
        verify(userService, never()).findTenantIdsByMobile(testMobile);
    }

    @Test
    @DisplayName("loginByMobileWithTenant - 手机号下有状态为null的账号和启用账号 - 应该成功登录")
    void testLoginByMobileWithTenant_AccountsWithNullStatusAndActive_ShouldSucceed() {
        // 准备测试数据 - 一个状态为null的账号和一个启用账号
        SysUser nullStatusUser = createTestUser(1L, testMobile, testTenantId1, null);
        SysUser activeUser = createTestUser(2L, testMobile, testTenantId2, 1);
        List<SysUser> allUsers = Arrays.asList(nullStatusUser, activeUser);

        SysTenant tenant = createTestTenant(testTenantId2, "tenant2", "租户2");

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findAllByMobile(testMobile)).thenReturn(allUsers);
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(Arrays.asList(testTenantId2));
        when(userService.findByMobileAndTenantId(testMobile, testTenantId2)).thenReturn(Optional.of(activeUser));
        when(tenantService.selectById(testTenantId2)).thenReturn(tenant);
        when(roleService.findByUserId(2L)).thenReturn(Collections.emptyList());
        when(permissionService.findByUserId(2L)).thenReturn(Collections.emptyList());
        when(userOrgService.findOrgIdsByUserId(2L)).thenReturn(Collections.emptyList());
        when(jwtUtil.generateTokenWithUserInfo(any())).thenReturn("test-token");

        // 执行测试
        TokenDTO result = authService.loginByMobileWithTenant(testMobile, testCode, testTenantId2);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-token", result.getAccessToken());

        // 验证调用顺序
        verify(userService).findAllByMobile(testMobile);
        verify(userService).findTenantIdsByMobile(testMobile);
    }
}
