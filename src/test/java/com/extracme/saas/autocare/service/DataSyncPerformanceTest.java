package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.model.dto.VehicleInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.VehicleModelSyncRequestDTO;
import com.extracme.saas.autocare.model.vo.DataSyncResultVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据同步性能测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class DataSyncPerformanceTest {

    @Autowired
    private DataSyncService dataSyncService;

    private String testSyncKey = "test_sync_key_001";
    private String testSourceIp = "127.0.0.1";

    @BeforeEach
    void setUp() {
        log.info("开始数据同步性能测试");
    }

    /**
     * 测试小批量数据同步（顺序处理）
     */
    @Test
    void testSmallBatchSync() {
        log.info("测试小批量数据同步（顺序处理）");
        
        // 创建50条测试数据（小于MIN_PARALLEL_SIZE=100）
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> batchData = createVehicleTestData(50);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey(testSyncKey);
        requestDTO.setBatchData(batchData);

        long startTime = System.currentTimeMillis();
        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, testSourceIp);
        long endTime = System.currentTimeMillis();

        // 验证结果
        assertNotNull(result);
        assertEquals(50, result.getTotalCount());
        assertTrue(result.getSyncDuration() > 0);
        
        log.info("小批量同步完成，耗时：{}ms，成功：{}，失败：{}", 
                endTime - startTime, result.getSuccessCount(), result.getFailedCount());
    }

    /**
     * 测试大批量数据同步（并行处理）
     */
    @Test
    void testLargeBatchSync() {
        log.info("测试大批量数据同步（并行处理）");
        
        // 创建200条测试数据（大于MIN_PARALLEL_SIZE=100）
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> batchData = createVehicleTestData(200);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey(testSyncKey);
        requestDTO.setBatchData(batchData);

        long startTime = System.currentTimeMillis();
        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, testSourceIp);
        long endTime = System.currentTimeMillis();

        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getTotalCount());
        assertTrue(result.getSyncDuration() > 0);
        
        log.info("大批量同步完成，耗时：{}ms，成功：{}，失败：{}", 
                endTime - startTime, result.getSuccessCount(), result.getFailedCount());
    }

    /**
     * 测试车型数据同步性能
     */
    @Test
    void testVehicleModelBatchSync() {
        log.info("测试车型数据同步性能");
        
        // 创建150条车型测试数据
        List<VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO> batchData = createVehicleModelTestData(150);
        
        VehicleModelSyncRequestDTO requestDTO = new VehicleModelSyncRequestDTO();
        requestDTO.setSyncKey(testSyncKey);
        requestDTO.setBatchData(batchData);

        long startTime = System.currentTimeMillis();
        DataSyncResultVO result = dataSyncService.syncVehicleModelBatch(requestDTO, testSourceIp);
        long endTime = System.currentTimeMillis();

        // 验证结果
        assertNotNull(result);
        assertEquals(150, result.getTotalCount());
        assertTrue(result.getSyncDuration() > 0);
        
        log.info("车型批量同步完成，耗时：{}ms，成功：{}，失败：{}", 
                endTime - startTime, result.getSuccessCount(), result.getFailedCount());
    }

    /**
     * 性能对比测试：顺序 vs 并行
     */
    @Test
    void testPerformanceComparison() {
        log.info("开始性能对比测试");
        
        int dataSize = 300;
        
        // 测试1：大批量数据（应该使用并行处理）
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> largeBatch = createVehicleTestData(dataSize);
        VehicleInfoSyncRequestDTO largeRequest = new VehicleInfoSyncRequestDTO();
        largeRequest.setSyncKey(testSyncKey);
        largeRequest.setBatchData(largeBatch);

        long parallelStartTime = System.currentTimeMillis();
        DataSyncResultVO parallelResult = dataSyncService.syncVehicleInfoBatch(largeRequest, testSourceIp);
        long parallelEndTime = System.currentTimeMillis();
        long parallelDuration = parallelEndTime - parallelStartTime;

        // 验证并行处理结果
        assertNotNull(parallelResult);
        assertEquals(dataSize, parallelResult.getTotalCount());
        
        log.info("并行处理完成，数据量：{}，耗时：{}ms，成功率：{}", 
                dataSize, parallelDuration, 
                (double) parallelResult.getSuccessCount() / parallelResult.getTotalCount() * 100);

        // 验证性能提升（并行处理应该更快，但由于测试环境限制，主要验证功能正确性）
        assertTrue(parallelResult.getSyncDuration() > 0, "同步耗时应该大于0");
        assertTrue(parallelResult.getSuccessCount() >= 0, "成功数量应该大于等于0");
    }

    /**
     * 测试错误处理和失败恢复
     */
    @Test
    void testErrorHandling() {
        log.info("测试错误处理和失败恢复");
        
        // 创建包含无效数据的测试集
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> batchData = new ArrayList<>();
        
        // 添加正常数据
        for (int i = 1; i <= 50; i++) {
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO data = 
                new VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO();
            data.setId((long) i);
            data.setVin("TEST_VIN_" + i);
            data.setVehicleNo("TEST_NO_" + i);
            batchData.add(data);
        }
        
        // 添加无效数据（缺少ID）
        for (int i = 51; i <= 60; i++) {
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO data = 
                new VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO();
            // 故意不设置ID，应该导致失败
            data.setVin("INVALID_VIN_" + i);
            data.setVehicleNo("INVALID_NO_" + i);
            batchData.add(data);
        }
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey(testSyncKey);
        requestDTO.setBatchData(batchData);

        DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, testSourceIp);

        // 验证错误处理
        assertNotNull(result);
        assertEquals(60, result.getTotalCount());
        assertEquals(50, result.getSuccessCount()); // 只有前50条应该成功
        assertEquals(10, result.getFailedCount()); // 后10条应该失败
        assertNotNull(result.getFailureDetails());
        assertEquals(10, result.getFailureDetails().size());
        
        log.info("错误处理测试完成，总数：{}，成功：{}，失败：{}", 
                result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());
    }

    /**
     * 创建车辆测试数据
     */
    private List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> createVehicleTestData(int count) {
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> batchData = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO data = 
                new VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO();
            data.setId((long) i);
            data.setVin("PERF_TEST_VIN_" + i + "_" + ThreadLocalRandom.current().nextInt(1000, 9999));
            data.setVehicleNo("测试" + String.format("%05d", i));
            data.setVehicleModelId((long) (i % 10 + 1)); // 循环使用车型ID
            batchData.add(data);
        }
        
        return batchData;
    }

    /**
     * 创建车型测试数据
     */
    private List<VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO> createVehicleModelTestData(int count) {
        List<VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO> batchData = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO data = 
                new VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO();
            data.setId((long) i);
            data.setVehicleModelName("性能测试车型_" + i + "_" + ThreadLocalRandom.current().nextInt(1000, 9999));
            batchData.add(data);
        }
        
        return batchData;
    }
}
