package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotInfo;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableRepairDepotInfoService;

/**
 * RepairDepotService 多租户功能测试
 */
@ExtendWith(SpringExtension.class)
class RepairDepotServiceImplTest {

    @Mock
    private TableRepairDepotInfoService repairDepotRepository;

    @InjectMocks
    private RepairDepotServiceImpl repairDepotService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void tearDown() {
        // 清理租户上下文
        TenantContextHolder.clear();
    }

    /**
     * 测试租户0（Extracme）的修理厂列表获取
     */
    @Test
    void testGetRepairDepotCombo_Tenant0() {
        // 准备测试数据
        MtcRepairDepotInfo depot1 = new MtcRepairDepotInfo();
        depot1.setRepairDepotId("RD001");
        depot1.setRepairDepotName("Extracme修理厂1");

        MtcRepairDepotInfo depot2 = new MtcRepairDepotInfo();
        depot2.setRepairDepotId("RD002");
        depot2.setRepairDepotName("Extracme修理厂2");

        // 设置当前租户
        TenantContextHolder.setTenant(0L);

        // 模拟Repository层返回数据
        when(repairDepotRepository.findValidDepots())
            .thenReturn(Arrays.asList(depot1, depot2));

        // 执行测试
        List<ComboVO<String>> result = repairDepotService.getRepairDepotCombo();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Extracme修理厂1", result.get(0).getValue());
        assertEquals("RD001", result.get(0).getId());
        assertEquals("Extracme修理厂2", result.get(1).getValue());
        assertEquals("RD002", result.get(1).getId());

        // 验证是否使用了正确的租户上下文
        assertEquals("auto_care_extracme", TenantContextHolder.getTenantSchema());
    }

    /**
     * 测试租户1（DZJT）的修理厂列表获取
     */
    @Test
    void testGetRepairDepotCombo_Tenant1() {
        // 准备测试数据
        MtcRepairDepotInfo depot1 = new MtcRepairDepotInfo();
        depot1.setRepairDepotId("RD003");
        depot1.setRepairDepotName("DZJT修理厂1");

        MtcRepairDepotInfo depot2 = new MtcRepairDepotInfo();
        depot2.setRepairDepotId("RD004");
        depot2.setRepairDepotName("DZJT修理厂2");

        // 设置当前租户
        TenantContextHolder.setTenant(1L);

        // 模拟Repository层返回数据
        when(repairDepotRepository.findValidDepots())
            .thenReturn(Arrays.asList(depot1, depot2));

        // 执行测试
        List<ComboVO<String>> result = repairDepotService.getRepairDepotCombo();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("DZJT修理厂1", result.get(0).getValue());
        assertEquals("RD003", result.get(0).getId());
        assertEquals("DZJT修理厂2", result.get(1).getValue());
        assertEquals("RD004", result.get(1).getId());

        // 验证是否使用了正确的租户上下文
        assertEquals("auto_care_dzjt", TenantContextHolder.getTenantSchema());
    }

    /**
     * 测试默认租户（未指定租户ID）的修理厂列表获取
     */
    @Test
    void testGetRepairDepotCombo_DefaultTenant() {
        // 不设置租户ID，使用默认租户

        // 执行测试
        List<ComboVO<String>> result = repairDepotService.getRepairDepotCombo();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty(), "默认租户不应该包含任何修理厂数据");

        // 验证是否使用了默认租户上下文
        assertEquals("auto_care_saas", TenantContextHolder.getTenantSchema());

        // 验证没有调用Repository
        verify(repairDepotRepository, never()).findValidDepots();
    }

    /**
     * 测试异常情况处理
     */
    @Test
    void testGetRepairDepotCombo_Exception() {
        // 设置当前租户
        TenantContextHolder.setTenant(0L);

        // 模拟Repository层抛出异常
        when(repairDepotRepository.findValidDepots())
            .thenThrow(new RuntimeException("数据库访问异常"));

        // 验证是否抛出预期的异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            repairDepotService.getRepairDepotCombo();
        });

        assertEquals("获取修理厂下拉列表失败", exception.getMessage());
    }
}