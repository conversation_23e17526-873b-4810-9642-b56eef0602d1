package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Calendar;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.extracme.saas.autocare.model.entity.SmsSendRecord;
import com.extracme.saas.autocare.model.entity.SysVerificationCode;
import com.extracme.saas.autocare.repository.TableSmsSendRecordService;
import com.extracme.saas.autocare.repository.TableSysVerificationCodeService;

/**
 * SmsService测试账号功能测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SmsService测试账号功能测试")
public class SmsServiceTestAccountTest {

    @Mock
    private TableSysVerificationCodeService verificationCodeService;

    @Mock
    private TableSmsSendRecordService smsSendRecordService;

    @InjectMocks
    private SmsServiceImpl smsService;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(smsService, "testMobile", "***********");
        ReflectionTestUtils.setField(smsService, "activeProfile", "dev");
        ReflectionTestUtils.setField(smsService, "codeExpiration", 300);
        ReflectionTestUtils.setField(smsService, "sendInterval", 60);
        ReflectionTestUtils.setField(smsService, "dailyLimitMobile", 20);
        ReflectionTestUtils.setField(smsService, "dailyLimitIP", 50);

        // 模拟数据库操作
        SysVerificationCode mockCode = new SysVerificationCode();
        mockCode.setId(1L);
        when(verificationCodeService.insert(any(SysVerificationCode.class))).thenReturn(mockCode);
        when(smsSendRecordService.save(any(SmsSendRecord.class))).thenReturn(1L);
        when(smsSendRecordService.findLastSendTime(any(), any())).thenReturn(null);
        when(smsSendRecordService.countByPhoneAndTime(any(), any(), any())).thenReturn(0);
        when(smsSendRecordService.countByIpAndTime(any(), any(), any())).thenReturn(0);
    }

    @Test
    @DisplayName("测试账号验证码生成 - 时间格式验证")
    void testGenerateVerificationCodeForTestAccount() throws Exception {
        // 准备测试数据
        String testMobile = "***********";
        String ipAddress = "***********";

        // 执行测试
        boolean result = smsService.sendLoginVerificationCode(testMobile, ipAddress);

        // 验证结果
        assertTrue(result);
    }

    @Test
    @DisplayName("普通账号验证码生成 - 非生产环境")
    void testGenerateVerificationCodeForNormalAccount() throws Exception {
        // 准备测试数据
        String normalMobile = "***********";
        String ipAddress = "***********";

        // 执行测试
        boolean result = smsService.sendLoginVerificationCode(normalMobile, ipAddress);

        // 验证结果
        assertTrue(result);
    }

    @Test
    @DisplayName("验证测试账号验证码格式")
    void testTestAccountCodeFormat() {
        // 使用反射调用私有方法来测试验证码格式
        String testMobile = "***********";
        
        // 通过反射调用generateVerificationCode方法
        try {
            java.lang.reflect.Method method = SmsServiceImpl.class.getDeclaredMethod("generateVerificationCode", String.class);
            method.setAccessible(true);
            String code = (String) method.invoke(smsService, testMobile);
            
            // 验证验证码格式：应该是6位数字
            assertEquals(6, code.length());
            assertTrue(code.matches("\\d{6}"));
            
            // 验证验证码是当前时间的月日时格式
            Calendar calendar = Calendar.getInstance();
            int month = calendar.get(Calendar.MONTH) + 1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            String expectedCode = String.format("%02d%02d%02d", month, day, hour);
            
            assertEquals(expectedCode, code);
        } catch (Exception e) {
            throw new RuntimeException("反射调用失败", e);
        }
    }

    @Test
    @DisplayName("验证普通账号验证码格式 - 非生产环境")
    void testNormalAccountCodeFormat() {
        // 使用反射调用私有方法来测试验证码格式
        String normalMobile = "***********";
        
        // 通过反射调用generateVerificationCode方法
        try {
            java.lang.reflect.Method method = SmsServiceImpl.class.getDeclaredMethod("generateVerificationCode", String.class);
            method.setAccessible(true);
            String code = (String) method.invoke(smsService, normalMobile);
            
            // 验证验证码格式：非生产环境应该是123456
            assertEquals("123456", code);
        } catch (Exception e) {
            throw new RuntimeException("反射调用失败", e);
        }
    }
}
