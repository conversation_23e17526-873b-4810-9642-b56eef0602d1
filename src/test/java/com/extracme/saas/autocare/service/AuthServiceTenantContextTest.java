package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.impl.AuthServiceImpl;
import com.extracme.saas.autocare.util.JwtUtil;
import com.extracme.saas.autocare.util.OrgHierarchyUtils;

/**
 * 测试AuthService中租户上下文的正确设置
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthService租户上下文测试")
class AuthServiceTenantContextTest {

    @Mock
    private SmsService smsService;
    
    @Mock
    private TableUserService userService;
    
    @Mock
    private TableRoleService roleService;
    
    @Mock
    private TablePermissionService permissionService;
    
    @Mock
    private TableTenantService tenantService;
    
    @Mock
    private TableUserOrgService userOrgService;
    
    @Mock
    private OrgHierarchyUtils orgHierarchyUtils;
    
    @Mock
    private JwtUtil jwtUtil;

    @InjectMocks
    private AuthServiceImpl authService;

    private SysUser testUser;
    private SysTenant testTenant;

    @BeforeEach
    void setUp() {
        // 清理租户上下文
        TenantContextHolder.clear();
        
        // 创建测试用户
        testUser = new SysUser();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setMobile("***********");
        testUser.setStatus(1);
        testUser.setTenantId(2L); // 租户ID为2
        testUser.setAccountType(1); // 普通用户
        
        // 创建测试租户
        testTenant = new SysTenant();
        testTenant.setId(2L);
        testTenant.setTenantCode("test_tenant");
        testTenant.setTenantName("测试租户");
        testTenant.setStatus(1);
    }

    @Test
    @DisplayName("测试登录时租户上下文的正确设置")
    void testTenantContextInLogin() throws Exception {
        // 准备测试数据
        String mobile = "***********";
        String code = "123456";
        Long tenantId = 2L;
        
        // Mock验证码验证
        when(smsService.verifyCode(mobile, code, "LOGIN")).thenReturn(true);
        
        // Mock用户查询
        when(userService.findTenantIdsByMobile(mobile)).thenReturn(Arrays.asList(tenantId));
        when(userService.findByMobileAndTenantId(mobile, tenantId)).thenReturn(Optional.of(testUser));
        
        // Mock租户查询
        when(tenantService.selectById(tenantId)).thenReturn(testTenant);
        
        // Mock权限查询
        when(permissionService.findByUserId(testUser.getId())).thenReturn(Collections.emptyList());
        
        // Mock组织查询
        List<String> orgIds = Arrays.asList("ORG001", "ORG002");
        when(userOrgService.findOrgIdsByUserId(testUser.getId())).thenReturn(orgIds);
        
        // Mock组织层级计算 - 这里是关键测试点
        List<String> allAccessibleOrgIds = Arrays.asList("ORG001", "ORG002", "ORG001001", "ORG002001");
        when(orgHierarchyUtils.calculateAllAccessibleOrgIds(orgIds)).thenReturn(allAccessibleOrgIds);
        
        // Mock JWT生成
        when(jwtUtil.generateTokenWithUserInfo(any())).thenReturn("test-jwt-token");
        
        // Mock验证码标记为已使用 - markCodeAsUsed方法返回boolean
        when(smsService.markCodeAsUsed(mobile, code, "LOGIN")).thenReturn(true);
        
        // 执行登录
        authService.loginByMobileWithTenant(mobile, code, tenantId);
        
        // 验证组织层级计算方法被调用
        verify(orgHierarchyUtils, times(1)).calculateAllAccessibleOrgIds(orgIds);
        
        // 验证租户上下文在调用过程中被正确设置
        // 注意：由于我们的实现是临时设置然后恢复，所以最终上下文可能被清理
        // 这里主要验证方法被正确调用
    }

    @Test
    @DisplayName("测试租户上下文设置和恢复逻辑")
    void testTenantContextSetAndRestore() {
        // 初始状态：没有租户上下文
        assertNull(TenantContextHolder.getTenantId());
        
        // 设置一个不同的租户上下文
        TenantContextHolder.setTenant(1L);
        assertEquals(1L, TenantContextHolder.getTenantId());
        
        // 模拟我们的逻辑：临时设置为用户的租户ID
        Long currentTenantId = TenantContextHolder.getTenantId();
        boolean needSetTenantContext = (currentTenantId == null || !currentTenantId.equals(testUser.getTenantId()));
        
        assertTrue(needSetTenantContext); // 应该需要设置，因为当前是1L，用户是2L
        
        if (needSetTenantContext) {
            TenantContextHolder.setTenant(testUser.getTenantId());
        }
        
        // 验证租户上下文被正确设置
        assertEquals(testUser.getTenantId(), TenantContextHolder.getTenantId());
        
        // 恢复原状态
        if (needSetTenantContext) {
            if (currentTenantId != null) {
                TenantContextHolder.setTenant(currentTenantId);
            } else {
                TenantContextHolder.clear();
            }
        }
        
        // 验证租户上下文被正确恢复
        assertEquals(1L, TenantContextHolder.getTenantId());
        
        // 清理
        TenantContextHolder.clear();
    }
}
