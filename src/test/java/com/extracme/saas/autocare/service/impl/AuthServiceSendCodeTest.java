package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.SmsService;
import com.extracme.saas.autocare.util.OrgHierarchyUtils;

/**
 * AuthService发送验证码功能测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthService发送验证码功能测试")
public class AuthServiceSendCodeTest {

    @Mock
    private SmsService smsService;

    @Mock
    private TableUserService tableUserService;

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TablePermissionService tablePermissionService;

    @Mock
    private TableTenantService tableTenantService;

    @Mock
    private TableUserOrgService tableUserOrgService;

    @Mock
    private OrgHierarchyUtils orgHierarchyUtils;

    @InjectMocks
    private AuthServiceImpl authService;

    private SysUser mockUser;

    @BeforeEach
    void setUp() {
        // 创建模拟用户
        mockUser = new SysUser();
        mockUser.setId(1L);
        mockUser.setMobile("***********");
        mockUser.setNickname("测试用户");
        mockUser.setStatus(1);
        mockUser.setTenantId(1L);
    }

    @Test
    @DisplayName("发送验证码 - 成功场景")
    void testSendLoginCode_Success() {
        // 准备测试数据
        String mobile = "***********";
        String ipAddress = "***********";

        // 模拟用户存在
        when(tableUserService.findAllByMobile(mobile)).thenReturn(Arrays.asList(mockUser));

        // 模拟短信发送成功
        when(smsService.sendLoginVerificationCode(mobile, ipAddress)).thenReturn(true);

        // 执行测试
        boolean result = authService.sendLoginCode(mobile, ipAddress);

        // 验证结果
        assertTrue(result);
    }

    @Test
    @DisplayName("发送验证码 - 非平台账号")
    void testSendLoginCode_NonPlatformAccount() {
        // 准备测试数据
        String mobile = "***********";
        String ipAddress = "***********";

        // 模拟用户不存在
        when(tableUserService.findAllByMobile(mobile)).thenReturn(Collections.emptyList());

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            authService.sendLoginCode(mobile, ipAddress);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("非平台账号"));
    }

    @Test
    @DisplayName("发送验证码 - 测试账号特殊处理")
    void testSendLoginCode_TestAccount() {
        // 准备测试数据
        String mobile = "***********"; // 测试账号
        String ipAddress = "***********";

        // 模拟用户存在
        when(tableUserService.findAllByMobile(mobile)).thenReturn(Arrays.asList(mockUser));

        // 模拟短信发送成功（测试账号应该不发送真实短信但返回成功）
        when(smsService.sendLoginVerificationCode(mobile, ipAddress)).thenReturn(true);

        // 执行测试
        boolean result = authService.sendLoginCode(mobile, ipAddress);

        // 验证结果
        assertTrue(result);
    }
}
