package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.dto.VehicleInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.vo.DataSyncResultVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 并行处理租户上下文传递测试
 * 验证在并行处理时租户上下文能正确传递到子线程
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class ParallelTenantContextTest {

    @Autowired
    private DataSyncService dataSyncService;

    @BeforeEach
    void setUp() {
        // 清理初始状态
        TenantContextHolder.clear();
    }

    /**
     * 测试ThreadLocal在多线程环境下的传递机制
     */
    @Test
    void testThreadLocalInheritance() throws InterruptedException {
        log.info("测试ThreadLocal在多线程环境下的传递机制");

        // 在主线程设置租户上下文
        Long testTenantId = 1L;
        TenantContextHolder.setTenant(testTenantId);
        
        // 验证主线程上下文
        assertEquals(testTenantId, TenantContextHolder.getTenantId(), "主线程租户上下文应正确设置");
        log.info("主线程租户上下文设置成功 - 租户ID: {}, 线程: {}", 
                TenantContextHolder.getTenantId(), Thread.currentThread().getName());

        // 创建线程池测试
        ExecutorService executor = Executors.newFixedThreadPool(3);
        List<CompletableFuture<Long>> futures = new ArrayList<>();

        // 测试不传递租户上下文的情况（问题场景）
        for (int i = 0; i < 3; i++) {
            final int taskId = i;
            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
                log.info("子线程{}未设置租户上下文 - 租户ID: {}, 线程: {}", 
                        taskId, TenantContextHolder.getTenantId(), Thread.currentThread().getName());
                return TenantContextHolder.getTenantId(); // 应该为null
            }, executor);
            futures.add(future);
        }

        // 等待所有任务完成并验证结果
        for (int i = 0; i < futures.size(); i++) {
            Long tenantIdInSubThread = futures.get(i).join();
            assertNull(tenantIdInSubThread, "子线程" + i + "应该无法获取到租户上下文");
        }

        // 测试手动传递租户上下文的情况（修复后的场景）
        List<CompletableFuture<Long>> fixedFutures = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            final int taskId = i;
            final Long currentTenantId = testTenantId; // 捕获当前租户ID
            
            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 在子线程中手动设置租户上下文
                    TenantContextHolder.setTenant(currentTenantId);
                    log.info("子线程{}手动设置租户上下文 - 租户ID: {}, 线程: {}", 
                            taskId, TenantContextHolder.getTenantId(), Thread.currentThread().getName());
                    return TenantContextHolder.getTenantId();
                } finally {
                    TenantContextHolder.clear();
                }
            }, executor);
            fixedFutures.add(future);
        }

        // 验证修复后的结果
        for (int i = 0; i < fixedFutures.size(); i++) {
            Long tenantIdInSubThread = fixedFutures.get(i).join();
            assertEquals(testTenantId, tenantIdInSubThread, "子线程" + i + "应该能正确获取到租户上下文");
        }

        executor.shutdown();
        assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS), "线程池应能正常关闭");

        // 验证主线程上下文仍然存在
        assertEquals(testTenantId, TenantContextHolder.getTenantId(), "主线程租户上下文应保持不变");

        log.info("ThreadLocal传递机制测试完成");
    }

    /**
     * 测试并行数据同步时的租户上下文传递
     */
    @Test
    void testParallelSyncWithTenantContext() {
        log.info("测试并行数据同步时的租户上下文传递");

        // 创建足够多的测试数据以触发并行处理（假设MIN_PARALLEL_SIZE=50）
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> testData = createLargeTestData(100);
        
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey("test_parallel_key");
        requestDTO.setBatchData(testData);

        try {
            // 执行批量同步（应该触发并行处理）
            DataSyncResultVO result = dataSyncService.syncVehicleInfoBatch(requestDTO, "127.0.0.1");

            // 验证同步结果
            assertNotNull(result, "同步结果不应为空");
            assertNotNull(result.getBatchNo(), "批次号不应为空");
            assertEquals(100, result.getTotalCount(), "总数应为100");
            
            log.info("并行同步完成 - 批次号: {}, 总数: {}, 成功: {}, 失败: {}", 
                    result.getBatchNo(), result.getTotalCount(), 
                    result.getSuccessCount(), result.getFailedCount());

            // 如果有失败记录，检查是否是租户上下文相关的错误
            if (result.getFailedCount() > 0) {
                for (DataSyncResultVO.SyncFailureDetailVO failure : result.getFailureDetails()) {
                    log.warn("同步失败详情: {} - {}", failure.getSourceDataId(), failure.getErrorMessage());
                    // 检查是否包含租户相关的错误信息
                    assertFalse(failure.getErrorMessage().contains("租户"), 
                               "不应该出现租户相关的错误: " + failure.getErrorMessage());
                    assertFalse(failure.getErrorMessage().contains("schema"), 
                               "不应该出现schema相关的错误: " + failure.getErrorMessage());
                }
            }

        } catch (Exception e) {
            log.error("并行同步测试失败", e);
            fail("并行同步不应该抛出异常: " + e.getMessage());
        }

        log.info("并行数据同步租户上下文传递测试完成");
    }

    /**
     * 测试租户上下文在高并发场景下的正确性
     */
    @Test
    void testConcurrentTenantContextIsolation() throws InterruptedException {
        log.info("测试租户上下文在高并发场景下的正确性");

        ExecutorService executor = Executors.newFixedThreadPool(5);
        List<CompletableFuture<Boolean>> futures = new ArrayList<>();

        // 模拟多个租户同时进行数据同步
        for (int tenantId = 1; tenantId <= 5; tenantId++) {
            final Long currentTenantId = (long) tenantId;
            
            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 设置当前租户上下文
                    TenantContextHolder.setTenant(currentTenantId);
                    
                    // 模拟数据处理过程
                    Thread.sleep(100); // 模拟处理时间
                    
                    // 验证租户上下文是否正确
                    Long actualTenantId = TenantContextHolder.getTenantId();
                    boolean isCorrect = currentTenantId.equals(actualTenantId);
                    
                    log.info("租户{}上下文验证 - 期望: {}, 实际: {}, 正确: {}, 线程: {}", 
                            currentTenantId, currentTenantId, actualTenantId, isCorrect, 
                            Thread.currentThread().getName());
                    
                    return isCorrect;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                } finally {
                    TenantContextHolder.clear();
                }
            }, executor);
            
            futures.add(future);
        }

        // 验证所有租户的上下文都正确
        for (int i = 0; i < futures.size(); i++) {
            Boolean isCorrect = futures.get(i).join();
            assertTrue(isCorrect, "租户" + (i + 1) + "的上下文应该正确");
        }

        executor.shutdown();
        assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS), "线程池应能正常关闭");

        log.info("高并发租户上下文隔离测试完成");
    }

    /**
     * 创建大量测试数据以触发并行处理
     */
    private List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> createLargeTestData(int count) {
        List<VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO> dataList = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO data = 
                new VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO();
            data.setId((long) i);
            data.setVin("PARALLEL_VIN_" + String.format("%03d", i));
            data.setVehicleNo("PARALLEL_NO_" + String.format("%03d", i));
            data.setVehicleModelId(1L);
            dataList.add(data);
        }
        
        return dataList;
    }
}
