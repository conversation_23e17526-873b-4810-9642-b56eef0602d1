package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.JingYouLossNotify;
import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.service.impl.JingYouServiceImpl;
import com.extracme.saas.autocare.util.SessionUtils;

@ExtendWith(MockitoExtension.class)
@DisplayName("精友服务单元测试")
class JingYouServiceTest {

    @Mock
    private TableOrgInfoService tableOrgInfoService;

    @Mock
    private TableRepairTaskService tableRepairTaskService;

    @InjectMocks
    private JingYouServiceImpl jingYouService;

    private static final String TASK_NO = "SG1750848604900";
    private static final String ORG_ID = "ORG001";
    private static final Long USER_ID = 1234L;
    private static final String NICKNAME = "测试用户";

    @BeforeEach
    void setUp() {
        // 模拟其他可能需要的依赖注入
    }

    @Test
    @DisplayName("测试正常通知精友状态变更")
    void testLossNotifySuccess() {
        // 准备测试数据
        JingYouLossNotify notify = new JingYouLossNotify("014", "核损退回到定损", "05", "核损退回到定损");
        MtcOrgInfo orgInfo = new MtcOrgInfo();
        orgInfo.setOrgId(ORG_ID);
        orgInfo.setOrgName("测试机构");

        // 模拟SessionUtils静态方法
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getDirectOrgIds).thenReturn(Arrays.asList(ORG_ID));
            mockedSessionUtils.when(SessionUtils::getUserId).thenReturn(Long.valueOf(USER_ID));
            mockedSessionUtils.when(SessionUtils::getNickname).thenReturn(NICKNAME);

            // 模拟服务调用
            when(tableOrgInfoService.findByOrgId(ORG_ID)).thenReturn(orgInfo);

            // 执行测试方法
            assertDoesNotThrow(() -> jingYouService.lossNotify(TASK_NO, notify));

            // 验证调用
            verify(tableOrgInfoService).findByOrgId(ORG_ID);
        }
    }

    @Test
    @DisplayName("测试无组织ID时通知精友状态变更")
    void testLossNotifyWithEmptyOrgId() {
        // 准备测试数据
        JingYouLossNotify notify = new JingYouLossNotify("014", "核损退回到定损", "05", "核损退回到定损");

        // 模拟SessionUtils静态方法返回空组织列表
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getDirectOrgIds).thenReturn(Collections.emptyList());
            mockedSessionUtils.when(SessionUtils::getUserId).thenReturn(Long.valueOf(USER_ID));
            mockedSessionUtils.when(SessionUtils::getNickname).thenReturn(NICKNAME);

            // 执行测试方法
            assertDoesNotThrow(() -> jingYouService.lossNotify(TASK_NO, notify));

            // 验证没有调用组织服务
            verify(tableOrgInfoService, never()).findByOrgId(anyString());
        }
    }

    @Test
    @DisplayName("测试通知精友状态变更时组织信息为空")
    void testLossNotifyWithNullOrgInfo() {
        // 准备测试数据
        JingYouLossNotify notify = new JingYouLossNotify("012", "定损提交到核损", "05", "定损提交到核损");

        // 模拟SessionUtils静态方法
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getDirectOrgIds).thenReturn(Arrays.asList(ORG_ID));
            mockedSessionUtils.when(SessionUtils::getUserId).thenReturn(Long.valueOf(USER_ID));
            mockedSessionUtils.when(SessionUtils::getNickname).thenReturn(NICKNAME);

            // 模拟组织服务返回空
            when(tableOrgInfoService.findByOrgId(ORG_ID)).thenReturn(null);

            // 执行测试方法
            assertDoesNotThrow(() -> jingYouService.lossNotify(TASK_NO, notify));

            // 验证调用
            verify(tableOrgInfoService).findByOrgId(ORG_ID);
        }
    }

    @Test
    @DisplayName("测试通知精友状态变更时参数为空")
    void testLossNotifyWithNullParameters() {
        // 模拟SessionUtils静态方法
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getDirectOrgIds).thenReturn(Arrays.asList(ORG_ID));

            // 测试任务编号为空
            assertThrows(BusinessException.class, () -> jingYouService.lossNotify(null, new JingYouLossNotify("012", "定损提交到核损", "05", "定损提交到核损")));

            // 测试通知对象为空
            assertThrows(BusinessException.class, () -> jingYouService.lossNotify(TASK_NO, null));
        }
    }

    @Test
    @DisplayName("测试通知精友状态变更时发生异常")
    void testLossNotifyWithException() {
        // 准备测试数据
        JingYouLossNotify notify = new JingYouLossNotify("012", "定损提交到核损", "05", "定损提交到核损");

        // 模拟SessionUtils静态方法
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getDirectOrgIds).thenReturn(Arrays.asList(ORG_ID));
            mockedSessionUtils.when(SessionUtils::getUserId).thenReturn(Long.valueOf(USER_ID));
            mockedSessionUtils.when(SessionUtils::getNickname).thenReturn(NICKNAME);

            // 模拟组织服务抛出异常
            when(tableOrgInfoService.findByOrgId(ORG_ID)).thenThrow(new RuntimeException("模拟服务异常"));

            // 执行测试方法并验证异常
            Exception exception = assertThrows(RuntimeException.class, () -> jingYouService.lossNotify(TASK_NO, notify));
            assertEquals("模拟服务异常", exception.getMessage());

            // 验证调用
            verify(tableOrgInfoService).findByOrgId(ORG_ID);
        }
    }
}