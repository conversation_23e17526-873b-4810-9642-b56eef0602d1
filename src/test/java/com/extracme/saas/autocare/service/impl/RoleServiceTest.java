package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.dto.RoleQueryDTO;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysRolePermission;
import com.extracme.saas.autocare.model.vo.RoleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.service.PermissionService;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;

@ExtendWith(MockitoExtension.class)
@DisplayName("角色服务测试")
class RoleServiceTest {

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TablePermissionService tablePermissionService;

    @Mock
    private UserPermissionCacheUtils userPermissionCacheUtils;

    @Mock
    private PermissionService permissionService;

    @InjectMocks
    private RoleServiceImpl roleService;

    private SysRole mockRole;
    private List<Long> mockPermissionIds;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockRole = new SysRole();
        mockRole.setId(1L);
        mockRole.setRoleName("测试角色");
        mockRole.setRoleCode("TEST_ROLE");
        mockRole.setRoleType(3);
        mockRole.setDescription("测试角色描述");
        mockRole.setStatus(1);
        mockRole.setTenantId(1L);
        mockRole.setCreatedTime(new Date());
        mockRole.setUpdatedTime(new Date());

        mockPermissionIds = Arrays.asList(1L, 2L, 3L);
    }

    @Test
    @DisplayName("分配角色权限 - 成功场景")
    void assignPermissions_Success() {
        // 准备测试数据
        Long roleId = 1L;
        List<Long> permissionIds = mockPermissionIds;

        // 执行测试
        roleService.assignPermissions(roleId, permissionIds);

        // 验证调用
        verify(tablePermissionService).deleteByRoleId(roleId);

        // 验证批量创建角色权限关联
        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<SysRolePermission>> captor = (ArgumentCaptor<List<SysRolePermission>>) (ArgumentCaptor<?>) ArgumentCaptor.forClass(List.class);
        verify(tablePermissionService).batchCreate(captor.capture());

        // 验证创建的角色权限关联
        List<SysRolePermission> createdRolePermissions = captor.getValue();
        assertEquals(3, createdRolePermissions.size());

        // 验证每个角色权限关联的属性
        for (int i = 0; i < createdRolePermissions.size(); i++) {
            SysRolePermission rp = createdRolePermissions.get(i);
            assertEquals(roleId, rp.getRoleId());
            assertEquals(permissionIds.get(i), rp.getPermissionId());
            assertNotNull(rp.getCreatedTime());
        }
    }

    @Test
    @DisplayName("分配角色权限 - 空权限列表")
    void assignPermissions_EmptyPermissionList() {
        // 准备测试数据
        Long roleId = 1L;
        List<Long> permissionIds = Collections.emptyList(); // 空列表

        // 执行测试
        roleService.assignPermissions(roleId, permissionIds);

        // 验证调用
        verify(tablePermissionService).deleteByRoleId(roleId);

        // 验证不会调用批量创建
        verify(tablePermissionService, never()).batchCreate(anyList());
    }

    @Test
    @DisplayName("分配角色权限 - 权限列表为null")
    void assignPermissions_NullPermissionList() {
        // 准备测试数据
        Long roleId = 1L;
        List<Long> permissionIds = null;

        // 执行测试
        roleService.assignPermissions(roleId, permissionIds);

        // 验证调用
        verify(tablePermissionService).deleteByRoleId(roleId);

        // 验证不会调用批量创建
        verify(tablePermissionService, never()).batchCreate(anyList());
    }

    @Test
    @DisplayName("获取角色权限ID列表")
    void findPermissionIdsByRoleId() {
        // 准备测试数据
        Long roleId = 1L;
        List<Long> expectedPermissionIds = mockPermissionIds;

        // 设置模拟行为
        when(tablePermissionService.findPermissionIdsByRoleId(roleId)).thenReturn(expectedPermissionIds);

        // 执行测试
        List<Long> result = roleService.findPermissionIdsByRoleId(roleId);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedPermissionIds.size(), result.size());
        assertEquals(expectedPermissionIds, result);

        // 验证调用
        verify(tablePermissionService).findPermissionIdsByRoleId(roleId);
    }

    @Test
    @DisplayName("创建角色 - 成功场景")
    void createRole_Success() {
        // 准备测试数据
        SysRole role = new SysRole();
        role.setRoleName("新角色");
        role.setRoleCode("NEW_ROLE");

        // 设置模拟行为
        when(tableRoleService.insert(any(SysRole.class))).thenAnswer(invocation -> {
            SysRole savedRole = invocation.getArgument(0);
            savedRole.setId(10L);
            return savedRole;
        });

        // 执行测试
        Long roleId = roleService.createRole(role);

        // 验证结果
        assertEquals(10L, roleId);

        // 验证调用
        ArgumentCaptor<SysRole> captor = ArgumentCaptor.forClass(SysRole.class);
        verify(tableRoleService).insert(captor.capture());

        // 验证创建的角色属性
        SysRole createdRole = captor.getValue();
        assertEquals("新角色", createdRole.getRoleName());
        assertEquals("NEW_ROLE", createdRole.getRoleCode());
        assertNotNull(createdRole.getCreatedTime());
        assertNotNull(createdRole.getUpdatedTime());
    }

    @Test
    @DisplayName("更新角色 - 成功场景")
    void updateRole_Success() {
        // 准备测试数据
        SysRole role = new SysRole();
        role.setId(1L);
        role.setRoleName("更新角色");
        role.setRoleCode("UPDATE_ROLE");

        // 执行测试
        roleService.updateRole(role);

        // 验证调用
        ArgumentCaptor<SysRole> captor = ArgumentCaptor.forClass(SysRole.class);
        verify(tableRoleService).updateSelectiveById(captor.capture());

        // 验证更新的角色属性
        SysRole updatedRole = captor.getValue();
        assertEquals(1L, updatedRole.getId());
        assertEquals("更新角色", updatedRole.getRoleName());
        assertEquals("UPDATE_ROLE", updatedRole.getRoleCode());
        assertNotNull(updatedRole.getUpdatedTime());
    }

    @Test
    @DisplayName("删除角色 - 成功场景")
    void deleteRole_Success() {
        // 准备测试数据
        Long roleId = 1L;

        // 执行测试
        roleService.deleteRole(roleId);

        // 验证调用
        ArgumentCaptor<SysRole> roleCaptor = ArgumentCaptor.forClass(SysRole.class);
        verify(tableRoleService).updateSelectiveById(roleCaptor.capture());

        // 验证更新的角色属性
        SysRole updatedRole = roleCaptor.getValue();
        assertEquals(roleId, updatedRole.getId());
        assertNotNull(updatedRole.getUpdatedTime());

        // 验证删除角色权限关联
        verify(tablePermissionService).deleteByRoleId(roleId);
    }

    @Test
    @DisplayName("获取角色详情 - 成功场景")
    void getRole_Success() {
        // 准备测试数据
        Long roleId = 1L;

        // 设置模拟行为
        when(tableRoleService.selectById(roleId)).thenReturn(mockRole);
        when(tablePermissionService.findPermissionIdsByRoleId(roleId)).thenReturn(mockPermissionIds);

        // 执行测试
        RoleVO result = roleService.getRole(roleId);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockRole.getId(), result.getId());
        assertEquals(mockRole.getRoleName(), result.getRoleName());
        assertEquals(mockRole.getRoleCode(), result.getRoleCode());
        assertEquals(mockPermissionIds, result.getPermissionIds());

        // 验证调用
        verify(tableRoleService).selectById(roleId);
        verify(tablePermissionService).findPermissionIdsByRoleId(roleId);
    }

    @Test
    @DisplayName("获取角色详情 - 角色不存在")
    void getRole_NotFound() {
        // 准备测试数据
        Long roleId = 999L;

        // 设置模拟行为
        when(tableRoleService.selectById(roleId)).thenReturn(null);

        // 执行测试
        RoleVO result = roleService.getRole(roleId);

        // 验证结果
        assertNull(result);

        // 验证调用
        verify(tableRoleService).selectById(roleId);
        verify(tablePermissionService, never()).findPermissionIdsByRoleId(anyLong());
    }

    @Test
    @DisplayName("分页查询角色列表 - 按名称查询")
    void getRoleList_WithRoleName() {
        // 准备测试数据
        RoleQueryDTO queryDTO = new RoleQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        queryDTO.setRoleName("测试");

        List<SysRole> roles = Collections.singletonList(mockRole);

        // 设置模拟行为
        when(tableRoleService.findByCondition("测试", null)).thenReturn(roles);
        when(tablePermissionService.findPermissionIdsByRoleId(mockRole.getId())).thenReturn(mockPermissionIds);

        // 执行测试
        BasePageVO<RoleVO> result = roleService.getRoleList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        assertEquals(mockRole.getId(), result.getList().get(0).getId());
        assertEquals(mockRole.getRoleName(), result.getList().get(0).getRoleName());
        assertEquals(mockPermissionIds, result.getList().get(0).getPermissionIds());

        // 验证调用
        verify(tableRoleService).findByCondition("测试", null);
        verify(tablePermissionService).findPermissionIdsByRoleId(mockRole.getId());
    }

    @Test
    @DisplayName("分页查询角色列表 - 无条件查询")
    void getRoleList_NoConditions() {
        // 准备测试数据
        RoleQueryDTO queryDTO = new RoleQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        List<SysRole> roles = Collections.singletonList(mockRole);

        // 设置模拟行为
        when(tableRoleService.findAll()).thenReturn(roles);
        when(tablePermissionService.findPermissionIdsByRoleId(mockRole.getId())).thenReturn(mockPermissionIds);

        // 执行测试
        BasePageVO<RoleVO> result = roleService.getRoleList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        assertEquals(mockRole.getId(), result.getList().get(0).getId());
        assertEquals(mockRole.getRoleName(), result.getList().get(0).getRoleName());
        assertEquals(mockPermissionIds, result.getList().get(0).getPermissionIds());

        // 验证调用
        verify(tableRoleService).findAll();
        verify(tablePermissionService).findPermissionIdsByRoleId(mockRole.getId());
    }

    @Test
    @DisplayName("更新角色时应该刷新权限缓存")
    void updateRole_ShouldRefreshPermissionCache() {
        // 准备测试数据
        SysRole existingRole = new SysRole();
        existingRole.setId(1L);
        existingRole.setRoleName("原角色名");
        existingRole.setTenantId(100L);

        SysRole updateRole = new SysRole();
        updateRole.setId(1L);
        updateRole.setRoleName("新角色名");

        // 设置模拟行为
        when(tableRoleService.selectById(1L)).thenReturn(existingRole);
        when(userPermissionCacheUtils.refreshUserPermissionsByRole(100L, 1L)).thenReturn(3);

        // 执行测试
        roleService.updateRole(updateRole);

        // 验证调用
        verify(tableRoleService).selectById(1L);
        verify(tableRoleService).updateSelectiveById(any(SysRole.class), anyString());
        verify(userPermissionCacheUtils).refreshUserPermissionsByRole(100L, 1L);
    }

    @Test
    @DisplayName("分配权限时应该刷新权限缓存")
    void assignPermissions_ShouldRefreshPermissionCache() {
        // 准备测试数据
        Long roleId = 1L;
        List<Long> permissionIds = Arrays.asList(1L, 2L, 3L);

        SysRole existingRole = new SysRole();
        existingRole.setId(roleId);
        existingRole.setRoleName("测试角色");
        existingRole.setTenantId(100L);

        // 设置模拟行为
        when(tableRoleService.selectById(roleId)).thenReturn(existingRole);
        when(tablePermissionService.findPermissionIdsByRoleId(roleId)).thenReturn(Arrays.asList(4L, 5L));
        when(userPermissionCacheUtils.refreshUserPermissionsByRole(100L, roleId)).thenReturn(2);

        // 执行测试
        roleService.assignPermissions(roleId, permissionIds);

        // 验证调用
        verify(tableRoleService).selectById(roleId);
        verify(tablePermissionService).findPermissionIdsByRoleId(roleId);
        verify(tablePermissionService).deleteByRoleId(roleId);
        verify(tablePermissionService).batchCreate(anyList());
        verify(userPermissionCacheUtils).refreshUserPermissionsByRole(100L, roleId);
    }

    @Test
    @DisplayName("更新角色状态时应该刷新权限缓存")
    void updateRoleStatus_ShouldRefreshPermissionCache() {
        // 准备测试数据
        Long roleId = 1L;
        Integer newStatus = 0; // 禁用

        SysRole existingRole = new SysRole();
        existingRole.setId(roleId);
        existingRole.setRoleName("测试角色");
        existingRole.setStatus(1); // 原状态为启用
        existingRole.setTenantId(100L);

        // 设置模拟行为
        when(tableRoleService.selectById(roleId)).thenReturn(existingRole);
        when(userPermissionCacheUtils.refreshUserPermissionsByRole(100L, roleId)).thenReturn(5);

        // 执行测试
        roleService.updateRoleStatus(roleId, newStatus);

        // 验证调用
        verify(tableRoleService).selectById(roleId);
        verify(tableRoleService).updateSelectiveById(any(SysRole.class), anyString());
        verify(userPermissionCacheUtils).refreshUserPermissionsByRole(100L, roleId);
    }

    @Test
    @DisplayName("删除角色时应该刷新权限缓存")
    void deleteRole_ShouldRefreshPermissionCache() {
        // 准备测试数据
        Long roleId = 1L;

        SysRole existingRole = new SysRole();
        existingRole.setId(roleId);
        existingRole.setRoleName("测试角色");
        existingRole.setTenantId(100L);

        // 设置模拟行为
        when(tableRoleService.selectById(roleId)).thenReturn(existingRole);
        when(userPermissionCacheUtils.refreshUserPermissionsByRole(100L, roleId)).thenReturn(1);

        // 执行测试
        roleService.deleteRole(roleId);

        // 验证调用
        verify(tableRoleService).selectById(roleId);
        verify(tableRoleService).updateSelectiveById(any(SysRole.class), anyString());
        verify(tablePermissionService).deleteByRoleId(roleId);
        verify(userPermissionCacheUtils).refreshUserPermissionsByRole(100L, roleId);
    }
}
