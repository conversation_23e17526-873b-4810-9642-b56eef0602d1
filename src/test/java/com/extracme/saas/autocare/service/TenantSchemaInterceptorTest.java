package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;
import com.extracme.saas.autocare.repository.TableVehicleInfoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 租户Schema拦截器在并行处理中的测试
 * 验证TenantSchemaInterceptor在多线程环境下能正确获取租户上下文
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class TenantSchemaInterceptorTest {

    @Autowired
    private TableVehicleInfoService tableVehicleInfoService;

    @BeforeEach
    void setUp() {
        // 清理初始状态
        TenantContextHolder.clear();
    }

    /**
     * 测试单线程环境下的租户Schema拦截器
     */
    @Test
    void testTenantSchemaInterceptorInSingleThread() {
        log.info("测试单线程环境下的租户Schema拦截器");

        // 设置租户上下文
        Long tenantId = 1L; // 对应 auto_care_dzjt schema
        TenantContextHolder.setTenant(tenantId);
        
        // 验证租户上下文设置正确
        assertEquals(tenantId, TenantContextHolder.getTenantId());
        assertEquals("auto_care_dzjt", TenantContextHolder.getTenantSchema());

        try {
            // 执行数据库查询，应该会触发TenantSchemaInterceptor
            // 这里使用一个简单的查询来测试拦截器是否正常工作
            MtcVehicleInfo vehicle = tableVehicleInfoService.selectById(1L);
            
            // 即使查询结果为null（因为测试数据可能不存在），
            // 只要没有抛出异常就说明拦截器正常工作了
            log.info("单线程查询完成，结果: {}", vehicle != null ? "找到数据" : "未找到数据");
            
        } catch (Exception e) {
            log.error("单线程查询失败", e);
            // 检查是否是租户相关的错误
            String errorMessage = e.getMessage();
            assertFalse(errorMessage.contains("schema"), "不应该出现schema相关错误: " + errorMessage);
            assertFalse(errorMessage.contains("租户"), "不应该出现租户相关错误: " + errorMessage);
        }

        log.info("单线程租户Schema拦截器测试完成");
    }

    /**
     * 测试多线程环境下的租户Schema拦截器
     * 验证修复后的并行处理能正确传递租户上下文
     */
    @Test
    void testTenantSchemaInterceptorInMultiThread() throws InterruptedException {
        log.info("测试多线程环境下的租户Schema拦截器");

        ExecutorService executor = Executors.newFixedThreadPool(3);
        List<CompletableFuture<Boolean>> futures = new ArrayList<>();

        // 测试多个租户的并行查询
        Long[] tenantIds = {1L, 2L}; // 对应不同的schema
        String[] expectedSchemas = {"auto_care_dzjt", "auto_care_extracme"};

        for (int i = 0; i < tenantIds.length; i++) {
            final Long tenantId = tenantIds[i];
            final String expectedSchema = expectedSchemas[i];
            final int taskId = i;

            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 在子线程中设置租户上下文（模拟修复后的并行处理）
                    TenantContextHolder.setTenant(tenantId);
                    
                    // 验证租户上下文设置正确
                    Long actualTenantId = TenantContextHolder.getTenantId();
                    String actualSchema = TenantContextHolder.getTenantSchema();
                    
                    log.info("任务{}设置租户上下文 - 期望租户ID: {}, 实际租户ID: {}, 期望Schema: {}, 实际Schema: {}, 线程: {}", 
                            taskId, tenantId, actualTenantId, expectedSchema, actualSchema, Thread.currentThread().getName());
                    
                    if (!tenantId.equals(actualTenantId) || !expectedSchema.equals(actualSchema)) {
                        log.error("任务{}租户上下文设置错误", taskId);
                        return false;
                    }

                    // 执行数据库查询，测试TenantSchemaInterceptor是否能正确工作
                    MtcVehicleInfo vehicle = tableVehicleInfoService.selectById(1L);
                    
                    log.info("任务{}查询完成，租户ID: {}, Schema: {}, 结果: {}", 
                            taskId, actualTenantId, actualSchema, vehicle != null ? "找到数据" : "未找到数据");
                    
                    return true;
                    
                } catch (Exception e) {
                    log.error("任务{}执行失败", taskId, e);
                    
                    // 检查是否是租户相关的错误
                    String errorMessage = e.getMessage();
                    if (errorMessage.contains("schema") || errorMessage.contains("租户")) {
                        log.error("任务{}出现租户相关错误: {}", taskId, errorMessage);
                        return false;
                    }
                    
                    // 其他错误（如网络、数据库连接等）不影响测试结果
                    log.warn("任务{}出现非租户相关错误，忽略: {}", taskId, errorMessage);
                    return true;
                    
                } finally {
                    // 清理租户上下文
                    TenantContextHolder.clear();
                }
            }, executor);
            
            futures.add(future);
        }

        // 等待所有任务完成并验证结果
        for (int i = 0; i < futures.size(); i++) {
            Boolean success = futures.get(i).join();
            assertTrue(success, "任务" + i + "应该成功执行");
        }

        executor.shutdown();
        assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS), "线程池应能正常关闭");

        log.info("多线程租户Schema拦截器测试完成");
    }

    /**
     * 测试并发场景下的租户上下文隔离
     * 验证不同线程的租户上下文不会相互干扰
     */
    @Test
    void testConcurrentTenantContextIsolation() throws InterruptedException {
        log.info("测试并发场景下的租户上下文隔离");

        ExecutorService executor = Executors.newFixedThreadPool(5);
        List<CompletableFuture<Boolean>> futures = new ArrayList<>();

        // 创建多个并发任务，每个任务使用不同的租户ID
        for (int i = 1; i <= 5; i++) {
            final Long tenantId = (long) (i % 2 == 0 ? 2 : 1); // 交替使用租户1和租户2
            final int taskId = i;

            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 设置租户上下文
                    TenantContextHolder.setTenant(tenantId);
                    
                    // 模拟一些处理时间
                    Thread.sleep(50 + (int)(Math.random() * 100));
                    
                    // 验证租户上下文是否正确
                    Long actualTenantId = TenantContextHolder.getTenantId();
                    String actualSchema = TenantContextHolder.getTenantSchema();
                    
                    boolean isCorrect = tenantId.equals(actualTenantId);
                    
                    log.info("并发任务{}验证 - 期望租户ID: {}, 实际租户ID: {}, Schema: {}, 正确: {}, 线程: {}", 
                            taskId, tenantId, actualTenantId, actualSchema, isCorrect, Thread.currentThread().getName());
                    
                    if (!isCorrect) {
                        return false;
                    }

                    // 执行数据库操作测试
                    try {
                        tableVehicleInfoService.selectById((long) taskId);
                        log.debug("并发任务{}数据库查询完成", taskId);
                    } catch (Exception e) {
                        // 数据库操作错误不影响租户上下文隔离测试
                        log.debug("并发任务{}数据库查询异常（忽略）: {}", taskId, e.getMessage());
                    }
                    
                    // 再次验证租户上下文是否仍然正确
                    actualTenantId = TenantContextHolder.getTenantId();
                    isCorrect = tenantId.equals(actualTenantId);
                    
                    log.info("并发任务{}最终验证 - 期望租户ID: {}, 实际租户ID: {}, 正确: {}", 
                            taskId, tenantId, actualTenantId, isCorrect);
                    
                    return isCorrect;
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("并发任务{}被中断", taskId);
                    return false;
                } catch (Exception e) {
                    log.error("并发任务{}执行异常", taskId, e);
                    return false;
                } finally {
                    TenantContextHolder.clear();
                }
            }, executor);
            
            futures.add(future);
        }

        // 验证所有任务的租户上下文隔离都正确
        for (int i = 0; i < futures.size(); i++) {
            Boolean success = futures.get(i).join();
            assertTrue(success, "并发任务" + (i + 1) + "的租户上下文隔离应该正确");
        }

        executor.shutdown();
        assertTrue(executor.awaitTermination(15, TimeUnit.SECONDS), "线程池应能正常关闭");

        log.info("并发租户上下文隔离测试完成");
    }
}
