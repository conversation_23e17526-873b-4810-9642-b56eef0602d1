package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.UserVO;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.repository.TableRepairDepotInfoService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.repository.TableUserService;

/**
 * UserServiceImpl 机构名称功能测试
 */
@ExtendWith(MockitoExtension.class)
public class UserServiceOrgNamesTest {

    @Mock
    private TableUserService tableUserService;

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TableUserRoleService tableUserRoleService;

    @Mock
    private TableUserOrgService tableUserOrgService;

    @Mock
    private TableOrgInfoService tableOrgInfoService;

    @Mock
    private TableTenantService tableTenantService;

    @Mock
    private TableRepairDepotInfoService tableRepairDepotInfoService;

    @InjectMocks
    private UserServiceImpl userService;

    @BeforeEach
    public void setUp() {
        // 初始化测试环境
    }

    /**
     * 测试getUserList方法的机构名称设置功能
     * 暂时跳过，因为涉及复杂的PageHelper Mock
     */
    // @Test
    public void testGetUserList_WithOrgNames() {
        // 暂时跳过这个测试，专注于测试核心的机构名称设置逻辑
    }

    /**
     * 测试getUserById方法的机构名称设置功能
     */
    @Test
    public void testGetUserById_WithOrgNames() {
        // 准备测试数据
        Long userId = 1L;
        SysUser user = createTestUser(userId, "张三", "13800138001");
        List<String> orgIds = Arrays.asList("ORG001", "ORG002");

        // 模拟租户数据
        SysTenant tenant = new SysTenant();
        tenant.setId(1L);
        tenant.setTenantName("测试租户");

        // 模拟机构信息数据
        MtcOrgInfo org1 = createTestOrgInfo("ORG001", "管理部");
        MtcOrgInfo org2 = createTestOrgInfo("ORG002", "销售部");
        List<MtcOrgInfo> orgInfos = Arrays.asList(org1, org2);

        // 设置Mock行为
        when(tableUserService.selectById(userId)).thenReturn(user);
        when(tableRoleService.findByUserId(userId)).thenReturn(Collections.emptyList());
        when(tableUserOrgService.findOrgIdsByUserId(userId)).thenReturn(orgIds);
        when(tableTenantService.selectById(1L)).thenReturn(tenant);
        when(tableOrgInfoService.findByOrgIds(orgIds)).thenReturn(orgInfos);

        // 执行测试
        UserVO result = userService.getUserById(userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(userId, result.getId());
        assertEquals(orgIds, result.getOrgIds());
        assertEquals(Arrays.asList("管理部", "销售部"), result.getOrgNames());
    }

    /**
     * 测试用户没有机构关联的情况
     */
    @Test
    public void testGetUserById_NoOrgs() {
        // 准备测试数据
        Long userId = 1L;
        SysUser user = createTestUser(userId, "张三", "13800138001");

        // 模拟租户数据
        SysTenant tenant = new SysTenant();
        tenant.setId(1L);
        tenant.setTenantName("测试租户");

        // 设置Mock行为
        when(tableUserService.selectById(userId)).thenReturn(user);
        when(tableRoleService.findByUserId(userId)).thenReturn(Collections.emptyList());
        when(tableUserOrgService.findOrgIdsByUserId(userId)).thenReturn(Collections.emptyList());
        when(tableTenantService.selectById(1L)).thenReturn(tenant);

        // 执行测试
        UserVO result = userService.getUserById(userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(userId, result.getId());
        assertTrue(result.getOrgIds().isEmpty());
        assertTrue(result.getOrgNames().isEmpty());
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用户
     */
    private SysUser createTestUser(Long id, String nickname, String mobile) {
        SysUser user = new SysUser();
        user.setId(id);
        user.setNickname(nickname);
        user.setMobile(mobile);
        user.setTenantId(1L);
        user.setStatus(1);
        return user;
    }

    /**
     * 创建测试机构信息
     */
    private MtcOrgInfo createTestOrgInfo(String orgId, String orgName) {
        MtcOrgInfo orgInfo = new MtcOrgInfo();
        orgInfo.setOrgId(orgId);
        orgInfo.setOrgName(orgName);
        return orgInfo;
    }
}
