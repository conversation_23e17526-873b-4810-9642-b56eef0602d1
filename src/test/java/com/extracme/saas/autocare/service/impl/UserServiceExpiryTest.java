package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.repository.TableRepairDepotInfoService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;

@ExtendWith(MockitoExtension.class)
@DisplayName("用户服务过期天数计算测试")
class UserServiceExpiryTest {

    @Mock
    private TableUserService tableUserService;

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TableUserRoleService tableUserRoleService;

    @Mock
    private TableUserOrgService tableUserOrgService;

    @Mock
    private TableOrgInfoService tableOrgInfoService;

    @Mock
    private TableTenantService tableTenantService;

    @Mock
    private TableRepairDepotInfoService tableRepairDepotInfoService;

    @Mock
    private UserPermissionCacheUtils userPermissionCacheUtils;

    @InjectMocks
    private UserServiceImpl userService;

    private LoginUser mockLoginUser;
    private SysUser mockUser;
    private SysTenant mockTenant;

    @BeforeEach
    void setUp() {
        // 创建模拟用户
        mockUser = new SysUser();
        mockUser.setId(1L);
        mockUser.setTenantId(100L);
        mockUser.setUsername("testuser");
        mockUser.setNickname("Test User");
        mockUser.setMobile("13800138000");

        // 创建模拟登录用户
        mockLoginUser = new LoginUser();
        mockLoginUser.setUser(mockUser);
        mockLoginUser.setTenantId(100L);

        // 创建模拟租户
        mockTenant = new SysTenant();
        mockTenant.setId(100L);
        mockTenant.setTenantName("测试租户");
        mockTenant.setTenantCode("TEST_TENANT");
    }

    @Test
    @DisplayName("获取当前用户信息 - 用户未登录")
    void getCurrentUserWithExpiry_UserNotLoggedIn() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 模拟用户未登录
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(null);

            // 执行测试
            LoginUser result = userService.getCurrentUserWithExpiry();

            // 验证结果
            assertNull(result);
        }
    }

    @Test
    @DisplayName("获取当前用户信息 - 租户未设置过期时间")
    void getCurrentUserWithExpiry_TenantNoExpireTime() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 模拟用户已登录
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);

            // 模拟租户未设置过期时间
            mockTenant.setExpireTime(null);
            when(tableTenantService.selectById(100L)).thenReturn(mockTenant);

            // 执行测试
            LoginUser result = userService.getCurrentUserWithExpiry();

            // 验证结果
            assertNotNull(result);
            assertNull(result.getDaysUntilExpiry()); // 未设置过期时间应返回null
        }
    }

    @Test
    @DisplayName("获取当前用户信息 - 租户不存在")
    void getCurrentUserWithExpiry_TenantNotFound() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 模拟用户已登录
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);

            // 模拟租户不存在
            when(tableTenantService.selectById(100L)).thenReturn(null);

            // 执行测试
            LoginUser result = userService.getCurrentUserWithExpiry();

            // 验证结果
            assertNotNull(result);
            assertNull(result.getDaysUntilExpiry()); // 租户不存在应返回null
        }
    }

    @Test
    @DisplayName("获取当前用户信息 - 租户还有30天过期")
    void getCurrentUserWithExpiry_TenantExpireIn30Days() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 模拟用户已登录
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);

            // 设置租户30天后过期
            LocalDate futureDate = LocalDate.now(ZoneId.of("GMT+8")).plusDays(30);
            Date expireTime = Date.from(futureDate.atStartOfDay(ZoneId.of("GMT+8")).toInstant());
            mockTenant.setExpireTime(expireTime);
            when(tableTenantService.selectById(100L)).thenReturn(mockTenant);

            // 执行测试
            LoginUser result = userService.getCurrentUserWithExpiry();

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDaysUntilExpiry());
            assertEquals(30, result.getDaysUntilExpiry().intValue());
        }
    }

    @Test
    @DisplayName("获取当前用户信息 - 租户已过期5天")
    void getCurrentUserWithExpiry_TenantExpired5Days() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 模拟用户已登录
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);

            // 设置租户5天前过期
            LocalDate pastDate = LocalDate.now(ZoneId.of("GMT+8")).minusDays(5);
            Date expireTime = Date.from(pastDate.atStartOfDay(ZoneId.of("GMT+8")).toInstant());
            mockTenant.setExpireTime(expireTime);
            when(tableTenantService.selectById(100L)).thenReturn(mockTenant);

            // 执行测试
            LoginUser result = userService.getCurrentUserWithExpiry();

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDaysUntilExpiry());
            assertEquals(-5, result.getDaysUntilExpiry().intValue()); // 负数表示已过期
        }
    }

    @Test
    @DisplayName("获取当前用户信息 - 租户今天过期")
    void getCurrentUserWithExpiry_TenantExpireToday() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 模拟用户已登录
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);

            // 设置租户今天过期
            LocalDate today = LocalDate.now(ZoneId.of("GMT+8"));
            Date expireTime = Date.from(today.atStartOfDay(ZoneId.of("GMT+8")).toInstant());
            mockTenant.setExpireTime(expireTime);
            when(tableTenantService.selectById(100L)).thenReturn(mockTenant);

            // 执行测试
            LoginUser result = userService.getCurrentUserWithExpiry();

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDaysUntilExpiry());
            assertEquals(0, result.getDaysUntilExpiry().intValue()); // 0表示今天过期
        }
    }

    @Test
    @DisplayName("获取当前用户信息 - 数据库查询异常")
    void getCurrentUserWithExpiry_DatabaseException() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 模拟用户已登录
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);

            // 模拟数据库查询异常
            when(tableTenantService.selectById(anyLong())).thenThrow(new RuntimeException("Database error"));

            // 执行测试
            LoginUser result = userService.getCurrentUserWithExpiry();

            // 验证结果
            assertNotNull(result);
            assertNull(result.getDaysUntilExpiry()); // 异常时应返回null
        }
    }

    @Test
    @DisplayName("获取当前用户信息 - 租户ID为null")
    void getCurrentUserWithExpiry_TenantIdNull() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 创建租户ID为null的登录用户
            LoginUser loginUserWithNullTenant = new LoginUser();
            loginUserWithNullTenant.setUser(mockUser);
            loginUserWithNullTenant.setTenantId(null);

            // 模拟用户已登录
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(loginUserWithNullTenant);

            // 执行测试
            LoginUser result = userService.getCurrentUserWithExpiry();

            // 验证结果
            assertNotNull(result);
            assertNull(result.getDaysUntilExpiry()); // 租户ID为null应返回null
        }
    }
}
