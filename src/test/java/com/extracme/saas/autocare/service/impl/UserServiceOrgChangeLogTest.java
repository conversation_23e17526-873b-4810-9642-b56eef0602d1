package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.dto.UserCreateDTO;
import com.extracme.saas.autocare.model.dto.UserUpdateDTO;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.repository.TableRepairDepotInfoService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserOrgService;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.util.OperateLogUtil;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 用户机构关联变更日志记录测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("用户机构关联变更日志记录测试")
class UserServiceOrgChangeLogTest {

    @Mock
    private TableUserService tableUserService;

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TableUserRoleService tableUserRoleService;

    @Mock
    private TableUserOrgService tableUserOrgService;

    @Mock
    private TableOrgInfoService tableOrgInfoService;

    @Mock
    private TableTenantService tableTenantService;

    @Mock
    private TableRepairDepotInfoService tableRepairDepotInfoService;

    @InjectMocks
    private UserServiceImpl userService;

    private SysUser mockUser;
    private UserCreateDTO createDTO;
    private UserUpdateDTO updateDTO;

    @BeforeEach
    void setUp() {
        // 模拟用户数据
        mockUser = new SysUser();
        mockUser.setId(1L);
        mockUser.setTenantId(100L);
        mockUser.setUsername("testuser");
        mockUser.setNickname("测试用户");
        mockUser.setMobile("13800138000");
        mockUser.setEmail("<EMAIL>");
        mockUser.setStatus(1);
        mockUser.setCreatedTime(new Date());
        mockUser.setUpdatedTime(new Date());

        // 创建用户DTO
        createDTO = new UserCreateDTO();
        createDTO.setNickname("测试用户");
        createDTO.setMobile("13800138000");
        createDTO.setEmail("<EMAIL>");
        createDTO.setStatus(1);
        createDTO.setTenantId(100L);
        createDTO.setOrgIds(Arrays.asList("ORG001", "ORG002"));

        // 更新用户DTO
        updateDTO = new UserUpdateDTO();
        updateDTO.setId(1L);
        updateDTO.setNickname("测试用户更新");
        updateDTO.setMobile("13800138000");
        updateDTO.setOrgIds(Arrays.asList("ORG002", "ORG003")); // 删除ORG001，保留ORG002，新增ORG003
    }

    @Test
    @DisplayName("创建用户时记录机构关联日志")
    void createUser_ShouldLogOrgAssociation() {
        try (MockedStatic<OperateLogUtil> operateLogUtilMock = Mockito.mockStatic(OperateLogUtil.class);
             MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {

            // 设置模拟行为
            when(tableUserService.countByMobile(anyString())).thenReturn(0);
            
            SysUser insertedUser = new SysUser();
            insertedUser.setId(1L);
            when(tableUserService.insert(any(SysUser.class))).thenReturn(insertedUser);
            
            when(tableUserOrgService.batchInsert(anyLong(), anyList())).thenReturn(2);

            // 执行测试
            Long userId = userService.createUser(createDTO);

            // 验证结果
            assertNotNull(userId);
            assertEquals(1L, userId);

            // 验证机构关联插入
            verify(tableUserOrgService).batchInsert(eq(1L), eq(Arrays.asList("ORG001", "ORG002")));

            // 验证操作日志记录（包含机构关联信息）
            operateLogUtilMock.verify(() -> OperateLogUtil.recordUserCreateDetailed(
                eq("13800138000"),
                eq("测试用户"),
                eq(Arrays.asList("ORG001", "ORG002"))
            ));
        }
    }

    @Test
    @DisplayName("更新用户时记录机构关联变更日志")
    void updateUser_ShouldLogOrgChanges() {
        try (MockedStatic<OperateLogUtil> operateLogUtilMock = Mockito.mockStatic(OperateLogUtil.class);
             MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {

            // 设置模拟行为
            when(tableUserService.selectById(1L)).thenReturn(mockUser);
            when(tableUserService.updateById(any(SysUser.class), anyString())).thenReturn(1);

            // 模拟获取更新前的机构关联
            when(tableUserOrgService.findOrgIdsByUserId(1L))
                .thenReturn(Arrays.asList("ORG001", "ORG002"));

            when(tableUserOrgService.updateUserOrgs(anyLong(), anyList())).thenReturn(true);

            sessionUtilsMock.when(SessionUtils::getUsername).thenReturn("admin");

            // 执行测试
            userService.updateUser(updateDTO);

            // 验证机构关联更新
            verify(tableUserOrgService).findOrgIdsByUserId(1L);
            verify(tableUserOrgService).updateUserOrgs(eq(1L), eq(Arrays.asList("ORG002", "ORG003")));

            // 验证用户更新日志记录（包含机构关联变更）
            operateLogUtilMock.verify(() -> OperateLogUtil.recordUserUpdateDetailed(
                any(SysUser.class),
                any(SysUser.class),
                eq(Arrays.asList("ORG001", "ORG002")), // 更新前的机构列表
                eq(Arrays.asList("ORG002", "ORG003"))  // 更新后的机构列表
            ));
        }
    }

    @Test
    @DisplayName("创建用户时使用单个orgId的向后兼容性")
    void createUser_WithSingleOrgId_ShouldLogCorrectly() {
        try (MockedStatic<OperateLogUtil> operateLogUtilMock = Mockito.mockStatic(OperateLogUtil.class)) {

            // 设置机构ID列表
            createDTO.setOrgIds(Collections.singletonList("ORG001"));

            // 设置模拟行为
            when(tableUserService.countByMobile(anyString())).thenReturn(0);
            
            SysUser insertedUser = new SysUser();
            insertedUser.setId(1L);
            when(tableUserService.insert(any(SysUser.class))).thenReturn(insertedUser);
            
            when(tableUserOrgService.batchInsert(anyLong(), anyList())).thenReturn(1);

            // 执行测试
            Long userId = userService.createUser(createDTO);

            // 验证结果
            assertNotNull(userId);
            assertEquals(1L, userId);

            // 验证机构关联插入（应该转换为单元素列表）
            verify(tableUserOrgService).batchInsert(eq(1L), eq(Collections.singletonList("ORG001")));

            // 验证操作日志记录（包含机构关联信息）
            operateLogUtilMock.verify(() -> OperateLogUtil.recordUserCreateDetailed(
                eq("13800138000"),
                eq("测试用户"),
                eq(Collections.singletonList("ORG001"))
            ));
        }
    }

    @Test
    @DisplayName("更新用户时使用单个orgId的向后兼容性")
    void updateUser_WithSingleOrgId_ShouldLogCorrectly() {
        try (MockedStatic<OperateLogUtil> operateLogUtilMock = Mockito.mockStatic(OperateLogUtil.class);
             MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {

            // 设置机构ID列表
            updateDTO.setOrgIds(Collections.singletonList("ORG003"));

            // 设置模拟行为
            when(tableUserService.selectById(1L)).thenReturn(mockUser);
            when(tableUserService.updateById(any(SysUser.class), anyString())).thenReturn(1);

            when(tableUserOrgService.findOrgIdsByUserId(1L))
                .thenReturn(Arrays.asList("ORG001", "ORG002"));

            when(tableUserOrgService.updateUserOrgs(anyLong(), anyList())).thenReturn(true);

            sessionUtilsMock.when(SessionUtils::getUsername).thenReturn("admin");

            // 执行测试
            userService.updateUser(updateDTO);

            // 验证机构关联更新（应该转换为单元素列表）
            verify(tableUserOrgService).updateUserOrgs(eq(1L), eq(Collections.singletonList("ORG003")));

            // 验证机构关联变更日志记录
            operateLogUtilMock.verify(() -> OperateLogUtil.recordUserOrgChangeDetailed(
                eq(1L),
                eq("13800138000"),
                eq("测试用户"),
                eq(Arrays.asList("ORG001", "ORG002")),
                eq(Collections.singletonList("ORG003"))
            ));
        }
    }
}
