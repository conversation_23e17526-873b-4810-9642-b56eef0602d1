package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.model.dto.UserCreateDTO;
import com.extracme.saas.autocare.model.dto.UserQueryDTO;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.UserVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableUserOrgService;

/**
 * 用户多机构关联功能测试
 * 
 * 测试用户与机构的多对多关联功能，包括：
 * 1. 创建用户时关联多个机构
 * 2. 更新用户机构关联
 * 3. 根据机构查询用户
 * 4. 向后兼容性测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class UserMultiOrgTest {

    @Autowired
    private UserService userService;

    @Autowired
    private TableUserOrgService tableUserOrgService;

    /**
     * 测试创建用户时关联多个机构
     */
    @Test
    public void testCreateUserWithMultipleOrgs() {
        // 准备测试数据
        UserCreateDTO createDTO = new UserCreateDTO();
        createDTO.setMobile("***********");
        createDTO.setNickname("测试用户1");
        createDTO.setEmail("<EMAIL>");
        createDTO.setStatus(1);
        createDTO.setAccountType(1);
        createDTO.setTenantId(1L);
        
        // 设置多个机构ID
        List<String> orgIds = Arrays.asList("ORG001", "ORG002", "ORG003");
        createDTO.setOrgIds(orgIds);

        // 执行创建操作
        Long userId = userService.createUser(createDTO);
        assertNotNull(userId);

        // 验证机构关联是否正确创建
        List<String> actualOrgIds = tableUserOrgService.findOrgIdsByUserId(userId);
        assertEquals(3, actualOrgIds.size());
        assertTrue(actualOrgIds.containsAll(orgIds));

        // 验证用户详情中包含机构信息
        UserVO userVO = userService.getUserById(userId);
        assertNotNull(userVO.getOrgIds());
        assertEquals(3, userVO.getOrgIds().size());
        assertTrue(userVO.getOrgIds().containsAll(orgIds));
    }

    /**
     * 测试向后兼容：使用单个orgId创建用户
     */
    @Test
    public void testCreateUserWithSingleOrgId() {
        // 准备测试数据
        UserCreateDTO createDTO = new UserCreateDTO();
        createDTO.setMobile("***********");
        createDTO.setNickname("测试用户2");
        createDTO.setEmail("<EMAIL>");
        createDTO.setStatus(1);
        createDTO.setAccountType(1);
        createDTO.setTenantId(1L);
        
        // 设置机构ID列表
        createDTO.setOrgIds(Collections.singletonList("ORG001"));

        // 执行创建操作
        Long userId = userService.createUser(createDTO);
        assertNotNull(userId);

        // 验证机构关联是否正确创建
        List<String> actualOrgIds = tableUserOrgService.findOrgIdsByUserId(userId);
        assertEquals(1, actualOrgIds.size());
        assertEquals("ORG001", actualOrgIds.get(0));

        // 验证用户详情
        UserVO userVO = userService.getUserById(userId);
        assertNotNull(userVO.getOrgIds());
        assertEquals(1, userVO.getOrgIds().size());
        assertEquals("ORG001", userVO.getOrgIds().get(0));
    }

    /**
     * 测试更新用户机构关联
     */
    @Test
    public void testUpdateUserOrgs() {
        // 先创建一个用户
        UserCreateDTO createDTO = new UserCreateDTO();
        createDTO.setMobile("***********");
        createDTO.setNickname("测试用户3");
        createDTO.setEmail("<EMAIL>");
        createDTO.setStatus(1);
        createDTO.setAccountType(1);
        createDTO.setTenantId(1L);
        createDTO.setOrgIds(Arrays.asList("ORG001", "ORG002"));

        Long userId = userService.createUser(createDTO);

        // 更新机构关联
        List<String> newOrgIds = Arrays.asList("ORG003", "ORG004", "ORG005");
        boolean success = userService.updateUserOrgs(userId, newOrgIds);
        assertTrue(success);

        // 验证更新结果
        List<String> actualOrgIds = tableUserOrgService.findOrgIdsByUserId(userId);
        assertEquals(3, actualOrgIds.size());
        assertTrue(actualOrgIds.containsAll(newOrgIds));

        // 验证旧的关联已被删除
        assertFalse(actualOrgIds.contains("ORG001"));
        assertFalse(actualOrgIds.contains("ORG002"));
    }

    /**
     * 测试根据机构查询用户
     */
    @Test
    public void testQueryUsersByOrgs() {
        // 创建多个用户，关联不同的机构
        createTestUser("***********", "用户4", Arrays.asList("ORG001", "ORG002"));
        createTestUser("13800138005", "用户5", Arrays.asList("ORG002", "ORG003"));
        createTestUser("13800138006", "用户6", Arrays.asList("ORG003", "ORG004"));

        // 查询ORG002机构的用户
        UserQueryDTO queryDTO = new UserQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        // 移除机构筛选功能，不再设置orgIds

        BasePageVO<UserVO> result = userService.getUserList(queryDTO);
        
        // 验证查询结果
        assertNotNull(result);
        assertEquals(2, result.getList().size()); // 用户4和用户5

        // 验证返回的用户都包含ORG002
        for (UserVO user : result.getList()) {
            assertTrue(user.getOrgIds().contains("ORG002"));
        }
    }

    /**
     * 测试多机构查询
     */
    @Test
    public void testQueryUsersByMultipleOrgs() {
        // 创建测试用户
        createTestUser("13800138007", "用户7", Arrays.asList("ORG001"));
        createTestUser("13800138008", "用户8", Arrays.asList("ORG002"));
        createTestUser("13800138009", "用户9", Arrays.asList("ORG003"));

        // 查询多个机构的用户
        UserQueryDTO queryDTO = new UserQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        // 移除机构筛选功能，不再设置orgIds

        BasePageVO<UserVO> result = userService.getUserList(queryDTO);
        
        // 验证查询结果
        assertNotNull(result);
        assertEquals(2, result.getList().size()); // 用户7和用户9
    }

    /**
     * 测试根据机构查询用户（Service层方法）
     */
    @Test
    public void testGetUsersByOrgIds() {
        // 创建测试用户
        createTestUser("13800138010", "用户10", Arrays.asList("ORG001", "ORG002"));
        createTestUser("13800138011", "用户11", Arrays.asList("ORG002", "ORG003"));

        // 查询指定机构的用户
        List<SysUser> users = userService.getUsersByOrgIds(Arrays.asList("ORG002"));
        
        // 验证结果
        assertNotNull(users);
        assertEquals(2, users.size());
    }

    /**
     * 创建测试用户的辅助方法
     */
    private Long createTestUser(String mobile, String nickname, List<String> orgIds) {
        UserCreateDTO createDTO = new UserCreateDTO();
        createDTO.setMobile(mobile);
        createDTO.setNickname(nickname);
        createDTO.setEmail(mobile + "@example.com");
        createDTO.setStatus(1);
        createDTO.setAccountType(1);
        createDTO.setTenantId(1L);
        createDTO.setOrgIds(orgIds);

        return userService.createUser(createDTO);
    }
}
