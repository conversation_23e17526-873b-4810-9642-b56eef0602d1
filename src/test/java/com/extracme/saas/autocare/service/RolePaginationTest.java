package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.dto.RoleQueryDTO;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.vo.RoleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.service.impl.RoleServiceImpl;

/**
 * 角色分页查询测试
 * 验证分页逻辑的正确性，确保分页信息准确无误
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("角色分页查询测试")
class RolePaginationTest {

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TablePermissionService tablePermissionService;

    @InjectMocks
    private RoleServiceImpl roleService;

    private SysRole testRole1;
    private SysRole testRole2;
    private SysRole testRole3;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testRole1 = new SysRole();
        testRole1.setId(1L);
        testRole1.setRoleName("管理员");
        testRole1.setDescription("系统管理员角色");
        testRole1.setStatus(1);
        testRole1.setTenantId(1L);
        testRole1.setCreatedTime(new Date());

        testRole2 = new SysRole();
        testRole2.setId(2L);
        testRole2.setRoleName("操作员");
        testRole2.setDescription("系统操作员角色");
        testRole2.setStatus(1);
        testRole2.setTenantId(1L);
        testRole2.setCreatedTime(new Date());

        testRole3 = new SysRole();
        testRole3.setId(3L);
        testRole3.setRoleName("查看员");
        testRole3.setDescription("只读查看角色");
        testRole3.setStatus(1);
        testRole3.setTenantId(1L);
        testRole3.setCreatedTime(new Date());
    }

    @Test
    @DisplayName("测试分页查询 - 验证分页信息准确性")
    void testGetRoleListPagination() {
        // 模拟查询返回3条记录
        List<SysRole> mockRoles = Arrays.asList(testRole1, testRole2, testRole3);
        when(tableRoleService.findByCondition(any(), any())).thenReturn(mockRoles);
        
        // 模拟权限查询
        when(tablePermissionService.findPermissionIdsByRoleId(1L)).thenReturn(Arrays.asList(1L, 2L));
        when(tablePermissionService.findPermissionIdsByRoleId(2L)).thenReturn(Arrays.asList(2L, 3L));
        when(tablePermissionService.findPermissionIdsByRoleId(3L)).thenReturn(Arrays.asList(3L));

        // 创建查询条件
        RoleQueryDTO queryDTO = new RoleQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        queryDTO.setRoleName("管理");

        // 执行查询
        BasePageVO<RoleVO> result = roleService.getRoleList(queryDTO);

        // 验证结果
        assertNotNull(result, "分页结果不应为空");
        assertNotNull(result.getList(), "数据列表不应为空");
        assertEquals(3, result.getList().size(), "数据列表大小应为3");

        // 验证数据转换正确性
        RoleVO firstRole = result.getList().get(0);
        assertEquals("管理员", firstRole.getRoleName(), "角色名称应正确转换");
        assertEquals("系统管理员角色", firstRole.getDescription(), "角色描述应正确转换");
        assertNotNull(firstRole.getPermissionIds(), "权限ID列表不应为空");
        assertEquals(2, firstRole.getPermissionIds().size(), "权限ID数量应正确");

        // 验证分页信息（注意：在单元测试中PageHelper不会真正生效，所以这里主要验证结构）
        assertNotNull(result.getPageNum(), "页码不应为空");
        assertNotNull(result.getPageSize(), "页大小不应为空");
        assertNotNull(result.getTotal(), "总记录数不应为空");
    }

    @Test
    @DisplayName("测试空结果分页查询")
    void testGetRoleListWithEmptyResult() {
        // 模拟查询返回空结果
        when(tableRoleService.findByCondition(any(), any())).thenReturn(Collections.emptyList());

        // 创建查询条件
        RoleQueryDTO queryDTO = new RoleQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        queryDTO.setRoleName("不存在的角色");

        // 执行查询
        BasePageVO<RoleVO> result = roleService.getRoleList(queryDTO);

        // 验证结果
        assertNotNull(result, "分页结果不应为空");
        assertNotNull(result.getList(), "数据列表不应为空");
        assertEquals(0, result.getList().size(), "空结果时数据列表大小应为0");
    }

    @Test
    @DisplayName("测试分页查询 - 验证VO转换不影响分页信息")
    void testPaginationInfoConsistency() {
        // 模拟查询返回2条记录
        List<SysRole> mockRoles = Arrays.asList(testRole1, testRole2);
        when(tableRoleService.findByCondition(any(), any())).thenReturn(mockRoles);
        
        // 模拟权限查询
        when(tablePermissionService.findPermissionIdsByRoleId(any())).thenReturn(Arrays.asList(1L, 2L));

        // 创建查询条件
        RoleQueryDTO queryDTO = new RoleQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        // 执行查询
        BasePageVO<RoleVO> result = roleService.getRoleList(queryDTO);

        // 验证VO转换后数据完整性
        assertNotNull(result, "分页结果不应为空");
        assertEquals(2, result.getList().size(), "转换后的VO列表大小应与原始数据一致");
        
        // 验证每个VO都包含完整信息
        for (RoleVO roleVO : result.getList()) {
            assertNotNull(roleVO.getId(), "角色ID不应为空");
            assertNotNull(roleVO.getRoleName(), "角色名称不应为空");
            assertNotNull(roleVO.getPermissionIds(), "权限ID列表不应为空");
        }
    }

    @Test
    @DisplayName("测试分页查询 - 验证BasePageVO.of()方法正确性")
    void testBasePageVOCreation() {
        // 模拟查询返回1条记录
        List<SysRole> mockRoles = Arrays.asList(testRole1);
        when(tableRoleService.findByCondition(any(), any())).thenReturn(mockRoles);
        when(tablePermissionService.findPermissionIdsByRoleId(any())).thenReturn(Arrays.asList(1L));

        // 创建查询条件
        RoleQueryDTO queryDTO = new RoleQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        // 执行查询
        BasePageVO<RoleVO> result = roleService.getRoleList(queryDTO);

        // 验证BasePageVO结构完整性
        assertNotNull(result, "BasePageVO不应为空");
        assertNotNull(result.getList(), "数据列表不应为空");
        
        // 验证分页字段存在（即使在单元测试中值可能为默认值）
        assertNotNull(result.getPageNum(), "页码字段应存在");
        assertNotNull(result.getPageSize(), "页大小字段应存在");
        assertNotNull(result.getTotal(), "总记录数字段应存在");
        assertNotNull(result.getPages(), "总页数字段应存在");
        assertNotNull(result.getHasNextPage(), "是否有下一页字段应存在");
        assertNotNull(result.getHasPreviousPage(), "是否有上一页字段应存在");
        assertNotNull(result.getIsFirstPage(), "是否为第一页字段应存在");
        assertNotNull(result.getIsLastPage(), "是否为最后一页字段应存在");
    }
}
