package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.AutoCareSaasApplication;
import com.extracme.saas.autocare.enums.ActivityDefinitionEnum;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.model.entity.ActivityInstance;
import com.extracme.saas.autocare.repository.TableActivityInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 活动实例状态更新测试
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = AutoCareSaasApplication.class)
@ActiveProfiles("test")
@Transactional
public class ActivityInstanceUpdateTest {

    @Autowired
    private TableActivityInstanceService tableActivityInstanceService;

    @Test
    public void testUpdateStatusCode() {
        log.info("开始测试活动实例状态码更新功能");

        // 1. 创建测试数据
        ActivityInstance activityInstance = new ActivityInstance();
        activityInstance.setInstanceId(1L);
        activityInstance.setToActivityCode(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode());
        activityInstance.setFromActivityCode(null);
        activityInstance.setTransitionId(1L);
        activityInstance.setStartTime(new Date());
        activityInstance.setCurrentStatusCode(ActivityStatusEnum.UNPROCESSED.getCode());
        activityInstance.setOperator("test-user");
        activityInstance.setRemarks("测试活动实例");

        // 2. 插入测试数据
        ActivityInstance inserted = tableActivityInstanceService.insert(activityInstance, "test-user");
        assertNotNull(inserted);
        assertNotNull(inserted.getId());
        log.info("创建测试活动实例成功，ID: {}, 初始状态: {}", inserted.getId(), inserted.getCurrentStatusCode());

        // 3. 验证初始状态
        ActivityInstance beforeUpdate = tableActivityInstanceService.selectById(inserted.getId());
        assertNotNull(beforeUpdate);
        assertEquals(ActivityStatusEnum.UNPROCESSED.getCode(), beforeUpdate.getCurrentStatusCode());
        log.info("验证初始状态成功，ID: {}, 状态: {}", beforeUpdate.getId(), beforeUpdate.getCurrentStatusCode());

        // 4. 更新状态码
        String newStatusCode = ActivityStatusEnum.PROCESSING.getCode();
        int updateResult = tableActivityInstanceService.updateStatusCode(inserted.getId(), newStatusCode);
        log.info("状态码更新操作完成，ID: {}, 影响行数: {}", inserted.getId(), updateResult);

        // 5. 验证更新结果
        assertTrue(updateResult > 0, "更新操作应该影响至少1行");

        ActivityInstance afterUpdate = tableActivityInstanceService.selectById(inserted.getId());
        assertNotNull(afterUpdate);
        assertEquals(newStatusCode, afterUpdate.getCurrentStatusCode());
        log.info("验证更新结果成功，ID: {}, 期望状态: {}, 实际状态: {}", 
                afterUpdate.getId(), newStatusCode, afterUpdate.getCurrentStatusCode());

        // 6. 测试相同状态码更新（应该跳过）
        int sameStatusUpdateResult = tableActivityInstanceService.updateStatusCode(inserted.getId(), newStatusCode);
        log.info("相同状态码更新操作完成，ID: {}, 影响行数: {}", inserted.getId(), sameStatusUpdateResult);
        assertEquals(1, sameStatusUpdateResult, "相同状态码更新应该返回1（逻辑成功）");

        // 7. 测试无效参数
        int invalidResult1 = tableActivityInstanceService.updateStatusCode(null, newStatusCode);
        assertEquals(0, invalidResult1, "空ID应该返回0");

        int invalidResult2 = tableActivityInstanceService.updateStatusCode(inserted.getId(), null);
        assertEquals(0, invalidResult2, "空状态码应该返回0");

        log.info("活动实例状态码更新功能测试完成");
    }

    @Test
    public void testUpdateNonExistentActivityInstance() {
        log.info("开始测试更新不存在的活动实例");

        // 尝试更新不存在的活动实例
        Long nonExistentId = 999999L;
        String newStatusCode = ActivityStatusEnum.PROCESSING.getCode();
        
        int updateResult = tableActivityInstanceService.updateStatusCode(nonExistentId, newStatusCode);
        log.info("更新不存在活动实例的结果，ID: {}, 影响行数: {}", nonExistentId, updateResult);
        
        assertEquals(0, updateResult, "更新不存在的活动实例应该返回0");
        
        log.info("更新不存在的活动实例测试完成");
    }
}
