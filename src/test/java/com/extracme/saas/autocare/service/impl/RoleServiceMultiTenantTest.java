package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.dto.RoleQueryDTO;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.RoleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.service.PermissionService;
import com.extracme.saas.autocare.util.SessionUtils;

@ExtendWith(MockitoExtension.class)
@DisplayName("角色服务多租户测试")
class RoleServiceMultiTenantTest {

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TablePermissionService tablePermissionService;

    @Mock
    private PermissionService permissionService;

    @InjectMocks
    private RoleServiceImpl roleService;

    private RoleQueryDTO queryDTO;
    private SysRole mockRole;
    private LoginUser mockLoginUser;

    @BeforeEach
    void setUp() {
        // 查询DTO
        queryDTO = new RoleQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        // 模拟角色数据
        mockRole = new SysRole();
        mockRole.setId(1L);
        mockRole.setTenantId(100L);
        mockRole.setRoleName("测试角色");
        mockRole.setRoleCode("TEST_ROLE");
        mockRole.setRoleType(2);
        mockRole.setDescription("测试角色描述");
        mockRole.setStatus(1);
        mockRole.setCreatedTime(new Date());
        mockRole.setUpdatedTime(new Date());

        // 模拟登录用户
        mockLoginUser = new LoginUser();
        SysUser loginUserEntity = new SysUser();
        loginUserEntity.setId(2L);
        loginUserEntity.setTenantId(100L);
        loginUserEntity.setAccountType(1); // 普通用户
        mockLoginUser.setUser(loginUserEntity);
    }

    @Test
    @DisplayName("普通用户查询角色 - 应该只能查询自己租户下的角色")
    void getRoleList_RegularUser_ShouldFilterByTenantId() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为普通用户
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);

            // 设置模拟行为
            when(tableRoleService.findByCondition(eq(null), null))
                .thenReturn(Arrays.asList(mockRole));
            when(tablePermissionService.findPermissionIdsByRoleId(anyLong()))
                .thenReturn(Arrays.asList(1L, 2L));

            // 执行测试
            BasePageVO<RoleVO> result = roleService.getRoleList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getList());
            assertEquals(1, result.getList().size());

            // 验证调用时传入了租户ID过滤条件
            verify(tableRoleService).findByCondition(eq(null), null);
            verify(tablePermissionService).findPermissionIdsByRoleId(1L);
        }
    }

    @Test
    @DisplayName("超级管理员查询角色 - 应该能查询所有租户的角色")
    void getRoleList_SuperAdmin_ShouldNotFilterByTenantId() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为超级管理员
            SysUser superAdminUser = new SysUser();
            superAdminUser.setId(3L);
            superAdminUser.setTenantId(100L);
            superAdminUser.setAccountType(0); // 超级管理员
            LoginUser superAdminLoginUser = new LoginUser();
            superAdminLoginUser.setUser(superAdminUser);

            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(superAdminLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(true);

            // 设置模拟行为
            when(tableRoleService.findByCondition(eq(null), eq(null)))
                .thenReturn(Arrays.asList(mockRole));
            when(tablePermissionService.findPermissionIdsByRoleId(anyLong()))
                .thenReturn(Arrays.asList(1L, 2L));

            // 执行测试
            BasePageVO<RoleVO> result = roleService.getRoleList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getList());
            assertEquals(1, result.getList().size());

            // 验证调用时没有传入租户ID过滤条件（传入null）
            verify(tableRoleService).findByCondition(eq(null), eq(null));
            verify(tablePermissionService).findPermissionIdsByRoleId(1L);
        }
    }

    @Test
    @DisplayName("普通用户带角色名称查询 - 应该同时应用查询条件和租户过滤")
    void getRoleList_RegularUserWithRoleName_ShouldApplyBothFilters() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为普通用户
            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(mockLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(false);

            // 设置查询条件
            queryDTO.setRoleName("测试");

            // 设置模拟行为
            when(tableRoleService.findByCondition(eq("测试"), null))
                .thenReturn(Arrays.asList(mockRole));
            when(tablePermissionService.findPermissionIdsByRoleId(anyLong()))
                .thenReturn(Arrays.asList(1L, 2L));

            // 执行测试
            BasePageVO<RoleVO> result = roleService.getRoleList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getList());
            assertEquals(1, result.getList().size());

            // 验证调用时同时传入了查询条件和租户ID过滤条件
            verify(tableRoleService).findByCondition(eq("测试"), null);
            verify(tablePermissionService).findPermissionIdsByRoleId(1L);
        }
    }

    @Test
    @DisplayName("超级管理员带角色名称查询 - 应该只应用查询条件不过滤租户")
    void getRoleList_SuperAdminWithRoleName_ShouldOnlyApplyRoleNameFilter() {
        try (MockedStatic<SessionUtils> sessionUtilsMock = Mockito.mockStatic(SessionUtils.class)) {
            // 设置当前登录用户为超级管理员
            SysUser superAdminUser = new SysUser();
            superAdminUser.setId(3L);
            superAdminUser.setTenantId(100L);
            superAdminUser.setAccountType(0); // 超级管理员
            LoginUser superAdminLoginUser = new LoginUser();
            superAdminLoginUser.setUser(superAdminUser);

            sessionUtilsMock.when(SessionUtils::getLoginUser).thenReturn(superAdminLoginUser);
            sessionUtilsMock.when(SessionUtils::isSuperAdmin).thenReturn(true);

            // 设置查询条件
            queryDTO.setRoleName("测试");

            // 设置模拟行为
            when(tableRoleService.findByCondition(eq("测试"), eq(null)))
                .thenReturn(Arrays.asList(mockRole));
            when(tablePermissionService.findPermissionIdsByRoleId(anyLong()))
                .thenReturn(Arrays.asList(1L, 2L));

            // 执行测试
            BasePageVO<RoleVO> result = roleService.getRoleList(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getList());
            assertEquals(1, result.getList().size());

            // 验证调用时传入了查询条件但没有租户ID过滤条件
            verify(tableRoleService).findByCondition(eq("测试"), eq(null));
            verify(tablePermissionService).findPermissionIdsByRoleId(1L);
        }
    }
}
