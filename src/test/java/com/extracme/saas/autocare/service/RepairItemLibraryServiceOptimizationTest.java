package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.enums.RepairItemTypeEnum;
import com.extracme.saas.autocare.enums.StatusEnum;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibrary;
import com.extracme.saas.autocare.model.excel.RepairItemLibraryExportExcel;
import com.extracme.saas.autocare.service.impl.RepairItemLibraryServiceImpl;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 维修项目库服务优化测试
 * 测试使用Optional简化数据转换逻辑的效果
 */
@DisplayName("维修项目库服务优化测试")
public class RepairItemLibraryServiceOptimizationTest {

    @Test
    @DisplayName("测试Optional简化数据转换逻辑")
    public void testOptionalDataConversion() {
        // 创建测试数据
        MtcRepairItemLibrary entity = new MtcRepairItemLibrary();
        entity.setItemNo("TEST001");
        entity.setItemName("测试维修项目");
        entity.setItemType(RepairItemTypeEnum.REPAIR.getCode());
        entity.setVehicleModelInfo("测试车型");
        entity.setHourFeeNationalMarketPrice(new BigDecimal("100.50"));
        entity.setHourFeeNationalHighestPrice(null); // 测试空值处理
        entity.setMaterialCostNationalMarketPrice(new BigDecimal("200.75"));
        entity.setMaterialCostNationalHighestPrice(new BigDecimal("250.00"));
        entity.setStatus(StatusEnum.ENABLE.getCode());

        // 创建服务实例
        RepairItemLibraryServiceImpl service = new RepairItemLibraryServiceImpl();
        
        // 使用反射调用私有方法进行测试
        RepairItemLibraryExportExcel result = (RepairItemLibraryExportExcel) 
            ReflectionTestUtils.invokeMethod(service, "convertToExportExcel", entity);

        // 验证转换结果
        assertNotNull(result);
        assertEquals("TEST001", result.getItemNo());
        assertEquals("测试维修项目", result.getItemName());
        assertEquals("维修", result.getItemTypeName());
        assertEquals("测试车型", result.getVehicleModelInfo());
        assertEquals(new BigDecimal("100.50"), result.getHourFeeNationalMarketPrice());
        assertNull(result.getHourFeeNationalHighestPrice()); // 验证空值处理
        assertEquals(new BigDecimal("200.75"), result.getMaterialCostNationalMarketPrice());
        assertEquals(new BigDecimal("250.00"), result.getMaterialCostNationalHighestPrice());
        assertEquals("启用", result.getStatusName());
    }

    @Test
    @DisplayName("测试Optional处理空值情况")
    public void testOptionalWithNullValues() {
        // 创建包含空值的测试数据
        MtcRepairItemLibrary entity = new MtcRepairItemLibrary();
        entity.setItemNo("TEST002");
        entity.setItemName("测试项目2");
        entity.setItemType(null); // 空值
        entity.setVehicleModelInfo(null); // 空值
        entity.setHourFeeNationalMarketPrice(new BigDecimal("50.00"));
        entity.setStatus(null); // 空值

        // 创建服务实例
        RepairItemLibraryServiceImpl service = new RepairItemLibraryServiceImpl();
        
        // 使用反射调用私有方法进行测试
        RepairItemLibraryExportExcel result = (RepairItemLibraryExportExcel) 
            ReflectionTestUtils.invokeMethod(service, "convertToExportExcel", entity);

        // 验证空值处理
        assertNotNull(result);
        assertEquals("TEST002", result.getItemNo());
        assertEquals("测试项目2", result.getItemName());
        assertEquals("", result.getItemTypeName()); // 空值应该转换为空字符串
        assertEquals("", result.getVehicleModelInfo()); // 空值应该转换为空字符串
        assertEquals(new BigDecimal("50.00"), result.getHourFeeNationalMarketPrice());
        assertEquals("", result.getStatusName()); // 空值应该转换为空字符串
    }

    @Test
    @DisplayName("测试枚举转换功能")
    public void testEnumConversion() {
        // 测试项目类型枚举转换
        assertEquals("保养", RepairItemTypeEnum.getNameByCode(RepairItemTypeEnum.MAINTENANCE.getCode()));
        assertEquals("终端", RepairItemTypeEnum.getNameByCode(RepairItemTypeEnum.TERMINAL.getCode()));
        assertEquals("维修", RepairItemTypeEnum.getNameByCode(RepairItemTypeEnum.REPAIR.getCode()));
        assertEquals("", RepairItemTypeEnum.getNameByCode(null));

        // 测试状态枚举转换
        assertEquals("启用", StatusEnum.getByCode(StatusEnum.ENABLE.getCode()).getDescription());
        assertEquals("禁用", StatusEnum.getByCode(StatusEnum.DISABLE.getCode()).getDescription());
        assertNull(StatusEnum.getByCode((Integer) null));
    }

    @Test
    @DisplayName("测试Optional链式调用的优势")
    public void testOptionalChaining() {
        // 模拟传统三元表达式的写法
        Integer status = 1;
        String traditionalWay = status != null ? 
            (StatusEnum.getByCode(status) != null ? StatusEnum.getByCode(status).getDescription() : "") : "";

        // 使用Optional的写法
        String optionalWay = Optional.ofNullable(status)
                .map(StatusEnum::getByCode)
                .map(StatusEnum::getDescription)
                .orElse("");

        // 验证结果相同
        assertEquals(traditionalWay, optionalWay);
        assertEquals("启用", optionalWay);

        // 测试空值情况
        status = null;
        traditionalWay = status != null ? 
            (StatusEnum.getByCode(status) != null ? StatusEnum.getByCode(status).getDescription() : "") : "";
        optionalWay = Optional.ofNullable(status)
                .map(StatusEnum::getByCode)
                .map(StatusEnum::getDescription)
                .orElse("");

        assertEquals(traditionalWay, optionalWay);
        assertEquals("", optionalWay);
    }

    @Test
    @DisplayName("测试价格优先级逻辑优化")
    public void testPricePriorityLogic() {
        // 测试本地价格优先逻辑
        BigDecimal localPrice = new BigDecimal("100.00");
        BigDecimal nationalPrice = new BigDecimal("120.00");

        // 使用Optional的优化写法
        BigDecimal finalPrice = Optional.ofNullable(localPrice)
                .orElse(nationalPrice);

        assertEquals(new BigDecimal("100.00"), finalPrice);

        // 测试本地价格为空的情况
        localPrice = null;
        finalPrice = Optional.ofNullable(localPrice)
                .orElse(nationalPrice);

        assertEquals(new BigDecimal("120.00"), finalPrice);

        // 测试两个都为空的情况
        nationalPrice = null;
        finalPrice = Optional.ofNullable(localPrice)
                .orElse(nationalPrice);

        assertNull(finalPrice);
    }
}
