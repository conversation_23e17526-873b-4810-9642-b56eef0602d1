package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.entity.MtcVehicleModel;
import com.extracme.saas.autocare.repository.TableVehicleModelService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 测试数据同步场景下的 SessionUtils 处理
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class DataSyncSessionUtilsTest {

    @Autowired
    private TableVehicleModelService tableVehicleModelService;

    @Test
    public void testUpdateWithoutSession() {
        log.info("测试在没有用户会话的情况下进行数据更新操作");
        
        try {
            // 创建一个测试车型对象
            MtcVehicleModel vehicleModel = new MtcVehicleModel();
            vehicleModel.setId(999999L); // 使用一个不存在的ID
            vehicleModel.setVehicleModelName("测试车型");
            
            // 尝试更新操作，这应该不会抛出异常
            int result = tableVehicleModelService.updateSelectiveById(vehicleModel);
            
            log.info("更新操作完成，影响行数：{}", result);
            
            // 验证操作没有抛出异常
            assert true; // 如果到达这里说明没有抛出异常
            
        } catch (Exception e) {
            log.error("更新操作失败：{}", e.getMessage(), e);
            throw e; // 重新抛出异常，让测试失败
        }
        
        log.info("测试完成：在没有用户会话的情况下，数据更新操作正常处理");
    }

    @Test
    public void testInsertWithoutSession() {
        log.info("测试在没有用户会话的情况下进行数据插入操作");
        
        try {
            // 创建一个测试车型对象
            MtcVehicleModel vehicleModel = new MtcVehicleModel();
            vehicleModel.setVehicleModelName("测试车型插入");
            
            // 尝试插入操作，这应该不会抛出异常
            MtcVehicleModel result = tableVehicleModelService.insert(vehicleModel);
            
            log.info("插入操作完成，结果：{}", result != null ? "成功" : "失败");
            
            // 验证操作没有抛出异常
            assert true; // 如果到达这里说明没有抛出异常
            
        } catch (Exception e) {
            log.error("插入操作失败：{}", e.getMessage(), e);
            throw e; // 重新抛出异常，让测试失败
        }
        
        log.info("测试完成：在没有用户会话的情况下，数据插入操作正常处理");
    }
}
