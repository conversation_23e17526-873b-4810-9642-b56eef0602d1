package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.entity.ActivityDefinition;
import com.extracme.saas.autocare.model.entity.ActivityInstance;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.entity.WorkflowTemplate;
import com.extracme.saas.autocare.model.vo.workflow.TaskProgressVO;
import com.extracme.saas.autocare.repository.TableActivityDefinitionService;
import com.extracme.saas.autocare.repository.TableActivityInstanceService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.repository.TableWorkflowTemplateService;
import com.extracme.saas.autocare.service.impl.WorkflowServiceImpl;
import com.extracme.saas.autocare.util.SessionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.atLeastOnce;

/**
 * WorkflowService任务进度查询方法单元测试
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@ExtendWith(MockitoExtension.class)
class WorkflowServiceTaskProgressTest {

    @Mock
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Mock
    private TableWorkflowTemplateService tableWorkflowTemplateService;

    @Mock
    private TableActivityDefinitionService tableActivityDefinitionService;

    @Mock
    private TableActivityInstanceService tableActivityInstanceService;

    @InjectMocks
    private WorkflowServiceImpl workflowService;

    private WorkflowInstance workflowInstance;
    private WorkflowTemplate workflowTemplate;
    private ActivityDefinition currentActivity;
    private List<ActivityDefinition> allActivities;
    private List<ActivityInstance> activityInstances;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        setupTestData();
    }

    @Test
    void testGetTaskProgress_Success() {
        // 准备测试数据
        String businessId = "TASK_001";
        Integer tenantId = 1;

        // Mock SessionUtils
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getTenantId).thenReturn(1L);

            // Mock repository方法
            when(tableWorkflowInstanceService.selectByBusinessIdAndTenantId(businessId, tenantId))
                    .thenReturn(workflowInstance);
            when(tableWorkflowTemplateService.selectById(workflowInstance.getWorkflowId()))
                    .thenReturn(workflowTemplate);
            when(tableActivityDefinitionService.selectByWorkflowId(workflowInstance.getWorkflowId()))
                    .thenReturn(allActivities);
            when(tableActivityInstanceService.selectByInstanceId(workflowInstance.getId()))
                    .thenReturn(activityInstances);
            when(tableActivityDefinitionService.selectByActivityCode(workflowInstance.getCurrentActivityCode()))
                    .thenReturn(currentActivity);

            // 执行测试方法
            TaskProgressVO result = workflowService.getTaskProgress(businessId);

            // 验证结果
            assertNotNull(result);
            assertEquals(businessId, result.getBusinessId());
            assertEquals(workflowInstance.getId(), result.getInstanceId());
            assertEquals(workflowInstance.getWorkflowId(), result.getWorkflowId());
            assertEquals(workflowTemplate.getWorkflowName(), result.getWorkflowName());
            assertEquals(workflowInstance.getCurrentActivityCode(), result.getCurrentActivityCode());
            assertEquals(currentActivity.getActivityName(), result.getCurrentActivityName());
            assertEquals(workflowInstance.getStatusCode(), result.getStatus());
            assertNotNull(result.getStatusName()); // 验证状态名称不为空
            assertEquals(workflowInstance.getTenantId(), result.getTenantId());

            // 验证列表不为空
            assertNotNull(result.getActivityDefinitions());
            assertNotNull(result.getActivityInstances());

            // 验证方法调用
            verify(tableWorkflowInstanceService).selectByBusinessIdAndTenantId(businessId, tenantId);
            verify(tableWorkflowTemplateService).selectById(workflowInstance.getWorkflowId());
            verify(tableActivityDefinitionService).selectByWorkflowId(workflowInstance.getWorkflowId());
            verify(tableActivityInstanceService).selectByInstanceId(workflowInstance.getId());
            // selectByActivityCode会被调用多次：一次用于获取当前活动节点，多次用于转换ActivityInstance
            verify(tableActivityDefinitionService, atLeastOnce()).selectByActivityCode(anyString());
        }
    }

    @Test
    void testGetTaskProgress_NullBusinessId() {
        // 测试空的任务编号
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            workflowService.getTaskProgress(null);
        });

        assertEquals("任务编号不能为空", exception.getMessage());
    }

    @Test
    void testGetTaskProgress_EmptyBusinessId() {
        // 测试空字符串任务编号
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            workflowService.getTaskProgress("   ");
        });

        assertEquals("任务编号不能为空", exception.getMessage());
    }

    @Test
    void testGetTaskProgress_WorkflowInstanceNotFound() {
        // 准备测试数据
        String businessId = "INVALID_TASK";
        Integer tenantId = 1;

        // Mock SessionUtils
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getTenantId).thenReturn(1L);

            // Mock repository方法返回null
            when(tableWorkflowInstanceService.selectByBusinessIdAndTenantId(businessId, tenantId))
                    .thenReturn(null);

            // 执行测试并验证异常
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                workflowService.getTaskProgress(businessId);
            });

            assertEquals("未找到对应的工作流实例，任务编号: " + businessId, exception.getMessage());
        }
    }

    @Test
    void testGetTaskProgress_MultiTenantIsolation() {
        // 测试多租户隔离
        String businessId = "TASK_001";
        Integer tenantId = 2; // 不同的租户ID

        // Mock SessionUtils
        try (MockedStatic<SessionUtils> sessionUtilsMock = mockStatic(SessionUtils.class)) {
            sessionUtilsMock.when(SessionUtils::getTenantId).thenReturn(2L);

            // Mock repository方法返回null（模拟租户隔离）
            when(tableWorkflowInstanceService.selectByBusinessIdAndTenantId(businessId, tenantId))
                    .thenReturn(null);

            // 执行测试并验证异常
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                workflowService.getTaskProgress(businessId);
            });

            assertEquals("未找到对应的工作流实例，任务编号: " + businessId, exception.getMessage());

            // 验证使用了正确的租户ID进行查询
            verify(tableWorkflowInstanceService).selectByBusinessIdAndTenantId(businessId, tenantId);
        }
    }

    /**
     * 初始化测试数据
     */
    private void setupTestData() {
        // 创建工作流实例
        workflowInstance = new WorkflowInstance();
        workflowInstance.setId(1L);
        workflowInstance.setWorkflowId(1L);
        workflowInstance.setBusinessId("TASK_001");
        workflowInstance.setCurrentActivityCode("VEHICLE_TRANSFER");
        workflowInstance.setStatusCode("UNPROCESSED");
        workflowInstance.setTenantId(1);
        workflowInstance.setCreateTime(new Date());
        workflowInstance.setCreateBy("testUser");
        workflowInstance.setUpdateTime(new Date());
        workflowInstance.setUpdateBy("testUser");

        // 创建工作流模板
        workflowTemplate = new WorkflowTemplate();
        workflowTemplate.setId(1L);
        workflowTemplate.setWorkflowName("标准维修流程");

        // 创建当前活动节点
        currentActivity = new ActivityDefinition();
        currentActivity.setId(1L);
        currentActivity.setActivityCode("VEHICLE_TRANSFER");
        currentActivity.setActivityName("车辆交接");
        currentActivity.setSequence(1);

        // 创建所有活动节点列表
        allActivities = new ArrayList<>();
        allActivities.add(currentActivity);

        ActivityDefinition secondActivity = new ActivityDefinition();
        secondActivity.setId(2L);
        secondActivity.setActivityCode("INSPECTION");
        secondActivity.setActivityName("进保预审");
        secondActivity.setSequence(2);
        allActivities.add(secondActivity);

        // 创建活动实例列表
        activityInstances = new ArrayList<>();
        ActivityInstance activityInstance = new ActivityInstance();
        activityInstance.setId(1L);
        activityInstance.setInstanceId(1L);
        activityInstance.setToActivityCode("VEHICLE_TRANSFER");
        activityInstance.setCurrentStatusCode("UNPROCESSED");
        activityInstance.setStartTime(new Date());
        activityInstance.setOperator("testUser");
        activityInstances.add(activityInstance);
    }
}
