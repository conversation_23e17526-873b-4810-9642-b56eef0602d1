package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateQueryDTO;
import com.extracme.saas.autocare.model.entity.WorkflowTemplate;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowTemplateVO;
import com.extracme.saas.autocare.repository.TableActivityDefinitionService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableWorkflowTemplateService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 工作流服务多租户访问控制测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("工作流服务多租户访问控制测试")
public class WorkflowServiceMultiTenantTest {

    @Mock
    private TableWorkflowTemplateService tableWorkflowTemplateService;

    @Mock
    private TableTenantService tableTenantService;

    @Mock
    private TableActivityDefinitionService tableActivityDefinitionService;

    @InjectMocks
    private WorkflowServiceImpl workflowService;

    private WorkflowTemplate template1;
    private WorkflowTemplate template2;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        template1 = new WorkflowTemplate();
        template1.setId(1L);
        template1.setWorkflowName("租户1模板");
        template1.setTenantId(1);
        template1.setTaskType(1);
        template1.setRepairFactoryType(1);
        template1.setSubProductLine(1);
        template1.setPartsLibraryType(1);
        template1.setIsActive(1);

        template2 = new WorkflowTemplate();
        template2.setId(2L);
        template2.setWorkflowName("租户2模板");
        template2.setTenantId(2);
        template2.setTaskType(1);
        template2.setRepairFactoryType(1);
        template2.setSubProductLine(1);
        template2.setPartsLibraryType(1);
        template2.setIsActive(1);
    }

    @Test
    @DisplayName("超级管理员应该能查询所有租户的工作流模板")
    void testSuperAdminCanAccessAllTenants() {
        // 模拟超级管理员登录
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(true);
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            // 模拟查询返回所有租户的模板
            when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class)))
                    .thenReturn(Arrays.asList(template1, template2));

            // 执行查询
            WorkflowTemplateQueryDTO queryDTO = new WorkflowTemplateQueryDTO();
            List<WorkflowTemplateVO> result = workflowService.listTemplates(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());

            // 验证查询条件中没有设置租户过滤
            verify(tableWorkflowTemplateService).findByCondition(argThat(dto -> dto.getTenantId() == null));
        }
    }

    @Test
    @DisplayName("普通用户只能查询自己租户的工作流模板")
    void testRegularUserCanOnlyAccessOwnTenant() {
        // 模拟普通用户登录（租户ID为1）
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(false);
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);

            // 模拟查询只返回当前租户的模板
            when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class)))
                    .thenReturn(Arrays.asList(template1));

            // 执行查询
            WorkflowTemplateQueryDTO queryDTO = new WorkflowTemplateQueryDTO();
            List<WorkflowTemplateVO> result = workflowService.listTemplates(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("租户1模板", result.get(0).getWorkflowName());

            // 验证查询条件中设置了租户过滤
            verify(tableWorkflowTemplateService).findByCondition(argThat(dto -> 
                dto.getTenantId() != null && dto.getTenantId().equals(1)));
        }
    }

    @Test
    @DisplayName("普通用户租户ID为空时应该有警告日志")
    void testRegularUserWithNullTenantId() {
        // 模拟普通用户登录但租户ID为空
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(false);
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(null);

            // 模拟查询返回空结果
            when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class)))
                    .thenReturn(Arrays.asList());

            // 执行查询
            WorkflowTemplateQueryDTO queryDTO = new WorkflowTemplateQueryDTO();
            List<WorkflowTemplateVO> result = workflowService.listTemplates(queryDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.size());

            // 验证查询条件中没有设置租户过滤（因为租户ID为空）
            verify(tableWorkflowTemplateService).findByCondition(argThat(dto -> dto.getTenantId() == null));
        }
    }
}
