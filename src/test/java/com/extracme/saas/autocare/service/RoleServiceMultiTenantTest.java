package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.service.impl.RoleServiceImpl;
import com.extracme.saas.autocare.util.SessionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 角色服务多租户功能测试
 */
@ExtendWith(MockitoExtension.class)
class RoleServiceMultiTenantTest {

    @Mock
    private TableRoleService tableRoleService;

    @InjectMocks
    private RoleServiceImpl roleService;

    private SysRole testRole;
    private SysUser superAdminUser;
    private SysUser normalUser;
    private LoginUser superAdminLoginUser;
    private LoginUser normalLoginUser;

    @BeforeEach
    void setUp() {
        // 创建测试角色
        testRole = new SysRole();
        testRole.setId(1L);
        testRole.setRoleName("测试角色");
        testRole.setRoleCode("TEST_ROLE");
        testRole.setStatus(1);

        // 创建超级管理员用户
        superAdminUser = new SysUser();
        superAdminUser.setId(1L);
        superAdminUser.setUsername("superadmin");
        superAdminUser.setAccountType(0); // 超级管理员
        superAdminUser.setTenantId(1L);

        // 创建普通用户
        normalUser = new SysUser();
        normalUser.setId(2L);
        normalUser.setUsername("normaluser");
        normalUser.setAccountType(1); // 普通用户
        normalUser.setTenantId(2L);

        // 创建登录用户对象
        superAdminLoginUser = new LoginUser();
        superAdminLoginUser.setUser(superAdminUser);
        superAdminLoginUser.setTenantId(1L);

        normalLoginUser = new LoginUser();
        normalLoginUser.setUser(normalUser);
        normalLoginUser.setTenantId(2L);
    }

    @Test
    void testCreateRole_SuperAdmin_ShouldSetTenantIdToMinusOne() {
        // 模拟超级管理员登录
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(true);
            mockedSessionUtils.when(SessionUtils::getUsername).thenReturn("superadmin");

            when(tableRoleService.insert(any(SysRole.class), anyString())).thenReturn(testRole);

            // 执行测试
            Long roleId = roleService.createRole(testRole);

            // 验证结果
            assertNotNull(roleId);
            assertEquals(-1L, testRole.getTenantId()); // 超级管理员创建的角色应该设置为系统级角色
            verify(tableRoleService).insert(testRole, "superadmin");
        }
    }

    @Test
    void testCreateRole_NormalUser_ShouldSetTenantIdToCurrentUserTenant() {
        // 模拟普通用户登录
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(false);
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(2L);
            mockedSessionUtils.when(SessionUtils::getUsername).thenReturn("normaluser");

            when(tableRoleService.insert(any(SysRole.class), anyString())).thenReturn(testRole);

            // 执行测试
            Long roleId = roleService.createRole(testRole);

            // 验证结果
            assertNotNull(roleId);
            assertEquals(2L, testRole.getTenantId()); // 普通用户创建的角色应该设置为当前用户的租户ID
            verify(tableRoleService).insert(testRole, "normaluser");
        }
    }

    @Test
    void testUpdateRole_SuperAdmin_ShouldSetTenantIdToMinusOne() {
        // 创建一个没有设置tenantId的角色
        SysRole roleToUpdate = new SysRole();
        roleToUpdate.setId(1L);
        roleToUpdate.setRoleName("更新角色");
        roleToUpdate.setTenantId(null); // 未设置租户ID

        // 模拟超级管理员登录
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(true);
            mockedSessionUtils.when(SessionUtils::getUsername).thenReturn("superadmin");

            when(tableRoleService.updateSelectiveById(any(SysRole.class), anyString())).thenReturn(1);

            // 执行测试
            roleService.updateRole(roleToUpdate);

            // 验证结果
            assertEquals(-1L, roleToUpdate.getTenantId()); // 超级管理员修改的角色应该设置为系统级角色
            verify(tableRoleService).updateSelectiveById(roleToUpdate, "superadmin");
        }
    }

    @Test
    void testUpdateRole_NormalUser_ShouldSetTenantIdToCurrentUserTenant() {
        // 创建一个没有设置tenantId的角色
        SysRole roleToUpdate = new SysRole();
        roleToUpdate.setId(1L);
        roleToUpdate.setRoleName("更新角色");
        roleToUpdate.setTenantId(null); // 未设置租户ID

        // 模拟普通用户登录
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(false);
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(2L);
            mockedSessionUtils.when(SessionUtils::getUsername).thenReturn("normaluser");

            when(tableRoleService.updateSelectiveById(any(SysRole.class), anyString())).thenReturn(1);

            // 执行测试
            roleService.updateRole(roleToUpdate);

            // 验证结果
            assertEquals(2L, roleToUpdate.getTenantId()); // 普通用户修改的角色应该设置为当前用户的租户ID
            verify(tableRoleService).updateSelectiveById(roleToUpdate, "normaluser");
        }
    }

    @Test
    void testUpdateRole_WithExistingTenantId_ShouldNotChangeTenantId() {
        // 创建一个已经设置tenantId的角色
        SysRole roleToUpdate = new SysRole();
        roleToUpdate.setId(1L);
        roleToUpdate.setRoleName("更新角色");
        roleToUpdate.setTenantId(3L); // 已设置租户ID

        // 模拟超级管理员登录
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(true);
            mockedSessionUtils.when(SessionUtils::getUsername).thenReturn("superadmin");

            when(tableRoleService.updateSelectiveById(any(SysRole.class), anyString())).thenReturn(1);

            // 执行测试
            roleService.updateRole(roleToUpdate);

            // 验证结果
            assertEquals(3L, roleToUpdate.getTenantId()); // 已设置的租户ID不应该被修改
            verify(tableRoleService).updateSelectiveById(roleToUpdate, "superadmin");
        }
    }

    @Test
    void testFindAll_CallsRepositoryMethod() {
        // 模拟Repository返回数据
        List<SysRole> expectedRoles = Arrays.asList(testRole);
        when(tableRoleService.findAll()).thenReturn(expectedRoles);

        // 执行测试
        List<SysRole> actualRoles = roleService.findAll();

        // 验证结果
        assertEquals(expectedRoles, actualRoles);
        verify(tableRoleService).findAll();
    }

    @Test
    void testFindByRoleNameLike_CallsRepositoryMethod() {
        // 模拟Repository返回数据
        List<SysRole> expectedRoles = Arrays.asList(testRole);
        when(tableRoleService.findByCondition("测试", null)).thenReturn(expectedRoles);

        // 执行测试
        List<SysRole> actualRoles = roleService.findByRoleNameLike("测试");

        // 验证结果
        assertEquals(expectedRoles, actualRoles);
        verify(tableRoleService).findByCondition("测试", null);
    }

    @Test
    void testRepositoryQueryLogic_UsesInStatement() {
        // 这个测试验证Repository层使用IN语句而不是OR条件
        // 注意：这是一个概念性测试，实际的SQL验证需要集成测试

        // 模拟普通用户登录
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::isSuperAdmin).thenReturn(false);
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(2L);

            // 验证Repository方法被调用
            // 实际的IN语句逻辑在Repository层实现
            roleService.findAll();

            // 验证Repository方法被调用
            verify(tableRoleService).findAll();
        }
    }
}
