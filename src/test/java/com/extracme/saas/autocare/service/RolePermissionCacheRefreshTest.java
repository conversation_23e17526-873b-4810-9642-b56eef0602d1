package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ActiveProfiles;

import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.util.UserPermissionCacheUtils;

/**
 * 角色权限缓存刷新功能测试
 * 
 * 这个测试类专门测试角色更新、禁用、删除等操作时是否正确刷新了相关用户的权限缓存
 */
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("角色权限缓存刷新功能测试")
class RolePermissionCacheRefreshTest {

    @SpyBean
    private RoleService roleService;

    @MockBean
    private UserPermissionCacheUtils userPermissionCacheUtils;

    @MockBean
    private TableUserRoleService tableUserRoleService;

    private SysRole testRole;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testRole = new SysRole();
        testRole.setId(1L);
        testRole.setRoleName("测试角色");
        testRole.setRoleCode("TEST_ROLE");
        testRole.setRoleType(3);
        testRole.setDescription("测试角色描述");
        testRole.setStatus(1);
        testRole.setTenantId(100L);

        // Mock用户角色关联数据
        List<Long> userIds = Arrays.asList(1L, 2L, 3L);
        when(tableUserRoleService.findUserIdsByRoleIdAndTenantId(1L, 100L)).thenReturn(userIds);
        
        // Mock权限缓存刷新成功
        when(userPermissionCacheUtils.refreshUserPermissions(anyLong(), anyLong())).thenReturn(true);
        when(userPermissionCacheUtils.refreshUserPermissionsByRole(anyLong(), anyLong())).thenReturn(3);
    }

    @Test
    @DisplayName("角色状态更新时应该刷新权限缓存")
    void testUpdateRoleStatus_ShouldRefreshPermissionCache() {
        // 执行角色禁用操作
        roleService.disableRole(testRole.getId());

        // 验证权限缓存刷新被调用
        verify(userPermissionCacheUtils, times(1))
            .refreshUserPermissionsByRole(any(), any());
    }

    @Test
    @DisplayName("角色权限分配时应该刷新权限缓存")
    void testAssignPermissions_ShouldRefreshPermissionCache() {
        // 准备权限ID列表
        List<Long> permissionIds = Arrays.asList(1L, 2L, 3L);

        // 执行权限分配操作
        roleService.assignPermissions(testRole.getId(), permissionIds);

        // 验证权限缓存刷新被调用
        verify(userPermissionCacheUtils, times(1))
            .refreshUserPermissionsByRole(any(), any());
    }

    @Test
    @DisplayName("UserPermissionCacheUtils应该正确查询角色关联的用户")
    void testUserPermissionCacheUtils_ShouldQueryCorrectUsers() {
        // 执行权限缓存刷新
        int refreshCount = userPermissionCacheUtils.refreshUserPermissionsByRole(100L, 1L);

        // 验证返回的刷新用户数量
        assertEquals(3, refreshCount);

        // 验证查询用户ID的方法被调用
        verify(tableUserRoleService, times(1))
            .findUserIdsByRoleIdAndTenantId(1L, 100L);

        // 验证每个用户的权限缓存都被刷新
        verify(userPermissionCacheUtils, times(3))
            .refreshUserPermissions(anyLong(), anyLong());
    }

    @Test
    @DisplayName("TableUserRoleService应该支持根据角色ID和租户ID查询用户")
    void testTableUserRoleService_ShouldSupportTenantFiltering() {
        // 执行查询
        List<Long> userIds = tableUserRoleService.findUserIdsByRoleIdAndTenantId(1L, 100L);

        // 验证结果
        assertNotNull(userIds);
        assertEquals(3, userIds.size());
        assertEquals(Arrays.asList(1L, 2L, 3L), userIds);
    }

    @Test
    @DisplayName("角色删除时应该刷新权限缓存")
    void testDeleteRole_ShouldRefreshPermissionCache() {
        // 执行角色删除操作
        roleService.deleteRole(testRole.getId());

        // 验证权限缓存刷新被调用
        verify(userPermissionCacheUtils, times(1))
            .refreshUserPermissionsByRole(any(), any());
    }
}
