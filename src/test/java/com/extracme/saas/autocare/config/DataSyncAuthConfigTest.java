package com.extracme.saas.autocare.config;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * 数据同步接口认证配置测试
 * 验证数据同步接口是否允许匿名访问
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
class DataSyncAuthConfigTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Test
    void testDataSyncEndpointsAllowAnonymousAccess() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试获取支持的同步表列表接口 - 应该允许匿名访问
        mockMvc.perform(get("/api/v1/data-sync/supported-tables"))
                .andExpect(status().isOk());

        // 测试查询同步状态接口 - 应该允许匿名访问
        mockMvc.perform(get("/api/v1/data-sync/status/TEST_BATCH_NO"))
                .andExpect(status().isOk());
    }

    @Test
    void testOtherEndpointsRequireAuth() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 测试其他需要认证的接口 - 应该返回401未授权
        // 注意：这里只是测试拦截器配置，实际的业务逻辑可能会有其他验证
        mockMvc.perform(get("/api/v1/users"))
                .andExpect(status().isUnauthorized());
    }
}
