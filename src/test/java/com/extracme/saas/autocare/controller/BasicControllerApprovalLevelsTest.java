package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.extracme.saas.autocare.model.vo.ApprovalLevelsVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.service.ApprovalLevelsService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * BasicController 审批层级相关功能测试类
 */
@ExtendWith(MockitoExtension.class)
class BasicControllerApprovalLevelsTest {

    @Mock
    private ApprovalLevelsService approvalLevelsService;

    @InjectMocks
    private BasicController basicController;

    @Test
    void testGetApprovalLevelsList_WithBasePageDTO() {
        // 准备测试数据
        BasePageDTO pageDTO = new BasePageDTO();
        pageDTO.setPageNum(1);
        pageDTO.setPageSize(10);

        // 模拟审批层级数据
        ApprovalLevelsVO vo1 = new ApprovalLevelsVO();
        vo1.setId(1L);
        vo1.setApprovalLevel(1);
        vo1.setApprovalLevelName("一级审批");
        vo1.setSelfApprovalAmount(new BigDecimal("10000.00"));

        ApprovalLevelsVO vo2 = new ApprovalLevelsVO();
        vo2.setId(2L);
        vo2.setApprovalLevel(2);
        vo2.setApprovalLevelName("二级审批");
        vo2.setSelfApprovalAmount(new BigDecimal("50000.00"));

        List<ApprovalLevelsVO> voList = Arrays.asList(vo1, vo2);

        // 模拟分页结果
        BasePageVO<ApprovalLevelsVO> mockPageVO = new BasePageVO<>();
        mockPageVO.setList(voList);
        mockPageVO.setTotal(2L);
        mockPageVO.setPageNum(1);
        mockPageVO.setPageSize(10);
        mockPageVO.setPages(1);

        // 模拟服务层返回
        when(approvalLevelsService.getApprovalLevelsList(pageDTO)).thenReturn(mockPageVO);

        // 执行测试
        Result<BasePageVO<ApprovalLevelsVO>> result = basicController.getApprovalLevelsList(pageDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().getList().size());
        assertEquals(2L, result.getData().getTotal());

        // 验证返回的数据内容
        ApprovalLevelsVO firstItem = result.getData().getList().get(0);
        assertEquals(1L, firstItem.getId());
        assertEquals((byte) 1, firstItem.getApprovalLevel());
        assertEquals("一级审批", firstItem.getApprovalLevelName());
        assertEquals(new BigDecimal("10000.00"), firstItem.getSelfApprovalAmount());

        ApprovalLevelsVO secondItem = result.getData().getList().get(1);
        assertEquals(2L, secondItem.getId());
        assertEquals((byte) 2, secondItem.getApprovalLevel());
        assertEquals("二级审批", secondItem.getApprovalLevelName());
        assertEquals(new BigDecimal("50000.00"), secondItem.getSelfApprovalAmount());
    }

    @Test
    void testGetApprovalLevelsList_VerifyNoQueryConditions() {
        // 准备测试数据 - 只包含分页参数
        BasePageDTO pageDTO = new BasePageDTO();
        pageDTO.setPageNum(1);
        pageDTO.setPageSize(5);

        // 模拟空的分页结果
        BasePageVO<ApprovalLevelsVO> mockPageVO = new BasePageVO<>();
        mockPageVO.setList(Arrays.asList());
        mockPageVO.setTotal(0L);
        mockPageVO.setPageNum(1);
        mockPageVO.setPageSize(5);
        mockPageVO.setPages(0);

        // 模拟服务层返回
        when(approvalLevelsService.getApprovalLevelsList(pageDTO)).thenReturn(mockPageVO);

        // 执行测试
        Result<BasePageVO<ApprovalLevelsVO>> result = basicController.getApprovalLevelsList(pageDTO);

        // 验证结果 - 确保接口能正常处理只有分页参数的请求
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        assertNotNull(result.getData());
        assertEquals(0, result.getData().getList().size());
        assertEquals(0L, result.getData().getTotal());
    }
}