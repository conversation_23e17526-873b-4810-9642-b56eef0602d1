package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.dto.MultipartUploadCompleteResult;
import com.extracme.saas.autocare.model.dto.MultipartUploadInitResult;
import com.extracme.saas.autocare.model.dto.PartETagInfo;
import com.extracme.saas.autocare.model.dto.ResumableUploadInitDTO;
import com.extracme.saas.autocare.service.OssService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * BasicController分片上传接口测试类
 */
@WebMvcTest(BasicController.class)
@DisplayName("BasicController分片上传接口测试")
class BasicControllerMultipartTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OssService ossService;

    @Autowired
    private ObjectMapper objectMapper;

    private MultipartUploadInitResult mockInitResult;
    private MultipartUploadCompleteResult mockCompleteResult;

    @BeforeEach
    void setUp() {
        // 设置模拟初始化结果
        mockInitResult = new MultipartUploadInitResult();
        mockInitResult.setUploadId("test-upload-id-123");
        mockInitResult.setRelativePath("tenant_1/uploads/2024-06-04/test.txt");
        mockInitResult.setBucketName("test-bucket");

        // 设置模拟完成结果
        mockCompleteResult = new MultipartUploadCompleteResult();
        mockCompleteResult.setRelativePath("tenant_1/uploads/2024-06-04/test.txt");
        mockCompleteResult.setFullUrl("https://test-bucket.oss-cn-shanghai.aliyuncs.com/tenant_1/uploads/2024-06-04/test.txt");
        mockCompleteResult.setBucketName("test-bucket");
        mockCompleteResult.setETag("test-etag-123");
        mockCompleteResult.setTotalSize(2048L);
        mockCompleteResult.setTotalParts(2);
        mockCompleteResult.setTenantCode("tenant_1");
    }

    @Test
    @DisplayName("测试初始化断点续传上传接口")
    void testInitializeResumableUpload() throws Exception {
        ResumableUploadInitDTO initDTO = new ResumableUploadInitDTO();
        initDTO.setOriginalFileName("test.txt");
        initDTO.setFileSize(1024L);
        initDTO.setContentType("text/plain");
        initDTO.setCategory("documents");

        when(ossService.initializeResumableUpload(any(ResumableUploadInitDTO.class)))
            .thenReturn(mockInitResult);

        mockMvc.perform(post("/api/v1/basic/upload/resumable/init")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(initDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.uploadId").value("test-upload-id-123"))
                .andExpect(jsonPath("$.data.relativePath").value("tenant_1/uploads/2024-06-04/test.txt"))
                .andExpect(jsonPath("$.data.bucketName").value("test-bucket"));
    }

    @Test
    @DisplayName("测试生成分片上传预签名URL接口")
    void testGeneratePresignedPartUploadUrl() throws Exception {
        String uploadId = "test-upload-id-123";
        int partNumber = 1;
        long expiration = 3600;
        String presignedUrl = "https://test-bucket.oss-cn-shanghai.aliyuncs.com/tenant_1/uploads/2024-06-04/test.txt?partNumber=1&uploadId=test-upload-id-123";

        when(ossService.generatePresignedPartUploadUrl(uploadId, partNumber, expiration))
            .thenReturn(presignedUrl);

        mockMvc.perform(get("/api/v1/basic/upload/resumable/presigned-url/{uploadId}/{partNumber}", uploadId, partNumber)
                .param("expiration", String.valueOf(expiration))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(presignedUrl));
    }

    @Test
    @DisplayName("测试生成分片上传预签名URL接口使用默认过期时间")
    void testGeneratePresignedPartUploadUrlWithDefaultExpiration() throws Exception {
        String uploadId = "test-upload-id-123";
        int partNumber = 1;
        long defaultExpiration = 3600;
        String presignedUrl = "https://test-bucket.oss-cn-shanghai.aliyuncs.com/tenant_1/uploads/2024-06-04/test.txt?partNumber=1&uploadId=test-upload-id-123";
        
        when(ossService.generatePresignedPartUploadUrl(uploadId, partNumber, defaultExpiration))
            .thenReturn(presignedUrl);

        mockMvc.perform(get("/api/v1/basic/upload/resumable/presigned-url/{uploadId}/{partNumber}", uploadId, partNumber)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(presignedUrl));
    }

    @Test
    @DisplayName("测试完成分片上传接口")
    void testCompleteMultipartUpload() throws Exception {
        String uploadId = "test-upload-id-123";

        List<PartETagInfo> partETags = Arrays.asList(
            new PartETagInfo(1, "etag1", 1024L),
            new PartETagInfo(2, "etag2", 1024L)
        );

        when(ossService.completeMultipartUpload(uploadId, partETags))
            .thenReturn(mockCompleteResult);

        mockMvc.perform(post("/api/v1/basic/upload/resumable/complete/{uploadId}", uploadId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(partETags)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.relativePath").value("tenant_1/uploads/2024-06-04/test.txt"))
                .andExpect(jsonPath("$.data.fullUrl").value(mockCompleteResult.getFullUrl()))
                .andExpect(jsonPath("$.data.eTag").value("test-etag-123"))
                .andExpect(jsonPath("$.data.totalSize").value(2048))
                .andExpect(jsonPath("$.data.totalParts").value(2));
    }

    @Test
    @DisplayName("测试取消分片上传接口")
    void testAbortMultipartUpload() throws Exception {
        String uploadId = "test-upload-id-123";

        mockMvc.perform(delete("/api/v1/basic/upload/resumable/abort/{uploadId}", uploadId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @DisplayName("测试初始化分片上传接口参数验证")
    void testInitializeMultipartUploadWithInvalidPath() throws Exception {
        String invalidPath = "";

        mockMvc.perform(get("/api/v1/basic/upload/multipart/init/{relativePath}", invalidPath)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound()); // 空路径会导致404
    }

    @Test
    @DisplayName("测试生成分片上传预签名URL接口参数验证")
    void testGeneratePresignedPartUploadUrlWithMissingParams() throws Exception {
        String relativePath = "tenant_1/uploads/2024-06-04/test.txt";

        // 缺少uploadId参数
        mockMvc.perform(get("/api/v1/basic/upload/multipart/presigned-url/{relativePath}", relativePath)
                .param("partNumber", "1")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        // 缺少partNumber参数
        mockMvc.perform(get("/api/v1/basic/upload/multipart/presigned-url/{relativePath}", relativePath)
                .param("uploadId", "test-upload-id-123")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试完成分片上传接口参数验证")
    void testCompleteMultipartUploadWithMissingParams() throws Exception {
        String relativePath = "tenant_1/uploads/2024-06-04/test.txt";
        
        List<PartETagInfo> partETags = Arrays.asList(
            new PartETagInfo(1, "etag1", 1024L)
        );

        // 缺少uploadId参数
        mockMvc.perform(post("/api/v1/basic/upload/multipart/complete/{relativePath}", relativePath)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(partETags)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试取消分片上传接口参数验证")
    void testAbortMultipartUploadWithMissingParams() throws Exception {
        String relativePath = "tenant_1/uploads/2024-06-04/test.txt";

        // 缺少uploadId参数
        mockMvc.perform(delete("/api/v1/basic/upload/multipart/abort/{relativePath}", relativePath)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
}
