package com.extracme.saas.autocare.controller;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.service.AuthService;

/**
 * 认证控制器测试类
 */
public class AuthControllerTest {

    private MockMvc mockMvc;
    
    @Mock
    private AuthService authService;
    
    @InjectMocks
    private AuthController authController;
    
    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(authController).build();
    }
    
    /**
     * 测试发送登录验证码 - 成功场景
     */
    @Test
    public void testSendCode_Success() throws Exception {
        // 准备测试数据
        String requestBody = "{\"mobile\":\"***********\"}";
        
        // 模拟发送验证码成功
        when(authService.sendLoginCode(eq("***********"), anyString())).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(post("/api/v1/auth/code/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));
        
        // 验证调用
        verify(authService, times(1)).sendLoginCode(eq("***********"), anyString());
    }
    
    /**
     * 测试发送登录验证码 - 发送失败
     */
    @Test
    public void testSendCode_Fail() throws Exception {
        // 准备测试数据
        String requestBody = "{\"mobile\":\"***********\"}";
        
        // 模拟发送验证码失败
        when(authService.sendLoginCode(eq("***********"), anyString())).thenReturn(false);
        
        // 执行测试
        mockMvc.perform(post("/api/v1/auth/code/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("验证码发送失败"));
        
        // 验证调用
        verify(authService, times(1)).sendLoginCode(eq("***********"), anyString());
    }
    
    /**
     * 测试发送登录验证码 - 非平台账号异常
     */
    @Test
    public void testSendCode_Exception() throws Exception {
        // 准备测试数据
        String requestBody = "{\"mobile\":\"***********\"}";

        // 模拟发送验证码异常 - 非平台账号
        when(authService.sendLoginCode(eq("***********"), anyString()))
            .thenThrow(new BusinessException("非平台账号！"));

        // 执行测试
        mockMvc.perform(post("/api/v1/auth/code/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("非平台账号！"));

        // 验证调用
        verify(authService, times(1)).sendLoginCode(eq("***********"), anyString());
    }
    
    /**
     * 测试短信验证码登录 - 成功场景
     */
    @Test
    public void testLoginByMobile_Success() throws Exception {
        // 准备测试数据
        String requestBody = "{\"mobile\":\"***********\",\"code\":\"123456\"}";
        
        // 模拟登录成功
        TokenDTO tokenDTO = new TokenDTO();
        tokenDTO.setAccessToken("test-token");
        tokenDTO.setUserId(1L);
        tokenDTO.setUsername("testuser");
        tokenDTO.setMobile("***********");
        
        when(authService.loginByMobile(eq("***********"), eq("123456"))).thenReturn(tokenDTO);
        
        // 执行测试
        mockMvc.perform(post("/api/v1/auth/login/mobile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.accessToken").value("test-token"))
                .andExpect(jsonPath("$.data.userId").value(1))
                .andExpect(jsonPath("$.data.username").value("testuser"))
                .andExpect(jsonPath("$.data.mobile").value("***********"));
        
        // 验证调用
        verify(authService, times(1)).loginByMobile(eq("***********"), eq("123456"));
    }
    
    /**
     * 测试短信验证码登录 - 登录失败
     */
    @Test
    public void testLoginByMobile_Fail() throws Exception {
        // 准备测试数据
        String requestBody = "{\"mobile\":\"***********\",\"code\":\"123456\"}";
        
        // 模拟登录失败
        when(authService.loginByMobile(eq("***********"), eq("123456"))).thenReturn(null);
        
        // 执行测试
        mockMvc.perform(post("/api/v1/auth/login/mobile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("登录失败，请检查手机号和验证码"));
        
        // 验证调用
        verify(authService, times(1)).loginByMobile(eq("***********"), eq("123456"));
    }
    
    /**
     * 测试退出登录 - 成功场景
     */
    @Test
    public void testLogout_Success() throws Exception {
        // 准备测试数据
        String token = "Bearer test-token";
        
        // 模拟退出成功
        when(authService.logout(eq("test-token"))).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(post("/api/v1/auth/logout")
                .header("Authorization", token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));
        
        // 验证调用
        verify(authService, times(1)).logout(eq("test-token"));
    }
    
    /**
     * 测试退出登录 - 失败场景
     */
    @Test
    public void testLogout_Fail() throws Exception {
        // 准备测试数据
        String token = "Bearer test-token";
        
        // 模拟退出失败
        when(authService.logout(eq("test-token"))).thenReturn(false);
        
        // 执行测试
        mockMvc.perform(post("/api/v1/auth/logout")
                .header("Authorization", token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("退出失败"));
        
        // 验证调用
        verify(authService, times(1)).logout(eq("test-token"));
    }
} 