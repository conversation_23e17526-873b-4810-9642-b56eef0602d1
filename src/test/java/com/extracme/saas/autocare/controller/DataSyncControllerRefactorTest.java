package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.dto.VehicleInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.VehicleModelSyncRequestDTO;
import com.extracme.saas.autocare.model.dto.OrgInfoSyncRequestDTO;
import com.extracme.saas.autocare.model.vo.DataSyncResultVO;
import com.extracme.saas.autocare.service.DataSyncService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * DataSyncController重构后的测试
 */
@WebMvcTest(DataSyncController.class)
public class DataSyncControllerRefactorTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private DataSyncService dataSyncService;

    @Test
    public void testSyncVehicleInfoBatch() throws Exception {
        // 准备测试数据
        VehicleInfoSyncRequestDTO requestDTO = new VehicleInfoSyncRequestDTO();
        requestDTO.setSyncKey("SYNC_KEY_TEST_001");
        VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO dataItem =
            new VehicleInfoSyncRequestDTO.VehicleInfoSyncDataDTO();
        dataItem.setId(1L);
        dataItem.setVin("LSGGG54X8EH123456");
        dataItem.setVehicleNo("沪A12345");
        dataItem.setVehicleModelId(1L);
        requestDTO.setBatchData(Arrays.asList(dataItem));

        // 模拟服务返回结果
        DataSyncResultVO mockResult = createMockResult("SYNC_VEHICLE_001", "SUCCESS");
        when(dataSyncService.syncVehicleInfoBatch(any(), anyString())).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(post("/api/v1/data-sync/vehicle-info/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.batchNo").value("SYNC_VEHICLE_001"))
                .andExpect(jsonPath("$.data.syncStatus").value("SUCCESS"));
    }

    @Test
    public void testSyncVehicleModelBatch() throws Exception {
        // 准备测试数据
        VehicleModelSyncRequestDTO requestDTO = new VehicleModelSyncRequestDTO();
        requestDTO.setSyncKey("SYNC_KEY_TEST_001");
        VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO dataItem =
            new VehicleModelSyncRequestDTO.VehicleModelSyncDataDTO();
        dataItem.setId(1L);
        dataItem.setVehicleModelName("丰田卡罗拉");
        requestDTO.setBatchData(Arrays.asList(dataItem));

        // 模拟服务返回结果
        DataSyncResultVO mockResult = createMockResult("SYNC_MODEL_001", "SUCCESS");
        when(dataSyncService.syncVehicleModelBatch(any(), anyString())).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(post("/api/v1/data-sync/vehicle-model/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.batchNo").value("SYNC_MODEL_001"))
                .andExpect(jsonPath("$.data.syncStatus").value("SUCCESS"));
    }

    @Test
    public void testSyncOrgInfoBatch() throws Exception {
        // 准备测试数据
        OrgInfoSyncRequestDTO requestDTO = new OrgInfoSyncRequestDTO();
        requestDTO.setSyncKey("SYNC_KEY_TEST_001");
        OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO dataItem =
            new OrgInfoSyncRequestDTO.OrgInfoSyncDataDTO();
        dataItem.setOrgId("ORG001");
        dataItem.setOrgName("上海分公司");
        requestDTO.setBatchData(Arrays.asList(dataItem));

        // 模拟服务返回结果
        DataSyncResultVO mockResult = createMockResult("SYNC_ORG_001", "SUCCESS");
        when(dataSyncService.syncOrgInfoBatch(any(), anyString())).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(post("/api/v1/data-sync/org-info/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.batchNo").value("SYNC_ORG_001"))
                .andExpect(jsonPath("$.data.syncStatus").value("SUCCESS"));
    }

    /**
     * 创建模拟的同步结果
     */
    private DataSyncResultVO createMockResult(String batchNo, String status) {
        DataSyncResultVO result = new DataSyncResultVO();
        result.setBatchNo(batchNo);
        result.setSyncStatus(status);
        result.setTotalCount(1);
        result.setSuccessCount(1);
        result.setFailedCount(0);
        result.setSyncStartTime(new Date());
        result.setSyncEndTime(new Date());
        result.setSyncDuration(100L);
        return result;
    }
}
