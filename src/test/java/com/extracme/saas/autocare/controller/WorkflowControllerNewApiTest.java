package com.extracme.saas.autocare.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateCopyTransitionsDTO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.WorkflowService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 工作流控制器新增API接口测试
 * 测试工作流模板下拉框查询和复制模板节点转换规则接口
 *
 * <AUTHOR>
 * @date 2024/06/18
 */
@WebMvcTest(WorkflowController.class)
public class WorkflowControllerNewApiTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WorkflowService workflowService;

    @Autowired
    private ObjectMapper objectMapper;

    private List<ComboVO<Long>> templateComboList;
    private WorkflowTemplateCopyTransitionsDTO copyDTO;

    @BeforeEach
    void setUp() {
        // 准备工作流模板下拉框测试数据
        templateComboList = new ArrayList<>();
        
        ComboVO<Long> combo1 = new ComboVO<>();
        combo1.setId(1L);
        combo1.setValue("车辆交接流程模板");
        templateComboList.add(combo1);
        
        ComboVO<Long> combo2 = new ComboVO<>();
        combo2.setId(2L);
        combo2.setValue("质量检测流程模板");
        templateComboList.add(combo2);

        // 准备复制转换规则测试数据
        copyDTO = new WorkflowTemplateCopyTransitionsDTO();
        copyDTO.setSourceTemplateId(1L);
        copyDTO.setTargetTemplateId(2L);
    }

    @Test
    void testGetTemplateCombo() throws Exception {
        // Mock service方法
        when(workflowService.getTemplateCombo()).thenReturn(templateComboList);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/v1/workflow/templates/combo"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].value").value("车辆交接流程模板"))
                .andExpect(jsonPath("$.data[1].id").value(2))
                .andExpect(jsonPath("$.data[1].value").value("质量检测流程模板"));
    }

    @Test
    void testGetTemplateCombo_EmptyResult() throws Exception {
        // Mock service方法返回空列表
        when(workflowService.getTemplateCombo()).thenReturn(new ArrayList<>());

        // 执行请求并验证结果
        mockMvc.perform(get("/api/v1/workflow/templates/combo"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }

    @Test
    void testCopyTemplateTransitions() throws Exception {
        // Mock service方法
        String expectedResult = "复制成功：共复制了3个活动节点转换规则和5个状态转换规则";
        when(workflowService.copyTemplateTransitions(any(WorkflowTemplateCopyTransitionsDTO.class)))
                .thenReturn(expectedResult);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/v1/workflow/templates/copy-transitions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(copyDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(expectedResult));
    }

    @Test
    void testCopyTemplateTransitions_ValidationError_SourceTemplateIdNull() throws Exception {
        // 准备无效的请求数据（源模板ID为空）
        WorkflowTemplateCopyTransitionsDTO invalidCopyDTO = new WorkflowTemplateCopyTransitionsDTO();
        invalidCopyDTO.setSourceTemplateId(null);
        invalidCopyDTO.setTargetTemplateId(2L);

        // 执行请求并验证参数验证错误
        mockMvc.perform(post("/api/v1/workflow/templates/copy-transitions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidCopyDTO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testCopyTemplateTransitions_ValidationError_TargetTemplateIdNull() throws Exception {
        // 准备无效的请求数据（目标模板ID为空）
        WorkflowTemplateCopyTransitionsDTO invalidCopyDTO = new WorkflowTemplateCopyTransitionsDTO();
        invalidCopyDTO.setSourceTemplateId(1L);
        invalidCopyDTO.setTargetTemplateId(null);

        // 执行请求并验证参数验证错误
        mockMvc.perform(post("/api/v1/workflow/templates/copy-transitions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidCopyDTO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testCopyTemplateTransitions_NoSourceRules() throws Exception {
        // Mock service方法返回无规则可复制的消息
        String expectedResult = "源模板没有活动节点转换规则，无需复制";
        when(workflowService.copyTemplateTransitions(any(WorkflowTemplateCopyTransitionsDTO.class)))
                .thenReturn(expectedResult);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/v1/workflow/templates/copy-transitions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(copyDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(expectedResult));
    }
}
