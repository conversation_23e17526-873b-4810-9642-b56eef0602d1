package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.TenantService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * TenantController 测试类
 */
@ExtendWith(MockitoExtension.class)
class TenantControllerTest {

    @Mock
    private TenantService tenantService;

    @InjectMocks
    private TenantController tenantController;

    @Test
    void testGetTenantCombo_ReturnsLongType() {
        // 准备测试数据 - 使用Long类型的ComboVO
        ComboVO<Long> combo1 = new ComboVO<>();
        combo1.setId(1L);
        combo1.setValue("租户1");

        ComboVO<Long> combo2 = new ComboVO<>();
        combo2.setId(2L);
        combo2.setValue("租户2");

        List<ComboVO<Long>> mockResult = Arrays.asList(combo1, combo2);

        // 模拟服务层返回
        when(tenantService.getTenantCombo()).thenReturn(mockResult);

        // 执行测试
        Result<List<ComboVO<Long>>> result = tenantController.getTenantCombo();

        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        // 验证返回的ID是Long类型
        ComboVO<Long> firstItem = result.getData().get(0);
        assertEquals(Long.class, firstItem.getId().getClass());
        assertEquals(1L, firstItem.getId());
        assertEquals("租户1", firstItem.getValue());

        ComboVO<Long> secondItem = result.getData().get(1);
        assertEquals(Long.class, secondItem.getId().getClass());
        assertEquals(2L, secondItem.getId());
        assertEquals("租户2", secondItem.getValue());
    }
} 