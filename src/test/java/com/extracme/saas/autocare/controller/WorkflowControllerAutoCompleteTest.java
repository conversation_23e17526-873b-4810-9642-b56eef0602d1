package com.extracme.saas.autocare.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionDTO;

/**
 * WorkflowController自动补全功能单元测试
 * 
 * <AUTHOR>
 * @date 2024/06/10
 */
public class WorkflowControllerAutoCompleteTest {

    private WorkflowController workflowController;

    @BeforeEach
    void setUp() {
        workflowController = new WorkflowController();
    }

    @Test
    void testAutoCompleteHandlerClassNames_SimpleClassName() throws Exception {
        // 准备测试数据 - 使用简单的类名
        ActivityTransitionDTO transitionDTO = new ActivityTransitionDTO();
        transitionDTO.setConditionHandler("SimpleConditionHandler");
        transitionDTO.setHandlerClass("SimpleTransitionHandler");

        // 使用反射调用私有方法
        java.lang.reflect.Method method = WorkflowController.class.getDeclaredMethod(
                "autoCompleteHandlerClassNames", ActivityTransitionDTO.class);
        method.setAccessible(true);
        method.invoke(workflowController, transitionDTO);

        // 验证结果
        assertEquals("com.extracme.saas.autocare.workflow.handler.SimpleConditionHandler", 
                transitionDTO.getConditionHandler());
        assertEquals("com.extracme.saas.autocare.workflow.handler.SimpleTransitionHandler", 
                transitionDTO.getHandlerClass());
    }

    @Test
    void testAutoCompleteHandlerClassNames_FullClassName() throws Exception {
        // 准备测试数据 - 使用完整的类名
        ActivityTransitionDTO transitionDTO = new ActivityTransitionDTO();
        transitionDTO.setConditionHandler("com.extracme.saas.autocare.workflow.handler.FullConditionHandler");
        transitionDTO.setHandlerClass("com.extracme.saas.autocare.workflow.handler.FullTransitionHandler");

        // 使用反射调用私有方法
        java.lang.reflect.Method method = WorkflowController.class.getDeclaredMethod(
                "autoCompleteHandlerClassNames", ActivityTransitionDTO.class);
        method.setAccessible(true);
        method.invoke(workflowController, transitionDTO);

        // 验证结果 - 应该保持不变
        assertEquals("com.extracme.saas.autocare.workflow.handler.FullConditionHandler", 
                transitionDTO.getConditionHandler());
        assertEquals("com.extracme.saas.autocare.workflow.handler.FullTransitionHandler", 
                transitionDTO.getHandlerClass());
    }

    @Test
    void testAutoCompleteHandlerClassNames_NullAndEmptyValues() throws Exception {
        // 准备测试数据 - null和空值
        ActivityTransitionDTO transitionDTO = new ActivityTransitionDTO();
        transitionDTO.setConditionHandler(null);
        transitionDTO.setHandlerClass("   "); // 空白字符串

        // 使用反射调用私有方法
        java.lang.reflect.Method method = WorkflowController.class.getDeclaredMethod(
                "autoCompleteHandlerClassNames", ActivityTransitionDTO.class);
        method.setAccessible(true);
        method.invoke(workflowController, transitionDTO);

        // 验证结果 - 应该保持不变
        assertNull(transitionDTO.getConditionHandler());
        assertEquals("   ", transitionDTO.getHandlerClass());
    }

    @Test
    void testAutoCompleteHandlerClassNames_EmptyString() throws Exception {
        // 准备测试数据 - 空字符串
        ActivityTransitionDTO transitionDTO = new ActivityTransitionDTO();
        transitionDTO.setConditionHandler("");
        transitionDTO.setHandlerClass("");

        // 使用反射调用私有方法
        java.lang.reflect.Method method = WorkflowController.class.getDeclaredMethod(
                "autoCompleteHandlerClassNames", ActivityTransitionDTO.class);
        method.setAccessible(true);
        method.invoke(workflowController, transitionDTO);

        // 验证结果 - 空字符串应该保持不变
        assertEquals("", transitionDTO.getConditionHandler());
        assertEquals("", transitionDTO.getHandlerClass());
    }

    @Test
    void testAutoCompleteHandlerClassNames_NullDTO() throws Exception {
        // 测试传入null DTO的情况
        java.lang.reflect.Method method = WorkflowController.class.getDeclaredMethod(
                "autoCompleteHandlerClassNames", ActivityTransitionDTO.class);
        method.setAccessible(true);
        
        // 应该不抛出异常
        method.invoke(workflowController, (ActivityTransitionDTO) null);
    }

    @Test
    void testAutoCompleteHandlerClassNames_OtherPackagePrefix() throws Exception {
        // 准备测试数据 - 使用其他包前缀的类名
        ActivityTransitionDTO transitionDTO = new ActivityTransitionDTO();
        transitionDTO.setConditionHandler("com.other.package.OtherConditionHandler");
        transitionDTO.setHandlerClass("com.other.package.OtherTransitionHandler");

        // 使用反射调用私有方法
        java.lang.reflect.Method method = WorkflowController.class.getDeclaredMethod(
                "autoCompleteHandlerClassNames", ActivityTransitionDTO.class);
        method.setAccessible(true);
        method.invoke(workflowController, transitionDTO);

        // 验证结果 - 其他包的完整类名应该保持不变
        assertEquals("com.other.package.OtherConditionHandler", 
                transitionDTO.getConditionHandler());
        assertEquals("com.other.package.OtherTransitionHandler", 
                transitionDTO.getHandlerClass());
    }
}
