package com.extracme.saas.autocare.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.time.LocalDateTime;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;

import com.extracme.saas.autocare.model.vo.FileUploadResultVO;
import com.extracme.saas.autocare.service.ApprovalLevelsService;
import com.extracme.saas.autocare.service.DataDictService;
import com.extracme.saas.autocare.service.OperateLogService;
import com.extracme.saas.autocare.service.OperatorLogService;
import com.extracme.saas.autocare.service.OrgService;
import com.extracme.saas.autocare.service.OssService;
import com.extracme.saas.autocare.service.RegionService;

/**
 * BasicController 普通文件上传测试类
 */
@WebMvcTest(controllers = BasicController.class,
    excludeAutoConfiguration = {
        org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class,
        org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration.class
    })
@DisplayName("BasicController 普通文件上传测试")
class BasicControllerSimpleUploadTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OssService ossService;

    @MockBean
    private DataDictService dataDictService;

    @MockBean
    private OrgService orgService;

    @MockBean
    private ApprovalLevelsService approvalLevelsService;

    @MockBean
    private OperateLogService operateLogService;

    @MockBean
    private OperatorLogService operatorLogService;

    @MockBean
    private RegionService regionService;

    private FileUploadResultVO mockUploadResult;

    @BeforeEach
    void setUp() {
        // 设置模拟上传结果
        mockUploadResult = new FileUploadResultVO();
        mockUploadResult.setFileName("abc123.txt");
        mockUploadResult.setOriginalFileName("test.txt");
        mockUploadResult.setRelativePath("tenant_1/uploads/2024-06-05/documents/abc123.txt");
        mockUploadResult.setFullUrl("https://evcard.oss-cn-shanghai.aliyuncs.com/tenant_1/uploads/2024-06-05/documents/abc123.txt");
        mockUploadResult.setFileSize(1024L);
        mockUploadResult.setContentType("text/plain");
        mockUploadResult.setFileExtension("txt");
        mockUploadResult.setUploadTime(LocalDateTime.now());
        mockUploadResult.setTenantCode("tenant_1");
        mockUploadResult.setUploadUserId(123L);
        mockUploadResult.setUploadUserName("testuser");
        mockUploadResult.setIsResumable(false);
        mockUploadResult.setProgress(100);
    }

    @Test
    @DisplayName("测试普通文件上传成功")
    void testUploadSimpleFileSuccess() throws Exception {
        // 创建测试文件
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.txt",
            "text/plain",
            "Hello World".getBytes()
        );

        when(ossService.uploadSimpleFile(any(), eq("documents")))
            .thenReturn(mockUploadResult);

        mockMvc.perform(multipart("/api/v1/basic/upload/simple")
                .file(file)
                .param("category", "documents")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.fileName").value("abc123.txt"))
                .andExpect(jsonPath("$.data.originalFileName").value("test.txt"))
                .andExpect(jsonPath("$.data.relativePath").value("tenant_1/uploads/2024-06-05/documents/abc123.txt"))
                .andExpect(jsonPath("$.data.fullUrl").value("https://evcard.oss-cn-shanghai.aliyuncs.com/tenant_1/uploads/2024-06-05/documents/abc123.txt"))
                .andExpect(jsonPath("$.data.fileSize").value(1024))
                .andExpect(jsonPath("$.data.contentType").value("text/plain"))
                .andExpect(jsonPath("$.data.fileExtension").value("txt"))
                .andExpect(jsonPath("$.data.tenantCode").value("tenant_1"))
                .andExpect(jsonPath("$.data.uploadUserId").value(123))
                .andExpect(jsonPath("$.data.uploadUserName").value("testuser"))
                .andExpect(jsonPath("$.data.isResumable").value(false))
                .andExpect(jsonPath("$.data.progress").value(100));
    }

    @Test
    @DisplayName("测试普通文件上传不带分类")
    void testUploadSimpleFileWithoutCategory() throws Exception {
        // 创建测试文件
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.txt",
            "text/plain",
            "Hello World".getBytes()
        );

        when(ossService.uploadSimpleFile(any(), isNull()))
            .thenReturn(mockUploadResult);

        mockMvc.perform(multipart("/api/v1/basic/upload/simple")
                .file(file)
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.fileName").value("abc123.txt"));
    }

    @Test
    @DisplayName("测试普通文件上传失败 - 文件为空")
    void testUploadSimpleFileFailureEmptyFile() throws Exception {
        // 创建空文件
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.txt",
            "text/plain",
            new byte[0]
        );

        when(ossService.uploadSimpleFile(any(), any()))
            .thenThrow(new RuntimeException("上传文件不能为空"));

        mockMvc.perform(multipart("/api/v1/basic/upload/simple")
                .file(file)
                .param("category", "documents")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("文件上传失败: 上传文件不能为空"));
    }

    @Test
    @DisplayName("测试普通文件上传失败 - 文件过大")
    void testUploadSimpleFileFailureTooLarge() throws Exception {
        // 创建测试文件
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "large-file.txt",
            "text/plain",
            "Large file content".getBytes()
        );

        when(ossService.uploadSimpleFile(any(), any()))
            .thenThrow(new RuntimeException("文件大小不能超过10MB，大文件请使用断点续传接口"));

        mockMvc.perform(multipart("/api/v1/basic/upload/simple")
                .file(file)
                .param("category", "documents")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("文件上传失败: 文件大小不能超过10MB，大文件请使用断点续传接口"));
    }

    @Test
    @DisplayName("测试普通文件上传失败 - 不支持的文件类型")
    void testUploadSimpleFileFailureUnsupportedType() throws Exception {
        // 创建不支持的文件类型
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.exe",
            "application/x-msdownload",
            "Executable content".getBytes()
        );

        when(ossService.uploadSimpleFile(any(), any()))
            .thenThrow(new RuntimeException("不支持的文件类型: exe"));

        mockMvc.perform(multipart("/api/v1/basic/upload/simple")
                .file(file)
                .param("category", "documents")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("文件上传失败: 不支持的文件类型: exe"));
    }

    @Test
    @DisplayName("测试普通文件上传 - 图片文件")
    void testUploadSimpleImageFile() throws Exception {
        // 创建图片文件
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "JPEG image content".getBytes()
        );

        // 更新模拟结果为图片文件
        mockUploadResult.setFileName("abc123.jpg");
        mockUploadResult.setOriginalFileName("test.jpg");
        mockUploadResult.setContentType("image/jpeg");
        mockUploadResult.setFileExtension("jpg");

        when(ossService.uploadSimpleFile(any(), eq("images")))
            .thenReturn(mockUploadResult);

        mockMvc.perform(multipart("/api/v1/basic/upload/simple")
                .file(file)
                .param("category", "images")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.fileName").value("abc123.jpg"))
                .andExpect(jsonPath("$.data.originalFileName").value("test.jpg"))
                .andExpect(jsonPath("$.data.contentType").value("image/jpeg"))
                .andExpect(jsonPath("$.data.fileExtension").value("jpg"));
    }

    @Test
    @DisplayName("测试普通文件上传 - PDF文件")
    void testUploadSimplePdfFile() throws Exception {
        // 创建PDF文件
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "document.pdf",
            "application/pdf",
            "PDF document content".getBytes()
        );

        // 更新模拟结果为PDF文件
        mockUploadResult.setFileName("abc123.pdf");
        mockUploadResult.setOriginalFileName("document.pdf");
        mockUploadResult.setContentType("application/pdf");
        mockUploadResult.setFileExtension("pdf");

        when(ossService.uploadSimpleFile(any(), eq("documents")))
            .thenReturn(mockUploadResult);

        mockMvc.perform(multipart("/api/v1/basic/upload/simple")
                .file(file)
                .param("category", "documents")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.fileName").value("abc123.pdf"))
                .andExpect(jsonPath("$.data.originalFileName").value("document.pdf"))
                .andExpect(jsonPath("$.data.contentType").value("application/pdf"))
                .andExpect(jsonPath("$.data.fileExtension").value("pdf"));
    }
}
