package com.extracme.saas.autocare.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;

import com.alibaba.fastjson.JSON;
import com.extracme.saas.autocare.config.WebMvcConfig;
import com.extracme.saas.autocare.interceptor.AuthInterceptor;
import com.extracme.saas.autocare.model.dto.DataDictAddDTO;
import com.extracme.saas.autocare.model.dto.DataDictDTO;
import com.extracme.saas.autocare.model.dto.DataDictUpdateDTO;
import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.extracme.saas.autocare.model.entity.DataDictInfo;
import com.extracme.saas.autocare.model.vo.DataDictSimpleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.DataDictService;
import com.extracme.saas.autocare.service.PermissionService;
import com.extracme.saas.autocare.util.JwtUtil;

@WebMvcTest(BasicController.class)
@Import({WebMvcConfig.class, AuthInterceptor.class, JwtUtil.class})
class BasicControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DataDictService dataDictService;

    @MockBean
    private JwtUtil jwtUtil;

    @MockBean
    private PermissionService permissionService;

    @MockBean
    private TableUserService userService;



    private DataDictInfo testDataDict;
    private BasePageVO<DataDictSimpleVO> testPageVO;

    @BeforeEach
    void setUp() {
        // 配置JWT验证通过
        when(jwtUtil.validateToken(anyString())).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(anyString())).thenReturn("1");

        // 准备测试数据
        testDataDict = new DataDictInfo();
        testDataDict.setId(1L);
        testDataDict.setDataName("测试字典");
        testDataDict.setDataCode("TEST_DICT");
        testDataDict.setCodeType(1);
        testDataDict.setCreateTime(new Date());
        testDataDict.setCreateBy("admin");

        // 准备分页数据
        testPageVO = new BasePageVO<>();
        testPageVO.setTotal(1L);
        testPageVO.setPageNum(1);
        testPageVO.setPageSize(10);
        testPageVO.setPages(1);

        // 创建DataDictSimpleVO对象
        DataDictSimpleVO simpleVO = new DataDictSimpleVO();
        simpleVO.setId(testDataDict.getId());
        simpleVO.setDataName(testDataDict.getDataName());
        simpleVO.setDataCode(testDataDict.getDataCode());
        simpleVO.setCodeType(testDataDict.getCodeType());

        testPageVO.setList(Arrays.asList(simpleVO));
        testPageVO.setHasNextPage(false);
        testPageVO.setHasPreviousPage(false);
        testPageVO.setIsFirstPage(true);
        testPageVO.setIsLastPage(true);
    }

    @Test
    @DisplayName("测试分页查询数据字典列表 - 成功场景")
    void listDictByPage_Success() throws Exception {
        // 模拟服务层返回
        when(dataDictService.pageList(any(BasePageDTO.class))).thenReturn(testPageVO);

        // 创建请求体
        BasePageDTO pageDTO = new BasePageDTO();
        pageDTO.setPageNum(1);
        pageDTO.setPageSize(10);

        // 执行测试
        mockMvc.perform(post("/api/v1/basic/dict/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(pageDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list[0].dataName").value("测试字典"))
                .andDo(MockMvcResultHandlers.print());

        // 验证服务层方法被调用
        verify(dataDictService, times(1)).pageList(any(BasePageDTO.class));
    }

    @Test
    @DisplayName("测试分页查询数据字典列表 - 空结果场景")
    void listDictByPage_EmptyResult() throws Exception {
        // 准备空结果的分页数据
        BasePageVO<DataDictSimpleVO> emptyPageVO = new BasePageVO<>();
        emptyPageVO.setTotal(0L);
        emptyPageVO.setPageNum(1);
        emptyPageVO.setPageSize(10);
        emptyPageVO.setPages(0);
        emptyPageVO.setList(Collections.emptyList());
        emptyPageVO.setHasNextPage(false);
        emptyPageVO.setHasPreviousPage(false);
        emptyPageVO.setIsFirstPage(true);
        emptyPageVO.setIsLastPage(true);

        // 模拟服务层返回空结果
        when(dataDictService.pageList(any(BasePageDTO.class))).thenReturn(emptyPageVO);

        // 创建请求体
        BasePageDTO pageDTO = new BasePageDTO();
        pageDTO.setPageNum(1);
        pageDTO.setPageSize(10);

        // 执行测试
        mockMvc.perform(post("/api/v1/basic/dict/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(pageDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(0))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list").isEmpty())
                .andDo(MockMvcResultHandlers.print());
    }

    @Test
    @DisplayName("测试分页查询数据字典列表 - 异常场景")
    void listDictByPage_Exception() throws Exception {
        // 模拟服务层抛出异常
        when(dataDictService.pageList(any(BasePageDTO.class))).thenThrow(new IllegalArgumentException("分页参数不能为空"));

        // 创建无效的请求体
        BasePageDTO pageDTO = new BasePageDTO();
        pageDTO.setPageNum(0); // 无效的页码
        pageDTO.setPageSize(10);

        // 执行测试
        mockMvc.perform(post("/api/v1/basic/dict/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(pageDTO)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists())
                .andDo(MockMvcResultHandlers.print());
    }

    @Test
    @DisplayName("测试创建数据字典 - 成功场景")
    void create_Success() throws Exception {
        // 创建测试DTO
        DataDictAddDTO<String> addDTO = new DataDictAddDTO<>();
        addDTO.setDataName("测试字典");
        addDTO.setDataCode("TEST_DICT");
        addDTO.setCodeType(1);

        // 创建字典项列表
        List<DataDictDTO<String>> dictItems = new ArrayList<>();
        DataDictDTO<String> item1 = new DataDictDTO<>();
        item1.setName("测试1");
        item1.setCode("1");
        item1.setRemark("测试备注1");
        DataDictDTO<String> item2 = new DataDictDTO<>();
        item2.setName("测试2");
        item2.setCode("2");
        item2.setRemark("测试备注2");
        dictItems.add(item1);
        dictItems.add(item2);
        addDTO.setDictItems(dictItems);

        // 模拟服务层返回
        when(dataDictService.add(any())).thenReturn(testDataDict);

        // 执行测试
        mockMvc.perform(post("/api/v1/basic/dict/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(addDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.dataName").value("测试字典"))
                .andDo(MockMvcResultHandlers.print());

        // 验证服务层方法被调用
        verify(dataDictService, times(1)).add(any());
    }

    @Test
    @DisplayName("测试创建数据字典 - 参数验证失败场景")
    void create_ValidationFailed() throws Exception {
        // 创建缺少必要字段的DTO
        DataDictAddDTO<String> invalidDTO = new DataDictAddDTO<>();
        // 不设置必要字段dataName和dataCode

        // 执行测试
        mockMvc.perform(post("/api/v1/basic/dict/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(invalidDTO)))
                .andExpect(status().isBadRequest()) // 预期返回400错误
                .andDo(MockMvcResultHandlers.print());

        // 验证服务层方法未被调用
        verify(dataDictService, never()).add(any());
    }

    @Test
    @DisplayName("测试创建数据字典 - 服务层异常场景")
    void create_ServiceException() throws Exception {
        // 创建测试DTO
        DataDictAddDTO<String> addDTO = new DataDictAddDTO<>();
        addDTO.setDataName("测试字典");
        addDTO.setDataCode("TEST_DICT");
        addDTO.setCodeType(1);

        // 创建字典项列表
        List<DataDictDTO<String>> dictItems = new ArrayList<>();
        DataDictDTO<String> item = new DataDictDTO<>();
        item.setName("测试");
        item.setCode("1");
        item.setRemark("测试备注");
        dictItems.add(item);
        addDTO.setDictItems(dictItems);

        // 模拟服务层抛出异常
        when(dataDictService.add(any())).thenThrow(new IllegalArgumentException("字典编码已存在"));

        // 执行测试
        mockMvc.perform(post("/api/v1/basic/dict/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(addDTO)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists())
                .andDo(MockMvcResultHandlers.print());
    }

    @Test
    @DisplayName("测试修改数据字典 - 成功场景")
    void updateDict_Success() throws Exception {
        // 创建测试DTO
        DataDictUpdateDTO<String> updateDTO = new DataDictUpdateDTO<>();
        updateDTO.setId(1L);
        updateDTO.setDataName("测试字典");
        updateDTO.setDataCode("TEST_DICT");
        updateDTO.setCodeType(1);

        // 创建字典项列表
        List<DataDictDTO<String>> dictItems = new ArrayList<>();
        DataDictDTO<String> item1 = new DataDictDTO<>();
        item1.setName("测试1");
        item1.setCode("1");
        item1.setRemark("测试备注1");
        DataDictDTO<String> item2 = new DataDictDTO<>();
        item2.setName("测试2");
        item2.setCode("2");
        item2.setRemark("测试备注2");
        dictItems.add(item1);
        dictItems.add(item2);
        updateDTO.setDictItems(dictItems);

        // 模拟服务层返回
        when(dataDictService.update(any())).thenReturn(testDataDict);

        // 执行测试
        mockMvc.perform(post("/api/v1/basic/dict/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(updateDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.dataName").value("测试字典"))
                .andDo(MockMvcResultHandlers.print());

        // 验证服务层方法被调用
        verify(dataDictService, times(1)).update(any());
    }

    @Test
    @DisplayName("测试修改数据字典 - ID为空场景")
    void updateDict_NullId() throws Exception {
        // 创建ID为空的DTO
        DataDictUpdateDTO<String> nullIdDTO = new DataDictUpdateDTO<>();
        nullIdDTO.setDataName("测试字典");
        nullIdDTO.setDataCode("TEST_DICT");
        nullIdDTO.setCodeType(1);
        // 不设置ID

        // 创建字典项列表
        List<DataDictDTO<String>> dictItems = new ArrayList<>();
        DataDictDTO<String> item = new DataDictDTO<>();
        item.setName("测试");
        item.setCode("1");
        item.setRemark("测试备注");
        dictItems.add(item);
        nullIdDTO.setDictItems(dictItems);

        // 模拟服务层抛出异常
        when(dataDictService.update(any())).thenThrow(new IllegalArgumentException("数据字典ID不能为空"));

        // 执行测试
        mockMvc.perform(post("/api/v1/basic/dict/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(nullIdDTO)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists())
                .andDo(MockMvcResultHandlers.print());
    }

    @Test
    @DisplayName("测试修改数据字典 - 数据不存在场景")
    void updateDict_NotFound() throws Exception {
        // 创建测试DTO，使用不存在的ID
        DataDictUpdateDTO<String> notExistDTO = new DataDictUpdateDTO<>();
        notExistDTO.setId(999L); // 不存在的ID
        notExistDTO.setDataName("测试字典");
        notExistDTO.setDataCode("TEST_DICT");
        notExistDTO.setCodeType(1);

        // 创建字典项列表
        List<DataDictDTO<String>> dictItems = new ArrayList<>();
        DataDictDTO<String> item = new DataDictDTO<>();
        item.setName("测试");
        item.setCode("1");
        item.setRemark("测试备注");
        dictItems.add(item);
        notExistDTO.setDictItems(dictItems);

        // 模拟服务层抛出异常
        when(dataDictService.update(any())).thenThrow(new IllegalArgumentException("数据字典不存在"));

        // 执行测试
        mockMvc.perform(post("/api/v1/basic/dict/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(notExistDTO)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists())
                .andDo(MockMvcResultHandlers.print());
    }


}