package com.extracme.saas.autocare.controller;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.vo.DataSyncResultVO;
import com.extracme.saas.autocare.service.DataSyncService;

/**
 * DataSyncController 单元测试
 */
@ExtendWith(MockitoExtension.class)
class DataSyncControllerTest {

    @Mock
    private DataSyncService dataSyncService;

    @InjectMocks
    private DataSyncController dataSyncController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(dataSyncController).build();
    }





    @Test
    void testQuerySyncStatus_Success() throws Exception {
        String batchNo = "SYNC_1640995200000_1234";
        DataSyncResultVO resultVO = createSuccessResult();
        
        when(dataSyncService.querySyncStatus(batchNo)).thenReturn(resultVO);

        // 执行测试
        mockMvc.perform(get("/api/v1/data-sync/status/{batchNo}", batchNo))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.batchNo").value(batchNo))
                .andExpect(jsonPath("$.data.syncStatus").value("SUCCESS"));

        verify(dataSyncService, times(1)).querySyncStatus(batchNo);
    }

    @Test
    void testQuerySyncStatus_NotFound() throws Exception {
        String batchNo = "INVALID_BATCH_NO";
        
        when(dataSyncService.querySyncStatus(batchNo))
            .thenThrow(new BusinessException("未找到批次号为 " + batchNo + " 的同步记录"));

        // 执行测试
        mockMvc.perform(get("/api/v1/data-sync/status/{batchNo}", batchNo))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));

        verify(dataSyncService, times(1)).querySyncStatus(batchNo);
    }



    @Test
    void testGetSupportedTables_Success() throws Exception {
        // 执行测试
        mockMvc.perform(get("/api/v1/data-sync/supported-tables"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0]").value("mtc_vehicle_info"))
                .andExpect(jsonPath("$.data[1]").value("mtc_vehicle_model"))
                .andExpect(jsonPath("$.data[2]").value("mtc_org_info"));
    }

    @Test
    void testGetSupportedOperations_Success() throws Exception {
        // 执行测试
        mockMvc.perform(get("/api/v1/data-sync/supported-operations"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0]").value("AUTO"));
    }

    /**
     * 创建成功的同步结果
     */
    private DataSyncResultVO createSuccessResult() {
        DataSyncResultVO result = new DataSyncResultVO();
        result.setBatchNo("SYNC_1640995200000_1234");
        result.setTenantCode("tenant_001");
        result.setTargetTable("mtc_vehicle_info");
        result.setOperationType("AUTO");
        result.setSyncStatus("SUCCESS");
        result.setTotalCount(1);
        result.setSuccessCount(1);
        result.setFailedCount(0);
        result.setSyncStartTime(new Date());
        result.setSyncEndTime(new Date());
        result.setSyncDuration(1000L);
        return result;
    }


}
