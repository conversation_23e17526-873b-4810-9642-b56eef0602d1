package com.extracme.saas.autocare.controller;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.ArrayList;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.exception.GlobalExceptionHandler;
import com.extracme.saas.autocare.model.vo.workflow.TaskProgressVO;
import com.extracme.saas.autocare.service.WorkflowService;

/**
 * WorkflowController任务进度查询接口单元测试
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@ExtendWith(MockitoExtension.class)
class WorkflowControllerTaskProgressTest {

    @Mock
    private WorkflowService workflowService;

    @InjectMocks
    private WorkflowController workflowController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(workflowController)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();
    }

    @Test
    void testGetTaskProgress_Success() throws Exception {
        // 准备测试数据
        String businessId = "TASK_001";
        TaskProgressVO taskProgressVO = createTaskProgressVO();

        // Mock服务方法
        when(workflowService.getTaskProgress(businessId)).thenReturn(taskProgressVO);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/v1/workflow/progress/{businessId}", businessId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.businessId").value(businessId))
                .andExpect(jsonPath("$.data.instanceId").value(1L))
                .andExpect(jsonPath("$.data.workflowId").value(1L))
                .andExpect(jsonPath("$.data.workflowName").value("标准维修流程"))
                .andExpect(jsonPath("$.data.currentActivityCode").value("VEHICLE_TRANSFER"))
                .andExpect(jsonPath("$.data.currentActivityName").value("车辆交接"))
                .andExpect(jsonPath("$.data.status").value("UNPROCESSED"))
                .andExpect(jsonPath("$.data.statusName").value("未处理"))
                .andExpect(jsonPath("$.data.tenantId").value(1))
                .andExpect(jsonPath("$.data.createBy").value("testUser"))
                .andExpect(jsonPath("$.data.updateBy").value("testUser"));
    }

    @Test
    void testGetTaskProgress_EmptyBusinessId() throws Exception {
        // 测试空的任务编号
        mockMvc.perform(get("/api/v1/workflow/progress/{businessId}", "")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound()); // 空路径参数会导致404
    }

    @Test
    void testGetTaskProgress_ServiceException() throws Exception {
        // 准备测试数据
        String businessId = "INVALID_TASK";

        // Mock服务方法抛出BusinessException
        when(workflowService.getTaskProgress(anyString()))
                .thenThrow(new BusinessException("未找到对应的工作流实例"));

        // 执行请求并验证异常处理
        // 现在有了全局异常处理器，会返回Result格式的错误响应
        mockMvc.perform(get("/api/v1/workflow/progress/{businessId}", businessId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()) // 全局异常处理器返回200状态码
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(500)) // BusinessException默认错误码
                .andExpect(jsonPath("$.message").value("未找到对应的工作流实例"));
    }

    /**
     * 创建测试用的TaskProgressVO对象
     */
    private TaskProgressVO createTaskProgressVO() {
        TaskProgressVO taskProgressVO = new TaskProgressVO();
        taskProgressVO.setBusinessId("TASK_001");
        taskProgressVO.setInstanceId(1L);
        taskProgressVO.setWorkflowId(1L);
        taskProgressVO.setWorkflowName("标准维修流程");
        taskProgressVO.setCurrentActivityCode("VEHICLE_TRANSFER");
        taskProgressVO.setCurrentActivityName("车辆交接");
        taskProgressVO.setStatus("UNPROCESSED");
        taskProgressVO.setStatusName("未处理");
        taskProgressVO.setTenantId(1);
        taskProgressVO.setCreateTime(new Date());
        taskProgressVO.setCreateBy("testUser");
        taskProgressVO.setUpdateTime(new Date());
        taskProgressVO.setUpdateBy("testUser");
        taskProgressVO.setActivityDefinitions(new ArrayList<>());
        taskProgressVO.setActivityInstances(new ArrayList<>());
        return taskProgressVO;
    }
}
