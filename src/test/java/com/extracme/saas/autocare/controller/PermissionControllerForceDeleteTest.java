package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.PermissionDeleteDTO;
import com.extracme.saas.autocare.service.PermissionService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 权限控制器强制删除功能测试类
 */
@ExtendWith(MockitoExtension.class)
class PermissionControllerForceDeleteTest {

    private MockMvc mockMvc;

    @Mock
    private PermissionService permissionService;

    @InjectMocks
    private PermissionController permissionController;

    private ObjectMapper objectMapper;

    private PermissionDeleteDTO deleteDTO;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(permissionController).build();
        objectMapper = new ObjectMapper();

        deleteDTO = new PermissionDeleteDTO();
        deleteDTO.setId(1L);
        deleteDTO.setForceDelete(false);
    }

    @Test
    @DisplayName("普通删除权限 - 成功")
    void deletePermission_Normal_Success() throws Exception {
        // Given
        doNothing().when(permissionService).deletePermission(1L, false);

        // When & Then
        mockMvc.perform(post("/api/v1/permission/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deleteDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(permissionService).deletePermission(1L, false);
    }

    @Test
    @DisplayName("强制删除权限 - 成功")
    void deletePermission_Force_Success() throws Exception {
        // Given
        deleteDTO.setForceDelete(true);
        doNothing().when(permissionService).deletePermission(1L, true);

        // When & Then
        mockMvc.perform(post("/api/v1/permission/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deleteDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(permissionService).deletePermission(1L, true);
    }

    @Test
    @DisplayName("删除权限 - 权限不存在")
    void deletePermission_NotFound() throws Exception {
        // Given
        doThrow(new BusinessException(ErrorCode.PERMISSION_NOT_FOUND))
                .when(permissionService).deletePermission(1L, false);

        // When & Then
        // 由于没有全局异常处理器，异常会直接抛出，导致500错误
        mockMvc.perform(post("/api/v1/permission/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deleteDTO)))
                .andExpect(status().is5xxServerError());

        verify(permissionService).deletePermission(1L, false);
    }

    @Test
    @DisplayName("普通删除权限 - 权限被使用")
    void deletePermission_Normal_InUse() throws Exception {
        // Given
        doThrow(new BusinessException(ErrorCode.PERMISSION_IN_USE))
                .when(permissionService).deletePermission(1L, false);

        // When & Then
        // 由于没有全局异常处理器，异常会直接抛出，导致500错误
        mockMvc.perform(post("/api/v1/permission/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deleteDTO)))
                .andExpect(status().is5xxServerError());

        verify(permissionService).deletePermission(1L, false);
    }

    @Test
    @DisplayName("删除权限 - 参数校验失败（权限ID为空）")
    void deletePermission_ValidationFailed_NullId() throws Exception {
        // Given
        deleteDTO.setId(null);

        // When & Then
        mockMvc.perform(post("/api/v1/permission/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deleteDTO)))
                .andExpect(status().isBadRequest());

        verify(permissionService, never()).deletePermission(any(), any());
    }

    @Test
    @DisplayName("删除权限 - forceDelete默认值为false")
    void deletePermission_DefaultForceDeleteValue() throws Exception {
        // Given
        PermissionDeleteDTO dtoWithoutForceDelete = new PermissionDeleteDTO();
        dtoWithoutForceDelete.setId(1L);
        // 不设置forceDelete，应该使用默认值false
        
        doNothing().when(permissionService).deletePermission(1L, false);

        // When & Then
        mockMvc.perform(post("/api/v1/permission/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dtoWithoutForceDelete)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(permissionService).deletePermission(1L, false);
    }

    @Test
    @DisplayName("删除权限 - 请求体为空")
    void deletePermission_EmptyRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/v1/permission/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isBadRequest());

        verify(permissionService, never()).deletePermission(any(), any());
    }

    @Test
    @DisplayName("删除权限 - 无效的JSON格式")
    void deletePermission_InvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/v1/permission/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andExpect(status().isBadRequest());

        verify(permissionService, never()).deletePermission(any(), any());
    }

    @Test
    @DisplayName("强制删除权限 - 即使权限被使用也应该成功")
    void deletePermission_Force_EvenWhenInUse() throws Exception {
        // Given
        deleteDTO.setForceDelete(true);
        // 强制删除不应该抛出PERMISSION_IN_USE异常
        doNothing().when(permissionService).deletePermission(1L, true);

        // When & Then
        mockMvc.perform(post("/api/v1/permission/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deleteDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(permissionService).deletePermission(1L, true);
    }

    @Test
    @DisplayName("删除权限 - 验证参数传递正确性")
    void deletePermission_VerifyParameterPassing() throws Exception {
        // Given
        deleteDTO.setId(123L);
        deleteDTO.setForceDelete(true);
        doNothing().when(permissionService).deletePermission(123L, true);

        // When & Then
        mockMvc.perform(post("/api/v1/permission/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deleteDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证参数传递正确
        verify(permissionService).deletePermission(123L, true);
        verify(permissionService, never()).deletePermission(eq(123L), eq(false));
    }
}
