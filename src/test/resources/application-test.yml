spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE;DB_CLOSE_DELAY=-1
    username: sa
    password: 
    schema: classpath:db/schema-h2.sql
    data: classpath:db/data-h2.sql
    initialization-mode: always
    sql-script-encoding: UTF-8
  sql:
    init:
      mode: always
      schema-locations: classpath:db/schema-h2.sql
      data-locations: classpath:db/data-h2.sql
      encoding: UTF-8
  h2:
    console:
      enabled: true
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: none
    show-sql: true
  redis:
    database: 0
    host: localhost
    port: 6379
    password: 
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms

# 多租户配置
tenant:
  default-schema: auto_care_saas
  schema-map:
    0: auto_care_extracme
    1: auto_care_dzjt

# 工作流配置
workflow:
  template:
    default-active: 1

# 测试配置
test:
  tenant-id: 1
  username: testuser

logging:
  level:
    com.extracme.saas.autocare: debug
    org.springframework: info
    org.hibernate: info
    com.aliyun.oss: info
    org.apache.http: warn

# OSS配置（集成测试用）
# 注意：这些是示例配置，实际使用时请替换为真实的OSS配置
# 可以通过环境变量设置：OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET, OSS_BUCKET_NAME等
# 阿里云 OSS 配置
oss:
  endpoint: http://oss-cn-shanghai.aliyuncs.com
  access-key-id: LTAI5tJumdVNXHPLszk49yAk
  access-key-secret: ******************************
  bucket-name: evcard
  bucket-short-time: evcard-short-time
  # 文件上传配置
  upload:
    max-file-size: 100MB          # 最大文件大小
    allowed-extensions:           # 允许的文件扩展名
      - jpg
      - jpeg
      - png
      - gif
      - bmp
      - pdf
      - doc
      - docx
      - xls
      - xlsx
      - ppt
      - pptx
      - txt
      - zip
      - rar
    base-url: https://evcard.oss-cn-shanghai.aliyuncs.com  # OSS 访问域名

  # STS配置（生产环境使用）
  sts-enabled: ${OSS_STS_ENABLED:false}  # 测试环境默认禁用STS
  role-arn: ${OSS_ROLE_ARN:acs:ram::1199455603890798:role/evcardtesttmpwrite}
  role-session-name: ${OSS_ROLE_SESSION_NAME:oss-upload-session}
  duration-seconds: ${OSS_DURATION_SECONDS:3600}
