-- 初始化活动节点定义
INSERT INTO activity_definition
(id, activity_code, activity_name, description, sequence, is_enabled, create_by, update_by)
VALUES
(1, 'HANDOVER', '车辆交接', '车辆交接节点', 1, 1, 'system', 'system'),
(2, 'INSPECTION', '进保预审', '进保预审节点', 2, 1, 'system', 'system'),
(3, 'QUOTE', '维修报价', '维修报价节点', 3, 1, 'system', 'system'),
(4, 'APPROVAL', '客户确认', '客户确认节点', 4, 1, 'system', 'system'),
(5, 'REPAIR', '维修施工', '维修施工节点', 5, 1, 'system', 'system'),
(6, 'QUALITY_CHECK', '质量检验', '质量检验节点', 6, 1, 'system', 'system'),
(7, 'RETURN', '车辆返还', '车辆返还节点', 7, 1, 'system', 'system');

-- 初始化节点状态定义
INSERT INTO activity_status
(id, status_code, status_name, description, create_by, update_by)
VALUES
(1, 'UNPROCESSED', '未处理', '节点尚未开始处理', 'system', 'system'),
(2, 'PROCESSING', '处理中', '节点正在处理中', 'system', 'system'),
(3, 'COMPLETED', '已完成', '节点处理已完成', 'system', 'system'),
(4, 'REJECTED', '已驳回', '节点处理被驳回', 'system', 'system'),
(5, 'SUSPENDED', '已暂停', '节点处理暂停', 'system', 'system'),
(6, 'CLOSED', '已关闭', '节点处理已关闭', 'system', 'system');

-- 初始化活动节点转换规则
INSERT INTO activity_transition
(id, workflow_id, from_activity_code, to_activity_code, trigger_event, condition_handler, handler_class, tenant_id, create_by, update_by, description)
VALUES
-- 这些值会在测试中被动态替换，先设置一些默认值
(1, 1, 'HANDOVER', 'INSPECTION', 'COMPLETE_HANDOVER', null, 'com.extracme.saas.autocare.workflow.handler.DefaultTransitionHandler', 1, 'system', 'system', '车辆交接完成转入进保预审'),
(2, 1, 'INSPECTION', 'QUOTE', 'COMPLETE_INSPECTION', null, 'com.extracme.saas.autocare.workflow.handler.DefaultTransitionHandler', 1, 'system', 'system', '进保预审完成转入维修报价'),
(3, 1, 'QUOTE', 'APPROVAL', 'COMPLETE_QUOTE', null, 'com.extracme.saas.autocare.workflow.handler.DefaultTransitionHandler', 1, 'system', 'system', '维修报价完成转入客户确认'),
(4, 1, 'APPROVAL', 'REPAIR', 'COMPLETE_APPROVAL', null, 'com.extracme.saas.autocare.workflow.handler.DefaultTransitionHandler', 1, 'system', 'system', '客户确认完成转入维修施工'),
(5, 1, 'REPAIR', 'QUALITY_CHECK', 'COMPLETE_REPAIR', null, 'com.extracme.saas.autocare.workflow.handler.DefaultTransitionHandler', 1, 'system', 'system', '维修施工完成转入质量检验');
