-- 工作流模板表
CREATE TABLE IF NOT EXISTS workflow_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_name VARCHAR(100) NOT NULL,
    description TEXT,
    task_type INT NOT NULL,
    repair_factory_type INT NOT NULL,
    sub_product_line INT NOT NULL,
    is_active INT NOT NULL DEFAULT 1,
    tenant_id INT NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(128) NOT NULL DEFAULT '',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(128) NOT NULL DEFAULT ''
);

-- 活动节点定义表
CREATE TABLE IF NOT EXISTS activity_definition (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    activity_code VARCHAR(50) NOT NULL,
    activity_name VARCHAR(100) NOT NULL,
    description TEXT,
    sequence INT NOT NULL,
    is_enabled INT NOT NULL DEFAULT 1,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(128) NOT NULL DEFAULT '',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(128) NOT NULL DEFAULT ''
);

-- 工作流活动节点关联表
CREATE TABLE IF NOT EXISTS workflow_activity (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_id BIGINT NOT NULL,
    activity_code VARCHAR(50) NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(128) NOT NULL DEFAULT '',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(128) NOT NULL DEFAULT ''
);

-- 活动节点转换规则表
CREATE TABLE IF NOT EXISTS activity_transition (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_id BIGINT NOT NULL,
    from_activity_code VARCHAR(50) NOT NULL,
    to_activity_code VARCHAR(50) NOT NULL,
    trigger_event VARCHAR(50) NOT NULL,
    condition_handler VARCHAR(255),
    handler_class VARCHAR(255) NOT NULL,
    description TEXT,
    tenant_id INT NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(128) NOT NULL DEFAULT '',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(128) NOT NULL DEFAULT ''
);

-- 工作流实例表
CREATE TABLE IF NOT EXISTS workflow_instance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_id BIGINT NOT NULL,
    business_id VARCHAR(100) NOT NULL,
    tenant_id INT NOT NULL,
    current_activity_code VARCHAR(50) NOT NULL,
    status_code VARCHAR(50) NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(128) NOT NULL DEFAULT '',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(128) NOT NULL DEFAULT ''
);

-- 活动实例记录表
CREATE TABLE IF NOT EXISTS activity_instance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    instance_id BIGINT NOT NULL,
    to_activity_code VARCHAR(50) NOT NULL,
    from_activity_code VARCHAR(50),
    transition_id BIGINT,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration INT,
    current_status_code VARCHAR(50) NOT NULL,
    operator VARCHAR(100) NOT NULL,
    remarks TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(128) NOT NULL DEFAULT '',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(128) NOT NULL DEFAULT ''
);

-- 活动状态表
CREATE TABLE IF NOT EXISTS activity_status (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    status_code VARCHAR(50) NOT NULL,
    status_name VARCHAR(100) NOT NULL,
    description TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(128) NOT NULL DEFAULT '',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(128) NOT NULL DEFAULT ''
);
