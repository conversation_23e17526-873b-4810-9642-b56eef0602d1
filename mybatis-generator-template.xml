<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- MySQL驱动jar包位置 -->
    <classPathEntry location="src/main/resources/mybatis/jdbc/mysql-connector-java-8.0.25.jar"/>

    <!-- 配置环境 -->
    <context id="dsql" targetRuntime="MyBatis3DynamicSql">
        <!-- 设置编码 -->
        <property name="javaFileEncoding" value="UTF-8"/>

        <!-- 序列化插件 -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <!-- 注释生成器配置 -->
        <commentGenerator>
            <!-- 是否生成注释 -->
            <property name="suppressAllComments" value="false"/>
            <!-- 是否生成日期 -->
            <property name="suppressDate" value="true"/>
            <!-- 是否添加数据库注释 -->
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <!-- 数据库连接配置 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="jdbc:mysql://[YOUR_DATABASE_HOST]/[YOUR_DATABASE_NAME]"
                        userId="[YOUR_USERNAME]" 
                        password="[YOUR_PASSWORD]">
            <!-- 使用MySQL的information_schema -->
            <property name="useInformationSchema" value="true"/>
            <!-- 确保使用正确的数据库 -->
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>

        <!-- Java模型生成器配置 -->
        <javaModelGenerator targetPackage="[YOUR_MODEL_PACKAGE]"
                            targetProject="src/main/java">
            <!-- 是否允许子包 -->
            <property name="enableSubPackages" value="true"/>
            <!-- 是否对字符串进行trim操作 -->
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- Mapper接口生成器配置 -->
        <javaClientGenerator targetPackage="[YOUR_MAPPER_PACKAGE]"
                             targetProject="src/main/java" 
                             type="XMLMAPPER">
            <!-- 是否允许子包 -->
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!-- 要生成的表配置 -->
        <table tableName="[YOUR_TABLE_NAME]" schema="">
            <!-- 主键生成配置 -->
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
            <!-- 表名前缀处理规则 -->
            <domainObjectRenamingRule searchString="^T" replaceString=""/>
        </table>

    </context>
</generatorConfiguration> 