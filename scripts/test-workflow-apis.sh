#!/bin/bash

# 工作流模板新增API接口测试脚本
# 用于测试工作流模板下拉框查询和复制转换规则接口

# 配置
BASE_URL="http://localhost:8080/api/v1/workflow"
JWT_TOKEN="your-jwt-token-here"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== 工作流模板新增API接口测试 ===${NC}"

# 测试1: 获取工作流模板下拉框数据
echo -e "\n${YELLOW}测试1: 获取工作流模板下拉框数据${NC}"
echo "请求: GET ${BASE_URL}/templates/combo"

response=$(curl -s -w "\n%{http_code}" \
  -X GET "${BASE_URL}/templates/combo" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json")

http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | head -n -1)

if [ "$http_code" -eq 200 ]; then
    echo -e "${GREEN}✓ 请求成功 (HTTP $http_code)${NC}"
    echo "响应内容:"
    echo "$body" | jq '.' 2>/dev/null || echo "$body"
else
    echo -e "${RED}✗ 请求失败 (HTTP $http_code)${NC}"
    echo "响应内容: $body"
fi

# 测试2: 复制模板转换规则 (正常情况)
echo -e "\n${YELLOW}测试2: 复制模板转换规则 (源模板ID=1, 目标模板ID=2)${NC}"
echo "请求: POST ${BASE_URL}/templates/copy-transitions"

request_body='{
  "sourceTemplateId": 1,
  "targetTemplateId": 2
}'

echo "请求体: $request_body"

response=$(curl -s -w "\n%{http_code}" \
  -X POST "${BASE_URL}/templates/copy-transitions" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "$request_body")

http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | head -n -1)

if [ "$http_code" -eq 200 ]; then
    echo -e "${GREEN}✓ 请求成功 (HTTP $http_code)${NC}"
    echo "响应内容:"
    echo "$body" | jq '.' 2>/dev/null || echo "$body"
else
    echo -e "${RED}✗ 请求失败 (HTTP $http_code)${NC}"
    echo "响应内容: $body"
fi

# 测试3: 复制模板转换规则 (参数验证错误)
echo -e "\n${YELLOW}测试3: 复制模板转换规则 (参数验证错误 - 源模板ID为空)${NC}"
echo "请求: POST ${BASE_URL}/templates/copy-transitions"

invalid_request_body='{
  "sourceTemplateId": null,
  "targetTemplateId": 2
}'

echo "请求体: $invalid_request_body"

response=$(curl -s -w "\n%{http_code}" \
  -X POST "${BASE_URL}/templates/copy-transitions" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "$invalid_request_body")

http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | head -n -1)

if [ "$http_code" -eq 400 ]; then
    echo -e "${GREEN}✓ 参数验证正常 (HTTP $http_code)${NC}"
    echo "响应内容:"
    echo "$body" | jq '.' 2>/dev/null || echo "$body"
else
    echo -e "${RED}✗ 参数验证异常 (HTTP $http_code)${NC}"
    echo "响应内容: $body"
fi

# 测试4: 复制模板转换规则 (相同模板ID)
echo -e "\n${YELLOW}测试4: 复制模板转换规则 (源模板和目标模板相同)${NC}"
echo "请求: POST ${BASE_URL}/templates/copy-transitions"

same_template_request='{
  "sourceTemplateId": 1,
  "targetTemplateId": 1
}'

echo "请求体: $same_template_request"

response=$(curl -s -w "\n%{http_code}" \
  -X POST "${BASE_URL}/templates/copy-transitions" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "$same_template_request")

http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | head -n -1)

if [ "$http_code" -eq 500 ] || [ "$http_code" -eq 400 ]; then
    echo -e "${GREEN}✓ 业务验证正常 (HTTP $http_code)${NC}"
    echo "响应内容:"
    echo "$body" | jq '.' 2>/dev/null || echo "$body"
else
    echo -e "${RED}✗ 业务验证异常 (HTTP $http_code)${NC}"
    echo "响应内容: $body"
fi

echo -e "\n${YELLOW}=== 测试完成 ===${NC}"
echo -e "${YELLOW}注意: 请确保已正确设置JWT_TOKEN变量${NC}"
echo -e "${YELLOW}注意: 请确保数据库中存在相应的工作流模板数据${NC}"
