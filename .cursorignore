# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Trae
.trae/


# IDE - IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# IDE - Eclipse
.settings/
.project
.classpath
.factorypath
bin/

# IDE - VS Code
.vscode/

# Compiled class files
*.class

# Log files
*.log
logs/

# Package files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*

# Spring Boot
.springBeans
.sts4-cache/

# Application properties with sensitive info
src/main/resources/application-prod.yml
src/main/resources/application-prod.properties

# OS specific
.DS_Store
Thumbs.db

# Temp files
*.swp
*.swo
*~

# Node (if using frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test coverage reports
coverage/
.coverage/
.tox/
.pytest_cache/

# Gradle (if using)
.gradle/
build/

# Other common files to ignore
*.bak
*.tmp
*.temp 